package revenue

import (
	"context"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo/readconcern"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"oss.navercorp.com/da-ssp/gfp-api/database"
	"oss.navercorp.com/da-ssp/gfp-api/util"
)

type BatchReportJobSchedule struct {
	Id             primitive.ObjectID `bson:"_id"`
	AdProviderType string             `bson:"adProviderType"`
	RkUse          int8               `bson:"rkUse"`
	Ymd            string             `bson:"ymd"`
	State          string             `bson:"state"`
}

type RevenueSchedule struct {
	Id                 primitive.ObjectID `bson:"_id"`
	Name               string             `bson:"name"`
	PublisherId        primitive.ObjectID `bson:"publisher_id"`
	KeyGroupId         primitive.ObjectID `bson:"keyGroup_id"`
	Interval           string             `bson:"interval"`           // DAILY, MONTHLY
	Period             string             `bson:"period"`             // DAY, MONTH
	FileCreateCriteria string             `bson:"fileCreateCriteria"` // DAILY, MONTHLY
	CreatedAt          time.Time          `bson:"createdAt"`
	ModifiedAt         time.Time          `bson:"modifiedAt"`
}

type RevenueReport struct {
	Id                              primitive.ObjectID `bson:"_id"`
	SummaryRevenueSharingScheduleId primitive.ObjectID `bson:"summaryRevenueSharingSchedule_id"`
	BundleId                        primitive.ObjectID `bson:"bundle_id"`
	DateForFileName                 string             `bson:"dateForFileName"`
	BeginYmd                        string             `bson:"beginYmd"`
	EndYmd                          string             `bson:"endYmd"`
	FilePath                        string             `bson:"filePath"`
	FileName                        string             `bson:"fileName"`
	RkCntInFile                     int64              `bson:"rkCntInFile"`
	NonRkCntInFile                  int64              `bson:"nonRkCntInFile"`
	Progress                        string             `bson:"progress"` // notyet, start, ongoing rk, ongoing nonrk, ongoing gfp, complete, failure
	Verifications                   []Verification     `bson:"verifications"`
	CreatedAt                       time.Time          `bson:"createdAt"`
	ModifiedAt                      time.Time          `bson:"modifiedAt"`
	CompletedAt                     time.Time          `bson:"completedAt"`
}

type Verification struct {
	StatsType string `bson:"statsType"`
	RowCnt    int64  `json:"rowCnt" bson:"rowCnt"`
	SumOfImp  int64  `json:"sumOfImp" bson:"sumOfImp"`
	SumOfClk  int64  `json:"sumOfClk" bson:"sumOfClk"`

	SumOfKrwRevenue    primitive.Decimal128 `json:"sumOfKrwRevenue,string" bson:"sumOfKrwRevenue"`
	SumOfKrwNetRevenue primitive.Decimal128 `json:"sumOfKrwNetRevenue,string" bson:"sumOfKrwNetRevenue"`
	//SumOfUsdRevenue    primitive.Decimal128 `json:"sumOfUsdRevenue,string" bson:"sumOfUsdRevenue"`
	//SumOfUsdNetRevenue primitive.Decimal128 `json:"sumOfUsdNetRevenue,string" bson:"sumOfUsdNetRevenue"`
}

type FinalVerification struct {
	Date     string `json:"date" bson:"date"`
	RowCnt   int64  `json:"rowCnt" bson:"rowCnt"`
	SumOfImp int64  `json:"sumOfImp" bson:"sumOfImp"`
	SumOfClk int64  `json:"sumOfClk" bson:"sumOfClk"`

	SumOfKrwRevenue    primitive.Decimal128 `json:"sumOfKrwRevenue,string" bson:"sumOfKrwRevenue" default:"0"`
	SumOfKrwNetRevenue primitive.Decimal128 `json:"sumOfKrwNetRevenue,string" bson:"sumOfKrwNetRevenue" default:"0"`
	//SumOfUsdRevenue    primitive.Decimal128 `json:"sumOfUsdRevenue,string" bson:"sumOfUsdRevenue"`
	//SumOfUsdNetRevenue primitive.Decimal128 `json:"sumOfUsdNetRevenue,string" bson:"sumOfUsdNetRevenue"`
}

type Environment struct {
	Id         primitive.ObjectID `bson:"_id"` // 요걸 해줘야 _id를 제대로 가져옴
	Name       string             `bson:"name"`
	Value      []string           `bson:"value"`
	CreatedAt  time.Time          `bson:"createdAt"` // 요걸 해줘야 시간을 제대로 가져옴
	ModifiedAt time.Time          `bson:"modifiedAt"`
}

type RevenueService struct {
}

func init() {
}

func (service *RevenueService) GetCount() int {
	var count int
	return count
}

func (service *RevenueService) List(ginCtx *gin.Context, filter RevenueParam) []RevenueReport {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debugf("[REVENUE] rs list filter:%v", filter)
	collection := database.GFP.Collection("SummaryRevenueSharingReports")

	// Pass these options to the Find method
	findOptions := options.Find()
	findOptions.SetLimit(2)

	var list []RevenueReport

	// Passing bson.D{{}} as the filter matches all documents in the environmentCollection
	ctx, _ := context.WithTimeout(context.TODO(), 30*time.Second)
	cur, err := collection.Find(ctx, bson.D{{}}, findOptions)
	if err != nil {
		entry.Error(err)
	}
	defer cur.Close(ctx)

	// Finding multiple documents returns a cursor
	// Iterating through the cursor allows us to decode documents one at a time
	for cur.Next(ctx) {

		// create a value into which the single document can be decoded
		var elem RevenueReport
		err := cur.Decode(&elem)
		if err != nil {
			entry.Error(err)
		}

		list = append(list, elem)
	}

	if err := cur.Err(); err != nil {
		entry.Error(err)
	}

	// Close the cursor once finished
	cur.Close(context.TODO())
	return list
}

func (service *RevenueService) Get(ginCtx *gin.Context, id string) RevenueReport {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// like try, catch, finally :: https://jaehue.github.io/post/resilent-go-service/
	defer func() {
		if e := recover(); e != nil {
			entry.Errorf("[REVENUE] revenue sharing get panic occured %s\r\n", e)
		}
	}()

	collection := database.GFP.Collection("SummaryRevenueSharingReports")
	objId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		entry.Errorf("Invalid ObjectID")
	}

	filter := bson.M{"_id": objId}

	var report RevenueReport
	err = collection.FindOne(context.TODO(), filter).Decode(&report)
	if err != nil {
		entry.Error(err)
	}

	entry.Debugf("[REVENUE] 리포트: %+v\n", util.PrettyPrint(report))

	return report
}

func (service *RevenueService) GetStatus(ginCtx *gin.Context, keyGroupId string, date string) string {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// like try, catch, finally :: https://jaehue.github.io/post/resilent-go-service/
	defer func() {
		if e := recover(); e != nil {
			entry.Errorf("[REVENUE] %s\r\n", e)
		}
	}()

	schedule := getScheduleByKeyGroupIdAndDate(ginCtx, keyGroupId, date)

	reportState := getReportState(ginCtx, schedule.Id, date)

	return reportState
}

func (service *RevenueService) Verification(ginCtx *gin.Context, date string) (err error, totalVerification FinalVerification) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	/*
		// API 호출 시 마다 배치서버를 호출할까 하다가, 그냥 젠킨스 잡으로 주기적 실행을 걸기로 함.
		// 배치서버로 검증을 위한 요약 지표 생성 요청
		entry.Debugf("검증을 위한 요약 지표 생성 요청 .......")
		defer func() {
			if e := recover(); e != nil {
				entry.Errorf("[REVENUE] %s\r\n", e)
			}
		}()
		batchUrl := config.GetConfig("batch.url").(string) + "/batch/monitoring/reportapistats/ncc?date=" + date
		entry.Debugf("검증을 위한 요약 지표 생성 요청. batchurl:" + batchUrl)
		resp, err := http.Get(batchUrl)
		if err != nil {
			panic(err)
		}
		defer resp.Body.Close()
	*/

	// 요약 지표 조회
	entry.Debugf("검증을 위한 요약 지표 조회 .......")
	collection := database.GFP.Collection("MonitoringReportApiStats")
	filter := bson.M{"reportApiType": "NCC", "date": date}
	err = collection.FindOne(context.TODO(), filter).Decode(&totalVerification)
	if err != nil {
		entry.Errorf("[REVENUE] 전산감사 용 NCC 요약 지표 조회 실패. %v", err)
		if err.Error() == "mongo: no documents in result" {
			err = nil
			v1, _ := primitive.ParseDecimal128("0")
			v2, _ := primitive.ParseDecimal128("0")
			totalVerification.SumOfKrwRevenue = v1
			totalVerification.SumOfKrwNetRevenue = v2
			return
		}
		return err, FinalVerification{} // 미생성
	}

	return
}

func (service *RevenueService) GetScheduleByKeyGroupIdAndDate(ginCtx *gin.Context, keyGroupId string, date string) RevenueSchedule {
	return getScheduleByKeyGroupIdAndDate(ginCtx, keyGroupId, date)
}

func getScheduleByKeyGroupIdAndDate(ginCtx *gin.Context, keyGroupId string, date string) RevenueSchedule {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// like try, catch, finally :: https://jaehue.github.io/post/resilent-go-service/
	defer func() {
		if e := recover(); e != nil {
			entry.Errorf("[REVENUE] %s\r\n", e)
		}
	}()

	var period string
	if len(date) == 8 {
		period = "DAY"
	} else {
		period = "MONTH"
	}

	list := getSchedulesByKeyGroupId(ginCtx, keyGroupId)

	var revenueSchedule RevenueSchedule
	for _, sch := range list {
		if sch.Period == period {
			revenueSchedule = sch
			break
		}
	}
	entry.Debugf("[REVENUE] keyGroupID:%s date:%s에 해당하는 스케줄:%+v", keyGroupId, date, util.PrettyPrint(revenueSchedule))

	return revenueSchedule
}

/**
keyGroupId로 수익쉐어 스케줄 조회
*/
func getSchedulesByKeyGroupId(ginCtx *gin.Context, keyGroupId string) []RevenueSchedule {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// like try, catch, finally :: https://jaehue.github.io/post/resilent-go-service/
	defer func() {
		if e := recover(); e != nil {
			entry.Errorf("[REVENUE] %s\r\n", e)
		}
	}()

	// 조회
	var list []RevenueSchedule

	// 필터
	keyGroup_id, _ := primitive.ObjectIDFromHex(keyGroupId)
	filter := bson.M{
		"keyGroup_id": keyGroup_id,
	}

	collection := database.GFP.Collection("SummaryRevenueSharingSchedules")
	ctx, _ := context.WithTimeout(context.TODO(), 30*time.Second)
	cur, err := collection.Find(ctx, filter)

	if err != nil {
		entry.Error(err)
	}
	defer cur.Close(ctx)

	// Finding multiple documents returns a cursor
	// Iterating through the cursor allows us to decode documents one at a time
	for cur.Next(ctx) {

		// create a value into which the single document can be decoded
		var elem RevenueSchedule
		err := cur.Decode(&elem)
		if err != nil {
			entry.Error(err)
		}

		list = append(list, elem)
	}

	if err := cur.Err(); err != nil {
		entry.Error(err)
	}

	// Close the cursor once finished
	cur.Close(context.TODO())

	return list
}

/**
스케줄ID와 날짜로 수익쉐어 리포트 상태

[ timezone 타임존 한국시간으로 찍는 방법 ]

config.go에 아래와 같이 기술
var LocationAsiaSeoul, _ = time.LoadLocation("Asia/Seoul")

필요한 곳에서 time.In
revenueReport.CompletedAt.In(config.LocationAsiaSeoul)


[ 파라미터 ]
date : yyyymmdd or yyyymm
*/
func getReportState(ginCtx *gin.Context, schedule_id primitive.ObjectID, date string) string {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// like try, catch, finally :: https://jaehue.github.io/post/resilent-go-service/
	defer func() {
		if e := recover(); e != nil {
			entry.Errorf("[REVENUE] %s\r\n", e)
		}
	}()

	collOptions := &options.CollectionOptions{}
	collOptions.SetReadConcern(readconcern.Majority())
	collOptions.SetReadPreference(readpref.SecondaryPreferred(readpref.WithMaxStaleness(time.Second * 90)))

	collection := database.GFP.Collection("SummaryRevenueSharingReports", collOptions)

	filter := bson.M{
		"summaryRevenueSharingSchedule_id": schedule_id,
		"dateForFileName":                  date,
	}

	options := options.FindOneOptions{}
	options.SetSort(bson.M{"_id": -1})

	// reportId로 정렬해서 최근 한개만
	var revenueReport RevenueReport
	err := collection.FindOne(context.TODO(), filter, &options).Decode(&revenueReport)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return "NOT_YET" // 미생성
		} else {
			entry.Errorf("[REVENUE] 리포트 상태 조회 실패. %v", err)
			return "FAILURE" // 실패
		}
	}

	entry.Debugf("[REVENUE] 리포트: %+v\n", util.PrettyPrint(revenueReport))

	// 구 상태 변환 -->
	newProgress := convert(revenueReport.Progress)
	revenueReport.Progress = newProgress
	// <-- 구 상태 변환

	return revenueReport.Progress
}

func convert(oldProgress string) (newProgress string) {
	if strings.EqualFold(oldProgress, "completed") {
		newProgress = "COMPLETE"
	} else if strings.EqualFold(oldProgress, "processing..") {
		newProgress = "START"
	} else if strings.EqualFold(oldProgress, "processing rk statistics") ||
		strings.EqualFold(oldProgress, "processing non rk statistics") ||
		strings.EqualFold(oldProgress, "processing gfp statistics") ||
		strings.EqualFold(oldProgress, "ONGOING_RK") ||
		strings.EqualFold(oldProgress, "ONGOING_NONRK") ||
		strings.EqualFold(oldProgress, "ONGOING_GFP") {
		newProgress = "ONGOING"
	} else { // STANDBY, COMPLETE
		newProgress = oldProgress
	}

	return
}

/*
// Decimal128 연산하는 법

func getReportsForVerification(ginCtx *gin.Context, date string, schIds []primitive.ObjectID) (err error, totalVerification FinalVerification) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	collOptions := &options.CollectionOptions{}
	collOptions.SetReadConcern(readconcern.Majority())
	collOptions.SetReadPreference(readpref.SecondaryPreferred(readpref.WithMaxStaleness(time.Second * 90)))
	collection := database.GFP.Collection("SummaryRevenueSharingReports", collOptions)

	entry.Debugf("[REVENUE] 리포트 검증. sch_ids: %v\n", schIds)

	filter := bson.M{
		"summaryRevenueSharingSchedule_id": bson.M{"$in": schIds},
		"dateForFileName":                  date,
	}

	options := options.FindOptions{}
	options.SetSort(bson.M{"_id": 1})

	ctx, _ := context.WithTimeout(context.TODO(), 30*time.Second)
	cur, err := collection.Find(ctx, filter, &options)

	if err != nil {
		entry.Errorf("[REVENUE] 리포트 검증 - 리포트 조회 실패. %v", err)
		return err, FinalVerification{} // 미생성
	}
	defer cur.Close(ctx)

	var sumOfKrwRevenue = decimal.NewFromFloat(0)
	var sumOfKrwNetRevenue = decimal.NewFromFloat(0)
	var sumOfUsdRevenue = decimal.NewFromFloat(0)
	var sumOfUsdNetRevenue = decimal.NewFromFloat(0)

	totalVerification.Date = date

	// Finding multiple documents returns a cursor
	// Iterating through the cursor allows us to decode documents one at a time
	for cur.Next(ctx) {

		// create a value into which the single document can be decoded
		var revenueReport RevenueReport
		err := cur.Decode(&revenueReport)
		if err != nil {
			entry.Error(err)
		}

		totalVerification.RowCnt += revenueReport.RkCntInFile

		for _, vf := range revenueReport.Verifications {
			if vf.StatsType == "AdProvider RK" {
				//entry.Debugf("[REVENUE] 리포트 검증. schId:%v vf:%v\n", revenueReport.SummaryRevenueSharingScheduleId, vf)

				totalVerification.SumOfImp += vf.SumOfImp
				totalVerification.SumOfClk += vf.SumOfClk

				sumOfKrwRevenueValue, _ := decimal.NewFromString(vf.SumOfKrwRevenue.String())
				sumOfKrwRevenue = sumOfKrwRevenue.Add(sumOfKrwRevenueValue)

				sumOfKrwNetRevenueValue, _ := decimal.NewFromString(vf.SumOfKrwNetRevenue.String())
				sumOfKrwNetRevenue = sumOfKrwNetRevenue.Add(sumOfKrwNetRevenueValue)

				sumOfUsdRevenueValue, _ := decimal.NewFromString(vf.SumOfUsdRevenue.String())
				sumOfUsdRevenue = sumOfUsdRevenue.Add(sumOfUsdRevenueValue)

				sumOfUsdNetRevenueValue, _ := decimal.NewFromString(vf.SumOfUsdNetRevenue.String())
				sumOfUsdNetRevenue = sumOfUsdNetRevenue.Add(sumOfUsdNetRevenueValue)
			}
		}

		//entry.Debugf("[REVENUE] 리포트 검증 토탈. sumOfKrwRevenue:%v\n", sumOfKrwRevenue)
		//entry.Debugf("[REVENUE] 리포트 검증 토탈. sumOfKrwNetRevenue:%v\n", sumOfKrwNetRevenue)
		//entry.Debugf("[REVENUE] 리포트 검증 토탈. sumOfUsdRevenue:%v\n", sumOfUsdRevenue)
		//entry.Debugf("[REVENUE] 리포트 검증 토탈. sumOfUsdNetRevenue:%v\n", sumOfUsdNetRevenue)

		v1, _ := primitive.ParseDecimal128(sumOfKrwRevenue.String())
		v2, _ := primitive.ParseDecimal128(sumOfKrwNetRevenue.String())
		v3, _ := primitive.ParseDecimal128(sumOfUsdRevenue.String())
		v4, _ := primitive.ParseDecimal128(sumOfUsdNetRevenue.String())

		totalVerification.SumOfKrwRevenue = v1
		totalVerification.SumOfKrwNetRevenue = v2
		totalVerification.SumOfUsdRevenue = v3
		totalVerification.SumOfUsdNetRevenue = v4
	}

	if err := cur.Err(); err != nil {
		entry.Error(err)
	}

	// Close the cursor once finished
	cur.Close(context.TODO())

	entry.Debugf("[REVENUE] 리포트 검증. 검증내용: %+v\n", util.PrettyPrint(totalVerification))

	return
}
*/
