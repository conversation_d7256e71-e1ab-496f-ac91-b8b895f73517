/*
	수익쉐어 리포트
*/
package revenue

import (
	"fmt"
	"net/http"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
)

type Login struct {
	User     string `form:"user" json:"user" xml:"user"  binding:"required"`
	Password string `form:"password" json:"password" xml:"password" binding:"required"`
}

var (
	rsController = RevenueController{}
)

func SetUpRoute(router *gin.Engine) {
	groupV1 := router.Group("/v1.0/revenue")
	groupV1.GET("/verification", rsController.Verification)

	groupV2 := router.Group("/v2.0/revenue")
	groupV2.GET("/status", rsController.GetStatus2)
	groupV2.GET("/download", setVersion, rsController.Download2)

	/*
		// 미들웨어를 라우팅보다 먼저 등록해야 함.
		// ACL 수행 전,후 처리를 하고 싶을 때
		groupV1.Use(func(ginCtx *gin.Context) {
			middlewares.MyAcl.CheckAclFunc(ginCtx.Writer, ginCtx.Request)
			ginCtx.Next()
		})

		// ACL을 전처리로 처리하고 싶을 때
		groupV1.Use(gin.WrapF(middlewares.MyAcl.CheckAclFunc))
		groupV1.GET("/v1/report/revenuesharing/make", func(ginCtx *gin.Context) {
			log.Debugf("와우.. acl-filter를 거쳤네.")
			ginCtx.JSON(http.StatusOK, "와우.. acl-filter를 거쳤네.")
		})*/
}

func setVersion(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	path := ginCtx.Request.URL.Path
	entry.Debugf("url....................:%s", path)

	if strings.Contains(path, "v1.0") {
		ginCtx.Set("version", 1)
	} else {
		ginCtx.Set("version", 2)
	}
}

func setupSampleRoute(router *gin.Engine) {
	sample := router.Group("/sample")

	/*
		파일 다운로드 샘플
	*/
	sample.GET("/someDataFromReader", func(ginCtx *gin.Context) {
		response, err := http.Get("https://raw.githubusercontent.com/gin-gonic/logo/master/color.png")
		if err != nil || response.StatusCode != http.StatusOK {
			ginCtx.Status(http.StatusServiceUnavailable)
			return
		}

		reader := response.Body
		contentLength := response.ContentLength
		contentType := response.Header.Get("Content-Type")

		extraHeaders := map[string]string{
			"Content-Disposition": `attachment; filename="gopher.png"`,
		}

		ginCtx.DataFromReader(http.StatusOK, contentLength, contentType, reader, extraHeaders)
	})

	/*
		JSON 바인딩 ({"user": "manu", "password": "123"})
	*/
	sample.POST("/loginJSON", func(c *gin.Context) {
		var json Login
		if err := c.ShouldBindJSON(&json); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		if json.User != "manu" || json.Password != "123" {
			c.JSON(http.StatusUnauthorized, gin.H{"status": "unauthorized"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"status": "you are logged in"})
	})

	/*
		static file serving
	*/
	router.MaxMultipartMemory = 8 << 20                  // 8 MiB
	sample.Static("/assets", "./sampleassets")           // upload 경로구만.
	sample.StaticFS("/static", http.Dir("samplestatic")) // 이 경로에 있는 파일들을 볼 수 있음.
	sample.StaticFile("/favicon.ico", "./sampleresources/favicon.ico")

	// 브라우저에서 http://localhost:8080/sample/uploadpage 입력
	sample.GET("/uploadpage", func(ginCtx *gin.Context) {
		fmt.Printf("업로드를 위한 페이지 HTML 렌더.................")

		// 여기에서 에러가 nil pointer 에러가 나고 있음.
		// 우리는 렌더링을 할게 아니기 때문에 이렇게 할 수 있다라고만 알고 넘어가자.
		ginCtx.HTML(http.StatusOK, "./samplestatic/uploadfiles.html", gin.H{
			"page": "upload single file",
		})
	})

	/*
		# upload 테스트를 위한 호출
		curl -X POST http://localhost:8080/upload \
		  -F "file=@/Users/<USER>/test.zip" \
		  -H "Content-Type: multipart/form-data"
	*/
	sample.POST("/upload", uploadSingle)

}

func uploadSingle(ginCtx *gin.Context) {
	// single file
	file, err := ginCtx.FormFile("file")
	if err != nil {
		ginCtx.String(http.StatusBadRequest, fmt.Sprintf("get form err: %s", err.Error()))
		return
	}

	fmt.Println(file.Filename)

	// Upload the file to specific dst.
	filename := filepath.Base(file.Filename)
	uploadPath := "./sampleassets/" + filename
	fmt.Println(filename)
	if err := ginCtx.SaveUploadedFile(file, uploadPath); err != nil {
		ginCtx.String(http.StatusBadRequest, fmt.Sprintf("upload file err: %s", err.Error()))
		return
	}

	ginCtx.JSON(200, gin.H{
		"status":    "posted",
		"file name": file.Filename,
	})
}
