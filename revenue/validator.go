package revenue

import (
	"context"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"oss.navercorp.com/da-ssp/gfp-api/database"
	"oss.navercorp.com/da-ssp/gfp-api/triplea"
)

type Partner struct {
	Id   primitive.ObjectID `bson:"_id,omitempty"`
	Name string             `bson:"name"`
}

type RevenueValidator struct {
}

/**
rsKeyGroupId와 publisherId의 매핑이 맞는지 확인
*/
func (validator *RevenueValidator) ValidateRsKeyGroupIdAndPublisherId(ginCtx *gin.Context, publisherId string, rsKeyGroupId string) bool {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debugf("[REVENUE] pubid:%s, rsKeyGroupid:%s", publisherId, rsKeyGroupId)

	collection := database.GFP.Collection("SummaryRevenueSharingSchedules")

	kgId, _ := primitive.ObjectIDFromHex(rsKeyGroupId)
	pId, _ := primitive.ObjectIDFromHex(publisherId)
	filter := bson.M{
		"keyGroup_id":  kgId,
		"publisher_id": pId,
	}

	findOptions := options.FindOneOptions{}
	findOptions.SetProjection(bson.M{"_id": 1})

	var doc bson.M

	ctx, _ := context.WithTimeout(context.TODO(), 30*time.Second)
	err := collection.FindOne(ctx, filter, &findOptions).Decode(&doc)
	if err != nil {
		entry.Error(err)
	}

	entry.Debugf("[REVENUE] PublisherID-RsKeyGroupId 매핑이 맞는지? doc._id:%v", doc["_id"])

	if doc["_id"] == nil {
		return false
	}

	return true
}

/**
scheduleId가 존재하는지 확인
*/
func (validator *RevenueValidator) ValidateScheduleId(ginCtx *gin.Context, scheduleId string) (bool, primitive.ObjectID) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debugf("[REVENUE] scheduleId:%s", scheduleId)

	collection := database.GFP.Collection("SummaryRevenueSharingSchedules")

	schId, _ := primitive.ObjectIDFromHex(scheduleId)
	filter := bson.M{
		"_id": schId,
	}

	findOptions := options.FindOneOptions{}
	findOptions.SetProjection(bson.M{"_id": 1, "publisher_id": 1})

	var doc bson.M

	ctx, _ := context.WithTimeout(context.TODO(), 30*time.Second)
	err := collection.FindOne(ctx, filter, &findOptions).Decode(&doc)
	if err != nil {
		entry.Error(err)
	}

	entry.Debugf("[REVENUE] ScheduleId가 존재하는지? doc._id:%v", doc["_id"])

	if doc["_id"] == nil {
		return false, primitive.ObjectID{}
	}

	return true, doc["publisher_id"].(primitive.ObjectID)
}

/**
rsKeyGroupId가 존재하는지 확인
*/
func (validator *RevenueValidator) ValidateRsKeyGroupId(ginCtx *gin.Context, rsKeyGroupId string) (bool, primitive.ObjectID) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	collection := database.GFP.Collection("SummaryRevenueSharingSchedules")

	if len(rsKeyGroupId) != 24 {
		entry.Errorf("Invalid rsKeyGroupId. length isn't 24")
		return false, primitive.ObjectID{}
	}

	keyGroup_id, err := primitive.ObjectIDFromHex(rsKeyGroupId)
	if err != nil {
		entry.Errorf("Invalid ObjectID")
		return false, primitive.ObjectID{}
	}

	filter := bson.M{"keyGroup_id": keyGroup_id}

	findOptions := options.FindOneOptions{}
	findOptions.SetProjection(bson.M{"_id": 1, "publisher_id": 1})

	var doc bson.M

	ctx, _ := context.WithTimeout(context.TODO(), 30*time.Second)
	err = collection.FindOne(ctx, filter, &findOptions).Decode(&doc)
	if err != nil {
		entry.Error(err)
		return false, primitive.ObjectID{}
	}

	entry.Debugf("[REVENUE] RsKeyGroupId가 존재하는지? doc:%v", doc)

	if doc["_id"] == nil {
		return false, primitive.ObjectID{}
	}

	return true, doc["publisher_id"].(primitive.ObjectID)
}

/**
rsKeyGroupId에 대응하는 publisherId에 대한 접근권한이 있는지 검사
*/
func (validator *RevenueValidator) CheckAccessRightsForPublisher(ginCtx *gin.Context, userId string, publisherId string) bool {
	// 권한 검증
	if !triplea.Authorization(ginCtx, userId, []string{publisherId}, []string{}) {
		return false
	}
	return true
}
