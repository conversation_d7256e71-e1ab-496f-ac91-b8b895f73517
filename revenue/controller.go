package revenue

/*
	// 별도의 로거로 RequestId 남기는 방법
	rsLog        = logger.GetLogger("rs")					// default 말고 rs logger 선택
	requestId, _ := ginCtx.Get("RequestId")					// context에서 RequetsId 추출
	rsLogEntry := rsLog.WithField("RequestId", requestId)	// 로거에 설정
*/

import (
	"fmt"
	"net/http"
	"os"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
	"reflect"
	"time"
	//"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"gopkg.in/go-playground/validator.v8"

	"oss.navercorp.com/da-ssp/gfp-api/config"
	gfpError "oss.navercorp.com/da-ssp/gfp-api/error"
)

type RevenueParam struct {
	RsKeyGroupId string `form:"rsKeyGroupId" json:"rsKeyGroupId" bson:"keyGroup_id" binding:"required,len=24"`

	// 일별 리포트는 8자리, 월별 리포트는 6자리
	Date string `form:"date" json:"date" binding:"required,len=8|len=6"`

	// ReportId string `bson:"reportId"`
	// Offset int64 `bson:"offset"`
	// Length int64 `bson:"length"`
	// CreatedAt time.Time `bson:"createdAt"`
	// ModifiedAt time.Time `bson:"modifiedAt"`
	// go lang rsValidator : https://godoc.org/gopkg.in/go-playground/validator.v8
	// Since time.Time `form:"since" binding:"required,sampleListable" time_format:"2006.01.02"`
	// Until time.Time `form:"until" binding:"required,gtfield=Since" time_format:"2006.01.02"`
}

type RevenueVerificationParam struct {
	// 일별 리포트는 8자리, 월별 리포트는 6자리
	Date string `form:"date" json:"date" binding:"required,len=8|len=6"`
}

type RevenueController struct {
	// services.RevenueService // 필드명 없이 타입만 선언하면 controller는 serviced이다 관계가 됨. is-a 관계
}

var (
	rootDir     string
	rsService   = RevenueService{}
	rsValidator = RevenueValidator{}
)

/*
	초기화
*/
func init() {
	// registerValidator()
	rootDir = config.GetConfig("download_root").(string) + "/report/revenuesharing2"
}

/**
	@apiVersion 1.0.0
 	@api {get} /v2.0/revenue/status 리포트 상태 조회 2.0
 	@apiName GetStatus2
	@apiGroup Revenue
	@apiDescription 날짜에 해당하는 리포트를 가져가기 전 상태를 조회합니다.<br/>

	@apiParam {String} userId GFP API 서버에 접근하기 위해 필요한 ID입니다.<br/>준비 과정에서 발급받은 userId로 설정합니다.<br/>ex) "apollo"
	@apiParam {String} timestamp Epoch 시간으로 1970년 1월 1일 00:00:00 협정 세계시 부터의 경과 시간을 초로 환산하여 정수로 나타낸 값<br/>ex) "1664955380"
	@apiParam {String{64}} encodedUserId {userId} + "_" + {timestamp}를 API_KEY로 hmac(sha256) 암호화한 뒤 hexadecimal 인코딩한 값<br/>ex) "d59d4e11c4470cda15746710fa2bfe282384fbc98155a3cc16b045ecd081d192"
	@apiParam {String{24}} rsKeyGroupId 매체 별로 리포트를 구분하기 위해 사용되는 ID입니다.<br/>준비 과정에서 발급받은 rsKeyGroupId로 설정합니다.<br/>ex) "5bfe4649762f05001995b8fe"
	@apiParam {String} date 다운로드 할 날짜.<br/>일별 리포트는 "yyyymmdd", 월별 리포트는"yyyymm" 포맷입니다.<br/>ex) "201909" or "20190901"
	@apiParamExample {form} Request-Example: Monthly
		http://test-api-gfp.da.navercorp.com:8080/v2.0/revenue/status?userId=v&timestamp=1664955548&encodedUserId=9495226073e9da4833107c2af554467c317310e4f742535b679e302c66f90bcf&rsKeyGroupId=5bf3a4e28399e7cabac24ac1&date=201909
	@apiParamExample {form} Request-Example: Daily
		http://test-api-gfp.da.navercorp.com:8080/v2.0/revenue/status?userId=apollo&timestamp=1664955548&encodedUserId=4a0c7bbbcc0ef8fe93dbe9ea9b03eee51e2e279b26e752d6ff020b2b9a09bd9a&rsKeyGroupId=5bfe4649762f05001995b8fe&date=20191011

	@apiSuccess {String} status <table>
							<thead><th>상태</th><th>설명</th></tr></thead>
							<tbody>
								<tr><td>NOT_YET</td><td>미생성</td></tr>
								<tr><td>STANDBY</td><td>대기중</td></tr>
								<tr><td>START</td><td>생성시작</td></tr>
								<tr><td>ONGOING</td><td>생성중</td></tr>
								<tr><td>COMPLETE</td><td>생성완료</td></tr>
								<tr><td>FAILURE</td><td>생성실패</td></tr>
							</tbody>
						</table>

	@apiSuccessExample {string} Response-Example
		HTTP/1.1 200 OK
		COMPLETE

	@apiError (401) Unauthorized
						Unauthenticated(1100). Invalid User ID or invalid encoded User ID.<br/>
						Unauthorized(1101). You don't have access rights to Publishers or AdProviders.<br/>
						ApiAccessDenied(1102). You don't have access right.
	@apiError (403) Forbidden You need to register server IP in the Pasta ACL Consumer.
	@apiError (400) BadRequest
						InvalidRsKeyGroupId(3003)
	@apiError (500) InternalServerError
						기타 에러(1000)

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"status": 400
			"code": "3003"
			"message": "invalid rsKeyGroupId"
		}
	@apiErrorExample {json} Error-Response-Example: Unauthorized
		HTTP/1.1 401 Unauthorized
		{
			"status": 401
			"code": "1100"
			"message": "Unauthenticated. userId: anonymous"
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		{
			"status": 500
			"code": "1000"
			"message": "unknown exception"
		}

	@apiSampleRequest	/v2.0/revenue/status
*/
func (revenueController *RevenueController) GetStatus2(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// binding validation
	var condition RevenueParam
	if err := ginCtx.ShouldBindWith(&condition, binding.Query); err != nil {
		// gin.H is a shortcut for map[string]interface{}
		msg := map[string]string{"params": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.InValidParam.BizError(msg))
		return
	}

	// 파라미터 파싱
	userId := ginCtx.Query("userId")
	keyGroupId := ginCtx.Query("rsKeyGroupId")
	date := ginCtx.Query("date")
	entry.Debugf("[REVENUE] 들어온 파라미터 keyGroupId:%s, date:%s", keyGroupId, date)

	// rsKeyGroupId에 대한 밸리데이션
	isValid, publisherId := rsValidator.ValidateRsKeyGroupId(ginCtx, keyGroupId)
	if !isValid {
		entry.Errorf("[REVENUE] rsKeyGroupId가 올바르지않음.")
		gfpError.ResponseError(ginCtx, gfpError.InvalidRsKeyGroupId.BizError(nil))
		return
	}

	// publisherId에 대한 접근권한 검사
	isValid = rsValidator.CheckAccessRightsForPublisher(ginCtx, userId, publisherId.Hex())
	if !isValid {
		entry.Errorf("[REVENUE] publisherId에 대한 접근권한 없음")
		param := map[string]string{"msg": fmt.Sprintf("you don't have access rights to publisherId(%s)", publisherId.Hex())}
		gfpError.ResponseError(ginCtx, gfpError.UnAuthorized.BizError(param))
		return
	}

	// 상태 조회
	rsReportState := rsService.GetStatus(ginCtx, keyGroupId, date)

	ginCtx.String(http.StatusOK, rsReportState)
}

/**
 	@apiVersion 1.0.0
 	@api {get} /v2.0/revenue/download 일별 또는 월별 다운로드 2.0
 	@apiName DownloadReport2
	@apiGroup Revenue
	@apiDescription 일별 또는 월별 리포트를 cvs 형식으로 다운로드합니다.<br/>
					Response Header의 Content-Type은 "application/vnd.ms-excel"입니다.<br/>
					Pasta > API Gateway를 통해 들어오는 경우에만 Header에 "x-apigw-consumer-id"가 자동 설정됩니다.<br/>
					<br/>
					1.0에 비해 몇몇 <b/>Revenue 관련 컬럼이 추가</b>되었습니다.<br/>
					자세한 사항은 GFP 담당자에게 문의 바랍니다.<br/>


	@apiHeader {String} [x-apigw-consumer-id]				Authentication key used to access from public network.<br/>If you don't have it, ask GFP administrator.<br/>Publisher in Naver is not applicable.
	@apiHeaderExample {json} Header-Example
		{
			"x-apigw-consumer-id": "yourConsumerId"
		}

	@apiParam {String} userId GFP API 서버에 접근하기 위해 필요한 ID입니다.<br/>준비 과정에서 발급받은 userId로 설정합니다.<br/>ex) "apollo"
	@apiParam {String} timestamp Epoch 시간으로 1970년 1월 1일 00:00:00 협정 세계시 부터의 경과 시간을 초로 환산하여 정수로 나타낸 값<br/>ex) "1664955380"
	@apiParam {String{64}} encodedUserId {userId} + "_" + {timestamp}를 API_KEY로 hmac(sha256) 암호화한 뒤 hexadecimal 인코딩한 값<br/>ex) "d59d4e11c4470cda15746710fa2bfe282384fbc98155a3cc16b045ecd081d192"
	@apiParam {String{24}} rsKeyGroupId 매체 별로 리포트를 구분하기 위해 사용되는 ID입니다.<br/>준비 과정에서 발급받은 rsKeyGroupId로 설정합니다.<br/>ex) "5bfe4649762f05001995b8fe"
	@apiParam {String} date 다운로드 할 날짜.<br/>일별 리포트는 "yyyymmdd", 월별 리포트는"yyyymm" 포맷입니다.<br/>ex) "201909" or "20190901"
	@apiParamExample {form} Request-Example: Monthly
		http://test-api-gfp.da.navercorp.com:8080/v2.0/revenue/download?userId=v&timestamp=1664955548&encodedUserId=9495226073e9da4833107c2af554467c317310e4f742535b679e302c66f90bcf&rsKeyGroupId=5bf3a4e28399e7cabac24ac1&date=201909
	@apiParamExample {form} Request-Example: Daily
		http://test-api-gfp.da.navercorp.com:8080/v2.0/revenue/download?userId=apollo&timestamp=1664955548&encodedUserId=4a0c7bbbcc0ef8fe93dbe9ea9b03eee51e2e279b26e752d6ff020b2b9a09bd9a&rsKeyGroupId=5bfe4649762f05001995b8fe&date=20191011

	@apiSuccess {attachement} file file attached
	@apiSuccessExample {json} Response-Header-Example: nothing in body
			Accept-Ranges: bytes
			Content-Disposition: attachment; filename="5c62662f5682d7001fee76d4_20191011.csv"
			Content-Length: 10370
			Content-Type: application/vnd.ms-excel
			Date: Wed, 04 Sep 2019 05:08:20 GMT
			Last-Modified: Tue, 03 Sep 2019 02:12:52 GMT
			X-Request-Id: e05a061a-b36e-47d3-98ae-7e43861e7d3e

	@apiError (401) Unauthorized
						Unauthenticated. Invalid User ID or invalid encoded User ID.<br/>
						Unauthorized. You don't have access rights to Publishers or AdProviders.
	@apiError (403) Forbidden You need to register server IP in the Pasta ACL Consumer.
	@apiError (400) BadRequest
						InvalidRsKeyGroupId(3003)
	@apiError (500) InternalServerError
						FileNotFound(3001)<br/>
						{otherwise..}(1000)

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"status": 400
			"code": "3003"
			"message": "invalid rsKeyGroupId"
		}
	@apiErrorExample {json} Error-Response-Example: Unauthorized
		HTTP/1.1 401 Unauthorized
		{
			"status": 401
			"code": "1100"
			"message": "Unauthenticated. userId: anonymous"
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		{
			"status": 500
			"code": "3001"
			"message": "file not found"
		}

	@apiSampleRequest	/v2.0/revenue/download
*/
func (revenueController *RevenueController) Download2(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// binding validation
	var condition RevenueParam
	if err := ginCtx.ShouldBindWith(&condition, binding.Query); err != nil {
		// gin.H is a shortcut for map[string]interface{}
		ginCtx.IndentedJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 파라미터 파싱
	userId := ginCtx.Query("userId")
	rsKeyGroupId := ginCtx.Query("rsKeyGroupId")
	date := ginCtx.Query("date")
	entry.Debugf("[REVENUE] 들어온 파라미터 rsKeyGroupId:%s, date:%s", rsKeyGroupId, date)

	// rsKeyGroupId에 대한 밸리데이션
	isValid, publisherId := rsValidator.ValidateRsKeyGroupId(ginCtx, rsKeyGroupId)
	if !isValid {
		entry.Errorf("[REVENUE] rsKeyGroupId가 올바르지않음.")
		gfpError.ResponseError(ginCtx, gfpError.InvalidRsKeyGroupId.BizError(nil))
		return
	}

	// publisherId에 대한 접근권한 검사
	isValid = rsValidator.CheckAccessRightsForPublisher(ginCtx, userId, publisherId.Hex())
	if !isValid {
		entry.Errorf("[REVENUE] publisherId에 대한 접근권한 없음")
		param := map[string]string{"msg": fmt.Sprintf("you don't have access rights to publisherId(%s)", publisherId.Hex())}
		gfpError.ResponseError(ginCtx, gfpError.UnAuthorized.BizError(param))
		return
	}

	// rsKeyGroupId에 대한 스케줄 조회
	schedule := rsService.GetScheduleByKeyGroupIdAndDate(ginCtx, rsKeyGroupId, date)

	// 경로 버저닝
	//pathPrefix := rootDir + "2" // '/home1/owfs_web/download/report/revenuesharing2'

	// 전체 경로 설정
	/*
		V 경우 30일치를 30개 파일로 나누기 때문에 결과적으로 일별로 파일을 가져가야 하므로
		date = "20191205"와 같은 형식으로 호출해야 함.
		만약 date = "201912"로 호출하면 월별 파일을 가져가게 됨.
	*/
	reportDir := rootDir + "/" + date[0:4] + "/" + date[4:6] + "/" + rsKeyGroupId // /home1/owfs_web/download/report/revenuesharing/2019/08/5bf3a4e28399e7cabac24ac1

	// 파일명 규칙(https://wiki.navercorp.com/pages/viewpage.action?pageId=524918720#id-03.%EB%B9%84%EC%A7%80%EB%8B%88%EC%8A%A4%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8-%ED%8C%8C%EC%9D%BC%EB%AA%85)
	// 		5bf3a4e28399e7cabac24ac1_20190828.csv        (period=DAY,   fileCreateCriteria=null,    interval=DAILY)   - 리포트를 매일 만듦
	// 		5bf3a4e28399e7cabac24ac1_201908.csv          (period=MONTH, fileCreateCriteria=MONTHLY, interval=DAILY)   - 리포트를 한 달에 한 번 만들고 한 달치를 한 파일로 만들되, 파일 내에서 일별로 분리
	// 		5bf3a4e28399e7cabac24ac1_201908_summary.csv  (period=MONTH, fileCreateCriteria=MONTHLY, interval=MONTHLY) - 리포트를 한 달에 한 번 만들고 한 달치를 한 파일로 만들되, 파일 내에서 일 데이터를 월로 합산해서 보여줌
	// 		5bf3a4e28399e7cabac24ac1_20190828.csv        (period=MONTH, fileCreateCriteria=DAILY,   interval=DAILY)   - 리포트를 한 달에 한 번 만드는데 한 달치를 일별 파일로 생성(예: 30일치를 30개 파일로)
	fileName := rsKeyGroupId + "_" + date
	if schedule.FileCreateCriteria == "MONTHLY" && schedule.Interval == "MONTHLY" {
		fileName += "_summary"
	}
	fileName += ".csv"

	fullPath := reportDir + "/" + fileName
	entry.Debugf("[REVENUE] fullPath:%s", fullPath)

	// 파일이 존재하는지 확인
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		entry.Errorf("[REVENUE] 파일이 존재하지 않음. %s", fullPath)
		gfpError.ResponseError(ginCtx, gfpError.FileNotFound.BizError(nil))
		return
	}

	/*
		파일 다운로드(간단 버전)
	*/
	ginCtx.FileAttachment(fullPath, fileName)

	/*
		파일 다운로드(상세한 버전)
	*/
	/*//Seems this headers needed for some browsers (for example without this headers Chrome will download files as txt)
	ginCtx.Header("Content-Description", "File Transfer")
	ginCtx.Header("Content-Transfer-Encoding", "binary")
	ginCtx.Header("Content-Disposition", "attachment; filename=" + fileName)
	ginCtx.Header("Content-Type", "application/vnd.msexcel")
	ginCtx.File(fullPath)*/
}

/**
	@apiVersion 1.0.0
 	@api {get} /v1.0/revenue/verification 리포트 검증 데이터 조회
 	@apiName Verification
	@apiGroup Revenue
	@apiDescription 날짜에 해당하는 리포트의 검증 데이터를 조회합니다. (재무 감사 용)<br/>

	@apiParam {String} userId API User ID<br/>ex) "adpost"
	@apiParam {String{64}} encodedUserId Encoded API User ID<br/>ex) "4a0c7bbbcc0ef8fe93dbe9ea9b03eee51e2e279b26e752d6ff020b2b9a09bd9a"
	@apiParam {String} date Date to verify. date format is yyyymmdd.<br/>
								ex) "20211115"
	@apiParamExample {form} Request-Example: Verification
		http://test-api-gfp.da.navercorp.com:8080/v1.0/revenue/verification?userId=adpost&encodedUserId=4a0c7bbbcc0ef8fe93dbe9ea9b03eee51e2e279b26e752d6ff020b2b9a09bd9a&date=20211115

	@apiSuccess {String} date 검증하고자 하는 날짜. 일별 파일의 경우 "20211125", 월별 파일의 경우 "202111"
	@apiSuccess {Number} verifications.rowCnt 파일 내 row 수
	@apiSuccess {Number} verifications.sumOfImp 노출수 합. MongoDB의 Integer64 타입.
	@apiSuccess {Number} verifications.sumOfClk 클릭수 합. MongoDB의 Integer64 타입.
	@apiSuccess {String} verifications.sumOfKrwRevenue KRW에 대한 수익의 합. MongoDB의 BigDecimal128 타입으로 문자열로 표현.
	@apiSuccess {String} verifications.sumOfKrwNetRevenue KRW에 대한 수수료 제외 수익의 합.MongoDB의 BigDecimal128 타입으로 문자열로 표현.

	@apiSuccessExample {json} Response-Example
		HTTP/1.1 200 OK
		{
			"date": "20211115",
			"rowCnt": 2,
			"sumOfImp": 10,
			"sumOfClk": 5,
			"sumOfKrwRevenue": "1.1",
			"sumOfKrwNetRevenue": "2.2"
		}

	@apiError (401) Unauthorized
						Unauthenticated(1100). Invalid User ID or invalid encoded User ID.<br/>
						Unauthorized(1101). You don't have access rights to Publishers or AdProviders.<br/>
						ApiAccessDenied(1102). You don't have access right.
	@apiError (403) Forbidden You need to register server IP in the Pasta ACL Consumer.
	@apiError (400) BadRequest
						InvalidScheduleId(3005)
	@apiError (500) InternalServerError
						기타 에러(1000)

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"status": 400
			"code": "3005"
			"message": "invalid scheduleId"
		}
	@apiErrorExample {json} Error-Response-Example: Unauthorized
		HTTP/1.1 401 Unauthorized
		{
			"status": 401
			"code": "1100"
			"message": "Unauthenticated. userId: anonymous"
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		{
			"status": 500
			"code": "1000"
			"message": "unknown exception"
		}

	@apiSampleRequest	/v1.0/revenue/verification
*/
func (revenueController *RevenueController) Verification(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// binding validation
	var condition RevenueVerificationParam
	if err := ginCtx.ShouldBindWith(&condition, binding.Query); err != nil {
		// gin.H is a shortcut for map[string]interface{}
		msg := map[string]string{"params": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.InValidParam.BizError(msg))
		return
	}

	// 파라미터 파싱
	date := ginCtx.Query("date")
	entry.Debugf("[REVENUE] 들어온 파라미터 date:%s", date)

	// 검증 데이터 조회
	err, totalVerification := rsService.Verification(ginCtx, date)
	if err != nil {
		entry.Errorf("[REVENUE] 검증 데이터 조회 에러 %s", err.Error())
		param := map[string]string{"msg": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.Default.BizError(param))
		return
	}

	/*
		// 클라이언트에 전달할 구조로 설정
		{
		    "date": "20211115",
			"rowCnt": 2,
			"sumOfImp": 10,
			"sumOfClk": 0,
			"sumOfKrwRevenue": "1.1",
			"sumOfKrwNetRevenue": "2.2"
		}
	*/
	result := gin.H{
		"date":               date,
		"rowCnt":             totalVerification.RowCnt,
		"sumOfImp":           totalVerification.SumOfImp,
		"sumOfClk":           totalVerification.SumOfClk,
		"sumOfKrwRevenue":    totalVerification.SumOfKrwRevenue,
		"sumOfKrwNetRevenue": totalVerification.SumOfKrwNetRevenue,
		//"sumOfUsdRevenue":    totalVerification.SumOfUsdRevenue,
		//"sumOfUsdNetRevenue": totalVerification.SumOfUsdNetRevenue,
	}

	ginCtx.IndentedJSON(http.StatusOK, result)
}

/**********************************************************************************************************************
샘플
*/
func (controller *RevenueController) SampleList(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// binding validation
	var listFilter RevenueParam
	if err := ginCtx.ShouldBindWith(&listFilter, binding.Query); err != nil {
		// gin.H is a shortcut for map[string]interface{}
		ginCtx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	list := rsService.List(ginCtx, listFilter)
	ginCtx.JSON(http.StatusOK, list)

	entry.Debug("수익쉐어 리포트 리스트 전달")
}

func (controller *RevenueController) SampleGet(ginCtx *gin.Context) {
	id := ginCtx.Param("id")
	report := rsService.Get(ginCtx, id)
	ginCtx.JSON(http.StatusOK, report)
}

func registerValidator() {
	// rsValidator 등록
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		v.RegisterValidation("sampleListable", sampleListable)
		// v.RegisterValidation("sampleListable", sampleListable)
	}
}

func sampleListable(
	v *validator.Validate, topStruct reflect.Value, currentStructOrField reflect.Value,
	field reflect.Value, fieldType reflect.Type, fieldKind reflect.Kind, param string,
) bool {
	if date, ok := field.Interface().(time.Time); ok {
		today := time.Now()
		if today.After(date) {
			return false
		}
	}
	return true
}
