airflow:
  dags:
    path: /opt/airflow/dags
    gitSync:
      branch: develop
  airflow:
    config:
      AIRFLOW__CORE__SQL_ALCHEMY_POOL_SIZE: "10"
      AIRFLOW__CORE__SQL_ALCHEMY_MAX_OVERFLOW: "100"
      AIRFLOW__CORE__PARALLELISM: "128"
      AIRFLOW__CORE__MAX_ACTIVE_TASKS_PER_DAG: "128"
      AIRFLOW__CORE__DEFAULT_POOL_TASK_SLOT_COUNT: "128"
      AIRFLOW__CELERY__WORKER_AUTOSCALE: "32,4"
      AIRFLOW__CELERY_BROKER_TRANSPORT_OPTIONS__VISIBILITY_TIMEOUT: "36000"
      AIRFLOW__WEBSERVER__BASE_URL: "http://airflow-web.nam-batch.svc.ad1.io.navercorp.com:8080"
      AIRFLOW__WEBSERVER__INSTANCE_NAME: "DAGs (NAM-test)"
      AIRFLOW__SMTP__SMTP_MAIL_FROM: <EMAIL>
      AIRFLOW__CORE__PLUGINS_FOLDER: "/opt/airflow/dags/repo/plugins"
      PYTHONPATH: "/opt/airflow/dags/repo/config"
      DEPLOY_DATETIME: "20220412"
  web:
    replicas: 1
  scheduler:
    replicas: 1
    resources:
      limits:
        ephemeral-storage: "16Gi"
    # extraVolumes:
    #   - name: logs
    #     flexVolume:
    #       driver: "naver/nubes-fuse-driver"
    #       options:
    #         bucket: "nam-batch-airflow-logs"
    #         clearCache: "false"
    # extraVolumeMounts:
    #   - name: logs
    #     mountPath: /opt/airflow/logs
  workers:
    replicas: 4
    # extraVolumes:
    #   - name: logs
    #     flexVolume:
    #       driver: "naver/nubes-fuse-driver"
    #       options:
    #         bucket: "nam-batch-airflow-logs"
    #         clearCache: "false"
    # extraVolumeMounts:
    #   - name: logs
    #     mountPath: /opt/airflow/logs
  externalRedis:
    databaseNumber: 1
  externalDatabase:
    user: airflow
    database: airflow
    passwordSecret: postgresql
    passwordSecretKey: postgresql-password

