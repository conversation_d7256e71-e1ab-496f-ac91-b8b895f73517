# profile for developments. Customize the following items
#
#   - Helm Release Name
#     - 'airflow' for 'test' profile
#     - 'airflow-dev' for 'dev' profile
#   - Prepare new nubes bucket for airflow logs if needed
#   - Create new postgres user/database, and register password with the postgresql secret
#   - Identify git branch for git-sync
#   - Values
#     - airflow.dags.gitSync.branch
#     - airflow.airflow.config.AIRFLOW__WEBSERVER__BASE_URL
#     - airflow.airflow.extraVolumes/extraVolumeMounts
#     - externalRedis.databaseNumber
#       - 1 for 'test' profile
#       - 5 for 'dev' profile
#       - ...
#     - externalDatabase
#       - user
#       - database
#       - passwordSecretKey
#
# Note:
#   - N2C Ceph volumes are dynamically extendable
#     - postgresql/redis PVs
#
airflow:
  dags:
    path: /opt/airflow/dags
    gitSync:
      branch: okjo
  airflow:
    config:
      AIRFLOW__CORE__SQL_ALCHEMY_POOL_SIZE: "10"
      AIRFLOW__CORE__SQL_ALCHEMY_MAX_OVERFLOW: "100"
      AIRFLOW__CORE__PARALLELISM: "128"
      AIRFLOW__CORE__MAX_ACTIVE_TASKS_PER_DAG: "128"
      AIRFLOW__CORE__DEFAULT_POOL_TASK_SLOT_COUNT: "128"
      AIRFLOW__CELERY__WORKER_AUTOSCALE: "32,4"
      AIRFLOW__CELERY_BROKER_TRANSPORT_OPTIONS__VISIBILITY_TIMEOUT: "28800"
      AIRFLOW__WEBSERVER__BASE_URL: "http://airflow-dev-web.nam-batch.svc.ad1.io.navercorp.com:8080"
      AIRFLOW__WEBSERVER__INSTANCE_NAME: "DAGs (NAM-dev)"
      AIRFLOW__SMTP__SMTP_MAIL_FROM: <EMAIL>
      AIRFLOW__CORE__PLUGINS_FOLDER: "/opt/airflow/dags/repo/plugins"
      PYTHONPATH: "/opt/airflow/dags/repo/config"
      DEPLOY_DATETIME: "20220412"
  web:
    replicas: 1
  scheduler:
    replicas: 1
    resources:
      limits:
        ephemeral-storage: "16Gi"
    # extraVolumes:
    #   - name: logs
    #     flexVolume:
    #       driver: "naver/nubes-fuse-driver"
    #       options:
    #         bucket: "nam-batch-airflow-dev-logs"
    #         clearCache: "false"
    # extraVolumeMounts:
    #   - name: logs
    #     mountPath: /opt/airflow/logs
  workers:
    replicas: 2
    # extraVolumes:
    #   - name: logs
    #     flexVolume:
    #       driver: "naver/nubes-fuse-driver"
    #       options:
    #         bucket: "nam-batch-airflow-dev-logs"
    #         clearCache: "false"
    # extraVolumeMounts:
    #   - name: logs
    #     mountPath: /opt/airflow/logs
  externalRedis:
    databaseNumber: 5
  externalDatabase:
    user: airflow_dev
    database: airflow_dev
    passwordSecret: postgresql
    passwordSecretKey: postgresql-airflow-dev-password

