worker 에 ceph volume 을 logs 로 잡아주기
==============================================

- airflow charts 가 community version 인 것 확인하기
  - airflow-helm.github.io, ver 8.5.3

- 먼저 기존 values 내용중 logs volume 있는 것을 제거

- rm -rf charts
- helm3 dep up
- (cd charts && tar xzf airflow-8.5.3.tgz && rm airflow-8.5.3.tgz)
- cp -f patches/airflow-helm.github.io-8.5.3-worker-statefulset.yaml charts/airflow/templates/worker/worker-statefulset.yaml

- ceph volume 으로 설정되지 않은, 이미 배포된 instance 가 있으면, uninstall
  - volumeClaimTemplates 추가는 update 되지 않는다...
- scripts/deploy.sh 로 배포
  - scripts/deploy.sh dev airflow-dev
  - scripts/deploy.sh prod airflow

- PVC 권한 문제로 worker 가 제대로 뜨지 않는다면
  - kubectl get events 로 오류 내용 확인
- 아래 문서 참고하여 
  - https://pages.oss.navercorp.com/n2c/docs/topics/storage/ceph-rbd/appendix/
- 다음을 각 pvc 마다 실행
  - kubectl annotate pvc logs-airflow-worker-0 --overwrite uid=50000 gid=0
- scripts/deploy.sh 로 다시 배포
