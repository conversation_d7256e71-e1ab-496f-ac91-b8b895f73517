#!/bin/bash
# vim: ft=bash:

# HELM 3 CLI installation required:
#
# Usage: deploy.sh <profile> <helm-upgrade-options> <release-name>
#  or
# Usage: HELM=helm3 <profile> deploy.sh <helm-upgrade-options> <release-name>
#
# ex)
# $ HELM=helm3 scripts/deploy.sh dev --debug --dry-run airflow-ssp-batch-cm
# $ HELM=helm3 scripts/deploy.sh prod -f my_values.yml airflow-ssp-batch-cm
# $ HELM=helm3 scripts/deploy.sh test \
# > --set airflow.airflow.dbMigrations.enabled=false \
# > airflow-release-name

SOURCE_DIR=$(cd -P -- "$(dirname -- "${BASH_SOURCE[0]}")" && pwd -P)
CHART_DIR=$(dirname "$SOURCE_DIR")

HELM=${HELM:-helm3}

PROFILE="$1"
shift

PROFILE_FILE="$CHART_DIR/profiles/${PROFILE}.yaml"

set -exuo pipefail
cd "$CHART_DIR"

if [ ! -f "$PROFILE_FILE" ]; then
    echo profile \"${PROFILE_FILE}\" not found
    exit 1
fi

set -ex

# '--set airflow.airflow.defaultSecurityContext=null'
#   - remove 'fsGroup: 0' from pod security context
#   - To mount nubes flexVolume, 'fsGroup' setting must be removed
#$HELM upgrade -i -f "$PROFILE_FILE" --set airflow.airflow.defaultSecurityContext=null "$@" .

# nubes volume changed to ceph volume...
$HELM upgrade -i -f "$PROFILE_FILE" "$@" .
