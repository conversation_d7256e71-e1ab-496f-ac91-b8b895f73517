{{/*
Create default object name
*/}}
{{- define "defaultName" -}}
{{- printf "%s" .Release.Name | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create labels
*/}}
{{- define "defaultLabels" -}}
app.kubernetes.io/name: {{ .Values.ncc.application }}
app.kubernetes.io/instance: {{ .Values.ncc.instance }}
{{- end -}}

{{/*
Create selectors
*/}}
{{- define "defaultSelectors" -}}
app.kubernetes.io/name: {{ .Values.ncc.application }}
app.kubernetes.io/instance: {{ .Values.ncc.instance }}
{{- end -}}
