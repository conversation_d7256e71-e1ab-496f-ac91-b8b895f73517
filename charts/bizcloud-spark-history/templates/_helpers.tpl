{{/*
Create default object name
*/}}
{{- define "defaultName" -}}
{{- printf "%s" .Release.Name | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create default labels
*/}}
{{- define "labels" -}}
app.kubernetes.io/name: {{ .Values.n2c.application }}
app.kubernetes.io/instance: {{ .Values.n2c.instance }}
{{- end -}}

{{/*
Create default selectors
*/}}
{{- define "selectors" -}}
app.kubernetes.io/name: {{ .Values.n2c.application }}
app.kubernetes.io/instance: {{ .Values.n2c.instance }}
{{- end -}}
