{{- $defaultName := include "defaultName" . }}
{{- $labels := include "labels" . }}
{{- $selectors := include "selectors" . }}
{{- $his_opts := printf "-Dspark.history.ui.port=%s" ( .Values.server.port | toString ) }}
{{- if .Values.server.config.hs.logDir }}
  {{- $his_opts = printf "%s -Dspark.history.fs.logDirectory=%s" $his_opts .Values.server.config.hs.logDir }}
{{- end }}
{{- if .Values.server.config.hs.cleaner.enabled }}
  {{- $his_opts = printf "%s -Dspark.history.fs.cleaner.enabled=true" $his_opts }}
  {{- $his_opts = printf "%s -Dspark.history.fs.cleaner.interval=%s" $his_opts ( .Values.server.config.hs.cleaner.interval | default "1d" | toString ) }}
  {{- $his_opts = printf "%s -Dspark.history.fs.cleaner.maxAge=%s" $his_opts ( .Values.server.config.hs.cleaner.maxAge | default "7d" | toString ) }}
{{- end }}
{{- if .Values.server.config.hs.numReplayThreads }}
  {{- $his_opts = printf "%s -Dspark.history.fs.numReplayThreads=%s" $his_opts ( .Values.server.config.hs.numReplayThreads | toString ) }}
{{- end }}
{{- if .Values.server.config.hs.maxDiskUsage }}
  {{- $his_opts = printf "%s -Dspark.history.store.maxDiskUsage=%s" $his_opts ( .Values.server.config.hs.maxDiskUsage | toString ) }}
{{- end }}
{{- if hasKey .Values.server.config "spark_history_opts" }}
{{- if .Values.server.config.spark_history_opts.kerberos.enabled }}
  {{- $his_opts = printf "%s -Dspark.history.kerberos.enabled=true" $his_opts }}
  {{- $his_opts = printf "%s -Dspark.history.kerberos.principal=%s" $his_opts ( .Values.server.config.spark_history_opts.kerberos.principal | toString ) }}
  {{- $his_opts = printf "%s -Dspark.history.kerberos.keytab=%s" $his_opts ( .Values.server.config.spark_history_opts.kerberos.keytab | toString ) }}
{{- end }}
{{- end }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $defaultName }}
  labels:
{{ $labels | indent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels:
{{ $selectors | indent 6 }}
  template:
    metadata:
      annotations:
        deploy/tag: "{{ .Values.deployTag }}"
      labels:
{{ $labels | indent 8 }}
    spec:
      containers:
        {{- with .Values.server }}
        - name: history-server
          image: "{{ .image.name }}:{{ .image.tag }}"
          imagePullPolicy: Always
          {{- with .command }}
          command:
{{ toYaml . | trim | indent 12 }}
          {{- end }}
          {{- with .args }}
          args:
{{ toYaml . | trim | indent 12 }}
          {{- end }}
          env:
            - name: SPARK_HISTORY_OPTS
              value: "{{ $his_opts }}"
          {{- with .config.hadoop_user }}
            - name: HADOOP_USER_NAME
              value: "{{ . }}"
          {{- end }}
          {{- with .config.hadoop_conf_dir }}
            - name: HADOOP_CONF_DIR
              value: "{{ . }}"
          {{- end }}
          {{- with .env }}
{{ toYaml . | trim | indent 12 }}
          {{- end }}
          ports:
            - name: spark-ui
              containerPort: {{ .port }}
          {{- with .resources }}
          resources:
{{ toYaml . | trim | indent 12 }}
          {{- end }}
          {{- if .extras }}
{{ toYaml .extras | trim | indent 10 }}
          {{- end }}
          volumeMounts:
            {{- if $.Values.c3s }}
          - name: c3s-krb5-conf
            mountPath: /etc/krb5.conf
            subPath: krb5.conf
          - name: c3s-keytab
            mountPath: /home1/irteam/c3s/{{ $.Values.c3s.account }}.keytab
            subPath: {{ $.Values.c3s.account }}.keytab
            {{- end }}
        {{- end }}
      volumes:
        {{- if $.Values.c3s }}
        - name: c3s-krb5-conf
          configMap:
            name: c3s-krb5-conf
        - name: c3s-keytab
          secret:
            secretName: c3s-{{ $.Values.c3s.account }}-keytab
        {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ $defaultName }}
  labels:
{{ $labels | indent 4 }}
spec:
  type: LoadBalancer
  ports:
    - port: {{ .Values.server.port }}
      targetPort: {{ .Values.server.port }}
  selector:
{{ $selectors | indent 4 }}
