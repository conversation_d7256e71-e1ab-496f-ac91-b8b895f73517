#!/bin/bash
# vim: ft=bash:

# Usage: deploy.sh <profile> <helm-upgrade-options> <release-name>
#  or
# Usage: HELM=helm3 <profile> deploy.sh <helm-upgrade-options> <release-name>
#
# ex)
# $ HELM=helm3 scripts/deploy.sh dev --debug --dry-run airflow-ssp-batch-cm
# $ HELM=helm3 scripts/deploy.sh prod -f my_values.yml airflow-ssp-batch-cm

SOURCE_DIR=$(cd -P -- "$(dirname -- "${BASH_SOURCE[0]}")" && pwd -P)
CHART_DIR=$(dirname "$SOURCE_DIR")

HELM=${HELM:-ncc helm}

PROFILE="$1"
shift

PROFILE_FILE="$CHART_DIR/profiles/${PROFILE}.yaml"

set -exuo pipefail
cd "$CHART_DIR"

if [ ! -f "$PROFILE_FILE" ]; then
    echo profile \"${PROFILE_FILE}\" not found
    exit 1
fi

[ -f requirements.yaml -a ! -f requirements.lock ] && $HELM dep build
$HELM upgrade -i -f "$PROFILE_FILE" "$@" .
