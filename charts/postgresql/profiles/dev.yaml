postgresql:
  image:
    debug: true
    registry: reg.navercorp.com
    repository: gfp/bitnami-pg
    tag: 11.10.0-debian-10-r0
    pullPolicy: Always
  # See scripts/secret.sh
  #   'postgresql-password' for 'postgres' admin if postgresqlUsername == 'postgres' (default)
  #   'postgresql-postgres-password' for 'postgres' admin if postgreqlUsername != 'postgres'
  existingSecret: postgresql
  #postgresqlUsername: airflow_ssp_batch
  #postgresqlDatabase: airflow_ssp_batch
  postgresqlExtendedConf:
    sharedBuffers: "1500MB"
    maxConnections: "200"
  # shmVolume size set to 1Gi
  shmVolume:
    enabled: true
  resources:
    limits:
      cpu: 8
      memory: "8Gi"
  persistence:
    storageClass: rbd-hdd
    size: "40Gi"
    # 'annotations' seems not to work (Permission denied)
    # annotations:
    #  uid: "postgres"
    #  gid: "postgres"
  # Do not set 'fsGroup' for Pod
  # https://pages.oss.navercorp.com/naver-container-cluster/docs/3/6.storage/4.ceph-rbd/appendix/#6-root가-아닌-유저로-볼륨-사용하기
  # But, annotations for setting uid/gid don't work, securityContext (fsGroup) is enabled
  securityContext:
    enabled: true
  replication:
    enabled: false
