# See documentation of dev profile values
postgresql:
  fullnameOverride: postgresql
  existingSecret: postgresql
  postgresqlUsername: airflow
  postgresqlDatabase: airflow
  # metrics:
  #   enabled: true
  image:
    registry: reg.navercorp.com
    repository: gfp/bitnami-pg
    tag: 11.10.0-debian-10-r0
    pullPolicy: Always
  postgresqlExtendedConf:
    sharedBuffers: "3GB"
    maxConnections: "300"
  shmVolume:
    enabled: true
  resources:
    limits:
      cpu: 6
      memory: "12Gi"
  service:
    type: LoadBalancer
  persistence:
    storageClass: rbd-hdd
    size: "200Gi"
  securityContext:
    enabled: true
  replication:
    enabled: false
