{"name": "@ssp/webhdfs", "version": "1.2.2", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "@ssp/webhdfs", "version": "1.2.2", "license": "MIT", "dependencies": {"buffer-stream-reader": "^0.1.1", "extend": "^3.0.0", "request": "^2.74.0"}, "devDependencies": {"mocha": "^2.3.3", "must": "^0.13.1", "sinon": "^1.17.2", "webhdfs-proxy": "^0.1.2", "webhdfs-proxy-memory": "^0.1.2"}}, "node_modules/ajv": {"version": "5.5.2", "resolved": "https://registry.npmjs.org/ajv/-/ajv-5.5.2.tgz", "integrity": "sha1-c7Xuyj+rZT49P5Qis0GtQiBdyWU=", "license": "MIT", "dependencies": {"co": "^4.6.0", "fast-deep-equal": "^1.0.0", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.3.0"}}, "node_modules/asn1": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.3.tgz", "integrity": "sha1-2sh4dxPJlmhJ/IGAd36+nB3fO4Y=", "license": "MIT"}, "node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/async": {"version": "0.9.2", "resolved": "https://registry.npmjs.org/async/-/async-0.9.2.tgz", "integrity": "sha1-rqdNXmHB+JlhO/ZL2mbUx48v0X0=", "dev": true, "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "license": "MIT"}, "node_modules/aws-sign2": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/aws4": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.6.0.tgz", "integrity": "sha1-g+9cqGCysy5KDe7e6MdxudtXRx4=", "license": "MIT"}, "node_modules/bcrypt-pbkdf": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.1.tgz", "integrity": "sha1-Y7xdy2EzG5K8Bf1SiVPDNGKgb40=", "license": "BSD-3-<PERSON><PERSON>", "optional": true, "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/bluebird": {"version": "2.11.0", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-2.11.0.tgz", "integrity": "sha1-U0uQM8AiyVecVro7Plpcqvu2UOE=", "dev": true, "license": "MIT"}, "node_modules/boom": {"version": "4.3.1", "resolved": "https://registry.npmjs.org/boom/-/boom-4.3.1.tgz", "integrity": "sha1-T4owBctKfjiJ90kDD9JbluAdLjE=", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"hoek": "4.x.x"}, "engines": {"node": ">=4.0.0"}}, "node_modules/buffer-stream-reader": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/buffer-stream-reader/-/buffer-stream-reader-0.1.1.tgz", "integrity": "sha1-yov5NjHe7di4+MO7RJkcwwlR4lk=", "license": "MIT"}, "node_modules/caseless": {"version": "0.12.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=", "license": "Apache-2.0"}, "node_modules/charenc": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/charenc/-/charenc-0.0.2.tgz", "integrity": "sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/co": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "license": "MIT", "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/combined-stream": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.6.tgz", "integrity": "sha1-cj599ugBrFYTETp+RFqbactjKBg=", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.3.0.tgz", "integrity": "sha1-/UMOiJgy7DU7ms0d4hfBHLPu+HM=", "dev": true, "engines": {"node": ">= 0.6.x"}}, "node_modules/core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "license": "MIT"}, "node_modules/crypt": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/crypt/-/crypt-0.0.2.tgz", "integrity": "sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "node_modules/cryptiles": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/cryptiles/-/cryptiles-3.1.2.tgz", "integrity": "sha1-qJ+7Ig9c4l7FboxKqKT9e1sNKf4=", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"boom": "5.x.x"}, "engines": {"node": ">=4.0.0"}}, "node_modules/cryptiles/node_modules/boom": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/boom/-/boom-5.2.0.tgz", "integrity": "sha512-Z5BTk6ZRe4tXXQlkqftmsAUANpXmuwlsF5Oov8ThoMbQRzdGTA1ngYRW160GexgOgjsFOKJz0LYhoNi+2AMBUw==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"hoek": "4.x.x"}, "engines": {"node": ">=4.0.0"}}, "node_modules/dashdash": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/debug": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha1-+HBX6ZWxofauaklgZkE3vFbwOdo=", "dev": true, "license": "MIT", "dependencies": {"ms": "0.7.1"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/diff": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/diff/-/diff-1.4.0.tgz", "integrity": "sha1-fyjS657nsVqX79ic5j3P2qPMur8=", "dev": true, "engines": {"node": ">=0.3.1"}}, "node_modules/ecc-jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.1.tgz", "integrity": "sha1-D8c6ntXw1Tw4GTOYUj735UN3dQU=", "license": "MIT", "optional": true, "dependencies": {"jsbn": "~0.1.0"}}, "node_modules/egal": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/egal/-/egal-1.3.0.tgz", "integrity": "sha1-IKGc+oDOlzP4QTY10AQmQfQpGHs=", "dev": true, "dependencies": {"kindof": ">= 2.0.0 < 3"}}, "node_modules/escape-string-regexp": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.2.tgz", "integrity": "sha1-Tbwv5nTnGUnK8/smlc5/LcHZqNE=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/extend": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.1.tgz", "integrity": "sha1-p1Xqe8Gt/MWjHOfnYtuq3F5jZEQ=", "license": "MIT"}, "node_modules/extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=", "engines": ["node >=0.6.0"], "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-1.1.0.tgz", "integrity": "sha1-wFNHeBfIa1HaqFPIHgWbcz0CNhQ=", "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz", "integrity": "sha1-1RQsDK7msRifh9OnYREGT4bIu/I=", "license": "MIT"}, "node_modules/forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/form-data": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.3.2.tgz", "integrity": "sha1-SXBJi+YEwgwAXU9cI67NIda0kJk=", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/formatio": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/formatio/-/formatio-1.1.1.tgz", "integrity": "sha1-XtPM1jZVEJc4NGXZlhmRAOhhYek=", "deprecated": "This package is unmaintained. Use @sinonjs/formatio instead", "dev": true, "dependencies": {"samsam": "~1.1"}}, "node_modules/getpass": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/glob": {"version": "3.2.11", "resolved": "https://registry.npmjs.org/glob/-/glob-3.2.11.tgz", "integrity": "sha1-Spc/Y1uRkPcV0QmH1cAP0oFevj0=", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "license": "BSD", "dependencies": {"inherits": "2", "minimatch": "0.3"}, "engines": {"node": "*"}}, "node_modules/growl": {"version": "1.9.2", "resolved": "https://registry.npmjs.org/growl/-/growl-1.9.2.tgz", "integrity": "sha1-Dqd0NxXbjY3ixe3hd14bRayFwC8=", "dev": true, "license": "MIT"}, "node_modules/har-schema": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=", "license": "ISC", "engines": {"node": ">=4"}}, "node_modules/har-validator": {"version": "5.0.3", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-5.0.3.tgz", "integrity": "sha1-ukAsJmGU8VlW7xXg/PJCmT9qff0=", "deprecated": "this library is no longer supported", "license": "ISC", "dependencies": {"ajv": "^5.1.0", "har-schema": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/hawk": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/hawk/-/hawk-6.0.2.tgz", "integrity": "sha512-miowhl2+U7Qle4vdLqDdPt9m09K6yZhkLDTWGoUiUzrQCn+mHHSmfJgAyGaLRZbPmTqfFFjRV1QWCW0VWUJBbQ==", "deprecated": "This module moved to @hapi/hawk. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"boom": "4.x.x", "cryptiles": "3.x.x", "hoek": "4.x.x", "sntp": "2.x.x"}, "engines": {"node": ">=4.5.0"}}, "node_modules/hoek": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/hoek/-/hoek-4.2.1.tgz", "integrity": "sha512-QLg82fGkfnJ/4iy1xZ81/9SIJiq1NGFUMGs6ParyjBZr6jW2Ufj/snDqTHixNlHdPNwN2RLVD0Pi3igeK9+JfA==", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=4.0.0"}}, "node_modules/http-signature": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/inherits": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true, "license": "ISC"}, "node_modules/is-buffer": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==", "dev": true, "license": "MIT"}, "node_modules/is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true, "license": "ISC"}, "node_modules/isstream": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=", "license": "MIT"}, "node_modules/jade": {"version": "0.26.3", "resolved": "https://registry.npmjs.org/jade/-/jade-0.26.3.tgz", "integrity": "sha1-jxDXl32NefL2/4YqgbBRPMslaGw=", "deprecated": "Jade has been renamed to pug, please install the latest version of pug instead of jade", "dev": true, "dependencies": {"commander": "0.6.1", "mkdirp": "0.3.0"}, "bin": {"jade": "bin/jade"}}, "node_modules/jade/node_modules/commander": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/commander/-/commander-0.6.1.tgz", "integrity": "sha1-+mihT2qUXVTbvlDYzbMyDp47GgY=", "dev": true, "engines": {"node": ">= 0.4.x"}}, "node_modules/jade/node_modules/mkdirp": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.3.0.tgz", "integrity": "sha1-G79asbqCevI1dRQ0kEJkVfSB/h4=", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dev": true, "license": "MIT/X11", "engines": {"node": "*"}}, "node_modules/jayschema": {"version": "0.2.8", "resolved": "https://registry.npmjs.org/jayschema/-/jayschema-0.2.8.tgz", "integrity": "sha1-6niD9QGkzce5H0iIn1d06kMVnxc=", "deprecated": "this project is no longer maintained", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"when": "~3.1.0"}, "bin": {"jayschema": "bin/validate.js"}, "engines": {"node": ">=0.6.6"}}, "node_modules/jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM=", "license": "MIT", "optional": true}, "node_modules/json-schema": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM="}, "node_modules/json-schema-traverse": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz", "integrity": "sha1-NJptRMU6Ud6JtAgFxdXlm0F9M0A=", "license": "MIT"}, "node_modules/json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=", "license": "ISC"}, "node_modules/jsprim": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/jsprim/-/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "node_modules/kindof": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/kindof/-/kindof-2.0.0.tgz", "integrity": "sha1-wzW69gOnfMN/i0Brc7ZGP9vfGr4=", "dev": true}, "node_modules/lodash._createwrapper": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/lodash._createwrapper/-/lodash._createwrapper-3.2.0.tgz", "integrity": "sha1-30U+ZkFjIXuJWkVAZa8cR6DqPE0=", "dev": true, "license": "MIT", "dependencies": {"lodash._root": "^3.0.0"}}, "node_modules/lodash._root": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash._root/-/lodash._root-3.0.1.tgz", "integrity": "sha1-+6HEUkwZ7ppfgTa0YJ8BfPTe1pI=", "dev": true, "license": "MIT"}, "node_modules/lodash.wrap": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/lodash.wrap/-/lodash.wrap-3.0.1.tgz", "integrity": "sha1-P82L74Z7LsjCG6xjjYFhgFk/cas=", "dev": true, "license": "MIT", "dependencies": {"lodash._createwrapper": "^3.0.0"}}, "node_modules/lolex": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/lolex/-/lolex-1.3.2.tgz", "integrity": "sha1-fD2mL/yzDw9agKJWbKJORdigHzE=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/lru-cache": {"version": "2.7.3", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.7.3.tgz", "integrity": "sha1-bUUk6LlV+V1PW1iFHOId1y+06VI=", "dev": true, "license": "ISC"}, "node_modules/md5": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/md5/-/md5-2.2.1.tgz", "integrity": "sha1-U6s41f48iJG6RlMp6iP6wFQBJvk=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"charenc": "~0.0.1", "crypt": "~0.0.1", "is-buffer": "~1.1.1"}}, "node_modules/mime-db": {"version": "1.33.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.33.0.tgz", "integrity": "sha512-BHJ/EKruNIqJf/QahvxwQZXKygOQ256myeN/Ew+THcAa5q+PjyTTMMeNQC4DZw5AwfvelsUrA6B67NKMqXDbzQ==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.18", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.18.tgz", "integrity": "sha512-lc/aahn+t4/SWV/qcmumYjymLsWfN3ELhpmVuUFjgsORruuZPVSwAQryq+HHGvO/SI2KVX26bx+En+zhM8g8hQ==", "license": "MIT", "dependencies": {"mime-db": "~1.33.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimatch": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz", "integrity": "sha1-J12O2qxPG7MyZHIInnlJyDlGmd0=", "deprecated": "Please update to minimatch 3.0.2 or higher to avoid a RegExp DoS issue", "dev": true, "license": "MIT", "dependencies": {"lru-cache": "2", "sigmund": "~1.0.0"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true, "license": "MIT"}, "node_modules/mkdirp": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dev": true, "license": "MIT", "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/mocha": {"version": "2.5.3", "resolved": "https://registry.npmjs.org/mocha/-/mocha-2.5.3.tgz", "integrity": "sha1-FhvlvetJZ3HrmzV0UFC2IrWu/Fg=", "dev": true, "license": "MIT", "dependencies": {"commander": "2.3.0", "debug": "2.2.0", "diff": "1.4.0", "escape-string-regexp": "1.0.2", "glob": "3.2.11", "growl": "1.9.2", "jade": "0.26.3", "mkdirp": "0.5.1", "supports-color": "1.2.0", "to-iso-string": "0.0.2"}, "bin": {"_mocha": "bin/_mocha", "mocha": "bin/mocha"}, "engines": {"node": ">= 0.8.x"}}, "node_modules/ms": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz", "integrity": "sha1-nNE8A62/8ltl7/3nzoZO6VIBcJg=", "dev": true}, "node_modules/must": {"version": "0.13.4", "resolved": "https://registry.npmjs.org/must/-/must-0.13.4.tgz", "integrity": "sha1-nROJ9FjolLKqBAMr/rekBxT3gXE=", "dev": true, "dependencies": {"egal": ">= 1.3.0 < 2", "json-stringify-safe": ">= 5 < 6", "kindof": ">= 2.0.0 < 3", "lodash.wrap": ">= 3 < 4", "oolong": ">= 1.11.0 < 2"}}, "node_modules/oauth-sign": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.2.tgz", "integrity": "sha1-Rqarfwrq2N6unsBWV4C31O/rnUM=", "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/oolong": {"version": "1.15.1", "resolved": "https://registry.npmjs.org/oolong/-/oolong-1.15.1.tgz", "integrity": "sha1-kLrJ585S9gkGqyIo2eKxFfEIbOo=", "dev": true}, "node_modules/os-tmpdir": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "integrity": "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pem": {"version": "1.12.3", "resolved": "https://registry.npmjs.org/pem/-/pem-1.12.3.tgz", "integrity": "sha512-hT7GwvQL35+0iqgYUl8vn5I5pAVR0HcJas07TXL8bNaR4c5kAFRquk4ZqQk1F9YMcQOr6WjGdY5OnDC0RBnzig==", "dev": true, "license": "MIT", "dependencies": {"md5": "^2.2.1", "os-tmpdir": "^1.0.1", "safe-buffer": "^5.1.1", "which": "^1.2.4"}, "engines": {"node": ">=4.0.0"}}, "node_modules/performance-now": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=", "license": "MIT"}, "node_modules/punycode": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "license": "MIT"}, "node_modules/qs": {"version": "6.5.1", "resolved": "https://registry.npmjs.org/qs/-/qs-6.5.1.tgz", "integrity": "sha512-eRzhrN1WSINYCDCbrz796z37LOe3m5tmW7RQf6oBntukAG1nmovJvhnwHHRMAfeoItc1m2Hk02WER2aQ/iqs+A==", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/request": {"version": "2.83.0", "resolved": "https://registry.npmjs.org/request/-/request-2.83.0.tgz", "integrity": "sha512-lR3gD69osqm6EYLk9wB/G1W/laGWjzH90t1vEa2xuxHD5KUrSzp9pUSfTm+YC5Nxt2T8nMPEvKlhbQayU7bgFw==", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "license": "Apache-2.0", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.6.0", "caseless": "~0.12.0", "combined-stream": "~1.0.5", "extend": "~3.0.1", "forever-agent": "~0.6.1", "form-data": "~2.3.1", "har-validator": "~5.0.3", "hawk": "~6.0.2", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.17", "oauth-sign": "~0.8.2", "performance-now": "^2.1.0", "qs": "~6.5.1", "safe-buffer": "^5.1.1", "stringstream": "~0.0.5", "tough-cookie": "~2.3.3", "tunnel-agent": "^0.6.0", "uuid": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/safe-buffer": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.1.tgz", "integrity": "sha512-kKvNJn6Mm93gAczWVJg7wH+wGYWNrDHdWvpUmHyEsgCtIwwo3bqPtV4tR5tuPaUhTOo/kvhVwd8XwwOllGYkbg==", "license": "MIT"}, "node_modules/samsam": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/samsam/-/samsam-1.1.2.tgz", "integrity": "sha1-vsEf3IOp/aBjQBIQ5AF2wwJNFWc=", "deprecated": "This package has been deprecated in favour of @sinonjs/samsam", "dev": true}, "node_modules/sigmund": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/sigmund/-/sigmund-1.0.1.tgz", "integrity": "sha1-P/IfGYytIXX587eBhT/ZTQ0ZtZA=", "dev": true, "license": "ISC"}, "node_modules/sinon": {"version": "1.17.7", "resolved": "https://registry.npmjs.org/sinon/-/sinon-1.17.7.tgz", "integrity": "sha1-RUKk9JugxFwF6y6d2dID4rjv4L8=", "deprecated": "16.1.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"formatio": "1.1.1", "lolex": "1.3.2", "samsam": "1.1.2", "util": ">=0.10.3 <1"}, "engines": {"node": ">=0.1.103"}}, "node_modules/sntp": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/sntp/-/sntp-2.1.0.tgz", "integrity": "sha512-FL1b58BDrqS3A11lJ0zEdnJ3UOKqVxawAkF3k7F0CVN7VQ34aZrV+G8BZ1WC9ZL7NyrwsW0oviwsWDgRuVYtJg==", "deprecated": "This module moved to @hapi/sntp. Please make sure to switch over as this distribution is no longer supported and may contain bugs and critical security issues.", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"hoek": "4.x.x"}, "engines": {"node": ">=4.0.0"}}, "node_modules/sshpk": {"version": "1.13.1", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.13.1.tgz", "integrity": "sha1-US322mKHFEMW3EwY/hzx2UBzm+M=", "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "dashdash": "^1.12.0", "getpass": "^0.1.1"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}, "optionalDependencies": {"bcrypt-pbkdf": "^1.0.0", "ecc-jsbn": "~0.1.1", "jsbn": "~0.1.0", "tweetnacl": "~0.14.0"}}, "node_modules/stringstream": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/stringstream/-/stringstream-0.0.5.tgz", "integrity": "sha1-TkhM1N5aC7vuGORjB3EKioFiGHg=", "license": "MIT"}, "node_modules/supports-color": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-1.2.0.tgz", "integrity": "sha1-/x7R5hFp0Gs88tWI4YixjYhH4X4=", "dev": true, "license": "MIT", "bin": {"supports-color": "cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-iso-string": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/to-iso-string/-/to-iso-string-0.0.2.tgz", "integrity": "sha1-TcGeZk38y+Jb2NtQiwDG2hWCVdE=", "deprecated": "to-iso-string has been deprecated, use @segment/to-iso-string instead.", "dev": true, "license": "MIT"}, "node_modules/tough-cookie": {"version": "2.3.4", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.3.4.tgz", "integrity": "sha512-TZ6TTfI5NtZnuyy/Kecv+CnoROnyXn2DN97LontgQpCwsX2XyLYCC0ENhYkehSOwAp8rTQKc/NUIF7BkQ5rKLA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"punycode": "^1.4.1"}, "engines": {"node": ">=0.8"}}, "node_modules/tunnel-agent": {"version": "0.6.0", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/tv4": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/tv4/-/tv4-1.3.0.tgz", "integrity": "sha1-0CDIRvrdUMhVq7JeuuzGj8EPeWM=", "dev": true, "license": [{"type": "Public Domain", "url": "http://geraintluff.github.io/tv4/LICENSE.txt"}, {"type": "MIT", "url": "http://jsonary.com/LICENSE.txt"}], "engines": {"node": ">= 0.8.0"}}, "node_modules/tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=", "license": "Unlicense", "optional": true}, "node_modules/util": {"version": "0.10.3", "resolved": "https://registry.npmjs.org/util/-/util-0.10.3.tgz", "integrity": "sha1-evsa/lCAUkZInj23/g7TeTNqwPk=", "dev": true, "license": "MIT", "dependencies": {"inherits": "2.0.1"}}, "node_modules/util/node_modules/inherits": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.1.tgz", "integrity": "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=", "dev": true, "license": "ISC"}, "node_modules/uuid": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/uuid/-/uuid-3.2.1.tgz", "integrity": "sha512-jZnMwlb9Iku/O3smGWvZhauCf6cvvpKi4BKRiliS3cxnI+Gz9j5MEpTz2UFuXiKPJocb7gnsLHwiS05ige5BEA==", "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "license": "MIT", "bin": {"uuid": "bin/uuid"}}, "node_modules/validator": {"version": "3.43.0", "resolved": "https://registry.npmjs.org/validator/-/validator-3.43.0.tgz", "integrity": "sha1-lkZLmS1BloM9l6GUv0Cxn/VLrgU=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/verror": {"version": "1.10.0", "resolved": "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/webhdfs-proxy": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/webhdfs-proxy/-/webhdfs-proxy-0.1.2.tgz", "integrity": "sha1-qEWCu6Vwf0N+SaDlwctNrV9VObM=", "dev": true, "license": "MIT", "dependencies": {"async": "^0.9.0", "bluebird": "^2.3.0", "jayschema": "^0.2.8", "pem": "^1.4.1", "qs": "^1.2.2", "tv4": "^1.1.0", "validator": "^3.17.0"}}, "node_modules/webhdfs-proxy-memory": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/webhdfs-proxy-memory/-/webhdfs-proxy-memory-0.1.2.tgz", "integrity": "sha1-eKqL9JEZFqrnOf5Co5OOluhmeCA=", "dev": true, "license": "MIT"}, "node_modules/webhdfs-proxy/node_modules/qs": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/qs/-/qs-1.2.2.tgz", "integrity": "sha1-GbV/8k3CqZzh+L32r82ln472H4g=", "dev": true}, "node_modules/when": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/when/-/when-3.1.0.tgz", "integrity": "sha1-okeWWcoV9yVUHs9S664JG3ge4TQ=", "dev": true}, "node_modules/which": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/which/-/which-1.3.0.tgz", "integrity": "sha512-xcJpopdamTuY5duC/KnTTNBraPK54YwpenP4lzxU8H91GudWpFv38u0CKjclE1Wi2EH2EDz5LRcHcKbCIzqGyg==", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}}}