# Airflow deployment to Naver N2C
Naver N2C cluster 에 apache airflow 을 배포

## Charts
- charts/postgresql
- charts/airflow-comm

## Postgresql deployment
airflow 2.0 에서 도입된 scheduler HA 을 이용하려면 postgresql 을 database 로 사용. mysql 은 HA 구성시 이슈가 있는 듯.

먼저 postgres admin user 를 위한 postgresql secret 을 생성.

values.yaml 과 profile values file 을 참고해서 custom profile 을 생성하고, deploy.sh script 로 profile 을 지정하여 배포.

N2C Ceph volume 이 할당되므로, 이를 인지할 필요있음.

## Airflow deployment
airflow 배포시 사전 작업이 필요.

- DAG 파일을 저장하는 oss repo 생성
- PASTA nubes volume 생성

### DAG repo
airflow 에서 읽어들이는 DAG 파일을 위한 저장소이고, git-sync 을 통해 airflow 에 지속적으로 동기화.

git-sync 에서 deploy-key 을 사용하여 oss 에 접근하므로, oss repo settings 에서 deploy key setup 필요. deploy key 생성에 사용된 ssh private key 는 다음과 같이 k8s secret 으로 생성.

```
$ kubectl create secret generic airflow-dags-deploy --from-file=ssh-private=/path/to/.ssh/id_rsa
```

### Nubes volumes
2 개의 nubes volume 이 필요. N2C storage/nubes 관련 문서 참고.

- git-sync 가 DAG 파일을 동기화해서 저장하는 용도 (5G 내외)
- airflow scheduler/webserver/workers 로그 파일을 저장하는 용도 (100G 내외)

### Deployment
scripts/secret.sh 파일을 참고하여 postgresql/redis secret 생성. postgresql 배포시 생성한 secret 을 edit 해도 무방.
values.yaml 과 profile values 파일을 참고하여 custom profile 생성하고 scripts/deploy.sh 로 배포.

## 로컬 개발 환경
airflow 을 배포했으면, 로컬 개발 환경을 setting 하여 DAG 을 생성/테스트하고 DAG 을 상기한 DAG repo 에 commit. 수십 초 후에 airflow  UI 에 나타남.

로컬 개발 환경 setting 관련해서는 oss.navercorp.com/da-ssp/airflow-dev repo 을 참고.
