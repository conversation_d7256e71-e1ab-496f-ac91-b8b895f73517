/*
API Documentation for MongoDB Drivers
	https://api.mongodb.com/
	golang은 mgo(https://godoc.org/labix.org/v2/mgo)로 연결해 주네?


Configure Read Preference
	https://docs.mongodb.com/v3.6/core/read-preference/#configure-read-preference

	When using a MongoDB driver, you can specify the read preference when connecting to the replica set or sharded cluster.
	For example, see connection string.
	You can also specify the read preference at a more granular level.
	For details, see your driver’s api documentation.


maxStalenessSeconds
	https://docs.mongodb.com/v3.6/core/read-preference/#maxstalenessseconds
*/
package database

import (
	"context"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"oss.navercorp.com/da-ssp/gfp-api/config"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
)

var GFP *mongo.Database
var client *mongo.Client
var log = logger.GetLogger("default")

func init() {
	// Set client options
	clientOptions := options.Client()
	clientOptions.ApplyURI(config.GetConfig("db_url").(string))
	clientOptions.SetMaxPoolSize(uint64(config.GetConfig("db_poolsize").(float64)))

	// Connect to MongoDB
	var err error
	client, err = mongo.Connect(context.Background(), clientOptions)

	if err != nil {
		log.Error(err)
	}

	// Check the connection
	err = client.Ping(context.Background(), nil)

	if err != nil {
		log.Error(err)
	}

	GFP = client.Database(config.GetConfig("db_name").(string))

	log.Debug("MongoDB 연결됨")
}

func Disconnect() {
	err := client.Disconnect(context.TODO())
	if err != nil {
		log.Error(err)
	}
	log.Info("MongoDB 연결 종료됨")
}
