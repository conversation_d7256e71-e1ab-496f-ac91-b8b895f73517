package zookeeper

import (
	"errors"
	"github.com/samuel/go-zookeeper/zk"
	"strings"
	"time"

	"oss.navercorp.com/da-ssp/gfp-api/config"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
)

var log = logger.GetLogger("default")

var (
	connectionUrls []string
	connection     *zk.Conn
	shouldStop     chan bool
	shouldStopDone bool
)

type zkLogger struct{}

func (zkLogger) Printf(format string, args ...interface{}) {
	log.Infof(format, args...)
}

func withLogger(conn *zk.Conn) {
	conn.SetLogger(zkLogger{})
}

func Init() {
	log.Debug("Zookeeper Init")

	for _, v := range config.GetConfig("zookeeper.connectionUrl").([]interface{}) {
		connectionUrls = append(connectionUrls, v.(string))
	}

	shouldStop = make(chan bool)
	shouldStopDone = false

	go setConnection()
}

func Stop() {
	if shouldStopDone == false {
		log.Debug("Zookeeper 연결 종료")

		shouldStop <- true

		time.Sleep(1000 * time.Millisecond)

		close(shouldStop)

		log.Debug("Zookeeper shouldStop 채널 종료")
	}
}

func Start() {
	if shouldStopDone == true {
		log.Debug("Zookeeper 연결 시작")

		shouldStop = make(chan bool)
		shouldStopDone = false

		go setConnection()
	}
}

func setConnection() {
	log.Debug("Zookeeper 연결 요청 시작")

CONN_LOOP:
	for {
		conn, ech, err := zk.Connect(connectionUrls, 100*time.Second, withLogger)
		connection = conn
		// defer conn.Close()

		if err != nil {
			log.Error(err)
			//return
		}

		for {
			select {
			case evt := <-ech:
				log.Debugf("%+v", evt)

				if evt.State == zk.StateConnected {
					log.Debug("Zookeeper Connected ...")
				} else if evt.State == zk.StateDisconnected {
					log.Debug("Zookeeper Disconnected ...")

					conn.Close()
					time.Sleep(1000 * time.Millisecond)

					continue CONN_LOOP
				}
			case v := <-shouldStop:
				if v == true {
					log.Debug("Zookeeper stop signal received")

					shouldStopDone = true

					if conn != nil {
						conn.Close()
					}

					break CONN_LOOP
				}
			}
		}
	}
}

/* zookeeper SetData */
func SetData(path string, data string) (err error) {
	log.Debugf("[SetData] path: %s, data: %s", path, data)

	if err = createPath(path); err != nil {
		log.Error(err)
		return
	}

	bufferData := []byte(data)

	if _, err := connection.Set(path, bufferData, -1); err != nil {
		log.Error(err)
	}

	return
}

func createPath(path string) (err error) {
	if path == "" {
		err = errors.New("[createPath] path is required")
		log.Error(err)
		return
	}

	paths := strings.Split(path, "/") // ['', 'ssp', 'preview']

	isExists, err := isExistNode(path)
	if err != nil {
		log.Error(err)
		return
	}

	if isExists == false {
		parentPath := strings.Join(paths[:len(paths)-1], "/")
		if parentPath != "" {
			if isExists, err := isExistNode(parentPath); err != nil {
				log.Error(err)
			} else if isExists == false {
				if err := createPath(parentPath); err != nil {
					log.Error(err)
				}
			}
		}

		if err := createNode(path); err != nil {
			log.Error(err)
		}
	}

	return
}

func createNode(path string) (err error) {
	_, err = connection.Create(path, nil, 0, zk.WorldACL(zk.PermAll))

	if err != nil {
		log.Error(err)
	}

	return
}

func isExistNode(path string) (isExists bool, err error) {
	isExists, _, err = connection.Exists(path)

	if err != nil {
		log.Error(err)
	}

	return
}

/* zookeeper GetData */
func GetData(path string) {
	log.Debugf("[GetData] path: %s", path)

	var cnt = 0

	for {
		cnt++

		bufferData, _, err := connection.Get(path)

		if err != nil {
			log.Error(err)
			return
		}

		data := string(bufferData[:])
		log.Debugf("[GetData] data : %s", data)

		if data != "" || cnt > 5 {
			return
		}
	}
}

// /* zookeeper GetData */
// func GetData(path string) {
// 	log.Debugf("[GetData] path: %s", path)

// 	for {
// 		bufferData, _, watch, err := connection.GetW(path)

// 		if err != nil {
// 			log.Error(err)
// 			return
// 		}

// 		data := string(bufferData[:])
// 		log.Debugf("[GetData] data : %s", data)

// 		if data != "" {
// 			return
// 		}

// 		// block till event fires
// 		event := <-watch

// 		log.Debug("[GetData] Received Zookeeper event: " + event.Type.String())
// 	}
// }
