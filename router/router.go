package router

import (
	"github.com/gin-gonic/gin"

	"oss.navercorp.com/da-ssp/gfp-api/testapi"

	"oss.navercorp.com/da-ssp/gfp-api/adprovider"
	"oss.navercorp.com/da-ssp/gfp-api/adunit"
	"oss.navercorp.com/da-ssp/gfp-api/kvext"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
	"oss.navercorp.com/da-ssp/gfp-api/nativeadstyle"
	"oss.navercorp.com/da-ssp/gfp-api/preview"
	"oss.navercorp.com/da-ssp/gfp-api/publisher"
	"oss.navercorp.com/da-ssp/gfp-api/revenue"
	"oss.navercorp.com/da-ssp/gfp-api/triplea"
)

var log = logger.GetLogger("default")

func Init(router *gin.Engine) {
	/*
		라우팅 로그 포맷 설정

		# 변경전
		[GIN-debug] GET    /ping                     --> main.setupRouter.func1 (5 handlers)
			->
		# 변경후
		2019/07/24 15:21:16 endpoint GET /ping main.setupRouter.func1 5
	*/
	gin.DebugPrintRouteFunc = func(httpMethod, absolutePath, handlerName string, nuHandlers int) {
		log.Debugf("라우트 등록 %v %v %v %v", httpMethod, absolutePath, handlerName, nuHandlers)
	}

	testapi.SetUpRoute(router)

	triplea.SetUpRoute(router)
	preview.SetUpRoute(router)
	publisher.SetUpRoute(router)
	adprovider.SetUpRoute(router)
	adunit.SetUpRoute(router)
	revenue.SetUpRoute(router)
	kvext.SetUpRoute(router)
	nativeadstyle.SetUpRoute(router)
}
