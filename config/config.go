package config

import (
	"flag"
	"fmt"
	"os"
	"time"

	"github.com/spf13/viper"
)

var config *viper.Viper

var LocationAsiaSeoul, _ = time.LoadLocation("Asia/Seoul")

// config 정보 초기화
func init() {
	env := GetEnv()

	config = viper.New()

	config.SetConfigType("json")

	/**
	- command line flag로부터 값을 받는다.
	- /home1/irteam/gfp-api/bin/gfp-api-runner.sh을 실행할 때 -confdir로 입력 받는다.
	- 자세한 사항은 gfp-api.sh의 start() 참조
	-----------------------------------------------------------------------------------------------------------------------------------
	$ nohup /home1/irteam/gfp-api/bin/gfp-api-runner.sh -confdir /home1/irteam/gfp-api/conf >> /home1/irteam/gfp-api/log/std.log &
	-----------------------------------------------------------------------------------------------------------------------------------
	*/
	var confDir string
	flag.StringVar(&confDir, "confdir", "./config/env", "Usage")
	flag.Parse()
	config.AddConfigPath(confDir)

	// 공통 설정 정보
	config.SetConfigName("common")

	// 에러 처리
	if err := config.ReadInConfig(); err != nil {
		panic(fmt.Sprintf("config 설정 에러. %v", err))
	}

	// 환경별 설정 정보
	config.SetConfigName(env)

	// 에러 처리
	if err := config.MergeInConfig(); err != nil {
		panic(fmt.Sprintf("config 설정 에러. %v", err))
	}
}

// GetConfig : key에 해당하는 value 가져오기
func GetConfig(key string) interface{} {
	return config.Get(key)
}

func GetEnv() string {
	gfpapi_profile := os.Getenv("GFPAPI_PROFILE")

	if gfpapi_profile == "" {
		gfpapi_profile = "local"
	}

	return gfpapi_profile
}
