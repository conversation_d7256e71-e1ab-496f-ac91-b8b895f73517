{"db_name": "ssp", "db_url": "*************************************************************************************************************************************", "db_poolsize": 20, "log_level": "info", "log_dir": "/home1/irteam/gfp-api/log", "nelo": {"url": "http://nelo2-col.navercorp.com/_store", "projectName": "gfp_api", "projectVersion": "1.0.0", "transfer": true}, "acl": {"remoteAclUrl": "http://acl-portal.navercorp.com/api/v2/acls", "providerId": "gladgfp", "printLoadedInfo": true}, "zookeeper": {"connectionUrl": ["avndpzkp01-da:2181", "avndpzkp02-da:2181", "cvndpzkp01-da:2181", "cvndpzkp02-da:2181", "pgvndpzkp01-da:2181"], "path": {"preview": "/ssp/preview"}}, "download_root": "/home1/owfs_web/download", "nclavis": {"url": "https://apis.nclavis.navercorp.com/kms/consumer/td1gsdQhX8pvTXkwli7c0PbqTUA="}, "druid": {"broker": {"url": "http://asdruidbroker.navercorp.com:8082", "endPoint": "/druid/v2"}}}