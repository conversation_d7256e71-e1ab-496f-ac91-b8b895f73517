package testapi

import (
	"github.com/gin-gonic/gin"

	"oss.navercorp.com/da-ssp/gfp-api/logger"
)

var (
	log = logger.GetLogger("default")

	testController = TestController{}
)

func SetUpRoute(router *gin.Engine) {
	group := router.Group("/test")

	// ping test
	group.GET("/ping", testController.TestPing)

	// 사전등록 키 확장 정보 조회
	group.GET("/fullsync/:publisherId/:key", testController.DownloadFullSync)


	// zookeeper 연결 종료 테스트
	group.GET("/zookeeper/stop", testController.StopZookeeper)

	// zookeeper 연결 시작 테스트
	group.GET("/zookeeper/start", testController.StartZookeeper)

	// zookeeper 데이터 Set
	group.GET("/zookeeper/setData", testController.SetZookeeperData)

	// zookeeper 데이터 Get
	group.GET("/zookeeper/getData", testController.GetZookeeperData)
}
