package testapi

import (
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"

	"oss.navercorp.com/da-ssp/gfp-api/config"
	gfpError "oss.navercorp.com/da-ssp/gfp-api/error"
	"oss.navercorp.com/da-ssp/gfp-api/logger"

	"oss.navercorp.com/da-ssp/gfp-api/zookeeper"
)

type TestController struct{}

func (testController *TestController) TestPing(ginCtx *gin.Context) {
	ginCtx.String(http.StatusOK, "OK")
}

// fullSync 파일 다운로드 API
func (testController *TestController) DownloadFullSync(ginCtx *gin.Context) {
	// 파라미터 파싱
	fileName := ginCtx.Query("fileName")
	log.Debugf("[DownloadFullSync] fileName: %s", fileName)

	// fullPath : /home1/owfs_web/download/kvext/test/150.csv
	rootDir := config.GetConfig("download_root").(string) + "/kvext/test"
	fullPath := rootDir + "/" + fileName
	log.Debugf("[DownloadFullSync] fullPath: %s", fullPath)

	// 파일이 존재하는지 확인
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		log.Errorf("[DownloadFullSync] 파일이 존재하지 않음. %s", fullPath)
		gfpError.ResponseError(ginCtx, gfpError.FileNotFound.BizError(nil))
		return
	}

	ginCtx.FileAttachment(fullPath, fileName)
}

// zookeeper 연결 종료 테스트
func (testController *TestController) StopZookeeper(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[StopZookeeper] start")

	zookeeper.Stop()

	entry.Debug("[StopZookeeper] end")
}

// zookeeper 연결 시작 테스트
func (testController *TestController) StartZookeeper(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[StartZookeeper] start")

	zookeeper.Start()

	entry.Debug("[StartZookeeper] end")
}

// zookeeper SetData
func (testController *TestController) SetZookeeperData(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[SetZookeeperData] Zookeeper 동기화 요청")

	previewPath := "/ssp/api/test"
	currentTime := time.Now().Format("20060102150405")

	// zookeeper 동기화 요청
	zookeeper.SetData(previewPath, currentTime)
}

// zookeeper GetData
func (testController *TestController) GetZookeeperData(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[GetZookeeperData] Zookeeper 동기화 결과")

	// zookeeper 동기화 결과
	zookeeper.GetData("/ssp/api/test")
}