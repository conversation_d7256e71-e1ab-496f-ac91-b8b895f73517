<configuration>
  <property>
    <name>dfs.block.access.token.enable</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.blockreport.initialDelay</name>
    <value>2000</value>
  </property>
  <property>
    <name>dfs.blockreport.split.threshold</name>
    <value>100000</value>
  </property>
  <property>
    <name>dfs.blocksize</name>
    <value>268435456</value>
  </property>
  <property>
    <name>dfs.client.failover.connection.retries</name>
    <value>1</value>
  </property>
  <property>
    <name>dfs.client.failover.connection.retries.on.timeouts</name>
    <value>1</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.abc</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.at</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.camino</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.cdt</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.jmt</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.kt</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pct</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg01</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg02</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg03</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg04</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg05</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg06</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg07</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg08</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg09</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg10</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg11</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg12</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg13</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg14</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg15</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg16</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg17</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg18</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg19</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pg20</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.pgcm</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.client.https.need-auth</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.client.ignore.namenode.default.kms.uri</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit.streams.cache.size</name>
    <value>4096</value>
  </property>
  <property>
    <name>dfs.client.retry.policy.enabled</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.cluster.administrators</name>
    <value> hdfs,hadoop-admins</value>
  </property>
  <property>
    <name>dfs.content-summary.limit</name>
    <value>5000</value>
  </property>
  <property>
    <name>dfs.data.transfer.protection</name>
    <value>authentication,privacy</value>
  </property>
  <property>
    <name>dfs.datanode.address</name>
    <value>0.0.0.0:9011</value>
  </property>
  <property>
    <name>dfs.datanode.balance.bandwidthPerSec</name>
    <value>6250000</value>
  </property>
  <property>
    <name>dfs.datanode.data.dir</name>
    <value>/data1/hadoop/hdfs/data,/data2/hadoop/hdfs/data,/data3/hadoop/hdfs/data,/data4/hadoop/hdfs/data,/data5/hadoop/hdfs/data,/data6/hadoop/hdfs/data,/data7/hadoop/hdfs/data,/data8/hadoop/hdfs/data,/data9/hadoop/hdfs/data,/data10/hadoop/hdfs/data</value>
  </property>
  <property>
    <name>dfs.datanode.data.dir.perm</name>
    <value>750</value>
  </property>
  <property>
    <name>dfs.datanode.du.reserved</name>
    <value>429496729600</value>
  </property>
  <property>
    <name>dfs.datanode.failed.volumes.tolerated</name>
    <value>2</value>
  </property>
  <property>
    <name>dfs.datanode.handler.count</name>
    <value>100</value>
  </property>
  <property>
    <name>dfs.datanode.http.address</name>
    <value>0.0.0.0:9075</value>
  </property>
  <property>
    <name>dfs.datanode.https.address</name>
    <value>0.0.0.0:9475</value>
  </property>
  <property>
    <name>dfs.datanode.ipc.address</name>
    <value>0.0.0.0:9010</value>
  </property>
  <property>
    <name>dfs.datanode.kerberos.principal</name>
    <value>dn/<EMAIL></value>
  </property>
  <property>
    <name>dfs.datanode.kerberos.principal.pattern</name>
    <value>*</value>
  </property>
  <property>
    <name>dfs.datanode.keytab.file</name>
    <value>/etc/security/keytabs/dn.service.keytab</value>
  </property>
  <property>
    <name>dfs.datanode.max.transfer.threads</name>
    <value>4096</value>
  </property>
  <property>
    <name>dfs.domain.socket.path</name>
    <value>/var/lib/hadoop-hdfs/dn_socket</value>
  </property>
  <property>
    <name>dfs.encrypt.data.transfer.cipher.suites</name>
    <value>AES/CTR/NoPadding</value>
  </property>
  <property>
    <name>dfs.encryption.key.provider.uri</name>
    <value></value>
  </property>
  <property>
    <name>dfs.ha.automatic-failover.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.ha.fencing.methods</name>
    <value>sshfence
shell(/bin/true)</value>
  </property>
  <property>
    <name>dfs.ha.fencing.ssh.private-key-files</name>
    <value>/home/<USER>/.ssh/id_rsa.c3s-pan</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.abc</name>
    <value>nn1,nn2</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.at</name>
    <value>nn7,nn8</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.camino</name>
    <value>nn5,nn6</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.cdt</name>
    <value>nn11,nn12</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.jmt</name>
    <value>nn3,nn4</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.kt</name>
    <value>nn13,nn14</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pct</name>
    <value>nn9,nn10</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg01</name>
    <value>nn3,nn4</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg02</name>
    <value>nn5,nn6</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg03</name>
    <value>nn7,nn8</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg04</name>
    <value>nn9,nn10</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg05</name>
    <value>nn11,nn12</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg06</name>
    <value>nn13,nn14</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg07</name>
    <value>nn15,nn16</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg08</name>
    <value>nn17,nn18</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg09</name>
    <value>nn19,nn20</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg10</name>
    <value>nn21,nn22</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg11</name>
    <value>nn23,nn24</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg12</name>
    <value>nn25,nn26</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg13</name>
    <value>nn27,nn28</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg14</name>
    <value>nn29,nn30</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg15</name>
    <value>nn31,nn32</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg16</name>
    <value>nn33,nn34</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg17</name>
    <value>nn35,nn36</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg18</name>
    <value>nn37,nn38</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg19</name>
    <value>nn39,nn40</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pg20</name>
    <value>nn41,nn42</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.pgcm</name>
    <value>nn1,nn2</value>
  </property>
  <property>
    <name>dfs.ha.zkfc.port</name>
    <value>9019</value>
  </property>
  <property>
    <name>dfs.heartbeat.interval</name>
    <value>3</value>
  </property>
  <property>
    <name>dfs.hosts.exclude</name>
    <value>/etc/hadoop/conf/dfs.exclude</value>
  </property>
  <property>
    <name>dfs.http.policy</name>
    <value>HTTPS_ONLY</value>
  </property>
  <property>
    <name>dfs.https.port</name>
    <value>9470</value>
  </property>
  <property>
    <name>dfs.image.transfer.bandwidthPerSec</name>
    <value>31457280</value>
  </property>
  <property>
    <name>dfs.internal.nameservices</name>
    <value>pgcm</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg01</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg02</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg03</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg04</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg05</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg06</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg07</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg08</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg09</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg10</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg11</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg12</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg13</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg14</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg15</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg16</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg17</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg18</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg19</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pg20</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir.pgcm</name>
    <value>/hadoop/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.http-address</name>
    <value>0.0.0.0:9480</value>
  </property>
  <property>
    <name>dfs.journalnode.https-address</name>
    <value>0.0.0.0:9481</value>
  </property>
  <property>
    <name>dfs.journalnode.kerberos.internal.spnego.principal</name>
    <value>HTTP/<EMAIL></value>
  </property>
  <property>
    <name>dfs.journalnode.kerberos.principal</name>
    <value>jn/<EMAIL></value>
  </property>
  <property>
    <name>dfs.journalnode.kerberos.principal.pattern</name>
    <value>*</value>
  </property>
  <property>
    <name>dfs.journalnode.keytab.file</name>
    <value>/etc/security/keytabs/jn.service.keytab</value>
  </property>
  <property>
    <name>dfs.journalnode.rpc-address</name>
    <value>0.0.0.0:9485</value>
  </property>
  <property>
    <name>dfs.namenode.accesstime.precision</name>
    <value>2592000000</value>
  </property>
  <property>
    <name>dfs.namenode.acls.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.namenode.audit.log.async</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.namenode.avoid.read.stale.datanode</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.namenode.avoid.write.stale.datanode</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.namenode.checkpoint.dir</name>
    <value>/hadoop/hdfs/namesecondary</value>
  </property>
  <property>
    <name>dfs.namenode.checkpoint.edits.dir</name>
    <value>${dfs.namenode.checkpoint.dir}</value>
  </property>
  <property>
    <name>dfs.namenode.checkpoint.period</name>
    <value>3600</value>
  </property>
  <property>
    <name>dfs.namenode.checkpoint.txns</name>
    <value>9000000</value>
  </property>
  <property>
    <name>dfs.namenode.fslock.fair</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.handler.count</name>
    <value>200</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.abc.nn1</name>
    <value>abc-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.abc.nn2</name>
    <value>abc-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.at.nn7</name>
    <value>at-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.at.nn8</name>
    <value>at-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.camino.nn5</name>
    <value>camino-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.camino.nn6</name>
    <value>camino-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.cdt.nn11</name>
    <value>cdt-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.cdt.nn12</name>
    <value>cdt-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.jmt.nn3</name>
    <value>jmt-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.jmt.nn4</name>
    <value>jmt-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.kt.nn13</name>
    <value>kt-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.kt.nn14</name>
    <value>kt-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pct.nn10</name>
    <value>pct-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pct.nn9</name>
    <value>pct-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg01.nn3</name>
    <value>pg01-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg01.nn4</name>
    <value>pg01-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg02.nn5</name>
    <value>pg02-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg02.nn6</name>
    <value>pg02-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg03.nn7</name>
    <value>pg03-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg03.nn8</name>
    <value>pg03-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg04.nn10</name>
    <value>pg04-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg04.nn9</name>
    <value>pg04-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg05.nn11</name>
    <value>pg05-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg05.nn12</name>
    <value>pg05-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg06.nn13</name>
    <value>pg06-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg06.nn14</name>
    <value>pg06-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg07.nn15</name>
    <value>pg07-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg07.nn16</name>
    <value>pg07-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg08.nn17</name>
    <value>pg08-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg08.nn18</name>
    <value>pg08-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg09.nn19</name>
    <value>pg09-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg09.nn20</name>
    <value>pg09-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg10.nn21</name>
    <value>pg10-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg10.nn22</name>
    <value>pg10-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg11.nn23</name>
    <value>pg11-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg11.nn24</name>
    <value>pg11-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg12.nn25</name>
    <value>pg12-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg12.nn26</name>
    <value>pg12-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg13.nn27</name>
    <value>pg13-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg13.nn28</name>
    <value>pg13-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg14.nn29</name>
    <value>pg14-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg14.nn30</name>
    <value>pg14-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg15.nn31</name>
    <value>pg15-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg15.nn32</name>
    <value>pg15-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg16.nn33</name>
    <value>pg16-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg16.nn34</name>
    <value>pg16-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg17.nn35</name>
    <value>pg17-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg17.nn36</name>
    <value>pg17-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg18.nn37</name>
    <value>pg18-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg18.nn38</name>
    <value>pg18-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg19.nn39</name>
    <value>pg19-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg19.nn40</name>
    <value>pg19-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg20.nn41</name>
    <value>pg20-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pg20.nn42</name>
    <value>pg20-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pgcm.nn1</name>
    <value>pgcm-nn1.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.pgcm.nn2</name>
    <value>pgcm-nn2.bdp.bdata.ai:9070</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.abc.nn1</name>
    <value>abc-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.abc.nn2</name>
    <value>abc-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.at.nn7</name>
    <value>at-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.at.nn8</name>
    <value>at-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.camino.nn5</name>
    <value>camino-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.camino.nn6</name>
    <value>camino-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.cdt.nn11</name>
    <value>cdt-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.cdt.nn12</name>
    <value>cdt-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.jmt.nn3</name>
    <value>jmt-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.jmt.nn4</name>
    <value>jmt-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.kt.nn13</name>
    <value>kt-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.kt.nn14</name>
    <value>kt-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pct.nn10</name>
    <value>pct-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pct.nn9</name>
    <value>pct-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg01.nn3</name>
    <value>pg01-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg01.nn4</name>
    <value>pg01-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg02.nn5</name>
    <value>pg02-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg02.nn6</name>
    <value>pg02-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg03.nn7</name>
    <value>pg03-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg03.nn8</name>
    <value>pg03-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg04.nn10</name>
    <value>pg04-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg04.nn9</name>
    <value>pg04-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg05.nn11</name>
    <value>pg05-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg05.nn12</name>
    <value>pg05-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg06.nn13</name>
    <value>pg06-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg06.nn14</name>
    <value>pg06-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg07.nn15</name>
    <value>pg07-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg07.nn16</name>
    <value>pg07-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg08.nn17</name>
    <value>pg08-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg08.nn18</name>
    <value>pg08-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg09.nn19</name>
    <value>pg09-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg09.nn20</name>
    <value>pg09-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg10.nn21</name>
    <value>pg10-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg10.nn22</name>
    <value>pg10-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg11.nn23</name>
    <value>pg11-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg11.nn24</name>
    <value>pg11-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg12.nn25</name>
    <value>pg12-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg12.nn26</name>
    <value>pg12-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg13.nn27</name>
    <value>pg13-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg13.nn28</name>
    <value>pg13-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg14.nn29</name>
    <value>pg14-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg14.nn30</name>
    <value>pg14-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg15.nn31</name>
    <value>pg15-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg15.nn32</name>
    <value>pg15-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg16.nn33</name>
    <value>pg16-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg16.nn34</name>
    <value>pg16-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg17.nn35</name>
    <value>pg17-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg17.nn36</name>
    <value>pg17-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg18.nn37</name>
    <value>pg18-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg18.nn38</name>
    <value>pg18-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg19.nn39</name>
    <value>pg19-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg19.nn40</name>
    <value>pg19-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg20.nn41</name>
    <value>pg20-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pg20.nn42</name>
    <value>pg20-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pgcm.nn1</name>
    <value>pgcm-nn1.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.pgcm.nn2</name>
    <value>pgcm-nn2.bdp.bdata.ai:9470</value>
  </property>
  <property>
    <name>dfs.namenode.inode.attributes.provider.class</name>
    <value>org.apache.ranger.authorization.hadoop.RangerHdfsAuthorizer</value>
  </property>
  <property>
    <name>dfs.namenode.kerberos.internal.spnego.principal</name>
    <value>HTTP/<EMAIL></value>
  </property>
  <property>
    <name>dfs.namenode.kerberos.principal</name>
    <value>nn/<EMAIL></value>
  </property>
  <property>
    <name>dfs.namenode.kerberos.principal.pattern</name>
    <value>*</value>
  </property>
  <property>
    <name>dfs.namenode.keytab.file</name>
    <value>/etc/security/keytabs/nn.service.keytab</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.handler.ratio</name>
    <value>0.20</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg01.nn3</name>
    <value>pg01-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg01.nn4</name>
    <value>pg01-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg02.nn5</name>
    <value>pg02-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg02.nn6</name>
    <value>pg02-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg03.nn7</name>
    <value>pg03-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg03.nn8</name>
    <value>pg03-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg04.nn10</name>
    <value>pg04-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg04.nn9</name>
    <value>pg04-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg05.nn11</name>
    <value>pg05-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg05.nn12</name>
    <value>pg05-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg06.nn13</name>
    <value>pg06-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg06.nn14</name>
    <value>pg06-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg07.nn15</name>
    <value>pg07-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg07.nn16</name>
    <value>pg07-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg08.nn17</name>
    <value>pg08-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg08.nn18</name>
    <value>pg08-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg09.nn19</name>
    <value>pg09-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg09.nn20</name>
    <value>pg09-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg10.nn21</name>
    <value>pg10-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg10.nn22</name>
    <value>pg10-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg11.nn23</name>
    <value>pg11-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg11.nn24</name>
    <value>pg11-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg12.nn25</name>
    <value>pg12-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg12.nn26</name>
    <value>pg12-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg13.nn27</name>
    <value>pg13-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg13.nn28</name>
    <value>pg13-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg14.nn29</name>
    <value>pg14-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg14.nn30</name>
    <value>pg14-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg15.nn31</name>
    <value>pg15-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg15.nn32</name>
    <value>pg15-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg16.nn33</name>
    <value>pg16-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg16.nn34</name>
    <value>pg16-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg17.nn35</name>
    <value>pg17-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg17.nn36</name>
    <value>pg17-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg18.nn37</name>
    <value>pg18-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg18.nn38</name>
    <value>pg18-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg19.nn39</name>
    <value>pg19-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg19.nn40</name>
    <value>pg19-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg20.nn41</name>
    <value>pg20-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pg20.nn42</name>
    <value>pg20-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pgcm.nn1</name>
    <value>pgcm-nn1.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-address.pgcm.nn2</name>
    <value>pgcm-nn2.bdp.bdata.ai:9024</value>
  </property>
  <property>
    <name>dfs.namenode.name.dir</name>
    <value>/hadoop/hdfs/namenode</value>
  </property>
  <property>
    <name>dfs.namenode.name.dir.restore</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.namenode.quota.init-threads</name>
    <value>8</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.abc.nn1</name>
    <value>abc-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.abc.nn2</name>
    <value>abc-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.at.nn7</name>
    <value>at-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.at.nn8</name>
    <value>at-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.camino.nn5</name>
    <value>camino-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.camino.nn6</name>
    <value>camino-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.cdt.nn11</name>
    <value>cdt-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.cdt.nn12</name>
    <value>cdt-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.jmt.nn3</name>
    <value>jmt-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.jmt.nn4</name>
    <value>jmt-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.kt.nn13</name>
    <value>kt-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.kt.nn14</name>
    <value>kt-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pct.nn10</name>
    <value>pct-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pct.nn9</name>
    <value>pct-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg01.nn3</name>
    <value>pg01-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg01.nn4</name>
    <value>pg01-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg02.nn5</name>
    <value>pg02-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg02.nn6</name>
    <value>pg02-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg03.nn7</name>
    <value>pg03-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg03.nn8</name>
    <value>pg03-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg04.nn10</name>
    <value>pg04-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg04.nn9</name>
    <value>pg04-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg05.nn11</name>
    <value>pg05-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg05.nn12</name>
    <value>pg05-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg06.nn13</name>
    <value>pg06-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg06.nn14</name>
    <value>pg06-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg07.nn15</name>
    <value>pg07-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg07.nn16</name>
    <value>pg07-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg08.nn17</name>
    <value>pg08-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg08.nn18</name>
    <value>pg08-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg09.nn19</name>
    <value>pg09-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg09.nn20</name>
    <value>pg09-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg10.nn21</name>
    <value>pg10-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg10.nn22</name>
    <value>pg10-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg11.nn23</name>
    <value>pg11-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg11.nn24</name>
    <value>pg11-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg12.nn25</name>
    <value>pg12-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg12.nn26</name>
    <value>pg12-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg13.nn27</name>
    <value>pg13-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg13.nn28</name>
    <value>pg13-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg14.nn29</name>
    <value>pg14-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg14.nn30</name>
    <value>pg14-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg15.nn31</name>
    <value>pg15-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg15.nn32</name>
    <value>pg15-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg16.nn33</name>
    <value>pg16-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg16.nn34</name>
    <value>pg16-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg17.nn35</name>
    <value>pg17-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg17.nn36</name>
    <value>pg17-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg18.nn37</name>
    <value>pg18-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg18.nn38</name>
    <value>pg18-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg19.nn39</name>
    <value>pg19-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg19.nn40</name>
    <value>pg19-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg20.nn41</name>
    <value>pg20-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pg20.nn42</name>
    <value>pg20-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pgcm.nn1</name>
    <value>pgcm-nn1.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.pgcm.nn2</name>
    <value>pgcm-nn2.bdp.bdata.ai:9020</value>
  </property>
  <property>
    <name>dfs.namenode.safemode.threshold-pct</name>
    <value>0.99</value>
  </property>
  <property>
    <name>dfs.namenode.service.handler.count</name>
    <value>100</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg01.nn3</name>
    <value>pg01-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg01.nn4</name>
    <value>pg01-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg02.nn5</name>
    <value>pg02-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg02.nn6</name>
    <value>pg02-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg03.nn7</name>
    <value>pg03-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg03.nn8</name>
    <value>pg03-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg04.nn10</name>
    <value>pg04-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg04.nn9</name>
    <value>pg04-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg05.nn11</name>
    <value>pg05-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg05.nn12</name>
    <value>pg05-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg06.nn13</name>
    <value>pg06-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg06.nn14</name>
    <value>pg06-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg07.nn15</name>
    <value>pg07-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg07.nn16</name>
    <value>pg07-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg08.nn17</name>
    <value>pg08-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg08.nn18</name>
    <value>pg08-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg09.nn19</name>
    <value>pg09-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg09.nn20</name>
    <value>pg09-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg10.nn21</name>
    <value>pg10-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg10.nn22</name>
    <value>pg10-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg11.nn23</name>
    <value>pg11-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg11.nn24</name>
    <value>pg11-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg12.nn25</name>
    <value>pg12-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg12.nn26</name>
    <value>pg12-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg13.nn27</name>
    <value>pg13-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg13.nn28</name>
    <value>pg13-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg14.nn29</name>
    <value>pg14-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg14.nn30</name>
    <value>pg14-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg15.nn31</name>
    <value>pg15-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg15.nn32</name>
    <value>pg15-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg16.nn33</name>
    <value>pg16-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg16.nn34</name>
    <value>pg16-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg17.nn35</name>
    <value>pg17-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg17.nn36</name>
    <value>pg17-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg18.nn37</name>
    <value>pg18-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg18.nn38</name>
    <value>pg18-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg19.nn39</name>
    <value>pg19-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg19.nn40</name>
    <value>pg19-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg20.nn41</name>
    <value>pg20-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pg20.nn42</name>
    <value>pg20-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pgcm.nn1</name>
    <value>pgcm-nn1.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.pgcm.nn2</name>
    <value>pgcm-nn2.bdp.bdata.ai:9022</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pgcm</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg01</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg01</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg02</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg02</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg03</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg03</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg04</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg04</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg05</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg05</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg06</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg06</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg07</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg07</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg08</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg08</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg09</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg09</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg10</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg10</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg11</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg11</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg12</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg12</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg13</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg13</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg14</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg14</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg15</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg15</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg16</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg16</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg17</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg17</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg18</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg18</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg19</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg19</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pg20</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pg20</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir.pgcm</name>
    <value>qjournal://zk-etcd1-pan.bdp.bdata.ai:9485;zk-etcd2-pan.bdp.bdata.ai:9485;zk-etcd3-pan.bdp.bdata.ai:9485/pgcm</value>
  </property>
  <property>
    <name>dfs.namenode.stale.datanode.interval</name>
    <value>30000</value>
  </property>
  <property>
    <name>dfs.namenode.startup.delay.block.deletion.sec</name>
    <value>3600</value>
  </property>
  <property>
    <name>dfs.namenode.write.stale.datanode.ratio</name>
    <value>1.0f</value>
  </property>
  <property>
    <name>dfs.nameservices</name>
    <value>pgcm,pg01,pg07</value>
  </property>
  <property>
    <name>dfs.permissions.ContentSummary.subAccess</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.permissions.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.permissions.superusergroup</name>
    <value>hadoop-admins</value>
  </property>
  <property>
    <name>dfs.qjournal.write-txns.timeout.ms</name>
    <value>90000</value>
  </property>
  <property>
    <name>dfs.replication</name>
    <value>3</value>
  </property>
  <property>
    <name>dfs.replication.max</name>
    <value>50</value>
  </property>
  <property>
    <name>dfs.web.authentication.kerberos.keytab</name>
    <value>/etc/security/keytabs/spnego.service.keytab</value>
  </property>
  <property>
    <name>dfs.web.authentication.kerberos.principal</name>
    <value>HTTP/<EMAIL></value>
  </property>
  <property>
    <name>dfs.webhdfs.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>fs.permissions.umask-mode</name>
    <value>077</value>
  </property>
  <property>
    <name>fs.tswebhdfs.impl</name>
    <value>org.apache.hadoop.hdfs.web.ThrottledSWebHdfsFileSystem</value>
  </property>
  <property>
    <name>fs.twebhdfs.impl</name>
    <value>org.apache.hadoop.hdfs.web.ThrottledWebHdfsFileSystem</value>
  </property>
  <property>
    <name>hadoop.caller.context.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>ipc.8020.callqueue.impl</name>
    <value>org.apache.hadoop.ipc.FairCallQueue</value>
  </property>
  <property>
    <name>ipc.9020.callqueue.impl</name>
    <value>org.apache.hadoop.ipc.FairCallQueue</value>
  </property>
  <property>
    <name>ipc.client.fallback-to-simple-auth-allowed</name>
    <value>true</value>
  </property>
  <property>
    <name>manage.include.files</name>
    <value>false</value>
  </property>
  <property>
    <name>nfs.exports.allowed.hosts</name>
    <value>* rw</value>
  </property>
  <property>
    <name>nfs.file.dump.dir</name>
    <value>/tmp/.hdfs-nfs</value>
  </property>
  <property>
    <name>twebhdfs.read.bandwidth.megabytes.per.sec</name>
    <value>50</value>
  </property>
<!-- BELOW THIS LINE IS ALLOWED EDITING. DO NOT EDIT THIS LINE. -->
<!-- ABOVE THIS LINE IS ALLOWED EDITING. DO NOT EDIT THIS LINE. -->
</configuration>
