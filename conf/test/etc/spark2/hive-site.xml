  <configuration  xmlns:xi="http://www.w3.org/2001/XInclude">
    
    <property>
      <name>hive.exec.scratchdir</name>
      <value>/tmp/spark</value>
    </property>
    
    <property>
      <name>hive.metastore.client.connect.retry.delay</name>
      <value>5</value>
    </property>
    
    <property>
      <name>hive.metastore.client.socket.timeout</name>
      <value>1800</value>
    </property>
    
    <property>
      <name>hive.metastore.kerberos.keytab.file</name>
      <value>/etc/security/keytabs/hive.service.keytab</value>
    </property>
    
    <property>
      <name>hive.metastore.kerberos.principal</name>
      <value>hive/<EMAIL></value>
    </property>
    
    <property>
      <name>hive.metastore.sasl.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>hive.metastore.uris</name>
      <value>thrift://adevthm002-sa.nfra.io:9083</value>
    </property>
    
    <property>
      <name>hive.server2.authentication</name>
      <value>KERBEROS</value>
    </property>
    
    <property>
      <name>hive.server2.authentication.kerberos.keytab</name>
      <value>/etc/security/keytabs/spark.service.keytab</value>
    </property>
    
    <property>
      <name>hive.server2.authentication.kerberos.principal</name>
      <value>spark/<EMAIL></value>
    </property>
    
    <property>
      <name>hive.server2.authentication.spnego.keytab</name>
      <value>/etc/security/keytabs/spnego.service.keytab</value>
    </property>
    
    <property>
      <name>hive.server2.authentication.spnego.principal</name>
      <value>HTTP/<EMAIL></value>
    </property>
    
    <property>
      <name>hive.server2.enable.doAs</name>
      <value>false</value>
    </property>
    
    <property>
      <name>hive.server2.thrift.http.port</name>
      <value>10002</value>
    </property>
    
    <property>
      <name>hive.server2.thrift.port</name>
      <value>10016</value>
    </property>
    
    <property>
      <name>hive.server2.transport.mode</name>
      <value>binary</value>
    </property>
    
    <property>
      <name>metastore.catalog.default</name>
      <value>spark</value>
    </property>
    
  </configuration>