  <configuration  xmlns:xi="http://www.w3.org/2001/XInclude">
    
    <property>
      <name>fs.azure.user.agent.prefix</name>
      <value>User-Agent: APN/1.0 Hortonworks/1.0 HDP/*******-78</value>
    </property>
    
    <property>
      <name>fs.defaultFS</name>
      <value>hdfs://bizcloud</value>
      <final>true</final>
    </property>
    
    <property>
      <name>fs.gs.application.name.suffix</name>
      <value> (GPN:Hortonworks; version 1.0) HDP/*******-78</value>
    </property>
    
    <property>
      <name>fs.gs.path.encoding</name>
      <value>uri-path</value>
    </property>
    
    <property>
      <name>fs.gs.working.dir</name>
      <value>/</value>
    </property>
    
    <property>
      <name>fs.s3a.fast.upload</name>
      <value>true</value>
    </property>
    
    <property>
      <name>fs.s3a.fast.upload.buffer</name>
      <value>disk</value>
    </property>
    
    <property>
      <name>fs.s3a.multipart.size</name>
      <value>67108864</value>
    </property>
    
    <property>
      <name>fs.s3a.user.agent.prefix</name>
      <value>User-Agent: APN/1.0 Hortonworks/1.0 HDP/*******-78</value>
    </property>
    
    <property>
      <name>fs.trash.interval</name>
      <value>360</value>
    </property>
    
    <property>
      <name>ha.failover-controller.active-standby-elector.zk.op.retries</name>
      <value>120</value>
    </property>
    
    <property>
      <name>ha.zookeeper.acl</name>
      <value>sasl:nn:rwcda</value>
    </property>
    
    <property>
      <name>ha.zookeeper.quorum</name>
      <value>adevthm001-sa.nfra.io:2181,adevthm002-sa.nfra.io:2181,adevthm003-sa.nfra.io:2181</value>
    </property>
    
    <property>
      <name>hadoop.http.authentication.alt-kerberos.non-browser.user-agents</name>
      <value>java,curl,wget,perl,python,commons-httpclient</value>
    </property>
    
    <property>
      <name>hadoop.http.authentication.authentication.provider.url</name>
      <value>https://adevthm004-sa.nfra.io:8443/gateway/knoxsso/api/v1/websso</value>
    </property>
    
    <property>
      <name>hadoop.http.authentication.cookie.domain</name>
      <value>.nfra.io</value>
    </property>
    
    <property>
      <name>hadoop.http.authentication.kerberos.keytab</name>
      <value>/etc/security/keytabs/spnego.service.keytab</value>
    </property>
    
    <property>
      <name>hadoop.http.authentication.kerberos.principal</name>
      <value>HTTP/<EMAIL></value>
    </property>
    
    <property>
      <name>hadoop.http.authentication.public.key.pem</name>
      <value>MIICUTCCAbqgAwIBAgIIFmjpzZhITX8wDQYJKoZIhvcNAQEFBQAwazELMAkGA1UEBhMCVVMxDTALBgNVBAgTBFRlc3QxDTALBgNVBAcTBFRlc3QxDzANBgNVBAoTBkhhZG9vcDENMAsGA1UECxMEVGVzdDEeMBwGA1UEAxMVYWRldnRobTAwNC1zYS5uZnJhLmlvMB4XDTIzMDcwMzA3MzAxMloXDTI0MDcwMjA3MzAxMlowazELMAkGA1UEBhMCVVMxDTALBgNVBAgTBFRlc3QxDTALBgNVBAcTBFRlc3QxDzANBgNVBAoTBkhhZG9vcDENMAsGA1UECxMEVGVzdDEeMBwGA1UEAxMVYWRldnRobTAwNC1zYS5uZnJhLmlvMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDG/8pOeOoh3wn38DK32zNMSMqSBWU7VNucFE7n6ow2Ux7qD+ACLfh2ImZhyFFJYaYFPyxsYdgOwTKfKnHCEGdww0OpIjSIb+/JCzMCAQURQrcARrO033s97dy7WGk40MW2bDrPI3TDXuCya63qEXyoMhySQ8k6UxGjT1g719jAWQIDAQABMA0GCSqGSIb3DQEBBQUAA4GBAGoKnO89sK2+afbYTtcgBr16ZM0IoARv2uQcf5eWiXJKKEiTGhCkkN3Q1pslv224VNv0N88g+5L0S/XFSamD/E6eXtx+kFmHy9rz603dciKXcohc16gi0/mdOSMRmRy/nYf9UWTGo3ZXg+0yxpHE5UmDA8NBuDky60/Bcs4bmfk+</value>
    </property>
    
    <property>
      <name>hadoop.http.authentication.signature.secret.file</name>
      <value>/etc/security/http_secret</value>
    </property>
    
    <property>
      <name>hadoop.http.authentication.signer.secret.provider</name>
      <value>file</value>
    </property>
    
    <property>
      <name>hadoop.http.authentication.simple.anonymous.allowed</name>
      <value>false</value>
    </property>
    
    <property>
      <name>hadoop.http.authentication.type</name>
      <value>org.apache.hadoop.security.authentication.server.JWTRedirectAuthenticationHandler</value>
    </property>
    
    <property>
      <name>hadoop.http.cross-origin.allowed-headers</name>
      <value>X-Requested-With,Content-Type,Accept,Origin,WWW-Authenticate,Accept-Encoding,Transfer-Encoding</value>
    </property>
    
    <property>
      <name>hadoop.http.cross-origin.allowed-methods</name>
      <value>GET,PUT,POST,OPTIONS,HEAD,DELETE</value>
    </property>
    
    <property>
      <name>hadoop.http.cross-origin.allowed-origins</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.http.cross-origin.max-age</name>
      <value>1800</value>
    </property>
    
    <property>
      <name>hadoop.http.filter.initializers</name>
      <value>org.apache.hadoop.security.AuthenticationFilterInitializer,org.apache.hadoop.security.HttpCrossOriginFilterInitializer</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.ambari-server-bizcloud.groups</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.ambari-server-bizcloud.hosts</name>
      <value>adevthm001-sa.nfra.io</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.hdfs.groups</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.hdfs.hosts</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.hive.groups</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.hive.hosts</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.HTTP.groups</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.HTTP.hosts</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.httpfs.groups</name>
      <value>*</value>
      <text>true</text>
    </property>
    
    <property>
      <name>hadoop.proxyuser.httpfs.hosts</name>
      <value>*</value>
      <text>true</text>
    </property>
    
    <property>
      <name>hadoop.proxyuser.hue.groups</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.hue.hosts</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.kms.groups</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.knox.groups</name>
      <value>users</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.knox.hosts</name>
      <value>adevthm004-sa.nfra.io</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.livy.groups</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.livy.hosts</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.yarn.groups</name>
      <value>*</value>
    </property>
    
    <property>
      <name>hadoop.proxyuser.yarn.hosts</name>
      <value>adevthm001-sa.nfra.io,adevthm004-sa.nfra.io</value>
    </property>
    
    <property>
      <name>hadoop.rpc.protection</name>
      <value>authentication,privacy</value>
    </property>
    
    <property>
      <name>hadoop.security.auth_to_local</name>
      <value>RULE:[1:$1@$0](<EMAIL>)s/.*/ambari-qa/
RULE:[1:$1@$0](<EMAIL>)s/.*/hdfs/
RULE:[1:$1@$0](<EMAIL>)s/.*/spark/
RULE:[1:$1@$0](<EMAIL>)s/.*/yarn-ats/
RULE:[1:$1@$0](.*@C3X.NAVER.COM)s/@.*//
RULE:[2:$1@$0](<EMAIL>)s/.*/ams/
RULE:[2:$1@$0](<EMAIL>)s/.*/ams/
RULE:[2:$1@$0](<EMAIL>)s/.*/ams/
RULE:[2:$1@$0](<EMAIL>)s/.*/hdfs/
RULE:[2:$1@$0](<EMAIL>)s/.*/hive/
RULE:[2:$1@$0](<EMAIL>)s/.*/mapred/
RULE:[2:$1@$0](<EMAIL>)s/.*/hdfs/
RULE:[2:$1@$0](<EMAIL>)s/.*/knox/
RULE:[2:$1@$0](<EMAIL>)s/.*/yarn/
RULE:[2:$1@$0](<EMAIL>)s/.*/hdfs/
RULE:[2:$1@$0](<EMAIL>)s/.*/ranger/
RULE:[2:$1@$0](<EMAIL>)s/.*/keyadmin/
RULE:[2:$1@$0](<EMAIL>)s/.*/rangerusersync/
RULE:[2:$1@$0](<EMAIL>)s/.*/yarn/
RULE:[2:$1@$0](<EMAIL>)s/.*/spark/
RULE:[2:$1@$0](<EMAIL>)s/.*/yarn/
RULE:[2:$1@$0](<EMAIL>)s/.*/yarn-ats/
DEFAULT</value>
    </property>
    
    <property>
      <name>hadoop.security.authentication</name>
      <value>kerberos</value>
    </property>
    
    <property>
      <name>hadoop.security.authorization</name>
      <value>true</value>
    </property>
    
    <property>
      <name>hadoop.security.instrumentation.requires.admin</name>
      <value>false</value>
    </property>
    
    <property>
      <name>hadoop.security.key.provider.path</name>
      <value>kms://<EMAIL>:9292/kms</value>
    </property>
    
    <property>
      <name>hadoop.security.token.service.use_ip</name>
      <value>false</value>
      <text>true</text>
    </property>
    
    <property>
      <name>hue.kerberos.principal.shortname</name>
      <value>hue</value>
    </property>
    
    <property>
      <name>io.compression.codecs</name>
      <value>org.apache.hadoop.io.compress.GzipCodec,org.apache.hadoop.io.compress.DefaultCodec,org.apache.hadoop.io.compress.SnappyCodec</value>
    </property>
    
    <property>
      <name>io.file.buffer.size</name>
      <value>131072</value>
    </property>
    
    <property>
      <name>io.serializations</name>
      <value>org.apache.hadoop.io.serializer.WritableSerialization</value>
    </property>
    
    <property>
      <name>ipc.client.connect.max.retries</name>
      <value>50</value>
    </property>
    
    <property>
      <name>ipc.client.connection.maxidletime</name>
      <value>30000</value>
    </property>
    
    <property>
      <name>ipc.client.idlethreshold</name>
      <value>8000</value>
    </property>
    
    <property>
      <name>ipc.server.tcpnodelay</name>
      <value>true</value>
    </property>
    
    <property>
      <name>mapreduce.jobtracker.webinterface.trusted</name>
      <value>false</value>
    </property>
    
    <property>
      <name>net.topology.script.file.name</name>
      <value>/etc/hadoop/conf/topology_script.py</value>
    </property>
    
  </configuration>