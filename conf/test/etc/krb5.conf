
[libdefaults]
  renew_lifetime = 7d
  forwardable = true
  default_realm = C3X.NAVER.COM
  ticket_lifetime = 24h
  dns_lookup_realm = false
  dns_lookup_kdc = false
  default_ccache_name = /tmp/krb5cc_%{uid}
  #default_tgs_enctypes = aes des3-cbc-sha1 rc4 des-cbc-md5
  #default_tkt_enctypes = aes des3-cbc-sha1 rc4 des-cbc-md5

[domain_realm]
  .nfra.io = C3X.NAVER.COM
  nfra.io = C3X.NAVER.COM

[logging]
  default = FILE:/var/log/krb5kdc.log
  admin_server = FILE:/var/log/kadmind.log
  kdc = FILE:/var/log/krb5kdc.log

[realms]
  C3X.NAVER.COM = {
    admin_server = test-c3s-bizcloud-kdc-ncl.nfra.io
    kdc = test-c3s-bizcloud-kdc-ncl.nfra.io
  }