package main

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"oss.navercorp.com/da-ssp/gfp-api/config"
	"oss.navercorp.com/da-ssp/gfp-api/database"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
	"oss.navercorp.com/da-ssp/gfp-api/middleware"
	"oss.navercorp.com/da-ssp/gfp-api/router"
	"oss.navercorp.com/da-ssp/gfp-api/validator"
	"oss.navercorp.com/da-ssp/gfp-api/zookeeper"
	//"oss.navercorp.com/da-ssp/gfp-api/zookeeper_old"

	"github.com/gin-gonic/gin"
)

var (
	contextTimeout = 20 * time.Millisecond
	log            = logger.GetLogger("default")
	Version        string
	Date           string
)

func main() {
	/*
		gin.Default()로 하면 defaultLogger가 붙기 때문에 customized formatted logger를 붙이려면 New()로 생성
		Default Logger 붙이지 않고 FootPrint middleware에서 로깅함
	*/
	gin.ForceConsoleColor() // Force log's color
	ginEngine := gin.New()

	// L7 Health Check
	ginEngine.GET("monitor/l7check", func(ginCtx *gin.Context) {
		// log.Debugf("l7check 요청...")

		ginCtx.AbortWithStatusJSON(http.StatusOK, gin.H{"ok": "1"})
		return
	})

	// 미들웨어 등록
	middleware.Init(ginEngine)

	// 라우터 등록
	router.Init(ginEngine)

	// 공통 밸리데이터 등록
	validator.Init()

	// zookeeper 등록
	zookeeper.Init()

	//// zookeeper_old 등록
	//zookeeper_old.Init()

	// 서버 시작
	startServer(ginEngine)
}

func startServer(router *gin.Engine) {
	srv := &http.Server{
		Addr:    config.GetConfig("addr").(string),
		Handler: router,
	}

	// 서버 리슨
	go func() {
		// service connections
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Errorf("서버 리슨 중 에러 발생", err)
		}
	}()
	log.Debugf("서버 시작됐음.")

	/*
		1. os로부터 종료 시그널을 받는 채널을 만들어 수신 대기
	*/
	// Wait for interrupt signal to gracefully shutdown the server with
	// a timeout of 5 seconds.
	quit := make(chan os.Signal, 1) // os Signal을 받는 채널 생성
	// kill (no param) default send syscanll.SIGTERM
	// kill -2 is syscall.SIGINT == ctrl+C
	// kill -9 is syscall.SIGKILL but can"t be catch, so don't need add it
	signal.Notify(quit, os.Interrupt, syscall.SIGINT, syscall.SIGTERM) // 이런 시그널이 오면 채널로 알림
	<-quit                                                             // 채널에서 이벤트를 받을 때까지 대기..(여기서 블로킹 되는 거였어)

	/*
		2. 서버 셧다운 전에 명시적인 마무리 작업 하고
	*/
	finalize()

	/*
		3. 서버를 내리고, 기타 뭐가 됐든 정리가 끝날때까지 어느 정도 기다려 줌.
		... finalize()에서 비동기적으로 종료되는 뭔가가 있다면 그걸 기다리기 위한 시간이 필요하겠지.
	*/
	// 일정 시간이 되면 자동으로 컨텍스트에 취소 신호가 전달되도록 컨텍스트 생성
	log.Debugf("2. 서버 셧다운 후 타임아웃만큼 기다림")
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Millisecond) // 3초 후에 컨텍스트 종료될 것임.
	defer cancel()                                                               // 컨텍스트를 취소시킴.

	log.Infof("2. 서버 셧다운 중..")
	err := srv.Shutdown(ctx)
	if err != nil {
		log.Errorf("2. 서버 셧다운 중 에러 발생: %s", err)
	}
	log.Infof("2. 서버 셧다운됐음.")

	/*
		4. 내려간거 확인하고 로그 찍고 종료
	*/
	select {
	case <-ctx.Done(): // 컨텍스트가 취소되면 Done이 호출됨
		log.Debugf("3. 타임아웃됐음.")
	}
	log.Infof("3. 프로세스 종료됨")
}

/*
	서버 종료 전 정리하기
*/
func finalize() {
	// DB 닫기
	database.Disconnect()

	// zookeeper 연결 종료
	zookeeper.Stop()

	//// zookeeper_old 연결 종료
	//zookeeper_old.Stop()
}
