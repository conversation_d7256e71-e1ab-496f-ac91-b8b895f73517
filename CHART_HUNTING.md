### 개요
특정 package 을 기반으로 솔루션을 구축하고 싶을 때, 해당 package 의 helm chart 을 찾아 customizing 하여 사용하게 됩니다. naver n2c 클러스트에 그러한 helm chart 을 배포하기 위해서 필요한 절차들을 아래에 설명합니다.

* Search chart
* Choose chart among several alternatives
* Test chart in n2c dev cluster
* Apply hardening to and promote images
* Deploy chart into n2c production cluster

### Search chart
helm chart 을 검색하면 대개 [arifact-hub][1] 사이트에 가게 됩니다. artifact-hub 에서 특정 package 을 검색하면, 다양한 chart 제작자들이 제공하는 선택지가 주어지는 것을 볼 수 있습니다.

### Choose chart
일반적으로 각 제작자들의 프로젝트 페이지에 가서 현황을 살피고, 문서화 정도을 고려하여 적절히 선택하면 보통입니다. [bitnami][2] 같은 전문적인 chart 제작자부터 개인이 직접 사용할 목적으로 제작하는 경우까지 다양하며, 사용 목적에 따라 신중히 선택하도록 합니다.

bitnami 에서 제공하는 chart 가 보통 가장 신뢰할 만하지만, 전문적인 만큼 설정할 것도 많아서 특정 package 에 대한 전문 지식이 부족하면 상당히 복잡하게 느껴질 수도 있으니 유의합니다.

artifact-hub 에서 제공하는 각 chart 의 정보 중에는 k8s 및 helm 호환성 정보가 있습니다. 예를 들어, k8s 1.14 이상, helm 2 지원 혹은 k8s 1.17 이상, helm 3 만 지원 등의 요구 사항이 있을 수 있습니다. bitnami 같은 전문적인 chart 제작자들은 각 package 대해서 오랜 시간 보수유지를 해왔기 때문에, 초기에 제작된 version 부터 최신 version 까지 제공되므로, 원하는 k8s 및 helm 호환성을 지원하는 chart version 을 찾을 개연성이 높은 편입니다.

참고로 n2c helm 은 helm 2 이며, n2c 공식 언급에 따르면 helm 3 을 지원할 생각은 없다고 합니다.

### Test chart
원활한 설명을 위해서, 구체적인 예를 들어보겠습니다. postgresql database 을 구축을 위해 artifact-hub 에서 bitnami postgresql chart 을 검색하고, helm 2 호환이 되는 chart version 9.8.12 을 (postgresql version 은 11.10 이다) 선택한 경우입니다. chart 정보 페이지 상단에 있는 helm repo url (이 경우, bitnami helm repo) 을 일단 copy 해둡니다.

```
$ # n2c helm
$ # copy 해둔 repo url 로 helm repo 을 등록한다.
$ ncc helm repo add bitnami https://charts.bitnami.com/bitnami
$
$ # bitnami postgresql chart 을 customizing 해서 배포할 helm project 시작
$ mkdir my-pg && cd my-pg
$ vi Chart.yaml
# apiVersion v1 is Helm 2
---
apiVersion: v1
name: my-pg
version: 9.8.12
appVersion: 11.10
description: Helm chart to deploy postgresql database. customized for naver n2c
:wq
$ vi requirements.yaml
dependencies:
  - name: postgresql
    version: 9.8.12
    repository: "https://bitname.com/chart"
:wq
```

artifact-hub 의 bitnami postgresql chart 페이지에 있는 문서를 보고, customizing 할 parameters 을 정리해서 `values.yaml` 을 작성합니다.
```
$ cd my-pg
$ vi values.yaml
postgresql:
  persistence:
    enabled: true
    storageClass: rbd-hdd
    size: 20Gi 
:wq
```
n2c cluster 에 배포하기 위해 PV 설정을 n2c 에서 제공하는 명세로 변경한 경우입니다.

이제 n2c dev cluster (여기서는 `ad1`) 에 chart 을 배포해봅시다.
```
$ cd my-pg
$ ncc set cluster gfp@ad1
$ ncc helm install my-pg-test .
...
```
chart 배포 중에 의존 관계에 있는 chart 을 내려받기 때문에, 성공적으로 배포되면 `charts` 라는 subdirectory 가 생성되고, 그 속에 postgresql-9.8.12.tgz 라는 파일이 생성된 것을 볼 수 있습니다. 원하는 형상으로 배포될 수 있도록 values.yaml 을 수정해가며, 몇 번 더 테스트를 진행합니다.

### Prepare images for production deployment
n2c production cluster 에 배포하려면, chart 에서 참조하는 docker image 에 대해서 다음과 같은 작업을 해주어야 합니다.

* n2c hardening 적용
  * [n2c 문서 참고][4]
* 변경 image 을 n2c 공식 image repo 에 등록
* values.yaml 에 image 정보 변경
* n2c image promotion
  * [Option 1][3]: dev cluster 에 배포하고 `ncc promote` 명령어 실행
  * Option 2: n2c pipeline 기능을 이용해 image build 와 promote 을 실행. [n2c pipeline 문서][5] 중 image build 부분 참고
* n2c production cluster 에 배포

[1]: https://artifacthub.io/
[2]: https://bitnami.com/stacks/helm
[3]: https://pages.oss.navercorp.com/naver-container-cluster/docs/4/1.imagescan/1.harbor_imagescan/
[4]: https://pages.oss.navercorp.com/naver-container-cluster/docs/4/1.imagescan/2.custom_image/
[5]: https://pages.oss.navercorp.com/naver-container-cluster/docs/3/10.pipeline/
