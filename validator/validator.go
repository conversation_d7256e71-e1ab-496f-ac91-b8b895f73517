package validator

import (
	"reflect"
	"time"

	"github.com/araddon/dateparse"
	"github.com/gin-gonic/gin/binding"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"gopkg.in/go-playground/validator.v8"

	"oss.navercorp.com/da-ssp/gfp-api/logger"
)

var log = logger.GetLogger("default")

/*
	validator 등록
*/
func Init() {
	// validator 등록
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		v.RegisterValidation("validateObjectId", validateObjectId)
		v.RegisterValidation("validateDateFormat", validateDateFormat)
		v.RegisterValidation("validateYmdhStrFormat", validateYmdhStrFormat)
	}
}

func validateObjectId(
	v *validator.Validate, topStruct reflect.Value, currentStructOrField reflect.Value,
	field reflect.Value, fieldType reflect.Type, fieldKind reflect.Kind, param string,
) bool {
	if id, ok := field.Interface().(string); ok {
		_, e := primitive.ObjectIDFromHex(id)

		if e != nil {
			log.Debugf("validateObjectId error :: %+v", e)
			return false
		}
	}

	return true
}

func validateDateFormat(
	v *validator.Validate, topStruct reflect.Value, currentStructOrField reflect.Value,
	field reflect.Value, fieldType reflect.Type, fieldKind reflect.Kind, param string,
) bool {
	// FORMAT :: 2019-08-01 01:02:03 또는 2019-08-01T01:02:03.000Z
	if date, ok := field.Interface().(string); ok {
		_, e := dateparse.ParseAny(date)

		if e != nil {
			log.Debugf("validateDateFormat error :: %+v", e)
			return false
		}
	}

	return true
}

func validateYmdhStrFormat(
	v *validator.Validate, topStruct reflect.Value, currentStructOrField reflect.Value,
	field reflect.Value, fieldType reflect.Type, fieldKind reflect.Kind, param string,
) bool {
	// FORMAT :: 2019080101
	if dateStr, ok := field.Interface().(string); ok {
		_, e := time.Parse("2006010215", dateStr)

		if e != nil {
			log.Debugf("validateYmdhStrFormat error :: %+v", e)
			return false
		}
	}

	return true
}
