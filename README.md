# NAM Batch

Naver Ad Manager 서비스에서 Report 생성 관련 batch 을 담당하는 과제을 위한 repo 입니다.

NAM batch 는 airflow 에 기반하여 구현하고 운영되고 있습니다. 여기서는 airflow 의 빌드/배포부터 운영, 그리고 airflow DAG 개발을 위한 환경 구성에 관련된 내용까지 기술합니다.

## 빌드/배포

[WIKI](https://wiki.navercorp.com/pages/viewpage.action?pageId=866567125)

## 운영

[WIKI](https://wiki.navercorp.com/pages/viewpage.action?pageId=866567138)

## DAG 개발 환경 구성

[WIKI](https://wiki.navercorp.com/pages/viewpage.action?pageId=866567780)

## 관련 Repo

* [k8s-lib](https://oss.navercorp.com/da-ssp/k8s-lib)
* [airflow-dev](https://oss.navercorp.com/da-ssp/airflow-dev)
* [airflow-dags](https://oss.navercorp.com/da-ssp/airflow-dags)
* [sparkling-s](https://oss.navercorp.com/da-ssp/sparkling-s)
* [nam-api](https://oss.navercorp.com/da-ssp/nam-api)

## Naver Platflorm Reources

**Pasta Dev**
* N2C workspace [nam-batch](https://da-ssp.pasta-dev.navercorp.com/ncc/nam-batch/dashboard)
  * airflow instance 4 개 배포 (test, dev, bitna, sylph)
  * pipeline for sparkling image build
  * pipeline for sparkling image build/promote
* Ceph volumes
  * postgresql volume
  * redis volume
  * worker log volume

**Pasta Real**
* N2C workspace [nam-batch](https://da-ssp.pasta.navercorp.com/ncc/nam-batch/dashboard)
  * airflow real instance
* Ceph volumes
  * postgresql volume
  * redis volume
  * worker log volume