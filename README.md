# 개요
Naver Ad Manager API Server

# Development
## Components
- Node 16
- Typescript 4.3
- NestJS 8
- MongoDB 4.2 / Mongoose 6
## Naver Pasta Platform
- Nubes
  - Storage for report files
  - Resources
    - dev Pasta > DA-SSP > bucket:nam_api_dev
    - real Pasta > DA-SSP > bucket:nam_api_report
- NELO
  - error log, access log
  - Resources
    - dev Pasta > DA-SSP > project:nam-api
    - real Pasta > DA-SSP > project:nam_api (not "nam-api")
- nClavis
  - Secret storage
  - Resources
    - dev-nclavis.navercorp.com > GFP > nam-api
    - nclavis.navercorp.com > GFP > nam-api-master
- N2C/N3R
  - N3R ELB
  - K8S with CRD `NAT`
  - Pipeline/Workflow (TTS 연동)
  - Resources
    - dev Pasta > DA-SSP > workspace:nam-api (ad1 cluster)
    - real Pasta > DA-SSP > workspace:nam-api (ar2 cluster)
    - n3r.navercorp.com > project:nam-api
      - Domain
        - nam-api.io.naver.com
      - Route/Backend
        - https://nam-api.io.naver.com/v1/otd/download
        - http://app.nam-api.svc.ar2.io.navercorp.com:8080/v1/otd/download
      - Public domain 전환 및 anti-DDOS 설정 요청 기록
        - https://yobi.navercorp.com/elb/posts/62?referrerId=-513108331

## Local Development
### Run app for development
```bash
$ cd project-home
$
$ # possible env vars for this app
$ #   - NEST_DEBUG=xx
$ #   - CONFIG_FILE=path or CONFIG_DIR=path
$ #   - NAM_API_CONFIG_FILE=path (override CONFIG_FILE if both vars defined)
$ #   - NAM_API_PUBLIC_URL=http://localhost:3001
$ #   - NAM_API_PORT=8080
$ #   - NAM_API_NUBES_TEST=xx
$ #   - NAM_API_NCLAVIS_TEST=xx
$ #   - NCLAVIS_NEOID_TOKEN='<token>'
$ [var=value...] npm run start:dev
...
$ # example (with config item 'nclavis.authn' == 'neoid')
$ NEST_DEBUG=yes CONFIG_FILE=./config.local.yaml NCLAVIS_NEOID_TOKEN='token-data' npm run start:dev
...
```
### NestJS CLI
```bash
$ node --version
v16.13.2
$ npm install -g @nestjs/cli@8.2
...
$ # Create new NestJS application
$ nest new myproject
...
$ cd myproject
$ nest --help
...
$ # Use 'nest' command for generating templates for module/controller/service...
$ nest generate module report
... src/report/report.module.ts
$ nest generate controller report
... src/report/report.controller.ts
$ nest generate service report
... src/report/report.service.ts
... src/report/report.service.spec.ts
$ # sub module
$ nest generate module ad-stat report
... src/report/ad-stat/ad-stat.module.ts
```

# N2C 배포
N2C Web 에서 진행하는 배포 절차를 대략적으로 설명합니다. 아래 배포 절차에서 `okjo` 라는 pipeline/instance 이름은 각 개발자의 식별자이니, 각자 적절히 naming 하고 pipeline 을 생성합니다.

dev zone N2C Web: **dev PASTA > DA SSP > n2c nam-api workspace** (ad1 cluster)

real zone N2C Web: **real PASTA > DA SSP > n2c nam-api workspace** (ar2 cluster)

- develop branch 에서 feature branch 을 fork 하여 local 로 작업합니다.
- local 작업이 마무리되면, dev zone N2C web 에서 `okjo` pipeline 으로 배포합니다.
  - feature branch 작업 내역이 sona/toothless 등 OSS tool 점검을 통과하는 지 확인합니다.
  - feature branch 에 `dev` profile 을 위한 config.yaml 과 values/dev.yaml 을 확인합니다.
- `okjo.nam-api.svc.ad1.io.navercorp.com` domain 으로 접근하여 결과물을 점검합니다.
- QA 준비가 되면, develop 에 merge 하여 `qa` pipeline 을 실행하여 배포합니다.
  - instance name 은 `app-dev` 이며, 대상 branch 는 PR 을 통과한 qa branch 입니다.
  - `app-dev` instance 는 QA 용 공용이니, 각 개발자는 QA 일정을 사전에 조정합니다.
- `app-dev.nam-api.svc.ad1.io.navercorp.com` domain 으로 QA 진행
  - QA 을 통과하지 못한다면, local 개발부터 QA 까지 과정을 반복합니다.
- real 배포 준비
  - stage/real 을 위한 config 파일과 values 파일을 점검합니다.
  - real 배포용 docker image 을 위한 release TAG 을 준비합니다.
  - TTS 등록, 긴급배포시 생략 가능
- real N2C Web 에서 `nam-api-cd` workflow 을 실행합니다.
  - develop branch 에서 docker image build/promote
  - stage 배포 및 최종 점검
    - `app-stg.nam-api.svc.ar2.io.navercorp.com` domain
  - real Blue/Green 배포
    - `app.nam-api.svc.ar2.io.navercorp.com` domain
    - Green 배포와 traffic 할당이 된 상태에서 외부 연결을 확인합니다.
      - `https://nam-api.io.navercorp.com`
    - 외부 연결까지 확인되면, Blue 배포판 삭제를 진행합니다.
- real 배포가 성공하면, develop 을 main 에 merge 합니다.

# Notes
### VSCode remote container (a.k.a devcontainer)
VSCode 셋팅을 위한 블로그 포스팅
- https://wiki.navercorp.com/pages/viewpage.action?pageId=670184810
### Typescript with CommonJS format
NPM 으로 설치하는 LIB 중에는 최신 버전이 ESM 만 지원하도록 변경되어서 CommonJS 모듈로 transpile 하도록 설정된 typescript app 에서 사용할 수 없는 경우가 있습니다. 이런 경우, 대부분 이전 버전을 설치하면 해결됩니다. 자세한 사항은 해당 LIB 문서를 참고합니다. (예, p-limit package)

관련 블로그 포스팅
- https://wiki.navercorp.com/display/dapd/2022/01/13/Javascript+Module+Anomalies+when+typescript+targets+nodejs+runtime