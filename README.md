# GFP API
- GFP API 서버입니다.<br>
- 리포트 다운로드 및 Publisher/AdProvider의 메타 데이터를 조회합니다.<br>
- 광고 미리보기를 위한 메타 데이터를 관리합니다.<br>

<br/>
구현 및 환경, 빌드/배포에 대한 자세한 사항은 [위키](https://wiki.navercorp.com/pages/viewpage.action?pageId=438357956#GFPAPI%EC%84%9C%EB%B2%84-%EC%97%85%EB%AC%B4%EB%B6%84%EC%9E%A5)를 참조하세요.


# Go(go1.12.9) 설치
1. sudo 로 서버에 로긴
2. /usr/local로 이동
	```
	$ cd /usr/local
	```
3. https://golang.org/dl/ 에서 linux 버전을 다운
	```
	$ sudo wget "https://dl.google.com/go/go1.12.9.linux-amd64.tar.gz"
	```
4. 압축해제
	```
	$ sudo tar -zxvf go1.12.9.linux-amd64.tar.gz
	```


# 사용자 환경 설정
> .bash_profile 설정
```
PATH=$PATH:$HOME/.local/bin:$HOME/bin
PATH=$PATH:/usr/local/go/bin

export PATH
export GOPATH=$HOME/go
export GFPAPI_PROFILE=test
```

# apidoc 생성 시
```
apidoc -f .go -i ./ -o ./doc
```