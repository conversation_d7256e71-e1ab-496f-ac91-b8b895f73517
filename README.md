# airflow-dev
airflow local development

## Local development setup
* git-clone and init this project
```
  $ git clone git@airflow-dev-repo airflow-dev
  $ cd airflow-dev
  $ git submodule update --init
  ...
  $ tree -d
  .
  +-- airflow-dags
  |   +-- dags
  |   +-- plugins
  +-- docker
  +-- support
  ...
  $ (cd airflow-dags; git status)
  ...
```
* airflow python 3.8 개발 환경
  * `support/local-dev.sh` 참고하여 airflow python 개발 환경 구축
* `airflow.cfg` 필요하면 수정
* `cd docker; docker-compose up -d`
  * `docker/docker-compose.yml` 이해 필요
  * `docker logs docker_airflow_scheduler_1` 로 오류 없는 지 확인
    * db init 이 완료되기 전에 scheduler 가 db 에 접속하려고 시도하는 경우, 오류 발생 가능
    * 오류 발생하는 경우 `docker-compose` 로 서비스 restart 하면 대개의 경우 정상화
      * `docker-compose restart airflow_scheduler`
* Web 에서 localhost:8080 으로 airflow WebUI 진입, admin/admin 으로 로긴
* `airflow-dags` 아래에 DAG file 과 관련 python 모듈을 작성
  * 아래 python 개발 도구에서 설정할 환경 정의 참고
* 테스트가 끝난 DAG 파일은 다음과 같이 repo 에 반영
  * `airflow-dags` 는 git-submodule 이므로, DAG 추가/수정 후에는 아래 git 명령 수행
  * `cd airflow-dags; git add new-or-modified-dag.py; git commit -m "message"; git push`
  * `cd ..; git commit -m "Update airflow-dags"; git push`

python 개발 도구에서 참고하는 `pyrightconfig.json` 파일 내용
```
{
    "include": [
        "airflow-dags/dags",
        "airflow-dags/plugins"
    ],
    "executionEnvironments": [
        {
            "root": "airflow-dags/dags",
            "extraPaths": ["airflow-dags/plugins"]
        },
        {
            "root": "airflow-dags/plugins",
            "extraPaths": ["airflow-dags/dags"]
        }
    ]
}
```

이렇게 개발/검증된 DAG file 과 module 들을 dag repo 추가하면, git-sync 로 자동 배포. airflow webUI 로 DAG 확인
