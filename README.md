# k8s-lib
k8s 관련 리소스 공유. docker image, helm chart 등

# Helm Charts
여기에서 제공되는 helm charts 는 helm v3 로 배포되어야 합니다. N2C 에서 제공하는 helm 은 v2 입니다. 따라서, 대부분의 chart 디렉토리에는 scripts/deploy.sh 스크립트가 제공되니, 참고하여 배포하시기 바랍니다.

helm v3 설치
```
$ # helm v3 설치 문서
$ # https://helm.sh/docs/intro/install/
$ 
$ /path/to/helm version
version.BuildInfo{Version:"v3.6.2", GitCommit:"ee407bdf364942bcb8e8c665f82e15aa28009b71", GitTreeState:"clean", GoVersion:"go1.16.5"}
```

airflow-comm 에서 제공되는 chart 을 배포하는 절차를 예시합니다.
```
$ ln -s /path/to/helm $HOME/bin/helm3
$
$ cd charts/airflow-comm
$
$ # airflow chart 는 bitnami helm charts 에 의존하므로, bitnami repo 을 추가합니다.
$ # chart 의존성은 requirements.yaml 을 참고하고 (helm v3 에서는 Chart.yaml 에 의존성을 기록하지만,
$ # v2 방식으로 requirements.yaml 에 기록해도 동작합니다), 해당 chart 의 github 이나
$ # airtifacthub.io 에서 chart 관련 정보를 수집합니다. (구글링 예:  "airflow helm chart")
$
$ helm3 repo add bitnami https://charts.bitnami.com/bitnami
...
$ # 배포 전에 values.yaml 을 점검하고, dry-run 으로 실행하여 산출물을 점검합니다.
$ HELM=helm3 scripts/deploy.sh dev --debug --dry-run my-airflow
...
$ HELM=helm3 scripts/deploy.sh dev my-airflow
...
$
```
