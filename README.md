# SSP-BATCH
- node v10.x.x
- Server Info
   - test
       * test-gfp-batch-v2-ncl (*************)
       * 메모리 8GB, CPU 4core, CentOS 7.4 64Bit
   - real
       * avgfpbatch03-glad (**************) / avgfpbatch04-glad (**************)
       * 메모리 32GB, CPU 8core, CentOS 7.4.1708 / 64
       * vip : *********

## Setting to local
1. get source from git
   ```
   $ git clone https://oss.navercorp.com/da-ssp/ssp-batch.git
   ```
2. install package dependencies
   ```
   $ npm install
   ```
3. execute server module on a terminal
   ```
   $ npm run local
   ```
4. open browser and type url
   ```
   http://localhost:3000/
   ```

## ssp-common-code
- https://oss.navercorp.com/da-ssp/ssp-common-code

## jenkins build
- test : https://ad1.jenkins.navercorp.com/gfp/gfp-build/job/build-ssp-batch-test/
- real : https://ad1.jenkins.navercorp.com/gfp/gfp-build/job/build-ssp-batch-production/
- Docker Registry JFrog -> Harbor로 이관 히스토리 : https://wiki.navercorp.com/pages/viewpage.action?pageId=533668959
- ncc 빌드가 오래걸리는 이유 (https://yobi.navercorp.com/counter/posts/2930#1606213594265)
  - 변경된 파일들만 diff 수행 후 이미지에 넣어햐 하는데(즉, 증분 비교), 전체 비교를 하고 있다 
  - ncc에서 이미지 빌드할 때 사용하고 있는 buildkit에 버그가 있으며, buildkit 조치가 취해지면 ncc 에서도 반영할 예정이라고 한다.

## nDeploy
- test: https://da-ssp.pasta.navercorp.com/ndeploy/v2/workspaces/3318/scenarios/3719198
- real : https://da-ssp.pasta.navercorp.com/ndeploy/v2/workspaces/3318/scenarios/3731393
   - SSP 배포 캘린더/변경 공지/gfp-deploy 이슈 등록 : https://oss.navercorp.com/da-ssp/gfp-deploy

## gfp-scheduler
- https://ad1.jenkins.navercorp.com/gfp/gfp-scheduler/
