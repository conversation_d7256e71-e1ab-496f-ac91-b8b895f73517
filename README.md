### 이미지 빌드 전 준비할 사항
- conf/real과 conf/test 하위에 각 환경에 맞는 gfp-data.keytab 파일을 위치키셔주세요.
- 참고로 keytab 파일은 git에 올리면 안됩니다.


### Dockerfile_xx_test, Dockerfile_xx_real
- Dockerfile_xx_test는 AdPipeline Dev에서 제공하는 테스트 환경의 하둡 클러스터로 연결되는 이미지입니다.
    - namespace : bizcloud

- Dockerfile_xx_real은 C3에서 제공하는 리얼 환경의 하둡 클러스터로 연결되는 이미지입니다.
    - namespace : pg07
    - 로컬에서 리얼 환경의 리소스를 사용하여 검증하기 위해 사용하는 이미지입니다.
    - 실행에 각별히 주의해 주세요.


### Dockerfile_sparkling_xx
- ssp-batch와 sparkling이 있는 이미지입니다.
- ssp-batch를 위한 port 연결이 필요합니다.
    - 디폴트 12000
- volume mount가 필요합니다.
    - ssp-batch 소스코드와 각종 리포트 파일 저장을 위한 local_download, owfs_download 경로를 본인의 환경에 맞게 마운트 해 주세요.
    - sparkling.jar와 로컬에서 실행할 $SPARK_HOME/bin/*.sh 들을 위해 호스트의 SPRAK_HOME을 volume mount해 주세요.


### Dockerfile_sparkling_s_xx
- sparkling-s를 위한 이미지입니다.
- volume mount가 필요합니다.
    - sparkling-s.jar와 로컬에서 실행할 $SPARK_HOME/bin/*.sh 들을 위해 호스트의 SPRAK_HOME을 volume mount해 주세요.


### Dockerfile_all_test
- ssp-batch, sparkling, sparkling-s 모두 포함된 버전
- volume mount 필요
- container 실행 후, 최초 수동으로 csync 명령어 실행하여 관련모듈 추가 설치 필요


### Dockerfile_by_env
- 빌드 시 실행 환경 argument 인 'BUILD_ENV' 를 입력 받아 하나의 Dockerfile 로 통합
- nClavis API 를 통해 keytab 파일 세팅 (token 기반 인증) 
- ssp-batch, sparkling, sparkling-s 모두 포함된 버전
- volume mount 및 port binding 필요
- Dockerfile 내 ENTRYPOINT 에서 support/bin 내의 스크립트를 실행하여 구동


### 이미지 빌드/실행/컨테이너 접속
각 Dockerfile의 첫 주석 문단을 확인하세요.
```
#######################################################################################
#################################### Dockerfile 안내 ##################################
# 이미지 빌드, 
# docker build -t sparkling-test -f Dockerfile_sparkling_test .

## 빌드된 이미지 실행
# docker run -it --rm -p 12000:12000 -v D:/Projects/ssp-develop/sparkling:/home1/irteam/repo/sparkling -v D:/Projects/ssp-develop/ssp-batch:/home1/irteam/repo/ssp-batch -v D:/local_download:/home1/irteam/local_download -v D:/owfs_download:/home1/irteam/owfs_download sparkling-test:latest
# 
# args
#  -d: background(detached) 모드로 실행되어 컨테이너 실행로그를 터미널로 보여주지 않음
#      docker run 으로 컨테이너 진입 후 ctrl + D 또는 exit 명령으로 빠져나와도 종료되지 않음 (docker start 후 exec 한 경우에는 원래 종료 안됨)
#  -it: (또는 --interactive와 --tty) 컨테이너와 상호 작용하는 대화형 모드로 컨테이너 내부의 터미널에 접속. 
#      컨테이너 내부에서 명령어를 실행하고 터미널 세션을 유지할 수 있음.

## 컨테이터에 들어가기
# docker exec -it {CONTAINER_ID} bash
```
