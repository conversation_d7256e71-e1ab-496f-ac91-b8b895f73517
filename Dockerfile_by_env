#######################################################################################
#################################### Dockerfile 안내 ###################################
##### env 정보에 해당하는 ./conf 에 존재하는 디렉토리 이름 중 하나에 따라 docker 환경을 구성하는 Dockerfile

#### 애플 칩인 경우 아래 설정 추가
#   export DOCKER_DEFAULT_PLATFORM=linux/amd64

##### 이미지 빌드
#   docker build --build-arg BUILD_ENV=(test|real) -t (test|real) -f Dockerfile_by_env .

##### 빌드된 이미지 실행
# 일반
#   docker run -it -d -p 12000:12000 -v /Users/<USER>/Documents/git/ssp-batch:/home1/irteam/apps/ssp-batch -v /Users/<USER>/Documents/git/sparkling:/home1/irteam/apps/sparkling -v /Users/<USER>/Documents/git/sparkling-s:/home1/irteam/apps/sparkling-s -v /Users/<USER>/Documents/logs/backup_restore:/home1/irteam/backup_restore --name test test:latest
# 주연
#   > windows
#       docker run -it --rm -p 12000:12000 -v D:/Projects:/home1/irteam/projects -v D:/local_download:/home1/irteam/local_download -v D:/owfs_download:/home1/irteam/owfs_download -v D:/backup_restore:/home1/irteam/backup_restore -v D:/Temp:/home1/irteam/temp --name test test:latest
#   > mac test
#       docker build --build-arg BUILD_ENV=test -t test -f Dockerfile_by_env .
#       docker run -it --rm -p 11000:11000 -p 3001:3001 -v /Users/<USER>/Projects:/home1/irteam/projects -v /Users/<USER>/Storage/local_download:/home1/irteam/local_download -v /Users/<USER>/Storage/owfs_downloa:/home1/irteam/owfs_download -v /Users/<USER>/Storage/backup_restore:/home1/irteam/backup_restore -v /Users/<USER>/Storage/Temp:/home1/irteam/temp --name test test:latest
#   > mac real
#       docker build --build-arg BUILD_ENV=real -t real -f Dockerfile_by_env .
#       docker run -it --rm -p 12000:12000 -p 3001:3001 -v /Users/<USER>/Projects:/home1/irteam/projects -v /Users/<USER>/Storage/local_download:/home1/irteam/local_download -v /Users/<USER>/Storage/owfs_downloa:/home1/irteam/owfs_download -v /Users/<USER>/Storage/backup_restore:/home1/irteam/backup_restore -v /Users/<USER>/Storage/Temp:/home1/irteam/temp --name real real:latest
# 빛나
#   docker build --build-arg BUILD_ENV=test -t data-test -f Dockerfile_by_env .
#   docker run -it -d -p 12000:12000 -v /Users/<USER>/Documents/git/ssp-batch:/home1/irteam/apps/ssp-batch -v /Users/<USER>/Documents/git/sparkling-s:/home1/irteam/apps/sparkling-s -v /Users/<USER>/Documents/logs/backup_restore:/home1/irteam/backup_restore --name data-test data-test:latest
#   docker build --build-arg BUILD_ENV=real -t data-real -f Dockerfile_by_env .
#   docker run -it -d -p 3000:3000 -v /Users/<USER>/Documents/git/ssp-batch:/home1/irteam/apps/ssp-batch -v /Users/<USER>/Documents/git/sparkling-s:/home1/irteam/apps/sparkling-s -v /Users/<USER>/Documents/logs/backup_restore:/home1/irteam/backup_restore --name data-real data-real:latest

# 인식
#docker run -it -d -p 12000:12000 \
# -v /Users/<USER>/project/ssp-batch:/home1/irteam/apps/ssp-batch \
# -v /Users/<USER>/project/NAM/sparkling:/home1/irteam/apps/sparkling \
# -v /Users/<USER>/project/sparkling-s:/home1/irteam/apps/sparkling-s \
# -v /Users/<USER>/project/local_download:/home1/irteam/local_download \
# -v /Users/<USER>/project/owfs_download:/home1/irteam/owfs_download \
# -v /Users/<USER>/project/backup_restore:/home2/backup_restore \
# --name test test:latest

#docker run -it -d -p 3000:3000 \
# -v /Users/<USER>/project/ssp-batch:/home1/irteam/apps/ssp-batch \
# -v /Users/<USER>/project/NAM/sparkling:/home1/irteam/apps/sparkling \
# -v /Users/<USER>/project/sparkling-s:/home1/irteam/apps/sparkling-s \
# -v /Users/<USER>/project/local_download:/home1/irteam/local_download \
# -v /Users/<USER>/project/owfs_download:/home1/irteam/owfs_download \
# -v /Users/<USER>/project/backup_restore:/home2/backup_restore \
# --name real real:latest

##### 컨테이터에 들어가기
# docker exec -it test bash


#######################################################################################
##################################### C3 기본이미지 ######################################
# C3 Docker Image 가져오기
FROM reg.navercorp.com/c3/c3s-pan-env:centos7-20240905

USER root

# docker 환경이 이루고 있는 환경. NODE_ENV=local 와 다름.
ARG BUILD_ENV

ENV VIRTUAL_ENV=${BUILD_ENV}
ENV NODE_ENV=local

ENV HOME /home1/irteam
ENV APPS_HOME /home1/irteam/apps
ENV C3_HOME /home1/irteam/apps/c3

# WORKDIR 은 빌드가 진행될 디렉토리를 명시하는 기능이지만, 존재하지 않는 경로라면 자동으로 생성해준다.
WORKDIR $C3_HOME

RUN echo "alias l='ls -al'" >> $HOME/.bashrc \
    && echo "alias k='kubectl'" >> $HOME/.bashrc


#######################################################################################
####################################### n2c ###########################################
RUN mkdir $APPS_HOME/n2c
WORKDIR $APPS_HOME/n2c

# RUN ./n2c install -f 대신 아래 명령어 사용하여 # 사용자 입력을 요구하는 명령어 실행(n2c 다운받을 경로 물어볼 때 엔터가 자동 입력 되도록 함)
RUN wget https://artifactory.navercorp.com/artifactory/dist/n2c/pkgs/n2c/linux-amd64/stable/n2c \
    && chmod +x ./n2c \
    && echo | ./n2c install -f

# helm 버전 3 설치(n2c에서 디폴트로 깔리는 helm은 버전 2)
RUN curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 \
    && chmod 700 get_helm.sh \
    && ./get_helm.sh \
    && cp /usr/local/bin/helm $APPS_HOME/n2c/helm

ENV PATH $APPS_HOME/n2c:$PATH


#######################################################################################
####################################### python 3.8 ####################################
WORKDIR $HOME
ARG PYTHON_VER=3.8.19

# install python 3.8
RUN yum install -y gcc \
                    openssl-devel \
                    bzip2-devel \
                    libffi-devel

ADD https://www.python.org/ftp/python/$PYTHON_VER/Python-$PYTHON_VER.tgz .

RUN tar -xvf Python-$PYTHON_VER.tgz --no-same-owner -C $APPS_HOME \
	&& rm Python-$PYTHON_VER.tgz

WORKDIR $APPS_HOME/Python-$PYTHON_VER

RUN ./configure --enable-optimizations \
    && make install \
    && echo 'alias python="/usr/local/bin/python3"' >> $HOME/.bashrc \
    && source $HOME/.bashrc

# install pip
RUN curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py \
    && python3 get-pip.py \
    && python3 -m pip install sqlalchemy_utils pendulum pymongo


#######################################################################################
######################################## nodejs #######################################
# install utilities
RUN yum install -y yum-plugin-ovl \
					gcc-c++ \
					make \
					unzip \
					wget \
					git-core \
					logrotate \
					net-tools \
					krb5-devel \
					pigz

# nvm environment variables
ENV NODE_VERSION 10.15.3
ENV NVM_VERSION 0.33.11
ENV NPM_VERSION 6.14.12

# install nvm, node and npm
RUN curl --silent -o- https://raw.githubusercontent.com/creationix/nvm/v$NVM_VERSION/install.sh | bash \
    && source $HOME/.nvm/nvm.sh \
	&& nvm install $NODE_VERSION \
	&& nvm alias default $NODE_VERSION \
	&& nvm use default

# add node and npm to path so the commands are available
ENV NODE_PATH $HOME/.nvm/v$NODE_VERSION/lib/node_modules
ENV PATH $HOME/.nvm/versions/node/v$NODE_VERSION/bin:$PATH

RUN npm install -g npm@$NPM_VERSION \
    && npm install -g node-gyp

######################################################################################
################################# nubescli and csync #################################
WORKDIR $APPS_HOME

# nubescli 설치
RUN mkdir -p $APPS_HOME/nubes \
    && curl http://owfsrepo.navercorp.com/nubes/dist/nubescli_latest/linux/nubescli -o $APPS_HOME/nubes/nubescli \
    && chmod +x $APPS_HOME/nubes/nubescli

# csync 설치
# 파이썬 'requests' 모듈이 필요하여 같이 설치 (python2)
RUN curl https://cuve.navercorp.com/doc/kr/get-cuve-release.py | python - cuve-eco csync \
    && tar -xzf csync-*.tar.gz --no-same-owner \
    && rm csync-*.tar.gz \
    && curl https://bootstrap.pypa.io/pip/2.7/get-pip.py | python - \
    && python -m pip install requests \
    && ln -s $APPS_HOME/csync-1.2.14/bin/csync /usr/bin/csync

ENV PATH $APPS_HOME/nubes:$APPS_HOME/csync:$PATH


#######################################################################################
####################################### spark 3.2.4 ###################################
# spark 3.2.4 다운로드
ARG SPARK_VER=spark-3.2.4-bin-without-hadoop
ADD https://archive.apache.org/dist/spark/spark-3.2.4/$SPARK_VER.tgz .

# spark 3.2.4 설치
RUN tar -xzf $SPARK_VER.tgz --no-same-owner -C $APPS_HOME \
	&& rm $SPARK_VER.tgz

ARG SPARK_HOME=$APPS_HOME/$SPARK_VER
# airflow에서 kubectl apply 할 때 적용되는 환경 변수
ENV SPARK_HOME $SPARK_HOME
ENV PATH $SPARK_HOME/bin:$PATH

# /home1/irteam/apps 경로 권한을 irteam 으로 변경
# kubectl exec로 들어갈 때 스파크 환경을 맞춰주기 위해 .bashrc 설정
RUN chown -R irteam:irteam $APPS_HOME \
    && echo "export SPARK_HOME=$SPARK_HOME" >> $HOME/.bashrc \
	&& echo "export PATH=$SPARK_HOME/bin:$PATH" >> $HOME/.bashrc


#############################################################################################
####################################### c3s conf ############################################
WORKDIR $HOME

COPY ./conf/$BUILD_ENV $C3_HOME
COPY ./support/bin $HOME/support/bin

# home1/irteam/.bashrc 설정. entrypoint 에서 source 수행
RUN echo 'export KRB5_CONFIG=$C3_HOME/etc/krb5.conf' >> $HOME/.bashrc \
    && echo 'export KRB5CCNAME=$C3_HOME/etc/krb5cc' >> $HOME/.bashrc

# 크론탭 등록
RUN echo -e '# kerberos 티켓 갱신\n10 8,20 * * * $HOME/support/bin/run_kinit.sh > $HOME/support/bin/run_kinit.sh.log 2>&1'  >> /var/spool/cron/root


#############################################################################################
####################################### batch env setting ###################################
RUN mkdir deploy && ln -s /home1/irteam/local_download /home1/irteam/deploy/local_download

# 쉘 프로픔트에서 사용자 input 없이 오래 대기해도 자동으로 빠져나오지 않게 하기 위해 타임아웃을 없앰
RUN echo "export TMOUT=0" >> /etc/bash.bashrc

# https://stackoverflow.com/questions/37904682/how-do-i-use-docker-environment-variable-in-entrypoint-array
ENTRYPOINT ${HOME}/support/bin/entrypoint.sh
