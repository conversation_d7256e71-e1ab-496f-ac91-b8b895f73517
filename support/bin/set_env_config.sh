#!/bin/bash

# VIRTUAL_ENV 에 따라 c3 관련 파일들을 세팅
if [ $VIRTUAL_ENV == "real" ] || [ $VIRTUAL_ENV == "stage" ]; then
	URL="https://apis.nclavis.navercorp.com/kms/consumer"
	RESOURCE_ID="EE7PhRuJ_etImywU8ExRemHu4rw="
	ACCESS_TOKEN="DrlREjMPxZe8dtfrCTp6Cocjh1eVGdpMzAKPuAgEZwbiQF03wq4QHfp6xnKperjg2f40hdJrFLaIDAPwFdKeSYEJ/MtHSPXktfkhsnIYK1beYh0SlTnyYO+sTDGvgJi1+GPsD/uqJeJmKE9hEd2GVo7EjuZHQq6eFsGRPy8Wljz9T5etCvxVnsz9bRmkEzeDWYwM5lXWI+IRaCkeC38e32lf0cICaC2yno21ZK6tASCXa7p+08Ee1B1v0nfF2vRkZIQX8VQ8bZyItukhDD1nTvxTV0/cIk5bT4bCh1yW7Noku4xecHFlsX/3qZ52fWfVUNSIR8jJ7d0zvruaq4bvBWEq/kk49QVfU/GTHFJ84O8="
	C3_REALM="C3.NAVER.COM"
	PROMPT_STYLE="38;5;209m"

#	ncc cluster set nam-batch@cr1 # ncc cluster 설정. 안전상 테스트 환경에서만 실행. 리얼에서는 컨테이너에 접속하여 수동 실행
else
	URL="https://dev-apis.nclavis.navercorp.com/kms/consumer"
	RESOURCE_ID="4HXcsOPwA43So3r16DxORi6NP7I="
	ACCESS_TOKEN="IwnQV1ruKzkfNi5Q9wDUc5UUK7VUIomL1TGXb+cOGunnltEFDwwPhRUacuXYI17MaENmVpvyZDidCmJZXXBEyQl9QlF17FEt40gx7fsCLfuQwXGxWADU2dnAH4H7NafIKIsKlw0mveGyD16MlkLfqT7lMeEIItmwHKNaraK2zUamZu2rfBO6EgqwqH0RRj5JFKuyUkC8Q9+dYB606HfiZxsDqHxZ69vP9Xldgnb1p0YzxlmpSoIPuAq4t1cDGaHsRE6ZE97UNH08mURdx7/Rb08KOnMovCDGooVL6JGMtYgUkbr18tYxKALkgkwqZmTCWijC0lmCciR6gPZVA5N7U3D7atNbkv5IlcBk09UQ6dwKo94m46C4rlEZLqK5+2EQ8jl2MeXdzfbVfTEcvQicbw=="
	C3_REALM="C3X.NAVER.COM"
	PROMPT_STYLE="38;5;81m"

    ncc cluster set nam-batch@ad1
fi

KRB5KEYTAB="$C3_HOME/gfp-data.keytab"

# KRB5KEYTAB 파일이 실제로 있는지 확인 (로컬 keytab 이 build 시 COPY 되었을 수 있음)
# 없을 때만 nClavis API 통해 토큰으로 keytab 저장
[ ! -f "$KRB5KEYTAB" ] &&
echo "$KRB5KEYTAB 존재하지 않으므로 nClavis 요청" && \
curl -v -F "cipherFile=@$C3_HOME/etc/cipher.txt" \
	-H "nclavis-access-token: $ACCESS_TOKEN" \
	-H "nclavis-cipher-meta: $(head -n1 < $C3_HOME/etc/cipher.txt)" \
	"$URL/$RESOURCE_ID/decrypt/binary" \
	--output $C3_HOME/gfp-data.keytab

cp $C3_HOME/etc/hadoop/* /etc/hadoop/conf/
cp $C3_HOME/etc/spark2/* /etc/spark2/conf/
cp $C3_HOME/etc/krb5.conf /etc/krb5.conf

echo "export KRB5KEYTAB=$KRB5KEYTAB" >> $HOME/.bashrc
echo "export C3_REALM=$C3_REALM" >> $HOME/.bashrc
echo "export KRB5PRINCIPAL=gfp-data@$C3_REALM" >> $HOME/.bashrc
echo "[ -f '$HOME/support/bin/run_kinit.sh' ] && \. '$HOME/support/bin/run_kinit.sh'" >> $HOME/.bashrc

# 터미널 프롬프트 설정
# 프롬프트 색상만 변경하고, output 은 기존 색을 쓰고 싶다면 export 문의 끝에서 \\$ \[\e[0m\]'" 와 같이 수정하면 된다.
# 참고로, output 색을 변경해도 ls 명령어의 output 은 색이 변하지 않는다. ls 색 변경은 다른 설정을 적용해야 한다.
echo "export PS1='\[\e[${PROMPT_STYLE}\][${VIRTUAL_ENV^^}::\u@\$PWD]\\$ \[\e[0m\]'" >> $HOME/.bashrc

# csync 최초 1회 실행을 통해 관련 모듈 자동 설치
# c3 관련 파일이 정해져야 사용할 수 있어 이미지 빌드 후에 실행
# Dockerfile 에서 ln -s $APPS_HOME/csync-1.2.14/bin/csync /usr/bin/csync 해줘서 가능
csync

# 환경변수 및 크론탭 확인
export
crontab -l
