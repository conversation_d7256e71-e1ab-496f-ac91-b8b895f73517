#!/bin/bash
# vim: set ft=bash:

# Usage: ssp_batch_deploy.sh [deploy|rollback]

PM2_ECOSYSTEM=pm2.process.config.js
PM2_PROCESS_ID=ssp-batch

DOWNLOAD_DIR=/home1/owfs_web/download
LOCAL_DOWNLOAD_DIR=/home1/irteam/local_download

log()
{
    echo $(date '+%F %T') "$@" 1>&2
}

log_info()
{
    log "INFO" "$@"
}

log_fatal()
{
    log "ERROR" "$@"
    exit 1
}

deploy() {
    # Make pm2 start with proper cwd unless already it did
    (cd $HOME && pm2 ls)

    set -x
    cd /home1/irteam/apps || log_fatal "/home1/irteam/apps not found"
    set +x

    if [ ! -d ssp_batch.cur ]; then
        log_fatal "ssp_batch deployment not found: ssp_batch.cur"
    fi

    log_info "Install required packages"
    set -ex
    (cd ssp_batch.cur && npm install --no-production --no-progress --quiet)
    set +ex

    set -x
    pm2 stop $PM2_PROCESS_ID
    set +x

    log_info "Roll installations forward"
    [ -d ssp_batch.2 ] && rm -rf ssp_batch.2
    [ -d ssp_batch.1 ] && mv ssp_batch.1 ssp_batch.2
    [ -d ssp_batch.0 ] && mv ssp_batch.0 ssp_batch.1
    [ -d ssp_batch ] && mv ssp_batch ssp_batch.0

    set -ex
    mv ssp_batch.cur ssp_batch
    (cd ssp_batch; ln -sf $DOWNLOAD_DIR download; ln -sf $LOCAL_DOWNLOAD_DIR local_download)
    set +ex

    if [ -f ssp_batch/deploy_info.txt ]; then
        log_info "Current deployment info:"
        cat ssp_batch/deploy_info.txt
    fi

    set -x
    source /home1/irteam/apps/c3/source.me
    set +x

    set -x
    kinit -kt /home1/irteam/apps/c3/gfp-data.keytab <EMAIL>
    set +x

    log_info "Run the current deployment"
    set -ex
    (cd ssp_batch && pm2 start ./$PM2_ECOSYSTEM)
    set +ex

    sleep 5 && pm2 report
    pm2 save
}

rollback() {
    # Make pm2 start with proper cwd unless already it did
    (cd $HOME && pm2 ls)

    set -x
    cd /home1/irteam/apps || log_fatal "/home1/irteam/apps not found"
    set +x

    if [ ! -d ssp_batch.0 ]; then
        log_fatal "previous ssp_batch deployment not found: ssp_batch.0"
    fi

    log_info "Stop the running instance"
    set -x
    pm2 stop $PM2_PROCESS_ID
    set +x

    log_info "Roll installations backward"
    set -x
    rm -rf ssp_batch ssp_batch.cur
    mv ssp_batch.0 ssp_batch
    set +x
    [ -d ssp_batch.1 ] && mv ssp_batch.1 ssp_batch.0
    [ -d ssp_batch.2 ] && mv ssp_batch.2 ssp_batch.1

    if [ -f ssp_batch/deploy_info.txt ]; then
        log_info "Previous deployment info:"
        cat ssp_batch/deploy_info.txt
    fi

    log_info "Run the previous deployment"
    set -ex
    (cd ssp_batch && pm2 start ./$PM2_ECOSYSTEM)
    set +ex

    sleep 5 && pm2 report
    pm2 save
}

if [ "$1" == "deploy" ]; then
    deploy
elif [ "$1" == "rollback" ]; then
    rollback
else
    log_fatal "unknown deployment command: $1"
fi
