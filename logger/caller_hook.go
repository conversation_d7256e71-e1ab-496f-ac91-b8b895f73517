package logger

import (
	"fmt"
	"github.com/sirupsen/logrus"
	"os"
	"runtime"
	"strings"
)

var workingDirPath string

type CallerHook struct {
	name string
}

func init() {
	workingDirPath, _ = os.Getwd()
	workingDirPath = strings.Replace(workingDirPath, "\\", "/", -1)
}

func NewCallerHook(loggerName string) *CallerHook {

	return &CallerHook{
		name: loggerName,
	}
}

// Fire is triggered on new log entries
func (hook *CallerHook) Fire(entry *logrus.Entry) error {
	// entry.Data["caller"] = getCallerInfo2()
	entry.Message = "[" + getCallerInfo2() + "] " + entry.Message

	return nil
}

// Levels returns all levels this hook should be registered to
func (hook *CallerHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

// 로거를 호출한 함수 정보 가져오기
func getCallerInfo2() string {
	maximumCallerDepth := 25
	minimumCallerDepth := 8 // knownLogrusFrames

	// Restrict the lookback frames to avoid runaway lookups
	pcs2 := make([]uintptr, maximumCallerDepth)
	depth := runtime.Callers(minimumCallerDepth, pcs2)
	frames := runtime.CallersFrames(pcs2[:depth])

	for f, again := frames.Next(); again; f, again = frames.Next() {
		pkg := getPackageName(f.Function)

		// If the caller isn't part of this package, we're done
		if strings.HasPrefix(pkg, "oss.navercorp.com/da-ssp/gfp-api") || strings.HasPrefix(pkg, "main") {
			if strings.Contains(pkg, "logger") {
				continue
			}

			filePath := strings.Replace(f.File, BuildRoot, "", -1)
			filePath = strings.Replace(filePath, workingDirPath, "", -1)

			// function := strings.Replace(f.Function, "oss.navercorp.com/da-ssp/gfp-api", "", -1)
			// function = strings.Replace(function, "main", "", -1)

			return fmt.Sprintf("%s#%d", filePath, f.Line)
		}
	}

	return ""
}

func getPackageName(f string) string {
	for {
		lastPeriod := strings.LastIndex(f, ".")
		lastSlash := strings.LastIndex(f, "/")
		if lastPeriod > lastSlash {
			f = f[:lastPeriod]
		} else {
			break
		}
	}

	return f
}
