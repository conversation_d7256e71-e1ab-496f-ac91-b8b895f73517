package logger

import (
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/lestrrat-go/file-rotatelogs"
	"github.com/sirupsen/logrus"

	"oss.navercorp.com/da-ssp/gfp-api/config"
	"oss.navercorp.com/da-ssp/gfp-api/nelo"
)

var BuildRoot string

type Logger struct {
	log *logrus.Logger
}

type Entry struct {
	ent *logrus.Entry

	logger *Logger
}

type DefaultFormatter struct {
}

func init() {
	BuildRoot = strings.Replace(BuildRoot, "\\", "/", -1)
}

func (f *DefaultFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	var requestId string
	var fields string
	for key, value := range entry.Data {
		if key == "RequestId" {
			requestId = fmt.Sprintf(" [%s]", value)
		} else {
			fields += fmt.Sprintf(" %s=%s", key, value)
		}
	}
	return []byte(fmt.Sprintf("%s %s%s %s%s\n",
		strings.ToUpper(entry.Level.String())[0:4],
		entry.Time.Format("2006.01.02 15:04:05.000"),
		requestId,
		entry.Message,
		fields)), nil
}

var (
	neloLog          = nelo.New()
	logDir           = config.GetConfig("log_dir").(string)
	loggers          = make(map[string]Logger)
	defaultLogger    Logger
	defaultFormatter *DefaultFormatter
)

func init() {
	defaultRotateLog := setDefault()
	setRevenueSharing(defaultRotateLog)
	defaultFormatter = new(DefaultFormatter)
}

func setDefault() *rotatelogs.RotateLogs {
	// 로테이션 설정
	rotateLogs, _ := rotatelogs.New(
		logDir+"/out.log.%Y%m%d",                  // 로그 파일이름
		rotatelogs.WithRotationTime(24*time.Hour), // 로테이션 주기 1일
		rotatelogs.WithMaxAge(3*30*24*time.Hour),  // 로그 보관기간 : 90일
	)

	// 어디다 찍을지
	defaultWriter := io.MultiWriter(os.Stdout, rotateLogs)
	defaultLogger = Logger{logrus.New()}
	defaultLogger.log.SetOutput(defaultWriter)

	// caller를 찍는 Hook 추가
	defaultLogger.log.AddHook(NewCallerHook("default"))

	// 포매팅
	defaultLogger.log.SetFormatter(defaultFormatter)

	// 레벨 설정
	level, _ := logrus.ParseLevel(config.GetConfig("log_level").(string))
	defaultLogger.log.SetLevel(level)

	loggers["default"] = defaultLogger

	// fatal handler. os.Exit(1) 하기 전에 핸들링..
	handler := func() {
		defaultLogger.log.Debug("로그루스 우아하게 셧다운...")
	}
	logrus.RegisterExitHandler(handler)

	// 웹서버의 defaultWriter 설정
	gin.DefaultWriter = defaultWriter

	return rotateLogs
}

func setRevenueSharing(defaultRotateLogs *rotatelogs.RotateLogs) {
	// 로테이션 설정
	rotateLogs, _ := rotatelogs.New(
		logDir+"/rs.log.%Y%m%d",                   // 로그 파일 경로
		rotatelogs.WithRotationTime(24*time.Hour), // 로테이션 주기 1일
		rotatelogs.WithMaxAge(3*30*24*time.Hour),  // 로그 보관기간 : 90일
	)

	// 어디다 찍을지
	rsWriter := io.MultiWriter(os.Stdout, defaultRotateLogs, rotateLogs)
	rsLogger := Logger{logrus.New()}
	rsLogger.log.SetOutput(rsWriter)

	// caller를 찍는 Hook 추가
	rsLogger.log.AddHook(NewCallerHook("rs"))

	// 포매팅
	rsLogger.log.SetFormatter(defaultFormatter)

	// 레벨 설정
	level, _ := logrus.ParseLevel(config.GetConfig("log_level").(string))
	rsLogger.log.SetLevel(level)

	loggers["rs"] = rsLogger
}

func GetLogger(name string) Logger {
	if name != "" {
		return loggers[name]
	}

	return loggers["default"]
}

/*
	class method version
*/
func (mylog *Logger) Debug(args ...interface{}) {
	mylog.log.Debug(args...)
}

func (mylog *Logger) Info(args ...interface{}) {
	mylog.log.Info(args...)
}

func (mylog *Logger) Warn(args ...interface{}) {
	mylog.log.Warn(args...)
}

func (mylog *Logger) Error(args ...interface{}) {
	mylog.log.Error(args...)

	err := neloLog.Send(fmt.Sprint(args...))
	if err != nil {
		mylog.log.Debugln("SendNelo Error : ", err.Error())
	}
}

func (mylog *Logger) Debugf(format string, args ...interface{}) {
	mylog.log.Debugf(format, args...)
}

func (mylog *Logger) Infof(format string, args ...interface{}) {
	mylog.log.Infof(format, args...)
}

func (mylog *Logger) Warnf(format string, args ...interface{}) {
	mylog.log.Warnf(format, args...)
}

func (mylog *Logger) Errorf(format string, args ...interface{}) {
	mylog.log.Errorf(format, args...)

	err := neloLog.Send(fmt.Sprintf(format, args...))
	if err != nil {
		mylog.log.Debugln("SendNelo Error : ", err.Error())
	}
}

func (mylog *Logger) WithField(key string, value interface{}) *Entry {
	return &Entry{
		ent:    mylog.log.WithField(key, value),
		logger: mylog,
	}
}

func (mylog *Logger) WithFields(fields logrus.Fields) *Entry {
	return &Entry{
		ent:    mylog.log.WithFields(fields),
		logger: mylog,
	}
}

func (mylog *Logger) WithError(err error) *Entry {
	return &Entry{
		ent:    mylog.log.WithError(err),
		logger: mylog,
	}
}

func (entry *Entry) Debug(args ...interface{}) {
	if entry.logger.log.Level >= logrus.DebugLevel {
		entry.ent.Debug(args...)
	}
}

func (entry *Entry) Debugf(format string, args ...interface{}) {
	if entry.logger.log.Level >= logrus.DebugLevel {
		entry.ent.Debugf(format, args...)
	}
}

func (entry *Entry) Info(args ...interface{}) {
	if entry.logger.log.Level >= logrus.InfoLevel {
		entry.ent.Info(args...)
	}
}

func (entry *Entry) Infof(format string, args ...interface{}) {
	if entry.logger.log.Level >= logrus.InfoLevel {
		entry.ent.Infof(format, args...)
	}
}

func (entry *Entry) Warn(args ...interface{}) {
	if entry.logger.log.Level >= logrus.WarnLevel {
		entry.ent.Warn(args...)
	}
}

func (entry *Entry) Warnf(format string, args ...interface{}) {
	if entry.logger.log.Level >= logrus.WarnLevel {
		entry.ent.Warnf(format, args...)
	}
}

func (entry *Entry) Error(args ...interface{}) {
	if entry.logger.log.Level >= logrus.ErrorLevel {
		entry.ent.Error(args...)

		msg := makeMsgForNelo(entry, "", args)

		err := neloLog.Send(msg)
		if err != nil {
			entry.logger.Error("SendNelo Error 1 : ", err.Error())
		}
	}
}

func (entry *Entry) Errorf(format string, args ...interface{}) {
	if entry.logger.log.Level >= logrus.ErrorLevel {
		entry.ent.Errorf(format, args...)

		msg := makeMsgForNelo(entry, format, args)

		err := neloLog.Send(msg)
		if err != nil {
			entry.logger.Errorf("SendNelo Error 2 : ", err.Error())
		}
	}
}

func makeMsgForNelo(entry *Entry, format string, args ...interface{}) string {
	var requestId string
	for key, value := range entry.ent.Data {
		if key == "RequestId" {
			requestId = value.(string)
			break
		}
	}

	var msg string
	if len(format) > 0 {
		msg = fmt.Sprintf(format, args...)
	} else {
		msg = fmt.Sprint(args...)
	}

	if len(requestId) > 0 {
		msg = fmt.Sprintf("RequestId:%s ", requestId) + msg
	}

	return msg
}
