package adunit

import (
	"context"
	"time"

	"github.com/araddon/dateparse"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readconcern"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	"oss.navercorp.com/da-ssp/gfp-api/database"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
)

/*
	AdUnit
		- omitempty로 설정하면, 값이 empty일 때 json에서 보이지 않는다. But 빈 스트링, 0인 경우에도 보이지 않게 되므로 포인터형으로 선언해준다.
*/
type AdUnit struct {
	AdUnitId       string             `json:"adUnitId" bson:"adUnitId"`
	PublisherId    primitive.ObjectID `json:"publisherId" bson:"publisher_id"`
	Name           string             `json:"name" bson:"name"`
	Description    string             `json:"description" bson:"description"`
	Status         string             `json:"status" bson:"status"`
	Sizes          []Size             `json:"sizes" bson:"sizes"`
	Startdelay     *int               `json:"startdelay,omitempty" bson:"startdelay"`
	Skip           *int               `json:"skip,omitempty" bson:"skip"`
	Maxduration    *int               `json:"maxduration,omitempty" bson:"maxduration"`
	VideoOutput    *string            `json:"videoOutput,omitempty" bson:"videoOutput"`
	CreativeType   string             `json:"creativeType" bson:"creativeType"`
	CreativeTypes  []string           `json:"creativeTypes" bson:"creativeTypes"`
	Rules          []Rule             `json:"rules" bson:"rules"`
	NativeAdStyles []NativeAdStyle    `json:"nativeAdStyles,omitempty" bson:"nativeAdStyles"`
	ProductType    string             `json:"productType" bson:"productType"`
	CreatedAt      time.Time          `json:"createdAt" bson:"createdAt"`
	ModifiedAt     time.Time          `json:"modifiedAt" bson:"modifiedAt"`
}

/*
	NativeAdStyle
*/
type NativeAdStyle struct {
	NativeAdStyle *AdStyle `json:"nativeAdStyle,omitempty" bson:"nativeAdStyle"`
}

/*
	AdStyle
*/
type AdStyle struct {
	ID string `json:"id" bson:"id"`
}

/*
	Rules
*/
type Rule struct {
	Rank             float64           `json:"rank" bson:"rank"`
	Rate             float64           `json:"rate" bson:"rate"`
	FloorPrice       *float64          `json:"floorPrice" bson:"floorPrice"`
	TierFormType     string            `json:"tierFormType" bson:"tierFormType"`
	AdProviderPlaces []AdProviderPlace `json:"adProviderPlaces" bson:"adProviderPlaces"`
	BiddingGroups    *[]BiddingGroup   `json:"biddingGroups,omitempty" bson:"biddingGroups"`
	Target           *Target           `json:"target,omitempty" bson:"target"`
}

/*
	AdProviderPlaces
*/
type AdProviderPlace struct {
	AdProviderPlaceId          primitive.ObjectID          `json:"adProviderPlace_id" bson:"adProviderPlace_id"`
	ResponseValidityConditions []ResponseValidityCondition `json:"responseValidityConditions" bson:"responseValidityConditions"`
}

/*
	ResponseValidityConditions
*/
type ResponseValidityCondition struct {
	Key      string   `json:"key" bson:"key"`
	Operator string   `json:"operator" bson:"operator"`
	Values   []string `json:"values" bson:"values"`
}

/*
	BiddingGroups
*/
type BiddingGroup struct {
	BiddingGroupId primitive.ObjectID `json:"biddingGroup_id" bson:"biddingGroup_id"`
}

/*
	Target
*/
type Target struct {
	KeyValues       *[]KeyValue       `json:"keyValues,omitempty" bson:"keyValues"`
	SystemVariables *[]SystemVariable `json:"systemVariables,omitempty" bson:"systemVariables"`
}

/*
	KeyValues
*/
type KeyValue struct {
	Key        string   `json:"key" bson:"key"`
	Name       string   `json:"name" bson:"name"`
	Operator   string   `json:"operator" bson:"operator"`
	Values     []string `json:"values" bson:"values"`
	ApplyEmpty *int     `json:"applyEmpty,omitempty" bson:"applyEmpty"`
}

/*
	SystemVariables
*/
type SystemVariable struct {
	Code        string                 `json:"code" bson:"code"`
	Name        string                 `json:"name" bson:"name"`
	Operator    string                 `json:"operator" bson:"operator"`
	ValueType   string                 `json:"valueType,omitempty" bson:"valueType"`
	Values      *[]SystemVariableValue `json:"values,omitempty" bson:"values"`
	KeyValValue *KeyValValue           `json:"keyValValue,omitempty" bson:"keyValValue"`
	UserValues  *[]string              `json:"userValues,omitempty" bson:"userValues"`
	ApplyEmpty  *int                   `json:"applyEmpty,omitempty" bson:"applyEmpty"`
}

/*
	SystemVariableValue
*/
type SystemVariableValue struct {
	Code     string `json:"code" bson:"code"`
	Name     string `json:"name" bson:"name"`
	Fullname string `json:"fullname" bson:"fullname"`
}

/*
	KeyValValue
*/
type KeyValValue struct {
	Key  string `json:"key" bson:"key"`
	Name string `json:"name" bson:"name"`
}

/*
	Sizes
*/
type Size struct {
	Width  int `json:"width" bson:"width"`
	Height int `json:"height" bson:"height"`
}

type MappingHistory struct {
	AdProviderPlaceId primitive.ObjectID `json:"adProviderPlaceId" bson:"adProviderPlace_id"`
	AdUnitId          string             `json:"adUnitId" bson:"adUnitId"`
	AdUnit_id         primitive.ObjectID `json:"adUnit_id" bson:"adUnit_id"`
}

type AdUnitService struct{}

/*
	List adUnit 리스트 조회
*/
func (service *AdUnitService) List(ginCtx *gin.Context, adUnitParam AdUnitParam) (list []AdUnit, err error) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[List] AdUnit 리스트 조회")

	// 1. 쿼리 스트링 추출
	adUnitId := adUnitParam.AdUnitId
	publisherId := adUnitParam.PublisherId
	status := adUnitParam.Status
	since := adUnitParam.Since
	publisherIds := adUnitParam.PublisherIds

	// 2. DB 파이프라인 설정
	matchQuery := bson.M{}

	if publisherId != "" {
		publisher_id, _ := primitive.ObjectIDFromHex(publisherId)
		matchQuery["publisher_id"] = publisher_id
	} else if len(publisherIds) > 0 {
		matchQuery["publisher_id"] = bson.M{"$in": publisherIds}
	}

	if since != "" {
		modifiedAt, _ := dateparse.ParseAny(since)

		matchQuery["modifiedAt"] = bson.M{"$gte": modifiedAt}
	}

	matchQuery["adUnitId"] = primitive.Regex{Pattern: ".*" + adUnitId + ".*", Options: "i"}
	matchQuery["status"] = primitive.Regex{Pattern: ".*" + status + ".*", Options: "i"}

	pipeline := mongo.Pipeline{
		{{"$match", matchQuery}},
		{{"$project", bson.M{
			"_id":            0,
			"adUnitId":       1,
			"publisher_id":   1,
			"name":           1,
			"description":    1,
			"status":         1,
			"ctSize":         bson.M{"$size": "$creativeTypes"},
			"creativeTypes":  1,
			"sizes":          1,
			"startdelay":     1,
			"skip":           1,
			"maxduration":    1,
			"videoOutput":    1,
			"rules":          1,
			"productType":    1,
			"nativeAdStyles": 1,
			"createdAt":      1,
			"modifiedAt":     1},
		}},

		{{"$unwind", bson.M{"path": "$nativeAdStyles", "preserveNullAndEmptyArrays": true}}},

		{{"$lookup", bson.M{
			"from":         "NativeAdStyles",
			"localField":   "nativeAdStyles.nativeAdStyle_id",
			"foreignField": "_id",
			"as":           "nativeAdStyles.nativeAdStyle"},
		}},

		{{"$unwind", bson.M{"path": "$nativeAdStyles.nativeAdStyle", "preserveNullAndEmptyArrays": true}}},

		{{"$project", bson.M{
			"adUnitId":                        1,
			"publisher_id":                    1,
			"name":                            1,
			"description":                     1,
			"status":                          1,
			"sizes":                           1,
			"startdelay":                      1,
			"skip":                            1,
			"maxduration":                     1,
			"videoOutput":                     1,
			"rules":                           1,
			"creativeType":                    bson.M{"$cond": bson.A{bson.M{"$gte": bson.A{"$ctSize", 1}}, bson.M{"$arrayElemAt": bson.A{"$creativeTypes", 0}}, "BANNER"}},
			"creativeTypes":                   1,
			"productType":                     1,
			"nativeAdStyles.nativeAdStyle.id": 1,
			"createdAt":                       1,
			"modifiedAt":                      1},
		}},

		{{"$project", bson.M{
			"adUnitId":       1,
			"publisher_id":   1,
			"name":           1,
			"description":    1,
			"status":         1,
			"sizes":          1,
			"startdelay":     1,
			"skip":           1,
			"maxduration":    1,
			"videoOutput":    1,
			"rules":          1,
			"creativeType":   1,
			"creativeTypes":  1,
			"productType":    1,
			"nativeAdStyles": bson.M{"$cond": bson.A{bson.M{"$not": bson.A{bson.M{"$eq": bson.A{"$nativeAdStyles", bson.M{}}}}}, "$nativeAdStyles", "$$REMOVE"}},
			"createdAt":      1,
			"modifiedAt":     1},
		}},

		{{"$group", bson.M{
			"_id": bson.M{
				"adUnitId":      "$adUnitId",
				"publisher_id":  "$publisher_id",
				"name":          "$name",
				"description":   "$description",
				"status":        "$status",
				"sizes":         "$sizes",
				"startdelay":    "$startdelay",
				"skip":          "$skip",
				"maxduration":   "$maxduration",
				"videoOutput":   "$videoOutput",
				"rules":         "$rules",
				"creativeType":  "$creativeType",
				"creativeTypes": "$creativeTypes",
				"productType":   "$productType",
				"createdAt":     "$createdAt",
				"modifiedAt":    "$modifiedAt",
			},
			"nativeAdStyles": bson.M{"$addToSet": "$nativeAdStyles"}},
		}},

		{{"$project", bson.M{
			"_id":            0,
			"adUnitId":       "$_id.adUnitId",
			"publisher_id":   "$_id.publisher_id",
			"name":           "$_id.name",
			"description":    "$_id.description",
			"status":         "$_id.status",
			"sizes":          "$_id.sizes",
			"startdelay":     "$_id.startdelay",
			"skip":           "$_id.skip",
			"maxduration":    "$_id.maxduration",
			"videoOutput":    "$_id.videoOutput",
			"rules":          "$_id.rules",
			"creativeType":   "$_id.creativeType",
			"creativeTypes":  "$_id.creativeTypes",
			"productType":    "$_id.productType",
			"nativeAdStyles": 1,
			"createdAt":      "$_id.createdAt",
			"modifiedAt":     "$_id.modifiedAt"},
		}},

		{{"$sort", bson.M{"modifiedAt": -1}}},
	}

	// 3. Aggregation 옵션 설정
	opts := options.Aggregate()
	opts.SetAllowDiskUse(true)

	// 4. timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// 5. Aggregation 실행
	collOptions := &options.CollectionOptions{}
	collOptions.SetReadConcern(readconcern.Majority())
	collOptions.SetReadPreference(readpref.SecondaryPreferred(readpref.WithMaxStaleness(time.Second * 90)))

	collection := database.GFP.Collection("AdUnits", collOptions)
	cur, err := collection.Aggregate(ctx, pipeline, opts)
	if err != nil {
		entry.Error(err)
		return
	}
	defer cur.Close(ctx)

	// 6. 조회 결과 가져오기
	for cur.Next(ctx) {
		var elem AdUnit

		err = cur.Decode(&elem)
		if err != nil {
			entry.Error(err)
			return
		}

		list = append(list, elem)
	}

	if err = cur.Err(); err != nil {
		entry.Error(err)
	}

	// 조회 결과가 없는 경우, empty 배열 처리
	if list == nil {
		list = make([]AdUnit, 0)
	}

	return
}

/*
	AdpIds와 연관된 publisherIds 조회
*/
func (service *AdUnitService) GetPublisherIdsByAdpIds(ginCtx *gin.Context, adproviderIds []primitive.ObjectID) (publisherIds []primitive.ObjectID, err error) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[GetPublisherIdsByAdpIds] AdpIds와 연관된 publisherIds 조회")

	// 1. DB 파이프라인 설정
	matchQuery := bson.M{"adProvider_id": bson.M{"$in": adproviderIds}}

	pipeline := mongo.Pipeline{
		{{"$match", matchQuery}},
		{{
			"$project", bson.M{
				"_id":          0,
				"publisher_id": 1,
			},
		}},
		{{
			"$group", bson.M{
				"_id":           nil,
				"publisher_ids": bson.M{"$addToSet": "$publisher_id"},
			},
		}},
		{{
			"$project", bson.M{
				"_id":           0,
				"publisher_ids": 1,
			},
		}},
	}

	// 2. Aggregation 옵션 설정
	opts := options.Aggregate()
	opts.SetAllowDiskUse(true)

	// 3. timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// 4. Aggregation 실행
	collOptions := &options.CollectionOptions{}
	collOptions.SetReadConcern(readconcern.Majority())
	collOptions.SetReadPreference(readpref.SecondaryPreferred(readpref.WithMaxStaleness(time.Second * 90)))

	collection := database.GFP.Collection("AdProviderInfos", collOptions)
	cur, err := collection.Aggregate(ctx, pipeline, opts)
	if err != nil {
		entry.Error(err)
		return
	}
	defer cur.Close(ctx)

	// 5. 조회 결과 가져오기
	var elem AdUnitParam
	for cur.Next(ctx) {
		err = cur.Decode(&elem)
		if err != nil {
			entry.Error(err)
			return
		}

		// entry.Debugf("%+v", elem)
	}

	if err = cur.Err(); err != nil {
		entry.Error(err)
		return
	}

	publisherIds = elem.PublisherIds

	return
}
