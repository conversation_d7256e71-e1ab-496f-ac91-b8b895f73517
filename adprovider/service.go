package adprovider

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"oss.navercorp.com/da-ssp/gfp-api/database"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
	"oss.navercorp.com/da-ssp/gfp-api/util"
)

type Service struct {
}

type AdProvider struct {
	Id          primitive.ObjectID `bson:"_id"` // 요걸 해줘야 _id를 제대로 가져옴
	Name        string             `bson:"name"`
	Description string             `bson:"description"`
	HomepageUrl string             `bson:"homepageUrl"`
	CreatedAt   time.Time          `bson:"createdAt"` // 요걸 해줘야 시간을 제대로 가져옴
	ModifiedAt  time.Time          `bson:"modifiedAt"`
}

type AdProviderPlace struct {
	Id               primitive.ObjectID `bson:"_id"` // 요걸 해줘야 _id를 제대로 가져옴
	Name             string             `bson:"name"`
	Description      string             `bson:"description"`
	CreativeType     string             `bson:"creativeType"`
	ChannelType      string             `bson:"channelType"`
	AdProviderInfoId primitive.ObjectID `bson:"adProviderInfo_id"`
	IsDel            int8               `bson:"isDel"`
	Status           string             `bson:"status"`
	PlaceKey         string             `bson:"placeKey"`
	CreatedAt        time.Time          `bson:"createdAt"` // 요걸 해줘야 시간을 제대로 가져옴
	ModifiedAt       time.Time          `bson:"modifiedAt"`
}

func (service *Service) List(ginCtx *gin.Context) []AdProvider {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debugf("adprovider list 조회 시작")

	collection := database.GFP.Collection("AdProviders")

	// Pass these options to the Find method
	findOptions := options.Find()
	findOptions.SetLimit(2)

	var list []AdProvider

	// Passing bson.D{{}} as the filter matches all documents in the environmentCollection
	ctx, _ := context.WithTimeout(context.TODO(), 30*time.Second)
	cur, err := collection.Find(ctx, bson.D{{}}, findOptions)
	if err != nil {
		entry.Error(err)
	}
	defer cur.Close(ctx)

	// Finding multiple documents returns a cursor
	// Iterating through the cursor allows us to decode documents one at a time
	for cur.Next(ctx) {

		// create a value into which the single document can be decoded
		var elem AdProvider
		err := cur.Decode(&elem)
		if err != nil {
			entry.Error(err)
		}

		list = append(list, elem)
		// rslog.Debugf("Found multiple documents each: %+v\n", utils.PrettyPrint(elem))
	}

	if err := cur.Err(); err != nil {
		entry.Error(err)
	}

	// Close the cursor once finished
	cur.Close(context.TODO())
	// rslog.Debugf("Found multiple documents (array of pointers): %+v\n", list)
	//
	return list
}

func (service *Service) Get(ginCtx *gin.Context, id string) AdProvider {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	collection := database.GFP.Collection("AdProviders")
	objId, _ := primitive.ObjectIDFromHex(id)
	filter := bson.M{"_id": objId}
	var adProvider AdProvider
	err := collection.FindOne(context.TODO(), filter).Decode(&adProvider)
	if err != nil {
		entry.Error(err)
	}

	entry.Debugf("프로바이더 상세: %+v\n", util.PrettyPrint(adProvider))

	return adProvider
}
