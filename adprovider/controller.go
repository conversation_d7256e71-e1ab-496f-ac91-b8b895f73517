package adprovider

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
)

// 원래....................프로바이더 컨트롤러...............
type Controller struct {
	adProviderService Service
}

// 프로바이더 목록 조회
func (controller *Controller) List(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	publishers := controller.adProviderService.List(ginCtx)

	ginCtx.JSON(http.StatusOK, gin.H{"list": publishers})

	v, exists := ginCtx.Get("RequestId")
	entry.Debugf("AdProvider 리스트 전달. requestId: %s err: %v\n", v, exists)
}

func (controller *Controller) Get(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	adProviderId := ginCtx.Param("id")
	adProvider := controller.adProviderService.Get(ginCtx, adProviderId)
	ginCtx.JSON(http.StatusOK, adProvider)

	entry.Debug("AdProvider 정보 조회 완료")
}
