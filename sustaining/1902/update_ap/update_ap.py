from bson import ObjectId
from pymongo import MongoClient


# db.getCollection("AdProviders").updateMany({
# 										   //    "name" : "AdView"
# },
# {
# $unset: {
# 	'report.distributionType': null,
# 	'report.estimatedRevenueType': null
# }
# 
# })
# 

def update(match: dict, update_set: dict):
	# test
	conn_url = '************************************************************************************************************'  # test
	db = 'ssp-test'

	# real
	# conn_url = '**************************************************************************************************************************'  # real
	# db = 'ssp'

	client = MongoClient(conn_url, 10011)
	db = client[db]
	collection = db['AdProviders']

	collection.update_one(match, update_set)

	print("문서 업데이트가 완료되었습니다.")


def update_estimated_revenue_type():
	"""
	https://oss.navercorp.com/da-ssp/bts/issues/1902#issuecomment-12982938
	추정수익기준 업데이트
	:return:
	"""
	# with open('/Users/<USER>/Projects/nam-branch/airflow-dev/sustaining/1902/update_ap/test_estimated_revenue_type.txt',
	with open('D:/Projects/nam-branch/airflow-dev/sustaining/1902/update_ap/test_estimated_revenue_type.txt',
			  'r', encoding='utf-8-sig') as file:
		cnt = 0
		for line in file:
			if len(line) > 1:
				parts = line.rstrip("\n").split(',')
				id = parts[0]
				name = parts[1]

				estimatedReportType = parts[2]
				print(f'\n.....id={id} name={name} estimatedReportType={estimatedReportType}')

				match = {'_id': ObjectId(id)}
				update_set = {}

				pair1 = estimatedReportType.split('@')
				esti = {}
				for item in pair1:
					pair2 = item.split(':')
					if pair2[1] != '-':
						print(
							f'\t\tid={id} name={name} estimatedReportType={estimatedReportType}     creativeType={pair2[0]} type={pair2[1]}')
						esti[pair2[0]] = pair2[1]

				update_set['$set'] = {'report.estimatedReportType': esti}
				print(f'\t\tmatch={match}\n\t\tupdate={update_set}')

				update(match, update_set)
				cnt += 1

			# if cnt == 2:
			# 	break

		print(f'\ncnt={cnt}')


def update_distribution_type():
	"""
	https://oss.navercorp.com/da-ssp/bts/issues/1902#issuecomment-12980448
	비중분배 기준 업데이트
	:return:
	"""
	# with open('/Users/<USER>/Projects/nam-branch/airflow-dev/sustaining/1902/update_ap/test_distribution_type.txt', 'r',
	with open('D:/Projects/nam-branch/airflow-dev/sustaining/1902/update_ap/test_distribution_type.txt', 'r',
			  encoding='utf-8-sig') as file:
		cnt = 0
		for line in file:
			if len(line) > 1:
				parts = line.rstrip("\n").split(',')
				id = parts[0]
				name = parts[1]

				distTypes = parts[2]
				print(f'\n.....id={id} name={name} distributionType={distTypes}')

				match = {'_id': ObjectId(id)}
				update_set = {}

				if distTypes and distTypes != '-':
					pair1 = distTypes.split('@')
					print(
						f'\t\tid={id} name={name} distTypes={distTypes}     distributionTypeForRvn={pair1[0]} distributionTypeForImp={pair1[1]}')

					update_set['$set'] = {
						'report.distributionType.revenue': pair1[0],
						'report.distributionType.impression': pair1[1]
					}
				else:
					update_set['$set'] = {
						'report.distributionType.revenue': None,
						'report.distributionType.impression': None
					}

				print(f'\t\tmatch={match}\n\t\tupdate={update_set}')
				update(match, update_set)

				cnt += 1

			# if cnt == 2:
			# 	break

		print(f'\ncnt={cnt}')


update_estimated_revenue_type()
# update_distribution_type()

# [ real AdProviders ]
#
# {
# 	"_id" : ObjectId("5f0e90036a93f789ce6189fe"),
# 	"report" : {
# 		"revenueTypeForPerf" : "ELECTION",
# 		"revenueTypeForAP" : "API"
# 	}
# }
#
# {
# 	"_id" : ObjectId("64e2dfc29fe4dc002b7a1b47"),
# 	"name" : "Admixer"
# }
