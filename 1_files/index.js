(()=>{var e={17382:(e,t,r)=>{var n={"./en.json":[6660,6660],"./ja.json":[95986,5986],"./ko.json":[76881,6881],"./zh.json":[2053,2053],"./zh_TW.json":[92063,2063]};function i(e){if(!r.o(n,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=n[e],i=t[0];return r.e(t[1]).then(()=>r.t(i,19))}i.keys=()=>Object.keys(n),i.id=17382,e.exports=i},24111:(e,t,r)=>{"use strict";function n(e,t){for(var r=e.length,n=0;n<r;++n)if(t(e[n],n))return!0;return!1}function i(e,t){for(var r=e.length,n=0;n<r;++n)if(t(e[n],n))return e[n];return null}function o(e){var t=e;if(void 0===t){if("undefined"==typeof navigator||!navigator)return"";t=navigator.userAgent||""}return t.toLowerCase()}function a(e,t){try{return RegExp(e,"g").exec(t)}catch(e){return null}}function s(e){return e.replace(/_/g,".")}function l(e,t){var r=null,i="-1";return n(e,function(e){var n,o=a("("+e.test+")((?:\\/|\\s|:)([0-9|\\.|_]+))?",t);return!!o&&!e.brand&&((r=e,i=o[3]||"-1",e.versionAlias)?i=e.versionAlias:e.versionTest&&(i=((n=a("("+e.versionTest.toLowerCase()+")((?:\\/|\\s|:)([0-9|\\.|_]+))",t))?n[3]:"")||i),i=s(i),!0)}),{preset:r,version:i}}function c(e,t){var r={brand:"",version:"-1"};return n(e,function(e){var n=u(t,e);return!!n&&(r.brand=e.id,r.version=e.versionAlias||n.version,"-1"!==r.version)}),r}function u(e,t){return i(e,function(e){var r=e.brand;return a(""+t.test,r.toLowerCase())})}r.d(t,{Ay:()=>w});var d=[{test:"phantomjs",id:"phantomjs"},{test:"whale",id:"whale"},{test:"edgios|edge|edg",id:"edge"},{test:"msie|trident|windows phone",id:"ie",versionTest:"iemobile|msie|rv"},{test:"miuibrowser",id:"miui browser"},{test:"samsungbrowser",id:"samsung internet"},{test:"samsung",id:"samsung internet",versionTest:"version"},{test:"chrome|crios",id:"chrome"},{test:"firefox|fxios",id:"firefox"},{test:"android",id:"android browser",versionTest:"version"},{test:"safari|iphone|ipad|ipod",id:"safari",versionTest:"version"}],f=[{test:"(?=.*applewebkit/(53[0-7]|5[0-2]|[0-4]))(?=.*\\schrome)",id:"chrome",versionTest:"chrome"},{test:"chromium",id:"chrome"},{test:"whale",id:"chrome",versionAlias:"-1",brand:!0}],p=[{test:"applewebkit",id:"webkit",versionTest:"applewebkit|safari"}],h=[{test:"(?=(iphone|ipad))(?!(.*version))",id:"webview"},{test:"(?=(android|iphone|ipad))(?=.*(naver|daum|; wv))",id:"webview"},{test:"webview",id:"webview"}],m=[{test:"windows phone",id:"windows phone"},{test:"windows 2000",id:"window",versionAlias:"5.0"},{test:"windows nt",id:"window"},{test:"win32|windows",id:"window"},{test:"iphone|ipad|ipod",id:"ios",versionTest:"iphone os|cpu os"},{test:"macos|macintel|mac os x",id:"mac"},{test:"android|linux armv81",id:"android"},{test:"tizen",id:"tizen"},{test:"webos|web0s",id:"webos"}];function v(e){return!!l(h,e).preset}let w=function(e){return void 0===e&&function(){if("undefined"==typeof navigator||!navigator||!navigator.userAgentData)return!1;var e=navigator.userAgentData,t=e.brands||e.uaList;return!!(t&&t.length)}()?function(e){var t=navigator.userAgentData,r=(t.uaList||t.brands).slice(),a=void 0,l=t.mobile||!1,w=r[0],b=(0,t.platform||navigator.platform).toLowerCase(),g={name:w.brand,version:w.version,majorVersion:-1,webkit:!1,webkitVersion:"-1",chromium:!1,chromiumVersion:"-1",webview:!!c(h,r).brand||v(o())},y={name:"unknown",version:"-1",majorVersion:-1};g.webkit=!g.chromium&&n(p,function(e){return u(r,e)});var E=c(f,r);if(g.chromium=!!E.brand,g.chromiumVersion=E.version,!g.chromium){var _=c(p,r);g.webkit=!!_.brand,g.webkitVersion=_.version}var A=i(m,function(e){return RegExp(""+e.test,"g").exec(b)});if(y.name=A?A.id:"",e&&(y.version=e.platformVersion),a&&a.length){var S=c(d,a);g.name=S.brand||g.name,g.version=S.version||g.version}else{var R=c(d,r);g.name=R.brand||g.name,g.version=R.brand&&e?e.uaFullVersion:R.version}return g.webkit&&(y.name=l?"ios":"mac"),"ios"===y.name&&g.webview&&(g.version="-1"),y.version=s(y.version),g.version=s(g.version),y.majorVersion=parseInt(y.version,10),g.majorVersion=parseInt(g.version,10),{browser:g,os:y,isMobile:l,isHints:!0}}():function(e){var t=o(e),r=!!/mobi/g.exec(t),n={name:"unknown",version:"-1",majorVersion:-1,webview:v(t),chromium:!1,chromiumVersion:"-1",webkit:!1,webkitVersion:"-1"},i={name:"unknown",version:"-1",majorVersion:-1},a=l(d,t),s=a.preset,c=a.version,u=l(m,t),h=u.preset,w=u.version,b=l(f,t);if(n.chromium=!!b.preset,n.chromiumVersion=b.version,!n.chromium){var g=l(p,t);n.webkit=!!g.preset,n.webkitVersion=g.version}return h&&(i.name=h.id,i.version=w,i.majorVersion=parseInt(w,10)),s&&(n.name=s.id,n.version=c,n.webview&&"ios"===i.name&&"safari"!==n.name&&(n.webview=!1)),n.majorVersion=parseInt(n.version,10),{browser:n,os:i,isMobile:r,isHints:!1}}(e)}},45588:(e,t,r)=>{"use strict";var n,i;/**
 * @remix-run/router v1.17.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}r.d(t,{AO:()=>f,B6:()=>E,G3:()=>W,Gh:()=>T,HS:()=>O,Oi:()=>l,Rr:()=>p,VV:()=>k,aE:()=>V,pX:()=>L,pb:()=>_,rc:()=>n,tH:()=>P,tW:()=>y,ue:()=>v,yD:()=>R,zR:()=>s}),function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(n||(n={}));let a="popstate";function s(e){return void 0===e&&(e={}),function(e,t,r,i){void 0===i&&(i={});let{window:s=document.defaultView,v5Compat:c=!1}=i,p=s.history,h=n.Pop,m=null,v=w();function w(){return(p.state||{idx:null}).idx}function b(){h=n.Pop;let e=w(),t=null==e?null:e-v;v=e,m&&m({action:h,location:y.location,delta:t})}function g(e){let t="null"!==s.location.origin?s.location.origin:s.location.href,r="string"==typeof e?e:f(e);return l(t,"No window.location.(origin|href) available to create URL for href: "+(r=r.replace(/ $/,"%20"))),new URL(r,t)}null==v&&(v=0,p.replaceState(o({},p.state,{idx:v}),""));let y={get action(){return h},get location(){return e(s,p)},listen(e){if(m)throw Error("A history only accepts one active listener");return s.addEventListener(a,b),m=e,()=>{s.removeEventListener(a,b),m=null}},createHref:e=>t(s,e),createURL:g,encodeLocation(e){let t=g(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){h=n.Push;let r=d(y.location,e,t),i=u(r,v=w()+1),o=y.createHref(r);try{p.pushState(i,"",o)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;s.location.assign(o)}c&&m&&m({action:h,location:y.location,delta:1})},replace:function(e,t){h=n.Replace;let i=d(y.location,e,t);r&&r(i,e);let o=u(i,v=w()),a=y.createHref(i);p.replaceState(o,"",a),c&&m&&m({action:h,location:y.location,delta:0})},go:e=>p.go(e)};return y}(function(e,t){let{pathname:r,search:n,hash:i}=e.location;return d("",{pathname:r,search:n,hash:i},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"==typeof t?t:f(t)},null,e)}function l(e,t){if(!1===e||null==e)throw Error(t)}function c(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw Error(t)}catch(e){}}}function u(e,t){return{usr:e.state,key:e.key,idx:t}}function d(e,t,r,n){return void 0===r&&(r=null),o({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?p(t):t,{state:r,key:t&&t.key||n||Math.random().toString(36).substr(2,8)})}function f(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&"?"!==r&&(t+="?"===r.charAt(0)?r:"?"+r),n&&"#"!==n&&(t+="#"===n.charAt(0)?n:"#"+n),t}function p(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(i||(i={}));let h=new Set(["lazy","caseSensitive","path","id","index","children"]);function m(e,t,r,n){return void 0===r&&(r=[]),void 0===n&&(n={}),e.map((e,i)=>{let a=[...r,String(i)],s="string"==typeof e.id?e.id:a.join("-");if(l(!0!==e.index||!e.children,"Cannot specify children on an index route"),l(!n[s],'Found a route id collision on id "'+s+"\".  Route id's must be globally unique within Data Router usages"),!0===e.index){let r=o({},e,t(e),{id:s});return n[s]=r,r}{let r=o({},e,t(e),{id:s,children:void 0});return n[s]=r,e.children&&(r.children=m(e.children,t,a,n)),r}})}function v(e,t,r){return void 0===r&&(r="/"),w(e,t,r,!1)}function w(e,t,r,n){let i=_(("string"==typeof t?p(t):t).pathname||"/",r);if(null==i)return null;let o=function e(t,r,n,i){void 0===r&&(r=[]),void 0===n&&(n=[]),void 0===i&&(i="");let o=(t,o,a)=>{let s={relativePath:void 0===a?t.path||"":a,caseSensitive:!0===t.caseSensitive,childrenIndex:o,route:t};s.relativePath.startsWith("/")&&(l(s.relativePath.startsWith(i),'Absolute route path "'+s.relativePath+'" nested under path "'+i+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),s.relativePath=s.relativePath.slice(i.length));let c=O([i,s.relativePath]),u=n.concat(s);if(t.children&&t.children.length>0&&(l(!0!==t.index,'Index routes must not have child routes. Please remove all child routes from route path "'+c+'".'),e(t.children,r,u,c)),null!=t.path||t.index){var d;let e,n;r.push({path:c,score:(d=t.index,n=(e=c.split("/")).length,e.some(g)&&(n+=-2),d&&(n+=2),e.filter(e=>!g(e)).reduce((e,t)=>e+(b.test(t)?3:""===t?1:10),n)),routesMeta:u})}};return t.forEach((e,t)=>{var r;if(""!==e.path&&null!=(r=e.path)&&r.includes("?"))for(let r of function e(t){let r=t.split("/");if(0===r.length)return[];let[n,...i]=r,o=n.endsWith("?"),a=n.replace(/\?$/,"");if(0===i.length)return o?[a,""]:[a];let s=e(i.join("/")),l=[];return l.push(...s.map(e=>""===e?a:[a,e].join("/"))),o&&l.push(...s),l.map(e=>t.startsWith("/")&&""===e?"/":e)}(e.path))o(e,t,r);else o(e,t)}),r}(e);!function(e){e.sort((e,t)=>{var r,n;return e.score!==t.score?t.score-e.score:(r=e.routesMeta.map(e=>e.childrenIndex),n=t.routesMeta.map(e=>e.childrenIndex),r.length===n.length&&r.slice(0,-1).every((e,t)=>e===n[t])?r[r.length-1]-n[n.length-1]:0)})}(o);let a=null;for(let e=0;null==a&&e<o.length;++e){let t=function(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return c(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}(i);a=function(e,t,r){void 0===r&&(r=!1);let{routesMeta:n}=e,i={},o="/",a=[];for(let e=0;e<n.length;++e){let s=n[e],l=e===n.length-1,c="/"===o?t:t.slice(o.length)||"/",u=E({path:s.relativePath,caseSensitive:s.caseSensitive,end:l},c),d=s.route;if(!u&&l&&r&&!n[n.length-1].route.index&&(u=E({path:s.relativePath,caseSensitive:s.caseSensitive,end:!1},c)),!u)return null;Object.assign(i,u.params),a.push({params:i,pathname:O([o,u.pathname]),pathnameBase:N(O([o,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(o=O([o,u.pathnameBase]))}return a}(o[e],t,n)}return a}let b=/^:[\w-]+$/,g=e=>"*"===e;function y(e,t){void 0===t&&(t={});let r=e;r.endsWith("*")&&"*"!==r&&!r.endsWith("/*")&&(c(!1,'Route path "'+r+'" will be treated as if it were "'+r.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+r.replace(/\*$/,"/*")+'".'),r=r.replace(/\*$/,"/*"));let n=r.startsWith("/")?"/":"",i=e=>null==e?"":"string"==typeof e?e:String(e);return n+r.split(/\/+/).map((e,r,n)=>{if(r===n.length-1&&"*"===e)return i(t["*"]);let o=e.match(/^:([\w-]+)(\??)$/);if(o){let[,e,r]=o,n=t[e];return l("?"===r||null!=n,'Missing ":'+e+'" param'),i(n)}return e.replace(/\?$/g,"")}).filter(e=>!!e).join("/")}function E(e,t){var r,n,i;let o,a;"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[s,l]=(r=e.path,n=e.caseSensitive,i=e.end,void 0===n&&(n=!1),void 0===i&&(i=!0),c("*"===r||!r.endsWith("*")||r.endsWith("/*"),'Route path "'+r+'" will be treated as if it were "'+r.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+r.replace(/\*$/,"/*")+'".'),o=[],a="^"+r.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,r)=>(o.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)")),r.endsWith("*")?(o.push({paramName:"*"}),a+="*"===r||"/*"===r?"(.*)$":"(?:\\/(.+)|\\/*)$"):i?a+="\\/*$":""!==r&&"/"!==r&&(a+="(?:(?=\\/|$))"),[new RegExp(a,n?void 0:"i"),o]),u=t.match(s);if(!u)return null;let d=u[0],f=d.replace(/(.)\/+$/,"$1"),p=u.slice(1);return{params:l.reduce((e,t,r)=>{let{paramName:n,isOptional:i}=t;if("*"===n){let e=p[r]||"";f=d.slice(0,d.length-e.length).replace(/(.)\/+$/,"$1")}let o=p[r];return i&&!o?e[n]=void 0:e[n]=(o||"").replace(/%2F/g,"/"),e},{}),pathname:d,pathnameBase:f,pattern:e}}function _(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&"/"!==n?null:e.slice(r)||"/"}function A(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t)+"` field ["+JSON.stringify(n)+"].  Please separate it out to the `to."+r+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function S(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function R(e,t){let r=S(e);return t?r.map((e,t)=>t===r.length-1?e.pathname:e.pathnameBase):r.map(e=>e.pathnameBase)}function T(e,t,r,n){let i,a;void 0===n&&(n=!1),"string"==typeof e?i=p(e):(l(!(i=o({},e)).pathname||!i.pathname.includes("?"),A("?","pathname","search",i)),l(!i.pathname||!i.pathname.includes("#"),A("#","pathname","hash",i)),l(!i.search||!i.search.includes("#"),A("#","search","hash",i)));let s=""===e||""===i.pathname,c=s?"/":i.pathname;if(null==c)a=r;else{let e=t.length-1;if(!n&&c.startsWith("..")){let t=c.split("/");for(;".."===t[0];)t.shift(),e-=1;i.pathname=t.join("/")}a=e>=0?t[e]:"/"}let u=function(e,t){let r;void 0===t&&(t="/");let{pathname:n,search:i="",hash:o=""}="string"==typeof e?p(e):e;return{pathname:n?n.startsWith("/")?n:(r=t.replace(/\/+$/,"").split("/"),n.split("/").forEach(e=>{".."===e?r.length>1&&r.pop():"."!==e&&r.push(e)}),r.length>1?r.join("/"):"/"):t,search:I(i),hash:x(o)}}(i,a),d=c&&"/"!==c&&c.endsWith("/"),f=(s||"."===c)&&r.endsWith("/");return!u.pathname.endsWith("/")&&(d||f)&&(u.pathname+="/"),u}let O=e=>e.join("/").replace(/\/\/+/g,"/"),N=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),I=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",x=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";class P extends Error{}class k{constructor(e,t,r,n){void 0===n&&(n=!1),this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}}function L(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}let D=["post","put","patch","delete"],M=new Set(D),C=new Set(["get",...D]),j=new Set([301,302,303,307,308]),U=new Set([307,308]),F={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},z={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},W={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},H=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,B=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),$="remix-router-transitions";function V(e){let t,r,a,s,u;let f=e.window?e.window:"undefined"!=typeof window?window:void 0,p=void 0!==f&&void 0!==f.document&&void 0!==f.document.createElement,h=!p;if(l(e.routes.length>0,"You must provide a non-empty routes array to createRouter"),e.mapRouteProperties)t=e.mapRouteProperties;else if(e.detectErrorBoundary){let r=e.detectErrorBoundary;t=e=>({hasErrorBoundary:r(e)})}else t=B;let b={},g=m(e.routes,t,void 0,b),y=e.basename||"/",E=e.unstable_dataStrategy||ee,A=e.unstable_patchRoutesOnMiss,S=o({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,unstable_skipActionErrorRevalidation:!1},e.future),R=null,T=new Set,O=null,N=null,I=null,x=null!=e.hydrationData,P=v(g,e.history.location,y),k=null;if(null==P&&!A){let t=ep(404,{pathname:e.history.location.pathname}),{matches:r,route:n}=ef(g);P=r,k={[n.id]:t}}if(P&&A&&ti(P,g,e.history.location.pathname).active&&(P=null),P){if(P.some(e=>e.route.lazy))a=!1;else if(P.some(e=>e.route.loader)){if(S.v7_partialHydration){let t=e.hydrationData?e.hydrationData.loaderData:null,r=e.hydrationData?e.hydrationData.errors:null,n=e=>!e.route.loader||("function"!=typeof e.route.loader||!0!==e.route.loader.hydrate)&&(t&&void 0!==t[e.route.id]||r&&void 0!==r[e.route.id]);if(r){let e=P.findIndex(e=>void 0!==r[e.route.id]);a=P.slice(0,e+1).every(n)}else a=P.every(n)}else a=null!=e.hydrationData}else a=!0}else a=!1,P=[];let D={historyAction:e.history.action,location:e.history.location,matches:P,initialized:a,navigation:F,restoreScrollPosition:null==e.hydrationData&&null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||k,fetchers:new Map,blockers:new Map},M=n.Pop,C=!1,V=!1,Y=new Map,K=null,Q=!1,er=!1,ea=[],es=[],em=new Map,eA=0,eI=-1,ex=new Map,eP=new Set,ek=new Map,eL=new Map,eD=new Set,eM=new Map,eC=new Map,ej=new Map,eU=!1;function eF(e,t){void 0===t&&(t={}),D=o({},D,e);let r=[],n=[];S.v7_fetcherPersist&&D.fetchers.forEach((e,t)=>{"idle"===e.state&&(eD.has(t)?n.push(t):r.push(t))}),[...T].forEach(e=>e(D,{deletedFetchers:n,unstable_viewTransitionOpts:t.viewTransitionOpts,unstable_flushSync:!0===t.flushSync})),S.v7_fetcherPersist&&(r.forEach(e=>D.fetchers.delete(e)),n.forEach(e=>e1(e)))}function ez(t,i,a){var s,l;let c,u;let{flushSync:d}=void 0===a?{}:a,f=null!=D.actionData&&null!=D.navigation.formMethod&&ey(D.navigation.formMethod)&&"loading"===D.navigation.state&&(null==(s=t.state)?void 0:s._isRedirect)!==!0;c=i.actionData?Object.keys(i.actionData).length>0?i.actionData:null:f?D.actionData:null;let p=i.loaderData?ec(D.loaderData,i.loaderData,i.matches||[],i.errors):D.loaderData,h=D.blockers;h.size>0&&(h=new Map(h)).forEach((e,t)=>h.set(t,W));let m=!0===C||null!=D.navigation.formMethod&&ey(D.navigation.formMethod)&&(null==(l=t.state)?void 0:l._isRedirect)!==!0;if(r&&(g=r,r=void 0),Q||M===n.Pop||(M===n.Push?e.history.push(t,t.state):M===n.Replace&&e.history.replace(t,t.state)),M===n.Pop){let e=Y.get(D.location.pathname);e&&e.has(t.pathname)?u={currentLocation:D.location,nextLocation:t}:Y.has(t.pathname)&&(u={currentLocation:t,nextLocation:D.location})}else if(V){let e=Y.get(D.location.pathname);e?e.add(t.pathname):(e=new Set([t.pathname]),Y.set(D.location.pathname,e)),u={currentLocation:D.location,nextLocation:t}}eF(o({},i,{actionData:c,loaderData:p,historyAction:M,location:t,initialized:!0,navigation:F,revalidation:"idle",restoreScrollPosition:tn(t,i.matches||D.matches),preventScrollReset:m,blockers:h}),{viewTransitionOpts:u,flushSync:!0===d}),M=n.Pop,C=!1,V=!1,Q=!1,er=!1,ea=[],es=[]}async function eW(t,r){if("number"==typeof t){e.history.go(t);return}let i=G(D.location,D.matches,y,S.v7_prependBasename,t,S.v7_relativeSplatPath,null==r?void 0:r.fromRouteId,null==r?void 0:r.relative),{path:a,submission:s,error:l}=q(S.v7_normalizeFormMethod,!1,i,r),c=D.location,u=d(D.location,a,r&&r.state);u=o({},u,e.history.encodeLocation(u));let f=r&&null!=r.replace?r.replace:void 0,p=n.Push;!0===f?p=n.Replace:!1===f||null!=s&&ey(s.formMethod)&&s.formAction===D.location.pathname+D.location.search&&(p=n.Replace);let h=r&&"preventScrollReset"in r?!0===r.preventScrollReset:void 0,m=!0===(r&&r.unstable_flushSync),v=e5({currentLocation:c,nextLocation:u,historyAction:p});if(v){e7(v,{state:"blocked",location:u,proceed(){e7(v,{state:"proceeding",proceed:void 0,reset:void 0,location:u}),eW(t,r)},reset(){let e=new Map(D.blockers);e.set(v,W),eF({blockers:e})}});return}return await eH(p,u,{submission:s,pendingError:l,preventScrollReset:h,replace:r&&r.replace,enableViewTransition:r&&r.unstable_viewTransition,flushSync:m})}async function eH(t,n,a){var s,l,c;let d;u&&u.abort(),u=null,M=t,Q=!0===(a&&a.startUninterruptedRevalidation),s=D.location,l=D.matches,O&&I&&(O[tr(s,l)]=I()),C=!0===(a&&a.preventScrollReset),V=!0===(a&&a.enableViewTransition);let f=r||g,p=a&&a.overrideNavigation,h=v(f,n,y),m=!0===(a&&a.flushSync),w=ti(h,f,n.pathname);if(w.active&&w.matches&&(h=w.matches),!h){let{error:e,notFoundMatches:t,route:r}=e9(n.pathname);ez(n,{matches:t,loaderData:{},errors:{[r.id]:e}},{flushSync:m});return}if(D.initialized&&!er&&(c=D.location).pathname===n.pathname&&c.search===n.search&&(""===c.hash?""!==n.hash:c.hash===n.hash||""!==n.hash)&&!(a&&a.submission&&ey(a.submission.formMethod))){ez(n,{matches:h},{flushSync:m});return}u=new AbortController;let b=eo(e.history,n,u.signal,a&&a.submission);if(a&&a.pendingError)d=[ed(h).route.id,{type:i.error,error:a.pendingError}];else if(a&&a.submission&&ey(a.submission.formMethod)){let t=await eB(b,n,a.submission,h,w.active,{replace:a.replace,flushSync:m});if(t.shortCircuited)return;if(t.pendingActionResult){let[e,r]=t.pendingActionResult;if(ew(r)&&L(r.error)&&404===r.error.status){u=null,ez(n,{matches:t.matches,loaderData:{},errors:{[e]:r.error}});return}}h=t.matches||h,d=t.pendingActionResult,p=eT(n,a.submission),m=!1,w.active=!1,b=eo(e.history,b.url,b.signal)}let{shortCircuited:E,matches:_,loaderData:A,errors:S}=await e$(b,n,h,w.active,p,a&&a.submission,a&&a.fetcherSubmission,a&&a.replace,a&&!0===a.initialHydration,m,d);E||(u=null,ez(n,o({matches:_||h},eu(d),{loaderData:A,errors:S})))}async function eB(e,t,r,o,a,s){let l;if(void 0===s&&(s={}),eJ(),eF({navigation:{state:"submitting",location:t,formMethod:r.formMethod,formAction:r.formAction,formEncType:r.formEncType,formData:r.formData,json:r.json,text:r.text}},{flushSync:!0===s.flushSync}),a){let r=await to(o,t.pathname,e.signal);if("aborted"===r.type)return{shortCircuited:!0};if("error"===r.type){let{error:e,notFoundMatches:n,route:o}=te(t.pathname,r);return{matches:n,pendingActionResult:[o.id,{type:i.error,error:e}]}}if(r.matches)o=r.matches;else{let{notFoundMatches:e,error:r,route:n}=e9(t.pathname);return{matches:e,pendingActionResult:[n.id,{type:i.error,error:r}]}}}let c=eS(o,t);if(c.route.action||c.route.lazy){if(l=(await eY("action",e,[c],o))[0],e.signal.aborted)return{shortCircuited:!0}}else l={type:i.error,error:ep(405,{method:e.method,pathname:t.pathname,routeId:c.route.id})};if(eb(l)){let t;return t=s&&null!=s.replace?s.replace:ei(l.response.headers.get("Location"),new URL(e.url),y)===D.location.pathname+D.location.search,await eX(e,l,{submission:r,replace:t}),{shortCircuited:!0}}if(ev(l))throw ep(400,{type:"defer-action"});if(ew(l)){let e=ed(o,c.route.id);return!0!==(s&&s.replace)&&(M=n.Push),{matches:o,pendingActionResult:[e.route.id,l]}}return{matches:o,pendingActionResult:[c.route.id,l]}}async function e$(t,n,i,a,s,l,c,d,f,p,h){let m=s||eT(n,l),v=l||c||eR(m),w=!Q&&(!S.v7_partialHydration||!f);if(a){if(w){let e=eV(h);eF(o({navigation:m},void 0!==e?{actionData:e}:{}),{flushSync:p})}let e=await to(i,n.pathname,t.signal);if("aborted"===e.type)return{shortCircuited:!0};if("error"===e.type){let{error:t,notFoundMatches:r,route:i}=te(n.pathname,e);return{matches:r,loaderData:{},errors:{[i.id]:t}}}if(e.matches)i=e.matches;else{let{error:e,notFoundMatches:t,route:r}=e9(n.pathname);return{matches:t,loaderData:{},errors:{[r.id]:e}}}}let b=r||g,[E,_]=X(e.history,D,i,v,n,S.v7_partialHydration&&!0===f,S.unstable_skipActionErrorRevalidation,er,ea,es,eD,ek,eP,b,y,h);if(tt(e=>!(i&&i.some(t=>t.route.id===e))||E&&E.some(t=>t.route.id===e)),eI=++eA,0===E.length&&0===_.length){let e=e3();return ez(n,o({matches:i,loaderData:{},errors:h&&ew(h[1])?{[h[0]]:h[1].error}:null},eu(h),e?{fetchers:new Map(D.fetchers)}:{}),{flushSync:p}),{shortCircuited:!0}}if(w){let e={};if(!a){e.navigation=m;let t=eV(h);void 0!==t&&(e.actionData=t)}_.length>0&&(e.fetchers=(_.forEach(e=>{let t=D.fetchers.get(e.key),r=eO(void 0,t?t.data:void 0);D.fetchers.set(e.key,r)}),new Map(D.fetchers))),eF(e,{flushSync:p})}_.forEach(e=>{em.has(e.key)&&e2(e.key),e.controller&&em.set(e.key,e.controller)});let A=()=>_.forEach(e=>e2(e.key));u&&u.signal.addEventListener("abort",A);let{loaderResults:R,fetcherResults:T}=await eK(D.matches,i,E,_,t);if(t.signal.aborted)return{shortCircuited:!0};u&&u.signal.removeEventListener("abort",A),_.forEach(e=>em.delete(e.key));let O=eh([...R,...T]);if(O){if(O.idx>=E.length){let e=_[O.idx-E.length].key;eP.add(e)}return await eX(t,O.result,{replace:d}),{shortCircuited:!0}}let{loaderData:N,errors:I}=el(D,i,E,R,h,_,T,eM);eM.forEach((e,t)=>{e.subscribe(r=>{(r||e.done)&&eM.delete(t)})}),S.v7_partialHydration&&f&&D.errors&&Object.entries(D.errors).filter(e=>{let[t]=e;return!E.some(e=>e.route.id===t)}).forEach(e=>{let[t,r]=e;I=Object.assign(I||{},{[t]:r})});let x=e3(),P=e8(eI),k=x||P||_.length>0;return o({matches:i,loaderData:N,errors:I},k?{fetchers:new Map(D.fetchers)}:{})}function eV(e){return e&&!ew(e[1])?{[e[0]]:e[1].data}:D.actionData?0===Object.keys(D.actionData).length?null:D.actionData:void 0}async function eG(t,n,i,o,a,s,c,d){function f(e){if(!e.route.action&&!e.route.lazy){let e=ep(405,{method:d.formMethod,pathname:i,routeId:n});return eQ(t,n,e,{flushSync:c}),!0}return!1}if(eJ(),ek.delete(t),!s&&f(o))return;let p=D.fetchers.get(t);eZ(t,{state:"submitting",formMethod:d.formMethod,formAction:d.formAction,formEncType:d.formEncType,formData:d.formData,json:d.json,text:d.text,data:p?p.data:void 0},{flushSync:c});let h=new AbortController,m=eo(e.history,i,h.signal,d);if(s){let e=await to(a,i,m.signal);if("aborted"===e.type)return;if("error"===e.type){let{error:r}=te(i,e);eQ(t,n,r,{flushSync:c});return}if(e.matches){if(f(o=eS(a=e.matches,i)))return}else{eQ(t,n,ep(404,{pathname:i}),{flushSync:c});return}}em.set(t,h);let w=eA,b=(await eY("action",m,[o],a))[0];if(m.signal.aborted){em.get(t)===h&&em.delete(t);return}if(S.v7_fetcherPersist&&eD.has(t)){if(eb(b)||ew(b)){eZ(t,eN(void 0));return}}else{if(eb(b))return(em.delete(t),eI>w)?void eZ(t,eN(void 0)):(eP.add(t),eZ(t,eO(d)),eX(m,b,{fetcherSubmission:d}));if(ew(b)){eQ(t,n,b.error);return}}if(ev(b))throw ep(400,{type:"defer-action"});let E=D.navigation.location||D.location,_=eo(e.history,E,h.signal),A=r||g,R="idle"!==D.navigation.state?v(A,D.navigation.location,y):D.matches;l(R,"Didn't find any matches after fetcher action");let T=++eA;ex.set(t,T);let O=eO(d,b.data);D.fetchers.set(t,O);let[N,I]=X(e.history,D,R,d,E,!1,S.unstable_skipActionErrorRevalidation,er,ea,es,eD,ek,eP,A,y,[o.route.id,b]);I.filter(e=>e.key!==t).forEach(e=>{let t=e.key,r=D.fetchers.get(t),n=eO(void 0,r?r.data:void 0);D.fetchers.set(t,n),em.has(t)&&e2(t),e.controller&&em.set(t,e.controller)}),eF({fetchers:new Map(D.fetchers)});let x=()=>I.forEach(e=>e2(e.key));h.signal.addEventListener("abort",x);let{loaderResults:P,fetcherResults:k}=await eK(D.matches,R,N,I,_);if(h.signal.aborted)return;h.signal.removeEventListener("abort",x),ex.delete(t),em.delete(t),I.forEach(e=>em.delete(e.key));let L=eh([...P,...k]);if(L){if(L.idx>=N.length){let e=I[L.idx-N.length].key;eP.add(e)}return eX(_,L.result)}let{loaderData:C,errors:j}=el(D,D.matches,N,P,void 0,I,k,eM);if(D.fetchers.has(t)){let e=eN(b.data);D.fetchers.set(t,e)}e8(T),"loading"===D.navigation.state&&T>eI?(l(M,"Expected pending action"),u&&u.abort(),ez(D.navigation.location,{matches:R,loaderData:C,errors:j,fetchers:new Map(D.fetchers)})):(eF({errors:j,loaderData:ec(D.loaderData,C,R,j),fetchers:new Map(D.fetchers)}),er=!1)}async function eq(t,r,n,i,o,a,s,c){let u=D.fetchers.get(t);eZ(t,eO(c,u?u.data:void 0),{flushSync:s});let d=new AbortController,f=eo(e.history,n,d.signal);if(a){let e=await to(o,n,f.signal);if("aborted"===e.type)return;if("error"===e.type){let{error:i}=te(n,e);eQ(t,r,i,{flushSync:s});return}if(e.matches)i=eS(o=e.matches,n);else{eQ(t,r,ep(404,{pathname:n}),{flushSync:s});return}}em.set(t,d);let p=eA,h=(await eY("loader",f,[i],o))[0];if(ev(h)&&(h=await e_(h,f.signal,!0)||h),em.get(t)===d&&em.delete(t),!f.signal.aborted){if(eD.has(t)){eZ(t,eN(void 0));return}if(eb(h)){if(eI>p){eZ(t,eN(void 0));return}eP.add(t),await eX(f,h);return}if(ew(h)){eQ(t,r,h.error);return}l(!ev(h),"Unhandled fetcher deferred data"),eZ(t,eN(h.data))}}async function eX(t,r,i){let{submission:a,fetcherSubmission:s,replace:c}=void 0===i?{}:i;r.response.headers.has("X-Remix-Revalidate")&&(er=!0);let h=r.response.headers.get("Location");l(h,"Expected a Location header on the redirect Response"),h=ei(h,new URL(t.url),y);let m=d(D.location,h,{_isRedirect:!0});if(p){let t=!1;if(r.response.headers.has("X-Remix-Reload-Document"))t=!0;else if(H.test(h)){let r=e.history.createURL(h);t=r.origin!==f.location.origin||null==_(r.pathname,y)}if(t){c?f.location.replace(h):f.location.assign(h);return}}u=null;let v=!0===c?n.Replace:n.Push,{formMethod:w,formAction:b,formEncType:g}=D.navigation;!a&&!s&&w&&b&&g&&(a=eR(D.navigation));let E=a||s;if(U.has(r.response.status)&&E&&ey(E.formMethod))await eH(v,m,{submission:o({},E,{formAction:h}),preventScrollReset:C});else{let e=eT(m,a);await eH(v,m,{overrideNavigation:e,fetcherSubmission:s,preventScrollReset:C})}}async function eY(e,r,n,o){try{let a=await et(E,e,r,n,o,b,t);return await Promise.all(a.map((e,t)=>{var a;if(a=e,eg(a.result)&&j.has(a.result.status)){let a=e.result;return{type:i.redirect,response:function(e,t,r,n,i,o){let a=e.headers.get("Location");if(l(a,"Redirects returned/thrown from loaders/actions must have a Location header"),!H.test(a)){let s=n.slice(0,n.findIndex(e=>e.route.id===r)+1);a=G(new URL(t.url),s,i,!0,a,o),e.headers.set("Location",a)}return e}(a,r,n[t].route.id,o,y,S.v7_relativeSplatPath)}}return en(e)}))}catch(e){return n.map(()=>({type:i.error,error:e}))}}async function eK(t,r,n,o,a){let[s,...l]=await Promise.all([n.length?eY("loader",a,n,r):[],...o.map(t=>t.matches&&t.match&&t.controller?eY("loader",eo(e.history,t.path,t.controller.signal),[t.match],t.matches).then(e=>e[0]):Promise.resolve({type:i.error,error:ep(404,{pathname:t.path})}))]);return await Promise.all([eE(t,n,s,s.map(()=>a.signal),!1,D.loaderData),eE(t,o.map(e=>e.match),l,o.map(e=>e.controller?e.controller.signal:null),!0)]),{loaderResults:s,fetcherResults:l}}function eJ(){er=!0,ea.push(...tt()),ek.forEach((e,t)=>{em.has(t)&&(es.push(t),e2(t))})}function eZ(e,t,r){void 0===r&&(r={}),D.fetchers.set(e,t),eF({fetchers:new Map(D.fetchers)},{flushSync:!0===(r&&r.flushSync)})}function eQ(e,t,r,n){void 0===n&&(n={});let i=ed(D.matches,t);e1(e),eF({errors:{[i.route.id]:r},fetchers:new Map(D.fetchers)},{flushSync:!0===(n&&n.flushSync)})}function e0(e){return S.v7_fetcherPersist&&(eL.set(e,(eL.get(e)||0)+1),eD.has(e)&&eD.delete(e)),D.fetchers.get(e)||z}function e1(e){let t=D.fetchers.get(e);em.has(e)&&!(t&&"loading"===t.state&&ex.has(e))&&e2(e),ek.delete(e),ex.delete(e),eP.delete(e),eD.delete(e),D.fetchers.delete(e)}function e2(e){let t=em.get(e);l(t,"Expected fetch controller: "+e),t.abort(),em.delete(e)}function e6(e){for(let t of e){let e=eN(e0(t).data);D.fetchers.set(t,e)}}function e3(){let e=[],t=!1;for(let r of eP){let n=D.fetchers.get(r);l(n,"Expected fetcher: "+r),"loading"===n.state&&(eP.delete(r),e.push(r),t=!0)}return e6(e),t}function e8(e){let t=[];for(let[r,n]of ex)if(n<e){let e=D.fetchers.get(r);l(e,"Expected fetcher: "+r),"loading"===e.state&&(e2(r),ex.delete(r),t.push(r))}return e6(t),t.length>0}function e4(e){D.blockers.delete(e),eC.delete(e)}function e7(e,t){let r=D.blockers.get(e)||W;l("unblocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"proceeding"===t.state||"blocked"===r.state&&"unblocked"===t.state||"proceeding"===r.state&&"unblocked"===t.state,"Invalid blocker state transition: "+r.state+" -> "+t.state);let n=new Map(D.blockers);n.set(e,t),eF({blockers:n})}function e5(e){let{currentLocation:t,nextLocation:r,historyAction:n}=e;if(0===eC.size)return;eC.size>1&&c(!1,"A router only supports one blocker at a time");let i=Array.from(eC.entries()),[o,a]=i[i.length-1],s=D.blockers.get(o);if((!s||"proceeding"!==s.state)&&a({currentLocation:t,nextLocation:r,historyAction:n}))return o}function e9(e){let t=ep(404,{pathname:e}),{matches:n,route:i}=ef(r||g);return tt(),{notFoundMatches:n,route:i,error:t}}function te(e,t){let r=t.partialMatches,n=r[r.length-1].route,i=ep(400,{type:"route-discovery",routeId:n.id,pathname:e,message:null!=t.error&&"message"in t.error?t.error:String(t.error)});return{notFoundMatches:r,route:n,error:i}}function tt(e){let t=[];return eM.forEach((r,n)=>{(!e||e(n))&&(r.cancel(),t.push(n),eM.delete(n))}),t}function tr(e,t){return N&&N(e,t.map(e=>(function(e,t){let{route:r,pathname:n,params:i}=e;return{id:r.id,pathname:n,params:i,data:t[r.id],handle:r.handle}})(e,D.loaderData)))||e.key}function tn(e,t){if(O){let r=O[tr(e,t)];if("number"==typeof r)return r}return null}function ti(e,t,r){if(A){if(!e)return{active:!0,matches:w(t,r,y,!0)||[]};{let n=e[e.length-1].route;if(n.path&&("*"===n.path||n.path.endsWith("/*")))return{active:!0,matches:w(t,r,y,!0)}}}return{active:!1,matches:null}}async function to(e,n,i){let o=e;for(o.length>0&&o[o.length-1].route;;){let e=null==r,a=r||g;try{await J(A,n,o,a,b,t,ej,i)}catch(e){return{type:"error",error:e,partialMatches:o}}finally{e&&(g=[...g])}if(i.aborted)return{type:"aborted"};let s=v(a,n,y),l=!1;if(s){let e=s[s.length-1].route;if(e.index)return{type:"success",matches:s};if(e.path&&e.path.length>0){if("*"!==e.path)return{type:"success",matches:s};l=!0}}let c=w(a,n,y,!0);if(!c||o.map(e=>e.route.id).join("-")===c.map(e=>e.route.id).join("-"))return{type:"success",matches:l?s:null};if("*"===(o=c)[o.length-1].route.path)return{type:"success",matches:o}}}return s={get basename(){return y},get future(){return S},get state(){return D},get routes(){return g},get window(){return f},initialize:function(){if(R=e.history.listen(t=>{let{action:r,location:n,delta:i}=t;if(eU){eU=!1;return}c(0===eC.size||null!=i,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let o=e5({currentLocation:D.location,nextLocation:n,historyAction:r});if(o&&null!=i){eU=!0,e.history.go(-1*i),e7(o,{state:"blocked",location:n,proceed(){e7(o,{state:"proceeding",proceed:void 0,reset:void 0,location:n}),e.history.go(i)},reset(){let e=new Map(D.blockers);e.set(o,W),eF({blockers:e})}});return}return eH(r,n)}),p){!function(e,t){try{let r=e.sessionStorage.getItem($);if(r){let e=JSON.parse(r);for(let[r,n]of Object.entries(e||{}))n&&Array.isArray(n)&&t.set(r,new Set(n||[]))}}catch(e){}}(f,Y);let e=()=>(function(e,t){if(t.size>0){let r={};for(let[e,n]of t)r[e]=[...n];try{e.sessionStorage.setItem($,JSON.stringify(r))}catch(e){c(!1,"Failed to save applied view transitions in sessionStorage ("+e+").")}}})(f,Y);f.addEventListener("pagehide",e),K=()=>f.removeEventListener("pagehide",e)}return D.initialized||eH(n.Pop,D.location,{initialHydration:!0}),s},subscribe:function(e){return T.add(e),()=>T.delete(e)},enableScrollRestoration:function(e,t,r){if(O=e,I=t,N=r||null,!x&&D.navigation===F){x=!0;let e=tn(D.location,D.matches);null!=e&&eF({restoreScrollPosition:e})}return()=>{O=null,I=null,N=null}},navigate:eW,fetch:function(e,t,n,i){if(h)throw Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");em.has(e)&&e2(e);let o=!0===(i&&i.unstable_flushSync),a=r||g,s=G(D.location,D.matches,y,S.v7_prependBasename,n,S.v7_relativeSplatPath,t,null==i?void 0:i.relative),l=v(a,s,y),c=ti(l,a,s);if(c.active&&c.matches&&(l=c.matches),!l){eQ(e,t,ep(404,{pathname:s}),{flushSync:o});return}let{path:u,submission:d,error:f}=q(S.v7_normalizeFormMethod,!0,s,i);if(f){eQ(e,t,f,{flushSync:o});return}let p=eS(l,u);if(C=!0===(i&&i.preventScrollReset),d&&ey(d.formMethod)){eG(e,t,u,p,l,c.active,o,d);return}ek.set(e,{routeId:t,path:u}),eq(e,t,u,p,l,c.active,o,d)},revalidate:function(){if(eJ(),eF({revalidation:"loading"}),"submitting"!==D.navigation.state){if("idle"===D.navigation.state){eH(D.historyAction,D.location,{startUninterruptedRevalidation:!0});return}eH(M||D.historyAction,D.navigation.location,{overrideNavigation:D.navigation})}},createHref:t=>e.history.createHref(t),encodeLocation:t=>e.history.encodeLocation(t),getFetcher:e0,deleteFetcher:function(e){if(S.v7_fetcherPersist){let t=(eL.get(e)||0)-1;t<=0?(eL.delete(e),eD.add(e)):eL.set(e,t)}else e1(e);eF({fetchers:new Map(D.fetchers)})},dispose:function(){R&&R(),K&&K(),T.clear(),u&&u.abort(),D.fetchers.forEach((e,t)=>e1(t)),D.blockers.forEach((e,t)=>e4(t))},getBlocker:function(e,t){let r=D.blockers.get(e)||W;return eC.get(e)!==t&&eC.set(e,t),r},deleteBlocker:e4,patchRoutes:function(e,n){let i=null==r;Z(e,n,r||g,b,t),i&&(g=[...g],eF({}))},_internalFetchControllers:em,_internalActiveDeferreds:eM,_internalSetRoutes:function(e){r=m(e,t,void 0,b={})}}}function G(e,t,r,n,i,o,a,s){let l,c;if(a){for(let e of(l=[],t))if(l.push(e),e.route.id===a){c=e;break}}else l=t,c=t[t.length-1];let u=T(i||".",R(l,o),_(e.pathname,r)||e.pathname,"path"===s);return null==i&&(u.search=e.search,u.hash=e.hash),(null==i||""===i||"."===i)&&c&&c.route.index&&!eA(u.search)&&(u.search=u.search?u.search.replace(/^\?/,"?index&"):"?index"),n&&"/"!==r&&(u.pathname="/"===u.pathname?r:O([r,u.pathname])),f(u)}function q(e,t,r,n){var i;let o,a;if(!n||!(null!=n&&("formData"in n&&null!=n.formData||"body"in n&&void 0!==n.body)))return{path:r};if(n.formMethod&&(i=n.formMethod,!C.has(i.toLowerCase())))return{path:r,error:ep(405,{method:n.formMethod})};let s=()=>({path:r,error:ep(400,{type:"invalid-body"})}),c=n.formMethod||"get",u=e?c.toUpperCase():c.toLowerCase(),d=em(r);if(void 0!==n.body){if("text/plain"===n.formEncType){if(!ey(u))return s();let e="string"==typeof n.body?n.body:n.body instanceof FormData||n.body instanceof URLSearchParams?Array.from(n.body.entries()).reduce((e,t)=>{let[r,n]=t;return""+e+r+"="+n+"\n"},""):String(n.body);return{path:r,submission:{formMethod:u,formAction:d,formEncType:n.formEncType,formData:void 0,json:void 0,text:e}}}if("application/json"===n.formEncType){if(!ey(u))return s();try{let e="string"==typeof n.body?JSON.parse(n.body):n.body;return{path:r,submission:{formMethod:u,formAction:d,formEncType:n.formEncType,formData:void 0,json:e,text:void 0}}}catch(e){return s()}}}if(l("function"==typeof FormData,"FormData is not available in this environment"),n.formData)o=ea(n.formData),a=n.formData;else if(n.body instanceof FormData)o=ea(n.body),a=n.body;else if(n.body instanceof URLSearchParams)a=es(o=n.body);else if(null==n.body)o=new URLSearchParams,a=new FormData;else try{o=new URLSearchParams(n.body),a=es(o)}catch(e){return s()}let h={formMethod:u,formAction:d,formEncType:n&&n.formEncType||"application/x-www-form-urlencoded",formData:a,json:void 0,text:void 0};if(ey(h.formMethod))return{path:r,submission:h};let m=p(r);return t&&m.search&&eA(m.search)&&o.append("index",""),m.search="?"+o,{path:f(m),submission:h}}function X(e,t,r,n,i,a,s,l,c,u,d,f,p,h,m,w){let b=w?ew(w[1])?w[1].error:w[1].data:void 0,g=e.createURL(t.location),y=e.createURL(i),E=w&&ew(w[1])?w[0]:void 0,_=E?function(e,t){let r=e;if(t){let n=e.findIndex(e=>e.route.id===t);n>=0&&(r=e.slice(0,n))}return r}(r,E):r,A=w?w[1].statusCode:void 0,S=s&&A&&A>=400,R=_.filter((e,r)=>{var i,s;let u,d,{route:f}=e;if(f.lazy)return!0;if(null==f.loader)return!1;if(a)return"function"!=typeof f.loader||!!f.loader.hydrate||void 0===t.loaderData[f.id]&&(!t.errors||void 0===t.errors[f.id]);if(i=t.loaderData,u=!(s=t.matches[r])||e.route.id!==s.route.id,d=void 0===i[e.route.id],u||d||c.some(t=>t===e.route.id))return!0;let p=t.matches[r];return K(e,o({currentUrl:g,currentParams:p.params,nextUrl:y,nextParams:e.params},n,{actionResult:b,unstable_actionStatus:A,defaultShouldRevalidate:!S&&(l||g.pathname+g.search===y.pathname+y.search||g.search!==y.search||Y(p,e))}))}),T=[];return f.forEach((e,i)=>{if(a||!r.some(t=>t.route.id===e.routeId)||d.has(i))return;let s=v(h,e.path,m);if(!s){T.push({key:i,routeId:e.routeId,path:e.path,matches:null,match:null,controller:null});return}let c=t.fetchers.get(i),f=eS(s,e.path);!p.has(i)&&(u.includes(i)||(c&&"idle"!==c.state&&void 0===c.data?l:K(f,o({currentUrl:g,currentParams:t.matches[t.matches.length-1].params,nextUrl:y,nextParams:r[r.length-1].params},n,{actionResult:b,unstable_actionStatus:A,defaultShouldRevalidate:!S&&l}))))&&T.push({key:i,routeId:e.routeId,path:e.path,matches:s,match:f,controller:new AbortController})}),[R,T]}function Y(e,t){let r=e.route.path;return e.pathname!==t.pathname||null!=r&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function K(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if("boolean"==typeof r)return r}return t.defaultShouldRevalidate}async function J(e,t,r,n,i,o,a,s){let l=[t,...r.map(e=>e.route.id)].join("-");try{var c;let u=a.get(l);u||(u=e({path:t,matches:r,patch:(e,t)=>{s.aborted||Z(e,t,n,i,o)}}),a.set(l,u)),u&&(c=u,"object"==typeof c&&null!=c&&"then"in c)&&await u}finally{a.delete(l)}}function Z(e,t,r,n,i){if(e){var o;let r=n[e];l(r,"No route found to patch children into: routeId = "+e);let a=m(t,i,[e,"patch",String((null==(o=r.children)?void 0:o.length)||"0")],n);r.children?r.children.push(...a):r.children=a}else{let e=m(t,i,["patch",String(r.length||"0")],n);r.push(...e)}}async function Q(e,t,r){if(!e.lazy)return;let n=await e.lazy();if(!e.lazy)return;let i=r[e.id];l(i,"No route found in manifest");let a={};for(let e in n){let t=void 0!==i[e]&&"hasErrorBoundary"!==e;c(!t,'Route "'+i.id+'" has a static property "'+e+'" defined but its lazy function is also returning a value for this property. The lazy route property "'+e+'" will be ignored.'),t||h.has(e)||(a[e]=n[e])}Object.assign(i,a),Object.assign(i,o({},t(i),{lazy:void 0}))}function ee(e){return Promise.all(e.matches.map(e=>e.resolve()))}async function et(e,t,r,n,a,s,c,u){let d=n.reduce((e,t)=>e.add(t.route.id),new Set),f=new Set,p=await e({matches:a.map(e=>{let n=d.has(e.route.id);return o({},e,{shouldLoad:n,resolve:o=>(f.add(e.route.id),n?er(t,r,e,s,c,o,u):Promise.resolve({type:i.data,result:void 0}))})}),request:r,params:a[0].params,context:u});return a.forEach(e=>l(f.has(e.route.id),'`match.resolve()` was not called for route id "'+e.route.id+'". You must call `match.resolve()` on every match passed to `dataStrategy` to ensure all routes are properly loaded.')),p.filter((e,t)=>d.has(a[t].route.id))}async function er(e,t,r,n,o,a,s){let c,u;let d=n=>{let i;let o=new Promise((e,t)=>i=t);u=()=>i(),t.signal.addEventListener("abort",u);let l=i=>"function"!=typeof n?Promise.reject(Error('You cannot call the handler for a route which defines a boolean "'+e+'" [routeId: '+r.route.id+"]")):n({request:t,params:r.params,context:s},...void 0!==i?[i]:[]);return Promise.race([a?a(e=>l(e)):(async()=>{try{let e=await l();return{type:"data",result:e}}catch(e){return{type:"error",result:e}}})(),o])};try{let a=r.route[e];if(r.route.lazy){if(a){let e;let[t]=await Promise.all([d(a).catch(t=>{e=t}),Q(r.route,o,n)]);if(void 0!==e)throw e;c=t}else if(await Q(r.route,o,n),a=r.route[e])c=await d(a);else{if("action"!==e)return{type:i.data,result:void 0};let n=new URL(t.url),o=n.pathname+n.search;throw ep(405,{method:t.method,pathname:o,routeId:r.route.id})}}else if(a)c=await d(a);else{let e=new URL(t.url),r=e.pathname+e.search;throw ep(404,{pathname:r})}l(void 0!==c.result,"You defined "+("action"===e?"an action":"a loader")+" for route "+('"'+r.route.id)+"\" but didn't return anything from your `"+e+"` function. Please return a value or `null`.")}catch(e){return{type:i.error,result:e}}finally{u&&t.signal.removeEventListener("abort",u)}return c}async function en(e){var t,r;let{result:n,type:o,status:a}=e;if(eg(n)){let e;try{let t=n.headers.get("Content-Type");e=t&&/\bapplication\/json\b/.test(t)?null==n.body?null:await n.json():await n.text()}catch(e){return{type:i.error,error:e}}return o===i.error?{type:i.error,error:new k(n.status,n.statusText,e),statusCode:n.status,headers:n.headers}:{type:i.data,data:e,statusCode:n.status,headers:n.headers}}return o===i.error?{type:i.error,error:n,statusCode:L(n)?n.status:a}:n&&"object"==typeof n&&"object"==typeof n.data&&"function"==typeof n.subscribe&&"function"==typeof n.cancel&&"function"==typeof n.resolveData?{type:i.deferred,deferredData:n,statusCode:null==(t=n.init)?void 0:t.status,headers:(null==(r=n.init)?void 0:r.headers)&&new Headers(n.init.headers)}:{type:i.data,data:n,statusCode:a}}function ei(e,t,r){if(H.test(e)){let n=new URL(e.startsWith("//")?t.protocol+e:e),i=null!=_(n.pathname,r);if(n.origin===t.origin&&i)return n.pathname+n.search+n.hash}return e}function eo(e,t,r,n){let i=e.createURL(em(t)).toString(),o={signal:r};if(n&&ey(n.formMethod)){let{formMethod:e,formEncType:t}=n;o.method=e.toUpperCase(),"application/json"===t?(o.headers=new Headers({"Content-Type":t}),o.body=JSON.stringify(n.json)):"text/plain"===t?o.body=n.text:"application/x-www-form-urlencoded"===t&&n.formData?o.body=ea(n.formData):o.body=n.formData}return new Request(i,o)}function ea(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,"string"==typeof n?n:n.name);return t}function es(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function el(e,t,r,n,i,a,s,c){let u,d,f,p,h,m;let{loaderData:v,errors:w}=(d={},f=null,p=!1,h={},m=i&&ew(i[1])?i[1].error:void 0,n.forEach((e,n)=>{let i=r[n].route.id;if(l(!eb(e),"Cannot handle redirect results in processLoaderData"),ew(e)){let r=e.error;void 0!==m&&(r=m,m=void 0),f=f||{};{let e=ed(t,i);null==f[e.route.id]&&(f[e.route.id]=r)}d[i]=void 0,p||(p=!0,u=L(e.error)?e.error.status:500),e.headers&&(h[i]=e.headers)}else ev(e)?(c.set(i,e.deferredData),d[i]=e.deferredData.data,null==e.statusCode||200===e.statusCode||p||(u=e.statusCode)):(d[i]=e.data,e.statusCode&&200!==e.statusCode&&!p&&(u=e.statusCode)),e.headers&&(h[i]=e.headers)}),void 0!==m&&i&&(f={[i[0]]:m},d[i[0]]=void 0),{loaderData:d,errors:f,statusCode:u||200,loaderHeaders:h});for(let t=0;t<a.length;t++){let{key:r,match:n,controller:i}=a[t];l(void 0!==s&&void 0!==s[t],"Did not find corresponding fetcher result");let c=s[t];if(!i||!i.signal.aborted){if(ew(c)){let t=ed(e.matches,null==n?void 0:n.route.id);w&&w[t.route.id]||(w=o({},w,{[t.route.id]:c.error})),e.fetchers.delete(r)}else if(eb(c))l(!1,"Unhandled fetcher revalidation redirect");else if(ev(c))l(!1,"Unhandled fetcher deferred data");else{let t=eN(c.data);e.fetchers.set(r,t)}}}return{loaderData:v,errors:w}}function ec(e,t,r,n){let i=o({},t);for(let o of r){let r=o.route.id;if(t.hasOwnProperty(r)?void 0!==t[r]&&(i[r]=t[r]):void 0!==e[r]&&o.route.loader&&(i[r]=e[r]),n&&n.hasOwnProperty(r))break}return i}function eu(e){return e?ew(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function ed(e,t){return(t?e.slice(0,e.findIndex(e=>e.route.id===t)+1):[...e]).reverse().find(e=>!0===e.route.hasErrorBoundary)||e[0]}function ef(e){let t=1===e.length?e[0]:e.find(e=>e.index||!e.path||"/"===e.path)||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function ep(e,t){let{pathname:r,routeId:n,method:i,type:o,message:a}=void 0===t?{}:t,s="Unknown Server Error",l="Unknown @remix-run/router error";return 400===e?(s="Bad Request","route-discovery"===o?l='Unable to match URL "'+r+'" - the `children()` function for route `'+n+"` threw the following error:\n"+a:i&&r&&n?l="You made a "+i+' request to "'+r+'" but did not provide a `loader` for route "'+n+'", so there is no way to handle the request.':"defer-action"===o?l="defer() is not supported in actions":"invalid-body"===o&&(l="Unable to encode submission body")):403===e?(s="Forbidden",l='Route "'+n+'" does not match URL "'+r+'"'):404===e?(s="Not Found",l='No route matches URL "'+r+'"'):405===e&&(s="Method Not Allowed",i&&r&&n?l="You made a "+i.toUpperCase()+' request to "'+r+'" but did not provide an `action` for route "'+n+'", so there is no way to handle the request.':i&&(l='Invalid request method "'+i.toUpperCase()+'"')),new k(e||500,s,Error(l),!0)}function eh(e){for(let t=e.length-1;t>=0;t--){let r=e[t];if(eb(r))return{result:r,idx:t}}}function em(e){let t="string"==typeof e?p(e):e;return f(o({},t,{hash:""}))}function ev(e){return e.type===i.deferred}function ew(e){return e.type===i.error}function eb(e){return(e&&e.type)===i.redirect}function eg(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"object"==typeof e.headers&&void 0!==e.body}function ey(e){return M.has(e.toLowerCase())}async function eE(e,t,r,n,i,o){for(let a=0;a<r.length;a++){let s=r[a],c=t[a];if(!c)continue;let u=e.find(e=>e.route.id===c.route.id),d=null!=u&&!Y(u,c)&&(o&&o[c.route.id])!==void 0;if(ev(s)&&(i||d)){let e=n[a];l(e,"Expected an AbortSignal for revalidating fetcher deferred result"),await e_(s,e,i).then(e=>{e&&(r[a]=e||r[a])})}}}async function e_(e,t,r){if(void 0===r&&(r=!1),!await e.deferredData.resolveData(t)){if(r)try{return{type:i.data,data:e.deferredData.unwrappedData}}catch(e){return{type:i.error,error:e}}return{type:i.data,data:e.deferredData.data}}}function eA(e){return new URLSearchParams(e).getAll("index").some(e=>""===e)}function eS(e,t){let r="string"==typeof t?p(t).search:t.search;if(e[e.length-1].route.index&&eA(r||""))return e[e.length-1];let n=S(e);return n[n.length-1]}function eR(e){let{formMethod:t,formAction:r,formEncType:n,text:i,formData:o,json:a}=e;if(t&&r&&n){if(null!=i)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:i};if(null!=o)return{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0};if(void 0!==a)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:a,text:void 0}}}function eT(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function eO(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function eN(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}Symbol("deferred")},69641:function(e){var t;t=function(){"use strict";var e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t=function(e){return"function"==typeof e.trim?e.trim():e.replace(/^[\s\uFEFF]+|[\s\uFEFF]+$/g,"")};function r(t,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]&&"object"===e(t[i])&&"object"===e(n[i])?r(t[i],n[i]):t[i]=n[i])}function n(e,r,n,o){var a=this;return o=!1!==o,r.replace(/(\\?(?:[#$])){([\w.]+)(?:\(([^)}]*)\))?}/g,function(r,s,l,c){switch(s.charAt(0)){case"\\":return r.substring(1);case"#":if(!o&&!/#{[\w.]+(\(.*\))?}/.test(r))return r;if(l=[l],"string"==typeof c)for(var u=0,d=(c=c.split(",")).length;u<d;u++){var f=t(c[u]);/^\$\d+$/.test(f)?l.push(n[u]):l.push(f)}return i.call.apply(i,[a,e].concat(l));default:return o||l in n&&/\${[\w.]+}/.test(r)?l in n?n[l].toString():"{undefined}":r}}).replace(/\\([#$])/g,"$1")}function i(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},r=arguments[1],o=void 0;if(!t.isDepth||0>r.indexOf("."))o=Object.prototype.hasOwnProperty.call(this,r)?this[r]:null;else for(var o=this,a=r.split("."),s=0,l=a.length;s<l;s++)o=o&&(Object.prototype.hasOwnProperty.call(o,a[s])?o[a[s]]:null);if("string"==typeof o){for(var c=arguments.length,u=Array(2<c?c-2:0),d=2;d<c;d++)u[d-2]=arguments[d];return n.call(this,t,o,("object"===e(u[0])?u[0]||{}:u)||{})}if("object"===(void 0===o?"undefined":e(o))&&o){var f,p=o instanceof Array?[]:{};for(f in o)Object.prototype.hasOwnProperty.call(o,f)&&(p[f]=i.call(this,t,r+"."+f));return p}return o}return function(){var o=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},a={isDepth:!(1<arguments.length&&void 0!==arguments[1])||arguments[1]},s=i.bind(o,a);return s.set=(function(){var n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=arguments[1],o=arguments[2],a=this,s=(i=n.isDepth?t(i).split("."):[i]).pop();if(s){for(var l=0,c=i.length;l<c;l++)Object.prototype.hasOwnProperty.call(a,i[l])||(a[i[l]]={}),a=a[i[l]];n.isDepth&&"object"===e(a[s])&&a[s]&&"object"===(void 0===o?"undefined":e(o))&&o?r(a[s],o):a[s]=o}}).bind(o,a),s.append=(function(t){var n,i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t.isDepth&&this[n]&&"object"===e(this[n])&&"object"===e(i[n])?r(this[n],i[n]):this[n]=i[n])}).bind(o,a),s.parse=(function(t,r){for(var i=arguments.length,o=Array(2<i?i-2:0),a=2;a<i;a++)o[a-2]=arguments[a];return n.call(this,t,r,("object"===e((o=o||[null])[0])?o[0]||{}:o)||{},!1)}).bind(o,a),s.escape=function t(r){if("string"==typeof r)return r.replace(/([#$])/g,"\\$1");if("object"===(void 0===r?"undefined":e(r))&&r)for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(r[n]=t(r[n]));return r},s}},e.exports=t()},34901:(e,t,r)=>{"use strict";r.d(t,{n8:()=>v,xn:()=>h,QB:()=>f,m2:()=>l});var n=r(96540);window.__INSTANCE__=window.__INSTANCE__||new Map;let i=window.__INSTANCE__,o=(e,t)=>{let r=i.get(e);return!r&&t?(i.set(e,t),t):r},a=()=>{let e=o("I18NEXT");return e?(t,r={})=>e.exists(t,r)?e.t(t,r):"":()=>""};var s=function(e){var t=e.text;return n.createElement(n.Fragment,null,t.split("\n").map(function(e,t){return n.createElement(n.Fragment,{key:t.toString()+(e.charAt(0)||"")},!!t&&n.createElement("br",null),e)}))};s.displayName="__MultiLine__";let l=(0,n.memo)(s);var c={b:function(e){var t=e.children;return n.createElement("b",null,t)},span:function(e){var t=e.children;return n.createElement("span",null,t)},strong:function(e){var t=e.children;return n.createElement("strong",null,t)},s:function(e){var t=e.children;return n.createElement("span",null,t)},em:function(e){var t=e.children;return n.createElement("em",null,t)}},u=function(e){var t=e.node,r=t.tag?t.component:n.Fragment;return n.createElement(r,null,t.children.map(function(e,t){return"string"==typeof e?n.createElement(l,{key:"".concat(t.toString()).concat(e.charAt(0)),text:e}):n.createElement(u,{key:"".concat(t.toString()).concat(e.tag),node:e})}))},d=function(e){var t=e.id,r=e.params,i=e.tags,o=e.getText,s=void 0===o?a():o,d=e.useDefaultTags,f=void 0!==d&&d,p=t?s(t,void 0===r?{}:r):"",h=(0,n.useMemo)(function(){return i||f?function(e,t){void 0===t&&(t={});for(var r,i=/<(\/?\w+)\s?(\/)?>/gm,o={tag:"",component:function(e){var t=e.children;return n.createElement(n.Fragment,null,t)},parent:null,children:[]},a=o,s=0;null!==(r=i.exec(e));){var l=r[0],u=r[1],d=r[2],f=u.replace("/",""),p=u===f;if(!t[f]){if(!Object.keys(c).includes(f))continue;t[u]=c[f]}if(a.children.push(e.substring(s,r.index)),s=l.length+r.index,p){var h={tag:u,component:t[u],parent:a,children:[]};a.children.push(h),a=h}a.parent&&(d||!p)&&(a=a.parent)}return e.length>s+1&&a.children.push(e.substring(s)),o}(p,i):null},[p,i]);return h?n.createElement(u,{node:h}):n.createElement(l,{text:p})};d.displayName="__Message__";let f=(0,n.memo)(d);var p={tag:"span",text:""};let h=n.memo(function(e){var t=e.tag,r=e.text,i=Object.keys(e).reduce(function(t,r){return r in p||(t[r]=e[r]),t},{});return n.createElement(t,Object.assign(i,{dangerouslySetInnerHTML:{__html:(r||"").replace(/\r\n|\n|\r/gm,"<br />")}}))});var m={id:"",params:{}};let v=(0,n.memo)(function(e){var t=e.id,r=e.params,i=e.getText,o=void 0===i?a():i,s=Object.keys(e).reduce(function(t,r){return r in m||(t[r]=e[r]),t},{});return(0,n.createElement)(h,Object.assign(s,{text:o(t,r||{})}))})},31721:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(51815),i=r(60548),o=r(30801);let a=n.Ay.createInstance();a.init(Object.assign(Object.assign({},i.C2),{lng:o.BH,resources:{en:{translation:{error_etc_desc:"(code:{{code}})",error_etc_tit:"Temporary error has occurred. Please try again later."}},ko:{translation:{error_etc_desc:"(code:{{code}})",error_etc_tit:"일시적인 오류가 발생하였습니다. 잠시 후 다시 시도해 주세요."}},ja:{translation:{error_etc_desc:"(code:{{code}})",error_etc_tit:"一時的なエラーが発生しました。しばらくしてから、もう一度お試しください。"}},zh_TW:{translation:{error_etc_desc:"(code:{{code}})",error_etc_tit:"臨時出現錯誤，請稍後。"}},zh:{translation:{error_etc_desc:"(code:{{code}})",error_etc_tit:"出现了暂时性的错误。请稍后再试。"}}}}));class s extends Error{constructor(e,t){let r="string"==typeof e?{message:e}:e||{};super(r.message||a.t("error_etc_tit")||""),this.name=r.name||"APIError",this.description=r.description||a.t("error_etc_desc",{code:this.code}),this.customData=r.data,t instanceof s?this.axiosError=null==t?void 0:t.axiosError:this.axiosError=t}get response(){var e;return null===(e=this.axiosError)||void 0===e?void 0:e.response}get status(){var e;return null===(e=this.response)||void 0===e?void 0:e.status}get data(){var e;return this.customData||(null===(e=this.response)||void 0===e?void 0:e.data)||{}}get code(){return(this.data.code||this.status||this.name).toString()}get isCancelled(){var e;return(null===(e=this.axiosError)||void 0===e?void 0:e.code)==="ERR_CANCELED"}get isNetworkError(){var e;return(null===(e=this.axiosError)||void 0===e?void 0:e.code)==="ERR_NETWORK"}}},45370:(e,t,r)=>{"use strict";var n;r.d(t,{A:()=>i}),(n||(n={})).I18NEXT="I18NEXT";let i=n},11394:(e,t,r)=>{"use strict";r.d(t,{Tq:()=>i}),window.__INSTANCE__=window.__INSTANCE__||new Map;let n=window.__INSTANCE__,i=(e,t)=>{n.set(e,t)}},60548:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>s,C2:()=>a});var n=r(45370),i=r(11394),o=r(30801);let a={fallbackLng:!1,nsSeparator:!1,keySeparator:!1,interpolation:{escapeValue:!1}};function s(e){var t,r,s,l;return t=this,r=arguments,s=void 0,l=function*({i18next:e,lng:t=o.BH,getResource:r,callback:s}){return e.init(Object.assign(Object.assign({},a),{lng:t,resources:{[t]:{translation:yield r(t)}}}),(t,r)=>{null==s||s(t,r),t||(e.t=e.t.bind(e),(0,i.Tq)(n.A.I18NEXT,e))})},new(s||(s=Promise))(function(e,n){function i(e){try{a(l.next(e))}catch(e){n(e)}}function o(e){try{a(l.throw(e))}catch(e){n(e)}}function a(t){var r;t.done?e(t.value):((r=t.value)instanceof s?r:new s(function(e){e(r)})).then(i,o)}a((l=l.apply(t,r||[])).next())})}},30801:(e,t,r)=>{"use strict";r.d(t,{BH:()=>u});var n=r(24111);function i(e){let t=null;if(document.cookie){let r=document.cookie.split(`${escape(e)}=`);r.length>1&&(t=unescape(r[1].split(";")[0]))}return t?t.trim():null}let o=["en","ko","ja","zh","zh_TW"],a=["en_US","ko_KR","ja_JP","zh_CN","zh_TW"],s=navigator.userAgent.includes("WorksMobile"),{isMobile:l}=(0,n.Ay)(navigator.userAgent.replace("WorksMobile","")),c=s&&l,u=(()=>{let e=window.navigator.language;return function(e){["ZH_TW","ZH_HANT","ZH_HK","ZH_MO"].includes((e=e.replace("-","_")).toUpperCase())&&(e="zh_TW");let t=e.toUpperCase();if(o.some(e=>e.toUpperCase()===t))return e;let r=(e.split("_").shift()||"").toUpperCase();return o.find(e=>e.toUpperCase()===r)||null}(c?e:i("language")||i("LC")||e||"en")||"en"})();(()=>{if(!a.includes(u))return a.find(e=>0===e.indexOf(`${u}_`))})(),i("timezone")||i("TZ")},32485:(e,t)=>{var r;/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function i(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var o=typeof r;if("string"===o||"number"===o)e.push(r);else if(Array.isArray(r)){if(r.length){var a=i.apply(null,r);a&&e.push(a)}}else if("object"===o){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){e.push(r.toString());continue}for(var s in r)n.call(r,s)&&r[s]&&e.push(s)}}}return e.join(" ")}e.exports?(i.default=i,e.exports=i):void 0!==(r=(function(){return i}).apply(t,[]))&&(e.exports=r)}()},10912:(e,t,r)=>{var n=1/0,i=/[\\^$.*+?()[\]{}|]/g,o=RegExp(i.source),a="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,s="object"==typeof self&&self&&self.Object===Object&&self,l=a||s||Function("return this")(),c=Object.prototype.toString,u=l.Symbol,d=u?u.prototype:void 0,f=d?d.toString:void 0;e.exports=function(e){var t;return(e=null==(t=e)?"":function(e){if("string"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==c.call(t))return f?f.call(e):"";var t,r=e+"";return"0"==r&&1/e==-n?"-0":r}(t))&&o.test(e)?e.replace(i,"\\$&"):e}},73065:function(e,t,r){var n,i;void 0!==(i="function"==typeof(n=function(){"use strict";var e=function(){},t="undefined",r=typeof window!==t&&typeof window.navigator!==t&&/Trident\/|MSIE /.test(window.navigator.userAgent),n=["trace","debug","info","warn","error"];function i(e,t){var r=e[t];if("function"==typeof r.bind)return r.bind(e);try{return Function.prototype.bind.call(r,e)}catch(t){return function(){return Function.prototype.apply.apply(r,[e,arguments])}}}function o(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function a(t,r){for(var i=0;i<n.length;i++){var o=n[i];this[o]=i<t?e:this.methodFactory(o,t,r)}this.log=this.debug}function s(e,r,n){return function(){typeof console!==t&&(a.call(this,r,n),this[e].apply(this,arguments))}}function l(n,a,l){var c;return"debug"===(c=n)&&(c="log"),typeof console!==t&&("trace"===c&&r?o:void 0!==console[c]?i(console,c):void 0!==console.log?i(console,"log"):e)||s.apply(this,arguments)}function c(e,r,i){var o,s=this;r=null==r?"WARN":r;var c="loglevel";function u(){var e;if(typeof window!==t&&c){try{e=window.localStorage[c]}catch(e){}if(typeof e===t)try{var r=window.document.cookie,n=r.indexOf(encodeURIComponent(c)+"=");-1!==n&&(e=/^([^;]+)/.exec(r.slice(n))[1])}catch(e){}return void 0===s.levels[e]&&(e=void 0),e}}"string"==typeof e?c+=":"+e:"symbol"==typeof e&&(c=void 0),s.name=e,s.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},s.methodFactory=i||l,s.getLevel=function(){return o},s.setLevel=function(r,i){if("string"==typeof r&&void 0!==s.levels[r.toUpperCase()]&&(r=s.levels[r.toUpperCase()]),"number"==typeof r&&r>=0&&r<=s.levels.SILENT){if(o=r,!1!==i&&function(e){var r=(n[e]||"silent").toUpperCase();if(typeof window!==t&&c){try{window.localStorage[c]=r;return}catch(e){}try{window.document.cookie=encodeURIComponent(c)+"="+r+";"}catch(e){}}}(r),a.call(s,r,e),typeof console===t&&r<s.levels.SILENT)return"No console available for logging"}else throw"log.setLevel() called with invalid level: "+r},s.setDefaultLevel=function(e){r=e,u()||s.setLevel(e,!1)},s.resetLevel=function(){s.setLevel(r,!1),function(){if(typeof window!==t&&c){try{window.localStorage.removeItem(c);return}catch(e){}try{window.document.cookie=encodeURIComponent(c)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch(e){}}}()},s.enableAll=function(e){s.setLevel(s.levels.TRACE,e)},s.disableAll=function(e){s.setLevel(s.levels.SILENT,e)};var d=u();null==d&&(d=r),s.setLevel(d,!1)}var u=new c,d={};u.getLogger=function(e){if("symbol"!=typeof e&&"string"!=typeof e||""===e)throw TypeError("You must supply a name when creating a logger.");var t=d[e];return t||(t=d[e]=new c(e,u.getLevel(),u.methodFactory)),t};var f=typeof window!==t?window.log:void 0;return u.noConflict=function(){return typeof window!==t&&window.log===u&&(window.log=f),u},u.getLoggers=function(){return d},u.default=u,u})?n.call(t,r,t,e):n)&&(e.exports=i)},10159:(e,t,r)=>{"use strict";var n=r(96540),i=function(e){return e&&"object"==typeof e&&"default"in e?e.default:e}(n),o=r(95576),a=new o,s=a.getBrowser(),l=a.getCPU(),c=a.getDevice(),u=a.getEngine(),d=a.getOS(),f=a.getUA(),p=function(e){if(!e){console.error("No userAgent string was provided");return}var t=new o(e);return{UA:t,browser:t.getBrowser(),cpu:t.getCPU(),device:t.getDevice(),engine:t.getEngine(),os:t.getOS(),ua:t.getUA(),setUserAgent:function(e){return t.setUA(e)}}},h={Mobile:"mobile",Tablet:"tablet",SmartTv:"smarttv",Console:"console",Wearable:"wearable",Embedded:"embedded",Browser:void 0},m={Chrome:"Chrome",Firefox:"Firefox",Opera:"Opera",Yandex:"Yandex",Safari:"Safari",InternetExplorer:"Internet Explorer",Edge:"Edge",Chromium:"Chromium",Ie:"IE",MobileSafari:"Mobile Safari",MIUI:"MIUI Browser",SamsungBrowser:"Samsung Browser"},v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"none";return e||t},w=function(){return!!("undefined"!=typeof window&&(window.navigator||navigator))&&(window.navigator||navigator)},b=function(e){var t=w();return t&&t.platform&&(-1!==t.platform.indexOf(e)||"MacIntel"===t.platform&&t.maxTouchPoints>1&&!window.MSStream)},g=function(e){return e.type===h.Mobile},y=function(e){return e.type===h.Tablet},E=function(e){var t=e.type;return t===h.Mobile||t===h.Tablet},_=function(e){return e.type===h.SmartTv},A=function(e){return e.type===h.Browser},S=function(e){return e.type===h.Wearable},R=function(e){return e.type===h.Console},T=function(e){return e.type===h.Embedded},O=function(e){return v(e.vendor)},N=function(e){return v(e.model)},I=function(e){return v(e.type,"browser")},x=function(e){return"Android"===e.name},P=function(e){return"Windows"===e.name},k=function(e){return"Mac OS"===e.name},L=function(e){return"Windows Phone"===e.name},D=function(e){return"iOS"===e.name},M=function(e){return v(e.version)},C=function(e){return v(e.name)},j=function(e){return e.name===m.Chrome},U=function(e){return e.name===m.Firefox},F=function(e){return e.name===m.Chromium},z=function(e){return e.name===m.Edge},W=function(e){return e.name===m.Yandex},H=function(e){var t=e.name;return t===m.Safari||t===m.MobileSafari},B=function(e){return e.name===m.MobileSafari},$=function(e){return e.name===m.Opera},V=function(e){var t=e.name;return t===m.InternetExplorer||t===m.Ie},G=function(e){return e.name===m.MIUI},q=function(e){return e.name===m.SamsungBrowser},X=function(e){return v(e.version)},Y=function(e){return v(e.major)},K=function(e){return v(e.name)},J=function(e){return v(e.name)},Z=function(e){return v(e.version)},Q=function(){var e=w(),t=e&&e.userAgent&&e.userAgent.toLowerCase();return"string"==typeof t&&/electron/.test(t)},ee=function(e){return"string"==typeof e&&-1!==e.indexOf("Edg/")},et=function(){var e=w();return e&&(/iPad|iPhone|iPod/.test(e.platform)||"MacIntel"===e.platform&&e.maxTouchPoints>1)&&!window.MSStream},er=function(){return b("iPad")},en=function(){return b("iPhone")},ei=function(){return b("iPod")},eo=function(e){return v(e)};_(c),R(c),S(c),T(c),B(s)||er(),F(s);var ea=E(c)||er(),es=(g(c),y(c)||er(),A(c),A(c),x(d),L(d),D(d)||er(),j(s),U(s)),el=H(s),ec=($(s),V(s),M(d),C(d)),eu=(X(s),Y(s),K(s),O(c),N(c),J(u),Z(u),eo(f),z(s)||ee(f),W(s),I(c),et(),er(),en(),ei(),Q(),ee(f),z(s)&&ee(f),P(d)),ed=k(d);G(s),q(s),t.bI=function(e){var t=e.renderWithFragment,r=e.children,o=(e.viewClassName,e.style,e.condition),a=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,["renderWithFragment","children","viewClassName","style","condition"]);return o?t?i.createElement(n.Fragment,null,r):i.createElement("div",a,r):null},t.gY=function(e){if(!e||"string"!=typeof e){console.error("No valid user agent string was provided");return}var t,r,n,i,o,a,s=p(e);return r=(t={device:s.device,browser:s.browser,os:s.os,engine:s.engine,ua:s.ua}).device,n=t.browser,i=t.os,o=t.engine,a=t.ua,{isSmartTV:_(r),isConsole:R(r),isWearable:S(r),isEmbedded:T(r),isMobileSafari:B(n)||er(),isChromium:F(n),isMobile:E(r)||er(),isMobileOnly:g(r),isTablet:y(r)||er(),isBrowser:A(r),isDesktop:A(r),isAndroid:x(i),isWinPhone:L(i),isIOS:D(i)||er(),isChrome:j(n),isFirefox:U(n),isSafari:H(n),isOpera:$(n),isIE:V(n),osVersion:M(i),osName:C(i),fullBrowserVersion:X(n),browserVersion:Y(n),browserName:K(n),mobileVendor:O(r),mobileModel:N(r),engineName:J(o),engineVersion:Z(o),getUA:eo(a),isEdge:z(n)||ee(a),isYandex:W(n),deviceType:I(r),isIOS13:et(),isIPad13:er(),isIPhone13:en(),isIPod13:ei(),isElectron:Q(),isEdgeChromium:ee(a),isLegacyEdge:z(n)&&!ee(a),isWindows:P(i),isMacOs:k(i),isMIUI:G(n),isSamsungBrowser:q(n)}},t.gm=es,t.Ue=ed,t.Fr=ea,t.nr=el,t.uF=eu,t.wH=ec},68155:(e,t,r)=>{"use strict";var n,i,o,a,s,l,c,u,d,f,p;r.d(t,{K6:()=>s,Le:()=>p,W4:()=>i,_v:()=>c,g0:()=>f,hm:()=>d,i9:()=>o,in:()=>a,q_:()=>l,qz:()=>n,w2:()=>u}),function(e){e[e.TEXT=-1]="TEXT",e[e.HTML=1]="HTML"}(n||(n={})),function(e){e.NOT_EXIST="MAIL_NOT_EXIST",e.NO_FOLDER="READ_NO_FOLDER_FAIL",e.READ_FAIL="READ_MAIL_FAIL"}(i||(i={})),function(e){e[e.NOT_USE=0]="NOT_USE",e[e.USE=1]="USE"}(o||(o={})),function(e){e[e.NONE=-1]="NONE",e[e.MOVE=0]="MOVE",e[e.PERMANENT_DELETE=1]="PERMANENT_DELETE"}(a||(a={})),function(e){e[e.UNUSE=0]="UNUSE",e[e.USE=2]="USE"}(s||(s={})),(l||(l={})).FOLDER_NOT_EXIST="FOLDER_NOT_EXIST",function(e){e.MAIL_NOT_EXIST="MAIL_NOT_EXIST",e.FORWARD_RESEND_VIOLATION="FORWARD_RESEND_VIOLATION"}(c||(c={})),function(e){e.FID_INVALID_ERROR="FID_INVALID_ERROR",e.ATTACHED_FILE_NOT_EXIST="ATTACHED_FILE_NOT_EXIST",e.BLOCKED_MALWARE="BLOCKED_MALWARE",e.BLOCKED_EXTENSION="BLOCKED_EXTENSION",e.NOT_AVAILABLE="NOT_AVAILABLE",e.FID_MD5_CHECK_ERROR="FID_MD5_CHECK_ERROR",e.DOWNLOAD_ELAPSED_TERM_ERROR="DOWNLOAD_ELAPSED_TERM_ERROR",e.MAIL_NOT_EXIST="MAIL_NOT_EXIST",e.DOWNLOAD_OWFS_ERROR="DOWNLOAD_OWFS_ERROR",e.DOWNLOAD_OVER_MAXCOUNT_ERROR="DOWNLOAD_OVER_MAXCOUNT_ERROR",e.IP_ADDRESS_RESTRICTED="IP_ADDRESS_RESTRICTED",e.ERROR="ERROR"}(u||(u={})),function(e){e.ACCESS_ERROR="ATTACHMENT_FILE_ACCESS_ERROR",e.FILE_NOT_EXIST="ATTACHED_FILE_NOT_EXIST"}(d||(d={})),function(e){e.DUPLICATED_REQUEST="DUPLICATED_REQUEST",e.IP_ADDRESS_RESTRICTED="IP_ADDRESS_RESTRICTED",e.NOLOGIN="NOLOGIN",e.EXTERNAL_IP_LOGOUT="EXTERNAL_IP_LOGOUT",e.RELOGIN="RELOGIN",e.FAIL="FAIL",e.SYSTEM_ERROR_RELOGIN="SYSTEM_ERROR_RELOGIN",e.ACTIVE_WAITING="ACTIVE_WAITING"}(f||(f={})),function(e){e.UPLOAD_FULL="UPLOAD_FULL",e.BLOCKED_EXTENSIONS="BLOCKED_IMAGE_EXTENSIONS"}(p||(p={}))},43399:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(96540);let i=({callback:e,children:t})=>((0,n.useEffect)(()=>{null==e||e()},[]),n.createElement(n.StrictMode,null,t))},86314:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,m:()=>l});var n=r(96540),i=r(32485),o=r.n(i),a=r(14946);let s=(0,n.forwardRef)(({label:e,className:t,disabled:r=!1,href:i="#/content",children:s,onClick:l,preventsDoubleClick:c=!0,onAuxClick:u,...d},f)=>{let p=(0,n.useCallback)(e=>{r||(c?(0,a.kj)(l)(e):null==l||l(e))},[c,l,r]);return n.createElement("a",{...d,href:i,className:o()(t,{inactive:r}),onClick:(0,a.wo)((0,a.kj)(p)),onAuxClick:e=>{u&&(e.preventDefault(),u(e))},ref:f,"data-disabled":r},e||s)}),l=e=>({children:t})=>n.createElement(s,e,t),c=s},61613:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(96540),i=r(14946),o=r(47883);let a=(0,n.forwardRef)((e,t)=>{let{label:r,type:a="button",className:s="lw_btn",disabled:l=!1,focused:c=!1,onClick:u,title:d,preventsDoubleClick:f=!0,children:p,...h}=e,m=(0,n.useRef)(null),v=(0,n.useMemo)(()=>f?(0,i.kj)(u):u,[f,u]);return(0,n.useEffect)(()=>{c&&setTimeout(()=>{var e;return null==m?void 0:null===(e=m.current)||void 0===e?void 0:e.focus()},0)},[]),n.createElement("button",{...h,ref:(0,o.P)([t,m]),type:a,disabled:l,className:s,onClick:v,title:d},r,p)})},84453:(e,t,r)=>{"use strict";r.d(t,{Aj:()=>c.A,Zb:()=>s,Jn:()=>l,b7:()=>a,Ay:()=>u});var n=r(61613),i=r(96540),o=r(39789);let a=e=>{let{label:t=(0,o.default)("btn_ok"),className:r="lw_btn_point_40"}=e;return i.createElement(n.A,{...e,label:t,className:r})},s=e=>{let{label:t=(0,o.default)("btn_cancel")}=e;return i.createElement(n.A,{...e,className:"lw_btn_40",label:t})},l=e=>{let{label:t,className:r="btn_close"}=e;return i.createElement(n.A,{label:t||i.createElement("i",{className:"blind"},(0,o.default)("blind_close")),className:r,...e})};var c=r(86314);let u=n.A},79746:(e,t,r)=>{"use strict";r.d(t,{n8:()=>d,xn:()=>n.xn,z5:()=>l,QB:()=>u,m2:()=>n.m2,vV:()=>c.default});var n=r(34901),i=r(96540),o=r(10912),a=r.n(o),s=r(67117);let l=({text:e,highlight:t,highlightTag:r="strong",highlightClassName:n="",highlightAll:o=!1})=>{if(!t||"string"!=typeof t)return i.createElement(i.Fragment,null,e);let l=a()(t),c=e.split(RegExp(`(${l})`,"gi"));return o?i.createElement(i.Fragment,null,c.map(e=>e===t?i.createElement(r,{className:n,key:(0,s.N4)()},e):e)):(c[1]=i.createElement(r,{className:n,key:t},c[1]),i.createElement(i.Fragment,null,c.map(e=>e)))};var c=r(39789);let u=n.QB,d=n.n8},46622:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u,x$:()=>h,tB:()=>p,pA:()=>f,xo:()=>d}),r(72712);var n=r(69641),i=r.n(n),o=r(25034),a=r(21633);let s=JSON.parse('{"version":"0","versionPath":"0","releaseVersion":"0","production":true,"ssr":false,"domain_cdn":"static.worksmobile.net","url_cdn":"https://#{domain_cdn}${0}","url_api":"${0}","url_dashboard":"","url_fe":"/public${0}","url_drive_api":"","use_workbox":true,"use_sentry":true,"use_jegle":false,"ai_chat_id":""}'),l=["navercorp.com","linecorp.com","nsbc.co.kr"];if("string"==typeof window.__CONFIG__){let e=JSON.parse(window.__CONFIG__);window.__CONFIG__=(e=>{var t;if("works"!==e.target)return e;let r=null!==(t=l.find(e=>window.location.hostname.indexOf(e)>-1))&&void 0!==t?t:"";return r===e.domain?e:Object.entries(e).reduce((t,[n,i])=>({...t,[n]:"string"==typeof i?i.replace(e.domain,r):i}),{})})({...s,...e})}let c=i()(window.__CONFIG__,!1);c.set("version","4-2-0-148-5e68036"),c.set("versionPath","4-2-0-148-5e68036"),c.set("releaseVersion","4.2.0-148"),c.set("url_fe","https://static.worksmobile.net/service/mail/4-2-0-148-5e68036${0}"),c.set("use_workbox",c("use_workbox")),c.set("use_sentry",c("use_sentry"));let u=c,d="works"===c("target"),f="lineworks"===c("target")&&"kr"===a.Ny,p="kr9"===(0,o.A)("WORKS_RE_LOC"),h=(()=>{let e=c("target");return"lineworks"===e&&f?"naverworks":e})()},45502:(e,t,r)=>{"use strict";var n;r.d(t,{A:()=>i}),function(e){e.LIST_AREA_RESIZE="LIST_AREA_RESIZE",e.UPDATE_INIT_DATA="UPDATE_INIT_DATA",e.ABORT_UPLOAD_IMAGE="ABORT_UPLOAD_IMAGE"}(n||(n={}));let i=n},60068:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>c,EI:()=>s,bo:()=>l,pp:()=>o,t7:()=>a});var n,i=r(71979);!function(e){e.INDEX="/",e.INBOX="/inbox",e.APPROVAL="/approval",e.MY="/my/:folderSN",e.HR="/hr",e.ALL="/all",e.SENT="/sent",e.DRAFT="/draft",e.TRASH="/trash",e.SPAM="/spam",e.MEMO="/memo",e.CUSTOM_MEMO="/memo/:folderSN",e.UNREAD="/unread",e.MARK="/mark",e.TOME="/tome",e.REMIND="/remind",e.VIP="/vip/:email",e.SENDER="/sender/:email",e.RECEIPT="/receipt",e.THREAD="/thread/:mailSN",e.SEARCH="/search",e.WRITE="/compose",e.WRITE_REDIRECT="/compose-redirect"}(n||(n={}));let o=["/inbox","/approval","/my/:folderSN","/hr","/all","/sent","/draft","/trash","/spam","/memo","/memo/:folderSN","/unread","/mark","/tome","/remind","/vip/:email","/sender/:email","/receipt","/thread/:mailSN","/search"],a=Object.values(n),s={[i.pm.ALL]:"/all",[i.pm.INBOX]:"/inbox",[i.pm.SEND]:"/sent",[i.pm.READRECEIPT]:"/receipt",[i.pm.DRAFT]:"/draft",[i.pm.TRASH]:"/trash",[i.pm.SPAM]:"/spam",[i.pm.APPROVE]:"/approval",[i.pm.MEMO]:"/memo",[i.pm.HR]:"/hr"},l={[i.RJ.UNREAD]:"/unread",[i.RJ.MARK]:"/mark",[i.RJ.TOME]:"/tome",[i.RJ.REMIND]:"/remind"},c=n},42350:(e,t,r)=>{"use strict";var n;r.d(t,{G:()=>n}),function(e){e.NAVIGATE="navigate",e.SYNC="sync",e.DRIVE_UPLOAD="DRIVE_UPLOAD",e.EDITOR_MODE="EDITOR_MODE",e.IMAGE_EDITOR="IMAGE_EDITOR",e.UPDATE_SETTINGS="UPDATE_SETTINGS",e.POPUP_WRITE="POPUP_WRITE",e.TOAST_ACTION="TOAST_ACTION",e.MOVE_CALLBACK="MOVE_CALLBACK",e.DELETE_CALLBACK="DELETE_CALLBACK",e.CANCEL_SEND_TOAST="CANCEL_SEND_TOAST",e.SECURITY_KEY="SECURITY_KEY",e.LATEST_INIT_DATA="LATEST_INIT_DATA"}(n||(n={}))},94962:(e,t,r)=>{"use strict";var n;r.d(t,{A:()=>i}),function(e){e.INDEX="/",e.READ="/read/:mailSN",e.LIST="/list/:folderId",e.WRITE="/write",e.REPLY="/reply/:mailSNList",e.REPLY_ALL="/replyAll/:mailSNList",e.FORWARD="/forward/:mailSNList"}(n||(n={}));let i=n},13038:(e,t,r)=>{"use strict";var n;r.d(t,{A:()=>i}),function(e){e.TIMELINE="time",e.CONVERSATION="conversation"}(n||(n={}));let i=n},44545:(e,t,r)=>{"use strict";var n,i;r.d(t,{D:()=>n,W:()=>i}),function(e){e.NORMAL="NORMAL",e.IN_HOUSE="IN_HOUSE",e.SECRET="SECRET",e.PRIVATE="PRIVATE"}(n||(n={})),function(e){e[e.NONE=1/0]="NONE",e[e.ONE_DAY=1]="ONE_DAY",e[e.ONE_WEEK=7]="ONE_WEEK",e[e.ONE_MONTH=30]="ONE_MONTH",e[e.THREE_MONTHS=90]="THREE_MONTHS"}(i||(i={}))},13261:(e,t,r)=>{"use strict";var n=r(96540),i=r(5338),o=r(45588),a=r(51662),s=r(21633),l=r(73065),c=r.n(l),u=r(46622),d=r(45502),f=r(42350),p=r(12379);let h={style:"normal",display:"swap",weight:"400"},m=e=>{let t;switch(s.Hg){case"ko_KR":t=new FontFace("Pretendard_KR",`url("${e("url_cdn","/static/wm/font/PretendardJP-20240112/woff2/PretendardJP-Regular-subset06-00.woff2")}") format("woff2"), url("${e("url_cdn","/static/wm/font/PretendardJP-20240112/woff/PretendardJP-Regular-subset06-00.woff")}") format("woff")`,h);break;case"ja_JP":t=new FontFace("Pretendard_JP",`url("${e("url_cdn","/static/wm/font/PretendardJP-20240112/woff2/PretendardJP-Regular-subset01-00.woff2")}") format("woff2"), url("${e("url_cdn","/static/wm/font/PretendardJP-20240112/woff/PretendardJP-Regular-subset01-00.woff")}") format("woff")`,h);break;case"en_US":t=new FontFace("Pretendard_EN",`url("${e("url_cdn","/static/wm/font/PretendardJP-20240112/woff2/PretendardJP-Regular-subset05-00.woff2")}") format("woff2"), url("${e("url_cdn","/static/wm/font/PretendardJP-20240112/woff/PretendardJP-Regular-subset05-00.woff")}") format("woff")`,h)}null==t||t.load()};var v=r(31721),w=r(71083),b=r(68155),g=r(72716),y=r(61123),E=r(439),_=r(3531),A=r(26294),S=r(43399);let R=async({ErrorComponent:e,cssUrl:t,container:r,props:o,callback:s})=>{t?(0,a.Ef)(t):(0,a.Ef)((0,u.Ay)("url_fe","/css/error.css")),(0,i.H)(null!=r?r:(0,p.It)()).render(n.createElement(n.Suspense,{fallback:null},n.createElement(S.A,{callback:s},n.createElement(e,o))))};var T=r(48899);let O=async()=>{let[{login:e},{closeAllWindows:t}]=await Promise.all([r.e(268).then(r.bind(r,70268)),r.e(7480).then(r.bind(r,27480))]);t(),e()},N=async()=>{let{logout:e}=await r.e(268).then(r.bind(r,70268));e()};async function I(e,t=new v.A){var n;let i=null!=e?e:{};if(!(0,u.Ay)("use_sentry")||!i.status)return;let{config:o,data:a={}}=i;t.name=`MailAPIError(${(null==o?void 0:o.url)||"unknown"}:${(null==a?void 0:a.code)||(null===(n=i.status)||void 0===n?void 0:n.toString())||"unknown"})`,t.message=(null==a?void 0:a.Message)||t.message||"unknown";let s=null==o?void 0:o.data;if(s){let e=null==o?void 0:o.headers.get("Content-Type");"string"!=typeof e&&(e=""),"application/x-www-form-urlencoded"===e?s=(0,_.jg)(s):e.endsWith("/json")||(s=void 0),s&&(0,E.m)(s,"body")&&delete s.body}Promise.all([r.e(6135),r.e(848),r.e(5588),r.e(6096)]).then(r.bind(r,86096)).then(({captureException:e})=>{e(t,s)})}let x=["/json/write","/json/read"];async function P(){w.A.defaults.timeout=3e4,w.A.defaults.paramsSerializer={serialize:e=>(0,_.Gz)(e)},w.A.defaults.formSerializer={indexes:null},w.A.interceptors.response.use(async e=>{var t;let{data:n={}}=e,{LoginStatus:i=""}=n;if(i===b.g0.NOLOGIN)return await O(),new Promise(g.lQ);if(i===b.g0.EXTERNAL_IP_LOGOUT)return await N(),new Promise(g.lQ);if(i===b.g0.RELOGIN){let[{default:e},{alert:t}]=await Promise.all([Promise.resolve().then(r.bind(r,39789)),Promise.all([r.e(4762),r.e(1862)]).then(r.bind(r,1862))]);return await t({title:e("write_alert_login_changed_title"),description:e("write_alert_login_changed_desc")}),(0,y.y)(),new Promise(g.lQ)}if((null==n?void 0:n.Result)!==b.g0.FAIL)return e;if((null==n?void 0:n.Message)===b.g0.SYSTEM_ERROR_RELOGIN)return(null==n?void 0:n.Link)?window.location.href=null==n?void 0:n.Link:await N(),new Promise(g.lQ);if((null==n?void 0:n.Message)===b.g0.IP_ADDRESS_RESTRICTED&&x.includes(null!==(t=e.config.url)&&void 0!==t?t:"")){let{default:e}=await r.e(4884).then(r.bind(r,74884)),t=document.getElementById("container");e("isStandalone")&&(document.getElementsByClassName("pop_wrap")[0].style.display="none",document.getElementById("wrap").style.display="",(0,T.loadGNB)(e("userId"),"N")),R({ErrorComponent:A.default,cssUrl:(0,u.Ay)("url_fe","/css/error_ip.css"),container:t,props:{ip:null==n?void 0:n.ip}})}return I(e),Promise.reject({response:e})},async e=>{let{status:t,data:{code:n}}=e.response||{data:{}};if(401===t)return await O(),new Promise(g.lQ);if(500===t&&n===b.g0.ACTIVE_WAITING){let[{default:e},{alert:t}]=await Promise.all([Promise.resolve().then(r.bind(r,39789)),Promise.all([r.e(4762),r.e(1862)]).then(r.bind(r,1862))]);return await t(e("common_error_active_waiting")),window.location.href=(0,u.Ay)("url_admin",""),new Promise(g.lQ)}return I(e.response),Promise.reject(e)})}r(72712);let k=["csv","doc","docx","ndoc","hwp","hwpx","pdf","ppt","pptx","txt","xls","xlsx"];var L=r(13038),D=r(44545),M=r(25034),C=r(67341),j=r(73885),U=r(71979),F=r(27622),z=r(61970);let W=(e,t,r)=>({...Object.entries(e||{}).reduce((e,[t,r])=>("object"==typeof r&&(r.retentionPeriod||(r.retentionPeriod=D.W.NONE),r.name=(0,j.A)(r.name)),{...e,[t]:r}),{}),defaultLevel:t||D.D.NORMAL,defaultPeriod:r||D.W.NONE}),H=e=>{let t=e=>[L.A.TIMELINE,L.A.CONVERSATION].includes(e),r=Object.entries(e).reduce((e,[r,n])=>(e[Number(r)]=t(n)?n:L.A.TIMELINE,e),{});return e[U.RJ.ALL]&&(r[U.pm.ALL]=t(e[U.RJ.ALL])?e[U.RJ.ALL]:L.A.TIMELINE),r},B=e=>{switch(e){case"verylarge":return 32;case"large":return 20;case"normal":return 14;case"small":return 12;case"verysmall":return 10;default:return parseInt(e,10)||14}},$=async e=>{var t;let{data:n}=e?{data:e}:await w.A.post("/json/initData");if("OK"!==n.Result)return n;let i=!u.tB;window.__STATIC_SETTINGS__=Object.assign(window.__STATIC_SETTINGS__||{},{serviceType:n.serviceType,isGov:"nwe"===n.serviceType&&"kr9"===(0,M.A)("WORKS_RE_LOC"),forwardMail:n.forwardMail,securityLevelInfo:W(n.securityLevelInfo,n.selectedSecurityLevel,n.selectedRetentionPeriod),useWebMessage:n.useWebMessage,useCalendar:n.useCalendar,useTask:n.useTask,useDrive:n.useDrive,mailOTPSettings:{use:n.useDriveLink,required:n.useDriveLink&&n.requiredDriveLink,maxCount:n.useDriveLink?n.driveLinkCount:0},mailAddress:n.mailAddress,userId:n.mailId,domainId:n.domainId,tenantId:n.tenantId,executionFile:n.executionFile?n.executionFile.split(","):[],docViewerExtensions:i?null===(t=n.docViewerExtensions)||void 0===t?void 0:t.map(e=>e.toLowerCase()):k,quickReplyList:n.quickReplyList,bigFileUse:n.bigFileUse,bigFilePeriod:n.bigFilePeriod,mailDomainList:n.mailDomainList.split(","),hideSpamFolder:n.hideSpamFolder,brandName:n.brandName,allowBackup:n.allowBackup,shortCut:n.shortCut||{},showExternalPop3Menu:"nwe"===n.serviceType&&n.showExternalPop3Menu,firstFolderSN:"all"===n.firstFolderSN?U.pm.ALL:n.firstFolderSN,quota:n.quota,useReadReceipt:n.useReadReceipt,listFontSize:Number(n.listFontPxSize),trashPeriod:Number(n.trashPeriod),pageSize:Number(n.listNum),readsFromPopup:n.popupRead,usesWritePopupStandardHeight:n.useStandardHeight,mxRecord:{isMxRecordInactive:n.isMxRecordInactive,isWorksAdmin:n.isWorksAdmin,topAdminEmail:n.topAdminEmail,useMxRecordInactiveUserLink:n.useMxRecordInactiveUserLink,contactAdminMessage:n.contactAdminMessage},isOpenFirstMail:n.openFirstMail,attachFileMaxSize:n.attachFileMaxSize,bigFileMaxSize:n.bigFileMaxSize,bigFileMaxCount:n.bigFileMaxCount,maxExternalRecipientCount:n.maxExternalRecipientCount,isSaveSentMail:n.saveSentMail,autoSavePeriod:Number(n.autoSavePeriod),isValidReplyTo:!1!==n.validReplyTo,sendSeparately:n.sendSeparately,fontName:(0,C.JY)(n.fontName),fontSize:B(n.fontSize),editorMode:n.editorMode||"H2",ndriveDownloadDomains:n.ndriveDownloadDomains,primaryEmail:n.primaryEmailAddr,skin:n.folderSkin,userName:n.userName,allowAIWorks:n.allowAIWorks||!1,isStandby:n.isStandby,showSenderCountry:n.showSenderCountry,isStandalone:!0,useNewDocViewer:i,hideSecurityLevels:n.hideSecurityLevels?n.hideSecurityLevels.split(","):[],popupWrite:n.popupWrite}),window.__DYNAMIC_SETTINGS__=Object.assign(window.__DYNAMIC_SETTINGS__||{},{useSearchHistory:n.useSearchHistory,showRcpt:n.showRcpt,hideSpamImage:n.hideSpamImage,useFoldFolder:n.useFoldFolder,foldedFolder:(0,z.F)(n.foldedFolder),foldVipMailBox:n.foldVipMailBox,useVipMailBox:n.useVipMailBox,divideMode:Object.values(F.H8).includes(n.divideMode)?n.divideMode:F.H8.NONE,actionAfterMoveDelete:n.actionAfterDelete,foldedLNB:n.foldedLNB,folderAreaWidth:n.folderAreaWidth,useQuickReply:n.useQuickReply,completelyDelete:n.completelyDelete,viewTypeMap:H(n.folderView),writesFromPopup:n.popupWrite,showsSearchPreview:!n.subjectOnly,sendAfterReview:n.sendAfterReview.split(";"),showSpamLayer:"Y"===n.showSpamLayer});let[{initializeByRawData:o},{parseAllFolders:a},{default:s}]=await Promise.all([Promise.all([r.e(8388),r.e(5818)]).then(r.bind(r,65818)),Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(1189),r.e(2840)]).then(r.bind(r,32535)),r.e(3556).then(r.bind(r,3556))]);o(a({...n.folderInfo,folderList:n.folders,vipMailBox:n.vipMailBox})),i&&s.getSupportExtensions().then(e=>{Object.assign(window.__STATIC_SETTINGS__,{docViewerExtensions:e})}),window.addEventListener(d.A.UPDATE_INIT_DATA,({detail:e})=>{if(!e||"OK"!==e.Result){(0,y.y)();return}(async()=>{await $(e);let[{dynamicSettings:t},{updateSettings:n}]=await Promise.all([r.e(4884).then(r.bind(r,74884)),Promise.all([r.e(8388),r.e(6135),r.e(848),r.e(3407),r.e(5588),r.e(2357)]).then(r.bind(r,42357))]);n(t)})()},{once:!0})};var V=r(60548),G=r(38842);async function q(e=s.BH){return(0,V.Ay)({i18next:G.A,lng:e,getResource:async()=>r(17382)(`./${e}.json`)})}var X=r(4592);let Y=async()=>{if(s.sg){let e=s.sg;try{Intl.DateTimeFormat(void 0,{timeZone:s.sg})}catch(t){switch(s.Ny){case"kr":e="Asia/Seoul";break;case"jp":e="Asia/Tokyo";break;case"de":e="Europe/Paris";break;default:e=null}}e&&(X.wB.defaultZone=e),X.wB.defaultLocale=s.BH.replace("_","-")}};async function K(){await Promise.all([Y(),q(),P()]);let{default:e}=await Promise.resolve().then(r.bind(r,39789));(0,u.Ay)("production")||c().setDefaultLevel(c().levels.TRACE),(0,p.Dz)(u.Ay),(0,u.Ay)("ssr")||m(u.Ay);let{Workbox:t}=(0,u.Ay)("use_workbox")&&"serviceWorker"in navigator?await r.e(9730).then(r.bind(r,79730)):{Workbox:null};if(t)try{let e=new URLSearchParams({v:(0,u.Ay)("versionPath")}),r=new t(`/sw.js?${e.toString()}`);r.addEventListener("message",e=>{var t,r;(null===(t=e.data)||void 0===t?void 0:t.type)===f.G.LATEST_INIT_DATA&&window.dispatchEvent(new CustomEvent(d.A.UPDATE_INIT_DATA,{detail:null===(r=e.data)||void 0===r?void 0:r.payload}))}),r.register()}catch(e){c().error("SW registration failed: ",e)}return(0,u.Ay)("use_sentry")&&Promise.all([r.e(6135),r.e(5588),r.e(2360),r.e(3184)]).then(r.bind(r,43184)).then(({default:e})=>{e()}),{config:u.Ay,lang:e}}class J extends Error{constructor(e,t){super(e),function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"error",void 0),this.name="SettingsError",this.error=t}}async function Z(e=$){let t;try{if(t=await e()){var n;throw c().error(Z.name,null!==(n=t.Message)&&void 0!==n?n:"No Content"),new J(t.Message,t)}}catch(e){throw c().error(Z.name,e),new J(e.response.data.Message,e.response.data)}let{default:i}=await r.e(4884).then(r.bind(r,74884));if((0,u.Ay)("use_sentry")){let{setUser:e}=await Promise.all([r.e(6135),r.e(5588),r.e(2360),r.e(3184)]).then(r.bind(r,43184));e({id:i("userId"),email:i("mailAddress")})}if((0,u.Ay)("use_jegle")){let{default:e}=await r.e(1473).then(r.bind(r,61473));e()}return i}var Q=r(94962),ee=r(72772);let et=!/^\/write\/(popup|popupExt)/.test(window.location.pathname);function er(){let e=(0,p.It)();(0,p.RZ)(),e.style.display=""}async function en(e,t){let a;let[{default:s},{presetTheme:l,setTheme:c}]=await Promise.all([r.e(4358).then(r.bind(r,34358)),r.e(6685).then(r.bind(r,96685))]),u=document.getElementById("workscmn_header"),d=null==u?void 0:u.getAttribute("data-query");l();let f=await Z();if(Object.values(Q.A).some(e=>(0,o.B6)(e,window.location.pathname))){let{default:e}=await Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(1189),r.e(2269)]).then(r.bind(r,91322));await e(f);return}if(!(0,o.ue)(ee.A,window.location.pathname,"/w")){R({ErrorComponent:(0,n.lazy)(async()=>r.e(8760).then(r.bind(r,88760))),callback:er});return}e("ssr")&&d?(null==u||u.removeAttribute("data-query"),a=async()=>(0,T.loadGNB)(d)):(a=async()=>(0,T.loadGNB)({userId:f("userId")}),er()),a().then(()=>{var e,t;null===(e=(t=window).initGSaasGnb)||void 0===e||e.call(t,{useCallback:!0})}),c(f("skin"));let p=(0,n.lazy)(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(668),r.e(5309),r.e(197),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(9603),r.e(2310),r.e(2298),r.e(857),r.e(6065),r.e(7308),r.e(889)]).then(r.bind(r,55246)));f.set("isStandalone",!1),(0,i.H)(document.getElementById("container")).render(n.createElement(n.Suspense,{fallback:n.createElement(s,{lang:t})},n.createElement(p,null)))}async function ei(e,t){if("IP_ADDRESS_RESTRICTED"===t.message){var i;let o=document.getElementById("container");R({ErrorComponent:(0,n.lazy)(async()=>Promise.resolve().then(r.bind(r,26294))),cssUrl:e("url_fe","/css/error_ip.css"),container:o,props:{ip:null===(i=t.error)||void 0===i?void 0:i.ip},callback:er}),(0,T.loadGNB)(void 0,"N"),window.toggleSettingLayer=()=>{let e=window.open("/settings","works_settings_mail");null==e||e.focus()},(await Promise.resolve().then(r.bind(r,48899))).loadNotifyScript()}else R({ErrorComponent:(0,n.lazy)(async()=>r.e(2332).then(r.bind(r,72332))),callback:er})}(async()=>{let e;if(et){let{protocol:e}=window.location;if("https:"!==e){let e=new URL(window.location.href);e.protocol="https:",window.location.replace(e.href);return}}let{config:t,lang:i}=await K(),o=["/w/selectMail","/w/selectMail/viewer"].includes(window.location.pathname);if(s.mC&&!o){R({ErrorComponent:(0,n.lazy)(async()=>r.e(1312).then(r.bind(r,61312))),cssUrl:t("url_fe","/css/error_mobile.css"),callback:er});return}let{pathname:l}=window.location,c=t("url_fe","/css/mail.css"),u="";switch(l.replace(/(\/)$/,"")){case"/read/popup":e=async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(668),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(2310),r.e(857),r.e(6065),r.e(8400)]).then(r.bind(r,2431));break;case"/write/popup":case"/write/popupExt":e=async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(668),r.e(5309),r.e(2140),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2310),r.e(2298),r.e(857),r.e(7547),r.e(7308),r.e(7990)]).then(r.bind(r,83387));break;case"/translate":e=async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(7255)]).then(r.bind(r,17255));break;case"/receipt/recipientPopup":e=async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(1189),r.e(7822),r.e(6761),r.e(9666)]).then(r.bind(r,9666));break;case"/address/worksPopup":e=async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(1189),r.e(9409)]).then(r.bind(r,61606));break;case"/address/extContactPopup":e=async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(1189),r.e(1797)]).then(r.bind(r,92924)),c=t("url_fe","/css/address_popup.css");break;case"/w/selectMail":e=async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(2331),r.e(874)]).then(r.bind(r,59365)),u=t("url_fe","/css/ai_mail_select.css");break;case"/w/selectMail/viewer":e=async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(2817),r.e(2331),r.e(3215),r.e(8749)]).then(r.bind(r,82744)),u=t("url_fe","/css/eml_viewer.css")}u?(0,a.Uc)(c,u):(0,a.Ef)(c);try{if(e)e(await Z());else{if(function(){if(et&&"mail.navercorp.com"===window.location.host&&"Y"===(0,M.A)("WORKS_CB_YN")){let e=new URL(window.location.href);return e.host="cb.mail.navercorp.com",window.location.replace(e.href),!0}return!1}())return;await en(t,i)}}catch(e){ei(t,e)}})()},39789:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(38842);let i=n.A.t.bind(n.A)},25034:(e,t,r)=>{"use strict";function n(e){let t=null;if(document.cookie){let r=document.cookie.split(`${escape(e)}=`);r.length>1&&(t=unescape(r[1].split(";")[0]))}return t?t.trim():null}function i({sKey:e,sValue:t,nExpire:r,sPath:n,sDomain:i}){let o="";if(n=n||"/",void 0!==r){let e=new Date;e.setDate(e.getDate()+Number(r)),o+=`;expires=${e.toUTCString()}`}void 0!==n&&(o+=`;path=${n}`),void 0!==i&&(o+=`;domain=${i}`),document.cookie=`${e}=${escape(t)}${o}`}r.d(t,{A:()=>n,T:()=>i})},67117:(e,t,r)=>{"use strict";var n,i;r.d(t,{N4:()=>a}),r(28845),r(373),r(72712);let o=(null===(n=self)||void 0===n?void 0:n.crypto)||(null===(i=window)||void 0===i?void 0:i.crypto)||null;function a(e=""){return"function"==typeof(null==o?void 0:o.randomUUID)?`${e}${o.randomUUID()}`:`${e}${Math.random().toString(36).substring(2)}_${Date.now().toString()}`}null==o||o.subtle},51662:(e,t,r)=>{"use strict";function n(e,t={},r){let n=document.createElement(e);return Object.keys(t).forEach(e=>{n.setAttribute(e,t[e]||"")}),r&&r.appendChild(n),n}function i(e){var t;if("function"==typeof e.remove){e.remove();return}null===(t=e.parentElement)||void 0===t||t.removeChild(e)}function o(e,t){let{children:r}=t;r.length?t.insertBefore(e,r[0]):t.appendChild(e)}function a(e){return n("link",{href:e,rel:"stylesheet",type:"text/css"},document.head)}function s(e,t){let r=document.querySelector(`[href="${e}"]`);r&&i(r),a(t)}async function l(e,t){return new Promise(r=>{let i=n("script",{...t,src:e},document.head);i.onload=()=>r(i)})}r.d(t,{Ef:()=>a,Hs:()=>o,Nz:()=>i,Uc:()=>s,ki:()=>l,n:()=>n})},21633:(e,t,r)=>{"use strict";r.d(t,{BH:()=>v,Fr:()=>f,Hg:()=>w,Ny:()=>y,XR:()=>m,iU:()=>g,mC:()=>h,sg:()=>b,tH:()=>p,ux:()=>d});var n,i,o=r(10159),a=r(25034);let s="en_US",l=["en","ko","ja","zh","zh_TW"],c=[s,"ko_KR","ja_JP","zh_CN","zh_TW"],u=()=>new URLSearchParams(window.location.search).get("language"),d=navigator.userAgent.includes("WorksMobile"),{isMobile:f}=(0,o.gY)(navigator.userAgent.replace("WorksMobile","")),p=d&&!f,h=d&&f,m=["ios","android"].includes(o.wH.toLowerCase())?o.wH.toLowerCase():"etc",v=(()=>{let e=window.navigator.language,t=h?e:(0,a.A)("language")||(0,a.A)("LC")||e||"en";return function(e){var t;["ZH_TW","ZH_HANT","ZH_HK","ZH_MO"].includes((e=e.replace("-","_")).toUpperCase())&&(e="zh_TW");let r=e.toUpperCase();if(l.some(e=>e.toUpperCase()===r))return e;let n=(null!==(t=e.split("_").shift())&&void 0!==t?t:"").toUpperCase();return l.find(e=>e.toUpperCase()===n)||""}(u()||t)||"en"})(),w=(()=>{var e;return c.includes(v)?v:null!==(e=c.find(e=>e.startsWith(`${v}_`)))&&void 0!==e?e:s})(),b=(()=>{var e;let t=(0,a.A)("timezone")||(0,a.A)("TZ");return(null!==(e=null==t?void 0:t.split(":")[0])&&void 0!==e?e:"").trim()})(),g=(()=>{let e=!1;try{let t={get passive(){return e=!0,null}};window.addEventListener("test",t,t),window.removeEventListener("test",t,t)}catch(t){e=!1}return e?{capture:!1,passive:!0}:void 0})(),y=null!==(i=null===(n=(0,a.A)("WORKS_RE_LOC"))||void 0===n?void 0:n.replace(/\d/g,""))&&void 0!==i?i:""},14946:(e,t,r)=>{"use strict";r.d(t,{MK:()=>l,dG:()=>a,kj:()=>u,u2:()=>c,uA:()=>s,wo:()=>o});var n=r(72716),i=r(90971);function o(e=n.lQ){return t=>{t.preventDefault(),e(t)}}function a(e=n.lQ){return t=>{t.stopPropagation(),e(t)}}function s(e){return 1===e.button}function l(e){let t=e.target.closest(i.GH);return!t||"true"===t.dataset.disabled||!0===t.disabled}function c(e,t,r={bubbles:!0,cancelable:!0}){e.dispatchEvent(new Event(t,r))}function u(e=n.lQ){return t=>{t.detail<=1&&e(t)}}},67341:(e,t,r)=>{"use strict";r.d(t,{Hp:()=>a,JY:()=>i});let n=e=>{var t;let r="Comic Sans MS"===e?"Courier New":"Comic Sans MS",n="position:absolute !important; font-size:200px !important; left:-9999px !important; top:-9999px !important;",i=document.body||document.documentElement,o=document.createElement("div");o.innerHTML=`mmmmiiiii${window.decodeURI("한글")}`,o.style.cssText=`${n}font-family:"${r}" !important`,i.firstChild?i.insertBefore(o,i.firstChild):document.body.appendChild(o);let a=o.offsetWidth,s=o.offsetHeight;o.style.cssText=`${n}font-family:"${e.replace(/,/gi,'","')}", "${r}" !important`;let l=a!==o.offsetWidth||s!==o.offsetHeight;return null===(t=o.parentElement)||void 0===t||t.removeChild(o),l},i=e=>{if("SANS-SERIF"===e.toUpperCase())return"sans-serif";switch(e){case"나눔고딕":case"NanumGothic":return"나눔고딕";case"나눔명조":case"NanumMyeongjo":return"나눔명조";case"나눔스퀘어":case"NanumSquare":return"나눔스퀘어";default:return e}},o=e=>{switch(e){case"나눔고딕":return"NanumGothic";case"나눔명조":return"NanumMyeongjo";case"나눔스퀘어":return"NanumSquare";default:return e}},a=e=>n(e)||n(o(e))},72716:(e,t,r)=>{"use strict";function n(e){window.setTimeout(e,10)}async function i(e){return new Promise(t=>{window.setTimeout(t,e)})}r.d(t,{V1:()=>n,cb:()=>i,lQ:()=>o});let o=(...e)=>{}},61123:(e,t,r)=>{"use strict";r.d(t,{V:()=>i,y:()=>o});var n=r(21633);function i(){try{if(n.ux){if(window.oneapp)window.oneapp.close();else{var e;null===(e=window.pcapp)||void 0===e||e.invoke("cancelForWeb","","")}}else window.close()}catch(e){window.close()}}function o(){window.location.reload()}},439:(e,t,r)=>{"use strict";function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}r.d(t,{m:()=>n})},47883:(e,t,r)=>{"use strict";r.d(t,{P:()=>n});let n=e=>t=>{e.forEach(e=>{"function"==typeof e?e(t):null!==e&&(e.current=t)})}},73885:(e,t,r)=>{"use strict";function n(e){return e.replace(/&amp;/g,"&").replace(/\\&quot;/g,'"').replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&#61;/g,"=").replace(/&nbsp;/g," ")}r.d(t,{A:()=>n})},3531:(e,t,r)=>{"use strict";r.d(t,{Gz:()=>n,TB:()=>a,aC:()=>o,jg:()=>i});let n=(e,t=!1)=>{t&&(e={...e,t:`${Date.now()}`});let r=new URLSearchParams;for(let[t,n]of Object.entries(e))if(void 0!==n&&""!==n&&null!==n){if(Array.isArray(n))for(let e of n)r.append(t,e.toString());else r.set(t,n.toString())}return r.toString()},i=e=>{"string"==typeof e&&(e=new URLSearchParams(e));let t={};for(let r of e.keys()){let n=e.getAll(r);n.length>1?t[r]=e.getAll(r):t[r]=n.shift()}return t},o=(e="",t={},r=!0)=>{let[o,a=""]=e.split("?"),s=n({...i(a),...t},r);return s?`${o}?${s}`:o},a="https:"},26294:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(96540),i=r(84453),o=r(79746),a=r(61123);let s=({ip:e})=>n.createElement("div",{className:"lw_error_wrap"},n.createElement("div",{className:"lw_error_cont"},n.createElement("h1",{className:"tit"},n.createElement(o.QB,{id:"mail_page_ip_based_access_control_alert_title"})),n.createElement("p",{className:"desc"},n.createElement(o.n8,{tag:"span",id:"mail_page_ip_based_access_control_alert_body",params:{ip:e}})),n.createElement(i.Aj,{className:"btn_back",onClick:a.y},n.createElement(o.QB,{id:"mail_page_ip_based_access_control_alert_refresh"}))))},72772:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(60068);function i(e){return async()=>{let{default:t}=await e();return{Component:t}}}function o(e){return async()=>({default:(await Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(1844)]).then(r.bind(r,31844)))[e]})}function a(e){return async()=>({default:(await Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(6606)]).then(r.bind(r,16606)))[e]})}function s(e){return async()=>({default:(await Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(8266)]).then(r.bind(r,8266)))[e]})}let l=[{path:`${n.Ay.INBOX}/unread?`,lazy:i(o("Inbox"))},{path:`${n.Ay.MY}/unread?`,lazy:i(o("My"))},{path:`${n.Ay.HR}/unread?`,lazy:i(o("HRList"))},{path:`${n.Ay.ALL}/unread?`,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(9599)]).then(r.bind(r,39599)))},{path:`${n.Ay.SENT}/unread?`,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(1112)]).then(r.bind(r,51112)))},{path:`${n.Ay.DRAFT}/unread?`,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(7975)]).then(r.bind(r,7975)))},{path:`${n.Ay.TRASH}/unread?`,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(6960)]).then(r.bind(r,76960)))},{path:`${n.Ay.SPAM}/unread?`,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(4859)]).then(r.bind(r,94859)))},{path:`${n.Ay.THREAD}/unread?`,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(420)]).then(r.bind(r,90420)))},{path:`${n.Ay.APPROVAL}/unread?`,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(9995)]).then(r.bind(r,59995)))},{path:`${n.Ay.MEMO}/unread?`,lazy:i(a("MemoList"))},{path:`${n.Ay.CUSTOM_MEMO}/unread?`,lazy:i(a("CustomMemoList"))},{path:`${n.Ay.MARK}/unread?`,lazy:i(s("Mark"))},{path:`${n.Ay.TOME}/unread?`,lazy:i(s("ToMe"))},{path:n.Ay.UNREAD,lazy:i(s("Unread"))},{path:n.Ay.REMIND,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(3738)]).then(r.bind(r,93738)))},{path:`${n.Ay.VIP}/unread?`,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(8910)]).then(r.bind(r,8910)))},{path:`${n.Ay.SENDER}/unread?`,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(5027)]).then(r.bind(r,35027)))},{path:n.Ay.RECEIPT,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(7702)]).then(r.bind(r,87702)))},{path:n.Ay.SEARCH,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(8056),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2817),r.e(2331),r.e(9603),r.e(1084),r.e(9605),r.e(2622),r.e(2454)]).then(r.bind(r,2454)))},{path:n.Ay.WRITE,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(6135),r.e(848),r.e(3407),r.e(3966),r.e(4641),r.e(668),r.e(5309),r.e(2140),r.e(1189),r.e(7822),r.e(6761),r.e(1772),r.e(1160),r.e(7473),r.e(3855),r.e(2310),r.e(2298),r.e(7547),r.e(393)]).then(r.bind(r,60393)))},{path:n.Ay.WRITE_REDIRECT,lazy:i(async()=>Promise.all([r.e(8388),r.e(4762),r.e(1324),r.e(1189),r.e(3734)]).then(r.bind(r,80147)))}].map(({path:e,...t})=>({path:e.slice(1),...t}))},12379:(e,t,r)=>{"use strict";r.d(t,{Dz:()=>l,It:()=>c,RZ:()=>u});var n=r(51662),i=r(21633);function o(e){window.document.body.classList.add(e)}let a=()=>{let e;switch(i.Hg){case"ko_KR":e="ko";break;case"ja_JP":e="ja";break;case"zh_CN":e="zh-Hans";break;case"zh_TW":e="zh-Hant";break;default:e="en"}document.documentElement.setAttribute("lang",e),o(i.Hg)},s=()=>{document.documentElement.setAttribute("data-useragent",navigator.userAgent)};function l(e){a(),s(),e&&o("works"===e("target")?"ncs":"bp_works")}function c(){return document.getElementById("wrap")}function u(){let e=document.getElementById("first_loading");e&&(0,n.Nz)(e)}},61970:(e,t,r)=>{"use strict";function n(e){var t;return null===(t=e.split(","))||void 0===t?void 0:t.map(e=>Number(e))}r.d(t,{F:()=>n})},71979:(e,t,r)=>{"use strict";var n,i,o;r.d(t,{KO:()=>n,RJ:()=>i,pm:()=>o}),function(e){e.ALL="all",e.SYSTEM="system",e.USER="user",e.MEMO="memo",e.VIP="vip"}(n||(n={})),function(e){e.ALL="all",e.UNREAD="unread",e.MARK="mark",e.TOME="tome",e.IDOMAIN="idomain",e.REMIND="remind"}(i||(i={})),function(e){e[e.ALL=-1]="ALL",e[e.INBOX=0]="INBOX",e[e.SEND=1]="SEND",e[e.READRECEIPT=2]="READRECEIPT",e[e.DRAFT=3]="DRAFT",e[e.TRASH=4]="TRASH",e[e.SPAM=5]="SPAM",e[e.MEMO=6]="MEMO",e[e.HR=11]="HR",e[e.APPROVE=12]="APPROVE",e[e.TEMPLATE=13]="TEMPLATE"}(o||(o={}))},27622:(e,t,r)=>{"use strict";var n,i,o;r.d(t,{H8:()=>i,p6:()=>o}),function(e){e.NCS="ncs",e.NWE="nwe"}(n||(n={})),function(e){e.NONE="L",e.HORIZONTAL="H"}(i||(i={})),function(e){e.LIST="0",e.PREV_MAIL="1",e.NEXT_MAIL="2"}(o||(o={}))},90971:(e,t,r)=>{"use strict";r.d(t,{GH:()=>n,J0:()=>l,_1:()=>c,aO:()=>o,cH:()=>s,r:()=>u});let n='a,button,input,textarea,select,label,div[contenteditable="true"]',i=e=>{let t=document.createRange(),r=window.getSelection();t.selectNodeContents(e),null==r||r.removeAllRanges(),null==r||r.addRange(t),e.scrollIntoView({block:"nearest"})},o=(e,t)=>{let r=window.getSelection(),n=null==r?void 0:r.getRangeAt(0);n&&t.contains(n.commonAncestorContainer)?(n.deleteContents(),n.insertNode(e)):t.appendChild(e),i(e)},a=e=>{let t=e.parentElement;for(;t;){if(t.scrollHeight>t.clientHeight)return t;t=null==t?void 0:t.parentElement}return null},s=(e,t)=>{let r=a(e),{top:n,bottom:i,left:o,right:s}=e.getBoundingClientRect();if(r){let e=r.getBoundingClientRect();return t?n>=e.top&&i<=e.bottom&&o>=e.left&&s<=e.right:i>e.top&&n<e.bottom&&s>e.left&&o<e.right}return t?n>=0&&i<=window.innerHeight&&o>=0&&s<=window.innerWidth:i>0&&n<window.innerHeight&&s>0&&o<=window.innerWidth},l=(e,t)=>{let r=e.matches(n)?e:e.querySelector(n);r&&(r.focus(t),r.blur())},c=()=>document.querySelector(".toolbars")||void 0,u=e=>{if(document.getElementById(e))return document.getElementById(e);let t=document.createElement("div");return t.setAttribute("id",e),document.body.appendChild(t),t}},48899:(e,t,r)=>{"use strict";r.r(t),r.d(t,{loadGNB:()=>u,loadNotifyScript:()=>c});var n=r(73065),i=r.n(n),o=r(46622),a=r(51662),s=r(21633),l=r(72716);let c=async()=>{if(await (0,l.cb)(2e3),await (0,a.ki)((0,o.Ay)("url_notify",`/latest.min.js?${Date.now()}`),{async:""}),"object"==typeof window.WorksNotify)try{window.WorksNotify.ready({service:window.WorksNotify.ServiceName.MAIL})}catch(e){i().log(e)}},u=async(e,t="Y")=>{var r;let n=Date.now(),i="string"==typeof e?e:encodeURIComponent(`${s.Hg}_${(null!==(r=null==e?void 0:e.userId)&&void 0!==r?r:n).toString()}_${Math.floor(n/9e5)}`),l=(0,o.Ay)("url_gnb",`?q=${i}`);return"N"===t&&(l+=`&showToggleLnb=${t}`),(0,a.ki)(l)}},95576:function(e,t,r){var n;!function(i,o){"use strict";var a="function",s="undefined",l="object",c="string",u="model",d="name",f="type",p="vendor",h="version",m="architecture",v="console",w="mobile",b="tablet",g="smarttv",y="wearable",E="embedded",_="Amazon",A="Apple",S="ASUS",R="BlackBerry",T="Browser",O="Chrome",N="Firefox",I="Google",x="Huawei",P="Microsoft",k="Motorola",L="Opera",D="Samsung",M="Sharp",C="Sony",j="Xiaomi",U="Zebra",F="Facebook",z=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},W=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},H=function(e,t){return typeof e===c&&-1!==B(t).indexOf(B(e))},B=function(e){return e.toLowerCase()},$=function(e,t){if(typeof e===c)return e=e.replace(/^\s\s*/,"").replace(/\s\s*$/,""),typeof t===s?e:e.substring(0,350)},V=function(e,t){for(var r,n,i,s,c,u,d=0;d<t.length&&!c;){var f=t[d],p=t[d+1];for(r=n=0;r<f.length&&!c;)if(c=f[r++].exec(e))for(i=0;i<p.length;i++)u=c[++n],typeof(s=p[i])===l&&s.length>0?2===s.length?typeof s[1]==a?this[s[0]]=s[1].call(this,u):this[s[0]]=s[1]:3===s.length?typeof s[1]!==a||s[1].exec&&s[1].test?this[s[0]]=u?u.replace(s[1],s[2]):void 0:this[s[0]]=u?s[1].call(this,u,s[2]):void 0:4===s.length&&(this[s[0]]=u?s[3].call(this,u.replace(s[1],s[2])):void 0):this[s]=u||o;d+=2}},G=function(e,t){for(var r in t)if(typeof t[r]===l&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(H(t[r][n],e))return"?"===r?o:r}else if(H(t[r],e))return"?"===r?o:r;return e},q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},X={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[h,[d,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[h,[d,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[d,h],[/opios[\/ ]+([\w\.]+)/i],[h,[d,L+" Mini"]],[/\bopr\/([\w\.]+)/i],[h,[d,L]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(weibo)__([\d\.]+)/i],[d,h],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[h,[d,"UC"+T]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[h,[d,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[h,[d,"WeChat"]],[/konqueror\/([\w\.]+)/i],[h,[d,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[h,[d,"IE"]],[/yabrowser\/([\w\.]+)/i],[h,[d,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[d,/(.+)/,"$1 Secure "+T],h],[/\bfocus\/([\w\.]+)/i],[h,[d,N+" Focus"]],[/\bopt\/([\w\.]+)/i],[h,[d,L+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[h,[d,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[h,[d,"Dolphin"]],[/coast\/([\w\.]+)/i],[h,[d,L+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[h,[d,"MIUI "+T]],[/fxios\/([-\w\.]+)/i],[h,[d,N]],[/\bqihu|(qi?ho?o?|360)browser/i],[[d,"360 "+T]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[d,/(.+)/,"$1 "+T],h],[/(comodo_dragon)\/([\w\.]+)/i],[[d,/_/g," "],h],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[d,h],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[d],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[d,F],h],[/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[d,h],[/\bgsa\/([\w\.]+) .*safari\//i],[h,[d,"GSA"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[h,[d,O+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[d,O+" WebView"],h],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[h,[d,"Android "+T]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[d,h],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[h,[d,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[h,d],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[d,[h,G,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[d,h],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[d,"Netscape"],h],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[h,[d,N+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[d,h]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[m,"amd64"]],[/(ia32(?=;))/i],[[m,B]],[/((?:i[346]|x)86)[;\)]/i],[[m,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[m,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[m,"armhf"]],[/windows (ce|mobile); ppc;/i],[[m,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[m,/ower/,"",B]],[/(sun4\w)[;\)]/i],[[m,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[m,B]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[u,[p,D],[f,b]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[u,[p,D],[f,w]],[/\((ip(?:hone|od)[\w ]*);/i],[u,[p,A],[f,w]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[u,[p,A],[f,b]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[u,[p,x],[f,b]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[u,[p,x],[f,w]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[u,/_/g," "],[p,j],[f,w]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[u,/_/g," "],[p,j],[f,b]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[u,[p,"OPPO"],[f,w]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[u,[p,"Vivo"],[f,w]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[u,[p,"Realme"],[f,w]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[u,[p,k],[f,w]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[u,[p,k],[f,b]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[u,[p,"LG"],[f,b]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[u,[p,"LG"],[f,w]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[u,[p,"Lenovo"],[f,b]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[u,/_/g," "],[p,"Nokia"],[f,w]],[/(pixel c)\b/i],[u,[p,I],[f,b]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[u,[p,I],[f,w]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[u,[p,C],[f,w]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[u,"Xperia Tablet"],[p,C],[f,b]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[u,[p,"OnePlus"],[f,w]],[/(alexa)webm/i,/(kf[a-z]{2}wi)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[u,[p,_],[f,b]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[u,/(.+)/g,"Fire Phone $1"],[p,_],[f,w]],[/(playbook);[-\w\),; ]+(rim)/i],[u,p,[f,b]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[u,[p,R],[f,w]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[u,[p,S],[f,b]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[u,[p,S],[f,w]],[/(nexus 9)/i],[u,[p,"HTC"],[f,b]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic|sony(?!-bra))[-_ ]?([-\w]*)/i],[p,[u,/_/g," "],[f,w]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[u,[p,"Acer"],[f,b]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[u,[p,"Meizu"],[f,w]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[u,[p,M],[f,w]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[p,u,[f,w]],[/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[p,u,[f,b]],[/(surface duo)/i],[u,[p,P],[f,b]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[u,[p,"Fairphone"],[f,w]],[/(u304aa)/i],[u,[p,"AT&T"],[f,w]],[/\bsie-(\w*)/i],[u,[p,"Siemens"],[f,w]],[/\b(rct\w+) b/i],[u,[p,"RCA"],[f,b]],[/\b(venue[\d ]{2,7}) b/i],[u,[p,"Dell"],[f,b]],[/\b(q(?:mv|ta)\w+) b/i],[u,[p,"Verizon"],[f,b]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[u,[p,"Barnes & Noble"],[f,b]],[/\b(tm\d{3}\w+) b/i],[u,[p,"NuVision"],[f,b]],[/\b(k88) b/i],[u,[p,"ZTE"],[f,b]],[/\b(nx\d{3}j) b/i],[u,[p,"ZTE"],[f,w]],[/\b(gen\d{3}) b.+49h/i],[u,[p,"Swiss"],[f,w]],[/\b(zur\d{3}) b/i],[u,[p,"Swiss"],[f,b]],[/\b((zeki)?tb.*\b) b/i],[u,[p,"Zeki"],[f,b]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[p,"Dragon Touch"],u,[f,b]],[/\b(ns-?\w{0,9}) b/i],[u,[p,"Insignia"],[f,b]],[/\b((nxa|next)-?\w{0,9}) b/i],[u,[p,"NextBook"],[f,b]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[p,"Voice"],u,[f,w]],[/\b(lvtel\-)?(v1[12]) b/i],[[p,"LvTel"],u,[f,w]],[/\b(ph-1) /i],[u,[p,"Essential"],[f,w]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[u,[p,"Envizen"],[f,b]],[/\b(trio[-\w\. ]+) b/i],[u,[p,"MachSpeed"],[f,b]],[/\btu_(1491) b/i],[u,[p,"Rotor"],[f,b]],[/(shield[\w ]+) b/i],[u,[p,"Nvidia"],[f,b]],[/(sprint) (\w+)/i],[p,u,[f,w]],[/(kin\.[onetw]{3})/i],[[u,/\./g," "],[p,P],[f,w]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[u,[p,U],[f,b]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[u,[p,U],[f,w]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[p,u,[f,v]],[/droid.+; (shield) bui/i],[u,[p,"Nvidia"],[f,v]],[/(playstation [345portablevi]+)/i],[u,[p,C],[f,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[u,[p,P],[f,v]],[/smart-tv.+(samsung)/i],[p,[f,g]],[/hbbtv.+maple;(\d+)/i],[[u,/^/,"SmartTV"],[p,D],[f,g]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[p,"LG"],[f,g]],[/(apple) ?tv/i],[p,[u,A+" TV"],[f,g]],[/crkey/i],[[u,O+"cast"],[p,I],[f,g]],[/droid.+aft(\w)( bui|\))/i],[u,[p,_],[f,g]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[u,[p,M],[f,g]],[/(bravia[\w ]+)( bui|\))/i],[u,[p,C],[f,g]],[/(mitv-\w{5}) bui/i],[u,[p,j],[f,g]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w ]*; *(\w[^;]*);([^;]*)/i],[[p,$],[u,$],[f,g]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[f,g]],[/((pebble))app/i],[p,u,[f,y]],[/droid.+; (glass) \d/i],[u,[p,I],[f,y]],[/droid.+; (wt63?0{2,3})\)/i],[u,[p,U],[f,y]],[/(quest( 2)?)/i],[u,[p,F],[f,y]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[p,[f,E]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[u,[f,w]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[u,[f,b]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[f,b]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[f,w]],[/(android[-\w\. ]{0,9});.+buil/i],[u,[p,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[h,[d,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[h,[d,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i],[d,h],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[h,d]],os:[[/microsoft (windows) (vista|xp)/i],[d,h],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[d,[h,G,q]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[d,"Windows"],[h,G,q]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/cfnetwork\/.+darwin/i],[[h,/_/g,"."],[d,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[d,"Mac OS"],[h,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[h,d],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[d,h],[/\(bb(10);/i],[h,[d,R]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[h,[d,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[h,[d,N+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[h,[d,"webOS"]],[/crkey\/([\d\.]+)/i],[h,[d,O+"cast"]],[/(cros) [\w]+ ([\w\.]+\w)/i],[[d,"Chromium OS"],h],[/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[d,h],[/(sunos) ?([\w\.\d]*)/i],[[d,"Solaris"],h],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux)/i,/(unix) ?([\w\.]*)/i],[d,h]]},Y=function(e,t){if(typeof e===l&&(t=e,e=o),!(this instanceof Y))return new Y(e,t).getResult();var r=e||(typeof i!==s&&i.navigator&&i.navigator.userAgent?i.navigator.userAgent:""),n=t?z(X,t):X;return this.getBrowser=function(){var e,t={};return t[d]=o,t[h]=o,V.call(t,r,n.browser),t.major=typeof(e=t.version)===c?e.replace(/[^\d\.]/g,"").split(".")[0]:o,t},this.getCPU=function(){var e={};return e[m]=o,V.call(e,r,n.cpu),e},this.getDevice=function(){var e={};return e[p]=o,e[u]=o,e[f]=o,V.call(e,r,n.device),e},this.getEngine=function(){var e={};return e[d]=o,e[h]=o,V.call(e,r,n.engine),e},this.getOS=function(){var e={};return e[d]=o,e[h]=o,V.call(e,r,n.os),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(e){return r=typeof e===c&&e.length>350?$(e,350):e,this},this.setUA(r),this};Y.VERSION="1.0.32",Y.BROWSER=W([d,h,"major"]),Y.CPU=W([m]),Y.DEVICE=W([u,p,f,v,w,g,b,y,E]),Y.ENGINE=Y.OS=W([d,h]),typeof t!==s?(e.exports&&(t=e.exports=Y),t.UAParser=Y):r.amdO?o!==(n=(function(){return Y}).call(t,r,t,e))&&(e.exports=n):typeof i!==s&&(i.UAParser=Y);var K=typeof i!==s&&(i.jQuery||i.Zepto);if(K&&!K.ua){var J=new Y;K.ua=J.getResult(),K.ua.get=function(){return J.getUA()},K.ua.set=function(e){J.setUA(e);var t=J.getResult();for(var r in t)K.ua[r]=t[r]}}}("object"==typeof window?window:this)}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var o=t[n]={id:n,loaded:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}r.m=e,r.amdO={},(()=>{var e="function"==typeof Symbol?Symbol("webpack queues"):"__webpack_queues__",t="function"==typeof Symbol?Symbol("webpack exports"):"__webpack_exports__",n="function"==typeof Symbol?Symbol("webpack error"):"__webpack_error__",i=e=>{e&&e.d<1&&(e.d=1,e.forEach(e=>e.r--),e.forEach(e=>e.r--?e.r++:e()))},o=r=>r.map(r=>{if(null!==r&&"object"==typeof r){if(r[e])return r;if(r.then){var o=[];o.d=0,r.then(e=>{a[t]=e,i(o)},e=>{a[n]=e,i(o)});var a={};return a[e]=e=>e(o),a}}var s={};return s[e]=e=>{},s[t]=r,s});r.a=(r,a,s)=>{s&&((l=[]).d=-1);var l,c,u,d,f=new Set,p=r.exports,h=new Promise((e,t)=>{d=t,u=e});h[t]=p,h[e]=e=>(l&&e(l),f.forEach(e),h.catch(e=>{})),r.exports=h,a(r=>{c=o(r);var i,a=()=>c.map(e=>{if(e[n])throw e[n];return e[t]}),s=new Promise(t=>{(i=()=>t(a)).r=0;var r=e=>e!==l&&!f.has(e)&&(f.add(e),e&&!e.d&&(i.r++,e.push(i)));c.map(t=>t[e](r))});return i.r?s:a()},e=>(e?d(h[n]=e):u(p),i(l))),l&&l.d<0&&(l.d=0)}})(),(()=>{var e=[];r.O=(t,n,i,o)=>{if(n){o=o||0;for(var a=e.length;a>0&&e[a-1][2]>o;a--)e[a]=e[a-1];e[a]=[n,i,o];return}for(var s=1/0,a=0;a<e.length;a++){for(var[n,i,o]=e[a],l=!0,c=0;c<n.length;c++)s>=o&&Object.keys(r.O).every(e=>r.O[e](n[c]))?n.splice(c--,1):(l=!1,o<s&&(s=o));if(l){e.splice(a--,1);var u=i();void 0!==u&&(t=u)}}return t}})(),r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;r.t=function(n,i){if(1&i&&(n=this(n)),8&i||"object"==typeof n&&n&&(4&i&&n.__esModule||16&i&&"function"==typeof n.then))return n;var o=Object.create(null);r.r(o);var a={};e=e||[null,t({}),t([]),t(t)];for(var s=2&i&&n;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach(e=>a[e]=()=>n[e]);return a.default=()=>n,r.d(o,a),o}})(),r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.f={},r.e=e=>Promise.all(Object.keys(r.f).reduce((t,n)=>(r.f[n](e,t),t),[])),r.u=e=>""+e+".js",r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="worksmail-fe:";r.l=(n,i,o,a)=>{if(e[n]){e[n].push(i);return}if(void 0!==o)for(var s,l,c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==n||d.getAttribute("data-webpack")==t+o){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,r.nc&&s.setAttribute("nonce",r.nc),s.setAttribute("data-webpack",t+o),s.src=n),e[n]=[i];var f=(t,r)=>{s.onerror=s.onload=null,clearTimeout(p);var i=e[n];if(delete e[n],s.parentNode&&s.parentNode.removeChild(s),i&&i.forEach(e=>e(r)),t)return t(r)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=f.bind(null,s.onerror),s.onload=f.bind(null,s.onload),l&&document.head.appendChild(s)}})(),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),r.j=3057,r.p="https://static.worksmobile.net/service/mail/4-2-0-148-5e68036/",(()=>{r.b=document.baseURI||self.location.href;var e={3057:0,5588:0};r.f.j=(t,n)=>{var i=r.o(e,t)?e[t]:void 0;if(0!==i){if(i)n.push(i[2]);else{var o=new Promise((r,n)=>i=e[t]=[r,n]);n.push(i[2]=o);var a=r.p+r.u(t),s=Error();r.l(a,n=>{if(r.o(e,t)&&(0!==(i=e[t])&&(e[t]=void 0),i)){var o=n&&("load"===n.type?"missing":n.type),a=n&&n.target&&n.target.src;s.message="Loading chunk "+t+" failed.\n("+o+": "+a+")",s.name="ChunkLoadError",s.type=o,s.request=a,i[1](s)}},"chunk-"+t,t)}}},r.O.j=t=>0===e[t];var t=(t,n)=>{var i,o,[a,s,l]=n,c=0;if(a.some(t=>0!==e[t])){for(i in s)r.o(s,i)&&(r.m[i]=s[i]);if(l)var u=l(r)}for(t&&t(n);c<a.length;c++)o=a[c],r.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return r.O(u)},n=self.mailFunc=self.mailFunc||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var n=r.O(void 0,[4121],()=>r(13261));n=r.O(n)})();
//# sourceMappingURL=index.js.map