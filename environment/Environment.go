package environment

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

/*
	프로퍼티는 대문자로 시작해야 함.
*/
type Environment struct {
	Id         primitive.ObjectID `bson:"_id"` // 요걸 해줘야 _id를 제대로 가져옴
	Name       string             `bson:"name"`
	Value      string             `bson:"value"`
	CreatedAt  time.Time          `bson:"createdAt"` // 요걸 해줘야 시간을 제대로 가져옴
	ModifiedAt time.Time          `bson:"modifiedAt"`
}
