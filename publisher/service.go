package publisher

import (
	"context"
	"github.com/araddon/dateparse"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readconcern"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	"oss.navercorp.com/da-ssp/gfp-api/database"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
	"time"
)

/*
	Publisher
*/
type Publisher struct {
	Id                primitive.ObjectID `json:"publisherId" bson:"_id"`
	Name              string             `json:"name" bson:"name"`
	Status            string             `json:"status" bson:"status"`
	Description       string             `json:"description" bson:"description"`
	CorporationType   string             `json:"corporationType" bson:"corporationType"`
	FreeformKeyValues []FreeformKeyValue `json:"freeformKeyValues" bson:"freeformKeyValues"`
	CreatedAt         time.Time          `json:"createdAt" bson:"createdAt"`
	ModifiedAt        time.Time          `json:"modifiedAt" bson:"modifiedAt"`
}

/*
	freeformKeyValues
*/
type FreeformKeyValue struct {
	Key  string `json:"key" bson:"key"`
	Name string `json:"name" bson:"name"`
}

type PublisherService struct{}

/*
	List 퍼블리셔 리스트 조회
*/
func (service *PublisherService) List(ginCtx *gin.Context, publisherParam PublisherParam) (list []Publisher, err error) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[List] 퍼블리셔 리스트 조회")

	// 1. 쿼리 스트링 추출
	publisherId := publisherParam.PublisherId
	status := publisherParam.Status
	since := publisherParam.Since
	publisherIds := publisherParam.PublisherIds

	// 2. DB 파이프라인 설정
	matchQuery := bson.M{}

	if publisherId != "" {
		_id, _ := primitive.ObjectIDFromHex(publisherId)
		matchQuery["_id"] = _id
	} else if len(publisherIds) > 0 {
		matchQuery["_id"] = bson.M{"$in": publisherIds}
	}

	if since != "" {
		modifiedAt, _ := dateparse.ParseAny(since)

		matchQuery["modifiedAt"] = bson.M{"$gte": modifiedAt}
	}

	matchQuery["status"] = primitive.Regex{Pattern: ".*" + status + ".*", Options: "i"}

	pipeline := mongo.Pipeline{
		{{"$match", matchQuery}},
		{{"$project", bson.M{
			"_id":               1,
			"name":              1,
			"description":       1,
			"corporationType":   1,
			"status":            1,
			"freeformKeyValues": 1,
			"createdAt":         1,
			"modifiedAt":        1},
		}},
		{{"$sort", bson.M{"_id": 1}}},
	}

	// 3. Aggregation 옵션 설정
	opts := options.Aggregate()
	opts.SetAllowDiskUse(true)

	// 4. timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// 5. Aggregation 실행
	collOptions := &options.CollectionOptions{}
	collOptions.SetReadConcern(readconcern.Majority())
	collOptions.SetReadPreference(readpref.SecondaryPreferred(readpref.WithMaxStaleness(time.Second * 90)))

	collection := database.GFP.Collection("Publishers", collOptions)
	cur, err := collection.Aggregate(ctx, pipeline, opts)
	if err != nil {
		entry.Error(err)
		return
	}
	defer cur.Close(ctx)

	// 6. 조회 결과 가져오기
	for cur.Next(ctx) {
		var elem Publisher

		err = cur.Decode(&elem)
		if err != nil {
			entry.Error(err)
			return
		}

		list = append(list, elem)
	}

	if err = cur.Err(); err != nil {
		entry.Error(err)
	}

	return
}

/*
	AdpIds와 연관된 publisherIds 조회
*/
func (service *PublisherService) GetPublisherIdsByAdpIds(ginCtx *gin.Context, adproviderIds []primitive.ObjectID) (publisherIds []primitive.ObjectID, err error) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[GetPublisherIdsByAdpIds] AdpIds와 연관된 publisherIds 조회")

	// 1. DB 파이프라인 설정
	matchQuery := bson.M{"adProvider_id": bson.M{"$in": adproviderIds}}

	pipeline := mongo.Pipeline{
		{{"$match", matchQuery}},
		{{
			"$project", bson.M{
				"_id":          0,
				"publisher_id": 1,
			},
		}},
		{{
			"$group", bson.M{
				"_id":           nil,
				"publisher_ids": bson.M{"$addToSet": "$publisher_id"},
			},
		}},
		{{
			"$project", bson.M{
				"_id":           0,
				"publisher_ids": 1,
			},
		}},
	}

	// 2. Aggregation 옵션 설정
	opts := options.Aggregate()
	opts.SetAllowDiskUse(true)

	// 3. timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// 4. Aggregation 실행
	collOptions := &options.CollectionOptions{}
	collOptions.SetReadConcern(readconcern.Majority())
	collOptions.SetReadPreference(readpref.SecondaryPreferred(readpref.WithMaxStaleness(time.Second * 90)))

	collection := database.GFP.Collection("AdProviderInfos", collOptions)
	cur, err := collection.Aggregate(ctx, pipeline, opts)
	if err != nil {
		entry.Error(err)
		return
	}
	defer cur.Close(ctx)

	// 5. 조회 결과 가져오기
	var elem PublisherParam
	for cur.Next(ctx) {
		err = cur.Decode(&elem)
		if err != nil {
			entry.Error(err)
			return
		}

		// entry.Debugf("%+v", elem)
	}

	if err = cur.Err(); err != nil {
		entry.Error(err)
		return
	}

	publisherIds = elem.PublisherIds

	return
}
