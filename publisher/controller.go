package publisher

import (
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"net/http"

	gfpError "oss.navercorp.com/da-ssp/gfp-api/error"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
	"oss.navercorp.com/da-ssp/gfp-api/triplea"
)

type PublisherParam struct {
	PublisherId string `form:"publisherId" binding:"omitempty,validateObjectId"`
	Status      string `form:"status" binding:"omitempty,eq=ON|eq=OFF|eq=on|eq=off"`
	Since       string `form:"since" binding:"omitempty,validateDateFormat"`

	PublisherIds []primitive.ObjectID `bson:"publisher_ids"`
}

type PublisherController struct {
	publisherService PublisherService
}

var empty = make([]interface{}, 0)

/**
 	@apiVersion 1.0.0
	@api {get} /api/publisher/list Publisher 목록 조회
	@apiName List
	@apiGroup Publisher
	@apiDescription Publisher 목록 조회

	@apiParam {String} userId API User ID<br/>ex) "admin"
	@apiParam {String{64}} encodedUserId Encoded API User ID<br/>ex) "1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1"
	@apiParam {String} publisherId Publisher Id
	@apiParam {String=="ON","OFF"} status 상태
	@apiParam {String} since 시작일자<br/>ex) 2019-08-01 01:02:03 또는 2019-08-01T01:02:03.000Z

	@apiParamExample {form} Request-Example:
		http://test-api-gfp.da.navercorp.com:8080/api/publisher/list?userId=admin&encodedUserId=1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1&publisherId=5bd01269166015001fa8888d&status=ON&since=2019-08-01T01:02:03.000Z

	@apiSuccess {json} array
	@apiSuccessExample {json} Success-Response-Example: publisher 목록
		HTTP/1.1 200 OK
		[
			{
				"publisherId": "5b74d94bc36eef272090ca56",
				"name": "네이버서비스",
				"status": "ON",
				"description": "네이버는 전 세계 사람들이 사용하는 ...",
				"corporationType": "NAVER",
				"freeformKeyValues": [
					{
						"key": "calp",
						"name": "calp"
					},
					{
						"key": "blogId",
						"name": "BlogId"
					}
				],
				"createdAt": "2018-07-12T05:46:44.544Z",
				"modifiedAt": "2018-10-02T08:41:17.999Z"
			},
			...
		]

	@apiError (400) BadRequest
						PublisherId가 ObjectId 형식이 아닌 경우<br>
						Status가 ON 또는 OFF 가 아닌 경우<br>
						Since가 날짜 형식이 아닌 경우<br>
	@apiError (403) Forbidden Not authenticated. You need to register server IP in the pasta acl consumer.
	@apiError (500) InternalServerError
						DB 조회 오류<br/>

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"Status": 400,
			"Code": "2011",
			"Message": "invalid param. (Key: 'PublisherParam.PublisherId' Error:Field validation for 'PublisherId' failed on the 'validateObjectId' tag)"
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		{
			"Status": 500,
			"Code": "1000",
			"Message": "runtime error: invalid memory address or nil pointer dereference"
		}

	@apiSampleRequest /api/publisher/list
*/
func (controller *PublisherController) List(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[List] 퍼블리셔 리스트 조회")

	// 쿼리스트링 바인딩 및 밸리데이션 체크
	var publisherParam PublisherParam
	if err := ginCtx.ShouldBindWith(&publisherParam, binding.Query); err != nil {
		param := map[string]string{"params": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.InValidParam.BizError(param))
		return
	}

	publishers, err := controller.publisherService.List(ginCtx, publisherParam)
	if err != nil {
		param := map[string]string{"err": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.PublisherCommon.BizError(param))
		return
	}

	if len(publishers) == 0 {
		ginCtx.IndentedJSON(http.StatusOK, empty)
		return
	}

	ginCtx.IndentedJSON(http.StatusOK, publishers)
}

/**
 	@apiVersion 1.0.0
	@api {get} /api/publisher/list/mine 연관된 Publisher 목록 조회
	@apiName ListMine
	@apiGroup Publisher
	@apiDescription 연관된 Publisher 목록 조회

	@apiParam {String} userId API User ID<br/>ex) "admin"
	@apiParam {String{64}} encodedUserId Encoded API User ID<br/>ex) "1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1"

	@apiParam {String} publisherId Publisher Id
	@apiParam {String=="ON","OFF"} status 상태
	@apiParam {String} since 시작일자<br/>ex) 2019-08-01 01:02:03 또는 2019-08-01T01:02:03.000Z

	@apiParamExample {form} Request-Example:
		http://test-api-gfp.da.navercorp.com:8080/api/publisher/list/mine?userId=admin&encodedUserId=1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1&publisherId=5bd01269166015001fa8888d&status=ON&since=2019-08-01T01:02:03.000Z

	@apiSuccess {json} array
	@apiSuccessExample {json} Success-Response-Example: 연관된 Publisher 목록
		HTTP/1.1 200 OK
		[
			{
				"publisherId": "5b74d94bc36eef272090ca56",
				"name": "네이버서비스",
				"status": "ON",
				"description": "네이버는 전 세계 사람들이 사용하는 ...",
				"corporationType": "NAVER",
				"freeformKeyValues": [
					{
						"key": "calp",
						"name": "calp"
					},
					{
						"key": "blogId",
						"name": "BlogId"
					}
				],
				"createdAt": "2018-07-12T05:46:44.544Z",
				"modifiedAt": "2018-10-02T08:41:17.999Z"
			},
			...
		]

	@apiError (400) BadRequest
						PublisherId가 ObjectId 형식이 아닌 경우<br>
						Status가 ON 또는 OFF 가 아닌 경우<br>
						Since가 날짜 형식이 아닌 경우<br>
	@apiError (403) Forbidden Not authenticated. You need to register server IP in the pasta acl consumer.
	@apiError (500) InternalServerError
						DB 조회 오류<br/>

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"Status": 400,
			"Code": "2011",
			"Message": "invalid param. (Key: 'PublisherParam.PublisherId' Error:Field validation for 'PublisherId' failed on the 'validateObjectId' tag)"
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		{
			"Status": 500,
			"Code": "1000",
			"Message": "runtime error: invalid memory address or nil pointer dereference"
		}

	@apiSampleRequest /api/publisher/list/mine
*/
func (controller *PublisherController) ListMine(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[ListMine] 연관된 Publisher 리스트 조회")

	// 쿼리스트링 바인딩 및 밸리데이션 체크
	var publisherParam PublisherParam
	if err := ginCtx.ShouldBindWith(&publisherParam, binding.Query); err != nil {
		param := map[string]string{"params": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.InValidParam.BizError(param))
		return
	}

	// apiUser 정보 가져오기
	userId := ginCtx.Query("userId")
	apiUser, err := triplea.GetApiUser(ginCtx, userId)
	if err != nil {
		param := map[string]string{"err": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.PublisherCommon.BizError(param))
		return
	}

	// entry.Debugf("apiUser= %+v\n", apiUser)

	// AdpIds와 연관된 publisherIds 조회
	publisherIds, err := controller.publisherService.GetPublisherIdsByAdpIds(ginCtx, apiUser.AdpIds)
	if err != nil {
		param := map[string]string{"err": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.PublisherCommon.BizError(param))
		return
	}
	publisherParam.PublisherIds = append(publisherParam.PublisherIds, publisherIds...)

	// PubIds 추가
	publisherParam.PublisherIds = append(publisherParam.PublisherIds, apiUser.PubIds...)

	// 조회하려는 퍼블리셔에 대한 권한이 없는 경우, empty 응답
	if publisherParam.PublisherId != "" {
		isValid := false
		for _, id := range publisherParam.PublisherIds {
			if publisherParam.PublisherId == id.Hex() {
				isValid = true
			}
		}

		if isValid != true {
			ginCtx.IndentedJSON(http.StatusOK, empty)
			return
		}
	}

	publishers, err := controller.publisherService.List(ginCtx, publisherParam)
	if err != nil {
		param := map[string]string{"err": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.PublisherCommon.BizError(param))
		return
	}

	ginCtx.IndentedJSON(http.StatusOK, publishers)
}
