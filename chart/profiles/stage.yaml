global:
  # enable n2c nat-ip init-container
  profile: real

airflow:
  dags:
    path: /opt/airflow/dags
    # DAG_FOLDER = /opt/airflow/dags/repo/{{ gitSync.repoSubPath }}
    gitSync:
      branch: main
      resources:
        limits:
          memory: "2Gi"
      syncWait: "30"
  airflow:
    config:
      AIRFLOW__CORE__SQL_ALCHEMY_POOL_SIZE: "10"
      AIRFLOW__CORE__SQL_ALCHEMY_MAX_OVERFLOW: "100"
      AIRFLOW__CORE__PARALLELISM: "128"
      AIRFLOW__CORE__MAX_ACTIVE_TASKS_PER_DAG: "128"
      AIRFLOW__CORE__DEFAULT_POOL_TASK_SLOT_COUNT: "128"
      AIRFLOW__CELERY__WORKER_AUTOSCALE: "16,4"
      AIRFLOW__CELERY_BROKER_TRANSPORT_OPTIONS__VISIBILITY_TIMEOUT: "28800"
      AIRFLOW__WEBSERVER__BASE_URL: "http://stg-airflow-web.nam-batch.svc.cr1.io.navercorp.com:8080"
      AIRFLOW__WEBSERVER__INSTANCE_NAME: "DAGs (NAM Stage)"
      AIRFLOW__SMTP__SMTP_MAIL_FROM: <EMAIL>
      AIRFLOW__CORE__PLUGINS_FOLDER: "/opt/airflow/dags/repo/plugins"
      AIRFLOW__EMAIL__SUBJECT_TEMPLATE: "/opt/airflow/dags/repo/dags/core/email/email_alert_subject_template.txt"
      AIRFLOW__EMAIL__HTML_CONTENT_TEMPLATE: "/opt/airflow/dags/repo/dags/core/email/email_alert_content_template.html"
      AIRFLOW__SCHEDULER__SCHEDULER_ZOMBIE_TASK_THRESHOLD: "1200"
      AIRFLOW__CORE__SECURITY: "kerberos"
      AIRFLOW__KERBEROS__PRINCIPAL: "gfp-data"
      AIRFLOW__KERBEROS__KEYTAB: "/opt/airflow/c3s/gfp-data.keytab"
      AIRFLOW__KERBEROS__CCACHE: "/tmp/krb5cc_50000"
      AIRFLOW__KERBEROS__INCLUDE_IP: "False"
      PYTHONPATH: "/opt/airflow/dags/repo/config"
      DEPLOY_DATETIME: "20220606"
    dbMigrations:
      # Enable if deploy initially since airflow has been upgraded
      enabled: true
  workers:
    replicas: 4
    resources:
      limits:
        cpu: 4
        memory: "8Gi"
  externalRedis:
    databaseNumber: 2
  externalDatabase:
    user: airflow_stage
    database: airflow_stage
    passwordSecret: postgresql
    passwordSecretKey: postgresql-password

