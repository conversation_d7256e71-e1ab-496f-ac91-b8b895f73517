# profile for developments. Customize the following items
#
#   - Helm Release Name
#     - 'airflow' for 'test' profile
#     - 'airflow-dev' for 'dev' profile
#   - Prepare new nubes bucket for airflow logs if needed
#   - Create new postgres user/database, and register password with the postgresql secret
#   - Identify git branch for git-sync
#   - Values
#     - airflow.dags.gitSync.branch
#     - airflow.airflow.config.AIRFLOW__WEBSERVER__BASE_URL
#     - airflow.airflow.extraVolumes/extraVolumeMounts
#     - externalRedis.databaseNumber
#       - 1 for 'test' profile
#       - 5 for 'dev' profile
#       - ...
#     - externalDatabase
#       - user
#       - database
#       - passwordSecretKey
#
# Note:
#   - N2C Ceph volumes are dynamically extendable
#     - postgresql/redis PVs
#
airflow:
  dags:
    path: /opt/airflow/dags
    gitSync:
      branch: develop-c3
  airflow:
    config:
      AIRFLOW__CORE__SQL_ALCHEMY_POOL_SIZE: "10"
      AIRFLOW__CORE__SQL_ALCHEMY_MAX_OVERFLOW: "100"
      AIRFLOW__CORE__PARALLELISM: "128"
      AIRFLOW__CORE__MAX_ACTIVE_TASKS_PER_DAG: "128"
      AIRFLOW__CORE__DEFAULT_POOL_TASK_SLOT_COUNT: "128"
      AIRFLOW__CELERY__WORKER_AUTOSCALE: "16,4"
      AIRFLOW__CELERY_BROKER_TRANSPORT_OPTIONS__VISIBILITY_TIMEOUT: "28800"
      AIRFLOW__WEBSERVER__BASE_URL: "http://dev-airflow-web.nam-batch.svc.ad1.io.navercorp.com:8080"
      AIRFLOW__WEBSERVER__INSTANCE_NAME: "DAGs (NAM-dev)"
      AIRFLOW__SMTP__SMTP_MAIL_FROM: <EMAIL>
      AIRFLOW__CORE__PLUGINS_FOLDER: "/opt/airflow/dags/repo/plugins"
      AIRFLOW__EMAIL__SUBJECT_TEMPLATE: "/opt/airflow/dags/repo/dags/core/email/email_alert_subject_template.txt"
      AIRFLOW__EMAIL__HTML_CONTENT_TEMPLATE: "/opt/airflow/dags/repo/dags/core/email/email_alert_content_template.html"
      AIRFLOW__SCHEDULER__SCHEDULER_ZOMBIE_TASK_THRESHOLD: "1200"
      AIRFLOW__CORE__SECURITY: "kerberos"
      AIRFLOW__KERBEROS__PRINCIPAL: "gfp-data"
      AIRFLOW__KERBEROS__KEYTAB: "/opt/airflow/c3s/gfp-data.keytab"
      AIRFLOW__KERBEROS__CCACHE: "/tmp/krb5cc_50000"
      AIRFLOW__KERBEROS__INCLUDE_IP: "False"
      PYTHONPATH: "/opt/airflow/dags/repo/config"
      DEPLOY_DATETIME: "20220412"
#    dbMigrations:
#      # Enable if deploy initially since airflow has been upgraded
#      enabled: true
  workers:
    replicas: 4
  externalRedis:
    databaseNumber: 5
  externalDatabase:
    user: airflow_dev
    database: airflow_dev
    passwordSecret: postgresql
    passwordSecretKey: postgresql-airflow-dev-password
