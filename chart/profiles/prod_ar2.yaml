global:
  # enable n2c nat-ip init-container
  profile: real

ncc:
  application: airflow
  instance: nam-api

airflow:
  dags:
    path: /opt/airflow/dags
    # DAG_FOLDER = /opt/airflow/dags/repo/{{ gitSync.repoSubPath }}
    gitSync:
      branch: main
      resources:
        limits:
          memory: "2Gi"
  airflow:
    config:
      AIRFLOW__CORE__SQL_ALCHEMY_POOL_SIZE: "10"
      AIRFLOW__CORE__SQL_ALCHEMY_MAX_OVERFLOW: "100"
      AIRFLOW__CORE__PARALLELISM: "128"
      AIRFLOW__CORE__MAX_ACTIVE_TASKS_PER_DAG: "128"
      AIRFLOW__CORE__DEFAULT_POOL_TASK_SLOT_COUNT: "128"
      AIRFLOW__CELERY__WORKER_AUTOSCALE: "32,4"
      AIRFLOW__CELERY_BROKER_TRANSPORT_OPTIONS__VISIBILITY_TIMEOUT: "28800"
      AIRFLOW__WEBSERVER__BASE_URL: "http://airflow-web.nam-api.svc.ar2.io.navercorp.com:8080"
      AIRFLOW__WEBSERVER__INSTANCE_NAME: "DAGs (NAM)"
      AIRFLOW__SMTP__SMTP_MAIL_FROM: <EMAIL>
      AIRFLOW__CORE__PLUGINS_FOLDER: "/opt/airflow/dags/repo/plugins"
      AIRFLOW__EMAIL__SUBJECT_TEMPLATE: "/opt/airflow/dags/repo/dags/core/email/email_alert_subject_template.txt"
      AIRFLOW__EMAIL__HTML_CONTENT_TEMPLATE: "/opt/airflow/dags/repo/dags/core/email/email_alert_content_template.html"
#      AIRFLOW__SCHEDULER__SCHEDULER_ZOMBIE_TASK_THRESHOLD: "1200"
      PYTHONPATH: "/opt/airflow/dags/repo/config"
      DEPLOY_DATETIME: "20250523"
  web:
    replicas: 1
  scheduler:
    replicas: 1
    resources:
      limits:
        cpu: 4
        ephemeral-storage: "16Gi"
  workers:
    replicas: 4
    resources:
      limits:
        cpu: 6
        memory: "10Gi"

