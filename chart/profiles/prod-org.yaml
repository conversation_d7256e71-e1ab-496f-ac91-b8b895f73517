airflow:
  dags:
    path: /opt/airflow/dags
    # DAG_FOLDER = /opt/airflow/dags/repo/{{ gitSync.repoSubPath }}
    gitSync:
      branch: master
      resources:
        limits:
          memory: "1500Mi"
  airflow:
    config:
      AIRFLOW__CORE__SQL_ALCHEMY_POOL_SIZE: "20"
      AIRFLOW__CORE__SQL_ALCHEMY_MAX_OVERFLOW: "150"
      AIRFLOW__CORE__PARALLELISM: "512"
      AIRFLOW__CORE__MAX_ACTIVE_TASKS_PER_DAG: "128"
      AIRFLOW__CORE__DEFAULT_POOL_TASK_SLOT_COUNT: "128"
      AIRFLOW__CELERY__WORKER_AUTOSCALE: "32,8"
      AIRFLOW__CELERY_BROKER_TRANSPORT_OPTIONS__VISIBILITY_TIMEOUT: "28800"
      AIRFLOW__WEBSERVER__BASE_URL: "http://airflow-web.nam-batch.svc.ar2.io.navercorp.com:8080"
      AIRFLOW__WEBSERVER__INSTANCE_NAME: "DAGs (NAM)"
      AIRFLOW__SMTP__SMTP_MAIL_FROM: <EMAIL>
      AIRFLOW__CORE__PLUGINS_FOLDER: "/opt/airflow/dags/repo/plugins"
      PYTHONPATH: "/opt/airflow/dags/repo/config"
      DEPLOY_DATETIME: "20220412"
  web:
    replicas: 1
  scheduler:
    replicas: 1
    resources:
      limits:
        cpu: 4
        memory: "10Gi"
    extraVolumes:
      - name: logs
        flexVolume:
          driver: "naver/nubes-fuse-driver"
          options:
            bucket: "airflow-ssp-batch-logs"
            clearCache: "false"
    extraVolumeMounts:
      - name: logs
        mountPath: /opt/airflow/logs
  workers:
    replicas: 8
    resources:
      limits:
        cpu: 6
        memory: "12Gi"
    extraVolumes:
      - name: logs
        flexVolume:
          driver: "naver/nubes-fuse-driver"
          options:
            bucket: "airflow-ssp-batch-logs"
            clearCache: "false"
    extraVolumeMounts:
      - name: logs
        mountPath: /opt/airflow/logs

