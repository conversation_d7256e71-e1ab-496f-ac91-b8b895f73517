{{- $defaultName := include "defaultName" . | printf "%s-git-sync" }}
{{- $labels := include "defaultLabels" . }}
{{- $imageTag := printf "%s:%s" .Values.gitSync.image.name .Values.gitSync.image.tag }}
{{- if .Values.gitSync.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $defaultName }}
spec:
  replicas: 1
  selector:
    matchLabels:
      {{- $labels | nindent 6 }}
      component: git-sync
  template:
    metadata:
      labels:
        {{- $labels | nindent 8 }}
        component: git-sync
    spec:
      containers:
      - name: git-sync
        image: {{ $imageTag }}
        imagePullPolicy: Always
        env:
        - name: GIT_SYNC_REPO
          value: {{ .Values.gitSync.repo.url }}
        {{- with .Values.gitSync.repo.branch }}
        - name: GIT_SYNC_BRANCH
          value: {{ . }}
        {{- end }}
        {{- with .Values.gitSync.repo.rev }}
        - name: GIT_SYNC_REV
          value: {{ . }}
        {{- end }}
        - name: GIT_SYNC_SUBMODULES
          value: "off"
        - name: GIT_SYNC_ROOT
          value: /airflow-dags
        - name: GIT_SYNC_WAIT
          value: "15"
        - name: GIT_SYNC_SSH
          value: "true"
        - name: GIT_SSH_KEY_FILE
          value: /etc/git-sync-ssh/{{ .Values.gitSync.repo.ssh.secretKey }}
        - name: GIT_KNOWN_HOSTS
          value: "false"
        volumeMounts:
          - name: ssh-key
            mountPath: /etc/git-sync-ssh
          - name: dags
            mountPath: /airflow-dags
      volumes:
        - name: ssh-key
          secret:
            secretName: {{ .Values.gitSync.repo.ssh.secretName }}
        - name: dags
          {{- .Values.gitSync.dagVolume | toYaml | nindent 10 }}
---
{{- end }}
{{- if .Values.redis.enabled }}
kind: Service
apiVersion: v1
metadata:
  name: {{ .Values.redis.name }}
  labels:
    {{- $labels | nindent 4 }}
    component: redis
spec:
  type: ClusterIP
  selector:
    {{- $labels | nindent 4 }}
    component: redis
  ports:
    - name: redis-db
      protocol: TCP
      port: {{ .Values.redis.port }}
      targetPort: {{ .Values.redis.port }}
---
kind: StatefulSet
apiVersion: apps/v1
metadata:
  name: {{ .Values.redis.name }}
  labels:
    {{- $labels | nindent 4 }}
    component: redis
spec:
  serviceName: {{ .Values.redis.name }}
  selector:
    matchLabels:
      {{- $labels | nindent 6 }}
      component: redis
  template:
    metadata:
      labels:
        {{- $labels | nindent 8 }}
        component: redis
    spec:
      containers:
        - name: redis
          image: "{{ .Values.redis.image.name }}:{{ .Values.redis.image.tag }}"
          command: ["/bin/sh"]
          args: ["-c", "redis-server --requirepass ${REDIS_PASSWORD}"]
          resources:
{{ toYaml .Values.redis.resources | indent 12 }}
          ports:
            - name: redis-db
              containerPort: {{ .Values.redis.port }}
          volumeMounts:
            - name: redis-db
              mountPath: /data
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.redis.passwordSecret }}
                  key: {{ .Values.redis.passwordSecretKey }}
{{- if not .Values.redis.persistence.enabled }}
      volumes:
        - name: redis-db
          emptyDir: {}
{{- else }}
  volumeClaimTemplates:
    - metadata:
        name: redis-db
      spec:
      {{- if .Values.redis.persistence.storageClassName }}
        storageClassName: {{ .Values.redis.persistence.storageClassName }}
      {{- end }}
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: {{ .Values.redis.persistence.size }}
{{- end }}
{{- end }}

