#!/bin/bash
# vim: ft=bash:

# Usage:
#   $ c3s_krb5_conf=/path/to/c3s-krb5.conf \
#   > account_name=gfp-data \
#   > account_keytab=/path/to/gfp-data-keytab-file \
#   > c3s-artifacts.sh
#
: ${c3s_krb5_conf?} ${account_name?} ${account_keytab?}

set -eux -o pipefail

kubectl create configmap c3s-krb5-conf --from-file=krb5.conf=${c3s_krb5_conf}

kubectl create secret generic c3s-${account_name}-keytab \
	--from-file=${account_name}.keytab=${account_keytab}
