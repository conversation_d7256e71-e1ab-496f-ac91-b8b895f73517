#!/bin/bash
# vim: ft=bash:

# required env variables
: ${redis_passwd?} ${admin_passwd?} ${admin_email?}

set -e

python_ver=$(python -V)

fernet_key=$(python -c "from cryptography.fernet import Fernet; FERNET_KEY = Fernet.generate_key().decode(); print(FERNET_KEY)")
webserver_key=$(python -c "import os; print(os.urandom(16).hex())")

echo python version: $python_ver
echo fernet key: $fernet_key
echo webserver key: $webserver_key
echo

cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Secret
metadata:
  name: airflow
type: Opaque
data:
  redis-password: $(echo -n "$redis_passwd" | base64 -w 0)
  admin-password: $(echo -n "$admin_passwd" | base64 -w 0)
  admin-email: $(echo -n "$admin_email" | base64 -w 0)
  fernet-key: $(echo -n "$fernet_key" | base64 -w 0)
  webserver-secret-key: $(echo -n "$webserver_key" | base64 -w 0)
EOF
