ncc:
  application: airflow
  instance: nam-batch

###
# Customizing values.yaml
# - Dry-run with minimal settings
# - Look at the output to understand what are being created
# - Repeat
#   - Change a little aspect of the chart by setting one or two items
#   - Inspect the dry-run output to see whether it's your intended results
###
# fernet key:
#   $ python -c "from cryptography.fernet import Fernet; FERNET_KEY = Fernet.generate_key().decode(); print(FERNET_KEY)"
#
# webserver secret key:
#   $ python -c "import os; print(os.urandom(16).hex())"

###
# configmap for c3s krb5.conf => c3s-krb5-conf
#   krb5.conf: <config file contents>
# secret for c3s account keytab => c3s-{account}-keytab
#   {account}.keytab: <encoded keytab contents>
#
# See scripts/c3s-artifacts.sh

global:
  profile: your-profile
  c3s:
    account: gfp-data

airflow:
  postgresql:
    enabled: false
  pgbouncer:
    safeToEvict: false
    enabled: false
  redis:
    enabled: false
  rbac:
    create: false
  serviceAccount:
    create: false
  dags:
    path: /opt/airflow/dags
    # DAG_FOLDER = /opt/airflow/dags/repo/{{ gitSync.repoSubPath }}
    gitSync:
      enabled: true
      image:
        repository: reg.navercorp.com/gfp/git-sync
        tag: "3.5.0"
      resources:
        limits:
          cpu: 1
          memory: "2Gi"
      repo: *********************:da-ssp/airflow-dags.git
      # branch: NEED_TO_SET
      # revision: HEAD
      repoSubPath: dags
      syncWait: "300"
      sshSecret: airflow-dags-deploy
      sshSecretKey: ssh-private
      sshKnownHosts: |-
        oss.navercorp.com ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC5UAqhcOM8+r//5Eg5hWShwZHMossfgHep+L4BxibeQ0G1d/Kxy9AYlJJJ7fgQ6zBLhDU9QEHdef2DbXgT/0R5vKCD5/m3rVpWeryWrwSVOfv0k/wumvU6drqckqArSdl36pjtL9niY/d5j9XY6Gj6xr+mL3/040oPEwqtKzNhbVlUHksjsTwvHSp+YXnWITonS54kfW4kWA1+KjZZfzYmwnpOJHjVpEOHB9j6G1sfjO7CQ5oIR8Mt/iCyFHkJ13Jisyw98Fzco9UF+fkpqzW/MEfHAspyuGSEq1WQpM2W0C5aWLokK83xLIMYGCznD9BtfjxWj+jAb7fTasZKK8af
        oss.navercorp.com ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBH1xS5QLzFUimZMEZ58l0yJodRpeK0TggB4OvctQmeeCZA6Mf36zmcDx17hpEj6XH7YV/fIKuNxq/VY5xUFIU/g=
        ************ ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC5UAqhcOM8+r//5Eg5hWShwZHMossfgHep+L4BxibeQ0G1d/Kxy9AYlJJJ7fgQ6zBLhDU9QEHdef2DbXgT/0R5vKCD5/m3rVpWeryWrwSVOfv0k/wumvU6drqckqArSdl36pjtL9niY/d5j9XY6Gj6xr+mL3/040oPEwqtKzNhbVlUHksjsTwvHSp+YXnWITonS54kfW4kWA1+KjZZfzYmwnpOJHjVpEOHB9j6G1sfjO7CQ5oIR8Mt/iCyFHkJ13Jisyw98Fzco9UF+fkpqzW/MEfHAspyuGSEq1WQpM2W0C5aWLokK83xLIMYGCznD9BtfjxWj+jAb7fTasZKK8af
        ************ ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNTYAAAAIbmlzdHAyNTYAAABBBH1xS5QLzFUimZMEZ58l0yJodRpeK0TggB4OvctQmeeCZA6Mf36zmcDx17hpEj6XH7YV/fIKuNxq/VY5xUFIU/g=
  airflow:
    image:
      repository: reg.navercorp.com/gfp/airflow-nam
      tag: "2.2.5-2023062910"
    executor: CeleryExecutor
    config:
      AIRFLOW__CORE__LOAD_EXAMPLES: "False"
      AIRFLOW__CORE__DEFAULT_TIMEZONE: "Asia/Seoul"
      # Check new files
      AIRFLOW__SCHEDULER__DAG_DIR_LIST_INTERVAL: "60"
      # Reflect updates of DAGs
      AIRFLOW__SCHEDULER__MIN_FILE_PROCESS_INTERVAL: "60"
      AIRFLOW__WEBSERVER__BASE_URL: "http://airflow-web.nam-batch.svc.ad1.io.navercorp.com:8080"
      AIRFLOW__WEBSERVER__DEFAULT_UI_TIMEZONE: Asia/Seoul
      AIRFLOW__SMTP__SMTP_HOST: backendsmtp.naver.com
      AIRFLOW__SMTP__SMTP_STARTTLS: "False"
      AIRFLOW__SMTP__SMTP_SSL: "False"
      AIRFLOW__SMTP__SMTP_TIMEOUT: "10"
      # Authorization: Basic Base64(username:password)
      # curl api-backend-url --user 'username:password'
      AIRFLOW__API__AUTH_BACKEND: "airflow.api.auth.backend.basic_auth"
      AIRFLOW__CELERY__WORKER_AUTOSCALE: "16,4"
    extraEnv:
      - name: AIRFLOW__CORE__FERNET_KEY
        valueFrom:
          secretKeyRef:
            name: airflow
            key: fernet-key
      - name: AIRFLOW__WEBSERVER__SECRET_KEY
        valueFrom:
          secretKeyRef:
            name: airflow
            key: webserver-secret-key
    extraPipPackages:
      - nanoid==2.0.0
      - requests-gssapi==1.2.3
      - krbticket==1.0.6
    users:
      - username: admin
        role: Admin
        password: ${ADMIN_PASSWORD}
        firstName: admin
        lastName: airflow
        email: ${ADMIN_EMAIL}
    usersTemplates:
      ADMIN_PASSWORD:
        kind: secret
        name: airflow
        key: admin-password
      ADMIN_EMAIL:
        kind: secret
        name: airflow
        key: admin-email
    usersUpdate: false
    connectionsUpdate: false
    variablesUpdate: false
    poolsUpdate: false
    dbMigrations:
      safeToEvict: false
      # Enable if deploy initially since airflow has been upgraded
      enabled: false
    sync:
      safeToEvict: false
    # N2C nubes volume ('flexVolume') forbids pod 'securityContext.fsGroup' settings.
    #
    # Setting 'defaultSecurityContext' to null is an attempt to remove pod 'securitySeconxt.fsGroup',
    # but its doesn't work.
    #
    # On helm3 CLI, use '--set airflow.airflow.defaultSecurityContext=null'
    defaultSecurityContext: null
  scheduler:
    safeToEvict: false
    replicas: 1
    resources:
      limits:
        cpu: 2
        memory: "4Gi"
  flower:
    safeToEvict: false
    resources:
      limits:
        cpu: 2
        memory: "4Gi"
  web:
    safeToEvict: false
    replicas: 1
    resources:
      limits:
        cpu: 2
        memory: "4Gi"
    service:
      type: LoadBalancer
      externalPort: 8080
  workers:
    safeToEvict: false
    replicas: 2
    resources:
      limits:
        cpu: 4
        memory: "8Gi"
    celery:
      gracefullTermination: true
      gracefullTerminationPeriod: 120
    terminationPeriod: 60
    autoscaling:
      enabled: false
      maxReplicas: 8
      metrics: []
  externalRedis:
    host: redis
    port: 6379
    databaseNumber: 1
    passwordSecret: redis
    passwordSecretKey: password
  externalDatabase:
    type: postgres
    host: postgresql
    user: airflow
    database: airflow
    passwordSecret: postgresql
    passwordSecretKey: postgresql-password

gitSync:
  enabled: false
  image:
    name: reg.navercorp.com/gfp/git-sync
    tag: "3.5.0"
  repo:
    url: *********************:da-ssp/airflow-dags
    branch: master
    # kubectl create secret generic airflow-dags-deploy --from-file=ssh-private=/path/to/.ssh/id_rsa
    ssh:
      secretName: airflow-dags-deploy
      secretKey: ssh-private
  dagVolume:
    flexVolume:
      driver: "naver/nubes-fuse-driver"
      options:
        bucket: "nam-batch-airflow-dags"
        # region: "pyeongchon"
        # uid: "65533" required, 65533 is uid of 'git-sync' user
        uid: "65533"
        clearCache: "true"
  extra:
  podExtra:

redis:
  enabled: false
  name: airflow-redis
  image:
    name: reg.navercorp.com/gfp/redis
    tag: "6.2-p1"
  resources:
    limits:
      cpu: 1
      memory: "2Gi"
  port: 6379
  passwordSecret: airflow
  passwordSecretKey: redis-password
  persistence:
    enabled: true
    storageClassName: rbd-hdd
    size: 10Gi

