# nam-batch n2c deployments
## 개요
NAM batch components
- airflow with postgres
- sparkling-s (spark applications)

airflow 에서 spark-submit 호출을 위해, sparkling-s 을 serving 하는 k8s service 을 만든다.
- sparkling-s service
  - airflow 에서 submit 요청을 받아
  - sparkling-s app 을 submit 하고, 구동 결과를 되돌린다.
- airflow tasks
  - sparkling-s 에 spark submit 요청 및 오류/retry 처리
  - spark history server 을 통해 spark job monitoring

airflow 에서는 이외에도 NAM 관련 다양한 batch job 을 수행
- CMS collection sync to NAM API DB
- AP report fetch
- etc...

## 사전 준비
먼저 Linux 개발 서버을 준비합니다. NCloud VM 이면 충분합니다.

- n2c 배포판을 설치합니다. (kubectl 사용과 n2c cluster 접근을 위해)
- helm v3 cli 을 설치합니다.
  - airflow 같은 많은 package chart 가 helm v3 만 지원합니다.
  - n2c helm 은 v2 이며, n2c 는 공식적으로 helm v3 을 지원하지 않는다고 밝혔습니다.

아래 문서에서 `helm3` 라는 실행파일은 여러분이 설치한 helm v3 cli 에 대한 alias 입니다.

## Airflow: Docker image 빌드
- debian 기반 airflow 2.2.4 (https://github.com/apache/airflow) 공식 Dockerfile 을 ubuntu 기반 GFP python docker image 기반으로 build 하기 위한 변경 사항 적용
  - docker/base/python-3.8/Dockerfile -> reg.navercorp.com/gfp/base/python:3.8
- airflow extra packages 선별
- docker/airflow-gfp/build.sh 참조
```bash
$ cd docker/airflow-gfp
$ ls
airflow-2.2.4-patch.txt  build.sh
$ ./build.sh
...
$ docker push reg.navercorp.com/gfp/airflow-gfp:2.2.4
...
```

## Airflow: Postgresql 배포
```bash
$ cd charts/postgresql
$ n2c cluster set nam-batch@ad1
...
$ cat <<EOF | kubectl apply -f -
> apiVersion: v1
> data:
>   postgresql-password: blah
>   postgresql-postgres-password: blah
> kind: Secret
> metadata:
>   name: postgresql
> type: Opaque
> EOF
...
$ cat <<EOF > nam-batch.yaml
> postgresql:
>   fullnameOverride: postgresql
>   existingSecret: postgresql
>   postgresqlUsername: airflow
>   postgresqlDatabase: airflow
> EOF
$ helm3 upgrade -i -f profiles/dev.yaml -f nam-batch.yaml --debug --dry-run postgresql .
... <confirm output>
$ helm3 upgrade -i -f profiles/dev.yaml -f nam-batch.yaml postgresql .
...
$ helm3 ls && helm3 history postgresql
...
$ # psql: postgres client tool
$ # DB: airflow, User: airflow
$ psql -h postgresql-headless.nam-batch.svc.ad1.navercorp.com airflow airflow
Password: ***
> CREATE TABLE MY_TEST (id int, name varchar);
...
> DROP TABLE MY_TEST;
...
```

## airflow-dags OSS repo 배포 키 생성
airflow 에서 DAG 파일에 접근하려면, git-sync 을 통해 OSS repo 을 내려받아 동기화하는 과정이 필요

`ssh-keygen` 으로 key pair 을 생성하고, public key 는 airflow-dags OSS repo 의 deploy key 로 등록, private 키는 k8s secret 으로 저장

```bash
$ ssh-keygen -t rsa -C 'airflow-dags@oss/da-ssp' -f dags-deploy-key
...
$ # dags-deploy-key.pub 을 airflow-dags repo 의 settings/deploy-keys 메뉴에 등록
$ #   - oss.navercorp.com/da-ssp/airflow-dags
$ kubectl create secret generic airflow-dags-deploy --from-file=ssh-private=./dags-deploy-key
...
```

k8s secret 은 git-sync 에서 DAG repo 을 sync 하기 위해 사용

## Airflow nubes volumes for dag repo sync and logs
N2C 에서 ReadWriteMany 가능한 volume 을 할당하는 방법은
- nubes 에서 bucket 할당
- n2c 에서 개별 Pod 에서 flexVolume 유형으로 bucket 을 mount
  - PVC 형태로는 지원하지 않는다.

```yaml
flexVolume:
  driver: "naver/nubes-fuse-driver"
  options:
    bucket: "nam-batch-airflow-dags"
    region: "pyeongchon"
    uid: "50000"
    clearCache: "false"
```

nubes volume 은 root 소유자의 permission 777 로 mount 되므로 read/write 에 제약이 없어서 보통은 `uid` option 이 중요하지 않다. 그러나, ownership 을 따지는 program 의 경우 문제가 될 수 있다.

- airflow 배포의 경우, git-sync container 실행시, `uid` 을 git-sync user (uid: "65533") 로 변경하지 않으면 제대로 동작하지 않는다. git 의 문제인지, ssh 문제인지 (********************* protocol) 확실하지 않다.

Nubes buckets
- 개발존
  - nam-batch-airflow-dags
    - airflow-dags repo sync 을 위한 bucket
  - nam-batch-airflow-logs
    - airflow logging
- 리얼
  - nam-batch-airflow-dags
    - airflow-dags repo sync 을 위한 bucket
    - airflow-ssp-batch-dags 재활용하려고 했으나, git-sync 동작에 오류를 일으켜 다시 할당 받은 것.
      - nubes volume 이 non-posix 한 부분이 문제였던 것으로 추측
  - airflow-ssp-batch-logs
    - airflow logging
    - 이전에 할당되었으나 용도가 없던 것 재활용

## Airflow 배포
airflow helm chart (community edition) 을 사용한다.

- helm v3 (사전 설치 필요, n2c helm 은 v2 입니다)
- https://artifacthub.io/packages/helm/airflow-helm/airflow (artifacthub)
- https://github.com/airflow-helm/charts (github)
- great documentation
- chart version
  - 8.5.3 사용
  - 8.6 에서 airflow 2.2 에서 추가된 triggerer 지원이 된다고 합니다. (github issue 에서 확인)

주요 customization 은 다음과 같다.

- N2C 에서 ReadWriteMany PV 을 지원하지 않으므로
  - git-sync 는 side-car 형태가 아니라, 별개의 deployment 로 배포
    - nubes 공유 volume 을 mount 하여 git clone 수행
    - 상기했듯이, nubes volume 을 git-sync uid 로 mount 해야 합니다. (`options.uid: "65533"`)
- airflow dependency chart 에 있는 S/W 는 별도로 배포됩니다.
  - postgresql: 상기 문단 참조
  - redis: airflow chart 내에서 별도의 deployment 로 함께 배포

N2C cluster 배포시 문제점

- Pod spec, `securityContext.fsGroup`
  - N2C/Nubes 문서에서는 ceph/nubes volume 을 mount 할 때, fsGroup 설정을 금지
  - airflow chart 는 어떻게 해도 fsGroup 을 설정함
  - values 파일에서 fsGroup 설정을 null 로 override 해서 제거하려고 시도해도, helm bug 로 실패
  - helm command line 에서 `--set` 으로 null 설정시 제거 가능
    - `scripts/deploy.sh` 참고

helm 을 사용한 배포시, `scripts/deploy.sh` 스크립트 사용 권고합니다.

```
$ cd charts/airflow-gfp
$ n2c cluster set nam-batch@ad1
...
$ # 최초 1회, airflow-gfp chart 에서 사용하는 secret 항목들을 등록합니다.
$ cat <<EOF | kubectl apply -f -
> apiVersion: v1
>   fernet-key: blah
>   webserver-secret-key: blah
>   redis-password: blah
>   admin-password: blah
>   admin-email: blah
> kind: Secret
> metadata:
>   name: airflow
> type: Opaque
> data:
> EOF
...
$ # 최초 1회, airflow-gfp 의 dependency 인 airflow chart 을 내려받습니다.
$ helm3 dep up
...
$ # 'dev' profile 을 사용하여, 배포
$ HELM=helm3 scripts/deploy.sh dev -f custom-values.yaml airflow
...
```

Web Browser 에서 http://airflow-web.nam-batch.svc.ad1.io.navercorp.com:8080 로 접근합니다. `admin` user 로 secret 에 등록한 password 을 입력하여 로긴합니다.