package preview

import (
	"context"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readconcern"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"strings"
	"time"

	"oss.navercorp.com/da-ssp/gfp-api/database"
	"oss.navercorp.com/da-ssp/gfp-api/logger"

	gfpError "oss.navercorp.com/da-ssp/gfp-api/error"
)

var (
	MINUTES = "minutes"
)

type AdProvider struct {
	Id primitive.ObjectID `json:"adProviderId" bson:"_id"`
}

/*
	Preview
*/
type Preview struct {
	AdProviderCd        string    `json:"adProviderCd,omitempty" bson:"adProviderCd"`
	AdUnitId            string    `json:"adUnitId" bson:"adUnitId"`
	PreviewAdRequestUrl string    `json:"previewAdRequestUrl" bson:"previewAdRequestUrl"`
	UserId              *string   `json:"userId,omitempty" bson:"userId"`
	AdId                *string   `json:"adId,omitempty" bson:"adId"`
	LoginCookie         *string   `json:"loginCookie,omitempty" bson:"loginCookie"`
	BrowserCookie       *string   `json:"browserCookie,omitempty" bson:"browserCookie"`
	CreatedAt           time.Time `json:"createdAt" bson:"createdAt"`
	ExpiredAt           time.Time `json:"expiredAt" bson:"expiredAt"`
}

type PreviewService struct{}

/*
	AdProviderCd로 AdProviderId	찾기
*/
func (service *PreviewService) GetAdProviderId(ginCtx *gin.Context, adProviderCd string) (adProviderId string, err *gfpError.GfpApiError) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debugf("adProviderCd= %s", adProviderCd)

	// 1. DB 파이프라인 설정
	pipeline := mongo.Pipeline{
		{{
			"$match", bson.M{"adProviderCd": adProviderCd},
		}},
		{{
			"$project", bson.M{
				"_id": 1,
			},
		}},
	}

	// 3. Aggregation 옵션 설정
	opts := options.Aggregate()
	opts.SetAllowDiskUse(true)

	// 4. timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// 5. Aggregation 실행
	collection := database.GFP.Collection("AdProviders")
	cur, e := collection.Aggregate(ctx, pipeline, opts)
	if e != nil {
		entry.Error(e)

		param := map[string]string{"err": e.Error()}
		err = gfpError.PreviewCommon.BizError(param)

		return
	}
	defer cur.Close(ctx)

	// 6. 조회 결과 가져오기
	for cur.Next(ctx) {
		// entry.Debug("cursor current ::: ", cur.Current)

		var elem AdProvider
		e := cur.Decode(&elem)

		adProviderId = elem.Id.Hex()

		// entry.Debugf("elem= %+v", elem)

		if e != nil {
			entry.Error(e)

			param := map[string]string{"err": e.Error()}
			err = gfpError.PreviewCommon.BizError(param)

			return
		}
	}

	if e := cur.Err(); e != nil {
		entry.Error(e)

		param := map[string]string{"err": e.Error()}
		err = gfpError.PreviewCommon.BizError(param)

		return
	}

	entry.Debugf("adProviderId= %s", adProviderId)

	return
}

/*
	preview 중복 체크 (By MatchedUser)
		- validateMatchedUser
*/
func (service *PreviewService) CheckDuplicatePreview(ginCtx *gin.Context, previewRequestBody *PreviewRequestBody, adProviderCd string) (matchedUser *MatchedUserModel, err *gfpError.GfpApiError) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// 1. 데이터 추출
	adUnitId := previewRequestBody.AdUnitId

	adIds := previewRequestBody.MatchedUser.AdIds
	userIds := previewRequestBody.MatchedUser.UserIds
	loginCookies := previewRequestBody.MatchedUser.LoginCookies
	browserCookies := previewRequestBody.MatchedUser.BrowserCookies

	// 쿼리 조회 시, slice가 nil 이면 에러가 난다. 디폴트로 빈 슬라이스 셋팅.
	emptySlice := []string{}
	if adIds == nil {
		adIds = emptySlice
	}
	if userIds == nil {
		userIds = emptySlice
	}
	if loginCookies == nil {
		loginCookies = emptySlice
	}
	if browserCookies == nil {
		browserCookies = emptySlice
	}

	// 2. DB 파이프라인 설정
	today := time.Now()
	matchQuery := bson.M{
		"adUnitId":  adUnitId,
		"expiredAt": bson.M{"$gt": today},
		"$or": bson.A{
			bson.M{"userId": bson.M{"$in": userIds}},
			bson.M{"adId": bson.M{"$in": adIds}},
			bson.M{"loginCookie": bson.M{"$in": loginCookies}},
			bson.M{"browserCookie": bson.M{"$in": browserCookies}},
		},
	}

	if adProviderCd != "" {
		matchQuery["adProviderCd"] = bson.M{"$ne": adProviderCd}
	}

	pipeline := mongo.Pipeline{
		{{
			"$match", matchQuery,
		}},
		{{
			"$group", bson.M{
				"_id":            "",
				"userIds":        bson.M{"$addToSet": "$userId"},
				"adIds":          bson.M{"$addToSet": "$adId"},
				"loginCookies":   bson.M{"$addToSet": "$loginCookie"},
				"browserCookies": bson.M{"$addToSet": "$browserCookie"},
			},
		}},
		{{
			"$project", bson.M{
				"userIds": bson.M{
					"$filter": bson.M{
						"input": "$userIds",
						"as":    "userId",
						"cond":  bson.M{"$ne": bson.A{"$$userId", nil}},
					},
				},
				"adIds": bson.M{
					"$filter": bson.M{
						"input": "$adIds",
						"as":    "adId",
						"cond":  bson.M{"$ne": bson.A{"$$adId", nil}},
					},
				},
				"loginCookies": bson.M{
					"$filter": bson.M{
						"input": "$loginCookies",
						"as":    "loginCookie",
						"cond":  bson.M{"$ne": bson.A{"$$loginCookie", nil}},
					},
				},
				"browserCookies": bson.M{
					"$filter": bson.M{
						"input": "$browserCookies",
						"as":    "browserCookie",
						"cond":  bson.M{"$ne": bson.A{"$$browserCookie", nil}},
					},
				},
			},
		}},
	}

	// 3. Aggregation 옵션 설정
	opts := options.Aggregate()
	opts.SetAllowDiskUse(true)

	// 4. timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// 5. Aggregation 실행
	collection := database.GFP.Collection("Previews")
	cur, e := collection.Aggregate(ctx, pipeline, opts)
	if e != nil {
		entry.Error(e)

		param := map[string]string{"err": e.Error()}
		err = gfpError.PreviewCommon.BizError(param)

		return
	}
	defer cur.Close(ctx)

	// 6. 조회 결과 가져오기
	for cur.Next(ctx) {
		// entry.Debug("cursor current ::: ", cur.Current)

		e := cur.Decode(&matchedUser)
		if e != nil {
			entry.Error(e)

			param := map[string]string{"err": e.Error()}
			err = gfpError.PreviewCommon.BizError(param)

			return
		}
	}

	if e := cur.Err(); e != nil {
		entry.Error(e)

		param := map[string]string{"err": e.Error()}
		err = gfpError.PreviewCommon.BizError(param)

		return
	}

	// entry.Debugf("matchedUser= %+v", matchedUser)

	return
}

/*
	preview 리스트 조회
*/
func (service *PreviewService) List(ginCtx *gin.Context, previewQueryString *PreviewQueryString) (previews []Preview, err *gfpError.GfpApiError) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[List] 미리보기 리스트 조회")

	// 오늘 날짜
	today := time.Now()

	// string으로 되어있는 matchedUser 데이터를 슬라이스로 변환
	matchedUser := service.convertMatchedUserStringToSlice(previewQueryString)

	// 1. DB 파이프라인 설정
	matchQuery := bson.M{
		"adProviderCd": previewQueryString.AdProviderCd,
		"adUnitId":     primitive.Regex{Pattern: ".*" + previewQueryString.AdUnitId + ".*", Options: "i"},
		"expiredAt":    bson.M{"$gt": today},
	}

	if matchedUser.Count() > 0 {
		matchQuery["$or"] = bson.A{
			bson.M{"userId": bson.M{"$in": matchedUser.UserIds}},
			bson.M{"adId": bson.M{"$in": matchedUser.AdIds}},
			bson.M{"loginCookie": bson.M{"$in": matchedUser.LoginCookies}},
			bson.M{"browserCookie": bson.M{"$in": matchedUser.BrowserCookies}},
		}
	}

	pipeline := mongo.Pipeline{
		{{"$match", matchQuery}},
		{{
			"$project", bson.M{
				"_id":                 0,
				"adUnitId":            1,
				"userId":              1,
				"adId":                1,
				"loginCookie":         1,
				"browserCookie":       1,
				"previewAdRequestUrl": 1,
				"createdAt":           1,
				"expiredAt":           1,
			},
		}},
	}

	// 2. Aggregation 옵션 설정
	opts := options.Aggregate()
	opts.SetAllowDiskUse(true)

	// 3. timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// 4. Aggregation 실행
	collOptions := &options.CollectionOptions{}
	collOptions.SetReadConcern(readconcern.Majority())
	collOptions.SetReadPreference(readpref.SecondaryPreferred(readpref.WithMaxStaleness(time.Second * 90)))

	collection := database.GFP.Collection("Previews", collOptions)
	cur, e := collection.Aggregate(ctx, pipeline, opts)
	if e != nil {
		entry.Error(e)

		param := map[string]string{"err": e.Error()}
		err = gfpError.PreviewCommon.BizError(param)

		return
	}
	defer cur.Close(ctx)

	// 5. 조회 결과 가져오기
	for cur.Next(ctx) {
		// entry.Debug("cursor current ::: ", cur.Current)
		var elem Preview

		e := cur.Decode(&elem)
		if e != nil {
			entry.Error(e)

			param := map[string]string{"err": e.Error()}
			err = gfpError.PreviewCommon.BizError(param)

			return
		}

		previews = append(previews, elem)
	}

	if err := cur.Err(); err != nil {
		entry.Error(err)
	}

	if previews == nil {
		previews = []Preview{}
	}

	return
}

/*
	string으로 되어있는 matchedUser 데이터를 슬라이스로 변환
		주의:빈스트링을 split할 경우, 길이가 1인 슬라이스를 반환한다.
*/
func (service *PreviewService) convertMatchedUserStringToSlice(previewQueryString *PreviewQueryString) (matchedUser MatchedUserModel) {
	emptySlice := []string{}

	if previewQueryString.UserIds != "" {
		matchedUser.UserIds = strings.Split(previewQueryString.UserIds, ",")
	} else {
		matchedUser.UserIds = emptySlice
	}

	if previewQueryString.AdIds != "" {
		matchedUser.AdIds = strings.Split(previewQueryString.AdIds, ",")
	} else {
		matchedUser.AdIds = emptySlice
	}

	if previewQueryString.BrowserCookies != "" {
		matchedUser.BrowserCookies = strings.Split(previewQueryString.BrowserCookies, ",")
	} else {
		matchedUser.BrowserCookies = emptySlice
	}

	if previewQueryString.LoginCookies != "" {
		matchedUser.LoginCookies = strings.Split(previewQueryString.LoginCookies, ",")
	} else {
		matchedUser.LoginCookies = emptySlice
	}

	return
}

/*
	preview 추가
*/
func (service *PreviewService) Add(ginCtx *gin.Context, adProviderCd string, previewRequestBodys *[]PreviewRequestBody) (err *gfpError.GfpApiError) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[Add] 미리보기 추가")

	// 1. 미리보기 단일 row 만들기
	previews := service.makePreviewRows(adProviderCd, previewRequestBodys)

	// 2. timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// 3. InsertMany 실행
	collection := database.GFP.Collection("Previews")
	res, e := collection.InsertMany(ctx, previews)
	if e != nil {
		entry.Error(e)

		param := map[string]string{"err": e.Error()}
		err = gfpError.PreviewCommon.BizError(param)

		return
	}

	entry.Debugf("%+v", res)

	return
}

/*
	preview 수정
*/
func (service *PreviewService) Update(ginCtx *gin.Context, adProviderCd string, previewRequestBodys *[]PreviewRequestBody) (err *gfpError.GfpApiError) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[Update] 미리보기 수정")

	// 1. 미리보기 단일 row 만들기
	previews := service.makePreviewRows(adProviderCd, previewRequestBodys)
	bulkRows := service.makeUpdateRows(previews)

	// 2. timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// 3. BulkWrite 실행
	collection := database.GFP.Collection("Previews")
	res, e := collection.BulkWrite(ctx, bulkRows)

	if e != nil {
		entry.Error(e)

		param := map[string]string{"err": e.Error()}
		err = gfpError.PreviewCommon.BizError(param)

		return
	}

	entry.Debugf("%+v", res)

	return
}

/*
	preview 삭제
*/
func (service *PreviewService) Delete(ginCtx *gin.Context, adProviderCd string, previewRequestBodys *[]PreviewRequestBody) (err *gfpError.GfpApiError) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[Delete] 미리보기 삭제")

	// 1. 미리보기 단일 row 만들기
	previews := service.makePreviewRows(adProviderCd, previewRequestBodys)
	bulkRows := service.makeDeleteRows(previews)

	// 2. timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// 3. BulkWrite 실행
	collection := database.GFP.Collection("Previews")
	res, e := collection.BulkWrite(ctx, bulkRows)

	if e != nil {
		entry.Error(e)

		param := map[string]string{"err": e.Error()}
		err = gfpError.PreviewCommon.BizError(param)

		return
	}

	entry.Debugf("%+v", res)

	return
}

/*
	userIds, adIds, loginCookies, browserCookies를 DB에 넣을 단일 row로 쪼개기
		- insertMany로 데이터 넣으려면 []interface{} 로 만들어야 한다.
		- 변수(userId, adId 등)를 새로 만드는 이유는, for range 로 선언된 변수가 재사용되서, 기존 값을 덮어씌우기 때문임
		- Add / Update / Delete
		- duration은 Add / Update 만 적용됨. Delete는 BulkWrite 시, expiredAt을 무조건 현재 시간으로 셋팅함
*/
func (service *PreviewService) makePreviewRows(adProviderCd string, previewRequestBodys *[]PreviewRequestBody) (rows []interface{}) {
	// var log = logger.GetLogger("default")

	for _, previewRequestBody := range *previewRequestBodys {
		// log.Debugf("\n %+v \n", previewRequestBody)

		// 만료 시간 계산하기 (디폴트 60분) (Add/Update만 적용됨)
		var duration time.Duration
		if previewRequestBody.Expiration == 0 {
			duration = time.Duration(60) * time.Minute
		} else {
			duration = time.Duration(previewRequestBody.Expiration) * time.Minute
		}

		createdAt := time.Now()
		expiredAt := time.Now().Add(duration)

		userIdsData := make([]interface{}, len(previewRequestBody.MatchedUser.UserIds))
		for i, id := range previewRequestBody.MatchedUser.UserIds {
			var userId = id
			userIdsData[i] = Preview{
				AdProviderCd: adProviderCd, AdUnitId: previewRequestBody.AdUnitId, PreviewAdRequestUrl: previewRequestBody.PreviewAdRequestUrl,
				UserId: &userId, AdId: nil, LoginCookie: nil, BrowserCookie: nil,
				CreatedAt: createdAt, ExpiredAt: expiredAt,
			}
		}
		rows = append(rows, userIdsData...)

		adIdsData := make([]interface{}, len(previewRequestBody.MatchedUser.AdIds))
		for i, id := range previewRequestBody.MatchedUser.AdIds {
			var adId = id
			adIdsData[i] = Preview{
				AdProviderCd: adProviderCd, AdUnitId: previewRequestBody.AdUnitId, PreviewAdRequestUrl: previewRequestBody.PreviewAdRequestUrl,
				UserId: nil, AdId: &adId, LoginCookie: nil, BrowserCookie: nil,
				CreatedAt: createdAt, ExpiredAt: expiredAt,
			}
		}
		rows = append(rows, adIdsData...)

		loginCookiesData := make([]interface{}, len(previewRequestBody.MatchedUser.LoginCookies))
		for i, id := range previewRequestBody.MatchedUser.LoginCookies {
			var loginCookie = id
			loginCookiesData[i] = Preview{
				AdProviderCd: adProviderCd, AdUnitId: previewRequestBody.AdUnitId, PreviewAdRequestUrl: previewRequestBody.PreviewAdRequestUrl,
				UserId: nil, AdId: nil, LoginCookie: &loginCookie, BrowserCookie: nil,
				CreatedAt: createdAt, ExpiredAt: expiredAt,
			}
		}
		rows = append(rows, loginCookiesData...)

		browserCookiesData := make([]interface{}, len(previewRequestBody.MatchedUser.BrowserCookies))
		for i, id := range previewRequestBody.MatchedUser.BrowserCookies {
			var browserCookie = id
			browserCookiesData[i] = Preview{
				AdProviderCd: adProviderCd, AdUnitId: previewRequestBody.AdUnitId, PreviewAdRequestUrl: previewRequestBody.PreviewAdRequestUrl,
				UserId: nil, AdId: nil, LoginCookie: nil, BrowserCookie: &browserCookie,
				CreatedAt: createdAt, ExpiredAt: expiredAt,
			}
		}
		rows = append(rows, browserCookiesData...)
	}

	// for _, preview := range previews {
	// 	log.Debugf("\n preview= %+v \n", preview)
	// }

	return
}

/*
	previewRows를 bulk update 하기 위한 구조로 만들기
		- previewAdRequestUrl만 수정 가능하다.
		- Update
*/
func (service *PreviewService) makeUpdateRows(rows []interface{}) (bulkRows []mongo.WriteModel) {
	today := time.Now()

	for _, r := range rows {
		var row Preview = r.(Preview)
		filter := bson.M{
			"adProviderCd": row.AdProviderCd, "adUnitId": row.AdUnitId,
			"userId": row.UserId, "adId": row.AdId, "loginCookie": row.LoginCookie, "browserCookie": row.BrowserCookie,
			"expiredAt": bson.M{"$gt": today},
		}

		update := bson.M{
			"$set":         bson.M{"previewAdRequestUrl": row.PreviewAdRequestUrl},
			"$setOnInsert": bson.M{"createdAt": row.CreatedAt, "expiredAt": row.ExpiredAt},
		}

		model := mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpdate(update).
			SetUpsert(true)

		bulkRows = append(bulkRows, model)
	}

	// for _, r := range bulkRows {
	// 	log.Debugf("%+v", r)
	// }

	return
}

/*
	previewRows를 bulk update 하기 위한 구조로 만들기
		- expiredAt를 현재 시간으로 수정하여 논리적인 삭제 처리
		- Delete
*/
func (service *PreviewService) makeDeleteRows(rows []interface{}) (bulkRows []mongo.WriteModel) {
	today := time.Now()

	for _, r := range rows {
		var row Preview = r.(Preview)
		filter := bson.M{
			"adProviderCd": row.AdProviderCd, "adUnitId": row.AdUnitId,
			"userId": row.UserId, "adId": row.AdId, "loginCookie": row.LoginCookie, "browserCookie": row.BrowserCookie,
			"expiredAt": bson.M{"$gt": today},
		}

		update := bson.M{
			"$set": bson.M{"expiredAt": today},
		}

		model := mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpdate(update)

		bulkRows = append(bulkRows, model)
	}

	// for _, r := range bulkRows {
	// 	log.Debugf("%+v", r)
	// }

	return
}
