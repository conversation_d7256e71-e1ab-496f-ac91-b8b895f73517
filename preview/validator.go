package preview

import (
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	gfpError "oss.navercorp.com/da-ssp/gfp-api/error"
	"regexp"
)

type PreviewValidator struct{}

/* adProviderCd 가 없거나 영문/숫자/언더바 로 구성되어 있지 않는 경우, 에러 */
func (validator *PreviewValidator) ValidateAdProviderCd(adProviderCd string) (err *gfpError.GfpApiError) {

	regex := regexp.MustCompile("^[a-zA-Z0-9_]*$")

	if adProviderCd == "" || regex.MatchString(adProviderCd) != true {
		err = &gfpError.PreviewInValidAdProviderCd
	}

	return
}

/* 권한이 없는 adProviderCd인 경우, 에러 */
func (validator *PreviewValidator) CheckAuthorizedByAdProviderCd(ginCtx *gin.Context, adProviderCd string, adpIds []primitive.ObjectID) (err *gfpError.GfpApiError) {
	adProviderId, err := previewService.GetAdProviderId(ginCtx, adProviderCd)
	if err != nil {
		return
	}

	if adProviderId == "" {
		err = &gfpError.PreviewInValidAdProviderCd
		return
	}

	isValid := false
	for _, id := range adpIds {
		if adProviderId == id.Hex() {
			isValid = true
		}
	}

	if isValid != true {
		err = &gfpError.PreviewUnAuthorizedAdProviderCd
		return
	}

	return
}

/* RequestBody validation */
func (validator *PreviewValidator) ValidateRequestBody(ginCtx *gin.Context, previewRequestBody *PreviewRequestBody) (err *gfpError.GfpApiError) {
	methodType := ginCtx.Request.Method

	// [Validation] 3-1. adUnitId 가 없는 경우, 400 에러 (공통)
	adUnitId := previewRequestBody.AdUnitId
	if adUnitId == "" {
		err = &gfpError.PreviewInValidRequestBody
		return
	}

	// [Validation] 3-2. previewAdRequestUrl 가 없는 경우, 400 에러 (추가/수정)
	if methodType != "DELETE" {
		previewAdRequestUrl := previewRequestBody.PreviewAdRequestUrl
		if previewAdRequestUrl == "" {
			err = &gfpError.PreviewInValidRequestBody
			return
		}
	}

	// [Validation] 3-3. userIds, adIds, loginCookies, browserCookies 가 모두 없는 경우, 400 에러 (공통)
	matchedUser := previewRequestBody.MatchedUser
	if matchedUser.Count() == 0 {
		err = &gfpError.PreviewInValidRequestBody
		return
	}

	// [Validation] 3-4. userIds, adIds, loginCookies, browserCookies 에 공백이 들어간 경우, 400 에러 (공통)
	if matchedUser.HasEmptyString() {
		err = &gfpError.PreviewInValidRequestBody
		return
	}

	return
}

// [Validation] 4. userIds, adIds, loginCookies, browserCookies 중 하나라도 이미 등록된 게 있는 경우, 500 에러 (공통)
func (validator *PreviewValidator) ValidateMatchedUser(ginCtx *gin.Context, previewRequestBody *PreviewRequestBody, adProviderCd string) (failedUser *MatchedUserModel, err *gfpError.GfpApiError) {
	methodType := ginCtx.Request.Method

	var duplicate *MatchedUserModel
	if duplicate, err = previewService.CheckDuplicatePreview(ginCtx, previewRequestBody, adProviderCd); err != nil {
		return
	}

	if duplicate != nil && duplicate.Count() > 0 {
		failedUser = duplicate
		if methodType == "POST" {
			err = &gfpError.PreviewCannotInsert
		} else {
			err = &gfpError.PreviewCannotChange
		}
		return
	}

	return
}
