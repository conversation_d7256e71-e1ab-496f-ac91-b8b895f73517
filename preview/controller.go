package preview

import (
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"net/http"
	"time"

	"oss.navercorp.com/da-ssp/gfp-api/config"
	gfpError "oss.navercorp.com/da-ssp/gfp-api/error"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
	"oss.navercorp.com/da-ssp/gfp-api/triplea"
	"oss.navercorp.com/da-ssp/gfp-api/zookeeper"
	//"oss.navercorp.com/da-ssp/gfp-api/zookeeper_old"
)

type PreviewQueryString struct {
	AdProviderCd string

	AdUnitId       string `form:"adUnitId" binding:"omitempty"`
	UserIds        string `form:"userIds" binding:"omitempty"`
	AdIds          string `form:"adIds" binding:"omitempty"`
	LoginCookies   string `form:"loginCookies" binding:"omitempty"`
	BrowserCookies string `form:"browserCookies" binding:"omitempty"`
}

type PreviewRequestBody struct {
	RequestId string `form:"requestId"` // 다건 처리 시, 필수

	AdUnitId            string           `form:"adUnitId"`
	PreviewAdRequestUrl string           `form:"previewAdRequestUrl"` // 추가/수정 시, 필수
	Expiration          int              `form:"expiration"`          // 추가/수정 시, 옵션
	MatchedUser         MatchedUserModel `form:"matchedUser"`
}

type MatchedUserModel struct {
	UserIds        []string `form:"userIds" json:"userIds,omitempty" bson:"userIds"`
	AdIds          []string `form:"adIds" json:"adIds,omitempty" bson:"adIds"`
	LoginCookies   []string `form:"loginCookies" json:"loginCookies,omitempty" bson:"loginCookies"`
	BrowserCookies []string `form:"browserCookies" json:"browserCookies,omitempty" bson:"browserCookies"`
}

func (matchedUser *MatchedUserModel) Count() (count int) {
	count = len(matchedUser.UserIds) + len(matchedUser.AdIds) + len(matchedUser.LoginCookies) + len(matchedUser.BrowserCookies)

	return
}

func (matchedUser *MatchedUserModel) HasEmptyString() (hasEmptyString bool) {
	hasEmptyString = false

	var list []string
	list = append(list, matchedUser.UserIds...)
	list = append(list, matchedUser.AdIds...)
	list = append(list, matchedUser.LoginCookies...)
	list = append(list, matchedUser.BrowserCookies...)

	for _, i := range list {
		if i == "" {
			hasEmptyString = true
		}
	}

	return
}

type FailedResult struct {
	RequestId string `json:"requestId,omitempty"` // 다건 처리 시, 필수

	FailedReason string            `json:"failedReason"`
	FailedUser   *MatchedUserModel `json:"failedUser,omitempty"`
}

type PreviewController struct{}

/**
 	@apiVersion 1.0.0
	@api {get} /api/preview 미리보기 목록 조회
	@apiName List
	@apiGroup Preview
	@apiDescription 미리보기 목록 조회

	@apiParam {String} userId API User ID<br/>ex) "admin"
	@apiParam {String{64}} encodedUserId Encoded API User ID<br/>ex) "1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1"

	@apiParam {String} adProviderCd GFP AdProvider Code
	@apiParam {String} adUnitId SSP AdUnit Id (optional)
	@apiParam {String} userIds publisher 전용 User Id 목록 (optional)
	@apiParam {String} adIds AdId 목록 (optional)
	@apiParam {String} loginCookies Naver L-Cookie 목록 (optional)
	@apiParam {String} browserCookies Naver B-Cookie 목록 (optional)

	@apiParamExample {form} Request-Example:
		http://test-api-gfp.da.navercorp.com:8080/api/preview?userId=admin&encodedUserId=1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1&adProviderCd=GOOGLE1&adUnitId=m_post_body&userIds=uId1,uId2&adIds=adId1&loginCookies=lc1,lc2,lc3&browserCookies=bc1

	@apiSuccess {json} array
	@apiSuccessExample {json} Success-Response-Example: 미리보기 목록
		HTTP/1.1 200 OK
		[
			{
				"adUnitId": "adUnitId_001",
				"previewAdRequestUrl": "naver.com",
				"browserCookie": "bcookie_0827_04",
				"createdAt": "2019-08-28T04:52:30.128Z",
				"expiredAt": "2019-08-29T04:52:30.129Z"
			},
			{
				"adUnitId": "adUnitId_001",
				"previewAdRequestUrl": "naver.com",
				"browserCookie": "bcookie_0827_05",
				"createdAt": "2019-08-28T04:53:31.476Z",
				"expiredAt": "2019-08-29T04:53:31.477Z"
			},
		]

	@apiError (400) BadRequest
						DSP 코드가 영문자/숫자/언더바 로 구성되지 않은 경우<br>
						쿼리스트링 바인딩 오류가 나는 경우<br>
	@apiError (403) Forbidden Not authenticated. You need to register server IP in the pasta acl consumer.
	@apiError (500) InternalServerError
						DB 조회 오류<br/>

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"failedReason": "DSP 코드 값이 유효하지 않습니다."
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		{
			"failedReason": "runtime error: invalid memory address or nil pointer dereference"
		}

	@apiSampleRequest /api/preview
*/
func (controller *PreviewController) List(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[List] 미리보기 목록 조회")

	var previewQueryString PreviewQueryString

	// [Validation] 1-1. adProviderCd 가 없거나 영문/숫자/언더바 로 구성되어 있지 않는 경우, 400 에러 (공통)
	adProviderCd := ginCtx.Query("adProviderCd")
	if err := previewValidator.ValidateAdProviderCd(adProviderCd); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 1-2. 권한이 없는 adProviderCd인 경우, 401 에러 (공통)
	userId := ginCtx.Query("userId")
	apiUser, err := triplea.GetApiUser(ginCtx, userId)
	if err != nil {
		param := map[string]string{"err": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.PreviewCommon.BizError(param))
		return
	}
	if err := previewValidator.CheckAuthorizedByAdProviderCd(ginCtx, adProviderCd, apiUser.AdpIds); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	previewQueryString.AdProviderCd = adProviderCd

	// [Validation] 2. 쿼리스트링(AdUnitId, UserIds, AdIds, LoginCookies, BrowserCookies) 바인딩 에러
	if err := ginCtx.ShouldBindWith(&previewQueryString, binding.Query); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusBadRequest, FailedResult{FailedReason: err.Error()})
		return
	}

	entry.Debugf("\n\n AdProviderCd= %s \nQueryString= %+v\n", adProviderCd, previewQueryString)

	// 미리보기 조회
	if previews, err := previewService.List(ginCtx, &previewQueryString); err != nil {
		ginCtx.IndentedJSON(http.StatusInternalServerError, FailedResult{FailedReason: err.Message})
	} else {
		ginCtx.IndentedJSON(http.StatusOK, previews)
	}
}

/**
	@apiVersion 1.0.0
	@api {post} /api/preview 미리보기 단건 추가
	@apiName AddPreview
	@apiGroup Preview
	@apiDescription 미리보기 단건 추가
		AdUnitId에 어떤 DSP도 MatchedUser를 등록하지 않았다면, 추가 가능
		AdUnitId에 이미 등록된 MatchedUser가 있다면, 추가 불가

	@apiHeader {String} Content-Type=application/json

	@apiParam {String} userId API User ID<br/>ex) "admin"
	@apiParam {String{64}} encodedUserId Encoded API User ID<br/>ex) "1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1"

	@apiParam {String} adProviderCd GFP AdProvider Code
	@apiParam (Request Body) {String} adUnitId SSP AdUnit Id
	@apiParam (Request Body) {String} previewAdRequestUrl 미리보기 광고 호출 URL
	@apiParam (Request Body) {Number} expiration 미리보기 만료정보 (분단위) (optional)
	@apiParam (Request Body) {Object} matchedUser MatchedUser Object
	@apiParam (Request Body) {String[]} matchedUser.userIds publisher 전용 User Id 목록
	@apiParam (Request Body) {String[]} matchedUser.adIds AdId 목록
	@apiParam (Request Body) {String[]} matchedUser.loginCookies Naver L-Cookie 목록
	@apiParam (Request Body) {String[]} matchedUser.browserCookies Naver B-Cookie 목록

	@apiParamExample {json} Request-Example:
		{
			"adUnitId": "adUnitId_001",
			"previewAdRequestUrl": "naver.com",
			"matchedUser": {
				"userIds": ["uid01", "uid02"],
				"browserCookies": ["bcookie01"]
			}
 		}

	@apiSuccess 204 No Content.
	@apiSuccessExample {json} Success-Response-Example: 미리보기 단건 추가
		HTTP/1.1 200 OK

	@apiError (400) BadRequest
						DSP 코드가 영문자/숫자/언더바 로 구성되지 않은 경우<br>
						request body에 필수 값이 없는 경우<br>
	@apiError (403) Forbidden Not authenticated. You need to register server IP in the pasta acl consumer.
	@apiError (500) InternalServerError
						이미 등록된 MatchedUser가 있는 경우<br>
						DB 오류<br>

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"failedReason": "Request Body가 유효하지 않습니다."
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		{
			"failedReason": "이미 등록된 MatchedUser가 존재합니다.",
			"failedUser":{
				"userIds":["uid01"],
				"browserCookies":[
					"bcookie01"
				]
			}
		}

	@apiSampleRequest off
*/
func (controller *PreviewController) AddPreview(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[AddPreview] 미리보기 단건 추가")

	// [Validation] 1-1. adProviderCd 가 없거나 영문/숫자/언더바 로 구성되어 있지 않는 경우, 400 에러 (공통)
	adProviderCd := ginCtx.Query("adProviderCd")
	if err := previewValidator.ValidateAdProviderCd(adProviderCd); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 1-2. 권한이 없는 adProviderCd인 경우, 401 에러 (공통)
	userId := ginCtx.Query("userId")
	apiUser, err := triplea.GetApiUser(ginCtx, userId)
	if err != nil {
		param := map[string]string{"err": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.PreviewCommon.BizError(param))
		return
	}
	if err := previewValidator.CheckAuthorizedByAdProviderCd(ginCtx, adProviderCd, apiUser.AdpIds); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 2. RequestBody에 문제가 있는 경우, 400 에러 (공통)
	var previewRequestBody PreviewRequestBody
	if err := ginCtx.ShouldBindBodyWith(&previewRequestBody, binding.JSON); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusBadRequest, FailedResult{FailedReason: gfpError.PreviewInValidRequestBody.Message})
		return
	}

	// [Validation] 3-1. adUnitId 가 없는 경우, 400 에러 (공통)
	// [Validation] 3-2. previewAdRequestUrl 가 없는 경우, 400 에러 (추가/수정)
	// [Validation] 3-3. userIds, adIds, loginCookies, browserCookies 가 모두 없는 경우, 400 에러 (공통)
	if err := previewValidator.ValidateRequestBody(ginCtx, &previewRequestBody); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	entry.Debugf("\n\n AdProviderCd= %s \nRequestBody= %+v \n", adProviderCd, previewRequestBody)

	// [Validation] 4. userIds, adIds, loginCookies, browserCookies 중 하나라도 이미 등록된 게 있는 경우, 500 에러 (공통)
	if failedUser, err := previewValidator.ValidateMatchedUser(ginCtx, &previewRequestBody, ""); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusInternalServerError, FailedResult{FailedReason: err.Message, FailedUser: failedUser})
		return
	}

	// 미리보기 단건 추가
	targets := []PreviewRequestBody{previewRequestBody}
	if err := previewService.Add(ginCtx, adProviderCd, &targets); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusInternalServerError, FailedResult{FailedReason: err.Message})
		return
	}

	setZookeeperData(ginCtx)
}

/**
 	@apiVersion 1.0.0
	@api {post} /api/previews 미리보기 다건 추가
	@apiName AddPreviews
	@apiGroup Preview
	@apiDescription 미리보기 다건 추가
		AdUnitId에 어떤 DSP도 MatchedUser를 등록하지 않았다면, 추가 가능
		AdUnitId에 이미 등록된 MatchedUser가 있다면, 추가 불가

	@apiHeader {String} Content-Type=application/json

	@apiParam {String} userId API User ID<br/>ex) "admin"
	@apiParam {String{64}} encodedUserId Encoded API User ID<br/>ex) "1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1"

	@apiParam {String} adProviderCd GFP AdProvider Code
	@apiParam (Request Body) {Object[]} previewRequestBody
	@apiParam (Request Body) {String} previewRequestBody.requestId 서비스 requestId
	@apiParam (Request Body) {String} previewRequestBody.adUnitId SSP AdUnit Id
	@apiParam (Request Body) {String} previewRequestBody.previewAdRequestUrl 미리보기 광고 호출 URL
	@apiParam (Request Body) {Number} previewRequestBody.expiration 미리보기 만료정보 (분단위) (optional)
	@apiParam (Request Body) {Object} previewRequestBody.matchedUser MatchedUser Object
	@apiParam (Request Body) {String[]} previewRequestBody.matchedUser.userIds publisher 전용 User Id 목록
	@apiParam (Request Body) {String[]} previewRequestBody.matchedUser.adIds AdId 목록
	@apiParam (Request Body) {String[]} previewRequestBody.matchedUser.loginCookies Naver L-Cookie 목록
	@apiParam (Request Body) {String[]} previewRequestBody.matchedUser.browserCookies Naver B-Cookie 목록

	@apiParamExample {json} Request-Example:
		[{
			"requestId": "reqId0001"
			"adUnitId": "adUnitId_001",
			"previewAdRequestUrl": "naver.com",
			"matchedUser": {
				"userIds": ["uid01", "uid02"],
				"browserCookies": ["bcookie01"]
			}
		},
		{
			"requestId": "reqId0002"
			"adUnitId": "adUnitId_002",
			"previewAdRequestUrl": "naver.com",
			"matchedUser": {
				"userIds": ["uid03", "uid04"]
			}
		}]

	@apiSuccess 204 No Content.
	@apiSuccessExample {json} Success-Response-Example: 미리보기 다건 추가
		HTTP/1.1 200 OK

	@apiError (400) BadRequest
						DSP 코드가 영문자/숫자/언더바 로 구성되지 않은 경우<br>
						request body에 필수 값이 없는 경우<br>
	@apiError (403) Forbidden Not authenticated. You need to register server IP in the pasta acl consumer.
	@apiError (500) InternalServerError
						이미 등록된 MatchedUser가 있는 경우<br>
						DB 오류<br>

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"failedReason": "Request Body가 유효하지 않습니다."
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		[{
			"requestId": "reqId0002",
			"failedReason": "이미 등록된 MatchedUser가 존재합니다.",
			"failedUser":{
				"userIds":["uid03"]
			}
		}, { ... }, ... ]

	@apiSampleRequest off
*/
func (controller *PreviewController) AddPreviews(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[AddPreviews] 미리보기 다건 추가")

	// [Validation] 1-1. adProviderCd 가 없거나 영문/숫자/언더바 로 구성되어 있지 않는 경우, 400 에러 (공통)
	adProviderCd := ginCtx.Query("adProviderCd")
	if err := previewValidator.ValidateAdProviderCd(adProviderCd); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 1-2. 권한이 없는 adProviderCd인 경우, 401 에러 (공통)
	userId := ginCtx.Query("userId")
	apiUser, err := triplea.GetApiUser(ginCtx, userId)
	if err != nil {
		param := map[string]string{"err": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.PreviewCommon.BizError(param))
		return
	}
	if err := previewValidator.CheckAuthorizedByAdProviderCd(ginCtx, adProviderCd, apiUser.AdpIds); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 2-1. RequestBody에 문제가 있는 경우, 400 에러 (공통)
	var previewRequestBodys []PreviewRequestBody
	if err := ginCtx.ShouldBindBodyWith(&previewRequestBodys, binding.JSON); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusBadRequest, FailedResult{FailedReason: gfpError.PreviewInValidRequestBody.Message})
		return
	}

	// [Validation] 2-2. RequestBody가 빈 배열인 경우, 400 에러 (공통)
	if len(previewRequestBodys) < 1 {
		entry.Error(gfpError.PreviewInValidRequestBody.Error())
		ginCtx.IndentedJSON(http.StatusBadRequest, FailedResult{FailedReason: gfpError.PreviewInValidRequestBody.Message})
		return
	}

	// 여러 건중 하나라도 걸리면, 400 에러
	for _, previewRequestBody := range previewRequestBodys {
		// [Validation] 3-1. adUnitId 가 없는 경우, 400 에러 (공통)
		// [Validation] 3-2. previewAdRequestUrl 가 없는 경우, 400 에러 (추가/수정)
		// [Validation] 3-3. userIds, adIds, loginCookies, browserCookies 가 모두 없는 경우, 400 에러 (공통)
		if err := previewValidator.ValidateRequestBody(ginCtx, &previewRequestBody); err != nil {
			entry.Error(err.Error())
			ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
			return
		}
	}

	entry.Debugf("\n\n AdProviderCd= %s \n previewRequestBodys= %+v \n", adProviderCd, previewRequestBodys)

	var targets []PreviewRequestBody
	var failedResults []FailedResult

	// [Validation] 4. userIds, adIds, loginCookies, browserCookies 중 하나라도 이미 등록된 게 있는 경우, 500 에러 (공통)
	for _, previewRequestBody := range previewRequestBodys {
		if failedUser, err := previewValidator.ValidateMatchedUser(ginCtx, &previewRequestBody, ""); err != nil {
			failedResults = append(failedResults, FailedResult{
				RequestId:    previewRequestBody.RequestId,
				FailedReason: err.Message,
				FailedUser:   failedUser,
			})
		} else {
			targets = append(targets, previewRequestBody)
		}
	}

	for _, target := range targets {
		entry.Debugf("\n\n target= %+v \n", target)
	}

	// 미리보기 다건 추가
	if err := previewService.Add(ginCtx, adProviderCd, &targets); err != nil {
		for _, target := range targets {
			failedResults = append(failedResults, FailedResult{
				RequestId:    target.RequestId,
				FailedReason: err.Message,
			})
		}
	}

	if len(failedResults) > 0 {
		ginCtx.IndentedJSON(http.StatusInternalServerError, failedResults)
	}

	setZookeeperData(ginCtx)
}

/**
 	@apiVersion 1.0.0
	@api {put} /api/preview 미리보기 단건 수정
	@apiName UpdatePreview
	@apiGroup Preview
	@apiDescription 미리보기 단건 수정
		AdUnitId에 어떤 DSP도 MatchedUser를 등록하지 않았다면, 추가 가능
		AdUnitId에 동일 DSP로 등록된 MatchedUser가 있다면, URL만 수정 가능
		AdUnitId에 다른 DSP로 등록된 MatchedUser가 있다면, 추가/수정 불가

	@apiHeader {String} Content-Type=application/json

	@apiParam {String} userId API User ID<br/>ex) "admin"
	@apiParam {String{64}} encodedUserId Encoded API User ID<br/>ex) "1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1"

	@apiParam {String} adProviderCd GFP AdProvider Code
	@apiParam (Request Body) {String} adUnitId SSP AdUnit Id
	@apiParam (Request Body) {String} previewAdRequestUrl 미리보기 광고 호출 URL
	@apiParam (Request Body) {Number} expiration 미리보기 만료정보 (분단위) (optional)
	@apiParam (Request Body) {Object} matchedUser MatchedUser Object
	@apiParam (Request Body) {String[]} matchedUser.userIds publisher 전용 User Id 목록
	@apiParam (Request Body) {String[]} matchedUser.adIds AdId 목록
	@apiParam (Request Body) {String[]} matchedUser.loginCookies Naver L-Cookie 목록
	@apiParam (Request Body) {String[]} matchedUser.browserCookies Naver B-Cookie 목록

	@apiParamExample {json} Request-Example:
		{
			"adUnitId": "adUnitId_001",
			"previewAdRequestUrl": "naver.com",
			"matchedUser": {
				"userIds": ["uid01", "uid02"],
				"browserCookies": ["bcookie01"]
			}
 		}

	@apiSuccess 204 No Content.
	@apiSuccessExample {json} Success-Response-Example: 미리보기 단건 수정
		HTTP/1.1 200 OK

	@apiError (400) BadRequest
						DSP 코드가 영문자/숫자/언더바 로 구성되지 않은 경우<br>
						request body에 필수 값이 없는 경우<br>
	@apiError (403) Forbidden Not authenticated. You need to register server IP in the pasta acl consumer.
	@apiError (500) InternalServerError
						다른 DSP로 등록된 MatchedUser가 있는 경우<br>
						DB 오류<br>

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"failedReason": "Request Body가 유효하지 않습니다."
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		{
			"failedReason": "이미 등록된 MatchedUser가 존재합니다.",
			"failedUser":{
				"userIds":["uid01"],
				"browserCookies":[
					"bcookie01"
				]
			}
		}

	@apiSampleRequest off
*/
func (controller *PreviewController) UpdatePreview(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[Update] 미리보기 단건 수정")

	// [Validation] 1-1. adProviderCd 가 없거나 영문/숫자/언더바 로 구성되어 있지 않는 경우, 400 에러 (공통)
	adProviderCd := ginCtx.Query("adProviderCd")
	if err := previewValidator.ValidateAdProviderCd(adProviderCd); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 1-2. 권한이 없는 adProviderCd인 경우, 401 에러 (공통)
	userId := ginCtx.Query("userId")
	apiUser, err := triplea.GetApiUser(ginCtx, userId)
	if err != nil {
		param := map[string]string{"err": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.PreviewCommon.BizError(param))
		return
	}
	if err := previewValidator.CheckAuthorizedByAdProviderCd(ginCtx, adProviderCd, apiUser.AdpIds); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 2. RequestBody에 문제가 있는 경우, 400 에러 (공통)
	var previewRequestBody PreviewRequestBody
	if err := ginCtx.ShouldBindBodyWith(&previewRequestBody, binding.JSON); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusBadRequest, FailedResult{FailedReason: gfpError.PreviewInValidRequestBody.Message})
		return
	}

	// [Validation] 3-1. adUnitId 가 없는 경우, 400 에러 (공통)
	// [Validation] 3-2. previewAdRequestUrl 가 없는 경우, 400 에러 (추가/수정)
	// [Validation] 3-3. userIds, adIds, loginCookies, browserCookies 가 모두 없는 경우, 400 에러 (공통)
	if err := previewValidator.ValidateRequestBody(ginCtx, &previewRequestBody); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	entry.Debugf("\n\n AdProviderCd= %s \nRequestBody= %+v \n", adProviderCd, previewRequestBody)

	// [Validation] 4. userIds, adIds, loginCookies, browserCookies 중 하나라도 이미 등록된 게 있는 경우, 500 에러 (공통)
	if failedUser, err := previewValidator.ValidateMatchedUser(ginCtx, &previewRequestBody, adProviderCd); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusInternalServerError, FailedResult{FailedReason: err.Message, FailedUser: failedUser})
		return
	}

	// 미리보기 단건 수정
	targets := []PreviewRequestBody{previewRequestBody}
	if err := previewService.Update(ginCtx, adProviderCd, &targets); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusInternalServerError, FailedResult{FailedReason: err.Message})
		return
	}

	setZookeeperData(ginCtx)
}

/**
 	@apiVersion 1.0.0
	@api {put} /api/previews 미리보기 다건 수정
	@apiName UpdatePreviews
	@apiGroup Preview
	@apiDescription 미리보기 다건 수정
		AdUnitId에 어떤 DSP도 MatchedUser를 등록하지 않았다면, 추가 가능
		AdUnitId에 동일 DSP로 등록된 MatchedUser가 있다면, URL만 수정 가능
		AdUnitId에 다른 DSP로 등록된 MatchedUser가 있다면, 추가/수정 불가

	@apiHeader {String} Content-Type=application/json

	@apiParam {String} userId API User ID<br/>ex) "admin"
	@apiParam {String{64}} encodedUserId Encoded API User ID<br/>ex) "1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1"

	@apiParam {String} adProviderCd GFP AdProvider Code
	@apiParam (Request Body) {Object[]} previewRequestBody
	@apiParam (Request Body) {String} previewRequestBody.requestId 서비스 requestId
	@apiParam (Request Body) {String} previewRequestBody.adUnitId SSP AdUnit Id
	@apiParam (Request Body) {String} previewRequestBody.previewAdRequestUrl 미리보기 광고 호출 URL
	@apiParam (Request Body) {Number} previewRequestBody.expiration 미리보기 만료정보 (분단위) (optional)
	@apiParam (Request Body) {Object} previewRequestBody.matchedUser MatchedUser Object
	@apiParam (Request Body) {String[]} previewRequestBody.matchedUser.userIds publisher 전용 User Id 목록
	@apiParam (Request Body) {String[]} previewRequestBody.matchedUser.adIds AdId 목록
	@apiParam (Request Body) {String[]} previewRequestBody.matchedUser.loginCookies Naver L-Cookie 목록
	@apiParam (Request Body) {String[]} previewRequestBody.matchedUser.browserCookies Naver B-Cookie 목록

	@apiParamExample {json} Request-Example:
		[{
			"requestId": "reqId0001"
			"adUnitId": "adUnitId_001",
			"previewAdRequestUrl": "naver.com",
			"matchedUser": {
				"userIds": ["uid01", "uid02"],
				"browserCookies": ["bcookie01"]
			}
		},
		{
			"requestId": "reqId0002"
			"adUnitId": "adUnitId_002",
			"previewAdRequestUrl": "naver.com",
			"matchedUser": {
				"userIds": ["uid03", "uid04"]
			}
		}]

	@apiSuccess 204 No Content.
	@apiSuccessExample {json} Success-Response-Example: 미리보기 다건 수정
		HTTP/1.1 200 OK

	@apiError (400) BadRequest
						DSP 코드가 영문자/숫자/언더바 로 구성되지 않은 경우<br>
						request body에 필수 값이 없는 경우<br>
	@apiError (403) Forbidden Not authenticated. You need to register server IP in the pasta acl consumer.
	@apiError (500) InternalServerError
						이미 등록된 MatchedUser가 있는 경우<br>
						DB 오류<br>

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"failedReason": "Request Body가 유효하지 않습니다."
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		[{
			"requestId": "reqId0002",
			"failedReason": "이미 등록된 MatchedUser가 존재합니다.",
			"failedUser":{
				"userIds":["uid03"]
			}
		}, { ... }, ... ]

	@apiSampleRequest off
*/
func (controller *PreviewController) UpdatePreviews(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[UpdatePreviews] 미리보기 다건 수정")

	// [Validation] 1-1. adProviderCd 가 없거나 영문/숫자/언더바 로 구성되어 있지 않는 경우, 400 에러 (공통)
	adProviderCd := ginCtx.Query("adProviderCd")
	if err := previewValidator.ValidateAdProviderCd(adProviderCd); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 1-2. 권한이 없는 adProviderCd인 경우, 401 에러 (공통)
	userId := ginCtx.Query("userId")
	apiUser, err := triplea.GetApiUser(ginCtx, userId)
	if err != nil {
		param := map[string]string{"err": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.PreviewCommon.BizError(param))
		return
	}
	if err := previewValidator.CheckAuthorizedByAdProviderCd(ginCtx, adProviderCd, apiUser.AdpIds); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 2-1. RequestBody에 문제가 있는 경우, 400 에러 (공통)
	var previewRequestBodys []PreviewRequestBody
	if err := ginCtx.ShouldBindBodyWith(&previewRequestBodys, binding.JSON); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusBadRequest, FailedResult{FailedReason: gfpError.PreviewInValidRequestBody.Message})
		return
	}

	entry.Debugf("\n\n adProviderCd= %s \n previewRequestBodys= %+v \n", adProviderCd, previewRequestBodys)

	// [Validation] 2-2. RequestBody가 빈 배열인 경우, 400 에러 (공통)
	if len(previewRequestBodys) < 1 {
		entry.Error(gfpError.PreviewInValidRequestBody.Error())
		ginCtx.IndentedJSON(http.StatusBadRequest, FailedResult{FailedReason: gfpError.PreviewInValidRequestBody.Message})
		return
	}

	// 여러 건중 하나라도 걸리면, 400 에러
	for _, previewRequestBody := range previewRequestBodys {
		// [Validation] 3-1. adUnitId 가 없는 경우, 400 에러 (공통)
		// [Validation] 3-2. previewAdRequestUrl 가 없는 경우, 400 에러 (추가/수정)
		// [Validation] 3-3. userIds, adIds, loginCookies, browserCookies 가 모두 없는 경우, 400 에러 (공통)
		if err := previewValidator.ValidateRequestBody(ginCtx, &previewRequestBody); err != nil {
			entry.Error(err.Error())
			ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
			return
		}
	}

	var targets []PreviewRequestBody
	var failedResults []FailedResult

	// [Validation] 4. userIds, adIds, loginCookies, browserCookies 중 하나라도 이미 등록된 게 있는 경우, 500 에러 (공통)
	for _, previewRequestBody := range previewRequestBodys {
		if failedUser, err := previewValidator.ValidateMatchedUser(ginCtx, &previewRequestBody, adProviderCd); err != nil {
			failedResults = append(failedResults, FailedResult{
				RequestId:    previewRequestBody.RequestId,
				FailedReason: err.Message,
				FailedUser:   failedUser,
			})
		} else {
			targets = append(targets, previewRequestBody)
		}
	}

	for _, target := range targets {
		entry.Debugf("\n\n target= %+v \n", target)
	}

	// 미리보기 다건 수정
	if len(targets) > 0 {
		if err := previewService.Update(ginCtx, adProviderCd, &targets); err != nil {
			for _, target := range targets {
				failedResults = append(failedResults, FailedResult{
					RequestId:    target.RequestId,
					FailedReason: err.Message,
				})
			}
		}
	}

	if len(failedResults) > 0 {
		ginCtx.IndentedJSON(http.StatusInternalServerError, failedResults)
	}

	setZookeeperData(ginCtx)
}

/**
	@apiVersion 1.0.0
	@api {delete} /api/preview 미리보기 단건 삭제
	@apiName DeletePreview
	@apiGroup Preview
	@apiDescription 미리보기 단건 삭제
		AdUnitId에 동일 DSP로 등록된 MatchedUser가 있다면, 삭제 가능
		AdUnitId에 다른 DSP로 등록된 MatchedUser가 있다면, 삭제 불가

	@apiHeader {String} Content-Type=application/json

	@apiParam {String} userId API User ID<br/>ex) "admin"
	@apiParam {String{64}} encodedUserId Encoded API User ID<br/>ex) "1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1"

	@apiParam {String} adProviderCd GFP AdProvider Code
	@apiParam (Request Body) {String} adUnitId SSP AdUnit Id
	@apiParam (Request Body) {Object} matchedUser MatchedUser Object
	@apiParam (Request Body) {String[]} matchedUser.userIds publisher 전용 User Id 목록
	@apiParam (Request Body) {String[]} matchedUser.adIds AdId 목록
	@apiParam (Request Body) {String[]} matchedUser.loginCookies Naver L-Cookie 목록
	@apiParam (Request Body) {String[]} matchedUser.browserCookies Naver B-Cookie 목록

	@apiParamExample {json} Request-Example:
		{
			"adUnitId": "adUnitId_001",
			"previewAdRequestUrl": "naver.com",
			"matchedUser": {
				"userIds": ["uid01", "uid02"],
				"browserCookies": ["bcookie01"]
			}
 		}

	@apiSuccess 204 No Content.
	@apiSuccessExample {json} Success-Response-Example: 미리보기 단건 삭제
		HTTP/1.1 200 OK

	@apiError (400) BadRequest
						DSP 코드가 영문자/숫자/언더바 로 구성되지 않은 경우<br>
						request body에 필수 값이 없는 경우<br>
	@apiError (403) Forbidden Not authenticated. You need to register server IP in the pasta acl consumer.
	@apiError (500) InternalServerError
						다른 DSP로 등록된 MatchedUser가 있는 경우<br>
						DB 오류<br>

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"failedReason": "Request Body가 유효하지 않습니다."
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		{
			"failedReason": "이미 등록된 MatchedUser가 존재합니다.",
			"failedUser":{
				"userIds":["uid01"],
				"browserCookies":[
					"bcookie01"
				]
			}
		}

	@apiSampleRequest off
*/
func (controller *PreviewController) DeletePreview(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[Delete] 미리보기 단건 삭제")

	// [Validation] 1-1. adProviderCd 가 없거나 영문/숫자/언더바 로 구성되어 있지 않는 경우, 400 에러 (공통)
	adProviderCd := ginCtx.Query("adProviderCd")
	if err := previewValidator.ValidateAdProviderCd(adProviderCd); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 1-2. 권한이 없는 adProviderCd인 경우, 401 에러 (공통)
	userId := ginCtx.Query("userId")
	apiUser, err := triplea.GetApiUser(ginCtx, userId)
	if err != nil {
		param := map[string]string{"err": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.PreviewCommon.BizError(param))
		return
	}
	if err := previewValidator.CheckAuthorizedByAdProviderCd(ginCtx, adProviderCd, apiUser.AdpIds); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 2. RequestBody에 문제가 있는 경우, 400 에러 (공통)
	var previewRequestBody PreviewRequestBody
	if err := ginCtx.ShouldBindBodyWith(&previewRequestBody, binding.JSON); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusBadRequest, FailedResult{FailedReason: gfpError.PreviewInValidRequestBody.Message})
		return
	}

	// [Validation] 3-1. adUnitId 가 없는 경우, 400 에러 (공통)
	// [Validation] 3-2. previewAdRequestUrl 가 없는 경우, 400 에러 (추가/수정)
	// [Validation] 3-3. userIds, adIds, loginCookies, browserCookies 가 모두 없는 경우, 400 에러 (공통)
	if err := previewValidator.ValidateRequestBody(ginCtx, &previewRequestBody); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	entry.Debugf("\n\n AdProviderCd= %s \nRequestBody= %+v \n", adProviderCd, previewRequestBody)

	// [Validation] 4. userIds, adIds, loginCookies, browserCookies 중 하나라도 이미 등록된 게 있는 경우, 500 에러 (공통)
	if failedUser, err := previewValidator.ValidateMatchedUser(ginCtx, &previewRequestBody, adProviderCd); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusInternalServerError, FailedResult{FailedReason: err.Message, FailedUser: failedUser})
		return
	}

	// 미리보기 단건 삭제
	targets := []PreviewRequestBody{previewRequestBody}
	if err := previewService.Delete(ginCtx, adProviderCd, &targets); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusInternalServerError, FailedResult{FailedReason: err.Message})
		return
	}

	setZookeeperData(ginCtx)
}

/**
 	@apiVersion 1.0.0
	@api {delete} /api/previews 미리보기 다건 삭제
	@apiName DeletePreviews
	@apiGroup Preview
	@apiDescription 미리보기 다건 삭제
		AdUnitId에 동일 DSP로 등록된 MatchedUser가 있다면, 삭제 가능
		AdUnitId에 다른 DSP로 등록된 MatchedUser가 있다면, 삭제 불가

	@apiHeader {String} Content-Type=application/json

	@apiParam {String} userId API User ID<br/>ex) "admin"
	@apiParam {String{64}} encodedUserId Encoded API User ID<br/>ex) "1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1"

	@apiParam {String} adProviderCd GFP AdProvider Code
	@apiParam (Request Body) {Object[]} previewRequestBody
	@apiParam (Request Body) {String} previewRequestBody.requestId 서비스 requestId
	@apiParam (Request Body) {String} previewRequestBody.adUnitId SSP AdUnit Id
	@apiParam (Request Body) {Object} previewRequestBody.matchedUser MatchedUser Object
	@apiParam (Request Body) {String[]} previewRequestBody.matchedUser.userIds publisher 전용 User Id 목록
	@apiParam (Request Body) {String[]} previewRequestBody.matchedUser.adIds AdId 목록
	@apiParam (Request Body) {String[]} previewRequestBody.matchedUser.loginCookies Naver L-Cookie 목록
	@apiParam (Request Body) {String[]} previewRequestBody.matchedUser.browserCookies Naver B-Cookie 목록

	@apiParamExample {json} Request-Example:
		[{
			"requestId": "reqId0001"
			"adUnitId": "adUnitId_001",
			"matchedUser": {
				"userIds": ["uid01", "uid02"],
				"browserCookies": ["bcookie01"]
			}
 		}, ... ]

	@apiSuccess 204 No Content.
	@apiSuccessExample {json} Success-Response-Example: 미리보기 다건 삭제
		HTTP/1.1 200 OK

	@apiError (400) BadRequest
						DSP 코드가 영문자/숫자/언더바 로 구성되지 않은 경우<br>
						request body에 필수 값이 없는 경우<br>
	@apiError (403) Forbidden Not authenticated. You need to register server IP in the pasta acl consumer.
	@apiError (500) InternalServerError
						다른 DSP로 등록된 MatchedUser가 있는 경우<br>
						DB 오류<br>

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"failedReason": "Request Body가 유효하지 않습니다."
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		[{
			"requestId": "reqId0001",
			"failedReason": "이미 등록된 MatchedUser가 존재합니다.",
			"failedUser":{
				"userIds":["uid01"],
				"browserCookies":[
					"bcookie01"
				]
			}
		}, ... ]

	@apiSampleRequest off
*/
func (controller *PreviewController) DeletePreviews(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[Delete] 미리보기 다건 삭제")

	// [Validation] 1-1. adProviderCd 가 없거나 영문/숫자/언더바 로 구성되어 있지 않는 경우, 400 에러 (공통)
	adProviderCd := ginCtx.Query("adProviderCd")
	if err := previewValidator.ValidateAdProviderCd(adProviderCd); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 1-2. 권한이 없는 adProviderCd인 경우, 401 에러 (공통)
	userId := ginCtx.Query("userId")
	apiUser, err := triplea.GetApiUser(ginCtx, userId)
	if err != nil {
		param := map[string]string{"err": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.PreviewCommon.BizError(param))
		return
	}
	if err := previewValidator.CheckAuthorizedByAdProviderCd(ginCtx, adProviderCd, apiUser.AdpIds); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
		return
	}

	// [Validation] 2-1. RequestBody에 문제가 있는 경우, 400 에러 (공통)
	var previewRequestBodys []PreviewRequestBody
	if err := ginCtx.ShouldBindBodyWith(&previewRequestBodys, binding.JSON); err != nil {
		entry.Error(err.Error())
		ginCtx.IndentedJSON(http.StatusBadRequest, FailedResult{FailedReason: gfpError.PreviewInValidRequestBody.Message})
		return
	}

	// [Validation] 2-2. RequestBody가 빈 배열인 경우, 400 에러 (공통)
	if len(previewRequestBodys) < 1 {
		entry.Error(gfpError.PreviewInValidRequestBody.Error())
		ginCtx.IndentedJSON(http.StatusBadRequest, FailedResult{FailedReason: gfpError.PreviewInValidRequestBody.Message})
		return
	}

	// 여러 건중 하나라도 걸리면, 400 에러
	for _, previewRequestBody := range previewRequestBodys {
		// [Validation] 3-1. adUnitId 가 없는 경우, 400 에러 (공통)
		// [Validation] 3-2. previewAdRequestUrl 가 없는 경우, 400 에러 (추가/수정)
		// [Validation] 3-3. userIds, adIds, loginCookies, browserCookies 가 모두 없는 경우, 400 에러 (공통)
		if err := previewValidator.ValidateRequestBody(ginCtx, &previewRequestBody); err != nil {
			entry.Error(err.Error())
			ginCtx.IndentedJSON(err.Status, FailedResult{FailedReason: err.Message})
			return
		}
	}

	entry.Debugf("\n\n AdProviderCd= %s \n previewRequestBodys= %+v \n", adProviderCd, previewRequestBodys)

	var targets []PreviewRequestBody
	var failedResults []FailedResult

	// [Validation] 4. userIds, adIds, loginCookies, browserCookies 중 하나라도 이미 등록된 게 있는 경우, 500 에러 (공통)
	for _, previewRequestBody := range previewRequestBodys {
		if failedUser, err := previewValidator.ValidateMatchedUser(ginCtx, &previewRequestBody, adProviderCd); err != nil {
			failedResults = append(failedResults, FailedResult{
				RequestId:    previewRequestBody.RequestId,
				FailedReason: err.Message,
				FailedUser:   failedUser,
			})
		} else {
			targets = append(targets, previewRequestBody)
		}
	}

	for _, target := range targets {
		entry.Debugf("\n\n target= %+v \n", target)
	}

	// 미리보기 다건 삭제
	if err := previewService.Delete(ginCtx, adProviderCd, &targets); err != nil {
		for _, target := range targets {
			failedResults = append(failedResults, FailedResult{
				RequestId:    target.RequestId,
				FailedReason: err.Message,
			})
		}
	}

	if len(failedResults) > 0 {
		ginCtx.IndentedJSON(http.StatusInternalServerError, failedResults)
	}

	setZookeeperData(ginCtx)
}

func (controller *PreviewController) SetZookeeperData(ginCtx *gin.Context) {
	setZookeeperData(ginCtx)
}

/* zookeeper SetData */
func setZookeeperData(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[setZookeeperData] Zookeeper 동기화 요청")

	previewPath := config.GetConfig("zookeeper.path.preview").(string)
	currentTime := time.Now().Format("20060102150405")

	// zookeeper 동기화 요청
	zookeeper.SetData(previewPath, currentTime)

	//// zookeeper old 동기화 요청
	//zookeeper_old.SetData(previewPath, currentTime)
}

/* zookeeper GetData */
func (controller *PreviewController) GetZookeeperData(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[GetZookeeperData] Zookeeper 수동 동기화 요청")

	// 데이터 잘 셋팅되는지 확인하는 용도
	zookeeper.GetData(config.GetConfig("zookeeper.path.preview").(string))

	//// zookeeper old 데이터 잘 셋팅되는지 확인하는 용도
	//zookeeper_old.GetData(config.GetConfig("zookeeper.path.preview").(string))
}
