package preview

import (
	"github.com/gin-gonic/gin"
)

var (
	previewController = PreviewController{}
	previewService    = PreviewService{}
	previewValidator  = PreviewValidator{}
)

func SetUpRoute(router *gin.Engine) {

	previewGroup := router.Group("api/preview")

	// preview 조회
	previewGroup.GET("", previewController.List)

	// preview 단건 추가
	previewGroup.POST("", previewController.AddPreview)

	// preview 단건 수정
	previewGroup.PUT("", previewController.UpdatePreview)

	// preview 단건 삭제
	previewGroup.DELETE("", previewController.DeletePreview)

	previewsGroup := router.Group("api/previews")

	// preview 다건 추가
	previewsGroup.POST("", previewController.AddPreviews)

	// preview 다건 수정
	previewsGroup.PUT("", previewController.UpdatePreviews)

	// preview 다건 삭제
	previewsGroup.DELETE("", previewController.DeletePreviews)

	syncGroup := router.Group("api/zookeeper/preview")

	// zookeeper 수동 동기화 요청
	syncGroup.GET("set/data", previewController.SetZookeeperData)

	//  동기화 데이터 받기
	syncGroup.GET("get/data", previewController.GetZookeeperData)
}
