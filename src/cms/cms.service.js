'use strict';

import fetch from 'node-fetch';
import _ from 'lodash';
import nodemailer from 'nodemailer';

import config from '../config/config';
import * as logger from '../utils/logger.util';

module.exports.getEmailAddrs = async (userIds) => {
	const cmsApiUrl = config.cms.api.url;
	const cmsApiMethod = config.cms.api.method;
	const cmsApiTimeout = config.cms.api.timeout;

	const params = new URLSearchParams();
	params.append('userIds', userIds);

	const response = await fetch(cmsApiUrl, {
		method: 'get',
		timeout: cmsApiTimeout,
		body: params
	});

	const data = await response.json();
	return data;

};