import { DynamicModule, Module } from '@nestjs/common';
import { NclavisService } from './nclavis.service';
import { NclavisController } from './nclavis.controller';

@Module({})
export class NclavisModule {
  static forRoot() {
    const module: DynamicModule = {
      global: true,
      module: NclavisModule,
      providers: [NclavisService],
      exports: [NclavisService],
    };

    if (process.env?.NAM_API_NCLAVIS_TEST) {
      module.controllers = [NclavisController];
    }

    return module;
  }
}
