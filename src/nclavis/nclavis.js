import fetch from 'node-fetch';
import * as _ from 'lodash';
import config from '../config/config';
import * as logger from '../utils/logger.util';

const URL = `${config.nclavis.url}/${config.nclavis.keyResourceId}`;

module.exports.encryptInline = async (plainText) => {
	const url = `${URL}/encrypt/inline`;

	const headers = {
		'Content-Type': 'application/json',
		'Accept': 'application/json;charset=UTF-8'
	};
	if (process.env.NODE_ENV === 'local') {
		headers['nclavis-access-token'] = config.nclavis.accessToken;
	}

	const body = {
		plainText: plainText
	}

	const options = {
		method: 'POST',
		headers: headers,
		body: JSON.stringify(body)
	};


	// nclavis 암호화
	const response = await fetch(url, options);
	if (!_.isEqual(response.status, 200)) {
		const result = await response.text();
		const msg = `nclavis 요청 실패 ::: statusCode=${response.status} result=${JSON.stringify(result, null, 2)} url=${url}`;
		logger.error(msg);
		return {
			message: msg,
			url,
			status: response.status,
			err: result
		};
	}


	const res = await response.json();
	return res.inlineCipherText;
}

module.exports.decryptInline = async (cipherText) => {
	const url = `${URL}/decrypt/inline`;

	const headers = {
		'Content-Type': 'application/json',
		'Accept': 'application/json;charset=UTF-8'
	};
	if (process.env.NODE_ENV === 'local') {
		headers['nclavis-access-token'] = config.nclavis.accessToken;
	}

	const body = {
		inlineCipherText: cipherText
	}

	const options = {
		method: 'POST',
		headers: headers,
		body: JSON.stringify(body)
	};

	// nclavis 복호화
	const response = await fetch(url, options);
	if (!_.isEqual(response.status, 200)) {
		const result = await response.text();
		const msg = `nclavis 요청 실패 ::: statusCode=${response.status} result=${JSON.stringify(result, null, 2)} url=${url}`;
		logger.error(msg);
		return {
			message: msg,
			url,
			status: response.status,
			err: result
		};
	}

	const res = await response.json();
	return res.plainText;
}
