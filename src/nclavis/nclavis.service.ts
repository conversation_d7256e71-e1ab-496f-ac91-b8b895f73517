import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import axiosRetry from 'axios-retry';
import https from 'https';

const NEOID_TOKEN_ENV = 'NCLAVIS_NEOID_TOKEN';

@Injectable()
export class NclavisService {
  private readonly logger = new Logger(NclavisService.name);

  private serviceUrl: string;
  private resourceId: string;
  private authn: string;
  private neoidToken: string;
  private client: AxiosInstance;

  constructor(private readonly config: ConfigService) {
    this.serviceUrl = config.get<string>('nclavis.service_url');
    this.resourceId = config.get<string>('nclavis.resource_id');
    this.authn = config.get<string>('nclavis.authn');

    const insecureHttps = config.get<boolean>('nclavis.insecureHttps', false);

    const options: AxiosRequestConfig = {
      baseURL: `${this.serviceUrl}/${this.resourceId}`,
      timeout: 500,
    };
    if (insecureHttps) {
      this.logger.warn('insecure HTTPS configured');

      options.httpsAgent = new https.Agent({
        rejectUnauthorized: false,
        checkServerIdentity: (hostname, cert) => {
          return undefined;
        },
      });
    }

    this.client = axios.create(options);
    axiosRetry(this.client, {
      retries: 3,
      retryDelay: (retryCount) => {
        return retryCount * 100;
      },
    });

    this.neoidToken = '';
    if (this.authn === 'neoid' && process.env[NEOID_TOKEN_ENV]) {
      this.logger.verbose('neoid authn enabled');
      // this.neoidToken = process.env[NEOID_TOKEN_ENV];
    }
    this.neoidToken = "EU3KsMJZhPJgkkz956t13K47qGtb4VSj2YOivWlj6jnLpmtEPfc1sYZ8R7hSsNov1K7g4BzHDZTogsSA9GnltvYbKFCS2OGpIP3igENdd+D5nu2C1YuV0ojWxfDmzp6V7uyZ+P8YsdrmEQTgaIk0EPszUI/pwsKj2iwhydfV6VhSJ1jyXQGLFEa9MYybVDfRH/am24I/wb9/2gq7CyoB+Z8Ye917BHls7YXbynsL4gYhQ4mjaQMHGaGcernwheznWzQjxvMwAisn3u4hxcnj4RbliEJStuvcerKaoosuZG5eZVILyvXxiIXbx28Hu7+xha6gMAMnPXUIo/Fhq401b0w5EsdJVclEUXx5WGfxc6+RcKCj5Z4EKY2tvxYMT82BmVGDvOeHWTufqlCLruBmYA==";
  }

  // $ curl -X POST -H 'Content-Type: application/json' \
  // > '<service-url>/<resource-id>/encrypt/inline' -d '{plainText: "secret info"}'
  // {"inlineCipherText":"blah..blah"}
  //

  async decryptInline(cipherText: string) {
    const url = '/decrypt/inline';
    const data = { inlineCipherText: cipherText };

    const headers = {};

    if (this.neoidToken) {
      headers['nclavis-access-token'] = this.neoidToken;
    }

    return await this.client.post(url, data, { headers }).then((res) => {
      return res.data.plainText;
    });
  }

  async encryptInline(plainText: string) {
    const url = '/encrypt/inline';
    const data = { plainText: plainText };

    const headers = {};

    if (this.neoidToken) {
      headers['nclavis-access-token'] = this.neoidToken;
    }

    return await this.client.post(url, data, { headers }).then((res) => {
      return res.data.inlineCipherText;
    });
  }
}
