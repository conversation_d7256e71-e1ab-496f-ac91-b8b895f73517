export type Union<T> = T[keyof T];

/* COMMON */
export const MethodType = {
	CREATE: 'CREATE',
	UPDATE: 'UPDATE',
	LIST: 'LIST',
} as const;

export const ReportState = {
	READY: 'READY',
	IN_PROGRESS: 'IN_PROGRESS',
	FAILURE: 'FAILURE',
	COMPLETE: 'COMPLETE',
} as const;

export const SilvergreyState = {
	WAIT: 'WAIT',
	READY: 'READY',
	IN_PROGRESS: 'IN_PROGRESS',
	FAILED: 'FAILED',
	COMPLETE: 'COMPLETE',
} as const;
export type SILVERGREY_STATE = Union<typeof SilvergreyState>;

export const Status = {
	ON: 'ON',
	OFF: 'OFF',
} as const;
export type STATUS = Union<typeof Status>;

export const ReportStates = Object.keys(ReportState) as (keyof typeof ReportState)[];
export type REPORT_STATE = Union<typeof ReportState>;

export const ReportPeriods = ['D', 'M'] as const;
export type REPORT_PERIOD = Union<typeof ReportPeriods>;

export const CmsType = {
	GFP: 'GFP',
	NAM: 'NAM',
} as const;

export const AP_TIMEZONE = '-';


/* Dimension */
export const AdProvider = 'adProvider';
export const Service = 'service';
export const AdUnit = 'adUnit';
export const Country = 'country';
export const Place = 'place';
export const BiddingGroup = 'biddingGroup';
export const Deal = 'deal';
export const DeviceOs = 'deviceOs';
export const ResponseCreativeType = 'responseCreativeType';


/* Metric */
export const Impressions = 'impressions';
export const Clicks = 'clicks';
export const UsdRevenue = 'usdRevenue';
export const KrwRevenue = 'krwRevenue';
export const UsdNetRevenue = 'usdNetRevenue';
export const KrwNetRevenue = 'krwNetRevenue';
export const Ctr = 'ctr';
export const UsdCpm = 'usdCpm';
export const KrwCpm = 'krwCpm';
export const UsdCpc = 'usdCpc';
export const KrwCpc = 'krwCpc';
export const UsdNetCpm = 'usdNetCpm';
export const KrwNetCpm = 'krwNetCpm';
export const UsdNetCpc = 'usdNetCpc';
export const KrwNetCpc = 'krwNetCpc';
export const AdUnitRequests = 'adUnitRequests';
export const AdProviderRequests = 'adProviderRequests';
export const AdProviderResponses = 'adProviderResponses';
export const SspFilledRequests = 'sspFilledRequests';
export const SspImpressions = 'sspImpressions';
export const SspViewableImpressions = 'sspViewableImpressions';
export const SspClicks = 'sspClicks';
export const SspCompletions = 'sspCompletions';
export const SspCtr = 'sspCtr';
export const EstimatedMetrics = ['sspEstimatedImpressions', 'sspEstimatedUsdNetRevenue', 'sspEstimatedKrwNetRevenue', 'sspEstimatedUsdNetCpm', 'sspEstimatedKrwNetCpm', 'sspEstimatedUsdNetCpc', 'sspEstimatedKrwNetCpc'];
