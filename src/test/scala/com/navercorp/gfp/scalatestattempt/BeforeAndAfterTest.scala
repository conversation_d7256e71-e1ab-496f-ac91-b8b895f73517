package com.navercorp.gfp.scalatestattempt

import com.navercorp.gfp.helper.FunSuiteWithSparkSession
import org.junit.runner.RunWith
import org.scalatest.BeforeAndAfter
import org.scalatestplus.junit.JUnitRunner

import scala.collection.mutable.ListBuffer

/**
 *
 * BeforeAndAfterAll: 처음에 한 번, 마지막에 한번, 그래서 stackable
 * BeforeAndAfterEach: 매 테스트를 할 때 마다 수행
 */
@RunWith(classOf[JUnitRunner])
class BeforeAndAfterTest extends FunSuiteWithSparkSession with BeforeAndAfter {
    val builder  = new StringBuilder
    val buffer = new ListBuffer[String]

    before {
        builder.append("ScalaTest is ")
    }

    after {
        builder.clear()
        buffer.clear()
    }

    test("testing should be easy") {
        builder.append("easy!")
//        assert(builder.toString === "ScalaTest is easy!")
//        assert(buffer.isEmpty)
        println(s"$builder")

        buffer += "sweet"
    }
    test("it should be fun") {
        builder.append("fun!")
//        assert(builder.toString === "ScalaTest is fun!")
//        assert(buffer.isEmpty)
        println(s"$builder")
        println(s"$buffer")
    }
}
