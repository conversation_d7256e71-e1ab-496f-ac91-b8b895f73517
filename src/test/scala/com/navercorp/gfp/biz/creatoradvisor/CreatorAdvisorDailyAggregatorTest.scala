package com.navercorp.gfp.biz.creatoradvisor

import com.navercorp.gfp.core.conf.Conf
import com.navercorp.gfp.helper.FunSuiteWithSparkSession
import com.typesafe.config.Config
import org.apache.log4j.{Level, Logger}
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, DateTimeZone}
import org.junit.runner.RunWith
import org.scalatest.BeforeAndAfterAll
import org.scalatestplus.junit.JUnitRunner

import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.time.{Instant, LocalDateTime, ZoneId, ZoneOffset}

@RunWith(classOf[JUnitRunner])
class CreatorAdvisorDailyAggregatorTest extends FunSuiteWithSparkSession with BeforeAndAfterAll {
    val conf: Config = Conf.getConf()
    val creatorAdvisorDao = new CreatorAdvisorDao()

    override def beforeAll(): Unit = {
        Logger.getLogger("org.apache.spark").setLevel(Level.ERROR)
        Logger.getLogger("org.mongodb").setLevel(Level.ERROR)
    }

    test("check time test") {
        val eventTime = 1683697437
        val apDateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(eventTime), ZoneId.of("America/Los_Angeles"))
        val localEventTime2 = apDateTime.toEpochSecond(ZoneOffset.of("+09:00"))

        val gfpDateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(eventTime), ZoneId.of("Asia/Seoul"))
        val diff = gfpDateTime.until(apDateTime, ChronoUnit.SECONDS)
        val localEventTime = eventTime + diff

        //        val apLocalDateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(localEventTime), ZoneId.of("Asia/Seoul"))

        println(s"eventTime = $eventTime")
        println(s"gfpDateTime = ${gfpDateTime.toString}")
        println(s"apDateTime = ${apDateTime.toString}")
        println(s"apDateTime(YYYYMMDD) = ${apDateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"))}")
        println(s"localEventTime = $localEventTime")

        println(s"localEventTime2 = $localEventTime2")
        //        println(s"apLocalDateTime = ${apLocalDateTime.toString}")

    }

    test("check adProviderIds") {
        val adProviderIds = creatorAdvisorDao.getAdProviderIds("5be0f4c7ad288d002bf54dc2")
        println(s"adProviderIds = $adProviderIds")

        val tzMap = creatorAdvisorDao.getAdProviderTimezoneMap(adProviderIds)

        println(s"tzMap = ${tzMap.size}")
        tzMap.foreach(tz => println(tz))

    }

    test("check timezone") {
        // snow = 5f61e7b0c5adac0018e2b5c1
        // apollo = 5be0f4c7ad288d002bf54dc2
        val adProviderIds = creatorAdvisorDao.getAdProviderIds("5be0f4c7ad288d002bf54dc2")
        println(s"adProviderIds = $adProviderIds")

        val tzMap = creatorAdvisorDao.getAdProviderTimezoneMap(adProviderIds)

        val date = "20230502"
        val toTimezone = DateTimeZone.forID("Asia/Seoul")

        val result = tzMap.values.toSet.toSeq.map { tz: String =>
            val dateTimeZone = DateTimeZone.forID(tz)
            println(s"$dateTimeZone, ${dateTimeZone.getOffset(new DateTime())}")

            dateTimeZone.getOffset(new DateTime(date, toTimezone)) -> dateTimeZone
        }.sortBy(_._1).map(_._2)

        val startDateTz = result.last
        val endDateTz = result.head


        // 날짜 포맷
        val tFormat = DateTimeFormat.forPattern("yyyyMMdd HH")

        // AP 타임존 시간 datetime
        val tStartDt = tFormat.parseDateTime(s"$date 00").withZoneRetainFields(startDateTz)
        val tEndDt = tFormat.parseDateTime(s"$date 23").withZoneRetainFields(endDateTz)

        println(tStartDt, tEndDt)

        // 한국시간 datetime
        val kStartDt = new DateTime(tStartDt, toTimezone)
        val kEndDt = new DateTime(tEndDt, toTimezone)


        println(kStartDt, kEndDt)

    }


    test("check ") {
        val rsKeyValue = "test:2134,bid:,naverId:242"

        // kv = bid:xxxxx
        val kvMap = rsKeyValue.split(",").map(_.split(":")).map { kv =>
            val value = if (kv.length == 2) kv(1) else "-"
            kv(0) -> value
        }.toMap

        println(s"bid = ${kvMap.getOrElse("bid", "")}")

        val keyValue = rsKeyValue.split(",").filter(_.indexOf("bid:") == 0).flatMap(_.split(":"))
        val value = if (keyValue.nonEmpty && keyValue.length == 2) keyValue(1) else "-"

        println(s"key= bid, value = $value")
    }

}
