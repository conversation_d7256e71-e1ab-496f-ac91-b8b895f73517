package com.navercorp.gfp.biz.silvergrey

import org.apache.logging.log4j.{LogManager, Logger}
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTimeZone, Period}
import org.junit.runner.RunWith
import org.scalatest.BeforeAndAfterAll
import org.scalatestplus.junit.JUnitRunner

import com.navercorp.gfp.biz.silvergrey.NonRkGfpDailyAggregator.getDateTimeRange
import com.navercorp.gfp.helper.FunSuiteWithSparkSession

@RunWith(classOf[JUnitRunner])
class NonRkGfpDailyAggregatorTest extends FunSuiteWithSparkSession with BeforeAndAfterAll {
    protected val logger: Logger = LogManager.getLogger(this.getClass)

    test("check datetime isBefore") {
        val startDate = "20220101"
        val endDate = "20220115"

        // 날짜 포맷
        val tFormat = DateTimeFormat.forPattern("yyyyMMdd")

        // AP 타임존 시간 datetime
        val tStartDt = tFormat.parseDateTime(s"$startDate")
        val tEndDt = tFormat.parseDateTime(s"$endDate")

        assert(!tStartDt.isAfter(tEndDt))
    }

    test("test dtRange") {
        val dtRange1 = getDateTimeRange("20240221", Period.hours(1), DateTimeZone.forID("America/Los_Angeles"))
        logger.debug(s"20240221 == ${dtRange1.head} ~ ${dtRange1.last}")

        val dtRange2 = getDateTimeRange("20240310", Period.hours(1), DateTimeZone.forID("America/Los_Angeles"))
        logger.debug(s"20240310 == ${dtRange2.head} ~ ${dtRange2.last}")


        val dtRange22 = getDateTimeRange("20241101", Period.hours(1), DateTimeZone.forID("America/Los_Angeles"))
        logger.debug(s"20241101 == ${dtRange22.head} ~ ${dtRange22.last}")


        val dtRange3 = getDateTimeRange("20241103", Period.hours(1), DateTimeZone.forID("America/Los_Angeles"))
        logger.debug(s"20241103 == ${dtRange3.head} ~ ${dtRange3.last}")


        val dtRange4 = getDateTimeRange("20241203", Period.hours(1), DateTimeZone.forID("America/Los_Angeles"))
        logger.debug(s"20241203 == ${dtRange4.head} ~ ${dtRange4.last}")

    }
}
