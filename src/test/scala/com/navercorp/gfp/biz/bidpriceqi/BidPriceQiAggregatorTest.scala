package com.navercorp.gfp.biz.bidpriceqi

import com.typesafe.config.Config
import org.apache.logging.log4j.Level
import org.apache.logging.log4j.core.config.Configurator
import org.junit.runner.RunWith
import org.scalatest.BeforeAndAfterAll
import org.scalatestplus.junit.JUnitRunner

import com.navercorp.gfp.core.base.model.{ClientLog, RkStatsLog}
import com.navercorp.gfp.core.conf.Conf
import com.navercorp.gfp.core.database.Database
import com.navercorp.gfp.helper.FunSuiteWithSparkSession
import com.navercorp.gfp.meta.publisher.PublisherDao

@RunWith(classOf[JUnitRunner])
class BidPriceQiAggregatorTest extends FunSuiteWithSparkSession with BeforeAndAfterAll {
	val pubDao = new PublisherDao
	val conf: Config = Conf.getConf()

	override def beforeAll(): Unit = {
		Configurator.setLevel("org.apache.spark", Level.ERROR)
		Configurator.setLevel("org.mongodb", Level.ERROR)
	}

	test("bid price qi aggregator") {
		implicit val spark = createSparkSession(this.getClass.getName)

		import spark.implicits._

		println(s"DecryptedDatabaseUri: ${Database.getDecryptedDatabaseUri}")

		/*
		ClientLog(test: Int,
					 isValid: String,
					 eventId: Int,
					 requestId: String,
					 publisherId: String,
					 serviceId: String,
					 adUnitId: String,
					 adProviderId: String,
					 adProviderPlaceId: String,
					 biddingGroupId: String,
					 bidPriceInKRW: Float,
					 bidPriceInUSD: Float,
					 stat: Int,
					 connectionType: String
					)
		 */
		val clientDf = Seq(
			//            ClientLog(0, "1", 1, "req1", "6282366348a245001dde680f", "svc1", "au1", "61cbdc7ac2873b0d7613fc14", "5bf5166df39ecc002ffc7755", "bg1", 2400.0f, 0.3f, 1, "C2S"),
			//            ClientLog(0, "1", 1, "req1", "628236d648e40a002b3ed3aa", "svc1", "au1", "61cbdc7ac2873b0d7613fc14", "43f5166df39ecc002ffc7756", "bg1", 1500.0f, 0.3f, 1, "C2S"),
			ClientLog(0, "1", 1, "req1", "6282371bd5774e0024459868", "svc1", "au1", "61cbdc7ac2873b0d7613fc14", "12cbdc7ac2873b0d7613fc12", "bg1", 1202.0f, 0.3f, 1, "C2S"),
			ClientLog(0, "1", 1, "req1", "6282371bd5774e0024459868", "svc1", "au2", "61cbdc7ac2873b0d7613fc14", "78cbdc7ac2873b0d7613fc12", "bg1", 1200.0f, 0.3f, 1, "C2S"),

			//            ClientLog(0, "1", 1, "req1", "6282366348a245001dde680f", "svc1", "AOS_BANNER_GFAExt-N533743227", "61cbdc7ac2873b0d7613fc14", "62838067fe3209003850ae34", "bg1", 1500.0f, 0.3f, 1, "C2S"),
			//            ClientLog(0, "1", 1, "req1", "6282366348a245001dde680f", "svc1", "AOS_NATIVE_GFAExt-N533743227", "61cbdc7ac2873b0d7613fc14", "62838067fe3209003850ae35", "bg1", 1200.0f, 0.3f, 1, "C2S"),
		).toDF()

		/*
		 RkStatsLog(publisherId: String,
					  serviceId: String,
					  adUnitId: String,
					  adProviderId: String,
					  adProviderPlaceId: String,
					  impressions: Long,
					  clicks: Long,
					  revenueUSD: Double,
					  netRevenueUSD: Double,
					  revenueKRW: Double,
					  netRevenueKRW: Double,
					)
		 */
		val rkDf = Seq(
			//            RkStatsLog("6282366348a245001dde680f", "svc1", "au1", "61cbdc7ac2873b0d7613fc14", "5bf5166df39ecc002ffc7755", 1, 1, 1.0, 2.0, 3.0, 2000.0),
			//            RkStatsLog("628236d648e40a002b3ed3aa", "svc1", "au1", "61cbdc7ac2873b0d7613fc14", "43f5166df39ecc002ffc7756", 2, 2, 2.0, 3.0, 4.0, 500.0),
			//            RkStatsLog("6282371bd5774e0024459868", "svc1", "au1", "61cbdc7ac2873b0d7613fc14", "12cbdc7ac2873b0d7613fc12", 1, 1, 3.0, 4.0, 5.0, 600.0),
			RkStatsLog("6282371bd5774e0024459868", "svc3", "au3", "61cbdc7ac2873b0d7613fc14", "33cbdc7ac2873b0d7613fc12", 1, 1, 3.0, 4.0, 5.0, 700.0),

			//            RkStatsLog("pub1", "svc1", "au1", "61cbdc7ac2873b0d7613fc14", "5bf5166df39ecc002ffc7756", 1, 1, 1.0, 2.0, 3.0, 4.0),
			//            RkStatsLog("pub1", "svc1", "au1", "61cbdc7ac2873b0d7613fc14", "5bf5166df39ecc002ffc7756", 1, 1, 1.0, 2.0, 3.0, 4.0),

			//            RkStatsLog("6282366348a245001dde680f", "svc1", "AOS_BANNER_GFAExt-N533743227", "61cbdc7ac2873b0d7613fc14", "62838067fe3209003850ae34", 1, 1, 1.0, 2.0, 3.0, 500.0),
			//            RkStatsLog("6282366348a245001dde680f", "svc1", "AOS_NATIVE_GFAExt-N533743227", "61cbdc7ac2873b0d7613fc14", "62838067fe3209003850ae35", 1, 1, 1.0, 2.0, 3.0, 600.0),
		).toDF()

		val pubIds = Vector("6282366348a245001dde680f", "628236d648e40a002b3ed3aa", "6282371bd5774e0024459868")
		val apIds = Seq("61cbdc7ac2873b0d7613fc14")

		val date = "20220701"
		val aggregator = new BidPriceQiAggregator(date, clientDf, rkDf, pubIds, apIds, 7)
		val aggDf = aggregator.aggregate()

		//        // 기존 데이터 삭제
		//        aggregator.delete(aggregator.getFuturesForDelete())

		//        // MongoDB에 저장
		//        aggregator.write(aggDf)

		spark.close()

	}
}
