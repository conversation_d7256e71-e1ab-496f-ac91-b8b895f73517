package com.navercorp.gfp.biz.exchangerate

import com.navercorp.gfp.helper.FunSuiteWithSparkSession
import org.apache.log4j.{Level, Logger}
import org.apache.spark.sql.SparkSession
import org.junit.runner.RunWith
import org.scalatest.BeforeAndAfterAll
import org.scalatestplus.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class ExchageRateAggregatorTest extends FunSuiteWithSparkSession with BeforeAndAfterAll {

    override def beforeAll(): Unit = {
        Logger.getLogger("org.apache.spark").setLevel(Level.ERROR)
        Logger.getLogger("org.mongodb").setLevel(Level.ERROR)
    }

    test("check aggregate result") {
        withSparkSession(this.getClass.getSimpleName) { implicit spark: SparkSession =>
            import spark.implicits._

            val aggregator = new ExchangeRateAggregator("20220228")

            val df = aggregator.aggregate()
            df.show()

            println(s"df.take(1) = ${df.take(1).toList.toString()}")
        }

    }

//    test("check date range") {
//        withSparkSession(this.getClass.getSimpleName) { implicit spark: SparkSession =>
//            import spark.implicits._
//
//            val aggregator = new ExchangeRateAggregator("20220302")
//
//            val dateRage = aggregator.dateStrRange
//
//            println(s"dateRage = $dateRage")
//        }
//
//    }
}
