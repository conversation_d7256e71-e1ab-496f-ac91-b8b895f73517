package com.navercorp.gfp.util

import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.spark.sql.SparkSession
import org.junit.runner.RunWith
import org.scalatest.BeforeAndAfterAll
import org.scalatestplus.junit.JUnitRunner

import com.navercorp.gfp.core.BaseEnv.HADOOP_NAME_SERVICE
import com.navercorp.gfp.helper.FunSuiteWithSparkSession

@RunWith(classOf[JUnitRunner])
class HdfsUtilTest extends FunSuiteWithSparkSession with BeforeAndAfterAll {
	/**
	 * hdfs 초기화
	 *
	 * @return FileSystem
	 */
	def initHadoopFileSystem(spark: SparkSession): FileSystem = {
		val hadoopConf = spark.sparkContext.hadoopConfiguration
		hadoopConf.set("fs.defaultFS", HADOOP_NAME_SERVICE)
		hadoopConf.setBoolean("fs.hdfs.impl.disable.cache", true)

		try {
			FileSystem.get(hadoopConf)
		} catch {
			case ex: Exception =>
				ex.printStackTrace()
				throw ex;
		}
	}

	test("hdfs util deleteOnExist") {
		val spark = createSparkSession("sylph")
		val hdfs = initHadoopFileSystem(spark)
		HdfsUtil.delete(hdfs, "/data/log/gfp/biz.monthly/2022/11/_AU_SUCCESS")
	}

	test("hdfs rename") {
		val spark = createSparkSession("sylph")
		val hdfs = initHadoopFileSystem(spark)
		val result = hdfs.rename(
			new Path("hdfs://bizcloud/user/gfp-data/zircon/subsilver/compaction/2024/01/15/00/er-ssp-client/_adProviderId=636105a8b6bab324bc43630e/_publisherId=5f61e7b0c5adac0018e2b5c1"),
			new Path("hdfs://bizcloud/user/gfp-data/zircon/subsilver/warehouse/2024/01/15/00/er-ssp-client/_adProviderId=636105a8b6bab324bc43630e/_publisherId=5f61e7b0c5adac0018e2b5c1")
		)
		println(s"rename result=$result")
	}

}
