package com.navercorp.gfp.meta.adproviderinfo

import scala.collection.JavaConverters._

import com.mongodb.client.AggregateIterable
import com.mongodb.client.model.Filters
import com.mongodb.client.model.Projections.include
import org.bson
import org.junit.runner.RunWith
import org.mongodb.scala.model.Aggregates
import org.mongodb.scala.model.Aggregates._
import org.scalamock.scalatest.MockFactory
import org.scalatest.BeforeAndAfter
import org.scalatestplus.junit.JUnitRunner

import com.navercorp.gfp.helper.FunSuiteWithSparkSession
//import org.bson.Document
import org.bson.types.ObjectId

import com.navercorp.gfp.core.database.Database


/**
 *
 * BeforeAndAfterAll: 처음에 한 번, 마지막에 한번, 그래서 stackable
 * BeforeAndAfterEach: 매 테스트를 할 때 마다 수행
 */
@RunWith(classOf[JUnitRunner])
class AdProviderInfoDaoTest extends FunSuiteWithSparkSession with BeforeAndAfter with MockFactory {
    val infoDao = new AdProviderInfoDao
    var apTzCache = Map[String, String]()

    before {
    }

    after {
    }

    test("get adprovider timezones") {
        initAdProviderTzCache("5b74d94bc36eef272090ca56")
    }

    private def initAdProviderTzCache(pubId: String): Unit = {
        var tzList = Vector[String]()

        /*
			org.bson.Document : https://mongodb.github.io/mongo-java-driver/4.0/apidocs/bson/org/bson/Document.html
			org.bson.types.ObjectId : https://mongodb.github.io/mongo-java-driver/4.0/apidocs/bson/org/bson/types/ObjectId.html
		*/
        val pipeline = Seq(
            Aggregates.filter(Filters.eq("publisher_id", new ObjectId(pubId))),
            project(include("adProvider_id")),
            lookup("SyncAdProviders", "adProvider_id", "_id", "ap"),
            unwind("$ap"),
            group(org.mongodb.scala.bson.Document("{_id: '$ap.timezone'}"))
        )
        val docs: AggregateIterable[bson.Document] = Database.getDatabase()
            .getCollection("SyncAdProviderInfos")
            .aggregate(pipeline.asJava)

        val iter = docs.iterator()
        while (iter.hasNext) {
            val doc: org.bson.Document = iter.next().get("_id", classOf[org.bson.Document])
            val tz = doc.getString("timezone")
            tzList = tzList :+ tz
        }

        println(".........")
        tzList.foreach(println)

    }

    test("get publisherIs by adProviderId") {
        val pubIds: Vector[String] = infoDao.getPublisherIdsByAdProviderId("5b74d94bc36eef272090ca52")
        pubIds.foreach(println)

        println("\n")

        val apIds: Vector[String] = infoDao.getAdProviderIdsByPublisherId("5b85fa7416eac7001fc43abe")
        apIds.foreach(println)
    }
}
