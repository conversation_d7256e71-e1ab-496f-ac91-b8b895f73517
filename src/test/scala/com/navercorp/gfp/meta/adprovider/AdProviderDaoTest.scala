package com.navercorp.gfp.meta.adprovider

import scala.collection.JavaConverters._

import com.mongodb.client.AggregateIterable
import com.mongodb.client.model.Filters
import com.mongodb.client.model.Projections.include
import org.bson
import org.junit.runner.RunWith
import org.mongodb.scala.model.Aggregates
import org.mongodb.scala.model.Aggregates._
import org.scalamock.scalatest.MockFactory
import org.scalatest.BeforeAndAfter
import org.scalatestplus.junit.JUnitRunner

import com.navercorp.gfp.helper.FunSuiteWithSparkSession
//import org.bson.Document
import org.bson.types.ObjectId

import com.navercorp.gfp.core.database.Database


/**
 *
 * BeforeAndAfterAll: 처음에 한 번, 마지막에 한번, 그래서 stackable
 * BeforeAndAfterEach: 매 테스트를 할 때 마다 수행
 */
@RunWith(classOf[JUnitRunner])
class AdProviderDaoTest extends FunSuiteWithSparkSession with BeforeAndAfter with MockFactory {
    val dao = new AdProviderDao
    var apTzCache = Map[String, String]()

    before {
    }

    after {
    }

    test("get adprovider timezones") {
        initAdProviderTzCache()
    }

    def initAdProviderTzCache(): Unit = {
		var tzList = Vector[String]()

		/*
			org.bson.Document : https://mongodb.github.io/mongo-java-driver/4.0/apidocs/bson/org/bson/Document.html
			org.bson.types.ObjectId : https://mongodb.github.io/mongo-java-driver/4.0/apidocs/bson/org/bson/types/ObjectId.html
		*/
		val pipeline = Seq(
			Aggregates.filter(Filters.eq("publisher_id", new ObjectId("5b74d94bc36eef272090ca56"))),
			project(include("adProvider_id")),
			lookup("SyncAdProviders", "adProvider_id", "_id", "ap"),
			unwind("$ap"),
			group(org.mongodb.scala.bson.Document("{_id: '$ap.timezone'}"))
		)
		val docs: AggregateIterable[bson.Document] = Database.getDatabase()
			.getCollection("SyncAdProviderInfos")
			.aggregate(pipeline.asJava)

		val iter = docs.iterator()
		while (iter.hasNext) {
			val doc: org.bson.Document = iter.next().get("_id", classOf[org.bson.Document])
			val tz = doc.getString("_id")
			tzList = tzList :+ tz
		}

		println(".........")
		tzList.foreach(println)

	}

	test("get adprovider reportApi On") {
		val adProviderIds = dao.getAdProvidersReportApiOn().into(new java.util.ArrayList[AdProvider]()).asScala.map(_._id)

		print(adProviderIds)
	}

	test("get adprovider timezone") {
		val timezone1 = dao.getTimezone("60b5f6892634cc216ad2e3c0")
		val timezone2 = dao.getTimezone("60b5f6892634cc216ad2e312")

		println("===== start =====")
		println(timezone1)
		println(timezone2)
		println("===== end =====")
		}

	test("getGfdAdProviderIds") {
		dao.getGfdAdProviderIds()
	}
}
