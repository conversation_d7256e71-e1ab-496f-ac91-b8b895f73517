import _ from 'lodash';
import krb5 from 'krb5';
import WebHD<PERSON> from '@ssp/webhdfs';
import child_process from 'child_process';

import config from '../config/config';

import * as logger from '../utils/logger.util';
import * as nclavis from '../nclavis/nclavis';

let hdfsClient = null;

export const namespace = config.c3.namespace;

const user = config.c3.user;
const realm = config.c3.realm;

const protocol = config.c3.protocol;
const host = config.c3.host;
const port = config.c3.port;
const pathPrefix = config.c3.path_prefix;

let activeHost = host;

export async function getHdfsClient() {
	hdfsClient = await connectHdfsSpnego();

	return hdfsClient;
}

const connectHdfsSpnego = async (host = activeHost) => {
	// logger.info(`[C3][spnego] profile=${process.env.NODE_ENV} namespace=${namespace} pathPrefix=${pathPrefix} user=${user}`);

	const token = await krb5.spnego({
		ccname: config.c3.ccname,
		service_principal: `HTTP/${host}@${realm}`
	});

	// logger.info('SPNEGO token :', token);

	return WebHDFS.createClient({
		user, protocol, host, port,
		path: pathPrefix,
	}, { headers: { 'Authorization': `Negotiate ${token}` } });
}

export async function setActiveHost() {
	try {
		if (config.c3.active_namenode) {
			const namenodes = config.c3.namenodes;
			if (!_.isEmpty(namenodes)) {
				for (const nn of namenodes) {
					logger.info(`[C3] check active namenode '${nn}'`);

					if (await isActiveNamenode(nn)) {
						activeHost = nn;
						break;
					}
				}
			}
		}

		logger.info(`[C3] active host is '${activeHost}'`);
	} catch (err) {
		logger.error('[C3] active host setting error', err);
	}
}

const isActiveNamenode = nn => {
	return new Promise(async (resolve, reject) => {
		try {
			const hdfs = await connectHdfsSpnego(nn);

			// standby 로 요청 시, Error Message
			// 	 Operation category READ is not supported in state standby.
			hdfs.stat(`/user/${user}`, err => {
				if (_.isNil(err)) {
					resolve(true);
				} else {
					if (_.includes(err.message, 'standby') ||
						_.includes(err.message, 'not supported in state standby')) {

						resolve(false);
					} else {
						reject(err);
					}
				}
			});
		} catch (err) {
			reject(err);
		}
	});
}


/* 백업 */
export let hdfs_basic = null;
export async function connectHdfsBasic() {
	logger.info(`[C3][basic] profile=${process.env.NODE_ENV} namespace=${namespace} pathPrefix=${pathPrefix} user=${user}`);

	const pwd = await nclavis.decryptInline(config.c3.pw);
	const credential = Buffer.from(`gfp-data:${pwd}`).toString('base64');

	// basic auth hdfs
	hdfs_basic = WebHDFS.createClient({
		user, protocol, host, port,
		path: pathPrefix,
	}, {
		headers: { 'Authorization': `Basic ${credential}` }
	});
}

/* TEST CODE */
export async function kinit() {
	await sourceMe();

	const cmd = `kinit -kt ${config.c3.home}/${user}.keytab ${user}@${realm}`;

	try {
		const stdout = await child_process.exec(cmd);
		logger.info(`[C3] c3.kinit() :: cmd=${cmd} stdout=${stdout}`);
	} catch (err) {
		logger.error(`[C3] c3.kinit() :: cmd=${cmd} err=${err.stack}`);
		throw err;
	}
}

/* TEST CODE */
export async function sourceMe() {
	const cmd = `source ${config.c3.home}/source.me`;

	try {
		const stdout = await child_process.exec(cmd);
		logger.info(`[C3] c3.sourceMe() :: cmd=${cmd} stdout=${stdout}`);
	} catch (err) {
		logger.error(`c3.sourceMe() :: cmd=${cmd} err=${err.stack}`);
		throw err;
	}
}
