'use strict';

import moment from 'moment/moment';

import * as logger from '../utils/logger.util';
import { BusinessError } from '../common/error';

import * as abtService from '../services/report/abtest/abtest.service';
import _ from "lodash";

const LOGGER = 'abtest.controller'

/**
 * uploadReportFromHdfsToCuve : hdfs 의 report 를 Cuve 에 업로드
 *
 * @RequestMapping(value='/batch/abtest/upload')
*/
module.exports.uploadReportFromHdfsToCuve = async (ctx) => {
	logger.debug(`[${LOGGER} :: uploadReportFromHdfsToCuve] ${ctx.url} 호출됨`);

	let body = {};
	let option = {};
	const query = ctx.request.query;
	const ymd = query.ymd;

	try {
		// 파라미터 세팅
		if (ymd) {
			if (!moment(ymd, 'YYYYMMDD', true).isValid()) {
				throw new BusinessError({ message: `[${LOGGER} :: uploadReportFromHdfsToCuve] ymd format is invalid ( ymd=${ymd} )` });
			}
			option.ymd = ymd;
		} else {
			// ymd 가 없는 경우, 오늘 날짜로 처리
			option.ymd = moment().format('YYYYMMDD');
		}

		// 리포트 다운로드
		const localReportFile = await abtService.downloadReportFromHdfs(option);

		// 로컬로 다운 받은 리포트 업로드
		if(_.isEmpty(localReportFile)) {
			body = {
				code: 500,
				message: `HDFS에서 ABT Report download 실패 ( ymd=${option.ymd} )`
			};
		}
		else {
			option.localFilePath = localReportFile;
			const isComplete = await abtService.uploadReportWithCsync(option);

			if(isComplete === false){
				body = {
					code: 500,
					message: "HDFS에서 download 받은 ABT Report Cuve로 upload 실패",
				};
			}
			else {
				body = {
					code: 200,
					message: "HDFS에서 Cuve로 ABT Report upload 완료",
				};
			}
		}

		logger.debug(`[${LOGGER} :: uploadReportFromHdfsToCuve] 처리 완료`);

	} catch(e) {
		logger.error(`[${LOGGER} :: uploadReportFromHdfsToCuve] Error :: \n ${e.stack} \n`, e);

		body = {
			code: 500,
			message: `Report upload 실패 ( ymd=${option.ymd} ) :: ${e.stack}`
		};
	} finally {
		ctx.body = body;
	}
};
