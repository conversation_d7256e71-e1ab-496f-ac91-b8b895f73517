'use strict';

import * as logger from '../utils/logger.util';
import * as silverService from '../services/metal/silver.service';
import * as goldService from '../services/metal/gold.service';
import * as silvergreyService from '../services/metal/silvergrey.service';
import * as zirconService from '../services/metal/zircon.service';

/**
 * 실버 로그 최근 적재/컴팩션 일시 갱신
 * @param ctx
 * @returns {Promise<void>}
 */
module.exports.refreshRecentSilverLogYmdh = async (ctx) => {
	let body;
	try {
		logger.debug(`metal.controller.refreshRecentSilverLogYmdh() received..`);

		await silverService.refreshRecentSilverLogAccuYmdh();       // 최근 실버 로그 적재 일시 갱신
		await silverService.refreshRecentSilverLogCompactionYmdh(); // 최근 실버 로그 켬팩션 일시 갱긴

		logger.debug(`metal.controller.refreshRecentSilverLogYmdh() done`);

		body = {
			code: 200,
			message: `실버 로그 최근 적재/컴팩션 일시 갱신`
		}
	} catch (e) {
		logger.error(`metal.controller.refreshRecentSilverLogYmdh() :: ${e.stack}`);

		body = {
			code: 500,
			message: `실버 로그 최근 적재/컴팩션 일시 갱신 에러. ${e.stack}`
		}
	}

	ctx.body = body;
};

module.exports.refreshRecentGoldIndexYmdh = async (ctx) => {
	let body;
	try {
		logger.debug(`metal.controller.refreshRecentGoldIndexYmdh() received..`);

		await goldService.refreshRecentGoldIndexYmdh();

		logger.debug(`metal.controller.refreshRecentGoldIndexYmdh() done`);

		body = {
			code: 200,
			message: `골드 인덱스 최근 집계 일시 갱신`
		}
	} catch (e) {
		logger.error(`metal.controller.refreshRecentGoldIndexYmdh() :: ${e.stack}`);

		body = {
			code: 500, message: `실버 로그 최근 적재/컴팩션 일시 갱신 에러. ${e.stack}`
		}
	}

	ctx.body = body;
};

/**
 * 실버그레이 로그 최근 컴팩션 일시 갱신
 *
 * @RequestMapping(value='/batch/silvergrey/recentymd/refresh')
 */
module.exports.refreshRecentSilvergreyYmd = async (ctx) => {
	let body;

	try {
		logger.debug(`[metal.controller :: refreshRecentSilvergreyYmd] ${ctx.path} 호출됨`);

		await silvergreyService.refreshRecentSilvergreyYmd();

		body = {
			code: 200, message: `실버그레이 로그 최근 컴팩션 일시 갱신`
		};
	} catch (e) {
		logger.error(`[metal.controller :: refreshRecentSilvergreyYmd] Error :: \n ${e.stack}\n\n`);

		body = {
			code: 500, message: `실버그레이 로그 최근 컴팩션 일시 갱신 에러. ${e.stack}`
		};
	}

	ctx.body = body;
};


/**
 * Zircon B GFP 최근 컴팩션 일자 갱신
 *
 * @RequestMapping(value='/batch/zircon/b/gfp/recentymd/refresh')
 */
module.exports.refreshRecentZirconBGfpYmd = async (ctx) => {
	let body;

	try {
		logger.debug(`[metal.controller :: refreshRecentZirconBGfpYmd] ${ctx.path} 호출됨`);

		await zirconService.refreshRecentZirconBGfpYmd();

		body = {
			code: 200, message: `Zircon B GFP 최근 컴팩션 일자 갱신`
		};
	} catch (e) {
		logger.error(`[metal.controller :: refreshRecentZirconBGfpYmd] Error :: \n ${e.stack}\n\n`);

		body = {
			code: 500, message: `Zircon B GFP 최근 컴팩션 일자 갱신 에러. ${e.stack}`
		};
	}

	ctx.body = body;
};

/**
 * Zircon B 최근 컴팩션 일자 갱신
 *
 * @RequestMapping(value='/batch/zircon/b/recentymd/refresh')
 */
module.exports.refreshRecentZirconBYmd = async (ctx) => {
	let body;

	try {
		logger.debug(`[metal.controller :: refreshRecentZirconBYmd] ${ctx.path} 호출됨`);

		await zirconService.refreshRecentZirconBYmd();

		body = {
			code: 200, message: `Zircon B 최근 컴팩션 일자 갱신`
		};
	} catch (e) {
		logger.error(`[metal.controller :: refreshRecentZirconBYmd] Error :: \n ${e.stack}\n\n`);

		body = {
			code: 500, message: `Zircon B 최근 컴팩션 일자 갱신 에러. ${e.stack}`
		};
	}

	ctx.body = body;
};

/**
 * 보관 기간이 지난 Zircon B, Zircon B GFP 삭제
 *
 * @RequestMapping(value='/batch/zircon/b/delete/expired')
 */
module.exports.deleteExpiredZirconB = async (ctx) => {
	let body;

	try {
		logger.debug(`[metal.controller :: deleteExpiredZirconB] ${ctx.path} 호출됨`);

		await zirconService.deleteExpiredZirconB();

		body = {
			code: 200, message: `보관 기간이 지난 Zircon B 삭제`
		};
	} catch (e) {
		logger.error(`[metal.controller :: deleteExpiredZirconB] Error :: \n ${e.stack}\n\n`);

		body = {
			code: 500, message: `보관 기간이 지난 Zircon B 삭제 에러. ${e.stack}`
		};
	}

	ctx.body = body;
};


/**
 * Zircon R GFP 최근 컴팩션 일자 갱신
 *
 * @RequestMapping(value='/batch/zircon/r/gfp/recentymd/refresh')
 */
module.exports.refreshRecentZirconRGfpYmd = async (ctx) => {
	let body;

	try {
		logger.debug(`[metal.controller :: refreshRecentZirconRGfpYmd] ${ctx.path} 호출됨`);

		await zirconService.refreshRecentZirconRGfpYmd();

		body = {
			code: 200, message: `Zircon R GFP 최근 컴팩션 일자 갱신`
		};
	} catch (e) {
		logger.error(`[metal.controller :: refreshRecentZirconRGfpYmd] Error :: \n ${e.stack}\n\n`);

		body = {
			code: 500, message: `Zircon R GFP 최근 컴팩션 일자 갱신 에러. ${e.stack}`
		};
	}

	ctx.body = body;
};

/**
 * 보관 기간이 지난 Zircon R GFP 삭제
 *
 * @RequestMapping(value='/batch/zircon/r/gfp/delete/expired')
 */
module.exports.deleteExpiredZirconRGfp = async (ctx) => {
	let body;

	try {
		logger.debug(`[metal.controller :: deleteExpiredZirconR] ${ctx.path} 호출됨`);

		await zirconService.deleteExpiredZirconRGfp();

		body = {
			code: 200, message: `보관 기간이 지난 Zircon R 삭제`
		};
	} catch (e) {
		logger.error(`[metal.controller :: deleteExpiredZirconR] Error :: \n ${e.stack}\n\n`);

		body = {
			code: 500, message: `보관 기간이 지난 Zircon R 삭제 에러. ${e.stack}`
		};
	}

	ctx.body = body;
};

/**
 * Zircon R GFP 모니터링
 * @param ctx
 * @returns {Promise<void>}
 */
module.exports.monitorZirconRGfp = async (ctx) => {
	let body;

	try {
		logger.debug(`[metal.controller :: monitorZirconRGfp] ${ctx.path} 호출됨`);

		await zirconService.monitorZirconRGfp();

		body = {
			code: 200, message: `Zircon R GFP 모니터링 완료`
		};
	} catch (e) {
		logger.error(`[metal.controller :: monitorZirconRGfp] Error :: \n ${e.stack}\n\n`);

		body = {
			code: 500, message: `Zircon R GFP 모니터링 에러. ${e.stack}`
		};
	}

	ctx.body = body;
};
