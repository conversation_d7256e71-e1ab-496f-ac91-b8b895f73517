'use strict';

import _ from 'lodash';
import moment from 'moment';

import SBE from '../common/error-code';
import { BusinessError } from '../common/error';

import extendedLogger from '../utils/logger-ext.util';

import * as caService from '../services/report/creator-advisor/creator-advisor-report.service';

const log = extendedLogger('ca'); // 확장로거


/**
 * Creator Advisor 리포트 다운로드 (HDFS -> OWFS)
 *
 * @RequestMapping(value='/batch/report/creatoradvisor/download')
*/
module.exports.download = async ctx => {
	const txId = moment().unix();

	log.debug(`[creator-advisor-report.controller :: download] /batch/report/creatoradvisor/download 호출됨 ( txId= ${txId} )`);

	let body;

	const query = ctx.request.query;

	if (_.isEmpty(query.date) || !moment(query.date, 'YYYYMMDD', true).isValid()) {
		throw new BusinessError(SBE.Common.MissingRequiredParam, {field: 'date'});
	}

	query.isForce = (!_.isEmpty(query.isForce) && query.isForce === 'true');

	try {
		const result = await caService.checkInProgress(txId, query.date);

		// 이미 처리 중인게 있는지 체크
		if (!_.isEmpty(result)) {
			body = {
				code: 200,
				message: `Creator Advisor 리포트 다운로드 진행 중인 게 존재함 ( txId= ${txId}, date=${query.date}, _id= ${result._id} )`
			};
		} else {
			await caService.download(txId, query.date, query.isForce);

			log.debug(`[creator-advisor-report.controller :: download] 처리 완료 ( txId= ${txId}, date=${query.date} )`);

			body = {
				code: 200,
				message: `Creator Advisor 리포트 다운로드 완료 ( txId= ${txId}, date=${query.date} )`
			};
		}
	} catch(e) {
		log.error(`[creator-advisor-report.controller :: download] ERROR ( txId= ${txId} ) \n ${e.stack} \n`);

		body = {
			code: 500,
			message: `Creator Advisor 리포트 다운로드 에러 ( txId= ${txId}, date=${query.date} ) ${e.stack}`
		};
	} finally {
		ctx.body = body;
	}
};


/**
 * Creator Advisor 리포트 LOCAL 삭제
 *     - 매주 2주 전 기준으로 한달치 리포트 LOCAL 삭제
 *     - ex> 12월 8일에 실행 시, 10월 23일 ~ 11월 23일 일괄 삭제 처리됨
 *     - ex> 12월 15일에 실행 시, 10월 30일 ~ 11월 30일 일괄 삭제 처리됨
 *     - ex> 12월 22일에 실행 시, 11월 7일 ~ 12월 7일 일괄 삭제 처리됨
 *
 * @RequestMapping(value='/batch/report/creatoradvisor/delete/local')
 */
module.exports.deleteLocal = async ctx => {
	const txId = moment().unix();

	log.debug(`[creator-advisor-report.controller :: deleteLocal] /batch/report/creatoradvisor/delete/local 호출됨 ( txId= ${txId} )`);

	let body;

	try {
		await caService.deleteLocal(txId);

		body = {
			code: 200,
			message: `Creator Advisor 리포트 LOCAL 삭제 완료 ( txId= ${txId} )`
		};
	} catch(e) {
		log.error(`[creator-advisor-report.controller :: deleteLocal] ERROR ( txId= ${txId} ) \n ${e.stack} \n`);

		body = {
			code: 500,
			message: `Creator Advisor 리포트 LOCAL 삭제 에러 ( txId= ${txId} ) ${e.stack}`
		};
	} finally {
		ctx.body = body;
	}
};


/**
 * Creator Advisor 리포트 HDFS 삭제
 *     - 매주 2주 전 기준으로 한달치 리포트 HDFS 일괄 삭제
 *     - ex> 12월 8일에 실행 시, 10월 23일 ~ 11월 23일 일괄 삭제 처리됨
 *     - ex> 12월 15일에 실행 시, 10월 30일 ~ 11월 30일 일괄 삭제 처리됨
 *     - ex> 12월 22일에 실행 시, 11월 7일 ~ 12월 7일 일괄 삭제 처리됨
 *
 * @RequestMapping(value='/batch/report/creatoradvisor/delete/hdfs')
 */
module.exports.deleteHdfs = async ctx => {
	const txId = moment().unix();

	log.debug(`[creator-advisor-report.controller :: deleteHdfs] /batch/report/creatoradvisor/delete/hdfs 호출됨 ( txId= ${txId} )`);

	let body;

	try {
		await caService.deleteHdfs(txId);

		body = {
			code: 200,
			message: `Creator Advisor 리포트 HDFS 삭제 완료 ( txId= ${txId} )`
		};
	} catch(e) {
		log.error(`[creator-advisor-report.controller :: deleteHdfs] ERROR ( txId= ${txId} ) \n ${e.stack} \n`);

		body = {
			code: 500,
			message: `Creator Advisor 리포트 HDFS 삭제 에러 ( txId= ${txId} ) ${e.stack}`
		};
	} finally {
		ctx.body = body;
	}
};
