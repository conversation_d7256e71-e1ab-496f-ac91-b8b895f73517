'use strict';

import moment from 'moment';
import * as _ from 'lodash';

import extendedLogger from '../utils/logger-ext.util';
import * as reportService from '../services/report/revenue-sharing3/revenue-sharing-report3.service';
import * as monitorService from '../services/report/revenue-sharing3/revenue-sharing-report-monitor3.service';

import { BusinessError } from '../common/error';

const log = extendedLogger('rs3'); // 확장로거

/**
 *
 * @param ctx
 * @returns {Promise<void>}
 */
module.exports.prepareReports = async (ctx) => {
	const query = ctx.request.query;

	let body;
	try {
		log.debug(`[RVN-SHAR-3] revenue-sharing-report3.controller.prepareReports() date: ${query.date} received..`);

		await reportService.prepareReports(query.date);

		log.debug(`[RVN-SHAR-3] revenue-sharing-report3.controller.prepareReports() date: ${query.date} done`);

		body = { code: 200, message: `수익쉐어 리포트 준비 완료` }

	} catch (e) {
		log.error(`[RVN-SHAR-3] revenue-sharing-report3.controller.prepareReports() date: ${query.date} :: ${e.stack}`);

		body = { code: 500, message: `수익쉐어 리포트 준비. ${e.stack}` }
	}

	ctx.body = body;
};

/**
 * 여러 리포트 상태 업데이트
 * @param ctx
 * @returns {Promise<void>}
 */
module.exports.updateReportsState = async (ctx) => {
	const txId = moment().format('YYYYMMDD-HHmmssSSS');
	let body;
	const query = ctx.request.query;
	try {
		log.debug(`[RVN-SHAR-3] txId=${txId} revenue-sharing-report3.controller.updateReportsState() received..`);

		let reportIdList = query.reportIds ? query.reportIds.split(',') : [];
		await reportService.updateReportsState(txId, reportIdList);

		log.debug(`[RVN-SHAR-3] txId=${txId} revenue-sharing-report3.controller.updateReportsState() done`);

		body = { code: 200, message: `수익쉐어 리포트 상태 업데이트 완료 txId=${txId}` }

	} catch (e) {
		log.error(`[RVN-SHAR-3] txId=${txId} revenue-sharing-report3.controller.updateReportsState() :: ${e.stack}`);

		body = { code: 500, message: `수익쉐어 리포트 상태 업데이트. txId=${txId} ${e.stack}` }
	}

	ctx.body = body;
};

module.exports.makeReportTraces = async (ctx) => {
	const query = ctx.request.query;

	let body;
	try {
		const previousSensingDateTime = query.previousSensingDateTime; // YYYYMMDDHHmmss

		log.debug(`[RVN-SHAR-3] revenue-sharing-report3.controller.makeReportTraces() received.. previousSensingDateTime=${previousSensingDateTime}`);

		await reportService.makeReportTraces(previousSensingDateTime);

		log.debug(`[RVN-SHAR-3] revenue-sharing-report3.controller.makeReportTraces() done`);

		body = { code: 200, message: `수익쉐어 리포트 트레이스 생성 완료` }

	} catch (e) {
		log.error(`[RVN-SHAR-3] revenue-sharing-report3.controller.makeReportTraces() :: ${e.stack}`);

		body = { code: 500, message: `수익쉐어 리포트 트레이스 생성. ${e.stack}` }
	}

	ctx.body = body;
};

/**
 * 수익쉐어 리포트 NUBES 업로드 ( HDFS -> NUBES )
 *
 * @RequestMapping(value='/batch/rs/upload')
 */
module.exports.upload = async (ctx) => {
	const txId = moment().format('YYYYMMDD-HHmmssSSS');

	log.debug(`[RVN-SHAR-3] txId= ${txId} [revenue-sharing-report3.controller :: upload] /batch/revenuesharing/upload 호출됨`);

	let body;

	const reportId = ctx.request.query.reportId;

	if (_.isEmpty(reportId)) {
		throw new BusinessError(SBE.Common.MissingRequiredParam, { field: 'reportId' });
	}

	try {
		await reportService.upload(txId, reportId);

		log.debug(`[RVN-SHAR-3] txId= ${txId} [revenue-sharing-report3.controller :: upload] 처리 완료. reportId= ${reportId} )`);

		body = { code: 200, message: `수익쉐어 리포트 업로드 완료. txId = ${txId}, reportId= ${reportId}` };
	} catch (e) {
		log.error(`[RVN-SHAR-3] txId= ${txId} [revenue-sharing-report3.controller :: upload]\n ${e.stack} \n`);

		body = { code: 500, message: `수익쉐어 리포트 업로드 실패. txId = ${txId}, reportId= ${reportId} ${e.stack}` };
	} finally {
		ctx.body = body;
	}
};

module.exports.monitor = async (ctx) => {
	const txId = moment().format('YYYYMMDD-HHmmssSSS');

	log.debug(`[RVN-SHAR-3] txId=${txId} [revenue-sharing-report3.controller :: monitor] /batch/revenuesharing/monitor`);

	let body;

	try {
		await monitorService.monitor(txId);

		log.debug(`[RVN-SHAR-3] txId=${txId} [revenue-sharing-report3.controller :: monitor] 처리 완료`);

		body = { code: 200, message: `수익쉐어 리포트 모니터 완료. txId=${txId}` };
	} catch (e) {
		log.error(`[RVN-SHAR-3] txId=${txId} [revenue-sharing-report3.controller :: monitor] ${e.stack}`);

		body = { code: 500, message: `수익쉐어 리포트 모니터 실패. txId=${txId} ${e.stack}` };
	} finally {
		ctx.body = body;
	}
};

// module.exports.delete = async (ctx) => {
// 	const txId = moment().unix();
//
// 	log.debug(`[RVN-SHAR-3] [revenue-sharing-report3.controller :: delete] /batch/revenuesharing/delete 호출됨 ( txId= ${txId} )`);
//
// 	let body;
//
// 	try {
// 		await reportService.delete(txId);
//
// 		log.debug(`[RVN-SHAR-3] [revenue-sharing-report3.controller :: delete] 처리 완료 ( txId= ${txId} )`);
//
// 		body = {
// 			code: 200, message: `수익쉐어 리포트 삭제 완료 ( txId= ${txId} )`
// 		};
// 	} catch (e) {
// 		log.error(`[RVN-SHAR-3] [revenue-sharing-report3.controller :: delete] ERROR ( txId= ${txId} ) \n ${e.stack} \n`);
//
// 		body = {
// 			code: 500, message: `수익쉐어 리포트 삭제 실패 ( txId= ${txId} ) ${e.stack}`
// 		};
// 	} finally {
// 		ctx.body = body;
// 	}
// };
