'use strict';

import _ from 'lodash';
import moment from 'moment';

import SBE from '../common/error-code';
import {BusinessError} from '../common/error';

import * as backupRestoreService from '../services/backup-restore/backup-restore.service';

import extendedLogger from '../utils/logger-ext.util';

const log = extendedLogger('backup_restore');
const logPrefix = __filename.split('/').slice(-1)[0].slice(0, -3);


/**
 * 대상 파일 백업 (HDFS -> NUBES)
 *
 * @RequestMapping(value='/batch/backup-restore/nubes/backup')
 */
module.exports.backup = async ctx => {
	log.debug(`[${logPrefix} :: backup] ${decodeURIComponent(ctx.url)} 호출됨`);

	let body;

	const query = ctx.request.query;
	const dataType = query.target_data.toString();
	const dt = query.target_dt;
	let force = false;

	try {
		if (_.isEmpty(dataType)) {
			throw new BusinessError(SBE.Common.MissingRequiredParam, { field: 'target_data' });
		}
		if (_.isEmpty(dt) || !moment(dt, ['YYYYMM', 'YYYYMMDD', 'YYYYMMDDHH'], true).isValid()) {
			throw new BusinessError(SBE.Common.MissingRequiredParam, { field: 'target_dt' });
		}

		if (!_.isNil(query.force) && query.force === 'True') {
			force = true;
		}

		await backupRestoreService.backup(dataType, dt, force);

		body = {
			code: 200,
			message: `Nubes 백업 완료 ( target_dt=${dt} )`
		};
	} catch (e) {
		log.error(`[${logPrefix} :: backup] ERROR \n ${e.stack} \n`);
		backupRestoreService.sendFailureMail('backup', dataType, dt, e)

		body = {
			code: 500,
			message: `Nubes 백업 실패 ( target_dt=${dt} ) ${e.stack} ${e}`
		};
	} finally {
		ctx.body = body;
	}
};

/**
 * 대상 파일 복구 (NUBES -> HDFS)
 *
 * @RequestMapping(value='/batch/backup-restore/nubes/restore')
 */
module.exports.restore = async ctx => {
	log.debug(`[${logPrefix} :: restore] ${decodeURIComponent(ctx.url)} 호출됨`);

	let body;

	const query = ctx.request.query;
	const dataType = query.target_data.toString();
	const dt = query.target_dt;

	try {
		if (_.isEmpty(dataType)) {
			throw new BusinessError(SBE.Common.MissingRequiredParam, { field: 'target_data' });
		}
		if (_.isEmpty(dt) || !moment(dt, ['YYYYMM', 'YYYYMMDD', 'YYYYMMDDHH'], true).isValid()) {
			throw new BusinessError(SBE.Common.MissingRequiredParam, { field: 'target_dt' });
		}

		await backupRestoreService.restore(dataType, dt);

		body = {
			code: 200,
			message: `Nubes 파일 복구 완료 ( target_dt=${dt} )`
		};
	} catch (e) {
		log.error(`[${logPrefix} :: restore] ERROR \n ${e.stack} \n`);
		backupRestoreService.sendFailureMail('restore', dataType, dt, e)

		body = {
			code: 500,
			message: `Nubes 파일 복구 실패 ( target_dt=${dt} ) ${e.stack} ${e}`
		};
	} finally {
		ctx.body = body;
	}
};

/**
 * 대상 파일 Nubes 에서 삭제
 *
 * @RequestMapping(value='/batch/backup-restore/nubes/delete')
 */
module.exports.delete = async ctx => {
	log.debug(`[${logPrefix} :: delete] ${decodeURIComponent(ctx.url)} 호출됨`);

	let body;

	const query = ctx.request.query;
	const dataType = query.target_data.toString();
	const dt = query.target_dt;

	try {
		if (_.isEmpty(dataType)) {
			throw new BusinessError(SBE.Common.MissingRequiredParam, { field: 'target_data' });
		}
		if (_.isEmpty(dt) || !moment(dt, ['YYYYMM', 'YYYYMMDD', 'YYYYMMDDHH'], true).isValid()) {
			throw new BusinessError(SBE.Common.MissingRequiredParam, { field: 'target_dt' });
		}

		await backupRestoreService.delete(dataType, dt);

		body = {
			code: 200,
			message: `Nubes 파일 삭제 완료 ( target_dt=${dt} )`
		};
	} catch (e) {
		log.error(`[${logPrefix} :: delete] ERROR \n ${e.stack} \n`);
		backupRestoreService.sendFailureMail('delete', dataType, dt, e)

		body = {
			code: 500,
			message: `Nubes 파일 삭제 실패 ( target_dt=${dt} ) ${e.stack} ${e}`
		};
	} finally {
		ctx.body = body;
	}
};
