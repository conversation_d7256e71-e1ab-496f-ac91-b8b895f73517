
'use strict';

import moment from 'moment';

import config from '../config/config';

import * as logger from '../utils/logger.util';

import { Preview } from '../models/previews.schema';

/**
 * Preview 일괄 삭제
 * 
 * @RequestMapping(value='/batch/preview')
 */
module.exports.deletePreview =  async (ctx) => {
	logger.debug(`[preview.controller :: deletePreview] 호출됨`);
	
	setTimeout(async () => {
		try {
			// Preview 일괄 삭제
			await _deletePreview();

			logger.debug(`[preview.controller :: deletePreview] 처리 완료`);
		} catch(e) {
			logger.error(`[preview.controller :: deletePreview] Error :: \n ${e.stack}\n\n`, e);
		}
	}, 0);

	ctx.body = {
		code: 200,
		message: 'Preview 일괄 삭제 요청 완료'
	};
};


/**
 * _deletePreview : Preview 일괄 삭제
 * 	- 만료된 미리보기 데이터 일괄 삭제 처리
 */
const _deletePreview = async () => {
	logger.debug('[preview.controller :: _deletePreview] 호출됨');

	const now = moment();

	// DB 일괄 삭제 청크 사이즈
	const DELETE_CHUNK_SIZE = config.delete_chunk_size || 50000;

	// 삭제 대상 Preview 의 _id 리스트 가져오기
	let deleteIds = await Preview.aggregate()
		.match({ expiredAt: { $lt: now.toDate() } })
		.project({ _id: 1 }) 
		.limit(DELETE_CHUNK_SIZE)
		.exec();

	// 삭제할 것이 없을 때까지, 50000건씩 끊어서 삭제 처리
	while(deleteIds.length > 0) {
		await Preview.deleteMany({ _id: { $in: deleteIds } });

		logger.debug(`[preview.controller :: _deletePreview] ${deleteIds.length} 건 삭제 완료`);

		deleteIds = await Preview.aggregate()
			.match({ expiredAt: { $lt: now.toDate() } })
			.project({ _id: 1 }) 
			.limit(DELETE_CHUNK_SIZE)
			.exec();
	}

	logger.debug('[preview.controller :: _deletePreview] 처리 완료');
};
