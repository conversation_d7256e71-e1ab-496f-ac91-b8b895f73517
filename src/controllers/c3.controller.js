'use strict';

import * as c3 from '../c3/c3';

import * as logger from '../utils/logger.util';


/**
 * C3 active namenode host 찾기
 *
 * @RequestMapping(value='/batch/c3/set_active_host')
*/
module.exports.setActiveHost = async (ctx) => {
	let body;

	try {
		await c3.setActiveHost();

		body = {
			code: 200,
			message: `C3 setActiveHost`
		};
	} catch (e) {
		logger.error(`c3.controller.setActiveHost() :: ${e.stack}`);

		body = {
			code: 500,
			message: `C3 setActiveHost. ${e.stack}`
		};
	}

	ctx.body = body;
};

/**
 * C3 kinit
 *
 * @param ctx
 * @returns {Promise<void>}
 */
module.exports.kinit = async (ctx) => {
	let body;
	try {
		logger.debug(`c3.controller.kinit() received..`);

		await c3.kinit();

		logger.debug(`c3.controller.kinit() done`);

		body = {
			code: 200,
			message: `C3 kinit`
		}

	} catch (e) {
		logger.error(`c3.controller.kinit() :: ${e.stack}`);

		body = {
			code: 500,
			message: `C3 kinit. ${e.stack}`
		}
	}

	ctx.body = body;
};
