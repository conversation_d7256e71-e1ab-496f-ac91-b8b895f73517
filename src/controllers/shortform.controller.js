'use strict';

import moment from 'moment/moment';

import * as logger from '../utils/logger.util';
import { BusinessError } from '../common/error';

import * as shortformService from '../services/report/shortform/shortform.service';
import _ from "lodash"; // shortform

const LOGGER = 'shortform.controller'

/**
 * uploadReportFromHdfsToCuve : HDFS 의 집계 결과 report 를 Cuve 에 업로드
 *
 * @RequestMapping(value='/batch/shortform/upload')
*/
module.exports.uploadReportFromHdfsToCuve = async (ctx) => {
	logger.debug(`[${LOGGER} :: uploadReportFromHdfsToCuve] ${ctx.url} 호출됨`);

	let body = {};
	let option = {};
	const query = ctx.request.query;
	const ymd = query.ymd;

	try {
		// 파라미터 세팅
		if (ymd) {
			if (!moment(ymd, 'YYYYMMDD', true).isValid()) {
				throw new BusinessError({ message: `[${LOGGER} :: uploadReportFromHdfsToCuve] ymd format is invalid ( ymd=${ymd} )` });
			}
			option.ymd = ymd;
		} else {
			// ymd 가 없는 경우, 오늘 날짜로 처리
			option.ymd = moment().format('YYYYMMDD');
		}

		// 리포트 다운로드
		const localReportFile = await shortformService.downloadReportFromHdfs(option);

		// 로컬로 다운 받은 리포트 업로드
		if(_.isEmpty(localReportFile)) {
			body = {
				code: 500,
				message: `HDFS에서 Shortform Report download 실패 ( ymd=${option.ymd} )`
			};
		}
		else {
			option.localFilePath = localReportFile;
			const isComplete = await shortformService.uploadReportWithCsync(option);

			if(isComplete === false){
				body = {
					code: 500,
					message: "HDFS에서 download 받은 Shortform Report Cuve로 upload 실패",
				};
			}
			else {
				body = {
					code: 200,
					message: "HDFS에서 Cuve로 Shortform Report upload 완료",
				};
			}
		}

		logger.debug(`[${LOGGER} :: uploadReportFromHdfsToCuve] 처리 완료`);

	} catch(e) {
		logger.error(`[${LOGGER} :: uploadReportFromHdfsToCuve] Error :: \n ${e.stack} \n`, e);

		body = {
			code: 500,
			message: `HDFS에서 Cuve로 Shortform Report upload 실패 ( ymd=${option.ymd} ) :: ${e.stack}`
		};
	} finally {
		ctx.body = body;
	}
};

/**
 * downloadReportFromCuveToHdfs : Cuve 의 소스 report 를 HDFS 로 다운로드
 *
 * @RequestMapping(value='/batch/shortform/upload')
 */
module.exports.downloadReportFromCuveToHdfs = async (ctx) => {
	logger.debug(`[${LOGGER} :: downloadReportFromCuveToHdfs] ${ctx.url} 호출됨`);

	let body = {};
	let option = {};
	const query = ctx.request.query;
	const ymd = query.ymd;

	try {
		// 파라미터 세팅
		if (ymd) {
			if (!moment(ymd, 'YYYYMMDD', true).isValid()) {
				throw new BusinessError({ message: `[${LOGGER} :: downloadReportFromCuveToHdfs] ymd format is invalid ( ymd=${ymd} )` });
			}
			option.ymd = ymd;
		} else {
			// ymd 가 없는 경우, 오늘 날짜로 처리
			option.ymd = moment().format('YYYYMMDD');
		}

		// 리포트 다운로드
		// const localReportFile = await shortformService.downloadReportWithApi(option);
		const localReportFile = await shortformService.downloadReportWithCsync(option);

		// 로컬로 다운 받은 리포트 업로드
		if(_.isEmpty(localReportFile) || !localReportFile[0]) {
			body = {
				code: 500,
				message: `Cuve에서 Source 리포트 파일 download 실패 ( ymd=${option.ymd} )\nservice File: ${localReportFile[1]}\nviewer File: ${localReportFile[2]}`
			};
		}
		else {
			option.localFilePath1 = localReportFile[1];
			option.localFilePath2 = localReportFile[2];
			const isComplete = await shortformService.uploadReportToHdfs(option);

			if(isComplete === false){
				body = {
					code: 500,
					message: "Cuve에서 download 받은 Source 리포트 파일 HDFS로 upload 실패",
				};
			}
			else {
				body = {
					code: 200,
					message: "Cuve에서 HDFS로 Source 리포트 파일 download 완료",
				};
			}
		}

		logger.debug(`[${LOGGER} :: downloadReportFromCuveToHdfs] 처리 완료`);

	} catch(e) {
		logger.error(`[${LOGGER} :: downloadReportFromCuveToHdfs] Error :: \n ${e.stack} \n`, e);

		body = {
			code: 500,
			message: `Cuve에서 HDFS로 Source 리포트 파일 download 실패 ( ymd=${option.ymd} ) :: ${e.stack}`
		};
	} finally {
		ctx.body = body;
	}
}
