'use strict';

import _ from 'lodash';
import moment from 'moment/moment';
import COMMON_CODE from '@ssp/ssp-common-code';

import * as logger from '../utils/logger.util';

import SBE from '../common/error-code';
import { BusinessError } from '../common/error';

import * as abuseScheduleService from '../services/reportapi/abuse/ad-provider-abuse-report-api-schedule.service';
import * as abuseReportApiService from '../services/reportapi/abuse/ad-provider-abuse-report-api.service';

const REPORT_API_TYPE = COMMON_CODE.codeEncAvailable()['ReportApiType'];

const REPORT_API_LOGGER = 'report_api';
const ERROR_LOGGER = 'report_api_error';

// In Naver AdProvider 코드 정보 - GFD, NCC, NDP, GFA
const IN_NAVER = [
	REPORT_API_TYPE.GFD.code,
	REPORT_API_TYPE.NCC.code,
	REPORT_API_TYPE.NDP.code,
	REPORT_API_TYPE.GFA.code
];


/**
 * makeAdProviderAbuseReportApiSchedules : AP 어뷰즈 리포트 연동 스케쥴 생성
 * 	- AdProviderAbuseReportApiSchedules
 *
 * @RequestMapping(value='/batch/adprovider/abuse/reportapi/schedule')
*/
module.exports.makeAdProviderAbuseReportApiSchedules = async (ctx) => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api.controller :: makeAdProviderAbuseReportApiSchedules] /batch/adprovider/abuse/reportapi/schedule 호출됨');

	let body = {};
	let option = {};

	try {
		// ymd 가 없는 경우, 오늘 날짜로 처리
		if (ctx.request.query.ymd) {
			option.ymd = ctx.request.query.ymd;
		} else {
			option.ymd = moment().format('YYYYMMDD');
		}

		if (ctx.request.query.reportApiType) option.reportApiType = ctx.request.query.reportApiType;

		// [Validation] ymd 날짜 포맷 (YYYYMMDD)
		if (!moment(option.ymd, 'YYYYMMDD', true).isValid()) {
			throw new BusinessError({ message: `[ad-provider-abuse-report-api.controller :: makeAdProviderAbuseReportApiSchedules] ymd format is invalid ( ymd= ${option.ymd} )` });
		}


		// 어뷰즈 리포트 연동 설정 정보 가져오기
		const abuseReportApiConfig = await abuseReportApiService.getAbuseReportApiConfig();


		// AdProviderAbuseReportApiSchedules 생성
		const failedAdProviders = await abuseScheduleService.makeAdProviderAbuseReportApiSchedules(option, abuseReportApiConfig);


		// 문제가 있는 AdProvider 가 있는 경우, 해당 AdProvider 의 어뷰즈 리포트 연동 스케쥴 생성 실패 알림 메일
		if (!_.isEmpty(failedAdProviders)) {
			await abuseScheduleService.sendBatchFailureMail(failedAdProviders);
		}

		logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api.controller :: makeAdProviderAbuseReportApiSchedules] 처리 완료');

		body = {
			code: 200,
			message: `AdProviderAbuseReportApiSchedule 생성 완료 ( ymd=${option.ymd} )`
		};
	} catch(e) {
		logger.error(ERROR_LOGGER, `[ad-provider-abuse-report-api.controller :: makeAdProviderAbuseReportApiSchedules] Error :: \n ${e.stack} \n`, e);

		body = {
			code: 500,
			message: `AdProviderAbuseReportApiSchedule 생성 실패 ( ymd=${option.ymd} ) :: ${e.stack}`
		};
	} finally {
		ctx.body = body;
	}
};


/**
 * processAbuseReportApi : AP 어뷰즈 리포트 연동
 * 	- IN_NAVER :: GFA
 *
 *  PROCESS 1. reportApiType 에 해당 하는 것 중, state 가 READY / FAILURE 인 abuseReportApiSchedules 가져오기 & state 를 WAIT 으로 변경
 *  			scheduledAt 이 현재 시각보다 이전이어야 함
 *  PROCESS 2. reportApiType 에 해당 하는 어뷰즈 리포트 연동 설정 정보 가져오기 ( abuse-report-api-config )
 *  PROCESS 3. 대상 스케쥴의 state 를 IN_PROGRESS 로 변경
 *  PROCESS 4. IN_NAVER AP 어뷰즈 리포트 연동
 *  PROCESS 5. 대상 스케쥴의 state 를 COMPLETE / FAILURE 로 변경
 *  PROCESS 6. 연동 처리 결과가
 *  			COMPLETE 인 경우, 배치 처리 결과 알림 메일 발송
 *  			FAILURE 이면서 재처리가 아닌 경우, 실패 알림 메일 보내기
 *
 * @RequestMapping(value='/batch/adprovider/abuse/reportapi/:reportApiType')
*/
module.exports.processAbuseReportApi = async (ctx) => {
	const query = ctx.request.query;
	const reportApiType = ctx.params.reportApiType.toUpperCase();

	// IN_NAVER 처리 대상 AdProvider 가 아닌 경우
	if(!_.includes(IN_NAVER, reportApiType)) {
		throw new BusinessError(SBE.ReportApi.NotInNaverAdProvider);
	}

	// 날짜 포맷 체크
	if (!_.isEmpty(query.ymd) && !moment(query.ymd, 'YYYYMMDD', true).isValid()) {
		throw new BusinessError({ message: 'ymd 는 YYYYMMDD 포맷이어야 함' });
	}

	// 재처리 여부
	const isRetry = (!_.isEmpty(query.isRetry) && query.isRetry.toLowerCase() === 'true');

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.controller :: processAbuseReportApi] ${ctx.path} 호출됨`);

	setTimeout(async () => {
		try{
			// [PROCESS 1]
			// 	reportApiType 에 해당 하는 것 중, state 가 READY / FAILURE 인 abuseReportApiSchedules 가져오기
			// 	대상 스케쥴의 state 를 WAIT 으로 변경
			// 	scheduledAt 이 현재 시각보다 이전이어야 함

			const option = { reportApiType, state : !isRetry ? 'READY' : 'FAILURE' };
			if (query.ymd) option.ymd = query.ymd;

			// [{ _id, ymd, reportApiType, retryCount, targetDate, apiResultPath, scheduledAt, modifiedAt }]
			const abuseReportApiSchedules = await abuseScheduleService.getAdProviderAbuseReportApiSchedules(option);

			// abuseReportApiSchedules 없는 경우, 종료
			if (_.isNil(abuseReportApiSchedules) || _.isEmpty(abuseReportApiSchedules)) {
				logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.controller :: processAbuseReportApi] adProviderAbuseReportApiSchedules(${reportApiType}) No Result`);

				return;
			}

			// 대상 스케쥴의 state 를 WAIT 으로 변경
			await abuseScheduleService.updateAbuseReportApiSchedulesState({ abuseReportApiSchedules, state: 'WAIT' });


			// [PROCESS 2] reportApiType 에 해당 하는 어뷰즈 리포트 연동 설정 정보 가져오기 ( abuse-report-api-config )
			/* abuseReportApiConfig = {
				reportApiType : 'GFA',
				apiResult : 'HDFS',
				apiInfo : {
					period : {
						start : -1,
						end : -1,
						unit : 'months'
					}
				},
				fileInfo : {
					filePath : '/user/naver-pa-dmp/gfp_report/revenue_refund/{{YYYY}}/{{MM}}',
					fileName : 'gfa_{{YYYY}}{{MM}}.tsv',
					separator : '\t'
				},
				scheduleInfo : {
					period : 'MONTH',
					day : 2,
					hour : 0
				},
				fields : {
					ymd : 'paymentDate',
					rk : 'reportKey',
					imp : 'impCount',
					clk : 'clickCount',
					netRevenue : 'rev1',
					revenue : 'rev2'
				}
			} */
			let abuseReportApiConfig = await abuseReportApiService.getAbuseReportApiConfig(reportApiType);
			abuseReportApiConfig = abuseReportApiConfig.pop();


			// [PROCESS 3 ~ PROCESS 6] abuseReportApiSchedule 별로 처리
			for (const abuseReportApiSchedule of abuseReportApiSchedules) {
				// [PROCESS 3] 대상 스케쥴의 state 를 IN_PROGRESS 로 변경
				await abuseScheduleService.updateAbuseReportApiScheduleState(abuseReportApiSchedule);


				// [PROCESS 4] IN_NAVER AP 어뷰즈 리포트 연동
				const isComplete = await _processAbuseReportApi(abuseReportApiSchedule, abuseReportApiConfig);


				// [PROCESS 5] 대상 스케쥴의 state 를 COMPLETE / FAILURE 로 변경
				await abuseScheduleService.updateAbuseReportApiScheduleState(abuseReportApiSchedule, isComplete);


				// [PROCESS 6] 연동 처리 결과가
				// 	COMPLETE 인 경우, 배치 처리 결과 알림 메일 발송
				// 	FAILURE 이면서 재처리가 아닌 경우, 실패 알림 메일 보내기
				if (isComplete) {
					// 배치 처리 결과 가져오기
					const result = await abuseScheduleService.getReportApiResults(abuseReportApiSchedule);

					// 배치 처리 결과 알림 메일 발송
					abuseReportApiService.sendResultMail(abuseReportApiSchedule, result);
				} else if (!isRetry) {
					// 재처리가 아닌 경우, 실패 알림 메일 발송
					abuseReportApiService.sendFailureMail(abuseReportApiSchedule);
				}
			}

		} catch(e) {
			logger.error(ERROR_LOGGER, `[ad-provider-abuse-report-api.controller :: processAbuseReportApi] Error :: \n ${e.stack}\n\n`, e);
		}
	}, 0);


	// 리포팅 처리가 오래 걸리므로 응답 먼저 완료
	ctx.body = {
		code: 200,
		message: `${reportApiType} 어뷰즈 리포트 연동 요청 완료`
	};
};


/**
 * [PROCESS 4] _processAbuseReportApi : IN_NAVER AP 어뷰즈 리포트 연동
 *
 * TASK 1. RkAdProviderMetaInfo 정보 가져오기
 * TASK 2. WebHDFS 연동 정보 가져오기
 * TASK 3. DSP가 API 연동 가능한 상태가 될 때까지 체크
 * TASK 4. 리포트 파일 가공 및 AdProviderRkStat DB 저장
 *
 * @param {Object} abuseReportApiSchedule { _id, ymd, reportApiType, retryCount, targetDate, apiResultPath }
 * @param {Object} abuseReportApiConfig
 * {
		reportApiType : 'GFA',
		apiResult : 'HDFS',
		apiInfo : {
			period : { start : -1, end : -1, unit : 'months' }
		},
		fileInfo : {
			filePath : '/user/naver-pa-dmp/gfp_report/revenue_refund/{{YYYY}}/{{MM}}',
			fileName : 'gfa_{{YYYY}}{{MM}}.tsv',
			separator : '\t'
		},
		scheduleInfo : { period : 'MONTH', day : 2, hour : 0 },
		fields : { ymd, rk, imp, clk, netRevenue, revenue : 'rev2' }
 * }
 * @return {Boolean} isComplete
*/
const _processAbuseReportApi = async (abuseReportApiSchedule, abuseReportApiConfig) => {
	const reportApiType = abuseReportApiSchedule.reportApiType;

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.controller :: _processAbuseReportApi] 호출됨 ( reportApiType= ${reportApiType} )`);

	try {
		// [PROCESS 4:: TASK 0] 어뷰즈 리포트 연동에 필요한 설정 정보 가져오기
		const baseMetaInfo = await abuseReportApiService.getBaseMetaInfo(abuseReportApiSchedule, abuseReportApiConfig);


		// [PROCESS 4:: TASK 1] AP 가 리포트 연동 가능한 상태인지 체크
		await abuseReportApiService.waitForReady(baseMetaInfo);


		// [PROCESS 4:: TASK 2] AP 어뷰즈 원본 파일 로컬 다운로드
		await abuseReportApiService.downloadToLocal(baseMetaInfo);


		// [PROCESS 4:: TASK 3] 어뷰즈 리포트 파일 정보 추출 ( 데이터 기간 및 총 건수 정보 )
		const abuseReportFileInfo = await abuseReportApiService.getAbuseReportFileInfo(baseMetaInfo);


		// [PROCESS 4:: TASK 4] adProviderMetaInfo 정보 가져오기
		/* adProviderMetaInfo = { // In Naver example
			ymd, reportApiType,
			adProvider_ids,
			apiResult, localFilePath,
			exchangeRate: { from, to, rate },
			targetDate, startDate, endDate,
			reportApiInfo: { checkSuccessFile, apiResultPath, separator, fields },
			adProviderInfos: [{ adProvider_id, publisher_id, adProviderInfo_id, place_ids }],
			gfpFeeRate: [{ adProvider_id, publisher_id, date, feeRate }],
			abuseReportFileInfo: { totalLineCount, dateList }
		} */
		const adProviderMetaInfo = await abuseReportApiService.getAdProviderMetaInfo(baseMetaInfo, abuseReportFileInfo);


		// [PROCESS 4:: TASK 5] 대상 스케쥴의 기간 정보 업데이트 ( startDate, endDate )
		await abuseScheduleService.updateAbuseReportApiSchedulePeriod(adProviderMetaInfo);


		// [PROCESS 4:: TASK 6] 리포트 파일 가공 및 HDFS 결과 저장
		await abuseReportApiService.processAdProviderAbuseStatHdfs(adProviderMetaInfo);


		return true;
	} catch(e) {
		logger.error(ERROR_LOGGER, `[ad-provider-abuse-report-api.controller :: _processAbuseReportApi] ${abuseReportApiSchedule.reportApiType} Error :: \n ${e.stack}\n\n`, e);

		return false;
	}
};

