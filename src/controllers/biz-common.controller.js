/* jshint node: true */
'use strict';

import moment from 'moment/moment';
import {BusinessError} from '../common/error';

import * as logger from '../utils/logger.util';
import * as bizCommonService from '../services/biz-common.service';
import * as gfpFeeService from "../services/report/gfp-fee.service";

/**
 * 국가 초기화
 * @param ctx
 * @returns {Promise.<void>}
 */
module.exports.initCountries = async (ctx) => {
	let body;
	try {
		logger.debug(`biz-common.controller.initCountries() received..`);

		await bizCommonService.initCountries();

		logger.debug(`biz-common.controller.initCountries() done`);

		body = {
			code: 200,
			message: `국가 초기화 완료`
		}
	} catch(e) {
		logger.error(`biz-common.controller.initCountries() :: ${e.stack}`);

		body = {
			code: 500,
			message: `국가 초기화 에러. ${e.stack}`
		}
	}

	ctx.body = body;
};

/**
 * GFP 수수료율 정보 초기화
 * @param ctx
 * @returns {Promise<void>}
 */
module.exports.initGfpFeeRate = async (ctx) => {
	let body;
	try {
		logger.debug(`biz-common.controller.initGfpFeeRate() received..`);

		const query = ctx.request.query;

		await gfpFeeService.initGfpFeeRate(query.period)

		logger.debug(`biz-common.controller.initGfpFeeRate() done`);

		body = {
			code: 200,
			message: `GFP 수수료율 정보 초기화 완료`
		}
	} catch(e) {
		logger.error(`biz-common.controller.initGfpFeeRate() :: ${e.stack}`);

		body = {
			code: 500,
			message: `GFP 수수료율 정보 초기화 에러. ${e.stack}`
		}
	}

	ctx.body = body;
};

/**
 * GFP 수수료율 정보 초기화 ( 일별 )
 * @param ctx
 * @returns {Promise<void>}
 */
module.exports.initGfpFeeRateByDate = async (ctx) => {
	let body;
	try {
		logger.debug(`biz-common.controller.initGfpFeeRateByDate() received..`);

		const date = ctx.request.query.date;

		if (!moment(date, 'YYYYMMDD', true).isValid()) {
			throw new BusinessError({ message: `biz-common.controller.initGfpFeeRateByDate() date is invalid ( date= ${date} )` });
		}

		await gfpFeeService.initGfpFeeRateByDate(date);

		logger.debug(`biz-common.controller.initGfpFeeRateByDate() done`);

		body = {
			code: 200,
			message: `GFP 수수료율 정보 일별 초기화 완료 (date= ${date})`
		}
	} catch(e) {
		logger.error(`biz-common.controller.initGfpFeeRateByDate() :: ${e.stack}`);

		body = {
			code: 500,
			message: `GFP 수수료율 정보 일별 초기화 에러. ${e.stack}`
		}
	}

	ctx.body = body;
};
