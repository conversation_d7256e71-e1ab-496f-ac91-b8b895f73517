'use strict';

import * as logger from '../utils/logger.util';
import * as multiDimensionalReportService from '../services/report/gfp/multidimensional/multi-dimensional-report.service';

/**
 * 다차원 리포트 생성 요청
 * @param ctx
 * @returns {Promise.<void>}
 */
module.exports.runMultiDimensionalReport = (ctx) => {
	let body;
	try {
		logger.debug(`multi-dimensional-report.controller.runMultiDimensionalReport() received..`);

		multiDimensionalReportService.runMultiDimensionalReport();

		logger.debug(`multi-dimensional-report.controller.runMultiDimensionalReport() done`);

		body = {
			code: 200,
			message: `다차원 리포트 Pro 생성 요청 완료`
		}
	} catch (e) {
		logger.error(`multi-dimensional-report.controller.runMultiDimensionalReport() :: ${e.stack}`);

		body = {
			code: 500,
			message: `다차원 리포트 Pro 생성 요청 에러. ${e.stack}`
		}
	}

	ctx.body = body;
};
