"use strict";

import mongoose from "mongoose";

let Publisher = {};

let createSchemaAndModel = (conn) => {
  let scheme = mongoose.Schema({
    // 이름
    name: {
      type: String,
      trim: true,
      unique: true,
      required: true,
      maxlength: [15, "name is longer than the maximum allowed length (15)"],
      match: [/^[A-Za-z0-9가-힣ㄱ-ㅎㅏ-ㅣ]*$/, "invalid name"],
    },

    // 설명
    description: {
      type: String,
      default: "",
      maxlength: [
        140,
        "description is longer than the maximum allowed length (140)",
      ],
      match: [/^[A-Za-z0-9가-힣ㄱ-ㅎㅏ-ㅣ.,\n ]*$/, "invalid description"],
    },

		cmsType: { type: String, uppercase: true, required: true, enum: ['GFP', 'NAM'] },

    // 법인 구분 (NAVER, FAMILY, OUTSIDE)
    corporationType: {
      type: String,
      uppercase: true,
      required: true,
      enum: ["NAVER", "FAMILY", "OUTSIDE"],
    },

    // 상태 (ON / OFF)
    status: {
      type: String,
      uppercase: true,
      required: true,
      enum: ["ON", "OFF"],
    },

    // 계약서
    contracts: {
      type: [
        {
          commonFile_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "CommonFile",
            required: true,
          },
          commissionRate: { type: Number, required: true },
          startAt: { type: Date, required: true },
          endAt: { type: Date, required: true },
        },
      ],
    },

    // predefined
    predefinedKeyValues: {
      type: [
        {
          predefinedKeyValue_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "PredefinedKeyValue",
            required: true,
          },
          creatorName: String,
          createdAt: { type: Date, default: Date.now },
        },
      ],
    },

    // freeform
    freeformKeyValues: {
      type: [
        {
          key: {
            type: String,
            trim: true,
            required: true,
            maxlength: [
              15,
              "freeform key is longer than the maximum allowed length (15)",
            ],
            match: [/^[a-zA-Z]*$/, "invalid freeform key"],
          },
          name: {
            type: String,
            trim: true,
            required: true,
            maxlength: [
              15,
              "freeform name is longer than the maximum allowed length (15)",
            ],
            match: [/^[A-Za-z0-9가-힣ㄱ-ㅎㅏ-ㅣ]*$/, "invalid freeform name"],
          },
          extendable: { type: Number, enum: [0, 1] },
          fullSync: {
            status: { type: String, uppercase: true, enum: ["ON", "OFF"] },
            url: String,
            acl: { type: Number, enum: [0, 1] },
            hmac: { type: Number, enum: [0, 1] },
            hmacKey: String,
            period: { type: String, uppercase: true, enum: ["DAY", "WEEK"] },
            hour: String,
            dayOfWeek: Number,
          },
          creatorName: String,
          createdAt: { type: Date, default: Date.now },
          modifiedAt: { type: Date, default: Date.now },
        },
      ],
    },

    // KeyGroup
    keyGroups: {
      type: [
        {
          // Group 명
          name: {
            type: String,
            trim: true,
            required: true,
            maxlength: [
              15,
              "name is longer than the maximum allowed length (15)",
            ],
            match: [/^[A-Za-z0-9가-힣ㄱ-ㅎㅏ-ㅣ]*$/, "invalid name"],
          },

          // 설명
          description: { type: String, trim: true, required: true },

          // 구분
          types: [{ type: String, uppercase: true, required: true }],

          // Key
          keys: [{ type: String, required: true }],

          // 등록자명
          creatorName: String,

          // 등록일시
          createdAt: { type: Date, default: Date.now },
        },
      ],
    },

    // providers
    adProvider_ids: {
      type: [
        {
          type: mongoose.Schema.Types.ObjectId,
          ref: "AdProvider",
        },
      ],
    },

    creatorName: String,
    creatorEmail: String,

    // 등록
    creator_id: String,
    createdAt: {
      type: Date,
      default: Date.now,
    },

    // 수정
    modifier_id: String,
    modifiedAt: {
      type: Date,
      default: Date.now,
    },

    invoice: {
      type: {
        contact: {
          type: {
            name: { type: String },
            department: { type: String },
            phoneNumber: { type: String },
            email: { type: String },
          },
        },
      },
    },
  });

  Publisher = conn.model("Publisher", scheme, "Publishers");

  return scheme;
};

export default createSchemaAndModel;

export { Publisher };
