'use strict';

import mongoose from 'mongoose';

let SilverSchedule = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		datetime: String,

		state: {
			type: String,
			enum: [
				'STANDBY',
				'ONGOING',
				'COMPLETE',
				'FAILURE'
			]
		},

		sparkSubmitState: {
			type: String,
			enum: [
				'STANDBY',		// spark-submit을 하기 위한 대기 상태
				'ACCEPTED',		// spark-submit 후 cluster에 spark app을 submit한 상태
				'RUNNING',		// cluster에서 spark app이 돌아가고 있음
				'FINISHED'		// spark-submit 종료
			]
		},
		sparkAppState: {
			type: String,
			enum: [
				'STANDBY',		// spark app 실행을 위한 대기 상태
				'ONGOING',		// 진행중
				'COMPLETE',		// spark app 종료
				'FAILURE'		// spark app 내부 로직 실패
			]
		},
		trackingUrl: String,
		sparkSubmitError: String,
		sparkAppError: String,

		erSspServer: {
			begunAt: Date,
			endedAt: Date
		},

		erSspClient: {
			begunAt: Date,
			endedAt: Date
		},

		begunAt: Date,
		endedAt: Date
	},{	versionKey: false });

	SilverSchedule = conn.model('SilverSchedule', scheme, 'SilverSchedules');

	return scheme;
};

export default createSchemaAndModel;

export { SilverSchedule };
