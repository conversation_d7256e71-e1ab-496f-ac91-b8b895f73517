'use strict';

import mongoose from 'mongoose';
import _ from 'lodash';
import { BusinessError } from '../common/error';


let AdProviderNonRkStat = {};

let createSchemaAndModel = (conn) => {
	// 참고 사항
	// Bulk insert 로 mongoose validation 적용이 안 됨.
	// validation 적용 하려면, insertMany 를 사용해야 하는데, 한 건 한 건 체크 하기 때문에 시간이 소요됨

	let scheme = mongoose.Schema({
		// 일자
		ymd: { type: String, required:true },

		// publisher id
		publisher_id: { type: mongoose.Schema.Types.ObjectId, required:true },

		// adProvider id
		adProvider_id: { type: mongoose.Schema.Types.ObjectId, required:true },

		// adProvider place key - DSP key 정보
		// key:value,key:value,key:value ...
		adProviderPlaceKey: { type: String, required:true },

		// 국가, os, 사이즈, 광고소스 정보
		country: String,
		os: String,
		size: String,
		adSource:String,

		// dealId
		dealId: String,

		// 수익쉐어용 KeyGroup에 해당하는 KeyValue 정보
		rsKeyValue: { type: Object },

		// GFP place id
		// publisher_id, adProviderPlaceKey를 이용해 DB 조회
		adProviderPlace_ids: {
			type:[{
				type: mongoose.Schema.Types.ObjectId
			}]
		},

		// GFP adUnit id
		// adProviderPlace_ids를 이용해 DB 조회
		adUnitIds: [String],

		imp: { type: Number, required:true },
		clk: { type: Number },

		// GFP 순수익 USD/KRW - GFP 수수료 제외
		gfpNetRevenueUSD: { type: mongoose.Schema.Types.Decimal128, set: _setNetRevenueUSD },
		gfpNetRevenueKRW: { type: mongoose.Schema.Types.Decimal128, set: _setNetRevenueKRW },

		// 순수익 USD/KRW - AP 수수료, 대행사 수수료 등 제외
		netRevenueUSD: { type: mongoose.Schema.Types.Decimal128, set: _setNetRevenueUSD },
		netRevenueKRW: { type: mongoose.Schema.Types.Decimal128, set: _setNetRevenueKRW },

		// AP Gross 수익 USD/KRW - 수수료 포함, RUBICON only
		revenueUSD: { type: mongoose.Schema.Types.Decimal128 },
		revenueKRW: { type: mongoose.Schema.Types.Decimal128 },

		// AP 순수익 USD/KRW - AP 수수료, 대행사 수수료 등 제외 ( 소수점 6째자리 )
		usdSales: { type: mongoose.Schema.Types.Decimal128, set: _setNetRevenueUSD },
		krwSales: { type: mongoose.Schema.Types.Decimal128, set: _setNetRevenueKRW },

		createdAt: {
			type: Date, default: Date.now
		}
	}, { minimize: false });

	AdProviderNonRkStat = conn.model('AdProviderNonRkStat', scheme, 'AdProviderNonRkStats');

	return scheme;
};

const _setNetRevenueUSD = (netRevenueUSD) => {
	if(_.isNil(netRevenueUSD) || _.isNaN(netRevenueUSD)) {
		throw new BusinessError({ message: `[ad-provider-non-rk-stats.schema] netRevenueUSD validation 오류 : ${netRevenueUSD}` });
	}

	return netRevenueUSD;
};

const _setNetRevenueKRW = (netRevenueKRW) => {
	if(_.isNil(netRevenueKRW) || _.isNaN(netRevenueKRW)) {
		throw new BusinessError({ message: `[ad-provider-non-rk-stats.schema] netRevenueKRW validation 오류 : ${netRevenueKRW}` });
	}

	return netRevenueKRW;
};

export default createSchemaAndModel;

export { AdProviderNonRkStat };
