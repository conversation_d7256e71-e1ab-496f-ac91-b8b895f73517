'use strict';

import mongoose from 'mongoose';

let SummaryAdProviderStatsDailySimple_new = {};

let createSchemaAndModel = (conn) => {
    let scheme = mongoose.Schema({
		ymd : { type: String, required:true },

		publisher_id : { type: mongoose.Schema.Types.ObjectId, required:true },
		adProvider_id : { type: mongoose.Schema.Types.ObjectId, required:true },

		country: String,

		imp: { type: Number, required:true },
		clk: { type: Number, required:true },

		// GFP 순수익 USD/KRW - GFP 수수료 제외
		netRevenueUSD: { type: mongoose.Schema.Types.Decimal128 },
		netRevenueKRW: { type: mongoose.Schema.Types.Decimal128 },

		// AP 순수익 USD/KRW - AP 수수료, 대행사 수수료 등 제외
		revenueUSD: { type: mongoose.Schema.Types.Decimal128 },
		revenueKRW: { type: mongoose.Schema.Types.Decimal128 },

		createdAt: { type: Date, default: Date.now }
	}, { versionKey: false });

	SummaryAdProviderStatsDailySimple_new = conn.model('SummaryAdProviderStatsDailySimple_new', scheme, 'SummaryAdProviderStatsDailySimple_new');

    return scheme;
};

export default createSchemaAndModel;

export { SummaryAdProviderStatsDailySimple_new };
