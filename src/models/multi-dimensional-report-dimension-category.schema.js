'use strict';

import mongoose from 'mongoose';

let MultiDimensionalReportCategory = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		_id: { type:  mongoose.Schema.Types.ObjectId},

		type: {
			type: String, trim: true, enum: ['dimension', 'metric']
		},

		name: {
			type: String, trim: true, unique: true, required:true
		},

		label: {
			type: String,
			trim: true,
		},

		order: {
			type: Number
		}
	});

	MultiDimensionalReportCategory = conn.model('MultiDimensionalReportCategory', scheme, 'MultiDimensionalReportCategories');

	return scheme;
};

export default createSchemaAndModel;

export { MultiDimensionalReportCategory };
