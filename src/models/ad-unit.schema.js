'use strict';

import mongoose from 'mongoose';

let AdUnit = {};

let createSchemaAndModel = (conn) => {
    let scheme = mongoose.Schema({
        cmsType : String,
        adUnitId : {
			type: String, trim: true, unique: true, required:true,
			maxlength:[15, 'name is longer than the maximum allowed length (15)'],
			match: [/^[a-zA-Z0-9\_]*$/, 'invalid name']
		},
        name : { type: String, trim: true, required:true },
        adUnitCd : { type: String, trim: true },
        description : { type: String, trim: true },
        status : { type: String, uppercase: true, required:true, enum:['ON', 'OFF'] },
        creativeTypes : [{ type: String, uppercase: true, required:true, enum:['BANNER', 'NATIVE', 'VIDEO', 'COMBINED'] }],
        channelType : { type: String, uppercase: true, required:true, enum:['WEB', 'IOS', 'ANDROID'] },
        sizes : {
            type: [{width: Number, height: Number, _id: false}],
        },
        revenueShareUse : Number,
        sdkUse : { type:Number, enum:[0, 1] },
        sessionRefreshTime : { type:Number, enum:[0, 1], default: 0 },
        currencies : [{ type: String, uppercase: true, required:true }],
        baseCurrency : { type: String, uppercase: true, required:true },
        //creativeLevel : { type: String, uppercase: true, required:true, enum:['PREMIUM', 'NORMAL'] },
        rules : {
            type: [{
				_id: false,
                floorPrice : { type: Number, required:true },
                rank : { type: Number, required:true },
                rate : { type: Number, required:true },
                connectionType : { type: String, uppercase: true, required:true, enum:['S2S', 'C2S'] },
                servingType : { type: String, uppercase: true, required:true, enum:['BID', 'DIRECT'] },
                adProviderPlaces : {
                    type: [{
						_id: false,
                        adProviderPlace_id : {
							_id: false,
                            type: mongoose.Schema.Types.ObjectId,
                            ref: 'Place'
                        },
						responseValidityConditions : {
							type: [{
								_id: false,
								key : { type: String, required:true },
								operator : { type: String, required:true },
								values : [{ type: String, required:true }]
							}]
						}
                    }]
                },
                keyValues : {
                    type: [{
						_id: false,
                        key : { type: String, required:true },
						operator : { type: String, required:true },
                        values : [{ type: String, required:true }]
                    }]
                }
            }]
        },
        publisher_id : {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Publisher'
        },
        /*adProviderPlace_ids : {
            type: [{
				_id: false,
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Place'
            }]
        },*/
		layoutType : { type: String, uppercase: true, required:true, enum:['FLUID', 'PIXEL'] },
        createdAt : Date,
        modifiedAt : Date,
		creatorName : String,
		modifierName : String,


		// 2018.11.1. 추가. kr12855
		service_id: {
			type: mongoose.Schema.Types.ObjectId
		},
		adProviderPlaces: {
			type: [{
				_id: false,
				adProviderPlace_id : {
					_id: false,
					type: mongoose.Schema.Types.ObjectId,
					ref: 'Place'
				},
				sizes : {
					type: [{width: Number, height: Number, _id: false}],
				},
				deleted : { type: Number, enum:[0, 1] },
			}]
		},
		keyGroup_id: {
			type: mongoose.Schema.Types.ObjectId
		},
		rsKeyGroup_id: {
			type: mongoose.Schema.Types.ObjectId
		},
		biddingGroups: {
			type: [{
				_id: false,
				biddingGroup_id : {
					_id: false,
					type: mongoose.Schema.Types.ObjectId,
					ref: 'BiddingGroup'
				},
				deleted : { type: Number, enum:[0, 1] },
			}]
		},

	}, {
		_id: true,
		id: false,
		versionKey: false,
	});

/*    scheme.virtual('rulesCnt').get(function() {
        return this.rules.length;
    });

    scheme.virtual('placeCnt').get(function() {
        return this.adProviderPlace_ids.length;
    });*/

    scheme.set('toJSON',{ getters : true , virtuals : true });

/*    scheme.pre('findOneAndUpdate', function(next) {  //validation check 활용
        if (true) {
            console.log('this._update.$set====================', this._update.$set);
            throw new Error('스키마 Validation Check pre method에서 보낸 오류 메시지');
        }
        next();
    });

    scheme.post('findOneAndUpdate', function(result) {
        console.log('-----------------------result', result);
    });*/

    AdUnit = conn.model('AdUnit', scheme, 'AdUnits');

    return scheme;
};

export default createSchemaAndModel;

export { AdUnit };
