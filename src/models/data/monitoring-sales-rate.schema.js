'use strict';

import mongoose from 'mongoose';

let MonitoringSalesRate = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		date: String,
		adProvider_id: { type: mongoose.Schema.Types.ObjectId, required: true },
		publisher_id: { type: mongoose.Schema.Types.ObjectId, required: true },

		krwGross: Number,
		krwSales: Number,
		krwSalesRate: Number,
		krwSalesRateDeviation: Number,

		usdGross: Number,
		usdSales: Number,
		usdSalesRate: Number,
		usdSalesRateDeviation: Number,

		createdAt: {
			type: Date,
			default: Date.now
		},
		expiredAt: {
			type: Date,
			default: Date.now
		}
	}, { versionKey: false });

	MonitoringSalesRate = conn.model('MonitoringSalesRate', scheme, 'MonitoringSalesRate');

	return scheme;
};

export default createSchemaAndModel;

export { MonitoringSalesRate };
