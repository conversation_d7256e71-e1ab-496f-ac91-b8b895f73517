'use strict';

import mongoose from 'mongoose';

let SilverTrace = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		date: String,

		zirconRGfpCompletedAt: Date,

		createdAt: Date,
		modifiedAt: Date,
		expiredAt: Date,
	}, { versionKey: false });

	SilverTrace = conn.model('SilverTrace', scheme, 'SilverTrace');

	return scheme;
};

export default createSchemaAndModel;

export { SilverTrace };
