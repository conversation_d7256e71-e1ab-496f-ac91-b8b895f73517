'use strict';

import mongoose from 'mongoose';

/*************************************************************************
	리포트 생성 조건
 */

let Schedule = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema(
		{
			publisher_id: {_id: false, type: mongoose.Schema.Types.ObjectId,},
			type: {type: String, uppercase: true}, // 리포트 타입. ex) RS, PERF
			name: {type: String, minlength: 1, trim: true},
			description: {type: String, trim: true, minlength: 1},
			status: {type: String, uppercase: true, enum: ['ON', 'OFF']},
			frequency: {type: String, uppercase: true, enum: ['DAILY', 'MONTHLY']}, // 생성주기
			granularity: {type: String, uppercase: true, enum: ['DAILY', 'MONTHLY']}, // 집계단위
			alarmReceiverIds: [String], // 알림 수신자
			creator_id: {_id: false, type: mongoose.Schema.Types.ObjectId,},
			createdAt: {type: Date, default: Date.now},
			modifier_id: {_id: false, type: mongoose.Schema.Types.ObjectId,},
			modifiedAt: {type: Date, default: Date.now},

			// 파생 필드
			cmsType: {type: String},

			// 독립 필드 - 수익쉐어 리포트
			adUnitIds: [String],
			keys: [String],

			// 독립 필드 - 맞춤성과 리포트
		},
		{
			versionKey: false
		}
	);

	Schedule = conn.model('Schedules', scheme, 'Schedules');

	return scheme;
};

export default createSchemaAndModel;

export {Schedule};
