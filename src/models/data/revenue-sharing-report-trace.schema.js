'use strict';

import mongoose from 'mongoose';

let RevenueSharingReportTrace = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema(
		{
			revenueSharingReport_id: { _id: false, type: mongoose.Schema.Types.ObjectId, required: true },

			date: { type: String }, // yyyyMMdd (일별 리포트만 재처리함)

			silverTraces: [{
				_id: false,
				silverTrace_id: { type: mongoose.Schema.Types.ObjectId },
				zirconRGfpCompletedAt: { type: Date },
			}],
			zirconTraces: [
				{
					_id: false,
					zirconTrace_id: { type: mongoose.Schema.Types.ObjectId },
					publisher_id: { type: mongoose.Schema.Types.ObjectId },
					adProvider_id: { type: mongoose.Schema.Types.ObjectId },
					silvergreyCompletedAt: { type: Date },
				}
			],

			succeededAt: { type: Date },
			failedAt: { type: Date },

			createdAt: { type: Date, default: Date.now },
			modifiedAt: { type: Date, default: Date.now },
			expiredAt: { type: Date },
		},
		{
			versionKey: false
		}
	);

	RevenueSharingReportTrace = conn.model('RevenueSharingReportTrace', scheme, 'RevenueSharingReportTrace');

	return scheme;
};

export default createSchemaAndModel;

export { RevenueSharingReportTrace };
