'use strict';

import mongoose from 'mongoose';

let CommonCode = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		category: { type: String, required: true },
		code: { type: String, required: true},
		description: { type: String, required: true },
	});

	CommonCode = conn.model('CommonCode', scheme, 'CommonCodes');

	return scheme;
};

export default createSchemaAndModel;

export { CommonCode };
