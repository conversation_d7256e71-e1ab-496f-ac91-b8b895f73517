'use strict';

import mongoose from 'mongoose';


let TimezoneTest = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema(
		{
			unixTime: {
				type: Number
			},
			timezone: {
				type: String
			},
			seoulAt: {
				type: Date
			},
			apAt: {
				type: Date
			},
			utcAt: {
				type: Date
			},
		},
		{
			versionKey: false
		}
	);

	TimezoneTest = conn.model('TimezoneTest', scheme, 'TimezoneTest');

	return scheme;
};

export default createSchemaAndModel;

export { TimezoneTest };
