'use strict';

import mongoose from 'mongoose';

let DiscrepancyReportSchedule = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		ymd: { type: String, required: true },

		// 리포트 API 타입
		reportApiType: { type: String, required: true },

		// adProvider ids
		adProvider_ids: { type: [{ type: mongoose.Schema.Types.ObjectId }], required: true },

		// publisher ids
		publisher_ids: { type: [{ type: mongoose.Schema.Types.ObjectId }], required: true },

		// ap timezone
		timezone: { type: String, required: true },

		// 디멘전 (spark 에서는 date, ap, pub 별 집계 후 DiscrepancyDaily 에 저장 후, NAM-API 으로 요청 시, 본 디멘전으로 다시 한번 집계)
		dimensions: { type: [{ type: String }], required: true },

		// 메트릭 (reportApiType 별로 원하는 메트릭이 다르므로, NAM-API 으로 요청 시 본 메트릭에 대한 값을 요청)
		metrics: { type: [{ type: String }], required: true },

		// 처리할 데이터 기간 
		startDate: { type: String, required: true },
		endDate: { type: String, required: true },

		// 전체 처리 상태 [READY, LAUNCH, IN_PROGRESS, COMPLETE, FAILURE]
		// 	- READY : 스파크 집계 대기
		// 	- LAUNCH : control DAG 에서 spark app 을 구동하는 target DAG 을 launch 한 상태. target DAG 에서 spark app 을 submit 하는데 성공하면, IN_PROGRESS 로 변경
		// 	- IN_PROGRESS : 스파크 집계 처리 중
		// 	- COMPLETE : 스파크 집계 완료
		// 	- FAILURE : 스파크 집계 실패
		sparkState: { type: String, required: true, uppercase: true, enum: ['READY', 'LAUNCH', 'IN_PROGRESS', 'COMPLETE', 'FAILURE'] },

		// 전체 처리 상태 [READY, WAIT, IN_PROGRESS, COMPLETE, FAILURE]
		// 	- READY : 연동 처리 대기
		// 	- WAIT : 스파크 집계 완료되어, 연동 처리 시작 준비
		// 	- IN_PROGRESS : 연동 처리 중
		// 	- COMPLETE : 연동 완료
		// 	- FAILURE : 연동 실패
		apiState: { type: String, required: true, uppercase: true, enum: ['READY', 'WAIT', 'IN_PROGRESS', 'COMPLETE', 'FAILURE'] },

		// 파일 방식인 경우, 파일 생성 결과 경로
		// API 방식인 경우, API 결과 정보
		result: { type: Object },

		// 처리 시작 시간
		begunAt: { type: Date },

		// 처리 종료 시간
		endedAt: { type: Date },

		createdAt: {
			type: Date,
			default: Date.now
		},

		modifiedAt: {
			type: Date,
			default: Date.now
		}
	}, { versionKey: false });

	DiscrepancyReportSchedule = conn.model('DiscrepancyReportSchedule', scheme, 'DiscrepancyReportSchedules');

	return scheme;
};

export default createSchemaAndModel;

export { DiscrepancyReportSchedule };
