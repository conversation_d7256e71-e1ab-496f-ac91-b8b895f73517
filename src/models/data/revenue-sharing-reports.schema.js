'use strict';

import mongoose from 'mongoose';

let RevenueSharingReport = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema(
		{
			schedule_id: { type: mongoose.Schema.Types.ObjectId, required: true },
			date: { type: String },
			startDate: { type: String },
			endDate: { type: String },

			state: {
				type: String,
				trim: true,
				enum: ['READY', 'IN_PROGRESS', 'COMPLETE', 'FAILURE'],
				required: true
			},
			taskState: {
				type: String,
				trim: true,
				enum: [
					'READY', 'WAIT', 'WAIT_SG', 'WAIT_ZRGFP', 'SATISFIED',
					'AGG_IN_PROGRESS', 'AGG_COMPLETE', 'AGG_FAILURE',
					'UPLOAD_IN_PROGRESS', 'UPLOAD_COMPLETE', 'UPLOAD_FAILURE',
					'REPROCESS'
				],
				required: true
			},

			silvergreyDetails: [{
				date: { type: String },
				adProvider_id: { type: mongoose.Schema.Types.ObjectId },
				adProviderName: { type: String },
				reportApiType: { type: String },
				howManyDaysBefore: { type: Number },
				reportApiStatus: { type: String },
				state: {
					type: String,
					enum: ['NOT_SPECIFIED', 'NOT_REGISTERED_ALL', 'NOT_REGISTERED', 'OFF', 'READY', 'IN_PROGRESS', 'COMPLETE', 'FAILURE']
				},
				expectedCompleteDate: { type: Date },
				_id: false
			}],

			filePath: { type: String, trim: true },

			startedAt: { type: Date },
			succeededAt: { type: Date },
			failedAt: { type: Date },

			createdAt: { type: Date, default: Date.now },
			modifiedAt: { type: Date, default: Date.now },



			// 모니터링 관련 필드
			monitoredAt: { type: Date }, // 마지막으로 모니터링한 시간
			notifiedStates: [{ // 알림 이력
				type: {
					type: String,
					enum: ['WAIT_SG', 'WAIT_ZRGFP', 'WAIT', 'WAIT_RESOLVED', 'FAILURE', 'REPROCESS', 'TIMEOUT', 'EMPTY_DATA']
				},
				notifiedAt: { type: Date },
				resolvedAt: { type: Date },
				_id: false
			}],

			// --------------> 파생 필드
			scheduleName: { type: String },
			frequency: { type: String },
			granularity: { type: String },

			publisher_id: { type: mongoose.Schema.Types.ObjectId },
			publisherName: { type: String },
			cmsType: { type: String },
			// <-------------- 파생 필드
		},
		{
			versionKey: false
		}
	);

	RevenueSharingReport = conn.model('RevenueSharingReports', scheme, 'RevenueSharingReports');

	return scheme;
};

export default createSchemaAndModel;

export { RevenueSharingReport };
