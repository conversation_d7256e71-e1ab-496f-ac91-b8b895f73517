'use strict';

import mongoose from 'mongoose';

let ExtendedKeyValue = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema(
		{
			publisher_id: {
				_id: false,
				type: mongoose.Schema.Types.ObjectId,
				ref: 'Publisher'
			},

			// Extension Key Name; 반드시 publisher에서 파라미터를 통하여 전달이 되어야 함
			key: {
				type: String,
				minlength: 1,
				trim: true
			},

			// Extension Key Value; 반드시 publisher에서 파라미터를 통하여 전달이 되어야 함
			value: {
				type: String,
				trim: true,
				minlength: 1
			},

			// 사용가능 Value 여부; 0(deleted)과 1(available)로 구성됨
			valid : {
				type: Number,
				required:true,
				enum:[0, 1]

			},


			createdAt: {
				type: Date,
				default: Date.now
			},
			modifiedAt: {
				type: Date,
				default: Date.now
			},

			// Extension Key와 Mapping 되어 확장될 Key-Value 조합 Object; link to ExtensionItem Object
			items: [
				{
					_id : false,
					key: {
						type: String,
						trim: true,
						minlength: 1
					},
					value: {
						type: String,
						trim: true
					}
				}
			]
		},
		{
			versionKey: false
		}
	);

	ExtendedKeyValue = conn.model('ExtendedKeyValue', scheme, 'ExtendedKeyValues');

	return scheme;
};

export default createSchemaAndModel;

export { ExtendedKeyValue };
