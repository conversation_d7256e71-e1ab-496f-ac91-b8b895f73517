'use strict';

import mongoose from 'mongoose';

let BatchReportJobSchedule = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		ymd: { type: String, required: true },

		// ad provider 타입
		adProviderType: { type: String, required: true, uppercase: true, enum: ['OUTSIDE', 'IN_NAVER'] },

		// 리포트 API 타입
		reportApiType: { type: String, required: true },

		// 전체 처리 상태 [READY, WAIT, IN_PROGRESS, COMPLETE, FAILED]
		// 	- mongoState 와 hdfsState 모두 COMPLETE 이면 COMPLETE, 하나라도 실패하면 FAILED 로 처리한다.
		// 	- 배치 처리 대상이지만 아직 READY인 경우가 있는데(스케쥴 순서 상), 두 번 요청으로 인해 이중으로 연동 처리되지 않도록 WAIT 상태를 추가한다.
		state: { type: String, required: true, uppercase: true, enum: ['READY', 'WAIT', 'IN_PROGRESS', 'COMPLETE', 'FAILED'] },

		// mongo 집계 결과
		mongoState: { type: String, required: true, uppercase: true, enum: ['READY', 'COMPLETE', 'FAILED'] },

		// hdfs 집계 결과
		hdfsState: { type: String, required: true, uppercase: true, enum: ['READY', 'COMPLETE', 'FAILED'] },

		// 처리할 데이터 기간 
		period: {
			startDate: { type: String, required: true },
			endDate: { type: String, required: true }
		},

		// rk 인 경우 1, nonRk인 경우 0
		rkUse: { type: Number, required: true },

		// 리포트 데이터를 어디서 제공하는지 [AP, GFP]
		source: { type: String, required: true, uppercase: true, enum: ['AP', 'GFP'] },

		// 재시도인 경우 1, 아닌 경우 0
		retry: { type: Number, required: true },

		timezone: { type: String, required: true },

		// adProvider id (rk인 경우 null)
		adProvider_id: { type: mongoose.Schema.Types.ObjectId },

		// publisher id (rk인 경우 null)
		publisher_id: { type: mongoose.Schema.Types.ObjectId },

		// api 결과 경로 (source=AP 인 경우만 해당)
		apiResultPath: { type: String, required: true },

		// HDFS 원본 파일 경로 (source=AP 인 경우만 해당)
		sourcePath: { type: String, required: true },

		begunAt: {
			type: Date,
			default: Date.now
		},

		endedAt: {
			type: Date,
			default: Date.now
		},

		createdAt: {
			type: Date,
			default: Date.now
		},

		modifiedAt: {
			type: Date,
			default: Date.now
		}
	});

	BatchReportJobSchedule = conn.model('BatchReportJobSchedule', scheme, 'BatchReportJobSchedules');

	return scheme;
};

export default createSchemaAndModel;

export { BatchReportJobSchedule };
