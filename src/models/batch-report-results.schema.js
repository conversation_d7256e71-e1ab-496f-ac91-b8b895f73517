'use strict';

import mongoose from 'mongoose';

let BatchReportResult = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		// 저장 유형
		type: { type: String, required: true, uppercase: true, enum: ['HDFS', 'DB'] }, 

		// ymd
		ymd : { type: String, required:true },

		// 리포트 API 타입
		reportApiType: { type: String, required:true }, 

		// api 결과 경로 (파일 경로 또는 URL 경로 등) 
		apiResultPath: { type: String, required:true },

		// nonRk인 경우에만 해당. Rk인 경우 null
		adProvider_id: { type: mongoose.Schema.Types.ObjectId },

		// nonRk인 경우에만 해당. Rk인 경우 null
		publisher_id: { type: mongoose.Schema.Types.ObjectId },

		// 총 라인 수
		totalLineCount: { type: Number, required:true },

		// 데이터 가공 처리 완료된 총 라인 수
		totalSuccessCount: { type: Number, required:true },

		// 케이스별 필터 처리된 총 건수
		totalFilteredCount: {
			notExistRk: { type: Number },
			invalidEncodedRk: { type: Number },
			invalidEncodedRsKeyValue: { type: Number },
			notExistRequired: { type: Number },
			notExistMeta: { type: Number }
		},

		// chunkInfos
		chunkInfos: {
			type: [{
				_id: false,

				// 청크 번호
				chunkNumber: { type: Number, required:true },

				// 청크 시작 라인
				startLine: { type: Number, required:true },

				// 청크 종료 라인
				endLine: { type: Number, required:true },

				// 청크 처리 상태 [READY, COMPLETE, FAILED]
				state: { type: String, required:true, uppercase:true, enum:['READY', 'COMPLETE', 'FAILED'] },

				// 데이터 가공 처리 완료된 개수
				successCount: { type: Number, required:true },

				// 케이스별 필터 처리된 건수
				filteredCount: {
					notExistRk: { type: Number },
					invalidEncodedRk: { type: Number },
					invalidEncodedRsKeyValue: { type: Number },
					notExistRequired: { type: Number },
					notExistMeta: { type: Number }
				},
			}]
		},

		// 전체 처리 상태 [READY, COMPLETE, FAILED, NO_DATA]
		state: { type: String, required:true, uppercase:true, enum:['READY', 'COMPLETE', 'FAILED', 'NO_DATA'] },

		begunAt: {
			type: Date,
			default: Date.now
		},

		endedAt: {
			type: Date,
			default: Date.now
		},

		createdAt: {
			type: Date,
			default: Date.now
		},

		modifiedAt: {
			type: Date,
			default: Date.now
		}
	});

	BatchReportResult = conn.model('BatchReportResult', scheme, 'BatchReportProcessingResults');

	return scheme;
};

export default createSchemaAndModel;

export { BatchReportResult };
