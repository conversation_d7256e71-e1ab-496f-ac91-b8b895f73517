'use strict';

import mongoose from 'mongoose';

let ExtendedKeyValueJob = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema(
		{
			// I(Indivisual), F(Fullsync)
			type: {
				type: String,
				uppercase: true,
				required:true,
				enum:['I', 'F']
			},

			status: {
				type: String,
				uppercase: true,
				required:true,
				enum:['READY', 'ONGOING', 'STANDBY', 'COMPLETE', 'FAILURE', 'INVALID']
			},

			publisher_id: {
				_id: false,
				type: mongoose.Schema.Types.ObjectId,
				ref: 'Publisher'
			},

			// Extension Key Name; 반드시 publisher에서 파라미터를 통하여 전달이 되어야 함
			key: {
				type: String
			},

			// Extension Key Name; 반드시 publisher에서 파라미터를 통하여 전달이 되어야 함
			value: {
				type: String
			},

			// GET, PUT, POST, DELETE
			httpMethod: {
				type: String,
				uppercase: true,
				enum:['GET', 'PUT', 'POST', 'DELETE']
			},

			// Extension Key와 Mapping 되어 확장될 Key-Value 조합 Object; link to ExtensionItem Object
			items: {
				type:[
					{
						key: {
							type: String,
							trim: true,
							minlength: 1,
							_id : false
						},
						value: {
							type: String,
							trim: true,
							minlength: 1,
							_id : false
						}
					}
				]
			},

			// 실패( status = 'FAILURE') 시 상세 메시지
			error: {
				type: Object
			},

			totalCnt: Number,

			retryCnt: Number,

			alarm: {
				type: Number,
				enum: [0, 1]
			},

			createdAt: {
				type: Date,
				default: Date.now
			},

			modifiedAt: {
				type: Date,
				default: Date.now
			},

			scheduledAt: {
				type: Date
			},

			begunAt: {
				type: Date
			},

			endedAt: {
				type: Date
			}
		},
		{
			versionKey: false
		}
	);

	ExtendedKeyValueJob = conn.model('ExtendedKeyValueJob', scheme, 'ExtendedKeyValueJobs');

	return scheme;
};

export default createSchemaAndModel;

export { ExtendedKeyValueJob };
