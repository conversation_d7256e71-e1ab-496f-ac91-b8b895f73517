'use strict';

import mongoose from 'mongoose';

let MultiDimensionalReportDimensionDefinition = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		_id: { type:  mongoose.Schema.Types.ObjectId},

		reportTypes: [{
			type: String, trim: true, enum: ['dimension', 'metric']
		}],

		category: {
			type: String, trim: true
		},

		variable: {
			type: String, trim: true
		},

		description: {
			type: String, trim: true
		},

		query: {
			base: String,
			aggregation: String,
			finalization: String
		},

		order: {
			type: Number
		},

		tooltip: {
			type: String,
			trim: true,
		},

		mandatoryDimensions:[{
			type: String
		}],

		embededDimensions:[{
			type: String
		}]
	});

	MultiDimensionalReportDimensionDefinition = conn.model('MultiDimensionalReportDimensionDefinition', scheme, 'MultiDimensionalReportDimensionDefinitions');

	return scheme;
};

export default createSchemaAndModel;

export { MultiDimensionalReportDimensionDefinition };
