'use strict';

import mongoose from 'mongoose';

/*************************************************************************
	수익쉐어 리포트 재시도 대상
 */

let RevenueSharingRetryForAdProviderStat = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema(
		{
			adProviderReportApiRetryYmd: { // AdProvider 리포트 API가 재시도를 수행한 날짜
				type: String
			},

			targets: { // 수익쉐어 재시도 대상
				type: [{
					ymd: {
						type: String,
					},
					publisher_id: {
						type: mongoose.Schema.Types.ObjectId,
						ref: 'Publishers'
					},
					state : { // 상태: 'READY', 'COMPLETE', 'FAILED'
						type: String,
						uppercase: true,
						enum: ['READY', 'COMPLETE', 'FAILED']
					},
					adProvider_ids: [{
						type: mongoose.Schema.Types.ObjectId,
						ref: 'AdProviders'
					}],
					modifiedAt: {
						type: Date,
						default: Date.now
					}
				}]
			},

			overallState : { // 상태: 'READY', 'COMPLETE', 'FAILED'
				type: String,
				uppercase: true,
				enum: ['READY', 'COMPLETE', 'FAILED']
			},

			createdAt: {
				type: Date,
				default: Date.now
			},

			// 최종생성완료일시
			modifiedAt: {
				type: Date
			}
		},
		{
			versionKey: false
		}
	);

	RevenueSharingRetryForAdProviderStat = conn.model('RevenueSharingRetryForAdProviderStat', scheme, 'RevenueSharingRetryForAdProviderStats');

	return scheme;
};

export default createSchemaAndModel;

export { RevenueSharingRetryForAdProviderStat };
