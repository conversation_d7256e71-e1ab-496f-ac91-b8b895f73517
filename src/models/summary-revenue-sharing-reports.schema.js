'use strict';

import mongoose from 'mongoose';


let SummaryRevenueSharingReport = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema(
		{
			// 조건ID
			summaryRevenueSharingSchedule_id: {
				_id: false,
				type: mongoose.Schema.Types.ObjectId,
				ref: 'SummaryRevenueSharingSchedule'
			},

			bundleId: {
				_id: false,
				type: mongoose.Schema.Types.ObjectId
			},

			// 데이터 시작일
			beginYmd: {
				type: String
			},

			// 데이터 종료일
			endYmd: {
				type: String
			},

			// 파일경로
			dateForFileName: {
				type: String,
				trim: true
			},

			// 파일경로
			filePath: {
				type: String,
				trim: true
			},

			// 파일명
			fileName: {
				type: String,
				trim: true
			},

			// 상태 : DSP 데이터 조회중, SSP 데이터 조회 중, DSP-SSP 병합중, 파일 생성중, 파일 생성완료
			progress: {
				type: String,
				trim: true
			},

			rkCntInDb: {
				type: Number,
				default: 0
			},
			rkCntInFile: {
				type: Number,
				default: 0
			},

			nonRkCntInDb: {
				type: Number,
				default: 0
			},
			nonRkCntInFile: {
				type: Number,
				default: 0
			},

			gfpCntInDb: {
				type: Number,
				default: 0
			},
			gfpCntInFile: {
				type: Number,
				default: 0
			},

			// 생성일시
			createdAt: {
				type: Date,
				default: Date.now
			},

			// 수정일시
			modifiedAt: {
				type: Date,
				default: Date.now
			},

			// 시작일시
			begunAt: {
				type: Date
			},

			// 완료일시
			completedAt: {
				type: Date
			}
		},
		{
			versionKey: false
		}
	);

	SummaryRevenueSharingReport = conn.model('SummaryRevenueSharingReport', scheme, 'SummaryRevenueSharingReports');

	return scheme;
};

export default createSchemaAndModel;

export { SummaryRevenueSharingReport };
