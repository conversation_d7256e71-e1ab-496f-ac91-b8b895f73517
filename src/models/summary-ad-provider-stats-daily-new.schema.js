'use strict';

import mongoose from 'mongoose';

let SummaryAdProviderStatsDaily_new = {};

let createSchemaAndModel = (conn) => {
    let scheme = mongoose.Schema({
		ymd: { type: String, required:true },

		publisher_id: { type: mongoose.Schema.Types.ObjectId, required:true },
		adProvider_id: { type: mongoose.Schema.Types.ObjectId, required:true },
		adProviderPlaceKey : { type: String, required:true },

		adProviderPlace_ids: { type: [{ type: mongoose.Schema.Types.ObjectId }] },
		adUnitIds: [String],

		dealId: String,

		country: String,
		os: String,
		size: String,
		adSource: String,

		imp: { type: Number, required:true },
		clk: { type: Number, required:true },

		// GFP 순수익 USD/KRW - GFP 수수료 제외
		netRevenueUSD: { type: mongoose.Schema.Types.Decimal128 },
		netRevenueKRW: { type: mongoose.Schema.Types.Decimal128 },

		// AP 순수익 USD/KRW - AP 수수료, 대행사 수수료 등 제외
		revenueUSD: { type: mongoose.Schema.Types.Decimal128 },
		revenueKRW: { type: mongoose.Schema.Types.Decimal128 },

		createdAt: { type: Date, default: Date.now }
	}, { versionKey: false });

	SummaryAdProviderStatsDaily_new = conn.model('SummaryAdProviderStatsDaily_new', scheme, 'SummaryAdProviderStatsDaily_new');

    return scheme;
};

export default createSchemaAndModel;

export { SummaryAdProviderStatsDaily_new };
