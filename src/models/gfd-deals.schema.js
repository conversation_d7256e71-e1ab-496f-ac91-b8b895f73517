'use strict';

import mongoose from 'mongoose';

let GFDDeal = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		dealId: { type: String, trim: true, required: true },
		name: {type: String, trim: true, required: true},
	}, {
		_id: true,
		id: false,
		versionKey: false,
	});

	GFDDeal = conn.model('GFDDeal', scheme, 'GFDDeals');

	return scheme;
};

export default createSchemaAndModel;

export {GFDDeal};
