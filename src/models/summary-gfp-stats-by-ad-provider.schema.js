'use strict';

import mongoose from 'mongoose';

let SummaryGfpStatsByAdProvider = {};

let createSchemaAndModel = (conn) => {
    let scheme = mongoose.Schema({
		ymd : { type: String, required:true },

		publisher_id : { type: mongoose.Schema.Types.ObjectId, required:true },
		adProvider_id : { type: mongoose.Schema.Types.ObjectId, required:true },
		country: String,

		imp: { type: Number, required:true },
		viewableImp: { type: Number, required:true },
		clk: { type: Number, required:true },

		createdAt: { type: Date, default: Date.now }

	}, {
		//_id: false,
		//id: false,
		versionKey: false,
	});

	SummaryGfpStatsByAdProvider = conn.model('SummaryGfpStatsByAdProvider', scheme, 'SummaryGfpStatsByAdProvider');

    return scheme;
};

export default createSchemaAndModel;

export { SummaryGfpStatsByAdProvider };
