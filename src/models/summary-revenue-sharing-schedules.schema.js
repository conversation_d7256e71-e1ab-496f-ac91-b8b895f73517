'use strict';

import mongoose from 'mongoose';

/*************************************************************************
	수익쉐어 리포트 생성 조건
 */

let SummaryRevenueSharingSchedule = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema(
		{
			publisher_id: {
				_id: false,
				type: mongoose.Schema.Types.ObjectId,
				ref: 'Publisher'
			},

			// 이름
			name: {
				type: String,
				minlength: 1,
				trim: true
			},

			// 설명
			description: {
				type: String,
				trim: true,
				minlength: 1
			},

			// 상태: ON, OFF
			status : {
				type: String,
				uppercase: true
			},

			// 포함시킬 통계타입: AdProvider, GFP
			statsType : {
				AdProvider: Number,
				GFP: Number
			},

			// GFP 추정수익 출력 여부
			estimatedRevenuePrint: {
				type: Number,
				enum: [0,1],
				default: 0
			},

			// 집계단위 : 일, 월
			interval : {
				type: String,
				uppercase: true
			},

			// 생성주기 : 일, 월
			period : {
				type: String,
				uppercase: true
			},

			// 파일생성기준 : DAILY, MONTHLY
			fileCreateCriteria : {
				type: String,
				uppercase: true
			},

			// 일단위 일경우 며칠 전 데이터부터 수집하는지
			howManyDaysBefore : {
				type: Number
			},

			keyGroup_id: {
				_id: false,
				type: mongoose.Schema.Types.ObjectId,
				ref: 'Publisher'
			},

			creator_id: {
				_id: false,
				type: mongoose.Schema.Types.ObjectId,
				ref: 'User'
			},

			createdAt: {
				type: Date,
				default: Date.now
			},

			// 최종생성완료일시
			lastCompletedAt: {
				type: Date
			},

			// 재집계 시 알림을 받을 수신자
			reAggregationAlarmReceiver: {
				ap: [String], // AdProvider 재집계 시 수신자
				gfp: [String] // GFP 재집계 시 수신자
			},

			cmsType: String
		},
		{
			versionKey: false
		}
	);

	SummaryRevenueSharingSchedule = conn.model('SummaryRevenueSharingSchedule', scheme, 'SummaryRevenueSharingSchedules');

	return scheme;
};

export default createSchemaAndModel;

export { SummaryRevenueSharingSchedule };
