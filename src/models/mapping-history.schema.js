'use strict';

import mongoose from 'mongoose';

let MappingHistory = {};

let createSchemaAndModel = (conn) => {
    let scheme = mongoose.Schema({

		adUnitId : { type: String, trim: true },

		adUnit_id : {
			_id: false,
			type: mongoose.Schema.Types.ObjectId,
			ref: 'AdUnit'
		},

		adProviderPlace_id : {
			_id: false,
			type: mongoose.Schema.Types.ObjectId,
			ref: 'Place'
		},

		biddingGroup_id : {
			_id: false,
			type: mongoose.Schema.Types.ObjectId,
			ref: 'BiddingGroup'
		},

		deal_id : {
			_id: false,
			type: mongoose.Schema.Types.ObjectId,
			ref: 'GFDDeal'
		},

        createdAt : Date,
        modifiedAt : Date,

	}, {
		_id: false,
		id: false,
		versionKey: false,
	});

    scheme.set('toJSON',{ getters : true , virtuals : true });

    MappingHistory = conn.model('MappingHistory', scheme, 'MappingHistory');

    return scheme;
};

export default createSchemaAndModel;

export { MappingHistory };
