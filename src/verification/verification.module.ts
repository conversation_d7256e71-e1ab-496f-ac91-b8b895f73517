import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { API_DB_NAME, CMS_DB_NAME } from '../db/constants';

import { ApiUserModel } from '../report/schema/api-user.schema';
import { EnvironmentModel } from '../report/schema/environment.schema';

import { VerificationController } from './verification.controller';
import { VerificationService } from './verification.service';
import { MonitoringReportApiStatsModel } from "../report/schema/monitoring-report-api-stats.schema";
import { SyncPublisherModel } from "../report/schema/sync-publisher.schema";

@Module({
	imports: [
		MongooseModule.forFeature([
			ApiUserModel,
		], CMS_DB_NAME),
		MongooseModule.forFeature([
			EnvironmentModel,
			SyncPublisherModel,
			MonitoringReportApiStatsModel,
		], API_DB_NAME),
	],
	controllers: [VerificationController],
	providers: [VerificationService],
	exports: []
})
export class VerificationModule {
}
