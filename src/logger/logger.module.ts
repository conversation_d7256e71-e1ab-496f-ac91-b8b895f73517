import { join } from 'path';
import { TransportTargetOptions } from 'pino';
import { v4 as uuidv4 } from 'uuid';
import moment from 'moment';
import { ConfigService } from '@nestjs/config';
import { LoggerModule } from 'nestjs-pino';

const neloTransport = join(__dirname, 'nelo-pino-transport.js');
const rotatorTransport = join(__dirname, 'file-rotate-transport.js');

function neloTransportDef(config): TransportTargetOptions {
  const url = config.get('logger.nelo.url');
  if (!url) {
    throw new Error('logger.nelo.url not configured');
  }

  const projectName = config.get('logger.nelo.projectName');
  if (!projectName) {
    throw new Error('logger.nelo.projectName not configured');
  }

  return {
    target: neloTransport,
    options: {
      url,
      projectName,
      projectVersion: config.get('logger.nelo.projectVersion') ?? '0.1-dev',
      maxInTransit: config.get('logger.nelo.maxInTransit') ?? 10,
      timeout: config.get('logger.nelo.timeoutMs') ?? 5000,
    },
    // level: config.get('logger.nelo.level') ?? 'error',
    level: 'info',
  };
}

function rotatorTransportDef(config): TransportTargetOptions {
  return {
    target: rotatorTransport,
    options: {
      // 'file-stream-rotator' options
      filename: config.get('logger.rotator.path') ?? './api.log',
      frequency: config.get('logger.rotator.frequency') ?? 'daily',
      max_logs: config.get('logger.rotator.maxLogFiles') ?? 5,
      size: config.get('logger.rotator.maxLogSize') ?? '10m',
    },
    level: config.get('logger.rotator.level') ?? 'trace',
  };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function prettyTransportDef(config): TransportTargetOptions {
  return {
    target: 'pino-pretty',
    options: {},
    level: 'trace',
  };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function fileTransportDef(config): TransportTargetOptions {
  return {
    target: 'pino/file',
    options: {
      destination: './nam-api.log',
      mkdir: true,
      append: true,
    },
    level: 'trace',
  };
}

export default () => {
  return LoggerModule.forRootAsync({
    inject: [ConfigService],
    useFactory: async (config: ConfigService) => {
      let requestIdHeader: string = config.get('logger.requestIdHeader');
      if (requestIdHeader) {
        requestIdHeader = requestIdHeader.toLowerCase();
      }

      return {
        pinoHttp: {
          level: config.get('logger.level'),
          formatters: {
            level(label, number) {
              return { level: number };
            },
          },
          // timestamp: pino.stdTimeFunctions.isoTime,
          timestamp: () => `,"time":"${moment().format('YYYY-MM-DD HH:mm:ss.SSSZ')}"`,
          transport: {
            targets: [neloTransportDef(config), rotatorTransportDef(config), prettyTransportDef(config)],
          },
          quietReqLogger: true,
          genReqId(req) {
            return requestIdHeader && req.headers[requestIdHeader] ? req.headers[requestIdHeader] : uuidv4();
          },
        },
      };
    },
  });
};
