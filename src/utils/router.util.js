/* jshint node: true */
'use strict';

import logger from './logger.util';


module.exports = router => {

    router.use("*", async (ctx, next) => {

        //logger.debug(ctx);

        if (ctx.params) {
            //logger.debug('[routes] ---------------------------------------- request params');
            //logger.debug(ctx.params);
        }

        if (ctx.request.body) {
            logger.debug('[routes] ---------------------------------------- request url');
            logger.debug(ctx.method, ctx.request.url);
            logger.debug('[routes] ---------------------------------------- request body values\n' + JSON.stringify(ctx.request.body, null, 2));
        }

        var start = Date.now();

        await next();


        logger.debug(`[routes] 소요 시간: ${Date.now() - start} ms`);

    });

};
