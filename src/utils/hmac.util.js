'use strict';

import crypto from 'crypto';

const MAX_MESSAGESIZE = 255;


/**
 * Hmac 인증키로 encrypedUrl 가져오기
 * 	- URL 내 쿼리스트링은 별도 인코딩 처리 해줘야함
 * 	- URL 길이는 최대 255
 *
 * @param {Object} { url, key, algorithm }
 * @return {String} encrypedUrl
 */
module.exports.getEncryptedUrlByKey = ({ url, key, algorithm='sha1' }) => {
	const currentTime = new Date().getTime();
	const message = url.substring(0, Math.min(MAX_MESSAGESIZE, url.length)) + currentTime;

	let md = crypto.createHmac(algorithm, key).update(message).digest('base64');
	md = encodeURIComponent(md);

	let seperator = '?';
	if (url.indexOf('?') != -1) {
		seperator = '&';
	}

	// ex> http://xxxx/xxxxxx?msgpad=1581051088060&md=***************************%3D
	const encrypedUrl = url + seperator + "msgpad=" + currentTime + '&md=' + md;

	return encrypedUrl;
};
