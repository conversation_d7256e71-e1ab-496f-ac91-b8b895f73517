'use strict';

import fetch from 'node-fetch';
import * as logger from '../utils/logger.util';

import WebHDFS from '@ssp/webhdfs'; // WEB HDFS
import fs from "fs";

/**
 * 파일이 있는지 확인
 * @param host
 * @param port
 * @param user
 * @param version
 * @param file
 * @returns {Promise<boolean>}
 */
module.exports.isExistHdfsFile = async ({host, port, user, version}, file) => {
	const url = `http://${host}:${port}${version}${file}?user.name=${user}&op=GETFILESTATUS`;
	// logger.debug(`_readFile() :: url:${url}`);

	try {
		const response = await fetch(url);

		if (response.ok) {
			const body = await response.json();
			// logger.debug('_isExistHdfsFile() :: 파일 있음.', JSON.stringify(body, null, 2));

			if (body.FileStatus && body.FileStatus.length > -1) {
				// 파일이 있을 때
				/*
				{
				  "FileStatus": {
					"pathSuffix": "",
					"type": "FILE",
					"length": 35,
					"owner": "irteam",
					"group": "supergroup",
					"permission": "644",
					"accessTime": 1588828156270,
					"modificationTime": 1588150449647,
					"blockSize": 134217728,
					"replication": 3
				  }
				}
				*/
				return true;
			} else {
				// 파일이 없을 때
				/*
				{
				  "RemoteException": {
					"message": "File does not exist: /tmp/test999",
					"exception": "FileNotFoundException",
					"javaClassName": "java.io.FileNotFoundException"
				  }
				}
				 */
				return false;
			}
		} else {
			if (response.status === 404) {
				return false;
			} else {
				logger.error('hdfs.util.isExistHdfsFile() :: 응답 오류:', response.status, response.statusText);
			}
		}
	} catch(error) {
		logger.error(error);
		throw error;
	}

	return false;
};

/**
 * {dir} 하위 리스트 조회
 * example: http://atcdh009-sa.nfra.io:14000/
 * 				webhdfs/v1/
 * 				data/log/gfp/gfp-revenue-sharing-report-v2/
 * 				daily/ or monthly/
 * 				20200624/ or 202006/
 * 				_publisherId=5e4cf96f28e100002e0ae004/
 * 				_adUnitId=I_NTV_SMR_VA/
 * 				_adProviderId=5be3aafe77bd856e4850945c/
 * 				_adProviderPlaceId=5e8ec6703abd000031a49c07
 * 				?user.name=irteam&op=LISTSTATUS
 *
 * 	일 별 파일경로:	'/webhdfs/v1/data/log/gfp/gfp-revenue-sharing-report-v2/daily/20200501/_publisherId={publisherId}/_adProviderId={adProviderId}/_adUnitId={adUnitId}/_adProviderPlaceId={adProviderPlaceId}/*.c000.avro'
 * 	월 별 파일경로:	'/webhdfs/v1/data/log/gfp/gfp-revenue-sharing-report-v2/monthly/202005/_publisherId={publisherId}/_adProviderId={adProviderId}/_adUnitId={adUnitId}/_adProviderPlaceId={adProviderPlaceId}/*.c000.avro'
 *
 * @param dir
 * @returns {Promise<null>}
 * @private
 */
module.exports.listHdfsDirectory = async ({host, port, user, version}, dir) => {
	let list = null;
	const url = `http://${host}:${port}${version}${dir}?user.name=${user}&op=LISTSTATUS`;
	// logger.debug(`_listHdfsDirectory() :: ${url}`);

	try {
		const response = await fetch(url);

		if (response.ok) {
			// 결과
			const body = await response.json();
			// logger.debug(`_listHdfsDirectory() :: hdfs ls placeDir:${dir}: ${JSON.stringify(body, null, 2)}`);

			if (body.FileStatuses && body.FileStatuses.FileStatus && body.FileStatuses.FileStatus.length > 0) {
				/*
					// 파일이 있을 때
					{
						"FileStatuses": {
							"FileStatus": [
								{
									"pathSuffix": "test",
									"type": "DIRECTORY",
									"length": 0,
									"owner": "Naver",
									"group": "supergroup",
									"permission": "755",
									"accessTime": 0,
									"modificationTime": 1521879638543,
									"blockSize": 0,
									"replication": 0
								},
								{
									"pathSuffix": "test5",
									"type": "FILE",
									"length": 35,
									"owner": "irteam",
									"group": "supergroup",
									"permission": "644",
									"accessTime": 1588828156270,
									"modificationTime": 1588150449647,
									"blockSize": 134217728,
									"replication": 3
								}
							]
						}
					}
				*/
				list = body.FileStatuses.FileStatus;
			} else { // ${placeDir}이 비어 있음.
				/*
					// 파일이 없을 때
					{
					  "RemoteException": {
						"message": "File /asdf does not exist.",
						"exception": "FileNotFoundException",
						"javaClassName": "java.io.FileNotFoundException"
					  }
					}
				 */
			}
		} else {
			if (response.status === 404) {
				// logger.debug(`_listHdfsDirectory() :: 응답:${response.status} ${response.statusText} placeDir:${placeDir} 해당 디렉토리가 없음.`);
			} else {
				logger.error(`hdfs.util.listHdfsDirectory() :: dir:${dir} 응답 오류:${response.status} ${response.statusText}`);
			}
		}
	} catch(error) {
		throw error;
	}

	return list;
};

const example_avroSchemaServer = {
	"type": "record",
	"name": "daSspServer",
	"namespace": "com.navercorp.da",
	"doc": "DA SSP Server Call",
	"fields": [
		{
			"name": "requestId",
			"type": "string",
			"doc": "요청 ID - SSP Server에서 요청을 받으면 생성하는 유니크값"
		},
		{
			"name": "publisherId",
			"type": "string",
			"doc": "퍼블리셔 ID"
		},
		// {
		// 	"name": "adUnitId",
		// 	"type": "string",
		// 	"doc": "광고유닛 ID"
		// },
		// {
		// 	"name": "creativeType",
		// 	"type": "string",
		// 	"doc": "상품 유형 - [BANNER(배너) | NATIVE(네이티브)| VIDEO(동영상)]"
		// },
		// {
		// 	"name": "country",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "국가",
		// 	"default": null
		// },
		// {
		// 	"name": "gender",
		// 	"type": "string",
		// 	"doc": "성별 - [M(남자) | F(여자) | O(알수없음)]"
		// },
		// {
		// 	"name": "age",
		// 	"type": [
		// 		"null",
		// 		"int"
		// 	],
		// 	"doc": "나이",
		// 	"default": null
		// },
		// {
		// 	"name": "userIp",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "사용자 IP",
		// 	"default": null
		// },
		// {
		// 	"name": "userAgent",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "UserAgent - App에서 호출하는 경우 Blank",
		// 	"default": null
		// },
		// {
		// 	"name": "keyValue",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "Key Value - 퍼블리셔가 SSP에 전달하는 파라미터 중에서 Mediation 및 리포트 조회용 파라미터 정보",
		// 	"default": null
		// },
		// {
		// 	"name": "nnb",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "NNB 쿠키 - 네이버 B쿠키",
		// 	"default": null
		// },
		// {
		// 	"name": "nidIdNo",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "nidIdNo - 네이버 Login Id에 대한 임의의 유니크 번호",
		// 	"default": null
		// },
		// {
		// 	"name": "adid",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "모바일 광고ID - Android ADID or iOS IDFA",
		// 	"default": null
		// },
		// {
		// 	"name": "test",
		// 	"type": "int",
		// 	"doc": "테스트 여부 - [0(No) | 1(Yes)]"
		// },
		// {
		// 	"name": "requestTime",
		// 	"type": "string",
		// 	"doc": "요청일시 - Unix Epoch Time"
		// },
		// {
		// 	"name": "responses",
		// 	"type": {
		// 		"type": "array",
		// 		"items": {
		// 			"type": "record",
		// 			"name": "response",
		// 			"doc": "response",
		// 			"fields": [
		// 				{
		// 					"name": "adProviderId",
		// 					"type": "string",
		// 					"doc": "DSP ID - DSP를 지칭하기 위해 만든 SSP 내부에서 사용하는 ID"
		// 				},
		// 				{
		// 					"name": "adProviderPlaceId",
		// 					"type": "string",
		// 					"doc": "Place ID - DSP 에서 실제 광고를 호출하는 단위의 정보를 지칭하기 위해 만든 SSP 내부에서 사용하는 ID"
		// 				},
		// 				{
		// 					"name": "stat",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "응답 결과 - [0(에러) | 1(성공) | 2(광고없음) | 3(타임아웃)]",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "rank",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "우선순위(=tier)",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "rate",
		// 					"type": [
		// 						"null",
		// 						"float"
		// 					],
		// 					"doc": "할당비율",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "timeout",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "연결제한시간(ms)",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "responseTime",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "응답소요시간(ms)",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "connectionType",
		// 					"type": [
		// 						"null",
		// 						"string"
		// 					],
		// 					"doc": "연결 유형 - [C2S(클라이언트 SDK 연결) | S2S(서버 연결)]",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "servingType",
		// 					"type": [
		// 						"null",
		// 						"string"
		// 					],
		// 					"doc": "처리 유형 - [DIRECT(단독입찰) | BID(비딩 입찰)]",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "size",
		// 					"type": [
		// 						"null",
		// 						"string"
		// 					],
		// 					"doc": "소재 사이즈",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "currency",
		// 					"type": [
		// 						"null",
		// 						"string"
		// 					],
		// 					"doc": "환율",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "bidPrice",
		// 					"type": [
		// 						"null",
		// 						"float"
		// 					],
		// 					"doc": "입찰가",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "priority",
		// 					"type": [
		// 						"null",
		// 						"long"
		// 					],
		// 					"doc": "노출 우선순위",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "placeKey",
		// 					"type": [
		// 						"null",
		// 						"string"
		// 					],
		// 					"doc": "Place 연동 Key - Place 자체 지면 구분 ID",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "dealId",
		// 					"type": [
		// 						"null",
		// 						"string"
		// 					],
		// 					"doc": "Deal Id",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "requestSizes",
		// 					"type": [
		// 						"null",
		// 						{
		// 							"type": "array",
		// 							"items": "string"
		// 						}
		// 					],
		// 					"doc": "요청 소재 사이즈 목록",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "responseSize",
		// 					"type": [
		// 						"null",
		// 						"string"
		// 					],
		// 					"doc": "응답 소재 사이즈",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "biddingGroupId",
		// 					"type": [
		// 						"null",
		// 						"string"
		// 					],
		// 					"doc": "BiddingGroup Id",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "requestCreativeTypes",
		// 					"type": [
		// 						"null",
		// 						{
		// 							"type": "array",
		// 							"items": "string"
		// 						}
		// 					],
		// 					"doc": "요청 상품유형 목록",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "responseCreativeType",
		// 					"type": [
		// 						"null",
		// 						"string"
		// 					],
		// 					"doc": "응답 상품유형",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "bidFloor",
		// 					"type": [
		// 						"null",
		// 						"float"
		// 					],
		// 					"doc": "최소입찰가",
		// 					"default": null
		// 				}
		// 			]
		// 		}
		// 	}
		// },
		// {
		// 	"name": "area1",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "접속 지역1 - 광역시/도 or ISO 3166-2 region",
		// 	"default": null
		// },
		// {
		// 	"name": "area2",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "접속 지역2 - 시/군/구 or ISO 3166-2 city",
		// 	"default": null
		// },
		// {
		// 	"name": "area3",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "접속 지역3 - 읍/면/동 or '-'",
		// 	"default": null
		// },
		// {
		// 	"name": "os",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "OS",
		// 	"default": null
		// },
		// {
		// 	"name": "osVer",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "OS Version",
		// 	"default": null
		// },
		// {
		// 	"name": "browser",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "Browser",
		// 	"default": null
		// },
		// {
		// 	"name": "browserVer",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "BrowserVersion",
		// 	"default": null
		// },
		// {
		// 	"name": "device",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "Device",
		// 	"default": null
		// },
		// {
		// 	"name": "interests",
		// 	"type": [
		// 		"null",
		// 		{
		// 			"type": "array",
		// 			"items": "string"
		// 		}
		// 	],
		// 	"doc": "User Interest - DMP에서 추출한 사용자 관심사 항목",
		// 	"default": null
		// },
		// {
		// 	"name": "rsKeyValue",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "Revenue Share Key Valu - 퍼블리셔가 SSP에 전달하는 파라미터 중에서 수익쉐어와 관련된 파라미터 정보",
		// 	"default": null
		// },
		// {
		// 	"name": "param",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "파라미터 - 퍼블리셔가 SSP에 전달하는 파라미터이며 페이지 URL의 파라미터 정보가 아님",
		// 	"default": null
		// },
		// {
		// 	"name": "geo",
		// 	"type": [
		// 		"null",
		// 		{
		// 			"type": "record",
		// 			"name": "geo",
		// 			"doc": "지역",
		// 			"fields": [
		// 				{
		// 					"name": "deviceGeo",
		// 					"type": [
		// 						"null",
		// 						{
		// 							"type": "record",
		// 							"name": "deviceGeo",
		// 							"doc": "접속지역",
		// 							"fields": [
		// 								{
		// 									"name": "country",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "Country code using ISO-3166-1-alpha-2 - MaxMind 추출 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "region",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "Region code using ISO-3166-2; 2-letter state code if USA. - MaxMind 추출 결과; 해외인경우에만 존재",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "city",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "City using United Nations Code for Trade & Transport Locations - MaxMind 추출 결과; 해외인경우에만 존재",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "barea1",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "법정동 광역시/도 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "barea2",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "법정동 시/군/구 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "barea3",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "법정동 읍/면/동 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "bcode",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "법정동 코드 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "rcodeOfBcode",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "법정동 코드에 매핑된 네이버 지역 코드 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "harea1",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "행정동 광역시/도 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "harea2",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "행정동 시/군/구 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "harea3",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "행정동 읍/면/동 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "hcode",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "행정동 코드 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "rcodeOfHcode",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "행정동 코드에 매핑된 네이버 지역 코드 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								}
		// 							]
		// 						}
		// 					],
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "userGeo",
		// 					"type": [
		// 						"null",
		// 						{
		// 							"type": "record",
		// 							"name": "userGeo",
		// 							"doc": "사용자지역",
		// 							"fields": [
		// 								{
		// 									"name": "country",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "Country code using ISO-3166-1-alpha-2 - MaxMind 추출 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "region",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "Region code using ISO-3166-2; 2-letter state code if USA. - MaxMind 추출 결과; 해외인경우에만 존재",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "city",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "City using United Nations Code for Trade & Transport Locations - MaxMind 추출 결과; 해외인경우에만 존재",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "barea1",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "법정동 광역시/도 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "barea2",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "법정동 시/군/구 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "barea3",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "법정동 읍/면/동 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "bcode",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "법정동 코드 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "rcodeOfBcode",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "법정동 코드에 매핑된 네이버 지역 코드 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "harea1",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "행정동 광역시/도 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "harea2",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "행정동 시/군/구 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "harea3",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "행정동 읍/면/동 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "hcode",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "행정동 코드 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								},
		// 								{
		// 									"name": "rcodeOfHcode",
		// 									"type": [
		// 										"null",
		// 										"string"
		// 									],
		// 									"doc": "행정동 코드에 매핑된 네이버 지역 코드 - Ngin or Geocoding 조회 결과",
		// 									"default": null
		// 								}
		// 							]
		// 						}
		// 					],
		// 					"default": null
		// 				}
		// 			]
		// 		}
		// 	],
		// 	"default": null
		// },
		// {
		// 	"name": "mloc",
		// 	"type": [
		// 		"null",
		// 		{
		// 			"type": "record",
		// 			"name": "mloc",
		// 			"doc": "mloc 위경도",
		// 			"fields": [
		// 				{
		// 					"name": "latitude",
		// 					"type": [
		// 						"null",
		// 						"string"
		// 					],
		// 					"doc": "mloc에 입력된 위도",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "longitude",
		// 					"type": [
		// 						"null",
		// 						"string"
		// 					],
		// 					"doc": "mloc에 입력된 경도",
		// 					"default": null
		// 				}
		// 			]
		// 		}
		// 	],
		// 	"default": null
		// },
		// {
		// 	"name": "publisherUserId",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "퍼블리셔 사용자 식별자",
		// 	"default": null
		// },
		// {
		// 	"name": "elapsedTime",
		// 	"type": [
		// 		"null",
		// 		{
		// 			"type": "record",
		// 			"name": "elapsedTime",
		// 			"doc": "경과 시간(ms)",
		// 			"fields": [
		// 				{
		// 					"name": "x0",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "첫번째 로직 경과 시간",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "x1",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "두번째 로직 경과 시간",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "x2",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "세번째 로직 경과 시간",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "x3",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "네번째 로직 경과 시간",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "x4",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "다섯번째 로직 경과 시간",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "x5",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "여섯번째 플랫폼 경과 시간",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "x6",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "일곱번째 플랫폼 경과 시간",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "x7",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "여덟번째 로직 경과 시간",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "x8",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "아홉번째 로직 경과 시간",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "x9",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "열번째 로직 경과 시간",
		// 					"default": null
		// 				}
		// 			]
		// 		}
		// 	],
		// 	"default": null
		// },
		// {
		// 	"name": "hostname",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "server host name",
		// 	"default": null
		// },
		// {
		// 	"name": "creativeTypes",
		// 	"type": [
		// 		"null",
		// 		{
		// 			"type": "array",
		// 			"items": "string"
		// 		}
		// 	],
		// 	"doc": "소재 유형 목록 - [BANNER(배너) | NATIVE(네이티브)| VIDEO(동영상)]",
		// 	"default": null
		// },
		// {
		// 	"name": "optOut",
		// 	"type": [
		// 		"null",
		// 		{
		// 			"type": "record",
		// 			"name": "optOut",
		// 			"doc": "Opt-Out 정보",
		// 			"fields": [
		// 				{
		// 					"name": "id",
		// 					"type": [
		// 						"null",
		// 						"string"
		// 					],
		// 					"doc": "Opt-Out 처리 대상 서비스 ID",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "identity",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "식별 정보 Opt-Out 여부 - [0(No) | 1(Yes)]",
		// 					"default": null
		// 				},
		// 				{
		// 					"name": "nonIdentity",
		// 					"type": [
		// 						"null",
		// 						"int"
		// 					],
		// 					"doc": "비식별 정보 Opt-Out 여부 - [0(No) | 1(Yes)]",
		// 					"default": null
		// 				}
		// 			]
		// 		}
		// 	],
		// 	"default": null
		// },
		// {
		// 	"name": "sdkVer",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "SDK Version",
		// 	"default": null
		// },
		// {
		// 	"name": "videoAdScheduleId",
		// 	"type": [
		// 		"null",
		// 		"string"
		// 	],
		// 	"doc": "Video Ad Schedule Id",
		// 	"default": null
		// },
		// {
		// 	"name": "contentLength",
		// 	"type": [
		// 		"null",
		// 		"int"
		// 	],
		// 	"doc": "컨텐츠 재생 시간 (초단위)",
		// 	"default": null
		// },
		// {
		// 	"name": "startDelay",
		// 	"type": [
		// 		"null",
		// 		"int"
		// 	],
		// 	"doc": "광고 재생 시점(0: pre-roll, > 0: mid-roll, -2: post-roll)",
		// 	"default": null
		// }
	]
};

const example_avroSchemaClient = {
	"type": "record",
	"name": "daSspClient",
	"namespace": "com.navercorp.da",
	"doc": "DA SSP Client Call",
	"fields": [
		{
			"name": "isValid",
			"type": [
				"null",
				"string"
			],
			"doc": "0.어뷰즈 판정",
			"default": null
		},
		{
			"name": "requestId",
			"type": "string",
			"doc": "요청 ID - SSP Server에서 요청을 받으면 생성하는 유니크값"
		},
		{
			"name": "publisherId",
			"type": "string",
			"doc": "퍼블리셔 ID"
		},
		{
			"name": "adUnitId",
			"type": "string",
			"doc": "광고유닛 ID"
		},
		{
			"name": "creativeType",
			"type": "string",
			"doc": "상품 유형 - [BANNER(배너) | NATIVE(네이티브) | VIDEO(동영상)]"
		},
		{
			"name": "gender",
			"type": "string",
			"doc": "성별 - [M(남자) | F(여자) | O(알수없음)]"
		},
		{
			"name": "age",
			"type": [
				"null",
				"int"
			],
			"doc": "나이",
			"default": null
		},
		{
			"name": "userIp",
			"type": [
				"null",
				"string"
			],
			"doc": "사용자 IP",
			"default": null
		},
		{
			"name": "userAgent",
			"type": [
				"null",
				"string"
			],
			"doc": "UserAgent - App에서 호출하는 경우 Blank",
			"default": null
		},
		{
			"name": "keyValue",
			"type": [
				"null",
				"string"
			],
			"doc": "Key Value - 퍼블리셔가 SSP에 전달하는 파라미터 중에서 Mediation 및 리포트 조회용 파라미터 정보",
			"default": null
		},
		{
			"name": "nnb",
			"type": [
				"null",
				"string"
			],
			"doc": "NNB 쿠키 - 네이버 B쿠키",
			"default": null
		},
		{
			"name": "nidIdNo",
			"type": [
				"null",
				"string"
			],
			"doc": "nidIdNo - 네이버 Login Id에 대한 임의의 유니크 번호",
			"default": null
		},
		{
			"name": "adid",
			"type": [
				"null",
				"string"
			],
			"doc": "모바일 광고ID - Android ADID or iOS IDFA",
			"default": null
		},
		{
			"name": "pageUrl",
			"type": [
				"null",
				"string"
			],
			"doc": "호출URL - SSP Server를 호출하는 페이지의 URL",
			"default": null
		},
		{
			"name": "referer",
			"type": [
				"null",
				"string"
			],
			"doc": "레퍼러 - pageUrl을 호출한 페이지의 URL",
			"default": null
		},
		{
			"name": "test",
			"type": "int",
			"doc": "테스트 여부 - [0(No) | 1(Yes)]"
		},
		{
			"name": "adProviderId",
			"type": [
				"null",
				"string"
			],
			"doc": "DSP ID - DSP를 지칭하기 위해 만든 SSP 내부에서 사용하는 ID",
			"default": null
		},
		{
			"name": "adProviderPlaceId",
			"type": [
				"null",
				"string"
			],
			"doc": "Place ID - DSP 에서 실제 광고를 호출하는 단위의 정보를 지칭하기 위해 만든 SSP 내부에서 사용하는 ID",
			"default": null
		},
		{
			"name": "size",
			"type": [
				"null",
				"string"
			],
			"doc": "소재 사이즈",
			"default": null
		},
		{
			"name": "currency",
			"type": [
				"null",
				"string"
			],
			"doc": "환율",
			"default": null
		},
		{
			"name": "bidPrice",
			"type": [
				"null",
				"float"
			],
			"doc": "입찰가",
			"default": null
		},
		{
			"name": "eventId",
			"type": [
				"null",
				"int"
			],
			"doc": "Event ID - [1(call) | 2(imp) | 3(click)]",
			"default": null
		},
		{
			"name": "eventTime",
			"type": [
				"null",
				"string"
			],
			"doc": "이벤트 발생일시",
			"default": null
		},
		{
			"name": "logId",
			"type": [
				"null",
				"string"
			],
			"doc": "Logging Id - Log Event 호출에 대한 Unique Id",
			"default": null
		},
		{
			"name": "area1",
			"type": [
				"null",
				"string"
			],
			"doc": "접속지역1 - 광역시/도 or ISO 3166-2 region",
			"default": null
		},
		{
			"name": "area2",
			"type": [
				"null",
				"string"
			],
			"doc": "접속지역2 - 시/군/구 or ISO 3166-2 city",
			"default": null
		},
		{
			"name": "area3",
			"type": [
				"null",
				"string"
			],
			"doc": "접속지역3 - 읍/면/동 or '-'",
			"default": null
		},
		{
			"name": "os",
			"type": [
				"null",
				"string"
			],
			"doc": "OS",
			"default": null
		},
		{
			"name": "osVer",
			"type": [
				"null",
				"string"
			],
			"doc": "OS Version",
			"default": null
		},
		{
			"name": "browser",
			"type": [
				"null",
				"string"
			],
			"doc": "Browser",
			"default": null
		},
		{
			"name": "browserVer",
			"type": [
				"null",
				"string"
			],
			"doc": "BrowserVersion",
			"default": null
		},
		{
			"name": "device",
			"type": [
				"null",
				"string"
			],
			"doc": "Device",
			"default": null
		},
		{
			"name": "interests",
			"type": [
				"null",
				{
					"type": "array",
					"items": "string"
				}
			],
			"doc": "User Interest - DMP에서 추출한 사용자 관심사 항목",
			"default": null
		},
		{
			"name": "country",
			"type": [
				"null",
				"string"
			],
			"doc": "접속지역 - 국가",
			"default": null
		},
		{
			"name": "placeKey",
			"type": [
				"null",
				"string"
			],
			"doc": "Place 연동 Key - Place 자체 지면 구분 ID",
			"default": null
		},
		{
			"name": "rsKeyValue",
			"type": [
				"null",
				"string"
			],
			"doc": "Revenue Share Key Valu - 퍼블리셔가 SSP에 전달하는 파라미터 중에서 수익쉐어와 관련된 파라미터 정보",
			"default": null
		},
		{
			"name": "param",
			"type": [
				"null",
				"string"
			],
			"doc": "파라미터 - 퍼블리셔가 SSP에 전달하는 파라미터이며 페이지 URL의 파라미터 정보가 아님",
			"default": null
		},
		{
			"name": "requestTime",
			"type": [
				"null",
				"string"
			],
			"doc": "요청일시 - millisecond based Unix Epoch Time",
			"default": null
		},
		{
			"name": "requestedAdProviderNames",
			"type": [
				"null",
				{
					"type": "array",
					"items": "string"
				}
			],
			"doc": "AdProvider Name 목록 - 요청한 AdProvider에 한정됨",
			"default": null
		},
		{
			"name": "adPosition",
			"type": [
				"null",
				"string"
			],
			"doc": "광고 노출 위치 - 여러개의 광고가 한번에 호출되었을 때, 광고의 개별 위치",
			"default": null
		},
		{
			"name": "publisherUserId",
			"type": [
				"null",
				"string"
			],
			"doc": "퍼블리셔 사용자 식별자",
			"default": null
		},
		{
			"name": "abuseFilter",
			"type": [
				"null",
				"string"
			],
			"doc": "어뷰징 필터 - ['C'(CQ) | 'D'(Dedup)]",
			"default": null
		},
		{
			"name": "stat",
			"type": [
				"null",
				"int"
			],
			"doc": "응답 결과 - [0(에러) | 1(성공) | 2(광고없음) | 3(타임아웃)]",
			"default": null
		},
		{
			"name": "rank",
			"type": [
				"null",
				"int"
			],
			"doc": "우선순위(=tier)",
			"default": null
		},
		{
			"name": "rate",
			"type": [
				"null",
				"float"
			],
			"doc": "할당비율",
			"default": null
		},
		{
			"name": "timeout",
			"type": [
				"null",
				"int"
			],
			"doc": "연결제한시간(ms)",
			"default": null
		},
		{
			"name": "responseTime",
			"type": [
				"null",
				"int"
			],
			"doc": "응답소요시간(ms)",
			"default": null
		},
		{
			"name": "connectionType",
			"type": [
				"null",
				"string"
			],
			"doc": "연결 유형 - [C2S(클라이언트 SDK 연결) | S2S(서버 연결)]",
			"default": null
		},
		{
			"name": "servingType",
			"type": [
				"null",
				"string"
			],
			"doc": "처리 유형 - [DIRECT(단독입찰) | BID(비딩 입찰)]",
			"default": null
		},
		{
			"name": "priority",
			"type": [
				"null",
				"long"
			],
			"doc": "DSP 노출 우선순위",
			"default": null
		},
		{
			"name": "errorCode",
			"type": [
				"null",
				"string"
			],
			"doc": "공통 에러 코드",
			"default": null
		},
		{
			"name": "errorSubCode",
			"type": [
				"null",
				"string"
			],
			"doc": "세부 에러 코드",
			"default": null
		},
		{
			"name": "errorMessage",
			"type": [
				"null",
				"string"
			],
			"doc": "에러 메세지",
			"default": null
		},
		{
			"name": "errorObject",
			"type": [
				"null",
				"string"
			],
			"doc": "에러관련 상세정보 오브젝트 문자열",
			"default": null
		},
		{
			"name": "dealId",
			"type": [
				"null",
				"string"
			],
			"doc": "Deal Id",
			"default": null
		},
		{
			"name": "biddingGroupId",
			"type": [
				"null",
				"string"
			],
			"doc": "Bidding Group Id",
			"default": null
		},
		{
			"name": "hostname",
			"type": [
				"null",
				"string"
			],
			"doc": "server host name",
			"default": null
		},
		{
			"name": "requestCreativeTypes",
			"type": [
				"null",
				{
					"type": "array",
					"items": "string"
				}
			],
			"doc": "요청 상품유형 목록",
			"default": null
		},
		{
			"name": "responseCreativeType",
			"type": [
				"null",
				"string"
			],
			"doc": "응답 상품유형",
			"default": null
		},
		{
			"name": "requestSizes",
			"type": [
				"null",
				{
					"type": "array",
					"items": "string"
				}
			],
			"doc": "요청 사이즈 목록",
			"default": null
		},
		{
			"name": "responseSize",
			"type": [
				"null",
				"string"
			],
			"doc": "응답 사이즈",
			"default": null
		},
		{
			"name": "bidFloor",
			"type": [
				"null",
				"float"
			],
			"doc": "최소입찰가",
			"default": null
		},
		{
			"name": "sdkVer",
			"type": [
				"null",
				"string"
			],
			"doc": "SDK Version",
			"default": null
		},
		{
			"name": "videoAdScheduleId",
			"type": [
				"null",
				"string"
			],
			"doc": "Video Ad Schedule Id",
			"default": null
		},
		{
			"name": "contentLength",
			"type": [
				"null",
				"int"
			],
			"doc": "컨텐츠 재생 시간 (초단위)",
			"default": null
		},
		{
			"name": "startDelay",
			"type": [
				"null",
				"int"
			],
			"doc": "광고 재생 시점(0: pre-roll, > 0: mid-roll, -2: post-roll)",
			"default": null
		}
	]
};

/**
 * downloadHdfsFile : 파일 다운로드하기
 *
 * @param webHdfsConfig
 * @param {String} remotePath hdfs 파일 경로
 * @param {String} localPath 로컬 파일 경로
 */
module.exports.downloadHdfsFile = (webHdfsConfig, remotePath, localPath) => {
	logger.debug(`[hdfs.util.js :: downloadHdfsFile] 호출됨 (remotePath= ${remotePath}, localPath= ${localPath})`);

	const hdfs = WebHDFS.createClient(webHdfsConfig);

	return new Promise((resolve, reject) => {
		const hdfsReadStream = hdfs.createReadStream(remotePath);
		const fsWriteStream = fs.createWriteStream(localPath);

		hdfsReadStream.on('error', err => {
			reject({ message: `[hdfs.util.js :: downloadHdfsFile] hdfsReadStream 에러`, err });
			closeStreams();
		});

		fsWriteStream.on('close', () => {
			logger.debug('[hdfs.util.js :: downloadHdfsFile] fsWriteStream end 완료');

			resolve(true);
		})
			.on('error', err => {
				reject({ message: '[hdfs.util.js :: downloadHdfsFile] fsWriteStream 에러', err });
				closeStreams();
			});

		hdfsReadStream.pipe(fsWriteStream);

		// 모든 stream 종료 처리
		const closeStreams = () => {
			if (hdfsReadStream && hdfsReadStream.close) hdfsReadStream.close();
			if (fsWriteStream) fsWriteStream.close();
		};
	});
}

/**
 * uploadLocalFile : 파일 업로드하기
 *
 * @param webHdfsConfig
 * @param {String} localPath 로컬 파일 경로
 * @param {String} remotePath hdfs 파일 경로
 */
module.exports.uploadLocalFile = (webHdfsConfig, localPath, remotePath) => {
	logger.debug(`[hdfs.util.js :: uploadLocalFile] 호출됨 (localPath= ${localPath}, remotePath= ${remotePath})`);

	const hdfs = WebHDFS.createClient(webHdfsConfig);

	return new Promise((resolve, reject) => {
		const localReadStream = fs.createReadStream(localPath);
		const hdfsWriteStream = hdfs.createWriteStream(remotePath);

		localReadStream.on('error', err => {
			reject({ message: `[hdfs.util.js :: uploadLocalFile] fsReadStream 에러`, err });
			closeStreams();
		})

		hdfsWriteStream.on('close', () => {
			logger.debug('[hdfs.util.js :: uploadLocalFile] hdfsWriteStream end 완료');

			resolve(true);
		})
			.on('create', function(chunk) {
				logger.info('write: ' + chunk);
			})
			.on('error', err => {
				reject({ message: '[hdfs.util.js :: uploadLocalFile] fsWriteStream 에러', err });
				closeStreams();
			});

		localReadStream.pipe(hdfsWriteStream);

		// 모든 stream 종료 처리
		const closeStreams = () => {
			if (localReadStream) localReadStream.close();
			if (hdfsWriteStream && hdfsWriteStream.close) hdfsWriteStream.close();
		};
	});
}
