'use strict';

import fetch from 'node-fetch';
import _ from 'lodash';
import nodemailer from 'nodemailer';

import config from '../config/config';
import * as logger from '../utils/logger.util';

exports.sendMail = ({
						host = 'backendsmtp.naver.com',
						port = 25,
						from = 'SSP_BATCH<<EMAIL>>',
						to = null,
						subject = '',
						html = '',
						attachments = [],
						isBatchNoti = true
					} = {}) => {
	logger.debug('[mail.util] sendMail() 호출됨');

	return new Promise((resolve, reject) => {
		html = html.replace(/\n/g, '<br/>');

		// local에서는 메일 전송 안됨. 대신 로그로..
		if (process.env.NODE_ENV === 'local') {
			to = '<EMAIL>';
			// logger.debug('[mail.util] local인 경우, 메일 전송 안함.\n' +
			// 	'to:' + to + '\n' +
			// 	'subject:' + subject + '\n' +
			// 	'html:\n' + html + '\n' +
			// 	'attachments: '+ attachments.length + ' 개');
			// return resolve();
		}

		// to 정보가 없는 경우
		if (to === null || to === '') {
			logger.error('[mail.util] 받는 사람 메일 정보 없음');
			return resolve();
		}

		subject = isBatchNoti ? `[SSP-BATCH][${_.upperCase(process.env.NODE_ENV)}] ${subject}` : subject;

		let transporter = nodemailer.createTransport({ host, port });
		transporter.sendMail({ from, to, subject, html, attachments }, (err, info) => {
			if (err) {
				// {
				//		"errno": "ENOTFOUND",
				//		"code": "ECONNECTION",
				//		"syscall": "getaddrinfo",
				//		"hostname": "xxxx.naver.com",
				//		"host": "xxxx.naver.com",
				//		"port": 25,
				//		"command": "CONN"
				// }
				logger.error('[mail.util] sendMail 실패\n', err);
				resolve(err);
			} else {
				// {
				//   "accepted": [
				//     "<EMAIL>"
				//   ],
				//   "rejected": [],
				//   "envelopeTime": 35,
				//   "messageTime": 154,
				//   "messageSize": 3004,
				//   "response": "250 2.0.0 OK K67NlgBJScOLVh1FDOix9Q - nsmtp",
				//   "envelope": {
				//     "from": "<EMAIL>",
				//     "to": [
				//       "<EMAIL>"
				//     ]
				//   },
				//   "messageId": "<<EMAIL>>"
				// }
				logger.debug('[mail.util] sendMail 성공\n', info);
				resolve(info);
			}
		});
	});
};

/**
 * COS 플랫폼을 통한 이메일 전송
 *
 * @param {string} templateId - COS Mail Template Id
 * @param {string[]} emailList - E-Mail 수신자 목록
 * @param {{name:string, value:string}[]} nameValues - COS Mail Template PlaceHolder
 * @return {Promise<void>}
 */
exports.sendMailByCos = async ({templateId, emailList = [], nameValues = [], mailTitle, mailBody}) => {
	logger.debug('[mail.util] sendMail() 호출됨');

	if (_.isEmpty(emailList)) {
		throw new Error('[mail.util] e-mail 수신자 정보 없음');
	}

	const mailRequest = {
		serviceId: config.mail.service.gfp.id,
		templateId,
		individualDispatchYn: 'Y',
		recipients: []
	};

	// e-mail 수신자 정보 추가
	for (const email of emailList) {
		mailRequest.recipients.push({
			primaryMailAddress: email,
			recipientType: 'O'
		});
	}

	// COS Mail Template PlaceHolder 추가
	if (!_.isEmpty(nameValues)) {
		mailRequest['nameValues'] = nameValues;
	}

	if (!_.isEmpty(mailTitle)) {
		mailRequest['mailTitle'] = mailTitle;
	}

	if (!_.isEmpty(mailBody)) {
		mailRequest['mailBody'] = mailBody;
	}

	logger.debug('[mail.util] mail request body', mailRequest);

	// Request Parameter 생성
	const params = new URLSearchParams();
	params.append('mailRequest', JSON.stringify(mailRequest));

	// COS MAIL API 호출
	const response = await fetch(config.mail.api.url, {
		method: config.mail.api.method,
		timeout: config.mail.api.timeout,
		body: params
	});

	if (!response.ok) {
		// response.status < 200 || response.status >= 300
		throw new Error('[mail.util] response status : ' + response.status);
	} else {
		// response.status >= 200 && response.status < 300
		const data = await response.json();

		logger.debug('[mail.util] cos response data', data);

		if (!_.isEqual(data.state, 'SUCCESS')) {
			throw new Error('[mail.util] ' + data.errorMessage);
		}
	}
};
