import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

import _ from 'lodash';
import moment from 'moment-timezone';

import { CommonReportErrorCode } from '../error/error-code';


export function IsTimezone(validationOptions?: ValidationOptions): Function {
	return (object: Object, propertyName: string): void => {
		registerDecorator({
			name: 'IsTimezone',
			target: object.constructor,
			async: false,
			propertyName,
			constraints: [],
			options: validationOptions,
			validator: {
				validate(value: any): boolean {
					if (_.isEmpty(value)) return true;

					return !!moment.tz.zone(value) || value === '-';
				},
				defaultMessage(args: ValidationArguments): string {
					return CommonReportErrorCode.INVALID_VALUE.message;
				},
			},
		});
	};
}

