import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';
import { CommonReportErrorCode } from '../error/error-code';

// Object[] 는 적용 대상이 아님. 필요하면 추가 구현하도록 한다.
// values 가 array 가 아닌 경우, 스킵 처리 한다.
export function ArrayDistinct(validationOptions?: ValidationOptions): Function {
	return (object: Object, propertyName: string): void => {
		registerDecorator({
			name: 'ArrayDistinct',
			target: object.constructor,
			async: false,
			propertyName,
			constraints: [],
			options: validationOptions,
			validator: {
				validate(values: any): boolean {
					// Array 가 아닌 경우, Validation 체크 하지 않음
					if (Array.isArray(values) === false) {
						return true;
					}

					const distinct = [...new Set(values)];
					return distinct.length === values.length;
				},
				defaultMessage(args: ValidationArguments): string {
					return CommonReportErrorCode.DUPLICATED_VALUE.message;
				},
			},
		});
	};
}

