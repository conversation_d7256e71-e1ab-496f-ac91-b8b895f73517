'use strict';

import * as controller from '../controllers/monitoring.controller';

module.exports = router => {
	router.get('/batch/monitoring/reportapistats/ncc', controller.makeNccReportApiStats);

	// 모니터링 지르콘B 알림 메일 발송
	router.get('/batch/monitoring/zircon/b/send_mail', controller.sendMailMonitoringZirconB);

	// 광고공급자, 매체 수익률 모니터링
	router.get('/batch/monitoring/revenue', controller.monitorRevenue);
};
