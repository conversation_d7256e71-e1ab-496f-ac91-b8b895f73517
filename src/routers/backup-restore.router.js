'use strict';

import * as backupRestore from '../controllers/backup-restore.controller';
import * as logger from "../utils/logger.util";

module.exports = router => {
	logger.info('[backup-restore.router] 호출됨');

	// HDFS 의 파일을 Nubes 로 백업
	// 1. 아카이빙 하는 경우: HDFS -> LOCAL -> Nubes
	// 2. 아카이빙 하지 않는 경우: HDFS -> Nubes
	router.get('/batch/backup-restore/nubes/backup', backupRestore.backup);

	// Nubes 의 파일을 HDFS 로 복구
	// 1. 아카이빙 된 파일: Nubes -> LOCAL -> HDFS
	// 2. 아카이빙 되지 않은 파일: Nubes -> HDFS
	router.get('/batch/backup-restore/nubes/restore', backupRestore.restore);

	// Nubes 의 파일 삭제
	router.get('/batch/backup-restore/nubes/delete', backupRestore.delete);
};
