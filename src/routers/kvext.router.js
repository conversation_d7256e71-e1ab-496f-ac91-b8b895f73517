'use strict';

import * as controller from '../controllers/kvext.controller';

module.exports = router => {
	router.get('/batch/kvext/manager/list', controller.getManagers);
	router.get('/batch/kvext/job/list', controller.getOngoingJobs);

	// fullSync job 일괄 추가
	router.get('/batch/kvext/fullsync/job', controller.processFullSyncJob);

	// ExtendedKeyValue job 일괄 삭제
	router.delete('/batch/kvext/job', controller.deleteExtendedKeyValueJob);

	// fullSync LOCAL 파일 일괄 삭제
	router.get('/batch/kvext/fullsync/delete', controller.deleteFullSyncLocal);

	// 재처리 횟수를 넘은 Full Sync 건에 대해 알림
	router.get('/batch/kvext/fullsync/alarm', controller.alarmAbnormalFullSync);

	// Test fullsync job 실행
	router.get('/batch/kvext/fullsync', controller.processFullSyncJobTest);

	router.get('/test/sylph', controller.sylph);
};
