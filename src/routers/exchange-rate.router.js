'use strict';


import logger from '../utils/logger.util';

import * as controller from '../controllers/exchange-rate.controller';


module.exports = router => {
	logger.info('[exchange-rate.router] 호출됨');

	// 환율 일배치
	router.get('/batch/day/exchangeRate', controller.processExchangeRateApi);


	// [ext] 특정 기간에 대해 환율 처리하기
	router.get('/ext/exchangeRate', controller.processExchangeRateApiExt);

	// [ext] 특정일에 대해 평균 환율 처리하기
	router.get('/ext/exchangeRateAverage', controller.processExchangeRateAverageExt);


	// [ext] 환율 조회하기
	router.get('/ext/getExchangeRate', controller.getExchangeRateExt);

	// [ext] 평균 환율 조회하기
	router.get('/ext/getExchangeRateAverage', controller.getExchangeRateAverageExt);
};
