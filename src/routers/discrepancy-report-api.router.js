'use strict';


import * as controller from '../controllers/discrepancy-report-api.controller';
import * as logger from '../utils/logger.util';

module.exports = router => {
	logger.info('[discrepancy-report-api.router] 호출됨');

	// 대상 AP : TTD, NATIVO, RTBHOUSE, PANGLE, ADVIEW, BRIGHT_MOUNTAIN

	// Discrepacny Report 연동 스케쥴 생성
	router.get('/batch/discrepancy/report/schedule', controller.createDiscrepancyReportSchedules);

	// Discrepacny Report API 연동
	router.get('/batch/discrepancy/report/api/:reportApiType', controller.processDiscrepancyReportApi);
};
