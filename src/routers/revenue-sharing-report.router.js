'use strict';

import * as controller from '../controllers/revenue-sharing-report.controller';
import * as controller3 from '../controllers/revenue-sharing-report3.controller';

module.exports = router => {
	router.get('/batch/report/revenuesharing/csv', controller.runCsv);
	router.get('/batch/report/revenuesharing/csv/reRunByAp', controller.reRunByAp);
	router.get('/batch/report/revenuesharing/csv/reRunByGfp', controller.reRunByGfp);
	router.get('/batch/report/revenuesharing/makeGfpStatsSchedule', controller.makeGfpStatsSchedule);
	router.get('/batch/report/revenuesharing/monitor', controller.monitor);
	router.get('/batch/report/revenuesharing/delete', controller.deleteExpiredReportInLocalStorage);


	// 매체성과리포트 3차(수익쉐어 리포트 개선) - https://jira.navercorp.com/browse/GFP-392
	router.get('/batch/revenuesharing/prepareReports', controller3.prepareReports);
	router.get('/batch/revenuesharing/updateReportsState', controller3.updateReportsState);
	router.get('/batch/revenuesharing/makeReportTraces', controller3.makeReportTraces);
	router.get('/batch/revenuesharing/upload', controller3.upload);
	router.get('/batch/revenuesharing/monitor', controller3.monitor);
	// router.get('/batch/revenuesharing/delete', controller3.delete);
};
