import { Readable, Transform, Writable } from 'stream';
import { pipeline } from 'stream/promises';
import { posix as posixPath } from 'path';

import { Logger } from '@nestjs/common';

import axios, { Axios, AxiosRequestConfig, AxiosResponse } from 'axios';
import moment from 'moment';

import { ObjectDesc, ObjectListParams } from './types';
import { MAX_LIST_LENGTH } from './constants';

type UploadOptions = {
  overwrite?: boolean;
  maxRetries?: number;
  chunkSizeHint?: number;
};

type DeleteOptions = {
  isDir?: boolean;
  retentionSec?: number;
};

const NUBES_RETRY_STATUS_CODES = [503, 504];

const STREAM_BUF_SIZE = 256 * 1024;
const DEFAULT_CHUNK_SIZE_HINT = 8 * 1024 * 1024; // 8MB

export class ClientResult {
  public statusCode: number;
  private _statusText: string;
  private _errorCode: string | null;
  private _errorMessage: string | null;
  public size: number;

  constructor(res: AxiosResponse, size = 0) {
    this.statusCode = res.status;
    this._statusText = res.statusText;
    this._errorCode = res.headers['x-error-code'];
    this._errorMessage = res.headers['x-error-message'];
    this.size = size;
  }

  ok() {
    return this.statusCode >= 200 && this.statusCode < 300;
  }

  toString() {
    if (this.ok()) {
      if (this.size > 0) {
        return `${this.statusCode}: OK (size=${this.size})`;
      } else {
        return `${this.statusCode}: OK`;
      }
    }

    if (this._errorCode && this._errorMessage) {
      return `${this.statusCode}: ${this._errorCode}: ${this._errorMessage}`;
    }

    return `${this.statusCode}: ${this._statusText ?? 'HTTP ERROR'}`;
  }
}

class ListStream extends Readable {
  private logger = new Logger('nubes.client.listStream');

  private continuationToken: string | null;
  private queue: Array<ObjectDesc>;

  constructor(private nubesClient: Client, private path: string, options: object = {}) {
    super({ ...options, objectMode: true });

    this.continuationToken = null;
    this.queue = [];
  }

  async _fetch() {
    const params: ObjectListParams = {
      dir: this.path,
      'max-contents': MAX_LIST_LENGTH,
    };
    if (this.continuationToken) {
      params['continuation-token'] = this.continuationToken;
    }

    const url = await this.nubesClient._getBucketAddress();
    try {
      await axios.get<ObjectDesc[]>(url, { params }).then((res) => {
        this.continuationToken = res.headers['x-continuation-token'] ?? '';

        if (res.data.length > 0) {
          this.queue = res.data;
          this.push(this.queue.shift());
        } else {
          this.push(null);
        }
      });
    } catch (err) {
      this.destroy(err);
    }
  }

  _read() {
    if (this.continuationToken === null) {
      return this._fetch();
    }

    if (this.queue.length > 0) {
      return this.push(this.queue.shift());
    }

    if (this.continuationToken) {
      return this._fetch();
    }

    this.push(null);
  }
}

class DownloadStream extends Readable {
  private logger = new Logger('nubes.client.downloadStream');

  private activeResponse: AxiosResponse | null;
  private contentLength: number;
  private bytesRead: number;
  private tries: number;

  constructor(
    private nubesClient: Client,
    private path: string,
    private maxContinuation: number,
    options: object = {},
  ) {
    super(Object.assign({ highWaterMark: STREAM_BUF_SIZE }, options));

    this.activeResponse = null;
    this.contentLength = 0;
    this.bytesRead = 0;
    this.tries = 0;
  }

  async _openUpstream() {
    const url = `${await this.nubesClient._newGatewayPath(this.path)}`;

    axios
      .get(url, {
        responseType: 'stream',
        headers: { Range: `bytes=${this.bytesRead}-` },
      })
      .then((res) => {
        if (this.contentLength === 0) {
          this.contentLength = Number.parseInt(res.headers['content-length']);
        }

        res.data
          .on('error', (err) => {
            this.destroy(err);
          })
          .on('data', (chunk) => {
            this._push(chunk);
          })
          .on('end', () => {
            this.tries++;

            if (this.bytesRead < this.contentLength && this.tries <= this.maxContinuation) {
              this._openUpstream();
            } else if (this.bytesRead === this.contentLength) {
              this.push(null);
            } else {
              this.destroy(
                new Error(
                  `fail to download: ${this.path}: progress=${this.bytesRead}/${this.contentLength}: tries=${this.tries}/${this.maxContinuation}`,
                ),
              );
            }
          });

        this.activeResponse = res;
      })
      .catch((err) => {
        this.destroy(err);
      });
  }

  _push(chunk: Buffer) {
    this.bytesRead += chunk.length;

    if (!this.push(chunk)) {
      this.activeResponse.data.pause();
    }
  }

  _read() {
    if (this.activeResponse === null) {
      this._openUpstream();
    } else {
      this.activeResponse.data.resume();
    }
  }

  _destroy(err: Error, cb: (err?: Error) => void): void {
    if (this.activeResponse && this.activeResponse.data.readable) {
      this.activeResponse.data.destroy();
    }
    cb(err);
  }
}

type ChunkInfo = {
  seq: number;
  chunks: Array<Buffer>;
  chunkSize: number;
  totalSize: number;
};

class ChunkTransformer extends Transform {
  private readonly logger: Logger;
  private readonly chunkSizeHint: number;

  private seq: number;
  private chunks: Array<Buffer>;
  private chunkSize: number;
  totalSize: number;

  constructor(chunkSizeHint: number, options = {}) {
    const writableHighWaterMark = Math.min(STREAM_BUF_SIZE, chunkSizeHint);
    const defaultOptions = {
      writableHighWaterMark,
      readableObjectMode: true,
      readableHighWaterMark: 1,
    };

    super({ ...defaultOptions, ...options });

    this.logger = new Logger('nubes.client.chunkTransform');
    this.chunkSizeHint = chunkSizeHint;

    this.seq = 0;
    this.chunks = [];
    this.chunkSize = 0;
    this.totalSize = 0;
  }

  _flushChunk() {
    const chunkInfo: ChunkInfo = {
      seq: this.seq,
      chunks: this.chunks,
      chunkSize: this.chunkSize,
      totalSize: this.totalSize,
    };

    this.seq++;
    this.chunks = [];
    this.chunkSize = 0;

    // this.logger.verbose(
    //   JSON.stringify({
    //     ...chunkInfo,
    //     chunks: chunkInfo.chunks.map((v) => v.length),
    //   }),
    // );

    return chunkInfo;
  }

  _transform(chunk: Buffer, encoding, callback): void {
    // this.chunks.push(Buffer.from(chunk));
    this.chunks.push(chunk);
    this.chunkSize += chunk.length;
    this.totalSize += chunk.length;

    if (this.chunkSize >= this.chunkSizeHint) {
      this.push(this._flushChunk());
    }

    callback();
  }

  _flush(callback) {
    if (this.chunkSize > 0) {
      this.push(this._flushChunk());
    } else if (this.seq === 0) {
      // zero-length stream
      this.chunks.push(Buffer.from(''));
      this.push(this._flushChunk());
    }

    callback();
  }
}

class ChunkReadable extends Readable {
  private readonly logger: Logger;

  private chunks: Array<Buffer>;
  private chunkSize: number;

  constructor(chunks: Array<Buffer>, chunkSize: number, options = {}) {
    const defaultOptions = { highWaterMark: STREAM_BUF_SIZE };
    super({ ...defaultOptions, ...options });

    this.logger = new Logger('nubes.client.chunkReadable');
    this.chunks = Array.from(chunks);
    this.chunkSize = chunkSize;
  }

  _read() {
    while (this.chunks.length > 0) {
      // this.logger.verbose(`chunk: sz=${this.chunks[0].length}`);

      if (!this.push(this.chunks.shift())) {
        break;
      }
    }

    if (this.chunks.length === 0) {
      // this.logger.verbose(`chunk: end: ${this.chunkSize}`);

      return this.push(null);
    }
  }
}

export class Client {
  private readonly logger: Logger;
  private readonly axios: Axios;

  constructor(private gwAddress: string, private bucket: string, name: string) {
    this.logger = new Logger(`nubes.client.${name}`);
    this.axios = axios.create();
    this.axios.interceptors.request.use((config: AxiosRequestConfig) => {
      if (process.env?.NUBES_AXIOS_DEBUG) {
        this.logger.verbose(JSON.stringify({ ...config, data: typeof config.data }));
      }

      return config;
    });
  }

  async stat(path: string): Promise<ObjectDesc> {
    const url = await this._newGatewayPath(path);

    const res = await this.axios.head(url);
    const mtime = moment.utc(
      res.headers['last-modified'],
      'ddd, DD MMM YYYY HH:mm:ss', // RFC 1123 format
    );

    return {
      Name: posixPath.basename(path),
      IsEncodedName: false,
      IsDir: res.headers['x-object-type'] === 'directory',
      XETag: res.headers['x-etag'],
      ETag: res.headers['etag'],
      ModTime: mtime.isValid() ? mtime.local().format() : undefined,
      Size: Number.parseInt(res.headers['x-object-size'] ?? '0'),
    };
  }

  async delete(path: string, userOptions: DeleteOptions = {}) {
    const defaultOptions: DeleteOptions = { isDir: false };
    const options = { ...defaultOptions, ...userOptions };

    if (path === '' || path === '/') {
      throw Error('cannot delete root dir');
    }

    const url = await this._newGatewayPath(path);
    const params = { is_dir: options.isDir };
    if (options.retentionSec) {
      params['retention-time-sec'] = options.retentionSec;
    }

    await this.axios.delete(url, { params });

    this.logger.log(`deleted: ${path}`);
  }

  list(path: string): Readable {
    return new ListStream(this, this._normalizePath(path));
  }

  download(path: string, maxContinuation = 3): Readable {
    return new DownloadStream(this, path, maxContinuation);
  }

  async upload(src: Readable, dest: string, userOptions: UploadOptions = {}): Promise<void> {
    const defaultOptions: UploadOptions = {
      overwrite: false,
      maxRetries: 3,
      chunkSizeHint: DEFAULT_CHUNK_SIZE_HINT,
    };

    const options = { ...defaultOptions, ...userOptions };
    const transformer = new ChunkTransformer(options.chunkSizeHint);

    let retries = 0;

    const uploadTry = async (chunkInfo: ChunkInfo) => {
      const config: AxiosRequestConfig =
        chunkInfo.seq === 0
          ? {
              method: 'post',
              headers: {
                'Content-Length': chunkInfo.chunkSize.toString(),
                'Content-Type': 'application/octet-stream',
              },
              params: {
                overwrite: options.overwrite,
              },
            }
          : {
              method: 'put',
              headers: {
                'Content-Length': chunkInfo.chunkSize.toString(),
                'Content-Type': 'application/octet-stream',
                Range: `bytes=${chunkInfo.totalSize - chunkInfo.chunkSize}-${chunkInfo.totalSize - 1}`,
              },
            };

      retries += await this._requestWithRetries(
        dest,
        config,
        () => new ChunkReadable(chunkInfo.chunks, chunkInfo.chunkSize),
        options.maxRetries - retries,
      );
    };

    const uploadCompleted = () => {
      this.logger.log(`uploaded: ${dest} (sz=${transformer.totalSize})`);
    };

    const uploadAborted = (err) => {
      if (err.response) {
        const result = new ClientResult(err.response);
        this.logger.warn(`upload aborted: ${dest}: ${result}`);
      } else {
        this.logger.warn(`upload aborted: ${dest}`);
      }
    };

    await pipeline(
      src,
      transformer,
      new Writable({
        objectMode: true,
        highWaterMark: 1,
        write(chunk, enc, cb) {
          uploadTry(chunk).then(
            () => cb(),
            (err) => cb(err),
          );
        },
      }),
    ).then(
      () => uploadCompleted(),
      (err) => {
        uploadAborted(err);
        throw err;
      },
    );
  }

  async _requestWithRetries(dest: string, config: AxiosRequestConfig, dataSource: () => Readable, maxRetries: number) {
    let retries = 0;
    let retry = false;

    do {
      const data = dataSource();
      const options = Object.assign({}, config, {
        url: await this._newGatewayPath(dest),
        data,
      });

      // this.logger.verbose(`upload option: ${JSON.stringify({ ...options, data: 'stream' })}`);

      retry = false;

      try {
        await this.axios.request(options);
      } catch (err) {
        if (err.response) {
          this.logger.warn(new ClientResult(err.response));
        }

        if (err.response && NUBES_RETRY_STATUS_CODES.includes(err.response.status) && retries < maxRetries) {
          retries++;
          retry = true;
        } else {
          throw err;
        }
      }
    } while (retry);

    return retries;
  }

  _normalizePath(path: string) {
    return path.startsWith('/') ? path : '/' + path;
  }

  async _newGatewayPath(path: string) {
    return `${await this._getBucketAddress()}${this._normalizePath(path)}`;
  }

  async _getBucketAddress() {
    // eslint-disable-next-line prettier/prettier
    const bucketAddr = `http://${this.gwAddress}/v1/${this.bucket}`;
    this.logger.verbose(`bucket address: ${bucketAddr}`);

    return bucketAddr;
  }
}
