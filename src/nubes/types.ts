export type ObjectDesc = {
  ETag: string;
  IsDir: boolean;
  IsEncodedName: boolean;
  Name: string;
  Size: number;
  ModTime: string; // ISO 8601 format
  XETag: string;
};

export type ObjectListParams = {
  dir: string;
  'max-contents'?: number;
  'continuation-token'?: string;
  'name-only'?: boolean;
  'sort-by-name'?: 'asc' | 'desc';
};

export type ObjectListHeaders = 'x-content-counter' | 'x-continuation-token';

export type BucketStatus = 'Created' | 'DeleteInProgress' | 'CreateInProgress';
export type ObjectType = 'directory' | 'object';

export type ObjectPostParams = {
  overwrite?: boolean;
  'storage-group'?: string;
};
