import { Controller, Inject, Post, Req } from '@nestjs/common';
import { Request } from 'express';
import { createReadStream, createWriteStream } from 'fs';
import { pipeline } from 'stream/promises';
import { Client } from './clients';
import { NUBES_CLIENT_NAM_API } from './constants';

@Controller('nubes')
export class NubesController {
  constructor(@Inject(NUBES_CLIENT_NAM_API) private client: Client) {}

  @Post('stat')
  async stat(@Req() req: Request) {
    return await this.client.stat(req.body.path);
  }

  @Post('list')
  async list(@Req() req: Request) {
    const entries = [];

    for await (const entry of this.client.list(req.body.path)) {
      entries.push(entry);
    }

    return entries;
  }

  @Post('delete')
  async delete(@Req() req: Request) {
    const path = req.body.path;

    await this.client.delete(req.body.path, { isDir: false });

    return { status: 'OK', path };
  }

  @Post('download')
  async download(@Req() req: Request) {
    const path = req.body.path;
    const to = req.body.to;

    await pipeline(this.client.download(path), createWriteStream(to));

    return { status: 'OK', path, to };
  }

  @Post('upload')
  async upload(@Req() req: Request) {
    const path = req.body.path;
    const from = req.body.from;

    await this.client.upload(createReadStream(from), path);

    return { status: 'OK', path, from };
  }
}
