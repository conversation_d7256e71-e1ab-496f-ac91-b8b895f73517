
'use strict';

// Set the 'common' environment configuration object
module.exports = {
	// Local timezone name
	timezone: 'Asia/Seoul',

	// local download root
	local_root: 'local_download',

	// owfs root
	owfs_root: 'download',

	// AIDA CUVE LRGM
	cuve_lrgm_root: 'korea_real/korea/naver_gfp',

	// 스키마 파일 경로
	schema_root: '/src/models',
	schema_root_for_data: '/src/models/data',

	// kvext fullsync 청크 사이즈 & DB bulk 사이즈
	kvext_fullsync_chunk_size: 500000,
	kvext_fullsync_bulk_size: 20000,

	// DB 일괄 삭제 청크 사이즈
	delete_chunk_size: 50000,

	// 리포트 api 청크 사이즈
	report_api_chunk_size: 500000,

	// 리포트 api DB bulk 사이즈
	report_api_bulk_size: 20000,

	// 리포트 API 환율 변환 정보
	exchange_rate_info: [
		{ from: 'USD', to: 'KRW', },
		{ from: 'KRW', to: 'USD', },
	],

	// 리포트 API 결과 유형
	api_result_type: {
		local_file: 'LOCAL_FILE',
		hdfs: 'HDFS',
		nubes: 'NUBES',
	},

	// 배치서버 호스트 Environments 정보
	batch_host: 'batch-host',

	// 리포트 API 파일 다운로드 루트 경로
	report_api_path: 'reportapi',
	abuse_report_api_path: 'abusereportapi',

	// 리포트 API 연동 배치 결과 알림 메일 수신자 Environments 정보
	report_api_result_receiver_env_name: 'report-api-result-receiver',

	// 리포트 API 연동 배치 실패 알림 메일 수신자 Environments 정보
	report_api_failure_receiver_env_name: 'report-api-failure-receiver',

	// 리포트 API 연동 배치 재실행 시간 간격 Environments 정보
	report_api_retry_interval_env_name: 'report-api-retry-interval',

	// AdProvider 리포트 API 연동 정보
	report_api: {
		// [In Naver] GFD 연동 정보
		gfd: {
			api_result: 'NUBES',

			file_info: {
				file_path: '/revenue_share',
				file_name: 'gfd_rs.{{YYYY}}{{MM}}{{DD}}.csv',
				separator: ',',
			},

			ext: {
				// nubes 접속 정보
				nubes: {
					bucket: 'gfd-share-storage',
				},

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'created_at',
					country: 'country',
					os: 'os',
					imp: 'imp',
					clk: 'click',
					rk: 'rs_key',
					netRevenue: 'revenue_usd'
				}
			}
		},

		// [In Naver] NCC 연동 정보
		ncc: {
			api_result: 'HDFS',

			file_info: {
				file_path: '/user/addata/revenue_share',
				file_name: 'ncc_{{YYYY}}{{MM}}{{DD}}.tsv',
				separator: '\t',
			},

			ext: {
				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'ymd',
					rk: 'rk',
					imp: 'exp_cnt',
					clk: 'clk_cnt',
					netRevenue: 'net_sales_amt',
					revenue: 'sales_amt'
				}
			}
		},

		// [In Naver] NDP 연동 정보
		ndp: {
			api_result: 'HDFS',

			file_info: {
				file_path: '/user/ndp-dev/sales/revenue_share/{{YYYY}}{{MM}}{{DD}}',
				file_name: 'ndp_{{YYYY}}{{MM}}{{DD}}.csv',
				separator: ',',
			},

			ext: {
				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'job_dt',
					rk: 'report_key',
					imp: 'imp',
					clk: 'clk',
					netRevenue: 'net_revenue',
					revenue: 'revenue',
					serviceAmount: 'service_amount'
				}
			}
		},

		// [In Naver] GFA 연동 정보
		gfa: {
			api_result: 'HDFS',

			file_info: {
				file_path: '/user/naver-pa-dmp/gfp_report/revenue/{{YYYY}}/{{MM}}/{{DD}}',
				file_name: 'gfa_{{YYYY}}{{MM}}{{DD}}.tsv',
				separator: '\t',
			},

			ext: {
				// _SUCCESS 파일 체크 유무
				checkSuccessFile: true,

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'paymentDate',
					rk: 'reportKey',
					imp: 'impCount',
					clk: 'clickCount',
					netRevenue: 'rev1',
					revenue: 'rev2'
				}
			}
		},

		// [Outside] GOOGLE 연동 정보 (GOOGLE_GMA, GOOGLE_GPT, GOOGLE_IMA)
		google: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/google',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				// 리포트 쿼리 공통
				dimensions: ['DATE', 'AD_UNIT_ID', 'COUNTRY_NAME'],
				columns: ['TOTAL_LINE_ITEM_LEVEL_IMPRESSIONS', 'TOTAL_LINE_ITEM_LEVEL_CLICKS'],
				dimensionAttributes: ['AD_UNIT_CODE'],

				filters: {
					adUnitIds: 'AD_UNIT_ID in ( {{adUnitIds}} )',
					keyValueIds: 'CUSTOM_TARGETING_VALUE_ID in ( {{keyValueIds}} )',
				},

				// 기본 리포트 쿼리 정보
				defaultQueryInfo: {
					dimensions: ['MOBILE_DEVICE_NAME', 'CREATIVE_SIZE_DELIVERED'],
					columns: ['TOTAL_LINE_ITEM_LEVEL_ALL_REVENUE'],

					usableFields: {
						os: true,
						size: true,

						net_revenue: 'Column.TOTAL_LINE_ITEM_LEVEL_ALL_REVENUE'
					}
				},

				// 키값 리포트 쿼리 정보
				keyValueQueryInfo: {
					dimensions: ['CUSTOM_CRITERIA'],
					columns: ['TOTAL_LINE_ITEM_LEVEL_CPM_AND_CPC_REVENUE'],

					usableFields: {
						rsKeyValue: true,

						net_revenue: 'Column.TOTAL_LINE_ITEM_LEVEL_CPM_AND_CPC_REVENUE'
					}
				},

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'Dimension.DATE',

					place_keys: {
						AD_UNIT_CODE: 'DimensionAttribute.AD_UNIT_CODE',
					},

					country: 'Dimension.COUNTRY_NAME',

					os: 'Dimension.MOBILE_DEVICE_NAME',
					size: 'Dimension.CREATIVE_SIZE_DELIVERED',

					rsKeyValue: 'Dimension.CUSTOM_CRITERIA',

					imp: 'Column.TOTAL_LINE_ITEM_LEVEL_IMPRESSIONS',
					clk: 'Column.TOTAL_LINE_ITEM_LEVEL_CLICKS',

					net_revenue: 'NET_REVENUE', // Column.TOTAL_LINE_ITEM_LEVEL_ALL_REVENUE or Column.TOTAL_LINE_ITEM_LEVEL_CPM_AND_CPC_REVENUE
				},

				scopes: ['https://www.googleapis.com/auth/dfp'],

				env_name: 'report-api-google'
			}
		},

		// [Outside] FAN 연동 정보
		fan: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/fan',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				// 리포트 쿼리 조회용
				aggregation_period: 'day',
				breakdowns: ['placement', 'country', 'platform'],
				metrics: ['fb_ad_network_imp', 'fb_ad_network_click', 'fb_ad_network_revenue'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'time',

					place_keys: {
						PLACEMENT_ID: 'placement',
					},

					country: 'country',
					os: 'platform',

					imp: 'fb_ad_network_imp',
					clk: 'fb_ad_network_click',
					net_revenue: 'fb_ad_network_revenue',
				},

				base_url: 'https://graph.facebook.com',
				api_version: 'v13.0',
				request_api: 'adnetworkanalytics'
			}
		},

		// [Outside] INMOBI 연동 정보
		inmobi: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/inmobi',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				// 리포트 쿼리 조회용
				groupBy: ['date', 'placement', 'country', 'platform'],
				metrics: ['adImpressions', 'clicks', 'earnings'],
				orderBy: ['earnings'],
				orderType: 'desc',

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'date',

					place_keys: {
						PLACEMENT_ID: 'placementId',
					},

					country: 'country',
					os: 'platform',

					imp: 'adImpressions',
					clk: 'clicks',
					net_revenue: 'earnings',
				},

				// Environments 환경 정보 이름
				token_env_name: 'report-api-inmobi-token',

				session_request_api: 'https://api.inmobi.com/v1.0/generatesession/generate',
				report_request_api: 'https://api.inmobi.com/v3.0/reporting/publisher'
			}
		},

		// [Outside] APP_NEXUS 연동 정보
		app_nexus: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/app_nexus',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				columns: ['day', 'placement_id', 'geo_country', 'size', 'imps_resold', 'clicks', 'reseller_revenue', 'deal_code'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'day',

					place_keys: {
						PLACEMENT_ID: 'placement_id',
					},

					dealId: 'deal_code',

					country: 'geo_country',
					size: 'size',

					imp: 'imps_resold',
					clk: 'clicks',
					net_revenue: 'reseller_revenue',
				},

				// Environments 환경 정보 이름
				token_env_name: 'report-api-app-nexus-token',

				// 리포트 쿼리 조회용
				report_type: 'network_analytics',

				// 유효 토큰 확인용 URL
				check_valid_token_api: 'https://api.appnexus.com/report-status',

				report_jwt_auth_api: 'https://api.appnexus.com/v2/auth/jwt',
				report_request_api: 'https://api.appnexus.com/report',
				report_download_api: 'https://api.appnexus.com/report-download'
			}
		},

		// [Outside] PUBMATIC 연동 정보
		pubmatic: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/pubmatic',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				// 일반 리포트
				place_key: 'adTagId',
				dimensions: ['date', 'adTagId', 'countryId'],

				// DEAL 연동시 체크!!!!!!!!!!!!!!!!!!!!!!!!!
				// DEAL 리포트
				// publisherDealId는 dealMetaId 요청 시, 같이 가져오는 값이다.
				// deal_place_key: 'siteId',
				// deal_dimensions: ['date', 'siteId', 'dealMetaId'],

				metrics: ['paidImpressions', 'clicks', 'netRevenue'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'date',

					// 일반 리포트는 adTagId 가 placeKey 이고, DEAL 리포트는 siteId 가 placeKey 이다.
					// 따라서 데이터 가공 시, 필드 이름을 adTagId 로 변경하여 관리한다.
					place_keys: {
						adTagId: 'adTagId',
					},

					dealId: 'publisherDealId',

					country: 'countryId',

					imp: 'paidImpressions',
					clk: 'clicks',
					net_revenue: 'netRevenue',
				},

				// Environments 환경 정보 이름
				token_env_name: 'report-api-pubmatic-token',

				token_request_api: 'http://api.pubmatic.com/v1/developer-integrations/developer/refreshToken',
				report_request_api: 'http://api.pubmatic.com/v1/analytics/data/publisher',
			}
		},

		// [Outside] RUBICON 연동 정보
		rubicon: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/rubicon',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				dimensions: ['date', 'zone_id', 'country', 'device_os_name', 'size'],
				metrics: ['paid_impression', 'seller_net_revenue'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'date',

					place_keys: {
						zoneId: 'zone_id',
					},

					country: 'country',
					os: 'device_os_name',
					size: 'size',

					imp: 'paid_impression',
					net_revenue: 'seller_net_revenue',
				},

				report_request_api: 'https://api.rubiconproject.com/analytics/v2/default',
				report_status_api: 'https://api.rubiconproject.com/analytics/v2/default/{{REPORT_ID}}',
				report_download_api: 'https://api.rubiconproject.com/analytics/v2/default/{{REPORT_ID}}/data',
			}
		},

		// [Outside] OPENX 연동 정보
		openx: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/openx',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				attributes: ['day', 'publisherAdUnitId', 'userGeoCountry', 'userOperatingSystem'],
				metrics: ['marketImpressions', 'clicks', 'marketPublisherRevenue'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'day',

					place_keys: {
						adUnitId: 'publisherAdUnitId',
					},

					country: 'userGeoCountry',
					os: 'userOperatingSystem',

					imp: 'marketImpressions',
					clk: 'clicks',
					net_revenue: 'marketPublisherRevenueInPCoin'
				},

				// Environments 환경 정보 이름
				token_env_name: 'report-api-openx-token',

				unauth_token_request_api: 'https://sso.openx.com/api/index/initiate', // RequestToken
				auth_request_api: 'https://sso.openx.com/login/process',
				token_request_api: 'https://sso.openx.com/api/index/token', // AccessToken
				date_range_request_api: 'https://{{API_DOMAIN}}/data/1.0/date-range',
				report_request_api: 'https://{{API_DOMAIN}}/data/1.0/report',
			}
		},

		// [Outside] ADVIEW 연동 정보
		adview: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/adview',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'date',

					place_keys: {
						placementId: 'placementId',
					},

					country: 'country',

					imp: 'display',
					clk: 'click',
					net_revenue: 'income',
				},

				// appId 와 매핑 되는 placementId 정보를 placeInfos 에서 찾아서 추가하는 용도
				extendedFields: {
					appId: 'placementId'
				},

				report_request_api: 'http://us.adview.com/ssp/bid/adreport',
			}
		},

		// [Outside] CRITEO 연동 정보
		criteo: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/criteo',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				dimensions: ['Subid', 'usergeo'],
				metrics: ['CriteoDisplays', 'Clicks', 'Revenue'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'TimeId',

					place_keys: {
						subid: 'Subid'
					},

					country: 'usergeo',

					imp: 'CriteoDisplays',
					clk: 'Clicks',
					net_revenue: 'Revenue',
				},

				report_request_api: 'https://pmc.criteo.com/api/stats',
			}
		},

		// [Outside] SMAATO 연동 정보
		smaato: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/smaato',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				dimensions: ['Date', 'AdspaceId', 'CountryCode'],
				kpis: ['impressions', 'clicks', 'netRevenue'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'Date',

					place_keys: {
						adspaceid: 'AdspaceId'
					},

					country: 'CountryCode',

					imp: 'impressions',
					clk: 'clicks',
					net_revenue: 'netRevenue',
				},

				token_request_api: 'https://auth.smaato.com/v2/auth/token/',
				report_request_api: 'https://api.smaato.com/v1/reporting/',
			}
		},

		// [Outside] UNITYADS 연동 정보
		unityads: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/unityads',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				groupBy: ['placement', 'country', 'platform', 'game'],
				metrics: ['start_count', 'revenue_sum'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'timestamp',

					place_keys: {
						GAME_ID: 'source_game_id',
						PLACEMENT_ID: 'placement'
					},

					country: 'country',
					os: 'platform',

					imp: 'start_count',
					net_revenue: 'revenue_sum',
				},

				report_request_api: 'https://monetization.api.unity.com/stats/v1/operate/organizations/{{ORGANIZATION_ID}}',
			}
		},

		// [Outside] DISPLAYIO 연동 정보
		displayio: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/displayio',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				dimensions: ['day', 'inventoryId', 'placementId', 'platform', 'countryCode'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'day',

					place_keys: {
						appId: 'inventoryId',
						placementId: 'placementId',
					},

					country: 'countryCode',
					os: 'platform',

					imp: 'impressions',
					clk: 'clicks',
					net_revenue: 'revenue',
				},

				report_request_api: 'https://api.brand.display.io/api/developerAPI',
			}
		},

		// [Outside] TAPPX 연동 정보
		tappx: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/tappx',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				position: {
					Date: 0, TappxKey: 2, AdType: 4, Country: 5, OperatingSystemName: 3,
					Impressions: 8, Clicks: 9, Revenues: 10
				},

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'Date',

					place_keys: {
						appKey: 'TappxKey',
						sizeType: 'AdType',
					},

					country: 'Country',
					os: 'OperatingSystemName',

					imp: 'Impressions',
					clk: 'Clicks',
					net_revenue: 'Revenues',
				},

				report_request_api: 'http://reporting.api.tappx.com/ssp/v3',
			},
		},

		// [Outside] APPIER 연동 정보
		appier: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/appier',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'date',

					place_keys: {
						AdUnitID: 'tag_id',
					},

					imp: 'impressions',
					net_revenue: 'cost',
				},

				partner_ids: ['naver_kr', 'naver_sg', 'naver_use', 'naver_usw'],
				report_request_api: 'https://yggdrasil.appier.net/{{PARTNER_ID}}/0/tag_id/{{YYYY-MM-DD}}.json',
			},
		},

		// [Outside] VUNGLE 연동 정보
		vungle: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/vungle',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				dimensions: ['date', 'application', 'placement', 'country', 'platform', 'adSize'],
				aggregates: ['impressions', 'clicks', 'revenue'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'date',

					place_keys: {
						appId: 'application id',
						placementId: 'placement reference id',
					},

					country: 'country',
					os: 'platform',
					size: 'adSize',

					imp: 'impressions',
					clk: 'clicks',
					net_revenue: 'revenue',
				},

				report_request_api: 'https://report.api.vungle.com/ext/pub/reports/performance',
			},
		},

		// [Outside] DIGITAL_TURBINE 연동 정보
		digital_turbine: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/digital_turbine',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				splits: ['Date', 'Fyber App ID', 'Placement ID', 'Country', 'Device OS'],
				metrics: ['Impressions', 'Clicks', 'Revenue (USD)'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'Date',

					place_keys: {
						appId: 'Fyber App ID',
						placementId: 'Placement ID',
					},

					country: 'Country',
					os: 'Device OS',

					imp: 'Impressions',
					clk: 'Clicks',
					net_revenue: 'Revenue (USD)',
				},

				token_request_api: 'https://reporting.fyber.com/auth/v1/token',
				report_request_api: 'https://reporting.fyber.com/api/v1/report',
			},
		},

		// [Outside] IRONSOURCE 연동 정보
		ironsource: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/ironsource',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				breakdowns: ['date', 'app', 'instance', 'country', 'platform'],
				metrics: ['impressions', 'clicks', 'revenue'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'date',

					place_keys: {
						appKey: 'appKey',
						instanceId: 'instanceId',
					},

					country: 'countryCode',
					os: 'platform',

					imp: 'impressions',
					clk: 'clicks',
					net_revenue: 'revenue',
				},

				token_request_api: 'https://platform.ironsrc.com/partners/publisher/auth',
				report_request_api: 'https://platform.ironsrc.com/partners/publisher/mediation/applications/v6/stats',
			},
		},

		// [Outside] APPLOVIN_ZONE 연동 정보
		applovin_zone: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/applovin_zone',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				columns: ['day', 'zone_id', 'country', 'platform', 'impressions', 'clicks', 'revenue'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'day',

					place_keys: {
						zoneId: 'zone_id',
					},

					country: 'country',
					os: 'platform',

					imp: 'impressions',
					clk: 'clicks',
					net_revenue: 'revenue',
				},

				report_request_api: 'https://r.applovin.com/report',
			},
		},

		// [Outside] TEADS 연동 정보
		teads: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/teads',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				dimensions: ['day', 'page', 'country_code', 'operating_system'],
				metrics: ['publisher_sold_impression', 'teads_billing_converted_krw'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'day',

					place_keys: {
						tagId: 'page',
					},

					country: 'country_code',
					os: 'operating_system',

					imp: 'publisher_sold_impression',
					net_revenue: 'teads_billing_converted_krw',
				},

				report_request_api: 'https://api.teads.tv/v1/analytics/custom',
				report_status_api: 'https://api.teads.tv/v1/analytics/custom/{{REPORT_ID}}',
			},
		},

		// [Outside] DISPLAYIO_ED 연동 정보
		displayio_ed: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/displayio_ed',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				dimensions: ['date', 'zone'],
				columns: ['date', 'zone_id', 'rtb_pub_gross_impressions', 'rtb_pub_gross'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'Date',

					place_keys: {
						zone: 'Zone Id',
					},

					imp: 'Publisher Gross Impressions',
					net_revenue: 'Gross Estimated Revenue',
				},

				report_request_api: 'https://login.displayio.cloud/publisher/svc',
			}
		},

		// [Outside] ADPOPCORN 연동 정보
		adpopcorn: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/adpopcorn',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				// AP 수수료 20% 제외 처리
				apFeeRate: 0.2,

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'report_date',

					place_keys: {
						app_key: 'media_key',
						placement_id: 'placement_id',
					},

					country: 'country',

					imp: 'impression_value',
					clk: 'click_value',
					net_revenue: 'media_cost',
				},

				report_request_api: 'https://sspi-ext-report.adpopcorn.com/v1/analytics/data',
			}
		},

		// [Outside] NATIVO 연동 정보
		nativo: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/nativo',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				breakdown: ['placement', 'country'],
				metrics: ['standard_impressions', 'merged_clicks', 'merged_revenue'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'date',

					place_keys: {
						placement_id: 'placement',
					},

					country: 'country',

					imp: 'cpm impression',
					clk: 'clicks',
					net_revenue: 'revenue',
				},

				report_request_api: 'https://api.nativo.com/v3/data/demand',
			}
		},

		// [Outside] BIDMACHINE 연동 정보
		bidmachine: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/bidmachine',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				columns: ['date', 'imp_tagid', 'country', 'platform', 'impressions', 'clicks', 'revenue'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'date',

					place_keys: {
						placementId: 'imp_tagid',
					},

					country: 'country',
					os: 'platform',

					imp: 'impressions',
					clk: 'clicks',
					net_revenue: 'revenue',
				},

				report_request_api: 'http://api-eu.bidmachine.io/api/v1/report/ssp',
			}
		},

		// [Outside] VERVE 연동 정보
		verve: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/verve',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				group_by: ['date', 'store_app_id', 'country_code'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'date',

					place_keys: {
						app_id: 'store_app_id',
					},

					country: 'country_code',

					imp: 'impressions',
					clk: 'clicks',
					net_revenue: 'revenues',
				},

				report_request_api: 'https://dashboard.pubnative.net/api/reports',
			}
		},

		// [Outside] ALGORIX_S2S 연동 정보
		algorix_s2s: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/algorix_s2s',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				dimensions: ['date', 'app_bundle_id', 'country'],
				metrics: ['impression', 'net_revenue'],

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'date',

					place_keys: {
						app_id: 'app_bundle_id',
					},

					country: 'country',

					imp: 'impression',
					net_revenue: 'net_revenue',
				},

				report_request_api: 'https://ssp.svr-algorix.com/api/report/v2',
			},
		},

		// [Outside] RHYTHMONE 연동 정보
		rhythmone: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/rhythmone',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				groupBy: ['EVENT_TIME', 'DOMAIN_BUNDLE', 'COUNTRY'],
				sorting: { column: 'DOMAIN_BUNDLE', direction: 'ASC' },

				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'Date',

					place_keys: {
						app_id: 'Site/App Bundle',
					},

					country: 'Country',

					imp: 'Impressions',
					clk: 'Clicks',
					net_revenue: 'Est. Earnings (USD)',
				},

				token_request_api: 'https://api.unruly.co/ctrl/auth',
				report_request_api: 'https://api.unruly.co/ctrl/api/insights/detailed',
				report_download_api: 'https://api.unruly.co/ctrl/api/download/fileURL'
			}
		},

		// [Outside] PANGLE 연동 정보
		pangle: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/pangle',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'date',

					place_keys: {
						app_id: 'app_name',
					},

					country: 'region',
					os: 'os',

					imp: 'show',
					clk: 'click',
					net_revenue: 'revenue',
				},

				report_request_api: 'https://open-api.pangleglobal.com/union_pangle/open/api/rt/income',
			}
		},

		// [Outside] CHARTBOOST 연동 정보
		chartboost: {
			api_result: 'LOCAL_FILE',

			file_info: {
				file_path: '/chartboost',
				file_name: '{{AD_PROVIDER_ID}}_{{PUBLISHER_ID}}_{{START_DATE}}_{{END_DATE}}_{{HH}}.csv',
				separator: ',',
			},

			ext: {
				// 리포트 파일 데이터 필드 정보
				fields: {
					ymd: 'dt',

					place_keys: {
						appId: 'appId',
						adLocation: 'adLocation'
					},

					country: 'countryCode',
					os: 'platform',

					imp: 'impressionsDelivered',
					clk: 'clicksDelivered',
					net_revenue: 'moneyEarned',
				},

				token_request_api: 'https://api.chartboost.com/v5/oauth/token',
				report_request_api: 'https://api.chartboost.com/v5/analytics/appcountry',
			}
		},
	},

	// 리포트 다운로드 경로
	report: {
		path: '/report',
		ad_provider: {
			path: '/adprovider'
		},
		gfp: {
			path: '/gfp'
		},
		revenue_sharing: {
			path: '/revenuesharing2',
			hdfs: {
				root_dir: '/user/gfp-data/revenue-sharing-report-v2'
			}
		},
		revenue_sharing3: {
			path: '/revenue_sharing',
			nubes: {
				root_dir: 'revenue_sharing_report'
			},
			hdfs: {
				root_dir: '/user/gfp-data/revenue_sharing_report'
			}
		},
		creator_advisor: {
			path: '/creatoradvisor',
			hdfs: {
				root_dir: '/user/gfp-data/creator-advisor-report'
			}
		},
		multi_dimensional: {
			path: '/multidimensional',
			hdfs: {
				root_dir: '/user/gfp-data/multi-dimensional-report'
			},
		},
		abtest: {
			path: '/abtest',
			hdfs: {
				root_dir: '/user/gfp-data/abtest'
			}
		},
		shortform: {
			path: '/shortform',
			hdfs: {
				root_dir: '/user/gfp-data/shortform'
			}
		},
		biz_analysis: {
			path: '/biz_analysis',
			hdfs: {
				root_dir: '/user/gfp-data/biz_analysis_report'
			}
		},
		performance: {
			path: '/performance_report',
			api_path: '/api',
			cms_path: '/cms',
			hdfs: {
				root_dir: '/user/gfp-data/performance_report',
			}
		},
		monitoring_zircon_b: {
			path: '/monitoring_zircon_b',
			hdfs: {
				root_dir: '/user/gfp-data/monitoring/zircon/b',
			}
		},
		monitoring_revenue: {
			path: '/monitoring_revenue'
		}
	},

	silver: {
		hdfs: {
			root_dir: '/user/gfp-data/silver'
		},
		backup_restore: {
			sub_path: ['/er-ssp-server', '/er-ssp-client'],
			dt_path: '/YYYYMMDD-HH',
		}
	},

	gold: {
		hdfs: {
			root_dir: '/user/gfp-data/gold'
		},
		backup_restore: {
			sub_path: ['/adprovider', '/adunit'],
			dt_path: '/YYYY/MM/DD/HH',
		}
	},

	silvergrey: {
		hdfs: {
			root_dir: '/user/gfp-data/silvergrey',
			intermediate_dir: 'intermediate',
			source_dir: 'source',
			nonrk_dir: 'nonrk',
			rk_dir: 'rk',
			common: '/{{YYYY}}/{{MM}}/{{DD}}/adProviderId={{adProviderId}}/publisherId={{publisherId}}',
		},
		backup_restore: {
			sub_path: ['/rk', '/nonrk'],
			dt_path: '/YYYY/MM/DD',
		}
	},

	zircon_b: {
		hdfs: {
			root_dir: '/user/gfp-data/zircon/b',
			intermediate: '/intermediate',
			compaction: '/compaction',
			warehouse: '/warehouse',
			gfp: {
				root_dir: '/gfp',
				intermediate: '/intermediate',
				compaction: '/compaction',
				warehouse: '/warehouse',
			}
		},
		backup_restore: {
			sub_path: ['/warehouse'],
			dt_path: '/YYYY/MM/DD',
		}
	},

	zircon_r_gfp: {
		hdfs: {
			root_dir: '/user/gfp-data/zircon/r/gfp',
			intermediate: '/intermediate',
			compaction: '/compaction',
			warehouse: '/warehouse',
		},
		backup_restore: {
			sub_path: ['/warehouse'],
			dt_path: '/YYYY/MM/DD',
		}
	},

	adprovider_abuse: {
		hdfs: {
			root_dir: '/user/gfp-data/adprovider_abuse/cq',

			common: '/created={{targetDate}}/{{YYYY}}/{{MM}}/{{DD}}/adProviderId={{adProviderId}}/publisherId={{publisherId}}',
		}
	},

	// Key-Value Extension (확장키) fullSync 파일 다운로드 경로
	kvext_path: '/kvext/fullsync',

	backup_restore: {
		backup: {
			base_dir: '/home3/backup',
		},
		restore: {
			base_dir: '/home2/restore',
		},
		pigz: {
			process_cnt: 6
		}
	}
};
