// Set the 'local' environment configuration object
module.exports = {
	// server port
	// port: '3000',
	port: '11000',

	// dev
	//db_url: '***********************************************************************************************************',

	// test
	db_url: '************************************************************************************************************',
	data_db_url: '*******************************************************************************************************************************************************',

	// real
	// db_url: '**************************************************************************************************************************************************',
	// data_db_url: '****************************************************************************************************************************',

	db_poolsize: 20,

	// logger
	log_level: 'debug',

	// local download root
	local_root: '/home1/irteam/local_download',

	log_dir: './../../logs/ssp_batch',

	neon_url: 'https://nfidev.navercorp.com:5002',

	nam_api_url: 'http://app-test.nam-api.svc.ad1.io.navercorp.com:8080/v1',

	// nelo
	nelo: {
		url: 'http://nelo2-col.navercorp.com/_store',
		projectName: 'TEST_da_ssp_batch',
		projectVersion: '1.0.0',
		transfer: false,
		silent: false
	},

	nubes: {
		nam_api: {
			bucket: 'nam_api_dev',
			gatewayAddress: 'a-dev.nubes.sto.navercorp.com:8000',
		},
		gfp_data: {
			bucket: 'gfp-data',
			gatewayAddress: 'a-dev.nubes.sto.navercorp.com:8000',
		}
	},

	nclavis: {
		url: 'https://dev-apis.nclavis.navercorp.com/kms/consumer',
		keyResourceId: '4HXcsOPwA43So3r16DxORi6NP7I=', // 서비스(GFP), 키(gfp-report)
		accessToken: 'IwnQV1ruKzkfNi5Q9wDUc5UUK7VUIomL1TGXb+cOGunnltEFDwwPhRUacuXYI17MaENmVpvyZDidCmJZXXBEyQl9QlF17FEt40gx7fsCLfuQwXGxWADU2dnAH4H7NafIKIsKlw0mveGyD16MlkLfqT7lMeEIItmwHKNaraK2zUamZu2rfBO6EgqwqH0RRj5JFKuyUkC8Q9+dYB606HfiZxsDqHxZ69vP9Xldgnb1p0YzxlmpSoIPuAq4t1cDGaHsRE6ZE97UNH08mURdx7/Rb08KOnMovCDGooVL6JGMtYgUkbr18tYxKALkgkwqZmTCWijC0lmCciR6gPZVA5N7U3D7atNbkv5IlcBk09UQ6dwKo94m46C4rlEZLqK5+2EQ8jl2MeXdzfbVfTEcvQicbw=='
	},

	// test
	c3: {
		home: '/home1/irteam/apps/c3',
		namespace: 'bizcloud',
		user: 'gfp-data',
		realm: 'C3X.NAVER.COM',
		pw: '1:Om1pVezYWhzcLB4N:i6ygsjVxJwYNE4wWTVRrzhJLFCaJAp5m',
		ccname: '/home1/irteam/apps/c3/etc/krb5cc', // ccache(credential cache) name

		protocol: 'http',
		host: 'adevthm003-sa.nfra.io',
		port: 50070,
		path_prefix: '/webhdfs/v1',

		active_namenode: true, // true 인 경우, active namenode 찾아서 요청. false 인 경우, host 그대로 사용
		namenodes: ['adevthm003-sa.nfra.io', 'adevthm002-sa.nfra.io']
	},

	// real
	// c3: {
	// 	home: '/home1/irteam/apps/c3',
	// 	namespace: 'pg07',
	// 	user: 'gfp-data',
	// 	realm: 'C3.NAVER.COM',
	// 	pw: '2:ieezShaIyhRII0Ir:JDvUGK3Ey-GHRBZLfGahfwr0p5zwLAbWwMpfyw==',
	// 	ccname: '/home1/irteam/apps/c3/etc/krb5cc', // ccache(credential cache) name
	//
	// 	protocol: 'https',
	// 	host: 'knox-pan.c3s.navercorp.com',
	// 	port: 443,
	// 	path_prefix: '/gateway/pg07-auth-kerb/webhdfs/v1',
	//
	// 	active_namenode: false, // true 인 경우, active namenode 찾아서 요청. false 인 경우, host 그대로 사용
	// 	namenodes: []
	// },

	cms: {
		api: {
			url: 'https://gfp-admin-api-test.io.naver.com/api/',
		}
	},

	// AdProvider 리포트 API 연동 정보
	report_api: {
		gfd: {
			ext: {
				nubes: {
					gatewayAddress: 'a-dev.nubes.sto.navercorp.com:8000',
				},
			}
		},

		ndp: {
			ext: {
				report_request_ready_api: 'http://test.media.da.navercorp.com/api/report/ndpforgfp/status?ymd={{YYYYMMDD}}',
			}
		},
	},

	report: {
		revenue_sharing: {
			local_root: "/home1/irteam/local_download",
		},
		creator_advisor: {
			local_root: '/home1/irteam/local_download',
		},
		multi_dimensional: {
			local_root: "/home1/irteam/local_download",
			sparkling: {
				home: '/home1/irteam/apps/sparkling'
			}
		},
		discrepancy: {
			tmp_for_zip: 'download/discrepancy_report',
		}
	},

	hadoop: {
		file_browser_url: 'http://atcdh001-sa.nfra.io:8889'
	}
};
