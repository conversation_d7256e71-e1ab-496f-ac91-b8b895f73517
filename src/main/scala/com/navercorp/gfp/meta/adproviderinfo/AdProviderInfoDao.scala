package com.navercorp.gfp.meta.adproviderinfo

import scala.collection.JavaConverters._

import com.google.common.collect.Lists
import com.mongodb.client.model.{Filters, Projections}
import com.mongodb.client.{FindIterable, MongoCollection}
import org.bson.types.ObjectId

import com.navercorp.gfp.core.database.Database

class AdProviderInfoDao {
    def getAdProviderInfos(adProviderIds: Seq[String], publisherIds: Seq[String]): FindIterable[AdProviderInfo] = {
        val collection: MongoCollection[AdProviderInfo] = Database.getDatabase
            .getCollection("SyncAdProviderInfos", classOf[AdProviderInfo])

        val filters = Option(publisherIds) match {
            case Some(_) =>
                Filters.and(
                    Filters.in("adProvider_id", adProviderIds.map { new ObjectId(_) }: _*),
                    Filters.in("publisher_id", publisherIds.map { new ObjectId(_) }: _*),
                )
            case None =>
                Filters.and(
                    Filters.in("adProvider_id", adProviderIds.map { new ObjectId(_) }: _*),
                )
        }
        val projection = Projections.fields(Projections.include("adProvider_id", "publisher_id", "allowPlaceKeyDataPulling"))

        collection.find(filters)
            .projection(projection)
    }

    def getAdProviderInfos(publisherId: String, adProviderIds: Seq[String]): FindIterable[AdProviderInfo] = {
        val collection: MongoCollection[AdProviderInfo] = Database.getDatabase
            .getCollection("SyncAdProviderInfos", classOf[AdProviderInfo])

        val filters = Filters.and(
            Filters.eq("publisher_id", new ObjectId(publisherId)),
            Filters.in("adProvider_id", adProviderIds.map {
                new ObjectId(_)
            }: _*),
            Filters.in("reportStat", "COMPLETE", "NA"),
            Filters.eq("reportApiStatus", "ON"),
        )
        val projection = Projections.fields(Projections.include("adProvider_id", "publisher_id", "allowPlaceKeyDataPulling"))

        collection.find(filters)
            .projection(projection)
    }

    def getAdProviderIdsByPublisherId(publisherId: String): Vector[String] = {
        val collection: MongoCollection[AdProviderInfo] = Database.getDatabase
            .getCollection("SyncAdProviderInfos", classOf[AdProviderInfo])

        val filters = Filters.eq("publisher_id", new ObjectId(publisherId))
        val projection = Projections.fields(Projections.include("adProvider_id", "publisher_id"))

        val infos = collection.find(filters)
            .projection(projection)

        val ids = infos.map(_.adProvider_id.toString)
        Lists.newArrayList(ids).asScala.to[Vector]
    }

    def getPublisherIdsByAdProviderId(adProviderId: String): Vector[String] = {
        val collection: MongoCollection[AdProviderInfo] = Database.getDatabase
            .getCollection("SyncAdProviderInfos", classOf[AdProviderInfo])

        val filters = Filters.eq("adProvider_id", new ObjectId(adProviderId))
        val projection = Projections.fields(Projections.include("adProvider_id", "publisher_id"))

        val infos = collection.find(filters)
            .projection(projection)

        val ids = infos.map(_.publisher_id.toString)
        Lists.newArrayList(ids).asScala.to[Vector]
    }
}
