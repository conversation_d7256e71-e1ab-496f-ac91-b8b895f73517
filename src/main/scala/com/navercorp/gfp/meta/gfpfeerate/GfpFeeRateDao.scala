package com.navercorp.gfp.meta.gfpfeerate

import com.mongodb.client.model.Filters
import com.mongodb.client.{AggregateIterable, MongoCollection}
import com.navercorp.gfp.core.database.Database
import org.bson.types.ObjectId
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.model.Aggregates.{`match`, project}

import java.util

class GfpFeeRateDao {
	def getGfpFeeRate(adProviderId: String, publisherId: String, date: String): Option[GfpFeeRate] = {
		val collection: MongoCollection[GfpFeeRate] = Database.getDatabase
			.getCollection("GfpFeeRate", classOf[GfpFeeRate])

		val filters = Filters.and(
			Filters.eq("adProvider_id", new ObjectId(adProviderId)),
			Filters.eq("publisher_id", new ObjectId(publisherId)),
			Filters.eq("date", date),
		)

		Option(
			collection.find(filters).first
		)
	}

	def getGfpFeeRate(adProviderId: String, publisherId: String, startDate: String, endDate: String): AggregateIterable[GfpFeeRate] = {
		val collection: MongoCollection[GfpFeeRate] = Database.getDatabase
			.getCollection("GfpFeeRate", classOf[GfpFeeRate])

		collection.aggregate(util.Arrays.asList(
			`match`(Document(s"{ adProvider_id: ObjectId('$adProviderId'), publisher_id: ObjectId('$publisherId'), date: { $$gte: '$startDate', $$lte: '$endDate' } }")),
			project(Document("{ _id: 0, adProvider_id: 1, publisher_id: 1, date: 1, feeRate: 1 }")),
		))
	}
}
