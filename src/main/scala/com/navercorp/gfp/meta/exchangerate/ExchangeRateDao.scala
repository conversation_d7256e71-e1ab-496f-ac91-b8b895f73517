package com.navercorp.gfp.meta.exchangerate

import com.mongodb.client.model.Filters
import com.mongodb.client.MongoCollection
import com.navercorp.gfp.core.database.Database

class ExchangeRateDao {
	def getExchangeRate(endDate: String, fromCurrency: String, toCurrency: String): Option[ExchangeRateAverages] = {
		val collection: MongoCollection[ExchangeRateAverages] = Database.getDatabase
			.getCollection("ExchangeRateAverages", classOf[ExchangeRateAverages])

		val filters = Filters.and(
			Filters.eq("endDate", endDate),
			Filters.eq("currencyCdFrom", fromCurrency),
			Filters.eq("currencyCdTo", toCurrency),
		)

		Option(
			collection.find(filters).first
		)
	}
}
