package com.navercorp.gfp.meta.customreport

import com.mongodb.client.MongoCollection
import com.mongodb.client.model.Filters
import org.bson.types.ObjectId

import com.navercorp.gfp.core.database.Database

class CustomReportDao {
	def getCustomReport(reportId: String): Option[CustomReport] = {
		val collection: MongoCollection[CustomReport] = Database.getDatabase().getCollection("CustomReports", classOf[CustomReport])

		Option(collection.find(Filters.eq("_id", new ObjectId(reportId))).first())
	}
}
