package com.navercorp.gfp.meta.publisher

import org.bson.types.ObjectId

object CorporationType extends Enumeration {
	val NAVER = Value("NAVER")
	val FAMILY = Value("FAMILY")
	val OUTSIDE = Value("OUTSIDE")
}

case class Publisher(
						_id: ObjectId,
						name: Option[String],
						cmsType: Option[String],
						doohUse: Option[String] = None,

						// 법인타입: NAVER, FAMILY, OUTSIDE
						corporationType: Option[String]
					)