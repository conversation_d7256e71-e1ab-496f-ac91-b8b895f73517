package com.navercorp.gfp.biz.creatoradvisor

import scala.collection.JavaConverters._

import com.mongodb.client.model.Filters
import org.apache.logging.log4j.{LogManager, Logger}
import org.bson.types.ObjectId

import com.navercorp.gfp.core.database.Database
import com.navercorp.gfp.meta.adprovider.AdProvider
import com.navercorp.gfp.meta.adproviderinfo.AdProviderInfo

class CreatorAdvisorDao {
	private val logger: Logger = LogManager.getLogger(this.getClass)

	def getAdProviderIds(publisherId: String): Seq[String] = {
		val collection = Database.getDatabase.getCollection("SyncAdProviderInfos", classOf[AdProviderInfo])

		val apInfos = collection.find(Filters.eq("publisher_id", new ObjectId(publisherId)))

		apInfos.map(apInfo => apInfo.adProvider_id.toHexString).into(new java.util.ArrayList[String]()).asScala
	}

	def getAdProviderTimezoneMap(adProviderIds: Seq[String]): Map[String, String] = {
		val collection = Database.getDatabase.getCollection("SyncAdProviders", classOf[AdProvider])

		val filters = Filters.and(
			Filters.in("_id", adProviderIds.map(new ObjectId(_)): _*),
			Filters.eq("reportApi.rkUse", 1),
		)

		val adProviders = collection.find(filters)

		adProviders.map(ap => ap._id.toHexString -> ap.timezone.getOrElse("")).asScala.toMap
	}
}
