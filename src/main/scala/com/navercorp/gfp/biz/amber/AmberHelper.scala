package com.navercorp.gfp.biz.amber

import org.apache.logging.log4j.{LogManager, Logger}
import org.apache.spark.sql.DataFrame

trait AmberHelper {
	private val logger: Logger = LogManager.getLogger(this.getClass)

	def getNumPartitions(df: DataFrame): Int = {
		val dfCount = df.count()
		val fiveThousand = 5000

		var numPartitions = dfCount / fiveThousand

		if (dfCount % fiveThousand > 0) {
			numPartitions += 1
		}

		if (numPartitions < 1) {
			numPartitions = 1
		}

		logger.debug(s".......... [AmberHelper] numPartitions: $numPartitions")
		numPartitions.toInt
	}
}
