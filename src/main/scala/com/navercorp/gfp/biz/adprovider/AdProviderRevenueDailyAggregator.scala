package com.navercorp.gfp.biz.adprovider

import java.util.Date
import scala.collection.JavaConverters._
import scala.concurrent.Future
import scala.util.Try

import org.apache.spark.sql._
import org.apache.spark.sql.functions.{expr, lit, when}
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, Period}
import org.mongodb.scala.model.Filters

import com.navercorp.gfp.core.BaseEnv.{SILVERGREY_NONRK_PATH, SILVERGREY_RK_PATH, mdbDefaultWriteOptions}
import com.navercorp.gfp.core.BaseUdf.toObjectId
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory, SummaryHistoryDetail}
import com.navercorp.gfp.core.{BaseAggregator, BizAggregator, DeleteFutureHelper}
import com.navercorp.gfp.exception.{BusinessException, BusinessExceptionType}
import com.navercorp.gfp.meta.adproviderinfo.{AdProviderInfo, AdProviderInfoDao}
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, ObjectIdUtil, TimeUtil}


/*
	실버그레이 로그에서 요약 통계를 MongoDB에 저장 (AdProvider 단위)
    - rk path : /user/gfp-data/silvergrey/rk
    - nonrk path : /user/gfp-data/silvergrey/nonrk

    - AP 리포트 연동을 하는 매체들을 처리한다. ( 스케쥴 생성 조건 : reportStat=COMPLETE/NA, reportApiStatus=ON )
    - publisher_ids 가 null 인 경우가 있으므로, AdProviderInfos 를 조회한다.

    rk : adProvider_ids=[N], publisher_ids=null
    nonRk : adProvider_ids=[1], publisher_ids=[1]

    spark 설정
		--num-executors 1
		--executor-cores 4
		--executor-memory 500m
		--conf spark.sql.shuffle.partitions=5
*/
object AdProviderRevenueDailyAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [DAILY-REVENUE]"

	private var sparkAppId: String = ""
	private var HDFS_ADPROVIDER_PATH: String = ""

	private val adProviderInfoDao = new AdProviderInfoDao

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 처리 대상 일자
		val startDate = args(0)
		val endDate = args(1)

		val rkUse = args(2).toInt
		val adProviderIds: Seq[String] = args(3).split(",").distinct
		val publisherIds: Seq[String] = if (args.length > 4 && args(4).nonEmpty) args(4).split(",").distinct else null

		// 이력쌓기 상세정보
		val detail = summaryHistoryId match {
			case Some(_) => None
			case None => Option(SummaryHistoryDetail(
				publisherIds = Option(publisherIds), adProviderIds = Option(adProviderIds), rkUse = Option(rkUse),
				startDate = Option(startDate), endDate = Option(endDate)
			))
		}

		try {
			// args 밸리데이션 체크
			// require 는 runtime 시점에 Exception 을 던진다.
			// Exception 없이, 인스턴스 생성 시점에 validation 체크를 하고 싶으면 apply 를 구현하도록 한다.
			//  - https://gist.github.com/jkpl/4932e8730c1810261381851b13dfd29d
			//  - https://www.47deg.com/blog/smart-constructors-in-scala/#smart-constructors-for-case-classes-0
			//  - https://stackoverflow.com/questions/5982484/scala-lift-check-if-date-is-correctly-formatted
			require(ObjectIdUtil.isValidObjectIds(adProviderIds), s"adProviderIds($adProviderIds) is invalid ObjectIds")
			require(ObjectIdUtil.isValidObjectIds(publisherIds), s"publisherIds($publisherIds) is invalid ObjectIds")
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(startDate)).isSuccess, s"startDate($startDate) is invalid format (must be yyyyMMdd)")
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(endDate)).isSuccess, s"endDate($endDate) is invalid format (must be yyyyMMdd)")
			require(isValidDateRange(startDate, endDate), s"endDate($endDate) should be greater than startDate($startDate)")
			require(rkUse == 0 || rkUse == 1, s"rkUse($rkUse) is invalid")


			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(endDate),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
				detail = detail
			)
			this.upsertSummaryHistory(inProgressHist)

			logger.debug(s"$LOG_PREFIX startDate= $startDate, endDate= $endDate")
			logger.debug(s"$LOG_PREFIX adProviderIds= $adProviderIds")
			logger.debug(s"$LOG_PREFIX publisherIds= $publisherIds")

			// AdProviderInfo 정보 가져오기
			val adProviderInfos = adProviderInfoDao.getAdProviderInfos(adProviderIds, publisherIds).into(new java.util.ArrayList[AdProviderInfo]()).asScala

			HDFS_ADPROVIDER_PATH = Option(rkUse) match {
				case Some(0) => SILVERGREY_NONRK_PATH // rkUse=0 : nonRK path
				case Some(1) => SILVERGREY_RK_PATH // rkUse=1 : rK path
				case _ => throw BusinessException("rkUse is missing")
			}

			// startDt ~ endDt 일자별 경로 추출
			val dateTimeRange = getDateTimeRange(startDate, endDate, Period.days(1))

			// ap 경로 가져오기
			val pathList = getPathList(adProviderInfos, dateTimeRange)

			// AP 로그 로딩
			val apDf = loadParquetLog(pathList) match {
				case Some(df) => df
				case _ => spark.emptyDataFrame
			}

			// 집계 인스턴스 생성
			val aggregator = new AdProviderRevenueDailyAggregator(apDf, dateTimeRange, adProviderInfos)

			aggregator.init()

			val aggregatedDf = aggregator.aggregate()

			// 기존 데이터 삭제
			aggregator.delete(aggregator.getFuturesForDelete())

			// MongoDB에 저장
			aggregator.write(aggregatedDf)

			// 이력쌓기 - 완료 (COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					)),
					detail = detail
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	def isValidDateRange(startDate: String, endDate: String): Boolean = {
		// 날짜 포맷
		val tFormat = DateTimeFormat.forPattern("yyyyMMdd")

		// AP 타임존 시간 datetime
		val tStartDt = tFormat.parseDateTime(s"$startDate")
		val tEndDt = tFormat.parseDateTime(s"$endDate")

		!tStartDt.isAfter(tEndDt)
	}

	/**
	 * ap 로그 경로 가져오기
	 *
	 * @param adProviderInfos
	 * @param dateTimeRange
	 * @return pathList
	 */
	def getPathList(adProviderInfos: Seq[AdProviderInfo], dateTimeRange: List[DateTime]): Seq[String] = {
		logger.debug(s"$LOG_PREFIX HDFS_ADPROVIDER_PATH= $HDFS_ADPROVIDER_PATH")

		val pathList = dateTimeRange.flatMap { dt =>
			adProviderInfos.map { adProviderInfo =>
				val path: String = s"$HDFS_ADPROVIDER_PATH/${dt.toString("yyyy/MM/dd")}/adProviderId=${adProviderInfo.adProvider_id}/publisherId=${adProviderInfo.publisher_id}"

				// hdfs 에 존재하는 경로만 따로 추출
				if (HdfsUtil.exists(hdfs, path)) path else ""
			}
		}.filter(_.nonEmpty)

		//    logger.debug(s"pathList $pathList")

		pathList match {
			case List() => throw BusinessException("pathList is empty", BusinessExceptionType.EmptyPath)
			case _ => pathList
		}
	}
}

/**
 * 데이터 삭제 / 집계 / 추가 등의 작업을 한다.
 *
 * spark-submit에서 호출할 때나 test case에서 호출할 때 사용함
 * 환경에 따라 달라지는 값은 생성자 파라미터로 받아서 처리함.
 *
 * @param logDf
 * @param dateTimeRange
 * @param adProviderInfos
 */
class AdProviderRevenueDailyAggregator(logDf: Dataset[Row], dateTimeRange: List[DateTime], adProviderInfos: Seq[AdProviderInfo])(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper {
	val LOG_PREFIX = ".......... [DAILY-REVENUE]"

	COLLECTION_NAME = "AdProviderRevenueDaily"

	import spark.implicits._

	/**
	 * 데이터 집계
	 *
	 * @return finalDf Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 실행")

		val groupByList: Seq[String] = Seq("date", "publisherId", "adProviderId", "dealYn")
		val selectList: Seq[String] = groupByList ++ Seq(
			"netRevenueUSD", "netRevenueKRW"

			// // decimal 로 cast 를 이미 했기 때문에, SUM 할 때는 따로 처리 안 해줘도 됨
			// "cast(netRevenueUSD as decimal(38,18)) AS netRevenueUSD",
			// "cast(netRevenueKRW as decimal(38,18)) AS netRevenueKRW",
		)

		// GFP 수수료 포함
		val aggList = Seq(
			expr("NVL(SUM(netRevenueUSD), 0)").as("revenueUSD"),
			expr("NVL(SUM(netRevenueKRW), 0)").as("revenueKRW")
		)

		var newDf = logDf

		// dealId 디폴트 값 빈스트링 추가
		if (!newDf.columns.contains("dealId")) {
			newDf = newDf.withColumn("dealId", lit(""))
		} else {
			newDf = newDf.withColumn("dealId", when($"dealId".isNull, "").otherwise($"dealId"))
		}

		// AP 로그에서 가져온 데이터
		val finalDf = newDf
			// dealYn 추가
			.withColumn("dealYn", when($"dealId" === "", "N").otherwise("Y"))
			.selectExpr(selectList: _*)
			.groupBy(groupByList.head, groupByList.tail: _*)
			.agg(aggList.head, aggList.tail: _*)
			// 생성한 날짜
			.withColumn("createdAt", functions.current_timestamp)
			// MongoDB TTL Index 에 의해 삭제될 날짜
			.withColumn("expiredAt", TimeUtil.getExpiredAtMonthlyAsColumn($"date"))
			// 몽고디비 ObjectId 타입으로 변환
			.withColumn("publisher_id", toObjectId($"publisherId"))
			.withColumn("adProvider_id", toObjectId($"adProviderId"))
			// 불필요한 필드 제거
			.drop("publisherId", "adProviderId")

		// logger.debug("finalDf schema >>>>>>")
		// finalDf.printSchema()
		// finalDf.show(30, false)
		// finalDf.explain()

		finalDf
	}

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	override def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		logger.debug(s"$LOG_PREFIX ${dateTimeRange.head.toString("yyyyMMdd")} ~ ${dateTimeRange.last.toString("yyyyMMdd")}")
		logger.debug(s"$LOG_PREFIX adProviderInfos= ${adProviderInfos.toString}")

		val futures: Seq[Future[Option[Boolean]]] = adProviderInfos.flatMap { adProviderInfo =>
			val additionalFilters = Seq(
				Filters.eq("publisher_id", adProviderInfo.publisher_id),
				Filters.eq("adProvider_id", adProviderInfo.adProvider_id),
			)

			super.getFuturesForDeleteByDateRange(COLLECTION_NAME, dateTimeRange.map(_.toString("yyyyMMdd")), additionalFilters)
		}

		futures.toList
	}

	/**
	 * 데이터 추가
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		logger.debug(s"$LOG_PREFIX COLLECTION_NAME=$COLLECTION_NAME")

		val writeOptions = mdbDefaultWriteOptions
			.updated("spark.mongodb.output.collection", COLLECTION_NAME)
			.updated("spark.mongodb.output.shardKey", "{ adProvider_id:1, date:1, publisher_id:1, dealYn:1 }")

		super.writeToMongoDB(df, writeOptions)
	}
}
