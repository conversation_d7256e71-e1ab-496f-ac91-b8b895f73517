package com.navercorp.gfp.biz.adprovider

import com.navercorp.gfp.core.BaseEnv.{SILVER_CLIENT_INPUT_PATH, SILVER_SERVER_INPUT_PATH, mdbDefaultWriteOptions}
import com.navercorp.gfp.core.BaseUdf.toObjectId
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory, SummaryHistoryDetail}
import com.navercorp.gfp.core.{BaseAggregator, BizAggregator, DeleteFutureHelper}
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, TimeUtil}
import org.apache.spark.sql.functions.{explode, expr, lit}
import org.apache.spark.sql.types._
import org.apache.spark.sql._
import org.bson.types.ObjectId
import org.joda.time.format.DateTimeFormat
import org.mongodb.scala.model.Filters

import java.util.Date
import scala.concurrent.Future
import scala.util.Try
import scala.util.control.Breaks.{break, breakable}

/*
	실버 로그에서 요약통계를 MongoDB에저장 (AdProvider 단위)
	- path : /user/gfp-data/silver
	- NAM 매체를 대상으로 AP 별 타임아웃률을 구한다.

	GFP 7개 매체 대상 으로 확인 시, spark 설정
		--num-executors 64
		--executor-cores 3
		--executor-memory 1g


	NAM 매체 대상으로 확인 시, spark 설정
		--num-executors 24
		--executor-cores 3
		--executor-memory 800m
		--conf spark.sql.shuffle.partitions=800
		--conf spark.sql.files.maxPartitionBytes=64mb
*/
object AdProviderTimeoutDailyAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [DAILY-TIMEOUT]"

	private var sparkAppId: String = ""

	// Vector vs List
	//  - Vector 가 List 보다 여러 측면에서 좋은점이 많다고 하는데, 사내 소스코드에는 Vector 보다 List 를 쓰는 경우가 더 많다.
	//    매체나 경로 정보가 수십만개도 아니고, 성능에 영향을 줄 정도는 아니라고 생각하지만,
	//    혹~시 나중에 성능 문제가 생기면 Vector로 바꿔보도록 한다.
	//  - https://www.baeldung.com/scala/vector-benefits
	//  - https://www.geeksforgeeks.org/difference-between-vector-and-list/
	//  - https://stackoverflow.com/questions/2209224/vector-vs-list-in-stl

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 처리 대상 일자
		val targetDate = args(0)

		var detail: Option[SummaryHistoryDetail] = None

		try {
			// args 밸리데이션 체크
			// require 는 runtime 시점에 Exception 을 던진다.
			// Exception 없이, 인스턴스 생성 시점에 validation 체크를 하고 싶으면 apply 를 구현하도록 한다.
			//  - https://gist.github.com/jkpl/4932e8730c1810261381851b13dfd29d
			//  - https://www.47deg.com/blog/smart-constructors-in-scala/#smart-constructors-for-case-classes-0
			//  - https://stackoverflow.com/questions/5982484/scala-lift-check-if-date-is-correctly-formatted
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(targetDate)).isSuccess, s"targetDate($targetDate) is invalid format (must be yyyyMMdd)")


			// NAM 매체 목록
			val publisherIds: Seq[String] = publisherDao.getNamPublisherIds()

			// 이력쌓기 상세정보
			detail = Option(SummaryHistoryDetail(publisherIds = Option(publisherIds)))

			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(targetDate),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
				detail = detail
			)
			this.upsertSummaryHistory(inProgressHist)

			logger.debug(s"$LOG_PREFIX publisherIds= $publisherIds")

			// 집계 인스턴스 생성
			val aggregator = new AdProviderTimeoutDailyAggregator(targetDate)

			// breakable 을 loop 안에 넣으면 break 가 continue 로 동작하고, loop 밖에 넣으면 break 로 동작한다.
			// 	- https://m.blog.naver.com/jiwon2772/221317139127
			publisherIds.foreach { publisherId =>
				breakable {
					logger.debug(s"$LOG_PREFIX 매체별 처리 시작 ( publisherId= $publisherId )")

					// 실버 로그 로딩
					val serverPathList = Seq(s"$SILVER_SERVER_INPUT_PATH/$targetDate-*/publisherId=$publisherId")
					val clientPathList = Seq(s"$SILVER_CLIENT_INPUT_PATH/$targetDate-*/publisherId=$publisherId")

					val (serverDf, clientDf) = (loadParquetLog(serverPathList), loadParquetLog(clientPathList)) match {
						case (Some(sdf), Some(cdf)) => (sdf, cdf)
						case (Some(sdf), None) => (sdf, emptyClientDf)
						case (None, Some(cdf)) => (emptyServerDf, cdf)
						case (None, None) =>
							logger.warn(s"$LOG_PREFIX publisherId= $publisherId 에 해당하는 HDFS 로그 경로가 없음")
							break
					}

					val aggregatedDf = aggregator.aggregate(Option(serverDf, clientDf, publisherId))

					// 기존 데이터 삭제
					aggregator.delete(aggregator.getFuturesForDelete(Option(publisherId)))

					// MongoDB에 저장
					aggregator.write(aggregatedDf)
				}
			}

			// 이력쌓기 - 완료 (COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					)),
					detail = detail
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * 로그 로딩
	 * @param pathList
	 * @return Dataset[Row]
	 */
	override def loadParquetLog(pathList: Seq[String]): Option[Dataset[Row]] = {
		// hdfs 에 pathPattern(*) 에 해당하는 파일이 있는지 확인
		if (HdfsUtil.existsPathPattern(hdfs, pathList.head)) {
			super.loadParquetLog(pathList) match {
				case Some(df) =>
					Option(PROFILE match {
						case "stage" | "real" =>
							df.filter("test == 0")
						case _ => df
					})
				case _ => None
			}
		} else {
			None
		}
	}

	def emptyClientDf: Dataset[Row] = {
		val clientSchema = StructType(List(
			StructField("eventId", IntegerType, nullable = true),
			StructField("adProviderId", StringType, nullable = true),
			StructField("stat", IntegerType, nullable = true),
			StructField("connectionType", StringType, nullable = true),
		))

		spark.createDataFrame(spark.sparkContext.emptyRDD[Row], clientSchema)
	}

	def emptyServerDf: Dataset[Row] = {
		val serverSchema = StructType(List(
			StructField("responses", ArrayType(StructType(List(
				StructField("adProviderId", StringType, nullable = true),
				StructField("stat", IntegerType, nullable = true),
				StructField("connectionType", StringType, nullable = true),
			))), nullable = true)
		))

		spark.createDataFrame(spark.sparkContext.emptyRDD[Row], serverSchema)
	}
}

/**
 * 데이터 삭제 / 집계 / 추가 등의 작업을 한다.
 *
 * spark-submit에서 호출할 때나 test case에서 호출할 때 사용함
 * 환경에 따라 달라지는 값은 생성자 파라미터로 받아서 처리함.
 *
 * @param date
 */
class AdProviderTimeoutDailyAggregator(date: String)(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper {
	val LOG_PREFIX = ".......... [DAILY-TIMEOUT]"

	COLLECTION_NAME = "AdProviderTimeoutDaily"

	private val groupByList: Seq[String] = Seq("adProviderId")
	private val aggList: Seq[Column] = Seq(
		expr("SUM(CASE WHEN (eventId IS NULL AND connectionType == 'S2S') OR (eventId == 1 AND connectionType == 'C2S') OR (eventId == 91 AND connectionType == 'C2S') THEN 1 ELSE 0 END)").as("adProviderRequests"),
		expr("SUM(CASE WHEN (eventId IS NULL AND connectionType == 'S2S' AND stat == 3) OR (eventId == 91 AND connectionType == 'C2S' AND stat == 3) THEN 1 ELSE 0 END)").as("timeout"),
	)

	import spark.implicits._

	/**
	 * 서버 데이터셋에서 필요한 필드만 추출
	 * @param df
	 * @return
	 */
	def prepareServerDf(df: Dataset[Row]): Dataset[Row] = {
		df.withColumn("res", explode($"responses"))
			.selectExpr("null AS eventId", "res.adProviderId AS adProviderId", "res.stat AS stat", "res.connectionType AS connectionType")
			.filter("adProviderId IS NOT NULL")
			.filter("connectionType == 'S2S'")
			.groupBy(groupByList.head, groupByList.tail:_*)
			.agg(aggList.head, aggList.tail: _*)
	}

	/**
	 * 클라이언트 데이터셋에서 필요한 필드만 추출
	 * @param df
	 * @return
	 */
	def prepareClientDf(df: Dataset[Row]): Dataset[Row] = {
		df.select("eventId", "adProviderId", "stat", "connectionType")
			.filter("adProviderId IS NOT NULL")
			.filter("eventId == 1 OR eventId == 91")
			.filter("connectionType == 'C2S'")
			.groupBy(groupByList.head, groupByList.tail:_*)
			.agg(aggList.head, aggList.tail: _*)
	}

	/**
	 * 데이터 집계
	 * @return finalDf Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 실행")

		val (serverDf, clientDf, publisherId) = aggParam.get

		val aggList = Seq(
			expr("SUM(adProviderRequests)").as("adProviderRequests"),
			expr("SUM(timeout)").as("timeout"),
		)

		// union vs unionAll
		//  - union 은 union distinct 의 약자로 중복을 제거한다.
		//  - unionAll 은 중복을 제거하지 않는다.

		// 실버 로그에서 가져온 데이터
		val finalDf = prepareServerDf(serverDf)
			.unionAll(prepareClientDf(clientDf))
			.groupBy(groupByList.head, groupByList.tail:_*)
			.agg(aggList.head, aggList.tail: _*)
			// 요약 대상 날짜
			.withColumn("date", lit(date))
			// 생성한 날짜
			.withColumn("createdAt", functions.current_timestamp)
			// MongoDB TTL Index 에 의해 삭제될 날짜
			.withColumn("expiredAt", TimeUtil.getExpiredAtDailyAsColumn($"date"))
			// 몽고디비 ObjectId 타입으로 변환
			.withColumn("publisher_id", toObjectId(lit(publisherId)))
			.withColumn("adProvider_id", toObjectId($"adProviderId"))
			// 불필요한 필드 제거
			.drop("adProviderId")

//		logger.debug("finalDf schema >>>>>>")
//		finalDf.printSchema()
//		finalDf.show(30)
		finalDf.explain("formatted")

		finalDf
	}

	type A = (Dataset[Row], Dataset[Row], String)

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		val publisherId = param.get

		logger.debug(s"$LOG_PREFIX date= $date, publisherId=$publisherId")

		val additionalFilters = Seq(
			Filters.eq("publisher_id", new ObjectId(publisherId)),
		)

		super.getFuturesForDeleteByDateRange(COLLECTION_NAME, List(date), additionalFilters)
	}

	type T = String

	/**
	 * 데이터 추가
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		logger.debug(s"$LOG_PREFIX COLLECTION_NAME=$COLLECTION_NAME")

		val writeOptions = mdbDefaultWriteOptions
			.updated("spark.mongodb.output.collection", COLLECTION_NAME)

		super.writeToMongoDB(df, writeOptions)
	}
}
