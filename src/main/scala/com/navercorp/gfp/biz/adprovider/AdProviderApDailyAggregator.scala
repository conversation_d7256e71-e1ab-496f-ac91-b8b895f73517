package com.navercorp.gfp.biz.adprovider

import java.util
import java.util.Date
import scala.collection.JavaConverters._
import scala.concurrent.Future
import scala.util.Try

import org.apache.spark.sql._
import org.apache.spark.sql.functions.{expr, lit}
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, Period}
import org.mongodb.scala.model.Accumulators.addToSet
import org.mongodb.scala.model.Aggregates.{`match`, group, project}
import org.mongodb.scala.model.Filters
import org.mongodb.scala.model.Projections.excludeId

import com.navercorp.gfp.core.BaseEnv.{SILVERGREY_NONRK_PATH, SILVERGREY_RK_PATH, mdbDefaultWriteOptions}
import com.navercorp.gfp.core.BaseUdf.toObjectId
import com.navercorp.gfp.core.database.Database
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory, SummaryHistoryDetail}
import com.navercorp.gfp.core.{BaseAggregator, BizAggregator, DeleteFutureHelper}
import com.navercorp.gfp.exception.{BusinessException, BusinessExceptionType}
import com.navercorp.gfp.meta.adproviderinfo.{AdProviderInfo, AdProviderInfoDao}
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, ObjectIdUtil, TimeUtil}

/*
	실버그레이 로그에서 요약 통계를 MongoDB에 저장 (AdProvider 단위)
    - rk path : /user/gfp-data/silvergrey/rk
    - nonrk path : /user/gfp-data/silvergrey/nonrk

    - AP 리포트 연동을 하는 매체들을 처리한다. ( 스케쥴 생성 조건 : reportStat=COMPLETE/NA, reportApiStatus=ON )
    - publisher_ids 가 null 인 경우가 있으므로, AdProviderInfos 를 조회한다.

    rk : adProvider_ids=[N], publisher_ids=null
    nonRk : adProvider_ids=[1], publisher_ids=[1]

    spark 설정
		--num-executors 3
		--executor-cores 3
		--executor-memory 500m
		--conf spark.executor.memoryOverhead=500m
		--conf spark.sql.shuffle.partitions=5
*/
object AdProviderApDailyAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [DAILY-AP]"

	private var sparkAppId: String = ""
	private var HDFS_ADPROVIDER_PATH: String = ""

	private val adProviderInfoDao = new AdProviderInfoDao

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 처리 대상 일자
		val startDate = args(0)
		val endDate = args(1)

		val rkUse = args(2).toInt
		val adProviderIds: Seq[String] = args(3).split(",").distinct
		val publisherIds: Seq[String] = if (args.length > 4 && args(4).nonEmpty) args(4).split(",").distinct else null

		// 이력쌓기 상세정보
		val detail = summaryHistoryId match {
			case Some(_) => None
			case None => Option(SummaryHistoryDetail(
				publisherIds = Option(publisherIds), adProviderIds = Option(adProviderIds), rkUse = Option(rkUse),
				startDate = Option(startDate), endDate = Option(endDate)
			))
		}

		try {
			// args 밸리데이션 체크
			// require 는 runtime 시점에 Exception 을 던진다.
			// Exception 없이, 인스턴스 생성 시점에 validation 체크를 하고 싶으면 apply 를 구현하도록 한다.
			//  - https://gist.github.com/jkpl/4932e8730c1810261381851b13dfd29d
			//  - https://www.47deg.com/blog/smart-constructors-in-scala/#smart-constructors-for-case-classes-0
			//  - https://stackoverflow.com/questions/5982484/scala-lift-check-if-date-is-correctly-formatted
			require(ObjectIdUtil.isValidObjectIds(adProviderIds), s"adProviderIds($adProviderIds) is invalid ObjectIds")
			require(ObjectIdUtil.isValidObjectIds(publisherIds), s"publisherIds($publisherIds) is invalid ObjectIds")
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(startDate)).isSuccess, s"startDate($startDate) is invalid format (must be yyyyMMdd)")
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(endDate)).isSuccess, s"endDate($endDate) is invalid format (must be yyyyMMdd)")
			require(isValidDateRange(startDate, endDate), s"endDate($endDate) should be greater than startDate($startDate)")
			require(rkUse == 0 || rkUse == 1, s"rkUse($rkUse) is invalid")


			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(endDate),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
				detail = detail
			)
			this.upsertSummaryHistory(inProgressHist)

			logger.debug(s"$LOG_PREFIX startDate= $startDate, endDate= $endDate")
			logger.debug(s"$LOG_PREFIX adProviderIds= $adProviderIds")
			logger.debug(s"$LOG_PREFIX publisherIds= $publisherIds")

			// AdProviderInfo 정보 가져오기
			val adProviderInfos = adProviderInfoDao.getAdProviderInfos(adProviderIds, publisherIds).into(new java.util.ArrayList[AdProviderInfo]()).asScala

			HDFS_ADPROVIDER_PATH = Option(rkUse) match {
				case Some(0) => SILVERGREY_NONRK_PATH // rkUse=0 : nonRK path
				case Some(1) => SILVERGREY_RK_PATH // rkUse=1 : rK path
				case _ => throw BusinessException("rkUse is missing")
			}

			// startDt ~ endDt 일자별 경로 추출
			val dateTimeRange = getDateTimeRange(startDate, endDate, Period.days(1))

			// ap 경로 가져오기
			val pathList = getPathList(adProviderInfos, dateTimeRange)

			// AP 로그 로딩
			val apDf = loadParquetLog(pathList) match {
				case Some(df) => df
				case _ => throw BusinessException("pathList is empty", BusinessExceptionType.EmptyPath)
			}

			// 집계 인스턴스 생성
			val aggregator = new AdProviderApDailyAggregator(apDf, dateTimeRange, adProviderInfos)

			aggregator.init()

			val aggregatedDf = aggregator.aggregate()

			// 기존 데이터 삭제
			aggregator.delete(aggregator.getFuturesForDelete())

			// MongoDB에 저장
			aggregator.write(aggregatedDf)

			// 이력쌓기 - 완료 (COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					)),
					detail = detail
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	def isValidDateRange(startDate: String, endDate: String): Boolean = {
		// 날짜 포맷
		val tFormat = DateTimeFormat.forPattern("yyyyMMdd")

		// AP 타임존 시간 datetime
		val tStartDt = tFormat.parseDateTime(s"$startDate")
		val tEndDt = tFormat.parseDateTime(s"$endDate")

		!tStartDt.isAfter(tEndDt)
	}

	/**
	 * ap 로그 경로 가져오기
	 *
	 * @param adProviderInfos
	 * @param dateTimeRange
	 * @return pathList
	 */
	def getPathList(adProviderInfos: Seq[AdProviderInfo], dateTimeRange: List[DateTime]): Seq[String] = {
		logger.debug(s"$LOG_PREFIX HDFS_ADPROVIDER_PATH= $HDFS_ADPROVIDER_PATH")

		val pathList = dateTimeRange.flatMap { dt =>
			adProviderInfos.map { adProviderInfo =>
				val path: String = s"$HDFS_ADPROVIDER_PATH/${dt.toString("yyyy/MM/dd")}/adProviderId=${adProviderInfo.adProvider_id}/publisherId=${adProviderInfo.publisher_id}"

				// hdfs 에 존재하는 경로만 따로 추출
				if (HdfsUtil.exists(hdfs, path)) path else ""
			}
		}.filter(_.nonEmpty)

		//    logger.debug(s"pathList $pathList")

		pathList
	}


	/**
	 * 로그 로딩
	 *
	 * @param pathList
	 * @return Dataset[Row]
	 */
	override def loadParquetLog(pathList: Seq[String]): Option[Dataset[Row]] = {
		// HDFS 경로가 하나도 없는 경우, None
		if (pathList.isEmpty) {
			None
		} else {
			super.loadParquetLog(pathList)
		}
	}
}

/**
 * 데이터 삭제 / 집계 / 추가 등의 작업을 한다.
 *
 * spark-submit에서 호출할 때나 test case에서 호출할 때 사용함
 * 환경에 따라 달라지는 값은 생성자 파라미터로 받아서 처리함.
 *
 * @param logDf
 * @param dateTimeRange
 * @param adProviderInfos
 */
class AdProviderApDailyAggregator(logDf: Dataset[Row], dateTimeRange: List[DateTime], adProviderInfos: Seq[AdProviderInfo])(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper {
	val LOG_PREFIX = ".......... [DAILY-AP]"

	COLLECTION_NAME = "AdProviderDaily"

	private val AD_PROVIDER_DAILY_TYPE = "AP"

	// Option[Seq[String]] 보다 Seq[Option[String]] 를 사용하는게 좋다.
	//  - https://stackoverflow.com/questions/32371382/scala-optionseqstring-vs-seqoptionstring/32371849#32371849
	private var countryList: Seq[String] = Seq()

	// country 는 aggregate 시 쓰이는데, countryList 는 init 할 때 생성 된다.
	// country 를 lazy 없이 쓰면, countryList 가 empty 이기 때문에, country 구문이 원하는 대로 생성 되지 않는다.
	// 물론 country 를 var 로 선언 하고, init 시점에 수정할 수는 있으나, 공통 쿼리 구문을 상단에 모아두고 싶어서 lazy 를 사용하였다.
	private lazy val country = s"CASE WHEN country IN ( ${countryList.mkString("'", "','", "'")} ) THEN country ELSE 'OTHERS' END AS country"

	import spark.implicits._
	// https://www.scala-lang.org/api/2.13.3/scala/collection/JavaConverters$.html


	/**
	 * 집계에 필요한 정보 조회하기
	 *    - Countries 에서 code 정보 조회
	 */
	override def init(): Unit = {
		countryList = getCountryCodes

		logger.debug(s"$LOG_PREFIX countries= ${countryList.toString()}")
	}

	def getCountryCodes: Seq[String] = {
		val collection = Database.getDatabase().getCollection("Countries")

		val countryCodes = Option(collection.aggregate(util.Arrays.asList(
			`match`(Filters.eq("major", 1)),
			group(null, addToSet("countryCodes", "$code")),
			project(excludeId()),
		)).first()) match {
			case Some(v) => v.getList("countryCodes", classOf[String]).asScala
			case _ => Seq()
		}

		countryCodes
	}

	/**
	 * 데이터 집계
	 *
	 * @return finalDf Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX country= $country")

		val baseList: Seq[String] = Seq("date", "publisherId", "adProviderId")
		val groupByList: Seq[String] = baseList ++ Seq("country")
		val selectList: Seq[String] = baseList ++ Seq(
			country, "impressions", "clicks",
			"netRevenueUSD", "netRevenueKRW",
			"gfpNetRevenueUSD", "gfpNetRevenueKRW"
		)

		val aggList = Seq(
			expr("NVL(SUM(impressions), 0)").as("impressions"),
			expr("NVL(SUM(clicks), 0)").as("clicks"),

			// GFP 수수료 포함
			expr("NVL(SUM(netRevenueUSD), 0)").as("revenueUSD"),
			expr("NVL(SUM(netRevenueKRW), 0)").as("revenueKRW"),

			// GFP 수수료 제외
			expr("NVL(SUM(gfpNetRevenueUSD), 0)").as("netRevenueUSD"),
			expr("NVL(SUM(gfpNetRevenueKRW), 0)").as("netRevenueKRW")
		)

		// AP 로그에서 가져온 데이터
		val finalDf = logDf
			.selectExpr(selectList: _*)
			.groupBy(groupByList.head, groupByList.tail: _*)
			.agg(aggList.head, aggList.tail: _*)
			// type=AP 추가
			.withColumn("type", lit(AD_PROVIDER_DAILY_TYPE))
			// 생성한 날짜
			.withColumn("createdAt", functions.current_timestamp)
			// MongoDB TTL Index 에 의해 삭제될 날짜
			.withColumn("expiredAt", TimeUtil.getExpiredAtMonthlyAsColumn($"date"))
			// 몽고디비 ObjectId 타입으로 변환
			.withColumn("publisher_id", toObjectId($"publisherId"))
			.withColumn("adProvider_id", toObjectId($"adProviderId"))
			// 불필요한 필드 제거
			.drop("publisherId", "adProviderId")

		// logger.debug("finalDf schema >>>>>>")
		// finalDf.printSchema()
		// finalDf.show(30, false)
		// finalDf.explain()

		finalDf
	}

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	override def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		logger.debug(s"$LOG_PREFIX ${dateTimeRange.head.toString("yyyyMMdd")} ~ ${dateTimeRange.last.toString("yyyyMMdd")}")
		logger.debug(s"$LOG_PREFIX adProviderInfos= ${adProviderInfos.toString}")

		val futures: Seq[Future[Option[Boolean]]] = adProviderInfos.flatMap { adProviderInfo =>
			val additionalFilters = Seq(
				Filters.eq("publisher_id", adProviderInfo.publisher_id),
				Filters.eq("adProvider_id", adProviderInfo.adProvider_id),
				Filters.eq("type", AD_PROVIDER_DAILY_TYPE),
			)

			super.getFuturesForDeleteByDateRange(COLLECTION_NAME, dateTimeRange.map(_.toString("yyyyMMdd")), additionalFilters)
		}

		futures.toList
	}

	/**
	 * 데이터 추가
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		logger.debug(s"$LOG_PREFIX COLLECTION_NAME=$COLLECTION_NAME")

		val writeOptions = mdbDefaultWriteOptions
			.updated("spark.mongodb.output.collection", COLLECTION_NAME)
			.updated("spark.mongodb.output.shardKey", "{ publisher_id:1, date:1, adProvider_id:1, type:1, country:1 }")

		super.writeToMongoDB(df, writeOptions)
	}
}
