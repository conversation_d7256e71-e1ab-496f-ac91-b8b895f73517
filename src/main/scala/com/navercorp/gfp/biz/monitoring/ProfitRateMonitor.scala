package com.navercorp.gfp.biz.monitoring

import java.util.Date
import scala.collection.JavaConverters._
import scala.concurrent.Future

import org.apache.spark.sql._
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, Period}

import com.navercorp.gfp.core.BaseEnv.{ZIRCON_B_WAREHOUSE_PATH, mdbDefaultWriteOptions}
import com.navercorp.gfp.core.BaseUdf.toObjectId
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory, SummaryHistoryDetail}
import com.navercorp.gfp.core.{BaseAggregator, BizAggregator, DeleteFutureHelper}
import com.navercorp.gfp.meta.adprovider.AdProvider
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, TimeUtil}


object ProfitRateMonitor extends BaseAggregator {
	val LOG_PREFIX = ".......... [MONITORING-PROFIT-RATE]"
	val sparkAppId: String = spark.sparkContext.applicationId

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 처리 대상 일자
		val startDate = args(0)
		val endDate = args(1)

		// 결과 파일 관련 정보
		val format = DateTimeFormat.forPattern("yyyyMMdd")
		// 전날 profitRate 과의 비교를 위해 하루 전 로그까지 load
		val dateTimeList = dateTimeRange(format.parseDateTime(startDate).minusDays(1), format.parseDateTime(endDate), Period.days(1)).toList

		try {
			val adProviderIds: Seq[String] = adProviderDao.getAdProvidersReportApiOn().into(new java.util.ArrayList[AdProvider]()).asScala.map(_._id.toString)
			val publisherIds: Seq[String] = publisherDao.getPublisherIds()

			logger.debug(s"$LOG_PREFIX startDate: $startDate")
			logger.debug(s"$LOG_PREFIX endDate: $endDate")
			logger.debug(s"$LOG_PREFIX adProviderIds: $adProviderIds")
			logger.debug(s"$LOG_PREFIX publisherIds: $publisherIds")

			// 이력쌓기 - IN_PROGRESS
			val detail = summaryHistoryId match {
				case Some(_) => None
				case None => Option(SummaryHistoryDetail(
					startDate = Option(startDate), endDate = Option(endDate),
					publisherIds = Option(publisherIds), adProviderIds = Option(adProviderIds),
				))
			}
			val inProgressHist = SummaryHistory(
				datetime = Option(endDate),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
				detail = detail
			)
			this.upsertSummaryHistory(inProgressHist)

			// 지르콘 B 경로
			val zirconPathList = getZirconPathList(adProviderIds, publisherIds, dateTimeList)

			val zirconDf = loadParquetLog(zirconPathList) match {
				case Some(df) => df
				case _ =>
					logger.warn(s"$LOG_PREFIX publisherId= $publisherIds 에 해당하는 HDFS 로그 경로가 없음")
					spark.emptyDataFrame
			}

			val aggregator = new ProfitRateMonitor(zirconDf, startDate, endDate)

			val aggdDf = aggregator.aggregate()

			// MongoDB 기존 데이터 삭제
			aggregator.delete(aggregator.getFuturesForDelete(Option(dateTimeList)))

			// MongoDB 데이터 쓰기
			aggregator.write(aggdDf)

			// 이력쌓기 - COMPLETE
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					))
				)
				this.upsertSummaryHistory(failureHist)
				logger.error(s"$LOG_PREFIX GAP report 생성 실패. sparkAppId=$sparkAppId, startDate: $startDate, endDate: $endDate, summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}", t)

				throw t
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * 지르콘B 경로 구하기
	 *
	 * @param adProviderIds
	 * @param publisherIds
	 * @param dateList
	 * @return pathList
	 */
	def getZirconPathList(adProviderIds: Seq[String], publisherIds: Seq[String], dateList: List[DateTime]): Seq[String] = {
		val pathList = adProviderIds.flatMap { apId =>
			publisherIds.flatMap { pubId =>
				dateList.map { date =>
					val path = s"$ZIRCON_B_WAREHOUSE_PATH/${date.toString("yyyy/MM/dd")}/*/_publisherId=$pubId/_adProviderId=$apId"

					if (HdfsUtil.existsPathPattern(hdfs, path))
						path
					else {
						logger.debug(s"존재하지 않음 $path")
						""
					}
				}

			}
		}.filter(_.nonEmpty)

		logger.debug(s"pathList $pathList")
		pathList
	}

	/**
	 * 주어진 경로로부터 parquet 로그 로드
	 *
	 * @param pathList
	 * @return Dataset[Row]
	 */
	override def loadParquetLog(pathList: Seq[String]): Option[Dataset[Row]] = {
		// HDFS 경로가 하나도 없는 경우, None
		if (pathList.isEmpty) {
			None
		} else {
			super.loadParquetLog(pathList)
		}
	}
}

class ProfitRateMonitor(zDf: Dataset[Row], startDate: String, endDate: String)(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper {
	val LOG_PREFIX = ".......... [MONITORING-PROFIT-RATE]"
	COLLECTION_NAME = "MonitoringProfitRate"

	import spark.implicits._

	private val dimensions = Seq("date", "publisherId", "adProviderId")
	private val metrics = Seq("apKrwSales", "apKrwProfit", "apUsdSales", "apUsdProfit")
	private val selectListForAgg = dimensions ++ metrics
	private val exprs = metrics.map(met => coalesce(sum(met), lit(0)).as(met))

	private val renameHeader = Seq(
		("date", "date"),
		("adProviderId", "adProvider_id"),
		("publisherId", "publisher_id"),

		("apKrwSales", "krwSales"),
		("apKrwProfit", "krwProfit"),
		("krwProfitRate", "krwProfitRate"),
		("deviationKrwProfitRate", "krwProfitRateDeviation"),

		("apUsdSales", "usdSales"),
		("apUsdProfit", "usdProfit"),
		("usdProfitRate", "usdProfitRate"),
		("deviationUsdProfitRate", "usdProfitRateDeviation"),
	)

	/**
	 * 데이터 집계
	 *
	 * @return finalDf Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		// 1. 지르콘으로부터 수익률 계산을 위한 기본 집계
		val aggDf = zDf.withColumn("date", date_format($"apTimestamp", "yyyyMMdd"))
			.selectExpr(selectListForAgg: _*)
			.groupBy(dimensions.head, dimensions.tail: _*)
			.agg(exprs.head, exprs.tail: _*)

		// 2. 수익 정보가 0 인 Row 제외 및 수익률 추가
		val rateDf = filterAllZeroMetrics(aggDf, metrics)
			.withColumn("krwProfitRate", when($"apKrwSales" === 0, 0).otherwise(round($"apKrwProfit" / $"apKrwSales", 3)))
			.withColumn("usdProfitRate", when($"apUsdSales" === 0, 0).otherwise(round($"apUsdProfit" / $"apUsdSales", 3)))

		// 3. 어제 profitRate 과 비교하기 위한 Window Spec 정의 및 적용
		val windowSpec = Window.partitionBy("publisherId", "adProviderId").orderBy("date")
		val deviationDf = rateDf
			.withColumn("prevKrwProfitRate", lag("krwProfitRate", 1).over(windowSpec))
			.withColumn("prevUsdProfitRate", lag("usdProfitRate", 1).over(windowSpec))
			.withColumn("deviationKrwProfitRate", when($"prevKrwProfitRate".isNull || $"prevKrwProfitRate" === 0, 0).otherwise(round(($"krwProfitRate" - $"prevKrwProfitRate") / $"prevKrwProfitRate", 3)))
			.withColumn("deviationUsdProfitRate", when($"prevUsdProfitRate".isNull || $"prevUsdProfitRate" === 0, 0).otherwise(round(($"usdProfitRate" - $"prevUsdProfitRate") / $"prevUsdProfitRate", 3)))

		// 4. DB 스키마에 맞게 컬럼명 수정 및 추가
		val finalDf = renameColumn(deviationDf.filter($"date".between(startDate, endDate)), renameHeader)
			// 몽고디비 ObjectId 타입으로 변환
			.withColumn("publisher_id", toObjectId($"publisher_id"))
			.withColumn("adProvider_id", toObjectId($"adProvider_id"))
			// 생성한 날짜
			.withColumn("createdAt", functions.current_timestamp)
			// MongoDB TTL Index 에 의해 삭제될 날짜
			.withColumn("expiredAt", TimeUtil.getExpiredAtMonthlyAsColumn($"date", 4))

		println(s"$LOG_PREFIX 최종 스키마 ........")
		finalDf.printSchema()

		finalDf
	}

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	type T = List[DateTime]

	def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		val dateTimeList = param.get
		logger.debug(s"$LOG_PREFIX dateTimeList= ${dateTimeList.tail.head.toString("yyyyMMdd")} ~ ${dateTimeList.last.toString("yyyyMMdd")}")

		val futures: Seq[Future[Option[Boolean]]] = super.getFuturesForDeleteByDateRange(COLLECTION_NAME, dateTimeList.tail.map(_.toString("yyyyMMdd")))

		futures.toList
	}

	/**
	 * 데이터 추가
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		logger.debug(s"$LOG_PREFIX COLLECTION_NAME=$COLLECTION_NAME")

		val writeOptions = mdbDefaultWriteOptions
			.updated("spark.mongodb.output.collection", COLLECTION_NAME)

		super.writeToMongoDB(df, writeOptions)
	}
}
