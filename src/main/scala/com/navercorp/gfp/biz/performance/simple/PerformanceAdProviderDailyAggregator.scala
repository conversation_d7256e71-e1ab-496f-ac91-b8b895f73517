package com.navercorp.gfp.biz.performance.simple

import java.util.Date
import scala.concurrent.Future
import scala.util.Try

import org.apache.spark.sql._
import org.bson.types.ObjectId
import org.joda.time.Period
import org.joda.time.format.DateTimeFormat

import com.navercorp.gfp.core.BaseEnv.{ZIRCON_B_WAREHOUSE_PATH, mdbDefaultWriteOptions}
import com.navercorp.gfp.core.BaseSchema.ZIRCON_B_SCHEMA
import com.navercorp.gfp.core.BaseUdf.toObjectId
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory, SummaryHistoryDetail}
import com.navercorp.gfp.core.{BaseAggregator, BizAggregator}
import com.navercorp.gfp.exception.BusinessException
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, ObjectIdUtil, TimeUtil}


/*
	지르콘B 기반 광고공급자별 성과 집계 데이터를 MongoDB 에 저장
	- input zircon : /zircon/b/warehouse/yyyy/mm/dd/hh/_publisherId=xxx/_adProviderId=xxx
	- output DB : PerformanceAdProviderDaily
		- 샤드키 : publisherId x timezone x date x adProviderId

	spark 설정
	ALL
		--num-executors 20
		--executor-cores 3
		--executor-memory 2g
		--conf spark.executor.memoryOverhead=500m
		--conf spark.sql.shuffle.partitions=500
		--conf spark.sql.files.maxPartitionBytes=64mb

	개별 매체
		--num-executors 5
		--executor-cores 4
		--executor-memory 2g
		--conf spark.executor.memoryOverhead=500m
		--conf spark.sql.shuffle.partitions=500
		--conf spark.sql.files.maxPartitionBytes=64mb
*/
object PerformanceAdProviderDailyAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [PERFORMANCE-AD-PROVIDER-DAILY]"

	private var sparkAppId: String = ""

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 처리 대상 일자
		val date = args(0)

		// empty 또는 * 인 경우, None 으로 처리함
		val publisherId = if (args.length > 1 && args(1).nonEmpty && !args(1).equals("*")) Option(args(1)) else None
		val adProviderIds: Option[Seq[String]] = if (args.length > 2 && args(2).nonEmpty && !args(2).equals("*")) Option(args(2).split(",").distinct) else None

		// 이력쌓기 상세정보
		val detail = summaryHistoryId match {
			case Some(_) => None
			case None => Option(SummaryHistoryDetail(publisherId = publisherId, adProviderIds = adProviderIds))
		}

		try {
			// args 밸리데이션 체크
			// require 는 runtime 시점에 Exception 을 던진다.
			// Exception 없이, 인스턴스 생성 시점에 validation 체크를 하고 싶으면 apply 를 구현하도록 한다.
			//  - https://gist.github.com/jkpl/4932e8730c1810261381851b13dfd29d
			//  - https://www.47deg.com/blog/smart-constructors-in-scala/#smart-constructors-for-case-classes-0
			//  - https://stackoverflow.com/questions/5982484/scala-lift-check-if-date-is-correctly-formatted
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(date)).isSuccess, s"date($date) is invalid format (must be yyyyMMdd)")
			if (publisherId.isDefined) require(ObjectId.isValid(publisherId.get), s"publisherId($publisherId) is invalid ObjectId")
			if (adProviderIds.isDefined) require(ObjectIdUtil.isValidObjectIds(adProviderIds.get), s"adProviderIds($adProviderIds) is invalid ObjectIds")

			logger.debug(s"$LOG_PREFIX date= $date, publisherId= $publisherId, adProviderIds= $adProviderIds")

			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(date),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
				detail = detail
			)
			this.upsertSummaryHistory(inProgressHist)

			// date(D-1, D+1 포함) 에 해당하는 지르콘B 경로 가져오기
			val pathList = getPathList(date, publisherId, adProviderIds)

			// 지르콘B 로딩
			val zirconDf = if (pathList.nonEmpty) {
				loadParquetLog(pathList) match {
					case Some(df) => df
					case _ => spark.createDataFrame(spark.sparkContext.emptyRDD[Row], ZIRCON_B_SCHEMA)
				}
			} else spark.createDataFrame(spark.sparkContext.emptyRDD[Row], ZIRCON_B_SCHEMA)

			// 집계 인스턴스 생성
			val aggregator = new PerformanceAdProviderDailyAggregator()

			// 집계에 필요한 정보 조회하기
			// 	- Countries 에서 countryCodes 정보 조회
			// 	- TimezoneMap 에서 timezones 정보 조회
			aggregator.init()

			val aggregatedDf = aggregator.aggregate(Option(zirconDf, date))

			// 기존 데이터 삭제
			aggregator.delete(aggregator.getFuturesForDelete(Option(date, publisherId, adProviderIds)))

			// MongoDB에 저장
			aggregator.write(aggregatedDf)

			aggregator.unpersist()

			// 이력쌓기 - 완료 (COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					))
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * date(D-1, D+1 포함) 에 해당하는 지르콘B 경로 가져오기
	 *
	 * @param date
	 * @param publisherId
	 * @param adProviderIds
	 * @return pathList
	 */
	def getPathList(date: String, publisherId: Option[String], adProviderIds: Option[Seq[String]]): Seq[String] = {
		val format = DateTimeFormat.forPattern("yyyyMMdd")
		val dtRange = dateTimeRange(format.parseDateTime(date).minus(Period.days(1)), format.parseDateTime(date).plus(Period.days(1)), Period.days(1)).toList

		val apPath = if (adProviderIds.isDefined) s"{${adProviderIds.get.mkString(",")}}" else "*"

		val pathList = dtRange.map { dt =>
			// /user/gfp-data/zircon/b/warehouse/yyyy/MM/dd/*/_publisherId=*/_adProviderId=*
			// /user/gfp-data/zircon/b/warehouse/yyyy/MM/dd/*/_publisherId=xxx/_adProviderId={xxx,xxx,xxx}
			val path: String = s"$ZIRCON_B_WAREHOUSE_PATH/${dt.toString("yyyy/MM/dd")}/*/_publisherId=${publisherId.getOrElse("*")}/_adProviderId=$apPath"

			// hdfs 에 존재하는 경로만 따로 추출
			if (HdfsUtil.existsPathPattern(hdfs, path)) path else ""
		}.filter(_.nonEmpty)

		logger.debug(s"pathList= $pathList")

		pathList
	}
}

/**
 * 데이터 삭제 / 집계 / 추가 등의 작업을 한다.
 *
 * spark-submit에서 호출할 때나 test case에서 호출할 때 사용함
 * 환경에 따라 달라지는 값은 생성자 파라미터로 받아서 처리함.
 */
class PerformanceAdProviderDailyAggregator()(implicit spark: SparkSession) extends BizAggregator with PerformanceDailyHelper {
	val LOG_PREFIX = ".......... [PERFORMANCE-AD-PROVIDER-DAILY]"

	COLLECTION_NAME = "PerformanceAdProviderDaily"

	import spark.implicits._

	// baseList = ["publisherId", "adProviderId"]
	// groupByList = ["publisherId", "adProviderId", "country"]
	val baseList: Seq[String] = Seq("publisherId", "adProviderId")
	val groupByList: Seq[String] = baseList ++ Seq("country")

	private var intermediateCachedDf: Dataset[Row] = _


	/**
	 * 집계에 필요한 정보 조회하기
	 *    - Countries 에서 countryCodes 정보 조회
	 *    - TimezoneMap 에서 timezones 정보 조회
	 */
	override def init(): Unit = {
		initInfos()
	}

	/**
	 * 데이터 집계
	 *
	 * @return Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		val (logDf, date) = aggParam.get

		// [1차] groupBy & agg 처리 ( 시간별 집계 )
		// hourlyGroupByList = ["apTimestamp", "utcTimestamp", "publisherId", "adProviderId", "country"]
		// hourlySelectList = ["apTimestamp", "utcTimestamp", "CASE WHEN country IN (...) THEN country ELSE 'OTHERS' END AS country", "publisherId", "adProviderId", "apImpressions as impressions", "apClicks as clicks", "apUsdSales as usdRevenue", ... ]
		val hourlyGroupByList: Seq[String] = Seq("apTimestamp", "utcTimestamp") ++ groupByList
		val hourlySelectList: Seq[String] = Seq("apTimestamp", "utcTimestamp", country) ++ baseList ++ metricList.map(METRICS_MAPPING)

		intermediateCachedDf = logDf
			.selectExpr(hourlySelectList: _*)
			.groupBy(hourlyGroupByList.head, hourlyGroupByList.tail: _*)
			.agg(aggList.head, aggList.tail: _*)
			.cache()

		// [2차] groupBy & agg 처리 ( 타임존별 일별 집계 )
		// dailyGroupByList = ["date", "publisherId", "adProviderId", "country"]
		// dailySelectList = ["date", "publisherId", "adProviderId", "country", "impressions", "clicks", "usdRevenue", ... ]
		val dailyGroupByList: Seq[String] = Seq("date") ++ groupByList
		val dailySelectList: Seq[String] = Seq("date") ++ groupByList ++ metricList

		val finalDf = getDailyByTimezoneDf(intermediateCachedDf, timezones, date, dailySelectList, dailyGroupByList, aggList) // 타임존별 일별 집계 Union 처리
			// 지표가 모두 0인 경우 제외 처리
			.filter(s"NOT(${metricList.map(mtr => s"$mtr == 0").mkString(" AND ")})")
			// 몽고디비 ObjectId 타입으로 변환
			.withColumn("publisher_id", toObjectId($"publisherId"))
			// 생성한 날짜
			.withColumn("createdAt", functions.current_timestamp)
			// MongoDB TTL Index 에 의해 삭제될 날짜 ( 1년 보관 )
			.withColumn("expiredAt", TimeUtil.getExpiredAtMonthlyAsColumn($"date"))
			// 불필요한 필드 제거
			.drop("publisherId")

		// logger.debug("finalDf schema >>>>>>")
		// finalDf.printSchema()
		// finalDf.show(100, truncate = false)
		// finalDf.explain("formatted")

		finalDf
	}
	type A = (Dataset[Row], String)

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		val (date, publisherId, adProviderIds) = param.get

		getFuturesForDelete(COLLECTION_NAME, date, publisherId, adProviderIds)(baseDao)
	}

	type T = (String, Option[String], Option[Seq[String]]) // ABSTRACT TYPE MEMBERS :: https://docs.scala-lang.org/tour/abstract-type-members.html

	/**
	 * 데이터 추가
	 *
	 * @param df
	 * @param writeParam
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		logger.debug(s"$LOG_PREFIX COLLECTION_NAME=$COLLECTION_NAME")

		val writeOptions = mdbDefaultWriteOptions
			.updated("spark.mongodb.output.collection", COLLECTION_NAME)
			// MongoDB 콜렉션의 샤드키를 의미하는게 아님. DataFrame 내에서의 유니크한 키를 의미함
			.updated("spark.mongodb.output.shardKey", "{ publisher_id:1, timezone:1, date:1, adProviderId:1, country:1 }")

		super.writeToMongoDB(df, writeOptions)
	}

	/**
	 * 캐시 제거
	 */
	def unpersist(): Unit = {
		if (intermediateCachedDf != null) intermediateCachedDf.unpersist()
	}
}
