package com.navercorp.gfp.biz.performance

import org.apache.spark.sql.Column

case class Metric(name: String, formula: Column, requiredMetric: Seq[String] = Seq())

case class FilterValue(filterType: String, value: List[String])

object FilterType extends Enumeration {
	val IN = "IN"
	val NOT_IN = "NOT_IN"
}

object FieldType extends Enumeration {
	/* Dimensions */
	val month = "month"
	val date = "date"
	val hour = "hour"
	val service = "service"
	val adUnit = "adUnit"
	val adProvider = "adProvider"
	val place = "place"
	val placeKey = "placeKey"
	val biddingGroup = "biddingGroup"
	val deal = "deal"
	val responseCreativeType = "responseCreativeType"
	val deviceOs = "deviceOs"
	val country = "country"
	val adProviderGroup = "adProviderGroup"

	/* Metrics */
	val impressions = "impressions"
	val clicks = "clicks"
	val ctr = "ctr"
	val krwRevenue = "krwRevenue"
	val usdRevenue = "usdRevenue"
	val krwNetRevenue = "krwNetRevenue"
	val usdNetRevenue = "usdNetRevenue"
	val krwCpm = "krwCpm"
	val usdCpm = "usdCpm"
	val krwCpc = "krwCpc"
	val usdCpc = "usdCpc"
	val krwNetCpm = "krwNetCpm"
	val usdNetCpm = "usdNetCpm"
	val krwNetCpc = "krwNetCpc"
	val usdNetCpc = "usdNetCpc"

	val adUnitRequests = "adUnitRequests"
	val adProviderRequests = "adProviderRequests"
	val adProviderResponses = "adProviderResponses"
	val sspFilledRequests = "sspFilledRequests"
	val sspImpressions = "sspImpressions"
	val sspViewableImpressions = "sspViewableImpressions"
	val sspEstimatedImpressions = "sspEstimatedImpressions"
	val sspClicks = "sspClicks"
	val sspCtr = "sspCtr"
	val sspCompletions = "sspCompletions"
	val sspEstimatedKrwNetRevenue = "sspEstimatedKrwNetRevenue"
	val sspEstimatedUsdNetRevenue = "sspEstimatedUsdNetRevenue"
	val sspEstimatedKrwNetCpm = "sspEstimatedKrwNetCpm"
	val sspEstimatedUsdNetCpm = "sspEstimatedUsdNetCpm"
	val sspEstimatedKrwNetCpc = "sspEstimatedKrwNetCpc"
	val sspEstimatedUsdNetCpc = "sspEstimatedUsdNetCpc"

	/* Filters */
	val publisherId = "publisherId"
	val serviceId = "serviceId"
	val adUnitId = "adUnitId"
	val adProviderId = "adProviderId"
	val dealId = "dealId"
	val biddingGroupId = "biddingGroupId"
	val placeChannelType = "placeChannelType"
	val placeCreativeType = "placeCreativeType"
	val placeProductType = "placeProductType"

	/* ETC */
	val dateTime = "dateTime"
	val adProviderPlaceId = "adProviderPlaceId"
	val serviceName = "serviceName"
	val adProviderName = "adProviderName"
	val connectionType = "connectionType"
	val timezone = "timezone"
	val currency = "currency"
	val placeName = "placeName"
	val biddingGroupName = "biddingGroupName"
	val dealName = "dealName"
	val countryName = "countryName"
}
