package com.navercorp.gfp.biz.performance.simple

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

import org.apache.logging.log4j.{LogManager, Logger}
import org.apache.spark.sql.functions.{date_format, expr, from_utc_timestamp, lit}
import org.apache.spark.sql.{Column, Dataset, Row, SparkSession}
import org.bson.types.ObjectId
import org.mongodb.scala.model.Filters

import com.navercorp.gfp.biz.performance.PerformanceDao
import com.navercorp.gfp.core.BaseDao


trait PerformanceDailyHelper {
	private val LOG_PREFIX = ".......... [PERFORMANCE-DAILY-HELPER]"
	private val logger: Logger = LogManager.getLogger(this.getClass)

	val performanceReportDao = new PerformanceDao()

	var countryCodes: Seq[String] = Seq()
	var timezones: Seq[String] = Seq("-")

	// country 는 aggregate 시 쓰이는데, countryCodes 는 init 할 때 생성 된다.
	// country 를 lazy 없이 쓰면, countryCodes 가 empty 이기 때문에, country 구문이 원하는 대로 생성 되지 않는다.
	// 물론 country 를 var 로 선언 하고, init 시점에 수정할 수는 있으나, 공통 쿼리 구문을 상단에 모아두고 싶어서 lazy 를 사용하였다.
	lazy val country = s"CASE WHEN country IN ( ${countryCodes.mkString("'", "','", "'")} ) THEN country ELSE 'OTHERS' END AS country"

	// metric rename 정보
	val METRICS_MAPPING: Map[String, String] = Map(
		"impressions" -> "apImpressions as impressions",
		"clicks" -> "apClicks as clicks",
		"usdRevenue" -> "apUsdSales as usdRevenue",
		"krwRevenue" -> "apKrwSales as krwRevenue",
		"usdNetRevenue" -> "apUsdProfit as usdNetRevenue",
		"krwNetRevenue" -> "apKrwProfit as krwNetRevenue",

		"adProviderRequests" -> "adProviderRequests",
		"adProviderResponses" -> "adProviderResponses",

		"sspFilledRequests" -> "gfpFilledRequests as sspFilledRequests",
		"sspImpressions" -> "gfpImpressions as sspImpressions",
		"sspViewableImpressions" -> "gfpViewableImpressions as sspViewableImpressions",
		"sspEstimatedImpressions" -> "gfpEstimatedImpressions as sspEstimatedImpressions",
		"sspClicks" -> "gfpClicks as sspClicks",
		"sspCompletions" -> "gfpCompletions as sspCompletions",
		"sspEstimatedUsdNetRevenue" -> "gfpEstimatedUsdProfit as sspEstimatedUsdNetRevenue",
		"sspEstimatedKrwNetRevenue" -> "gfpEstimatedKrwProfit as sspEstimatedKrwNetRevenue",
	)

	// aggList = [expr("SUM(impressions)").as("impressions"), expr("SUM(clicks)").as("clicks"), ... ]
	val metricList: Seq[String] = Seq(
		"impressions", "clicks", "usdRevenue", "krwRevenue", "usdNetRevenue", "krwNetRevenue",
		"adProviderRequests", "adProviderResponses",
		"sspFilledRequests", "sspImpressions", "sspViewableImpressions", "sspEstimatedImpressions", "sspClicks", "sspCompletions",
		"sspEstimatedUsdNetRevenue", "sspEstimatedKrwNetRevenue"
	)
	val aggList: Seq[Column] = metricList.map(metric => expr(s"SUM($metric)").as(metric))


	/**
	 * 집계에 필요한 정보 조회하기
	 *    - Countries 에서 countryCodes 정보 조회
	 *    - TimezoneMap 에서 timezones 정보 조회
	 */
	def initInfos(): Unit = {
		// Countries 에서 countryCodes 정보 조회
		countryCodes = performanceReportDao.getCountryCodes()

		logger.debug(s"$LOG_PREFIX countryCodes= ${countryCodes.toString()}")

		// TimezoneMap 에서 timezones 정보 조회
		timezones = timezones ++ performanceReportDao.getTimezones()

		logger.debug(s"$LOG_PREFIX timezones= ${timezones.toString()}")
	}

	/**
	 * 타임존별 date 컬럼 추가
	 */
	def addDateByTimezone(logDf: Dataset[Row], timezone: String)(implicit spark: SparkSession): Dataset[Row] = {
		import spark.implicits._

		if (timezone.equals("-"))
			// ap 타임존으로 집계 시
			logDf.withColumn("date", date_format($"apTimestamp", "yyyyMMdd"))
		else
			// 기준 타임존으로 집계 시
			logDf.withColumn("date", date_format(from_utc_timestamp($"utcTimestamp", timezone), "yyyyMMdd")) // UTC -> 다른 타임존으로 변환
	}

	/**
	 * 타임존별 일별 집계 df 생성
	 */
	def getDailyByTimezoneDf(df: Dataset[Row], timezones: Seq[String], date: String, dailySelectList: Seq[String], dailyGroupByList: Seq[String], dailyAggList: Seq[Column])(implicit spark: SparkSession): Dataset[Row] = {
		timezones.map { timezone =>
			addDateByTimezone(df, timezone)
				.filter(s"date == '$date'")
				.selectExpr(dailySelectList: _*)
				.groupBy(dailyGroupByList.head, dailyGroupByList.tail: _*)
				.agg(dailyAggList.head, dailyAggList.tail: _*)
				.withColumn("timezone", lit(timezone))
		}.reduce(_ unionAll _) // 타임존별 일별 집계 Union 처리
	}

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	def getFuturesForDelete(COLLECTION_NAME: String, date: String, publisherId: Option[String], adProviderIds: Option[Seq[String]])(baseDao: BaseDao): List[Future[Option[Boolean]]] = {
		logger.debug(s"$LOG_PREFIX date= $date, publisherId= ${publisherId.getOrElse("*")}, adProviderIds= ${adProviderIds.getOrElse("*")}")

		List(Future {
			var filters = Seq(Filters.eq("date", date))

			if (publisherId.isDefined) {
				filters = filters ++ Seq(Filters.eq("publisher_id", new ObjectId(publisherId.get)))
			}

			if (adProviderIds.isDefined) {
				filters = filters ++ Seq(Filters.in("adProviderId", adProviderIds.get: _*))
			}

			val filter = Filters.and(filters: _*)

			try {
				val start = System.currentTimeMillis()
				val deletedCount = baseDao.deleteStats(COLLECTION_NAME, filter)
				val end = System.currentTimeMillis()
				logger.debug(s"$LOG_PREFIX 삭제 성공 :: date= $date, publisherId= ${publisherId.getOrElse("*")}, adProviderIds= ${adProviderIds.getOrElse("*")} :: deletedCount: $deletedCount, 소요시간: ${end - start} ms")

				// 기존 데이터 삭제 성공 시, true 리턴
				Option(true)
			} catch {
				case t: Throwable =>
					logger.error(s"$LOG_PREFIX 삭제 실패 :: date= $date, publisherId= ${publisherId.getOrElse("*")}, adProviderIds= ${adProviderIds.getOrElse("*")}")
					logger.error(t.getMessage, t)

					// 기존 데이터 삭제 실패 시, false 리턴
					Option(false)
			}
		})
	}
}
