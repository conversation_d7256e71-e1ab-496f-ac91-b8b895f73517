package com.navercorp.gfp.biz.performance

import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import org.apache.logging.log4j.{LogManager, Logger}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{DataType, LongType, StringType}
import org.apache.spark.sql.{Column, Dataset, Row, SparkSession}

import com.navercorp.gfp.core.BaseConstant.DECIMAL_TYPE_17_6
import com.navercorp.gfp.core.BaseEnv
import com.navercorp.gfp.meta.customreport.CustomReportFilter

trait PerformanceHelper {
	private val LOG_PREFIX = ".......... [PERFORMANCE-HELPER]"
	private val logger: Logger = LogManager.getLogger(this.getClass)

	implicit val spark: SparkSession

	import spark.implicits._

	val COALESCE_CNT = 50

	val performanceReportDao = new PerformanceDao()

	/* dimension */
	// # 공통
	// dimension : date, service, adProvider, adUnit, place, biddingGroup, deal, responseCreativeType, deviceOs, country
	// 	- service -> svc.name
	// 	- adProvider -> ap.name, ap.connectionType, ap.timezone, ap.currency
	// 	- place -> pl.name, pl.channelType, pl.productType, pl.creativeType
	// 	- biddingGroup -> bg.name
	// 	- deal -> dl.name
	//
	// # 매체성과
	// dimension : placeKey
	// 	- 필수 Dimension : date, service, adProvider
	//
	// # 맞춤성과
	// dimension : month, hour, adProviderGroup
	//  - adProvider -> ap.adProviderGroup
	// 	- place 선택 시, placeKey 자동 추가
	// 	- GFP 매체인 경우, adProviderGroup 미포함 (API 단에서 처리)

	/* metrics */
	// # 공통
	// AP metrics : impressions, clicks, krwRevenue, usdRevenue, krwNetRevenue, usdNetRevenue
	// 	- impressions = apImpressions
	// 	- clicks = apClicks
	// 	- krwRevenue = apKrwSales
	// 	- usdRevenue = apUsdSales
	// 	- krwNetRevenue = apKrwProfit
	// 	- usdNetRevenue = apUsdProfit
	// AP Calculated : ctr, krwNetCpm, usdNetCpm, krwNetCpc, usdNetCpc
	// 	- ctr = (clicks / impressions) * 100
	// 	- krwNetCpm = (krwNetRevenue / impressions) * 1000
	// 	- usdNetCpm = (usdNetRevenue / impressions) * 1000
	// 	- krwNetCpc = krwNetRevenue / clicks
	// 	- usdNetCpc = usdNetRevenue / clicks
	// SSP metrics : adUnitRequests, adProviderRequests, adProviderResponses, sspFilledRequests, sspImpressions, sspViewableImpressions, sspEstimatedImpressions, sspClicks, sspCompletions, sspEstimatedKrwNetRevenue, sspEstimatedUsdNetRevenue
	// 	- sspFilledRequests = gfpFilledRequests
	// 	- sspImpressions = gfpImpressions
	// 	- sspViewableImpressions = gfpViewableImpressions
	// 	- sspEstimatedImpressions = gfpEstimatedImpressions
	// 	- sspClicks = gfpClicks
	// 	- sspCompletions = gfpCompletions
	// 	- sspEstimatedKrwNetRevenue = gfpEstimatedKrwProfit
	// 	- sspEstimatedUsdNetRevenue = gfpEstimatedUsdProfit
	// SSP Calculated : sspCtr, sspEstimatedKrwNetCpm, sspEstimatedUsdNetCpm, sspEstimatedKrwNetCpc, sspEstimatedUsdNetCpc
	// 	- sspCtr = (sspClicks / sspImpressions) * 100
	// 	- sspEstimatedKrwNetCpm = (sspEstimatedKrwNetRevenue / sspImpressions) * 1000
	// 	- sspEstimatedUsdNetCpm = (sspEstimatedUsdNetRevenue / sspImpressions) * 1000
	// 	- sspEstimatedKrwNetCpc = sspEstimatedKrwNetRevenue / sspClicks
	// 	- sspEstimatedUsdNetCpc = sspEstimatedUsdNetRevenue / sspClicks
	//
	// # 맞춤성과
	// AP Calculated : krwCpm, usdCpm, krwCpc, usdCpc
	// 	- krwCpm = (krwRevenue / impressions) * 1000
	// 	- usdCpm = (usdRevenue / impressions) * 1000
	// 	- krwCpc = krwRevenue / clicks
	// 	- usdCpc = usdRevenue / clicks
	//
	// # 매체성과 & 맞춤성과
	// NAM 매체인 경우, 추정 지표 미포함 (API 단에서 처리)

	// Union, Join 시, 양쪽 DF 스키마를 맞추기 위한 용도
	private val SCHEMA_MAPPING: Map[String, DataType] = Map(
		FieldType.dateTime -> StringType,
		FieldType.serviceId -> StringType,
		FieldType.adUnitId -> StringType,
		FieldType.adProviderId -> StringType,
		FieldType.adProviderPlaceId -> StringType,
		FieldType.placeKey -> StringType,
		FieldType.biddingGroupId -> StringType,
		FieldType.dealId -> StringType,
		FieldType.responseCreativeType -> StringType,
		FieldType.deviceOs -> StringType,
		FieldType.country -> StringType,

		FieldType.impressions -> LongType,
		FieldType.clicks -> LongType,
		FieldType.usdRevenue -> DECIMAL_TYPE_17_6,
		FieldType.krwRevenue -> DECIMAL_TYPE_17_6,
		FieldType.usdNetRevenue -> DECIMAL_TYPE_17_6,
		FieldType.krwNetRevenue -> DECIMAL_TYPE_17_6,
		FieldType.ctr -> DECIMAL_TYPE_17_6,
		FieldType.krwCpm -> DECIMAL_TYPE_17_6,
		FieldType.usdCpm -> DECIMAL_TYPE_17_6,
		FieldType.krwCpc -> DECIMAL_TYPE_17_6,
		FieldType.usdCpc -> DECIMAL_TYPE_17_6,
		FieldType.usdNetCpm -> DECIMAL_TYPE_17_6,
		FieldType.krwNetCpm -> DECIMAL_TYPE_17_6,
		FieldType.usdNetCpc -> DECIMAL_TYPE_17_6,
		FieldType.krwNetCpc -> DECIMAL_TYPE_17_6,

		FieldType.adUnitRequests -> LongType,
		FieldType.adProviderRequests -> LongType,
		FieldType.adProviderResponses -> LongType,
		FieldType.sspFilledRequests -> LongType,
		FieldType.sspImpressions -> LongType,
		FieldType.sspViewableImpressions -> LongType,
		FieldType.sspClicks -> LongType,
		FieldType.sspCompletions -> LongType,
		FieldType.sspCtr -> DECIMAL_TYPE_17_6,
		FieldType.sspEstimatedImpressions -> LongType,
		FieldType.sspEstimatedUsdNetRevenue -> DECIMAL_TYPE_17_6,
		FieldType.sspEstimatedKrwNetRevenue -> DECIMAL_TYPE_17_6,
		FieldType.sspEstimatedUsdNetCpm -> DECIMAL_TYPE_17_6,
		FieldType.sspEstimatedKrwNetCpm -> DECIMAL_TYPE_17_6,
		FieldType.sspEstimatedUsdNetCpc -> DECIMAL_TYPE_17_6,
		FieldType.sspEstimatedKrwNetCpc -> DECIMAL_TYPE_17_6
	)

	// 공통 metrics
	val COMMON_METRICS: Seq[String] = Seq(
		FieldType.impressions, FieldType.clicks,
		FieldType.usdRevenue, FieldType.krwRevenue, FieldType.usdNetRevenue, FieldType.krwNetRevenue,
		FieldType.ctr, FieldType.usdNetCpm, FieldType.krwNetCpm, FieldType.usdNetCpc, FieldType.krwNetCpc,

		FieldType.adUnitRequests, FieldType.adProviderRequests, FieldType.adProviderResponses,
		FieldType.sspFilledRequests, FieldType.sspImpressions, FieldType.sspViewableImpressions, FieldType.sspClicks, FieldType.sspCompletions,
		FieldType.sspCtr,
	)

	// 추정 metrics
	val ESTIMATED_METRICS: Seq[String] = Seq(
		FieldType.sspEstimatedImpressions,
		FieldType.sspEstimatedUsdNetRevenue, FieldType.sspEstimatedKrwNetRevenue,
		FieldType.sspEstimatedUsdNetCpm, FieldType.sspEstimatedKrwNetCpm, FieldType.sspEstimatedUsdNetCpc, FieldType.sspEstimatedKrwNetCpc
	)

	// metric 별 selectExpr 매핑 정보
	private val SELECT_METRICS_MAPPING: Map[String, String] = Map(
		FieldType.impressions -> s"apImpressions as ${FieldType.impressions}",
		FieldType.clicks -> s"apClicks as ${FieldType.clicks}",
		FieldType.usdRevenue -> s"apUsdSales as ${FieldType.usdRevenue}",
		FieldType.krwRevenue -> s"apKrwSales as ${FieldType.krwRevenue}",
		FieldType.usdNetRevenue -> s"apUsdProfit as ${FieldType.usdNetRevenue}",
		FieldType.krwNetRevenue -> s"apKrwProfit as ${FieldType.krwNetRevenue}",

		FieldType.adProviderRequests -> FieldType.adProviderRequests,
		FieldType.adProviderResponses -> FieldType.adProviderResponses,
		FieldType.sspFilledRequests -> s"gfpFilledRequests as ${FieldType.sspFilledRequests}",
		FieldType.sspImpressions -> s"gfpImpressions as ${FieldType.sspImpressions}",
		FieldType.sspViewableImpressions -> s"gfpViewableImpressions as ${FieldType.sspViewableImpressions}",
		FieldType.sspEstimatedImpressions -> s"gfpEstimatedImpressions as ${FieldType.sspEstimatedImpressions}",
		FieldType.sspClicks -> s"gfpClicks as ${FieldType.sspClicks}",
		FieldType.sspCompletions -> s"gfpCompletions as ${FieldType.sspCompletions}",
		FieldType.sspEstimatedUsdNetRevenue -> s"gfpEstimatedUsdProfit as ${FieldType.sspEstimatedUsdNetRevenue}",
		FieldType.sspEstimatedKrwNetRevenue -> s"gfpEstimatedKrwProfit as ${FieldType.sspEstimatedKrwNetRevenue}",
	)

	// dimension 별 groupBy 매핑 정보
	// 	- 참고사항 :: adProviderGroup 은 메타 정보이므로 dimensions 에서 제외됨
	private val GROUP_BY_DIMENSIONS_MAPPING: Map[String, String] = Map(
		FieldType.month -> FieldType.dateTime,
		FieldType.date -> FieldType.dateTime,
		FieldType.hour -> FieldType.dateTime,
		FieldType.service -> FieldType.serviceId,
		FieldType.adUnit -> FieldType.adUnitId,
		FieldType.adProvider -> FieldType.adProviderId,
		FieldType.place -> FieldType.adProviderPlaceId,
		FieldType.placeKey -> FieldType.placeKey,
		FieldType.biddingGroup -> FieldType.biddingGroupId,
		FieldType.deal -> FieldType.dealId,
		FieldType.responseCreativeType -> FieldType.responseCreativeType,
		FieldType.deviceOs -> FieldType.deviceOs,
		FieldType.country -> FieldType.country,
	)

	// 광고 유닛 요청수 Distinct 기준 항목 정보
	// 	- 지르콘 Distinct 기준 항목 : seoulTimestamp(utcTimestamp), publisherId, adUnitId, deviceOs, country, adUnitRequests
	private val AUR_DISTINCT_FIELDS: Seq[String] = Seq("utcTimestamp", FieldType.serviceId, FieldType.adUnitId, FieldType.deviceOs, FieldType.country, FieldType.adUnitRequests)

	// 광고 유닛 요청수 집계를 위한 dimensions 정보
	private val AUR_DIMENSIONS: Seq[String] = Seq(FieldType.dateTime, FieldType.serviceId, FieldType.adUnitId, FieldType.deviceOs, FieldType.country)

	// metric 별 계산식 매핑 정보 (소수점 7째 자리에서 반올림)
	private val CALCULATED_METRICS_MAPPING: Map[String, Metric] = Map(
		// 계산 지표 -> (지표명, 계산식, 계산에 필요한 다른 지표)
		FieldType.ctr -> Metric(FieldType.ctr, when(col(FieldType.impressions) > 0, ((col(FieldType.clicks) / col(FieldType.impressions)) * 100).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.clicks, FieldType.impressions)),
		FieldType.krwNetCpm -> Metric(FieldType.krwNetCpm, when(col(FieldType.impressions) > 0, ((col(FieldType.krwNetRevenue) / col(FieldType.impressions)) * 1000).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.krwNetRevenue, FieldType.impressions)),
		FieldType.usdNetCpm -> Metric(FieldType.usdNetCpm, when(col(FieldType.impressions) > 0, ((col(FieldType.usdNetRevenue) / col(FieldType.impressions)) * 1000).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.usdNetRevenue, FieldType.impressions)),
		FieldType.krwNetCpc -> Metric(FieldType.krwNetCpc, when(col(FieldType.clicks) > 0, (col(FieldType.krwNetRevenue) / col(FieldType.clicks)).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.krwNetRevenue, FieldType.clicks)),
		FieldType.usdNetCpc -> Metric(FieldType.usdNetCpc, when(col(FieldType.clicks) > 0, (col(FieldType.usdNetRevenue) / col(FieldType.clicks)).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.usdNetRevenue, FieldType.clicks)),
		FieldType.krwCpm -> Metric(FieldType.krwCpm, when(col(FieldType.impressions) > 0, ((col(FieldType.krwRevenue) / col(FieldType.impressions)) * 1000).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.krwRevenue, FieldType.impressions)),
		FieldType.usdCpm -> Metric(FieldType.usdCpm, when(col(FieldType.impressions) > 0, ((col(FieldType.usdRevenue) / col(FieldType.impressions)) * 1000).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.usdRevenue, FieldType.impressions)),
		FieldType.krwCpc -> Metric(FieldType.krwCpc, when(col(FieldType.clicks) > 0, (col(FieldType.krwRevenue) / col(FieldType.clicks)).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.krwRevenue, FieldType.clicks)),
		FieldType.usdCpc -> Metric(FieldType.usdCpc, when(col(FieldType.clicks) > 0, (col(FieldType.usdRevenue) / col(FieldType.clicks)).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.usdRevenue, FieldType.clicks)),

		FieldType.sspCtr -> Metric(FieldType.sspCtr, when(col(FieldType.sspImpressions) > 0, ((col(FieldType.sspClicks) / col(FieldType.sspImpressions)) * 100).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.sspClicks, FieldType.sspImpressions)),
		FieldType.sspEstimatedKrwNetCpm -> Metric(FieldType.sspEstimatedKrwNetCpm, when(col(FieldType.sspImpressions) > 0, ((col(FieldType.sspEstimatedKrwNetRevenue) / col(FieldType.sspImpressions)) * 1000).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.sspEstimatedKrwNetRevenue, FieldType.sspImpressions)),
		FieldType.sspEstimatedUsdNetCpm -> Metric(FieldType.sspEstimatedUsdNetCpm, when(col(FieldType.sspImpressions) > 0, ((col(FieldType.sspEstimatedUsdNetRevenue) / col(FieldType.sspImpressions)) * 1000).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.sspEstimatedUsdNetRevenue, FieldType.sspImpressions)),
		FieldType.sspEstimatedKrwNetCpc -> Metric(FieldType.sspEstimatedKrwNetCpc, when(col(FieldType.sspClicks) > 0, (col(FieldType.sspEstimatedKrwNetRevenue) / col(FieldType.sspClicks)).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.sspEstimatedKrwNetRevenue, FieldType.sspClicks)),
		FieldType.sspEstimatedUsdNetCpc -> Metric(FieldType.sspEstimatedUsdNetCpc, when(col(FieldType.sspClicks) > 0, (col(FieldType.sspEstimatedUsdNetRevenue) / col(FieldType.sspClicks)).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq(FieldType.sspEstimatedUsdNetRevenue, FieldType.sspClicks)),
	)

	// dimensions 별 메타 매핑 정보
	private val META_DIMENSIONS_MAPPING: Map[String, Seq[String]] = Map(
		FieldType.serviceId -> Seq(FieldType.serviceName),
		FieldType.adProviderId -> Seq(FieldType.adProviderName, FieldType.connectionType, FieldType.timezone, FieldType.currency),
		FieldType.adProviderPlaceId -> Seq(FieldType.placeName, FieldType.placeChannelType, FieldType.placeProductType, FieldType.placeCreativeType),
		FieldType.biddingGroupId -> Seq(FieldType.biddingGroupName),
		FieldType.dealId -> Seq(FieldType.dealName),
		FieldType.country -> Seq(FieldType.countryName),
	)

	// [CSV] 항목별 헤더명 및 순서 정보
	private val HEADERS_ORDER: Seq[(String, String)] = Seq(
		(FieldType.dateTime, "DateTime"),

		(FieldType.serviceName, "Service"),

		(FieldType.adUnitId, "Ad Unit"),

		(FieldType.adProviderGroup, "Ad Provider Group"),
		(FieldType.adProviderName, "Ad Provider"),
		(FieldType.connectionType, "Ad Provider Connection Type"),
		(FieldType.timezone, "Ad Provider Time Zone"),
		(FieldType.currency, "Ad Provider Currency"),

		(FieldType.placeName, "Place"),
		(FieldType.placeChannelType, "Place Channel Type"),
		(FieldType.placeProductType, "Place Product Type"),
		(FieldType.placeCreativeType, "Place Creative Type"),
		(FieldType.placeKey, "Place Key"),

		(FieldType.biddingGroupName, "Bidding Group"),
		(FieldType.dealName, "Deal"),
		(FieldType.responseCreativeType, "Response Creative Type"),
		(FieldType.deviceOs, "Device OS"),
		(FieldType.countryName, "Country"),

		// AP Metric
		(FieldType.impressions, "Impressions"),
		(FieldType.clicks, "Clicks"),
		(FieldType.ctr, "CTR"),
		(FieldType.krwRevenue, "KRW Revenue"),
		(FieldType.usdRevenue, "USD Revenue"),
		(FieldType.krwNetRevenue, "KRW Net Revenue"),
		(FieldType.usdNetRevenue, "USD Net Revenue"),
		(FieldType.krwCpm, "KRW CPM"),
		(FieldType.usdCpm, "USD CPM"),
		(FieldType.krwCpc, "KRW CPC"),
		(FieldType.usdCpc, "USD CPC"),
		(FieldType.krwNetCpm, "KRW Net CPM"),
		(FieldType.usdNetCpm, "USD Net CPM"),
		(FieldType.krwNetCpc, "KRW Net CPC"),
		(FieldType.usdNetCpc, "USD Net CPC"),

		// GFP Metric
		(FieldType.adUnitRequests, "Ad Unit Requests"),
		(FieldType.adProviderRequests, "Ad Provider Requests"),
		(FieldType.adProviderResponses, "Ad Provider Responses"),
		(FieldType.sspFilledRequests, "SSP Filled Requests"),
		(FieldType.sspImpressions, "SSP Impressions"),
		(FieldType.sspViewableImpressions, "SSP Viewable Impressions"),
		(FieldType.sspEstimatedImpressions, "SSP Estimated Impressions"),
		(FieldType.sspClicks, "SSP Clicks"),
		(FieldType.sspCtr, "SSP CTR"),
		(FieldType.sspCompletions, "SSP Completions"),
		(FieldType.sspEstimatedKrwNetRevenue, "SSP Estimated KRW Net Revenue"),
		(FieldType.sspEstimatedUsdNetRevenue, "SSP Estimated USD Net Revenue"),
		(FieldType.sspEstimatedKrwNetCpm, "SSP Estimated KRW Net CPM"),
		(FieldType.sspEstimatedUsdNetCpm, "SSP Estimated USD Net CPM"),
		(FieldType.sspEstimatedKrwNetCpc, "SSP Estimated KRW Net CPC"),
		(FieldType.sspEstimatedUsdNetCpc, "SSP Estimated USD Net CPC"),
	)

	private val MONTHLY_FORMAT = "yyyy-MM"
	private val DAILY_FORMAT = "yyyy-MM-dd"
	private val HOURLY_FORMAT = "yyyy-MM-dd HH"

	/* 집계 시, 사용할 필드 리스트 */
	// metricList, aggList 는 adUnitRequests 를 제외한 나머지 지표만 포함 된다.
	// dimensions = ["date", "service", "adProvider", "adUnit", "place", "placeKey"]
	// metrics = ["impressions", "clicks", "krwRevenue", "usdNetCpm", "adUnitRequests"]
	// 	- metricList = ["impressions", "clicks", "krwRevenue", "usdNetRevenue"]
	// 	- groupByList = ["dateTime", "serviceId", "adProviderId", "adUnitId", "adProviderPlaceId", "placeKey"]
	// 	- aggList = [expr(s"NVL(SUM(impressions), 0)").as("impressions"),
	// 				expr(s"NVL(SUM(clicks), 0)").as("clicks"),
	// 				expr(s"NVL(SUM(krwRevenue), 0)").as("krwRevenue"),
	// 				expr(s"NVL(SUM(usdNetRevenue), 0)").as("usdNetRevenue")]
	// 	- selectList = ["dateTime", "serviceId", "adProviderId", "adUnitId", "adProviderPlaceId",
	// 					"CASE WHEN connectionType == 'C2S' THEN placeKey ELSE '-' END AS placeKey",
	// 					"apImpressions as impressions", "apClicks as clicks", "apKrwSales as krwRevenue", "apUsdProfit as usdNetRevenue"]
	// 	- calculatedMetricList = [Some(Metric("usdNetCpm", when($"impressions" > 0, (($"usdNetRevenue" / $"impressions") * 1000).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq("usdNetRevenue", "impressions") ))]
	// 	- aurBaseList = ["dateTime", "serviceId", "adUnitId"]
	// 	- finalFieldList = ["dateTime", "serviceName", "adProviderName", "connectionType", "timezone", "currency", "adUnitId",
	// 						"placeName", "placeChannelType", "placeProductType", "placeCreativeType", "placeKey",
	// 						"impressions", "clicks", "krwRevenue", "usdNetCpm"]
	// 	- finalHeaderList = [("dateTime", "DateTime"), ("serviceName", "Service"), ("adUnitId", "AdUnit"),
	// 						("adProviderName", "AdProvider"), ("connectionType", "AP Connection Type"), ("timezone", "AP Time Zone"), ("currency", "AP Currency"),
	// 						("placeName", "Place"), ("placeChannelType", "Place Channel Type"), ("placeProductType", "Place Product Type"), ("placeCreativeType", "Place Creative Type"),
	// 						("placeKey", "Place Key"),
	// 						("impressions", "Impressions"), ("clicks", "Clicks"), ("krwRevenue", "KRW Revenue"), ("usdNetCpm", "USD Net CPM")]
	private var metricList: Seq[String] = Seq()
	private var groupByList: Seq[String] = Seq()
	lazy val aggList: Seq[Column] = metricList.map(metric => expr(s"SUM($metric)").as(metric))
	lazy val selectList: Seq[String] = groupByList.map {
		case FieldType.placeKey => s"CASE WHEN ${FieldType.connectionType} == 'C2S' THEN ${FieldType.placeKey} ELSE '-' END AS ${FieldType.placeKey}"
		case others => others
	} ++ metricList.map(SELECT_METRICS_MAPPING)
	private var calculatedMetricList: Seq[Option[Metric]] = Seq()
	private var aurBaseList: Seq[String] = Seq()
	private var aurGroupByList: Seq[String] = Seq()
	var finalFieldList: Seq[String] = Seq()
	lazy val finalHeaderList: Seq[(String, String)] = HEADERS_ORDER.filter(el => finalFieldList.contains(el._1))

	private val aurAgg: Column = expr(s"SUM(${FieldType.adUnitRequests})").as(FieldType.adUnitRequests)

	// 광고 유닛 요청수 집계를 위한 타임존 목록
	private var aurTimezones: Seq[String] = Seq()

	// place 필터 적용 여부 ( placeChannelType, placeCreativeType, placeProductType 필터 조건이 하나라도 있는 경우, true )
	private var usePlaceFilter = false

	// 필터할 placeId 목록
	var filteredPlaceIds: Seq[String] = Seq()

	// 날짜 포맷 ( 디폴트 : 일별 )
	private var dateTimeFormat: String = DAILY_FORMAT

	// AP 타임존 적용 여부
	private var useApTimezone = true

	// 광고 유닛 요청수 여부
	private var useAdUnitRequests = false

	// 계산 지표 사용 여부
	private var useCalculatedMetric = false

	// 메타 추가 여부
	var usePlaceMeta = false
	var useBiddingGroupMeta = false
	var useDealMeta = false
	var useCountryMeta = false

	private var cachedDfList: Seq[Dataset[Row]] = Seq()


	/* 추상 메서드 */
	def addMeta(df: Dataset[Row]): Dataset[Row]

	def applyBaseFilters(df: Dataset[Row]): Dataset[Row]
	def applyFilters(df: Dataset[Row]): Dataset[Row]

	def unPersist(): Unit


	/**
	 * 초기화 설정
	 * 	- dimensions : month, date, hour, service, adProvider, adUnit, place, placeKey, biddingGroup, deal, responseCreativeType, country, deviceOs
	 * 	- metrics
	 * 		- impressions, clicks, krwRevenue, usdRevenue, krwNetRevenue, usdNetRevenue, ctr, krwCpm, usdCpm, krwCpc, usdCpc, krwNetCpm, usdNetCpm, krwNetCpc, usdNetCpc
	 * 		- adUnitRequests, adProviderRequests, adProviderResponses, sspFilledRequests, sspImpressions, sspViewableImpressions, sspEstimatedImpressions, sspClicks, sspCtr, sspCompletions
	 * 		- sspEstimatedKrwNetRevenue, sspEstimatedUsdNetRevenue, sspEstimatedKrwNetCpm, sspEstimatedUsdNetCpm, sspEstimatedKrwNetCpc, sspEstimatedUsdNetCpc
	 *
	 * 1. 공통 필드 리스트 초기화
	 * 	- metricList, aggList, groupByList, selectList, calculatedMetricList, aurBaseList, finalFieldList, finalHeaderList
	 *
	 * 2. 공통 변수 초기화
	 * 	- dateTimeFormat, useApTimezone, useAdUnitRequests, useCalculatedMetric, usePlaceMeta, useBiddingGroupMeta, useDealMeta, useCountryMeta
	 *
	 * 3. placeChannelType, placeCreativeType, placeProductType 필터 조건 초기화
	 * 	- placeChannelType, placeCreativeType, placeProductType 필터 조건에 해당 하는 filteredPlaceIds 조회 ( 필터 조건이 없는 경우, 전체 목록 조회 )
	 * 	- placeChannelType, placeCreativeType, placeProductType 필터 조건이 하나도 없는 경우, usePlaceFilter = false
	 * 	- placeMeta 필터 시, 사용
	 *
	 * 4. 광고 유닛 요청수 집계를 위한 타임존 목록 조회
	 * 	- AP 타임존인 경우, 매체와 연동 중인 AP 들의 타임존 목록을 구함
	 * 	- 고정 타임존인 경우, 해당 타임존으로 설정
	 */
	def init(publisherId: String, timezone: String, dimensions: Seq[String], metrics: Seq[String], filters: Map[String, CustomReportFilter]): Unit = {
		/* 1. 공통 필드 리스트 초기화 */
		// dimensions = ["date", "service", "adProvider", "adUnit", "place", "placeKey"]
		// metrics = ["impressions", "clicks", "krwRevenue", "usdNetCpm", "adUnitRequests"]
		// 	- metricList = ["impressions", "clicks", "krwRevenue", "usdNetRevenue"]
		// 	- groupByList = ["dateTime", "serviceId", "adProviderId", "adUnitId", "adProviderPlaceId", "placeKey"]
		// 	- calculatedMetricList = [Some(Metric("usdNetCpm", when($"impressions" > 0, (($"usdNetRevenue" / $"impressions") * 1000).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq("usdNetRevenue", "impressions") ))]
		// 	- aurBaseList = ["dateTime", "serviceId", "adUnitId"]
		// 	- finalFieldList = ["dateTime", "serviceName", "adProviderName", "connectionType", "timezone", "currency", "adUnitId",
		// 						"placeName", "placeChannelType", "placeProductType", "placeCreativeType", "placeKey",
		// 						"impressions", "clicks", "krwRevenue", "usdNetCpm", "adUnitRequests"]
		metricList = metrics.filter(_ != FieldType.adUnitRequests).flatMap { mtr =>
			// 계산 지표가 아닌 경우
			if (!CALCULATED_METRICS_MAPPING.keys.toSeq.contains(mtr)) {
				Seq(mtr)
			} else {
				CALCULATED_METRICS_MAPPING(mtr).requiredMetric
			}
		}.distinct
		groupByList = dimensions.map(GROUP_BY_DIMENSIONS_MAPPING)
		calculatedMetricList = metrics.map(mtr => CALCULATED_METRICS_MAPPING.get(mtr)).filter(_.nonEmpty)
		aurBaseList = groupByList.filter(AUR_DIMENSIONS.contains)
		aurGroupByList = aurBaseList ++ Seq(FieldType.timezone)
		finalFieldList = groupByList.flatMap(dim => META_DIMENSIONS_MAPPING.getOrElse(dim, Seq(dim))) ++ metrics

		logger.debug(s"$LOG_PREFIX metricList= $metricList")
		logger.debug(s"$LOG_PREFIX groupByList= $groupByList")
		logger.debug(s"$LOG_PREFIX aggList= $aggList")
		logger.debug(s"$LOG_PREFIX selectList= $selectList")
		logger.debug(s"$LOG_PREFIX calculatedMetricList= $calculatedMetricList")
		logger.debug(s"$LOG_PREFIX aurBaseList= $aurBaseList")
		logger.debug(s"$LOG_PREFIX aurGroupByList= $aurGroupByList")


		/* 2. 공통 변수 초기화 */
		// 날짜 포맷 설정
		dateTimeFormat = dimensions.intersect(Seq(FieldType.month, FieldType.date, FieldType.hour)).map {
			case FieldType.hour => HOURLY_FORMAT
			case FieldType.date => DAILY_FORMAT
			case FieldType.month => MONTHLY_FORMAT
			case _ => DAILY_FORMAT
		}.head

		// AP 타임존 사용 여부
		useApTimezone = timezone.equals("-")

		// 광고 유닛 요청수 여부
		useAdUnitRequests = metrics.indexOf(FieldType.adUnitRequests) > -1

		// 계산 지표 사용 여부
		useCalculatedMetric = calculatedMetricList.nonEmpty

		// 광고공급단위, 비딩그룹, 거래, 국가 메타 추가 여부
		usePlaceMeta = dimensions.contains(FieldType.place)
		useBiddingGroupMeta = dimensions.contains(FieldType.biddingGroup)
		useDealMeta = dimensions.contains(FieldType.deal)
		useCountryMeta = dimensions.contains(FieldType.country)

		logger.debug(s"$LOG_PREFIX dateTimeFormat= $dateTimeFormat")
		logger.debug(s"$LOG_PREFIX useApTimezone= $useApTimezone")
		logger.debug(s"$LOG_PREFIX useAdUnitRequests= $useAdUnitRequests")
		logger.debug(s"$LOG_PREFIX useCalculatedMetric= $useCalculatedMetric")
		logger.debug(s"$LOG_PREFIX usePlaceMeta= $usePlaceMeta")
		logger.debug(s"$LOG_PREFIX useBiddingGroupMeta= $useBiddingGroupMeta")
		logger.debug(s"$LOG_PREFIX useDealMeta= $useDealMeta")
		logger.debug(s"$LOG_PREFIX useCountryMeta= $useCountryMeta")


		/* 3. placeChannelType, placeCreativeType, placeProductType 필터 조건 초기화 */
		// 	- placeChannelType, placeCreativeType, placeProductType 필터 조건에 해당 하는 filteredPlaceIds 조회 ( 필터 조건이 없는 경우, 전체 목록 조회 )
		// 	- placeChannelType, placeCreativeType, placeProductType 필터 조건이 하나도 없는 경우, usePlaceFilter = false
		// 	- placeMeta 필터 시, 사용
		val placeChannelType = getFilterValue(filters, FieldType.placeChannelType)
		val placeCreativeType = getFilterValue(filters, FieldType.placeCreativeType)
		val placeProductType = getFilterValue(filters, FieldType.placeProductType)
		usePlaceFilter = placeChannelType.nonEmpty || placeCreativeType.nonEmpty || placeProductType.nonEmpty
		filteredPlaceIds = performanceReportDao.getPlaceIds(publisherId, placeChannelType, placeCreativeType, placeProductType)

		logger.debug(s"$LOG_PREFIX usePlaceFilter= $usePlaceFilter")
		logger.debug(s"$LOG_PREFIX filterPlaceIds= ${filteredPlaceIds.size}")


		/* 4. 광고 유닛 요청수 집계를 위한 타임존 목록 조회 */
		// 	- AP 타임존인 경우, 매체와 연동 중인 AP 들의 타임존 목록을 구함
		// 	- 고정 타임존인 경우, 해당 타임존으로 설정
		if (useAdUnitRequests) {
			aurTimezones = if (useApTimezone) performanceReportDao.getApTimezones(publisherId) else Seq(timezone)

			logger.debug(s"$LOG_PREFIX aurTimezones= ${aurTimezones.toString}")
		}
	}

	/**
	 * key 에 해당 하는 FilterValue 반환
	 * 	- ex> FilterValue(FilterType.IN, Seq("xxx", "xxx"))
	 */
	def getFilterValue(filters: Map[String, CustomReportFilter], key: String): Option[FilterValue] = {
		filters.get(key).flatMap { condition =>
			condition.in.map(inList => FilterValue(FilterType.IN, inList)).orElse(condition.notIn.map(notInList => FilterValue(FilterType.NOT_IN, notInList)))
		}
	}

	/**
	 * key 별 필터 적용
	 * 	- key 에 해당 하는 FilterValue 를 같이 전달
	 */
	def getFilteredDf(df: Dataset[Row], key: String, filterValue: Option[FilterValue]): Dataset[Row] = {
		if (filterValue.nonEmpty) {
			logger.debug(s"$LOG_PREFIX $key 필터 적용")

			val FilterValue(filterType, value) = filterValue.get
			val condition = if (filterType.equals(FilterType.IN)) col(key).isin(value: _*) else not(col(key).isin(value: _*))

			df.filter(condition)
		} else df
	}

	/**
	 * 공통 기본 필터 적용
	 * 	- 서비스, 디바이스, 국가
	 */
	def applyCommonBaseFilters(df: Dataset[Row], filters: Map[String, CustomReportFilter]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 공통 기본 필터 적용 (서비스, 디바이스, 국가)")

		// 서비스 필터 적용
		val filteredDf1 = getFilteredDf(df, FieldType.serviceId, getFilterValue(filters, FieldType.serviceId))

		// 디바이스 필터 적용
		val filteredDf2 = getFilteredDf(filteredDf1, FieldType.deviceOs, getFilterValue(filters, FieldType.deviceOs))

		// 국가 필터 적용
		val filteredDf3 = getFilteredDf(filteredDf2, FieldType.country, getFilterValue(filters, FieldType.country))

		filteredDf3
	}

	/**
	 * 공통 필터 적용
	 * 	- 광고공급단위, 응답소재유형
	 * 	- 광고공급단위는 항상 in 필터 적용됨
	 */
	def applyCommonFilters(df: Dataset[Row], filters: Map[String, CustomReportFilter]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 공통 필터 적용 (광고공급단위, 응답소재유형)")
		logger.debug(s"$LOG_PREFIX adProviderPlaceId 필터 적용")

		// 광고공급단위 필터 적용
		val filteredDf0 = if (usePlaceFilter) df.filter(col(FieldType.adProviderPlaceId).isin(filteredPlaceIds: _*)) else df

		// 응답소재유형 필터 적용
		val filteredDf1 = getFilteredDf(filteredDf0, FieldType.responseCreativeType, getFilterValue(filters, FieldType.responseCreativeType))

		filteredDf1
	}

	/**
	 * 타임존 기준 dateTime 추가
	 * 	- 포맷 : 월 (yyyy-MM), 일 (yyyy-MM-dd), 시간 (yyyy-MM-dd HH)
	 * 	- 조회 기간에 해당 하는 날짜로 필터 처리함
	 */
	def addDateTimeByTimezone(df: Dataset[Row], timezone: String, startDate: String, endDate: String): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 타임존 기준 dateTime 추가 ( timezone= $timezone )")

		// timezone 을 파라미터로 받아서 처리 하므로, useApTimezone 으로 체크 하면 안 됨
		val addedDateTimeDf = if (timezone.equals("-")) {
			// AP 타임존으로 집계 시
			df.withColumn(FieldType.dateTime, date_format($"apTimestamp", dateTimeFormat))
				.withColumn("date", date_format($"apTimestamp", "yyyyMMdd"))
		} else {
			// 고정 타임존으로 집계 시 ( UTC -> 고정 타임존으로 변환 )
			df.withColumn(FieldType.dateTime, date_format(from_utc_timestamp($"utcTimestamp", timezone), dateTimeFormat))
				.withColumn("date", date_format(from_utc_timestamp($"utcTimestamp", timezone), "yyyyMMdd"))
		}

		addedDateTimeDf.filter(s"'$startDate' <= date and date <= '$endDate'")
	}

	/**
	 * 계산 지표 추가
	 */
	def addCalculatedMetric(df: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 계산 지표 추가 ( useCalculatedMetric= $useCalculatedMetric )")

		if (useCalculatedMetric) {
			calculatedMetricList.foldLeft(df) { (x: Dataset[Row], y: Option[Metric]) => x.withColumn(y.get.name, y.get.formula) }
		} else df
	}

	/**
	 * 광고공급자 메타 정보 추가
	 * 	- AP 타임존 광고 유닛 요청수 머지를 위해 timezone 만 별도로 추가할 수 있도록 처리함
	 * 	- adProviderId :: timezone
	 * 	- adProviderId :: adProviderName, connectionType, timezone, currency, adProviderGroup
	 */
	def addMetaOfAdProvider(df: Dataset[Row], isTimezoneOnly: Boolean = false): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX adProviderName, connectionType, timezone, currency, adProviderGroup 추가")

		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdProviders")
		if (isTimezoneOnly) {
			val apDf = spark.read.mongo(readConfig).selectExpr(s"_id.oid AS ${FieldType.adProviderId}", FieldType.timezone)

			df.join(broadcast(apDf), Seq(FieldType.adProviderId), "left_outer")
				.na.fill("-", Seq(FieldType.timezone))
		} else {
			val apDf = spark.read.mongo(readConfig).selectExpr(s"_id.oid AS ${FieldType.adProviderId}", s"name AS ${FieldType.adProviderName}", FieldType.connectionType, FieldType.timezone, FieldType.currency, s"namGroup AS ${FieldType.adProviderGroup}")

			df.join(broadcast(apDf), Seq(FieldType.adProviderId), "left_outer")
				.na.fill("-", Seq(FieldType.adProviderName, FieldType.connectionType, FieldType.timezone, FieldType.currency, FieldType.adProviderGroup))
		}
	}

	/**
	 * 서비스 메타 정보 추가
	 * 	- serviceId :: serviceName
	 */
	def addMetaOfService(df: Dataset[Row], publisherId: String): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX serviceName 추가")

		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncPublisherServices")
		val svcDf = spark.read.mongo(readConfig)
			.filter(s"publisher_id.oid == '$publisherId'")
			.selectExpr(s"_id.oid AS ${FieldType.serviceId}", s"name AS ${FieldType.serviceName}")

		df.join(broadcast(svcDf), Seq(FieldType.serviceId), "left_outer")
			.na.fill("-", Seq(FieldType.serviceName))
	}

	/**
	 * 광고공급단위 메타 정보 추가
	 * 	- adProviderPlaceId :: placeName, placeChannelType, placeProductType, placeCreativeType
	 * 	- 광고공급단위는 건수가 많아서 필터를 적용함
	 */
	def addMetaOfPlace(df: Dataset[Row], filteredPlaceIds: Seq[String]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX placeName, placeChannelType, placeProductType, placeCreativeType 추가")

		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdProviderPlaces")
		val plDf = spark.read.mongo(readConfig)
			.filter($"_id.oid".isin(filteredPlaceIds: _*))
			.selectExpr(s"_id.oid AS ${FieldType.adProviderPlaceId}", s"name AS ${FieldType.placeName}",
				s"channelType AS ${FieldType.placeChannelType}", s"productType AS ${FieldType.placeProductType}", s"creativeType AS ${FieldType.placeCreativeType}")

		df.join(broadcast(plDf), Seq(FieldType.adProviderPlaceId), "left_outer")
			.na.fill("-", Seq(FieldType.placeName, FieldType.placeChannelType, FieldType.placeProductType, FieldType.placeCreativeType))
	}

	/**
	 * 비딩그룹 메타 정보 추가
	 * 	- biddingGroupId :: biddingGroupName
	 */
	def addMetaOfBiddingGroup(df: Dataset[Row], publisherId: String): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX biddingGroupName 추가")

		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncBiddingGroups")
		val bgDf = spark.read.mongo(readConfig)
			.filter(s"publisher_id.oid == '$publisherId'")
			.selectExpr(s"_id.oid AS ${FieldType.biddingGroupId}", s"name AS ${FieldType.biddingGroupName}")

		df.join(broadcast(bgDf), Seq(FieldType.biddingGroupId), "left_outer")
			.na.fill("-", Seq(FieldType.biddingGroupName))
	}

	/**
	 * 거래 메타 정보 추가
	 * 	- dealId :: dealName
	 */
	def addMetaOfDeal(df: Dataset[Row], publisherId: String): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX dealName 추가")

		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncGFDDeals")
		val dlDf = spark.read.mongo(readConfig)
			.filter(s"publisher_id.oid == '$publisherId'")
			.selectExpr(FieldType.dealId, s"name AS ${FieldType.dealName}")

		df.join(broadcast(dlDf), Seq(FieldType.dealId), "left_outer")
			.na.fill("-", Seq(FieldType.dealName))
	}

	/**
	 * 국가 정보 추가
	 * 	- country :: countryName
	 */
	def addMetaOfCountry(df: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX countryName 추가")

		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "Countries")
		val countryDf = spark.read.mongo(readConfig)
			.selectExpr(s"code As ${FieldType.country}", s"name AS ${FieldType.countryName}")

		df.join(broadcast(countryDf), Seq(FieldType.country), "left_outer")
			.na.fill("-", Seq(FieldType.countryName))
	}

	/**
	 * 기본 지표 집계
	 */
	def getAggDf(df: Dataset[Row]): Dataset[Row] = {
		// aggDf 항목 : isAur, dateTime, (선택 dimensions), (선택 metrics)
		val aggDf = if (aggList.nonEmpty) {
			df.groupBy(groupByList.head, groupByList.tail: _*)
				.agg(aggList.head, aggList.tail: _*)
				// 지표가 모두 0인 경우 제외 처리
				.filter(s"NOT(${metricList.map(mtr => s"$mtr == 0").mkString(" AND ")})")
				.withColumn("isAur", lit(0))
		} else spark.emptyDataFrame

		// 스키마 통일 : (선택 dimensions), isAur, timezone, adUnitRequests, (선택 metrics)
		addMissingColumns(aggDf, groupByList ++ Seq("isAur", FieldType.timezone, FieldType.adUnitRequests) ++ metricList)
	}

	/**
	 * 타임존별 광고 유닛 요청수 집계
	 * 	- 지르콘 광고 유닛 요청수 Distinct 기준 항목 : seoulTimestamp(utcTimestamp), publisherId, adUnitId, deviceOs, country
	 * 	- AP 타임존 : 매체와 연동 중인 AP 들의 타임존 으로 집계
	 * 	- 고정 타임존 : 해당 타임존 으로 집계
	 */
	def getAdUnitRequestsDf(df: Dataset[Row], startDate: String, endDate: String): Dataset[Row] = {
		// aurDf 최종 항목 : isAur, timezone, dateTime, (serviceId, adUnitId, deviceOs, country), adUnitRequests
		// AP 타임존 : 매체와 연동 중인 AP 들의 타임존 으로 집계
		// 고정 타임존 : 해당 타임존 으로 집계
		val aurDf = if (useAdUnitRequests) {
			// 광고 유닛 요청수 Distinct 기준 항목 정보 : utcTimestamp, serviceId, adUnitId, deviceOs, country, adUnitRequests
			val distinctDf = df.selectExpr(AUR_DISTINCT_FIELDS: _*).distinct()

			val cachedAurDf = if (useApTimezone) {
				// AP 타임존인 경우, 캐시 적용
				val cachedDf = distinctDf.cache()

				// 캐시 리스트에 추가
				cachedDfList = cachedDfList :+ cachedDf

				cachedDf
			} else distinctDf

			// 타임존별 광고 유닛 요청수 집계
			aurTimezones.map { timezone =>
				addDateTimeByTimezone(cachedAurDf, timezone, startDate, endDate)
					.groupBy(aurBaseList.head, aurBaseList.tail: _*)
					.agg(aurAgg)
					.withColumn(FieldType.timezone, lit(timezone))
					.withColumn("isAur", lit(1))
			}.reduce(_ unionAll _)
		} else spark.emptyDataFrame

		// 스키마 통일 : (선택 dimensions), isAur, timezone, adUnitRequests, (선택 metrics)
		addMissingColumns(aurDf, groupByList ++ Seq("isAur", FieldType.timezone, FieldType.adUnitRequests) ++ metricList)
	}

	/**
	 * 누락된 스키마 추가
	 * 	- Union & Join 하기 전에 스키마 및 컬럼 순서를 맞춘다.
	 */
	def addMissingColumns(df: Dataset[Row], columns: Seq[String]): Dataset[Row] = {
		val missingColumns = columns.diff(df.columns)
		missingColumns.foldLeft(df) { (tempDF, colName) =>
			tempDF.withColumn(colName, lit(null).cast(SCHEMA_MAPPING.getOrElse(colName, StringType)))
		}.selectExpr(columns: _*)
	}

	/**
	 * 기본 지표 & AUR 지표 최종 집계 및 조인
	 */
	def getJoinedDf(df: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 기본 지표 & AUR 지표 최종 집계 및 조인 ( useAdUnitRequests= $useAdUnitRequests, useApTimezone= $useApTimezone )")

		// 기본 지표 최종 집계
		val aggDf = if (aggList.nonEmpty) {
			df.filter("isAur == 0")
				.groupBy(groupByList.head, groupByList.tail: _*)
				.agg(aggList.head, aggList.tail: _*)
		} else addMissingColumns(spark.emptyDataFrame, groupByList ++ metricList)

		// 기본 지표 & AUR 지표 조인
		val joinedDf = if (useAdUnitRequests) {
			// AUR 지표 최종 집계
			val aurDf = df.filter("isAur == 1")
				.groupBy(aurGroupByList.head, aurGroupByList.tail: _*)
				.agg(aurAgg)

			// logger.debug("aurDf schema >>>>>>")
			// aurDf.printSchema()
			// aurDf.show(100, truncate = false)

			joinAdUnitRequests(aggDf, aurDf)
				.na.fill("-", groupByList)
				.na.fill(0, metricList)
		} else aggDf

		// logger.debug("aggDf schema >>>>>>")
		// aggDf.printSchema()
		// aggDf.show(100, truncate = false)

		// logger.debug("joinedDf schema >>>>>>")
		// joinedDf.printSchema()
		// joinedDf.show(100, truncate = false)

		joinedDf
	}

	/**
	 * 광고 유닛 요청수 조인
	 * - AP 타임존
	 *		- joinKey : timezone, dateTime, (serviceId, adUnitId, deviceOs, country)
	 *		- joinType : left_outer
	 * 	- 고정 타임존
	 *		- joinKey : dateTime, (serviceId, adUnitId, deviceOs, country)
	 *		- joinType : full_outer
	 */
	def joinAdUnitRequests(aggDf: Dataset[Row], aurDf: Dataset[Row]): Dataset[Row] = {
		// AP 타임존인 경우, joinKey 인 timezone 메타 추가
		val baseDf = if (useApTimezone) addMetaOfAdProvider(aggDf, isTimezoneOnly = true) else aggDf

		// AP 타임존 (aurGroupByList) : timezone, dateTime, (serviceId, adUnitId, deviceOs, country)
		// 고정 타임존 (aurBaseList) : dateTime, (serviceId, adUnitId, deviceOs, country)
		val joinKey = if (useApTimezone) aurGroupByList else aurBaseList
		val joinType = if (useApTimezone) "left_outer" else "full_outer"

		logger.debug(s"$LOG_PREFIX joinKey= ${joinKey.toString()}, joinType= $joinType")

		// 광고 유닛 요청수 조인
		val joinedDf = baseDf.join(aurDf, joinKey, joinType)

		joinedDf.drop(FieldType.timezone)
	}

	def unCommonPersist(): Unit = {
		if (cachedDfList.nonEmpty) {
			cachedDfList.map(_.unpersist())
		}
	}
}
