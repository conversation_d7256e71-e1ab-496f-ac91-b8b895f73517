package com.navercorp.gfp.biz.performance

import java.util.Date
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future
import scala.util.Try

import org.apache.hadoop.fs.FileSystem
import org.apache.spark.sql._
import org.bson.types.ObjectId
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, Period}

import com.navercorp.gfp.core.BaseEnv.{HADOOP_NAME_SERVICE, ZIRCON_B_WAREHOUSE_PATH}
import com.navercorp.gfp.core.BaseSchema.ZIRCON_B_SCHEMA
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory, SummaryHistoryDetail}
import com.navercorp.gfp.core.{BaseAggregator, BizAggregator}
import com.navercorp.gfp.exception.BusinessException
import com.navercorp.gfp.meta.customreport.{CustomReportDao, CustomReportFilter}
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil}


/*
	- 지르콘B 기반으로 매체성과 리포트를 집계해서 HDFS 에 csv 로 저장
		- input zircon : /zircon/b/warehouse/yyyy/mm/dd/hh/_publisherId=xxx
		- output csv : /performance_report/cms/{reqDate:yyyy/MM/dd}/reportId={reportId}/CustomPerformanceReport_{startDate}_{endDate}_{reportId}.csv

	spark 설정 (7일치)
		--num-executors 20
		--executor-cores 4
		--executor-memory 2g
		--conf spark.sql.shuffle.partitions=500
		--conf spark.executor.memoryOverhead=500m
		--conf spark.sql.files.maxPartitionBytes=32mb
*/
object CmsPerformanceAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [CMS-PERFORMANCE]"

	private var sparkAppId: String = ""

	// /user/gfp-data/performance_report/cms
	private val PR_ROOT_DIR = HADOOP_NAME_SERVICE + conf.getString("hadoop.nameservice.bizcloud.performance.path") + conf.getString("hadoop.nameservice.bizcloud.performance.cms.path")

	// /user/gfp-data/performance_report/cms/checkpoint
	private val CHECKPOINT_ROOT_DIR = PR_ROOT_DIR + conf.getString("hadoop.nameservice.bizcloud.performance.checkpoint.path")

	private val customReportDao = new CustomReportDao()

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		val reqDate = args(0)
		val reportId = args(1)

		try {
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(reqDate)).isSuccess, s"reqDate($reqDate) is invalid format (must be yyyyMMdd)")
			require(ObjectId.isValid(reportId), s"reportId($reportId) is invalid ObjectId")

			// reportId 에 해당하는 리포트 요청 정보 가져오기
			val (publisherId, timezone, startDate, endDate, dimensions, metrics, filters) = customReportDao.getCustomReport(reportId) match {
				case Some(cr) => (cr.publisher_id.toString, cr.timezone, cr.startDate, cr.endDate, cr.dimensions, cr.metrics, cr.filters.getOrElse(Map.empty))
				case _ => throw BusinessException(s"리포트 요청 정보가 DB 에 존재하지 않습니다 (reportId= $reportId)")
			}

			// publisherId 에 해당 하는 cmsType 정보 가져오기
			val cmsType = publisherDao.getCmsType(publisherId) match {
				case Some(cmsType) => cmsType
				case _ => throw BusinessException(s"매체 정보가 DB 에 존재하지 않습니다 (publisherId= $publisherId)")
			}

			logger.debug(s"$LOG_PREFIX reqDate= $reqDate")
			logger.debug(s"$LOG_PREFIX reportId= $reportId, publisherId= $publisherId")
			logger.debug(s"$LOG_PREFIX timezone= $timezone, startDate= $startDate, endDate= $endDate")
			logger.debug(s"$LOG_PREFIX dimensions= $dimensions, metrics= $metrics")
			logger.debug(s"$LOG_PREFIX filters= $filters")
			logger.debug(s"$LOG_PREFIX cmsType= $cmsType")

			// 이력쌓기 상세정보
			val detail = summaryHistoryId match {
				case Some(_) => None
				case None => Option(SummaryHistoryDetail(
					reportId = Option(reportId),
					publisherId = Option(publisherId),
					startDate = Option(startDate),
					endDate = Option(endDate),
				))
			}

			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(reqDate),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
				detail = detail
			)
			this.upsertSummaryHistory(inProgressHist)


			// root= /performance_report/cms/{reqDate:yyyy/MM/dd}/reportId={reportId}
			// tempPath= /performance_report/cms/{reqDate:yyyy/MM/dd}/reportId={reportId}/temp
			// finalPath= /performance_report/cms/{reqDate:yyyy/MM/dd}/reportId={reportId}/CustomPerformanceReport_{startDate}_{endDate}_{reportId}.csv
			// successFilePath= /performance_report/cms/{reqDate:yyyy/MM/dd}/reportId={reportId}/_SUCCESS
			// checkpointPath= /performance_report/cms/checkpoint/publisherId={publisherId}/reportId={reportId}
			val reqDt: DateTime = DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(reqDate)
			val root = s"$PR_ROOT_DIR/${reqDt.toString("yyyy/MM/dd")}/reportId=$reportId"
			val tempPath = s"$root/temp"
			val finalPath = s"$root/CustomPerformanceReport_${startDate}_${endDate}_$reportId.csv"
			val successFilePath = s"$root/_SUCCESS"
			val checkpointPath = s"$CHECKPOINT_ROOT_DIR/publisherId=$publisherId/reportId=$reportId"

			logger.debug(s"tempPath= $tempPath")
			logger.debug(s"finalPath= $finalPath")
			logger.debug(s"successFilePath= $successFilePath")
			logger.debug(s"checkpointPath= $checkpointPath")


			// 집계 인스턴스 생성
			val aggregator = new CmsPerformanceAggregator(publisherId, timezone, dimensions, metrics, filters, cmsType)

			// 리포트 집계를 위한 초기화
			aggregator.init()

			// 지르콘 일단위 체크포인트 적재
			val format = DateTimeFormat.forPattern("yyyyMMdd")
			val dtRange = dateTimeRange(format.parseDateTime(startDate), format.parseDateTime(endDate), Period.days(7)).toList
			val checkpointedDf = dtRange.map { dt =>
				val start = dt.toString("yyyyMMdd")
				val end = if (dt.plus(Period.days(6)).isBefore(format.parseDateTime(endDate))) dt.plus(Period.days(6)).toString("yyyyMMdd") else endDate

				// 지르콘B 경로 가져오기
				val pathList = getPathList(publisherId, start, end)

				// 지르콘B 로딩
				val zirconDf = if (pathList.nonEmpty) {
					loadParquetLog(pathList) match {
						case Some(df) => df
						case _ => spark.createDataFrame(spark.sparkContext.emptyRDD[Row], ZIRCON_B_SCHEMA)
					}
				} else spark.createDataFrame(spark.sparkContext.emptyRDD[Row], ZIRCON_B_SCHEMA)

				aggregator.checkpoint(Option(zirconDf, start, end, checkpointPath))
			}.reduce(_ unionAll _)

			// 체크포인트 결과 집계
			val aggregatedDf = aggregator.aggregate(Option(checkpointedDf))

			// HDFS - 기존 파일 삭제
			aggregator.delete(aggregator.getFuturesForDelete(Option((hdfs, Seq(tempPath, finalPath, successFilePath)))))

			// HDFS - csv 파일 쓰기
			aggregator.write(aggregatedDf, Option((hdfs, tempPath, finalPath)))

			// HDFS - 체크포인트 삭제
			aggregator.delete(aggregator.getFuturesForDelete(Option((hdfs, Seq(checkpointPath)))))

			// 캐시 해제
			aggregator.unPersist()

			// HDFS - SUCCESS 파일 쓰기
			createSuccessFile(successFilePath)

			// 이력쌓기 - 완료 (COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					))
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * 지르콘B 경로 가져오기
	 * 	- startDate-1 ~ endDate+1 로드
	 *
	 * @param publisherId
	 * @param startDate
	 * @param endDate
	 * @return pathList
	 */
	def getPathList(publisherId: String, startDate: String, endDate: String): List[String] = {
		val format = DateTimeFormat.forPattern("yyyyMMdd")
		val dtRange = dateTimeRange(format.parseDateTime(startDate).minus(Period.days(1)), format.parseDateTime(endDate).plus(Period.days(1)), Period.days(1)).toList

		val pathList = dtRange.map { dt =>
			val path: String = s"$ZIRCON_B_WAREHOUSE_PATH/${dt.toString("yyyy/MM/dd")}/*/_publisherId=$publisherId"

			// hdfs 에 존재하는 경로만 따로 추출
			if (HdfsUtil.existsPathPattern(hdfs, path)) path else ""
		}.filter(_.nonEmpty)

		logger.debug(s"pathList= $pathList")

		pathList
	}

	/**
	 * SuccessFile 생성
	 *
	 * @param successFile
	 */
	def createSuccessFile(successFile: String): Unit = {
		HdfsUtil.create(hdfs, successFile)
	}
}

/**
 * 데이터 삭제 / 집계 / 추가 등의 작업을 한다.
 *
 * spark-submit에서 호출할 때나 test case에서 호출할 때 사용함
 * 환경에 따라 달라지는 값은 생성자 파라미터로 받아서 처리함.
 *
 * @param publisherId
 * @param timezone
 * @param dimensions
 * @param metrics
 * @param filters
 * @param cmsType
 */
class CmsPerformanceAggregator(publisherId: String, timezone: String, var dimensions: Seq[String], var metrics: Seq[String], filters: Map[String, CustomReportFilter], cmsType: String)(implicit sparkSession: SparkSession) extends BizAggregator with PerformanceHelper {
	val LOG_PREFIX = ".......... [CMS-PERFORMANCE]"

	// 명시적으로 PerformanceHelper 의 spark 를 override
	override implicit val spark: SparkSession = sparkSession

	/* dimension */
	// dimension : month, date, hour, service, adUnit, adProviderGroup, adProvider, place, biddingGroup, deal, responseCreativeType, deviceOs, country
	// 	- service -> svc.name
	// 	- adProvider -> ap.name, ap.connectionType, ap.timezone, ap.currency, ap.adProviderGroup
	// 	- place -> pl.name, pl.channelType, pl.productType, pl.creativeType, placeKey
	// 	- biddingGroup -> bg.name
	// 	- deal -> dl.name
	// 	- place 선택 시, placeKey 자동 추가
	// 	- GFP 매체인 경우, adProviderGroup 미포함 (API 단에서 처리)

	/* metrics */
	// AP metrics : impressions, clicks, krwRevenue, usdRevenue, krwNetRevenue, usdNetRevenue
	// 	- impressions = apImpressions
	// 	- clicks = apClicks
	// 	- krwRevenue = apKrwSales
	// 	- usdRevenue = apUsdSales
	// 	- krwNetRevenue = apKrwProfit
	// 	- usdNetRevenue = apUsdProfit
	// AP Calculated : ctr, krwCpm, usdCpm, krwCpc, usdCpc, krwNetCpm, usdNetCpm, krwNetCpc, usdNetCpc
	// 	- ctr = (clicks / impressions) * 100
	// 	- krwCpm = (krwRevenue / impressions) * 1000
	// 	- usdCpm = (usdRevenue / impressions) * 1000
	// 	- krwCpc = krwRevenue / clicks
	// 	- usdCpc = usdRevenue / clicks
	// 	- krwNetCpm = (krwNetRevenue / impressions) * 1000
	// 	- usdNetCpm = (usdNetRevenue / impressions) * 1000
	// 	- krwNetCpc = krwNetRevenue / clicks
	// 	- usdNetCpc = usdNetRevenue / clicks
	// SSP metrics : adUnitRequests, adProviderRequests, adProviderResponses, sspFilledRequests, sspImpressions, sspViewableImpressions, sspEstimatedImpressions, sspClicks, sspCompletions, sspEstimatedKrwNetRevenue, sspEstimatedUsdNetRevenue
	// 	- sspFilledRequests = gfpFilledRequests
	// 	- sspImpressions = gfpImpressions
	// 	- sspViewableImpressions = gfpViewableImpressions
	// 	- sspViewableImpressions = gfpViewableImpressions
	// 	- sspEstimatedImpressions = gfpEstimatedImpressions
	// 	- sspClicks = gfpClicks
	// 	- sspCompletions = gfpCompletions
	// 	- sspEstimatedKrwNetRevenue = gfpEstimatedKrwProfit
	// 	- sspEstimatedUsdNetRevenue = gfpEstimatedUsdProfit
	// SSP Calculated : sspCtr, sspEstimatedKrwNetCpm, sspEstimatedUsdNetCpm, sspEstimatedKrwNetCpc, sspEstimatedUsdNetCpc
	// 	- sspCtr = (sspClicks / sspImpressions) * 100
	// 	- sspEstimatedKrwNetCpm = (sspEstimatedKrwNetRevenue / sspImpressions) * 1000
	// 	- sspEstimatedUsdNetCpm = (sspEstimatedUsdNetRevenue / sspImpressions) * 1000
	// 	- sspEstimatedKrwNetCpc = sspEstimatedKrwNetRevenue / sspClicks
	// 	- sspEstimatedUsdNetCpc = sspEstimatedUsdNetRevenue / sspClicks
	// NAM 매체인 경우, 추정 지표 미포함 (API 단에서 처리)

	/* 집계 시, 사용할 필드 리스트 */
	// dimensions = ["date", "service", "adProviderGroup", "adProvider", "adUnit", "place", "placeKey"]
	// metrics = ["impressions", "clicks", "krwRevenue", "usdNetCpm"]
	// 	- metricList = ["impressions", "clicks", "krwRevenue", "usdNetRevenue"]
	// 	- aggList = [expr(s"NVL(SUM(impressions), 0)").as("impressions"),
	// 				expr(s"NVL(SUM(clicks), 0)").as("clicks"),
	// 				expr(s"NVL(SUM(krwRevenue), 0)").as("krwRevenue"),
	// 				expr(s"NVL(SUM(usdNetRevenue), 0)").as("usdNetRevenue")]
	// 	- groupByList = ["dateTime", "serviceId", "adProviderId", "adUnitId", "adProviderPlaceId", "placeKey"]
	// 	- selectList = ["dateTime", "serviceId", "adProviderId", "adUnitId", "adProviderPlaceId",
	// 					"CASE WHEN connectionType == 'C2S' THEN placeKey ELSE '-' END AS placeKey",
	// 					"apImpressions as impressions", "apClicks as clicks", "apKrwSales as krwRevenue", "apUsdProfit as usdNetRevenue"]
	// 	- calculatedMetricList = [Some(Metric("usdNetCpm", when($"impressions" > 0, (($"usdNetRevenue" / $"impressions") * 1000).cast(DECIMAL_TYPE_17_6)).otherwise("-"), Seq("usdNetRevenue", "impressions") ))]
	// 	- finalFieldList = ["dateTime", "serviceName", "adProviderName", "connectionType", "timezone", "currency", "adUnitId",
	// 						"placeName", "placeChannelType", "placeProductType", "placeCreativeType", "placeKey", "adProviderGroup",
	// 						"impressions", "clicks", "krwRevenue", "usdNetCpm"]
	// 	- finalHeaderList = [("dateTime", "Date"), ("serviceName", "Service"), ("adUnitId", "AdUnit"),
	// 						("adProviderGroup", "Ad Provider Group"), ("adProviderName", "Ad Provider"), ("connectionType", "Ad Provider Connection Type"), ("timezone", "Ad Provider Time Zone"), ("currency", "Ad Provider Currency"),
	// 						("placeName", "Place"), ("placeChannelType", "Place Channel Type"), ("placeProductType", "Place Product Type"), ("placeCreativeType", "Place Creative Type"),
	// 						("placeKey", "Place Key"),
	// 						("impressions", "Impressions"), ("clicks", "Clicks"), ("krwRevenue", "KRW Revenue"), ("usdNetCpm", "USD Net CPM")]

	// 맞춤성과 metrics
	private val CMS_METRICS = Seq(FieldType.usdCpm, FieldType.krwCpm, FieldType.usdCpc, FieldType.krwCpc)

	// 맞춤성과 메타 추가 여부
	private var useServiceMeta = false
	private var useAdProviderMeta = false


	/**
	 * 초기화 설정
	 * 	- 맞춤성과 초기화
	 * 		- place 가 있는 경우, dimensions 에 placeKey 추가
	 * 		- metrics 가 empty 인 경우, 모든 metrics 설정
	 * 		- 맞춤성과 변수 초기화 : useServiceMeta, useAdProviderMeta
	 * 	- 공통 초기화
	 * 		- 1. 공통 필드 리스트 초기화 : metricList, aggList, groupByList, selectList, calculatedMetricList, aurBaseList, finalFieldList, finalHeaderList
	 * 		- 2. 공통 변수 초기화 : dateTimeFormat, useApTimezone, useAdUnitRequests, useCalculatedMetric, usePlaceMeta, useBiddingGroupMeta, useDealMeta, useCountryMeta
	 * 		- 3. placeChannelType, placeCreativeType, placeProductType 필터 조건 초기화
	 * 		- 4. 광고 유닛 요청수 집계를 위한 타임존 목록 조회
	 * 		- dimensions 에서 adProviderGroup 제거해서 넘김 (메타 추가할 때만 쓰이기 때문)
	 * 	- dimensions 에 adProviderGroup 이 있는 경우, finalFieldList 에 adProviderGroup 추가
	 */
	override def init(): Unit = {
		/* 맞춤성과 초기화 */
		// dimensions 에 place 가 있는 경우, placeKey 추가
		if (dimensions.indexOf(FieldType.place) > -1) {
			dimensions = (dimensions ++ Seq(FieldType.placeKey)).distinct
		}

		// metrics 가 empty 인 경우, 모든 metrics 설정
		if (metrics.isEmpty) {
			metrics = COMMON_METRICS ++ CMS_METRICS

			// GFP 매체인 경우, 추정 지표 추가
			if (cmsType == "GFP") metrics = metrics ++ ESTIMATED_METRICS
		}

		// 서비스, 광고공급자 메타 추가 여부
		useServiceMeta = dimensions.contains(FieldType.service)
		useAdProviderMeta = dimensions.contains(FieldType.adProvider)

		logger.debug(s"$LOG_PREFIX dimensions= $dimensions")
		logger.debug(s"$LOG_PREFIX metrics= $metrics")
		logger.debug(s"$LOG_PREFIX useServiceMeta= $useServiceMeta")
		logger.debug(s"$LOG_PREFIX useAdProviderMeta= $useAdProviderMeta")


		/* 공통 초기화 */
		// - 1. 공통 필드 리스트 초기화
		// - 2. 공통 변수 초기화
		// - 3. placeChannelType, placeCreativeType, placeProductType 필터 조건 초기화
		// - 4. 광고 유닛 요청수 집계를 위한 타임존 목록 조회
		// - dimensions 에서 adProviderGroup 제거 해서 넘김 (메타 추가할 때만 쓰이기 때문)
		init(publisherId, timezone, dimensions.filter(_ != FieldType.adProviderGroup), metrics, filters)

		/* adProviderGroup 이 있는 경우, finalFieldList 에 adProviderGroup 추가 */
		if (dimensions.contains(FieldType.adProviderGroup)) {
			finalFieldList = finalFieldList :+ FieldType.adProviderGroup
		}

		logger.debug(s"$LOG_PREFIX finalFieldList= $finalFieldList")
		logger.debug(s"$LOG_PREFIX finalHeaderList= $finalHeaderList")
	}


	/**
	 * 공통 & 맞춤성과 기본 필터 적용 (서비스, 디바이스, 국가, 광고유닛)
	 * 	- 공통 : 서비스, 디바이스, 국가
	 * 	- 맞춤성과 : 광고유닛
	 * 	- 광고유닛 요청수를 포함하는 경우, 캐시 적용
	 */
	def applyBaseFilters(df: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 공통 & 맞춤성과 기본 필터 적용 (서비스, 디바이스, 국가, 광고유닛)")

		// 공통 기본 필터 적용 (서비스, 디바이스, 국가)
		val filteredDf0 = applyCommonBaseFilters(df, filters)

		// 맞춤성과 필터 적용 (광고유닛)
		val filteredDf1 = getFilteredDf(filteredDf0, FieldType.adUnitId, getFilterValue(filters, FieldType.adUnitId))

		filteredDf1
	}


	/**
	 * 공통 & 맞춤성과 필터 적용 (광고공급단위, 응답소재유형, 광고공급자, 비딩그룹, 거래)
	 * 	- 공통 : 광고공급단위, 응답소재유형
	 * 	- 맞춤성과 : 광고공급자, 비딩그룹, 거래
	 */
	def applyFilters(df: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 공통 & 맞춤성과 필터 적용 (광고공급단위, 응답소재유형, 광고공급자, 비딩그룹, 거래)")

		// 공통 필터 적용 (광고공급단위, 응답소재유형)
		val filteredDf0 = applyCommonFilters(df, filters)

		logger.debug(s"$LOG_PREFIX 맞춤성과 필터 적용")

		// 맞춤성과 필터 적용 (광고공급자)
		// 	- adProviderId = '-' 인 경우, 광고유닛 요청수만 존재함
		val filteredDf1 = getFilteredDf(filteredDf0.filter("adProviderId != '-'"), FieldType.adProviderId, getFilterValue(filters, FieldType.adProviderId))

		// 맞춤성과 필터 적용 (비딩그룹)
		val filteredDf2 = getFilteredDf(filteredDf1, FieldType.biddingGroupId, getFilterValue(filters, FieldType.biddingGroupId))

		// 맞춤성과 필터 적용 (거래)
		val filteredDf3 = getFilteredDf(filteredDf2, FieldType.dealId, getFilterValue(filters, FieldType.dealId))

		filteredDf3
	}


	/**
	 * 메타 정보 추가
	 * 	- Services, AdProviders, Places, BiddingGroups, Deals, Countries
	 */
	def addMeta(df: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 메타 정보 추가 (Services, AdProviders, Places, BiddingGroups, Deals, Countries)")
		logger.debug(s"$LOG_PREFIX useServiceMeta = $useServiceMeta")
		logger.debug(s"$LOG_PREFIX useAdProviderMeta = $useAdProviderMeta")
		logger.debug(s"$LOG_PREFIX usePlaceMeta = $usePlaceMeta")
		logger.debug(s"$LOG_PREFIX useBiddingGroupMeta = $useBiddingGroupMeta")
		logger.debug(s"$LOG_PREFIX useDealMeta = $useDealMeta")
		logger.debug(s"$LOG_PREFIX useCountryMeta = $useCountryMeta")

		// 서비스 메타 추가
		val addedMetaDf1 = if (useServiceMeta) addMetaOfService(df, publisherId) else df

		// 광고공급자 메타 추가
		val addedMetaDf2 = if (useAdProviderMeta) addMetaOfAdProvider(addedMetaDf1) else addedMetaDf1

		// 광고공급단위 메타 추가 ( 광고공급단위는 건수가 많아서 Id 필터를 적용함 )
		val addedMetaDf3 = if (usePlaceMeta) addMetaOfPlace(addedMetaDf2, filteredPlaceIds) else addedMetaDf2

		// 비딩그룹 메타 추가
		val addedMetaDf4 = if (useBiddingGroupMeta) addMetaOfBiddingGroup(addedMetaDf3, publisherId) else addedMetaDf3

		// 거래 메타 추가
		val addedMetaDf5 = if (useDealMeta) addMetaOfDeal(addedMetaDf4, publisherId) else addedMetaDf4

		// 국가 메타 추가
		val addedMetaDf6 = if (useCountryMeta) addMetaOfCountry(addedMetaDf5) else addedMetaDf5

		addedMetaDf6
	}


	/**
	 * 기간별 체크포인트 적재
	 * 	- 기본 지표 + AUR 지표
	 *
	 * @return Dataset[Row]
	 */
	def checkpoint(param: Option[C] = None): Dataset[Row] = {
		val (logDf, startDate, endDate, checkpointPath) = param.get

		val checkpointDir = s"$checkpointPath/${startDate}_$endDate"
		spark.sparkContext.setCheckpointDir(checkpointDir)

		logger.debug(s"$LOG_PREFIX checkpointDir= $checkpointDir")

		// 1. 공통 & 맞춤성과 기본 필터 적용 (서비스, 디바이스, 국가, 광고유닛)
		val baseFilteredDf = applyBaseFilters(logDf)

		// 2. 공통 & 맞춤성과 필터 적용 (광고공급단위, 응답소재유형, 광고공급자, 비딩그룹, 거래)
		val filteredDf = applyFilters(baseFilteredDf)

		// 3. 타임존 기준 dateTime 추가 & selectExpr 설정
		val addedDatetimeDf = addDateTimeByTimezone(filteredDf, timezone, startDate, endDate).selectExpr(selectList: _*)

		// 4. 기본 지표 집계
		val aggDf = getAggDf(addedDatetimeDf)

		// 5. 타임존별 광고 유닛 요청수 집계
		val aurDf = getAdUnitRequestsDf(baseFilteredDf, startDate, endDate)

		// 6. 기간별 체크포인트
		val checkpointedDf = aggDf.unionAll(aurDf).coalesce(COALESCE_CNT).checkpoint()

		// logger.debug("aggDf schema >>>>>>")
		// aggDf.printSchema()
		// aggDf.show(100, truncate = false)

		// logger.debug("aurDf schema >>>>>>")
		// aurDf.printSchema()
		// aurDf.show(100, truncate = false)

		// logger.debug("checkpointedDf schema >>>>>>")
		// checkpointedDf.printSchema()
		// checkpointedDf.show(100, truncate = false)

		checkpointedDf
	}

	type C = (Dataset[Row], String, String, String)


	/**
	 * 데이터 집계
	 *
	 * @return Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		val unionDf = aggParam.get

		// 1. 최종 집계 및 조인
		val joinedDf = getJoinedDf(unionDf)

		// 2. 계산 지표 추가
		val addedCalculatedMetricDf = addCalculatedMetric(joinedDf)

		// 3. 메타 정보 추가 (Services, AdProviders, Places, BiddingGroups, Deals)
		val addedMetaDf = addMeta(addedCalculatedMetricDf)

		// 4. 엑셀 저장을 위한 Rename 처리
		val finalDf = renameColumn(addedMetaDf, finalHeaderList)

		// logger.debug("finalDf schema >>>>>>")
		// finalDf.printSchema()
		// finalDf.show(100, truncate = false)
		// finalDf.explain("formatted")

		finalDf
	}

	type A = Dataset[Row]


	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		val (hdfs, deletePathList) = param.get

		logger.debug(s"$LOG_PREFIX deletePathList= $deletePathList")

		List(Future {
			deletePathList.map(HdfsUtil.delete(hdfs, _))

			Option(true)
		})
	}

	type T = (FileSystem, Seq[String]) // ABSTRACT TYPE MEMBERS :: https://docs.scala-lang.org/tour/abstract-type-members.html


	/**
	 * 리포트 파일 생성 및 경로 이동
	 *
	 * @param df
	 * @param writeParam
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		val (hdfs, tempPath, finalPath) = writeParam.get

		logger.debug(s"$LOG_PREFIX tempPath= $tempPath, finalPath= $finalPath")

		this.writeCsvToHdfs(df, tempPath)
		this.moveFileWithBom(hdfs, s"$tempPath/*.csv", finalPath)
	}

	type W = (FileSystem, String, String)


	def unPersist(): Unit = {
		unCommonPersist()
	}
}
