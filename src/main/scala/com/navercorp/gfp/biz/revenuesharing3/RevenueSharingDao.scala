/**
 * java에서 mongodb 사용하는 법
 * https://mongodb.github.io/mongo-java-driver/
 * [p]
 * Mongo Java API Documentaton
 * https://mongodb.github.io/mongo-java-driver/4.0/apidocs/
 * [p]
 * Bson API Documentation
 * https://api.mongodb.com/java/3.0/index.html?org/bson/package-summary.html
 * [p]
 * MongoClient 인스턴스는 하나만 생성하고 이것을 전체 어플리케이션에서 사용해야 한다.
 * https://mongodb.github.io/mongo-java-driver/4.1/driver/getting-started/quick-start/#make-a-connection
 * The MongoClient instance represents a pool of connections to the database you will only need one instance of class MongoClient even with multiple threads.
 */
package com.navercorp.gfp.biz.revenuesharing3

import java.util
import java.util.Date
import scala.collection.JavaConverters._

import com.mongodb.client.MongoCollection
import com.mongodb.client.model.Updates.{combine, set}
import org.apache.logging.log4j.{<PERSON><PERSON><PERSON><PERSON><PERSON>, Logger}
import org.bson.BsonNull
import org.bson.conversions.Bson
import org.bson.types.ObjectId
import org.mongodb.scala.bson.collection.Document
import org.mongodb.scala.bson.{BsonArray, BsonString}
import org.mongodb.scala.model.Aggregates._
import org.mongodb.scala.model.Filters.{in, or}
import org.mongodb.scala.model.Sorts.ascending
import org.mongodb.scala.model.{Aggregates, Filters, Projections, Sorts, UnwindOptions}

import com.navercorp.gfp.biz.schedule.Schedule
import com.navercorp.gfp.core.database.Database
import com.navercorp.gfp.meta.publisher.Publisher

/*
Mongo Java API Documentaton
	- https://mongodb.github.io/mongo-java-driver/4.8/apidocs/
Bson API Documentation
	- https://api.mongodb.com/java/3.0/index.html?org/bson/package-summary.html
*/

class RevenueSharingDao {
	private val logger: Logger = LogManager.getLogger(this.getClass)

	/**
	 * 스케줄 ID에 해당하는 스케줄 조회
	 *
	 * @param scheduleId
	 * @return
	 */
	def getSchedule(scheduleId: String): Schedule = {
		/*
			db.getCollection('Schedules').aggregate(
				{
					$match: { _id: ObjectId('67332120f9f5f067ab274d6c') }
				},
				{
					'$lookup': {
						'from': 'SyncPublishers',
						'foreignField': '_id',
						'localField': 'publisher_id',
						'as': 'pub'
					}
				},
				{ '$unwind': { 'path': '$pub' } },
				{
					$replaceRoot: {
						newRoot: {
							$mergeObjects: [
								'$$ROOT',
								{publisherName: '$pub.name'},
								{cmsType: '$pub.cmsType'}
							]
						}
					}
				},
				{ $project: { pub: 0 } }
			)
		 */
		val coll: MongoCollection[Schedule] = Database.getDatabase().getCollection("Schedules", classOf[Schedule])
		val pipeline: java.util.List[Bson] = util.Arrays.asList(
			`match`(Document("_id" -> new ObjectId(scheduleId))),
			lookup("SyncPublishers", "publisher_id", "_id", "pub"),
			unwind("$pub"),
			replaceRoot(Document(
				// 주의할 점 !
				// 몽고디비 쿼리에서는 "newRoot"를 사용하지만,
				// 스칼라에서는 "newRoot" 제거해야 RevenueSharingReports의 필드들이 1 단계로 풀려 나온다.
				//				"newRoot" -> Document(
				"$mergeObjects" -> BsonArray(
					"$$ROOT",
					Document("cmsType" -> "$pub.cmsType"),
				)
				//				)
			)),
			project(Document("pub" -> 0)),
		)

		coll.aggregate(pipeline, classOf[Schedule]).first()
	}

	/**
	 * 수익쉐어 리포트 조회(스케줄, 매체 정보까지 포함)
	 *
	 * @param rptId
	 * @return
	 */
	def getReport(rptId: String): RevenueSharingReport = {
		val coll: MongoCollection[RevenueSharingReport] = Database.getDatabase().getCollection("RevenueSharingReports", classOf[RevenueSharingReport])

		/*
			db.getCollection('RevenueSharingReports').aggregate([
				{
					$match: {
						"_id": ObjectId("67a341185a8a63014a6459e3")
					}
				},
				{
					'$lookup': {
						from: 'Schedules',
						foreignField: '_id',
						localField: 'schedule_id',
						as: 'sch'
					}
				},
				{ '$unwind': '$sch' },
				{
					'$lookup': {
						from: 'SyncPublishers',
						foreignField: '_id',
						localField: 'sch.publisher_id',
						as: 'pub'
					}
				},
				{ '$unwind': '$pub' },
				{
					'$replaceRoot': {
						'newRoot': {
							'$mergeObjects': [
								'$$ROOT',
								// 파생필드(Schedules)
								{ 'schedule_id': '$sch._id' },
								{ 'scheduleName': '$sch.name' },
								{ 'frequency': '$sch.frequency' },
								{ 'adUnitIds': '$sch.ext.RS.adUnitIds' },
								{ 'keys': '$sch.ext.RS.keys' },

								// 파생필드(SyncPublishers)
								{ 'publisher_id': '$sch.publisher_id' },
								{ 'publisherName': '$pub.name' },
							]
						}
					}
				},
				{
					$project: {
						'pub': 0,
						'sch': 0
					}
				},
				{
					$sort: { publisherName: 1, scheduleName: 1, date: 1 }
				}
			])
		 */
		val pipeline: java.util.List[Bson] = util.Arrays.asList(
			`match`(Document("_id" -> new ObjectId(rptId))),
			lookup("Schedules", "schedule_id", "_id", "sch"),
			unwind("$sch"),
			lookup("SyncPublishers", "sch.publisher_id", "_id", "pub"),
			unwind("$pub"),

			// 주의할 점 !
			// 몽고디비 쿼리에서는 "newRoot"를 사용하지만,
			// 스칼라에서는 "newRoot" 제거해야 RevenueSharingReports의 필드들이 1 단계로 풀려 나온다.
			replaceRoot(Document(
				//				"newRoot" -> Document(
				"$mergeObjects" -> BsonArray(
					// RevenueSharingReports의 모든 필드
					"$$ROOT",

					// 파생필드(Schedules)
					Document("scheduleName" -> "$sch.name"),
					Document("frequency" -> "$sch.frequency"),
					Document("keys" -> "$sch.ext.RS.keys"),
					Document("includeAllServiceIds" -> "$sch.ext.RS.includeAllServiceIds"),
					Document("serviceIds" -> "$sch.ext.RS.serviceIds"),
					Document("adUnitIds" -> "$sch.ext.RS.adUnitIds"),

					// 파생필드(SyncPublishers)
					Document("publisher_id" -> "$pub._id"),
					Document("publisherName" -> "$pub.name"),
				)
				//				)
			)),
			project(Document("pub" -> 0, "sch" -> 0, "silvergreyDetails" -> 0)), // silvergreyDetails는 제외
		)

		val doc: RevenueSharingReport = coll.aggregate(pipeline).first()
		doc
	}

	/**
	 * 이 매체의 수익쉐어 용 키 조회. 생성일 오름차순.
	 * Zircon R GFP 집계 시 사용됨
	 *
	 * CMS Publishers 스키마
	 * 	- https://oss.navercorp.com/da-ssp/ssp-admin-web/issues/1169#issuecomment-15635737
	 * 	- Publishers.freeformKeyValues.reportAggregationTypes : [ 'GFP', 'REVENUE' ]
	 *
	 * @param pubId
	 * @return
	 */
	def getRevenueSharingKeys(pubId: String): Vector[String] = {
		/*
        db.getCollection('SyncPublishers').aggregate([
			{
				$match: {
					"_id": ObjectId("5be0f4c7ad288d002bf54dc2"),
					"freeformKeyValues.reportAggregationTypes": { $exists: true, $in: ["REVENUE"] }
				}
			},
			{
				$project: {
					name: '$name',
					freeformKeyValues: {
						$filter: {
							input: "$freeformKeyValues",
							as: "item",
							cond: {
								$and: [
									{ $ifNull: ["$$item.reportAggregationTypes", false] },
									{ $in: ["REVENUE", "$$item.reportAggregationTypes"] }
								]
							}
						}
					}
				}
			},
			{
				$project: {
					name: '$name',
					key: '$freeformKeyValues.key',

				}
			},
			{
				$sort: { 'freeformKeyValues.createdAt': 1 }
			}
		])

		doc = {
			"_id" : ObjectId("5be0f4c7ad288d002bf54dc2"),
			"name" : "퍼스널커뮤니티",
			"key" : [
				"bid",
				"adLocation",
				"adContentYN",
				"naverId",
				"saInfo",
				"spaceId",
				"adMediation",
				"influencerCtg",
				"calp",
				"bidInfluencer",
				"influencerGrade",
				"blogGrade",
				"a",
				"b",
				"c",
				"d",
				"e",
				"f",
				"g",
				"blogId",
				"abt"
			]
		}

		Document((_id,BsonObjectId{value=5bfd00d166de9300257b0bcb}), (name,BsonString{value='퍼스널커뮤니티'}), (keys,BsonArray{values=[BsonString{value='bid'}, BsonString{value='adContentYN'}, BsonString{value='adLocation'}, BsonString{value='naverId'}, BsonString{value='saInfo'}, BsonString{value='spaceId'}, BsonString{value='adMediation'}, BsonString{value='calp'}, BsonString{value='blogGrade'}, BsonString{value='influencerGrade'}, BsonString{value='blogId'}, BsonString{value='a'}, BsonString{value='pk'}, BsonString{value='trackingCode'}, BsonString{value='report'}, BsonString{value='adPlacement'}]}))
         */

		// 매체의 리포트 집계용 키 조회. 생성일 순 정렬
		val coll: MongoCollection[Publisher] = Database.getDatabase.getCollection("SyncPublishers", classOf[Publisher])
		val pipeline: java.util.List[Bson] = java.util.Arrays.asList(
			`match`(
				Filters.and(
					Filters.eq("_id", new ObjectId(pubId)),
					Filters.exists("freeformKeyValues.reportAggregationTypes"),
					Filters.in("freeformKeyValues.reportAggregationTypes", "REVENUE")
				)
			),
			Aggregates.project(
				Projections.fields(
					Projections.include("name"),
					Projections.computed(
						"freeformKeyValues",
						Document(
							"$filter" -> Document(
								"input" -> "$freeformKeyValues",
								"as" -> "item",
								"cond" -> Document(
									"$and" -> BsonArray(
										Document("$ifNull" -> BsonArray("$$item.reportAggregationTypes", false)),
										Document("$in" -> BsonArray("REVENUE", "$$item.reportAggregationTypes"))
									)
								)
							)
						)
					)
				)
			),
			Aggregates.project(
				Projections.fields(
					Projections.computed("keys", "$freeformKeyValues.key")
				)
			),
			Aggregates.sort(Sorts.ascending("freeformKeyValues.createdAt"))
		)

		// 조회
		val doc = coll.aggregate(pipeline, classOf[Document]).first()
		val keys = doc.get("keys") match {
			case Some(bsonArray: BsonArray) =>
				bsonArray.getValues.asScala.toVector.map(_.asInstanceOf[BsonString].getValue)
			case _ => Vector.empty[String]
		}
		println(s"....... keys=$keys")
		keys
	}

	/**
	 * pubId에 연결된 키 목록 중 keys에 해당하는 키들을 생성일 오름차순으로 정렬하여 가져온다.
	 *
	 * @param pubId
	 * @param keys
	 * @return
	 */
	def getSortedKeys(pubId: String, keys: Vector[String]): Vector[String] = {
		/*
			// 키 목록을 주고 생성일 오름차순 정렬
			db.getCollection('Publishers').aggregate([
				{
					'$match': {
						_id: ObjectId("5bfd00d166de9300257b0bcb"),
						'freeformKeyValues.key': {
							'$in': ["influencerGrade",
								"blogGrade",
								"calp",
								"adMediation",
								"spaceId",
								"naverId",
								"bid",
								"blogId"]
						}
					}
				},
				{
					'$sort': {
						'freeformKeyValues.createdAt': 1
					}
				},
				{
					'$project': {
						_id: 0,
						'keys': '$freeformKeyValues.key'
					}
				},
				{
					'$project': {
						'sortedKeys': {
							'$filter': {
								'input': "$keys",
								'as': "itemKey",
								'cond': {
									'$setIsSubset': [['$$itemKey'], ["influencerGrade",
										"blogGrade",
										"calp",
										"adMediation",
										"spaceId",
										"naverId",
										"bid",
										"blogId"]]
								}
							}
						}

					}
				}
			]);

		doc = {
			"sortedKeys" : [
				"bid",
				"naverId",
				"spaceId",
				"adMediation",
				"calp",
				"blogGrade",
				"influencerGrade",
				"blogId"
			]
		}

		Document((sortedKeys,BsonArray{values=[BsonString{value='bid'}, BsonString{value='naverId'}, BsonString{value='spaceId'}, BsonString{value='adMediation'}, BsonString{value='calp'}, BsonString{value='blogGrade'}, BsonString{value='influencerGrade'}, BsonString{value='blogId'}]}))
		 */
		val pipeline: java.util.List[Bson] = util.Arrays.asList(
			`match`(
				Document(
					"_id" -> new ObjectId(pubId),
					"freeformKeyValues.key" -> Document("$in" -> keys)
				)
			),
			project(
				Document(
					"_id" -> 0,
					"keys" -> "$freeformKeyValues.key"
				)
			),
			sort(ascending("freeformKeyValues.createdAt")),
			project(
				Document(
					"sortedKeys" -> Document(
						"$filter" -> Document(
							"input" -> "$keys",
							"as" -> "itemKey",
							"cond" -> Document(
								"$setIsSubset" -> BsonArray(
									BsonArray("$$itemKey"),
									keys
								)
							)
						)
					)
				)
			)
		)

		// 조회 파이프라인 출력
		val pipelineJson = pipeline.asScala.map(_.toBsonDocument().toJson).mkString("[", ", ", "]")
		println(s"..... pipelineJson = $pipelineJson")

		// 조회
		val coll: MongoCollection[Document] = Database.getDatabase().getCollection("SyncPublishers", classOf[Document])
		val doc = coll.aggregate(pipeline).first()
		val finalKeys = doc.get("sortedKeys") match {
			case Some(bsonArray: BsonArray) =>
				bsonArray.getValues
					.asScala.toVector.map(_.asInstanceOf[BsonString].getValue)
			case _ => Vector.empty[String]
		}

		finalKeys
	}

	/**
	 * pubId에 연결된 AP 중 키값분리연동 키가 있는 경우 adProviderId와 함께 해당 키(1개)를 가져온다.
	 * ApKey(adProviderId, key)
	 *
	 * @param pubId
	 * @return
	 */
	def getApKeys(pubId: String): Vector[ApKey] = {
		/*
			// real 5d11dc1a34480e001d31fb26 (LINEWEBTOON)
			// test 5d81f8d435c2b0001d48166a (리포트연동테스트용4)
			// test 5d6c7f1f100c8d0017ce8266 (라인웹툰DEV)

			db.getCollection('SyncAdProviderInfos').aggregate([
				{
					$match: {
						publisher_id: ObjectId('5d6c7f1f100c8d0017ce8266'),
					    reportApiKeyValueSplitKey: {
							$exists: true, // 필드 존재 여부 확인
							$ne: null,    // null이 아닌지 확인
							$ne: ""  // 빈 문자열이 아닌지 확인
						}
					}
				},
				{
					$project: {
						publisher_id: '$publisher_id',
						adProvider_id: '$adProvider_id',
						key: '$reportApiKeyValueSplitKey',
					}
				},
				{
					$sort: { publisher_id: 1, adProviderId: 1, key: 1 }
				}
			])


			// result
			{
				"adProviderId" : ObjectId("5c2436b438a0a3c9ea911556"),
				"key" : "contentId"
			}
			{
				"adProviderId" : ObjectId("5c2436e438a0a3c9ea9121d5"),
				"key" : "contentId"
			}
			{
				"adProviderId" : ObjectId("60eeeb6d6a93f789ce0836c7"),
				"key" : "contentId"
			}

			또는

			{
				"_id" : ObjectId("5d68b37d2785e6095476a5c7"),
				"adProvider_id" : ObjectId("5bf6500077bd856e48a03578"),
				"key" : "chnl"
			},
			{
				"_id" : ObjectId("5d68b37d2785e6095476a5c6"),
				"adProvider_id" : ObjectId("5be3aafe77bd856e4850945c"),
				"key" : "clipId"
			}
		 */

		val pipeline: java.util.List[Bson] = util.Arrays.asList(
			`match`(Document(
				"publisher_id" -> new ObjectId(pubId),

				// 필드가 존재하고, null이 아니고, 빈 문자열이 아닌 경우
				"reportApiKeyValueSplitKey" -> Document("$exists" -> true, "$ne" -> BsonNull.VALUE, "$ne" -> "")
			)),
			project(Document(
				"_id" -> 0,
				"adProvider_id" -> "$adProvider_id",
				"key" -> "$reportApiKeyValueSplitKey",
			))
		)

		// 조회 파이프라인 출력
		val pipelineJson = pipeline.asScala.map(_.toBsonDocument().toJson).mkString("[", ", ", "]")
		println(s"..... pipelineJson = $pipelineJson")

		// 조회
		val coll: MongoCollection[ApKey] = Database.getDatabase().getCollection("SyncAdProviderInfos", classOf[ApKey])
		val docs = coll.aggregate(pipeline)
		docs.asScala.toVector // 없으면 빈 Vector()
	}

	def getRelatedPlaceKeys(serviceIds: Vector[String], adUnitIds: Vector[String]): Vector[String] = {
		/*
			// 이 광고유닛 혹은 서비스에 연결된 플레이스
			db.getCollection('AdUnits').aggregate([
			{
				$match: {
					$or: [
						{
							adUnitId: {
								$in: [
									"Dis_PC_Viewer_end_nativebigbanner_600x500_image",
									"PC_Viewer_end_nativebigbanner_600x500_image",
									"MW_Viewer_end_bigbanner_300x250_image",
									"Dis_MW_Viewer_end_bigbanner_300x250_image",
									"iOSAPP_Viewer_end_bigbanner_300x250_image",
									"Dis_iOSAPP_Viewer_end_bigbanner_300x250_image",
									"AndAPP_Viewer_end_bigbanner_300x250_image",
									"Dis_AndAPP_Viewer_end_bigbanner_300x250_image",
									"AndAPP_RewardedVideo",
									"iOSAPP_RewardedVideo"
								]
							}
						},
		                { service_id: { $in: ["a", "b"] } }
					]
				}
			},
			{
				'$lookup': {
					from: 'MappingHistory',
					foreignField: 'adUnitId',
					localField: 'adUnitId',
					as: 'map'
				}
			},
			{ '$unwind': { 'path': '$map', 'preserveNullAndEmptyArrays': true } },
			{
				'$lookup': {
					from: 'AdProviderPlaces',
					foreignField: '_id',
					localField: 'map.adProviderPlace_id',
					as: 'place'
				}
			},
			{ '$unwind': { 'path': '$place', 'preserveNullAndEmptyArrays': true } },
			{
				$group: {
					_id: {
						placeKey: '$place.placeKey',
					}
				}
			},
			{ $replaceRoot: { newRoot: '$_id' } },
			{ $sort: { placeKey: 1 } },
		])
		 */

		val coll: MongoCollection[Document] = Database.getDatabase().getCollection("SyncAdUnits", classOf[Document])

		val conditions = Seq(
			if (serviceIds.nonEmpty) Some(in("service_id", serviceIds.map(new ObjectId(_)): _*)) else None,
			if (adUnitIds.nonEmpty) Some(in("adUnitId", adUnitIds: _*)) else None
		).flatten

		val matchCondition = if (conditions.nonEmpty) `match`(or(conditions: _*)) else `match`(Document())

		val pipeline: java.util.List[Bson] = util.Arrays.asList(
			matchCondition,
			lookup("SyncMappingHistory", "adUnitId", "adUnitId", "map"),
			unwind("$map", UnwindOptions().preserveNullAndEmptyArrays(true)),
			lookup("SyncAdProviderPlaces", "map.adProviderPlace_id", "_id", "place"),
			unwind("$place", UnwindOptions().preserveNullAndEmptyArrays(true)),
			group(Document("placeKey" -> "$place.placeKey")),
			replaceRoot(Document("placeKey" -> "$_id.placeKey")),
			sort(ascending("placeKey"))
		)

		val docs = coll.aggregate(pipeline)
		docs.map(doc => doc.getString("placeKey")).asScala.toVector
	}

	/**
	 * 수익쉐어 리포트 집계 후 데이터 존재 여부 업데이트
	 *
	 * @param rpt_id
	 * @param isDataPresent
	 */
	def updateDataPresent(rpt_id: ObjectId, isDataPresent: Boolean): Unit = {
		val coll: MongoCollection[RevenueSharingReport] = Database.getDatabase().getCollection("RevenueSharingReports", classOf[RevenueSharingReport])
		coll.updateOne(
			Document("_id" -> rpt_id),
			combine(
				set("isDataPresent", isDataPresent),
				set("modifiedAt", new Date())
			)
		)
	}
}
