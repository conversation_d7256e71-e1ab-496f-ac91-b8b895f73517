package com.navercorp.gfp.biz.bizanalysis

import java.util.Date
import scala.collection.mutable
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{MapType, StringType, StructField, StructType}
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat

import com.navercorp.gfp.biz.silver.SilverLogType
import com.navercorp.gfp.biz.silver.SilverLogType.SilverLogType
import com.navercorp.gfp.core.BaseConstant.DEVICE_OS
import com.navercorp.gfp.core.BaseEnv.{HADOOP_NAME_SERVICE, SILVER_CLIENT_INPUT_PATH, SILVER_SERVER_INPUT_PATH}
import com.navercorp.gfp.core._
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.util.{AggregatorUtil, HdfsUtil, NeloUtil}

object BizAnalysisHourlyAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [BIZ-ANLYS]"

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 대상 일자 설정
		val date = args(0)
		val publisherIds = publisherDao.getDomesticPublisherIds()

		// 결과 파일 관련 정보
		val dt: DateTime = DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(date)
		val hdfsPath = conf.getString("hadoop.nameservice.bizcloud.biz_analysis.path")
		val dtFullPath = s"$HADOOP_NAME_SERVICE$hdfsPath/${dt.toString("yyyy/MM/dd")}"

		try {
			logger.debug(s"---------------------------------------- target date: $date")

			// 이력쌓기 - IN_PROGRESS
			val sparkDocForInProgress = Spark(
				sparkAppId = Option(spark.sparkContext.applicationId),
				sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
				sparkStartedAt = Option(new Date)
			)
			val inProgressHist = SummaryHistory(
				datetime = Option(date),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(sparkDocForInProgress)
			)
			this.upsertSummaryHistory(inProgressHist)

			// 로그 로딩
			val sPaths = getPaths(date, publisherIds, SilverLogType.ErSspServer)
			val cPaths = getPaths(date, publisherIds, SilverLogType.ErSspClient)

			if (sPaths.nonEmpty && cPaths.nonEmpty) {
				val serverDf = loadSilverLog(sPaths)
				val clientDf = loadSilverLog(cPaths)

				// 집계
				val aggregator = new BizAnalysisHourlyAggregator(serverDf, clientDf, date, publisherIds)
				val aggDf = aggregator.aggregate()

				// HDFS - 기존 파일 삭제
				aggregator.delete(aggregator.getFuturesForDelete(Option((hdfs, dtFullPath))))

				// HDFS - CSV 파일 쓰기
				aggregator.write(aggDf, Option((hdfs, dtFullPath)))

			} else {
				logger.warn(s"서버 또는 클라이언트 경로가 존재하지 않음. date:$date sPaths.length:${sPaths.length} cPaths.length:${cPaths.length}")
			}

			// 이력쌓기 - COMPLETE
			val sparkDocForComplete = Spark(
				sparkAppState = Option(SparkAppState.COMPLETE.toString),
				sparkEndedAt = Option(new Date)
			)
			val completeHist = SummaryHistory(
				spark = Option(sparkDocForComplete)
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val sparkDocForFailure = Spark(
					sparkAppState = Option(SparkAppState.FAILURE.toString),
					sparkAppError = Option(t.getMessage),
					sparkEndedAt = Option(new Date)
				)
				val failureHist = SummaryHistory(
					spark = Option(sparkDocForFailure)
				)
				this.upsertSummaryHistory(failureHist)
				logger.error(s"$LOG_PREFIX 비즈 성과 리포트 생성 실패. sparkAppId=${spark.sparkContext.applicationId} date=$date summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")

				throw t
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * 국내 매체 경로만 설정
	 *
	 * @param date
	 * @param kind
	 * @return
	 */
	def getPaths(date: String, publisherIds: Seq[String], logType: SilverLogType): Seq[String] = {
		val paths = mutable.ArrayBuffer.empty[String]
		for (pubId <- publisherIds) {
			val path =
				if (logType == SilverLogType.ErSspServer)
					s"$SILVER_SERVER_INPUT_PATH/$date-*/publisherId=$pubId"
				else
					s"$SILVER_CLIENT_INPUT_PATH/$date-*/publisherId=$pubId"

			if (HdfsUtil.existsPathPattern(hdfs, path)) {
				paths += path
			}
		}

		logger.debug(s"대상경로..........................................")
		paths.foreach(path => logger.debug(s"대상경로: $path"))
		paths
	}
}

class BizAnalysisHourlyAggregator(serverDf: Dataset[Row], clientDf: Dataset[Row], date: String, publisherIds: Seq[String])(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper with AdProviderAggregator {
	val LOG_PREFIX = ".......... [BIZ-ANLYS]"

	import spark.implicits._

	// 원소를 drop 하여 사용하는 경우가 있으므로 DIMENSIONS, METRICS 의 순서 중요
	private val DIMENSIONS = Seq(
		"datetime", "publisherId", "serviceId", "adUnitId", "deviceOs",
		"adProviderId", "rank" // dropRight(2): 광고유닛 요청수는 adProviderId, rank 를 디멘전으로 포함하지 않음
	)
	private val METRICS = Seq(
		"adUnitRequests",
		// drop(1): 광고유닛 요청수는 서버로그에서만 집계하고, adProviderId 를 디멘전으로 포함하지 않음
		"adProviderRequests", "adProviderResponses",
		// 위 두 지표는 기재된 모든 디멘전에 대해 서버, 클라이언트 로그 모두 집계
		"gfpFilledRequests", "gfpImpressions", "gfpEstimatedImpressions", "gfpClicks"
		// dropRight(4): 위 세 지표는 클라이언트 로그에서만 집계
	)

	// dimension 에 포함된 필드에 대해서는 관련 메타 필드를 추가
	// metric 에 포함되는 필드에 대해서는 최종 헤더에 맞게 이름 수정
	// 나열 순서가 곧 최종 파일의 header 순서가 됨
	private val RENAMED_OR_EXTENDED_HEADER_MAP = Map(
		"publisherId" -> Seq("publisherId", "publisherName"),
		"serviceId" -> Seq("serviceId", "serviceName"),
		"adUnitId" -> Seq("adUnitId", "adUnitName"),
		"adProviderId" -> Seq("adProviderId", "adProviderName", "platform"),
		"gfpFilledRequests" -> Seq("gfpFilledRequests AS filledRequests"),
		"gfpImpressions" -> Seq("gfpImpressions AS impressions"),
		"gfpEstimatedImpressions" -> Seq("gfpEstimatedImpressions AS estimatedImpressions"),
		"gfpClicks" -> Seq("gfpClicks AS clicks")
	)

	// 최종 집계 결과 Df 에서 select 할 필드들
	private val FINAL_FIELD_LIST: Seq[String] = (DIMENSIONS ++ METRICS).flatMap(
		dim => RENAMED_OR_EXTENDED_HEADER_MAP.getOrElse(dim, Seq(dim))
	)

	def aggregate(aggParamOption: Option[A]): Dataset[Row] = {
		// 0.
		// 집계를 위한 Df 준비
		val apDf = broadcast(loadAdProviders())
		val sDfForAur = prepareServerDfNotExploded(serverDf)
		val filteredServerDf = prepareServerDf(serverDf, apDf)
		val filteredClientDf = prepareClientDf(clientDf, broadcast(applyDefaultEstRptType(apDf)))

		// 1.
		//  1-1. 광고유닛 요청수
		val auReqDf = getAdUnitRequests(sDfForAur)
		//  1-2. 선출수, 노출수, 추정노출수, 클릭수
		val apDlvDf = getAdProviderDelivery(filteredClientDf)
		//  1-3. 광고공급자 호출수, 광고공급자 응답수
		val s2sStats = getApReqRes(filteredServerDf)
		val c2sStats = getApReqRes(filteredClientDf)

		// 2.
		// AdProviderPlaces.creativeType=COMBINED 인 경우
		// 광고공급자 호출수, 광고공급자 응답수 추가 처리
		val apReqResDf = applyReqRes(s2sStats, c2sStats)

		// 3.
		// 다른 디멘전을 가진 집계 결과를 합침
		println("auReqDf")
		auReqDf.printSchema
		//		auReqDf.show(auReqDf.count().toInt, false)
		println("apDlvDf")
		apDlvDf.printSchema
		//		apDlvDf.show(apDlvDf.count().toInt, false)
		println("apReqResDf")
		apReqResDf.printSchema
		//		apReqResDf.show(apReqResDf.count().toInt, false)
		val allStatsDf = getAllStats(auReqDf, apDlvDf, apReqResDf)

		// 4.
		// 매체, 서비스, 광고유닛, 광고공급자 이름 추가
		val addedDf1 = addMetaOfPublishers(allStatsDf)
		val addedDf2 = addMetaOfServices(addedDf1)
		val addedDf3 = addMetaOfAdUnits(addedDf2)
		val addedDf4 = addMetaOfAdProviders(addedDf3, apDf)

		// 5.
		// 모든 지표가 0 인 row 제외 및 최종 파일 필드 구성
		val finalDf = filterAllZeroMetrics(addedDf4, METRICS)
			.selectExpr(FINAL_FIELD_LIST: _*)

		println(s"$LOG_PREFIX 최종 스키마 ........")
		finalDf.printSchema

		finalDf
	}

	/**
	 * - 서버 데이터셋에서 광고유닛 요청수 집계에 필요한 필드만 추출
	 * - datetime 필드 추가
	 * - os 분류
	 *
	 * @param df
	 * @return
	 */
	def prepareServerDfNotExploded(df: Dataset[Row]): Dataset[Row] = {
		val df1 = filterTest(df)
			.selectExpr(
				"requestTime",
				"requestId",
				"publisherId",
				"serviceId",
				"adUnitId",
				DEVICE_OS,
			)
			.withColumn("datetime", from_unixtime(col("requestTime").as("Long") / 1000, "yyyyMMddHH"))
			.drop("requestTime")

		df1
	}

	/**
	 * - 광과유닛 요청수 외의 딜리버리 지표 집계에 필요한 필드추가 전처리 (explode 가 필요한 작업)
	 * - adProviderId 로 필터링 (국내 ap 대상)
	 * - datetime, os 분류 추가
	 *
	 * @param df
	 * @return
	 */
	def prepareServerDf(df: Dataset[Row], apDf: Dataset[Row]): Dataset[Row] = {
		val df1 = filterTest(df)
			.withColumn("res", explode_outer($"responses")).drop("responses")
		val df2 = df1
			.filter($"res.adProviderId".isNotNull && $"res.adProviderId" =!= "")
			.join(apDf, df1("res.adProviderId") === apDf("adProviderId"), "leftsemi") // IN_NAVER AP 로 대상으로 위해 left semi join (=filter)
			.selectExpr(
				"null AS eventId",
				"requestTime",
				"publisherId",
				"serviceId",
				"adUnitId",
				DEVICE_OS,
				"res.adProviderId",
				"res.stat",
				"res.connectionType",
				"res.adProviderPlaceId",
				"NVL(res.responseCreativeType, '-') AS responseCreativeType",
				"res.rank",
			)
			.withColumn("datetime", from_unixtime(col("requestTime").as("Long") / 1000, "yyyyMMddHH"))
			.drop("requestTime")

		df2
	}

	/**
	 * - 클라이언트 데이터셋에서 필요한 필드만 추출
	 * - adProviderId 로 필터링 (국내 ap 대상)
	 * - datetime, os 분류 추가
	 * - 추정노출 집계기준(estimatedReportType) 필드 추가
	 *
	 * @param df
	 * @return
	 */
	def prepareClientDf(df: Dataset[Row], apDf: Dataset[Row]): Dataset[Row] = {
		val df1 = filterTest(df)
			.filter($"adProviderId".isNotNull && $"adProviderId" =!= "")
			.join(apDf, Seq("adProviderId"), "leftsemi") // IN_NAVER AP 로 대상으로 위해 left semi join (=filter)
			.selectExpr(
				AggregatorUtil.selectExprOfIsValid(),
				"eventId",
				"eventTime",
				"publisherId",
				"serviceId",
				"adUnitId",
				"adProviderId",
				DEVICE_OS,
				"stat",
				"connectionType",
				"adProviderPlaceId",
				"NVL(responseCreativeType, '-') AS responseCreativeType",
				"rank"
			)
			.withColumn("datetime", from_unixtime(col("eventTime"), "yyyyMMddHH"))
			.drop("eventTime")

		// 추정노출을 구하기 위해 estimatedReportType 추가
		val df2 = df1
			.join(apDf,
				(df1.col("adProviderId") === apDf.col("adProviderId")) &&
					(df1.col("responseCreativeType") === apDf.col("creativeType")), "left")
			.select(
				df1.col("*"),
				apDf.col("estimatedReportType")
			)

		df2
	}

	def loadAdProviders(): Dataset[Row] = {
		val oidStructType = StructType(List(StructField("oid", StringType, true)))
		val schema = StructType(List(
			StructField("_id", oidStructType, true),
			StructField("name", StringType, true),
			StructField("adProviderType", StringType, true),
			StructField("report", StructType(List(
				StructField("estimatedReportType", MapType(StringType, StringType), true),
			)))
		))

		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdProviders")
		val apDf = spark.read.schema(schema).mongo(readConfig)
			.filter("adProviderType == 'IN_NAVER'")
			.select(
				$"_id.oid".as("adProviderId"),
				$"name".as("adProviderName"),
				$"report.estimatedReportType"
			)
			.withColumn("platform", expr(
				"""CASE WHEN adProviderName LIKE 'GFA%' THEN 'GFA'
				WHEN adProviderName LIKE 'GFD%' THEN 'GFD'
				WHEN adProviderName LIKE 'NCC%' THEN 'NCC'
				WHEN (adProviderName LIKE 'NDP%' OR adProviderName LIKE 'NOSP%') THEN 'NDP'
			ELSE 'OTHERS' END""")
			)

		apDf
	}

	def getAdUnitRequests(df: Dataset[Row]): Dataset[Row] = {
		// 광고 유닛 요청수는 apId 기준으로 구할 수는 없으므로 제외 (dimensions 의 맨 끝이 'adProviderId' 라는 가정하에 dropRight)
		// dimension 변동 시 참고
		val auDims = DIMENSIONS.dropRight(2)

		val df1 = df
			.groupBy(auDims.head, auDims.tail: _*)
			.agg(count("requestId").as("adUnitRequests"))

		df1
	}

	def getAdProviderDelivery(df: Dataset[Row]): Dataset[Row] = {
		val df1 = df
			.groupBy(DIMENSIONS.head, DIMENSIONS.tail: _*)
			.agg(
				// 선출수
				sum(when($"eventId" === 1, 1L).otherwise(0L)).as("gfpFilledRequests"),
				// 노출수
				sum(when($"eventId" === 11 && $"isValid" === "1", 1L).otherwise(0L)).as("gfpImpressions"),
				// 추정노출수 (responseCreativeType 에 따른 estimatedReportType 별 노출수)
				sum(when($"estimatedReportType" === "FILL" && $"eventId" === 1, 1L)
					.when($"estimatedReportType" === "IMP" && $"eventId" === 11 && $"isValid" === "1", 1L)
					.when($"estimatedReportType" === "VIEW" && $"eventId" === 12 && $"isValid" === "1", 1L)
					.otherwise(0L)).as("gfpEstimatedImpressions"),
				// 클릭수
				sum(when($"eventId" === 3 && $"isValid" === "1", 1L).otherwise(0L)).as("gfpClicks")
			)

		df1
	}

	def getApReqRes(df: Dataset[Row]): Dataset[Row] = {
		// AdProviderPlaces.creativeType=COMBINED 인 경우에 대한 처리를 위해 디멘전 추가
		val dimensions = DIMENSIONS ++ Seq("adProviderPlaceId", "responseCreativeType")

		val df1 = df
			.groupBy(dimensions.head, dimensions.tail: _*)
			.agg(
				// 광고공급자 호출수
				sum(when(($"eventId".isNull && $"connectionType" === "S2S") || ($"eventId" === 1 && $"connectionType" === "C2S") || ($"eventId" === 91 && $"connectionType" === "C2S"), 1L).otherwise(0L)).as("adProviderRequests"),
				// 광고공급자 응답수
				sum(when(($"eventId".isNull && $"connectionType" === "S2S" && $"stat" === 1) || ($"eventId" === 1 && $"connectionType" === "C2S"), 1L).otherwise(0L)).as("adProviderResponses")
			)

		df1
	}

	def applyReqRes(s2sStats: Dataset[Row], c2sStats: Dataset[Row]): Dataset[Row] = {
		// "adProviderRequests", "adProviderResponses" 만을 metric 으로 하는 metric 리스트
		val exprs = METRICS.drop(1).dropRight(4).map(metric => sum($"$metric").as(metric))

		val dimensions = DIMENSIONS ++ Seq("adProviderPlaceId", "responseCreativeType")
		val unionedStats = s2sStats.unionByName(c2sStats, true)
			.groupBy(dimensions.head, dimensions.tail: _*)
			.agg(exprs.head, exprs.tail: _*)

		// adPvoiderPlaceId, placeCreativeType, responseCreativeType 이 포함된 상태
		val df1 = super.applyReqRes(unionedStats)

		// 리포트 디멘전만으로 합산
		val df2 = df1.groupBy(DIMENSIONS.head, DIMENSIONS.tail: _*)
			.agg(exprs.head, exprs.tail: _*)

		df2
	}

	def getAllStats(auReqDf: Dataset[Row], apDlvDf: Dataset[Row], apReqResDf: Dataset[Row]): Dataset[Row] = {
		// "adUnitRequests" 를 제외한 metric 리스트
		val apDlvAndReqResExprs = METRICS.drop(1).map(metric => coalesce(sum($"$metric"), lit(0)).as(metric))

		// 두 df 의 디멘전이 같으므로 union -> group by
		// "datetime", "publisherId", "adUnitId", "deviceOs", "adProviderId", "rank"
		// 선출수, 노출수, 추정노출수, 클릭수, 광고공급자 호출수, 광고공급자 응답수
		val apDlvAndReqResDf = apReqResDf
			.unionByName(apDlvDf, true)
			.groupBy(DIMENSIONS.head, DIMENSIONS.tail: _*)
			.agg(apDlvAndReqResExprs.head, apDlvAndReqResExprs.tail: _*)

		// 광고유닛 요청수는 adProviderId, rank 단위로 집계하지 않지만 하나의 row 로 표현하기 위해 union 이 아닌 join
		// 따라서 같은 ("datetime", "publisherId", "adUnitId", "deviceOs") 를 가진 row 라면
		// adproviderId, rank 가 다르더라도 광고유닛 요청수를 중복으로 기재
		val allStats = apDlvAndReqResDf
			.join(auReqDf, DIMENSIONS.dropRight(2), "full")
			.selectExpr((DIMENSIONS ++ METRICS): _*)
			// rank 는 integer 타입이므로 string 타입인 "-" 로 na.fill 이 적용되지 않으므로 아래와 같이 처리
			.withColumn("rank", when($"rank".isNull, lit("-")).otherwise($"rank"))
			.na.fill(0, METRICS)

		allStats
	}


	/**
	 * 광고공급자 메타 정보 추가
	 * 	- publisherId :: publisherName
	 *
	 */
	def addMetaOfPublishers(df: Dataset[Row]): Dataset[Row] = {
		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncPublishers")
		val pubDf = spark.read.mongo(readConfig)
			.filter($"_id.oid".isin(publisherIds: _*))
			.select(
				$"_id.oid".as("publisherId"),
				$"name".as("publisherName"),
			)

		df.join(broadcast(pubDf), Seq("publisherId"), "left")
			.na.fill("-", Seq("publisherName"))
	}

	/**
	 * 서비스 메타 정보 추가
	 * 	- serviceId :: serviceName
	 */
	def addMetaOfServices(df: Dataset[Row]): Dataset[Row] = {
		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncPublisherServices")
		val svcDf = spark.read.mongo(readConfig)
			.filter($"publisher_id.oid".isin(publisherIds: _*))
			.select(
				$"_id.oid".as("serviceId"),
				$"name".as("serviceName")
			)

		df.join(broadcast(svcDf), Seq("serviceId"), "left")
			.na.fill("-", Seq("serviceName"))
	}

	/**
	 * 광고유닛 메타 정보 추가
	 * 	- adUnitId :: adUnitName
	 */
	def addMetaOfAdUnits(df: Dataset[Row]): Dataset[Row] = {
		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdUnits")
		val auDf = spark.read.mongo(readConfig)
			.filter($"publisher_id.oid".isin(publisherIds: _*))
			.select(
				$"adUnitId",
				$"name".as("adUnitName"),
			)

		df.join(broadcast(auDf), Seq("adUnitId"), "left")
			.na.fill("-", Seq("adUnitName"))
	}


	/**
	 * 광고공급자 메타 정보 추가
	 * 	- adProviderId :: adProviderName, platform
	 */
	def addMetaOfAdProviders(df: Dataset[Row], apDf: Dataset[Row]): Dataset[Row] = {
		df.join(apDf, Seq("adProviderId"), "left")
			.select(
				df.col("*"),
				apDf.col("adProviderName"),
				apDf.col("platform")
			)
			.na.fill("-", Seq("adProviderId", "adProviderName", "platform"))
	}

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	type T = (FileSystem, String) // ABSTRACT TYPE MEMBERS :: https://docs.scala-lang.org/tour/abstract-type-members.html

	override def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		val (hdfs, dtFullPath) = param.get
		logger.debug(s"$LOG_PREFIX dstPath= $dtFullPath")

		List(Future {
			HdfsUtil.delete(hdfs, dtFullPath)
			Option(true)
		})
	}

	/**
	 * HDFS 에 집계 파일 쓰기 (rename 을 통해 최종 리포트 파일 이름으로 변환)
	 * 경로 내에 csv.gz 패턴의 파일이 1개일 때만 정상 동작
	 */
	type W = (FileSystem, String)

	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		val (hdfs, dtFullPath) = writeParam.get
		logger.debug(s"$LOG_PREFIX dstPath= $dtFullPath")

		// csv 생성
		this.writeCsvToHdfs(df, dtFullPath)

		// 이름 변경
		val files = hdfs.globStatus(new Path(s"$dtFullPath/*.csv.gz"))
		if (files.length == 1) {
			logger.debug(s"$LOG_PREFIX srcPath= ${files.head.getPath.toString} => dstPath= $dtFullPath/${date}_biz_analysis_report.csv.gz")
			HdfsUtil.rename(hdfs, files.head.getPath.toString, s"$dtFullPath/${date}_biz_analysis_report.csv.gz")
		} else {
			throw new Exception(s"$LOG_PREFIX $dtFullPath 에 csv.gz 파일이 1개가 아님")
		}
	}
}
