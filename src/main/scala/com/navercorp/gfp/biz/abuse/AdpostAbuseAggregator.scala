package com.navercorp.gfp.biz.abuse

import java.util.Date
import scala.collection.JavaConverters._
import scala.collection.immutable.HashMap
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future
import scala.util.Try
import scala.util.control.Breaks.{break, breakable}

import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.spark.sql._
import org.apache.spark.sql.functions.{expr, from_json, lit}
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.joda.time.format.DateTimeFormat

import com.navercorp.gfp.core.BaseEnv.{HADOOP_NAME_SERVICE, HDFS_ADPROVIDER_ABUSE_ROOT}
import com.navercorp.gfp.core.BaseUdf.decimalToString
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.core.{BaseAggregator, BaseEnv, BizAggregator, DeleteFutureHelper}
import com.navercorp.gfp.exception.BusinessException
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil}

/*
	ADPOST 어뷰즈 리포트 생성
	- input path : /user/gfp-data/adprovider_abuse/cq/created=yyyymm
	- output csv : /user/gfp-data/abuse-report/yyyy/mm/publisherId=xxx/gfp_yyyymm.csv
	- IN_NAVER RK AP 를 대상으로 함

    spark 설정
		--num-executors 3
		--executor-cores 3
		--executor-memory 1g
		--conf spark.executor.memoryOverhead=500m
		--conf spark.sql.shuffle.partitions=5
		--conf spark.sql.files.maxPartitionBytes=32mb
*/
object AdpostAbuseAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [ADPOST-ABUSE]"

	private var sparkAppId: String = ""

	private val abuseReportDao = new AbuseReportDao

	// /user/gfp-data/abuse-report
	private val OUTPUT_DIR = HADOOP_NAME_SERVICE + conf.getString("hadoop.nameservice.bizcloud.abuse.path")

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 처리 대상 월
		val yyyymm = args(0)

		try {
			// args 밸리데이션 체크
			// require 는 runtime 시점에 Exception 을 던진다.
			// Exception 없이, 인스턴스 생성 시점에 validation 체크를 하고 싶으면 apply 를 구현하도록 한다.
			//  - https://gist.github.com/jkpl/4932e8730c1810261381851b13dfd29d
			//  - https://www.47deg.com/blog/smart-constructors-in-scala/#smart-constructors-for-case-classes-0
			//  - https://stackoverflow.com/questions/5982484/scala-lift-check-if-date-is-correctly-formatted
			require(Try(DateTimeFormat.forPattern("yyyyMM").parseDateTime(yyyymm)).isSuccess, s"yyyymm($yyyymm) is invalid format (must be yyyyMM)")


			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(yyyymm),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(inProgressHist)

			// [{ publisher_id: ObjectId('xxx'), keyGroup_id: ObjectId('xxx') }, { publisher_id: ObjectId('yyy'), keyGroup_id: ObjectId('yyy') }]
			val adpostAbuseReportConfigList = abuseReportDao.getAdpostAbuseReportConfigList().into(new java.util.ArrayList[AdpostAbuseReportConfig]()).asScala

			if (adpostAbuseReportConfigList.isEmpty) {
				logger.warn(s"$LOG_PREFIX ADPOST 어뷰즈 리포트 생성을 위한 매체-키값그룹 설정 정보가 없음")
				return
			}

			logger.debug(s"adpostAbuseReportConfigList= $adpostAbuseReportConfigList")

			val publisherIds = adpostAbuseReportConfigList.map(conf => conf.publisherId)

			logger.debug(s"$LOG_PREFIX publisherIds= $publisherIds")

			// 집계 인스턴스 생성
			val aggregator = new AdpostAbuseAggregator(adpostAbuseReportConfigList)

			aggregator.init()

			// breakable 을 loop 안에 넣으면 break 가 continue 로 동작하고, loop 밖에 넣으면 break 로 동작한다.
			// 	- https://m.blog.naver.com/jiwon2772/221317139127
			publisherIds.foreach { publisherId =>
				breakable {
					// root= /user/gfp-data/abuse-report/yyyy/mm/publisherId=xxx/
					// tempPath= /user/gfp-data/abuse-report/yyyy/mm/publisherId=xxx/temp
					// finalPath= /user/gfp-data/abuse-report/yyyy/mm/publisherId=xxx/gfp_yyyymm.csv
					// successFilePath= /user/gfp-data/abuse-report/yyyy/mm/publisherId=xxx/_SUCCESS

					val ym = DateTimeFormat.forPattern("yyyyMM").parseDateTime(yyyymm)
					val root = s"$OUTPUT_DIR/${ym.toString("yyyy/MM")}/publisherId=$publisherId"
					val tempPath = s"$root/temp"
					val finalPath = s"$root/gfp_$yyyymm.csv"
					val successFilePath = s"$root/_SUCCESS"

					// abuse 경로 가져오기
					val pathList = getPathList(yyyymm, publisherId)

					// abuse 로그 로딩
					val abuseDf = loadParquetLog(pathList) match {
						case Some(df) => df
						case _ =>
							createSuccessFile(successFilePath)

							logger.warn(s"$LOG_PREFIX publisherId= $publisherId 에 해당하는 HDFS 로그 경로가 없음")

							break
					}

					val aggregatedDf = aggregator.aggregate(Option(abuseDf, publisherId))

					// HDFS - 기존 파일 삭제
					aggregator.delete(aggregator.getFuturesForDelete(Option((hdfs, tempPath, finalPath, successFilePath))))

					// HDFS - CSV 파일 쓰기
					aggregator.write(aggregatedDf, Option((hdfs, tempPath, finalPath)))

					createSuccessFile(successFilePath)
				}
			}

			// 이력쌓기 - 완료 (COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					))
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * abuse 경로 가져오기
	 *
	 * @param yyyymm
	 * @param publisherId
	 * @return pathList
	 */
	def getPathList(yyyymm: String, publisherId: String): Seq[String] = {
		val path: String = s"$HDFS_ADPROVIDER_ABUSE_ROOT/created=$yyyymm/*/*/*/adProviderId=*/publisherId=$publisherId"

		// hdfs 에 존재하는 경로만 따로 추출
		val pathList = if (HdfsUtil.existsPathPattern(hdfs, path)) Seq(path) else Seq()

		logger.debug(s"pathList $pathList")

		pathList
	}

	/**
	 * 로그 로딩
	 *
	 * @param pathList
	 * @return Dataset[Row]
	 */
	override def loadParquetLog(pathList: Seq[String]): Option[Dataset[Row]] = {
		// HDFS 경로가 하나도 없는 경우, None
		if (pathList.isEmpty) {
			None
		} else {
			super.loadParquetLog(pathList)
		}
	}

	/**
	 * SuccessFile 생성
	 *
	 * @param successFile
	 */
	def createSuccessFile(successFile: String): Unit = {
		HdfsUtil.create(hdfs, successFile)
	}
}

/**
 * 데이터 삭제 / 집계 / 추가 등의 작업을 한다.
 *
 * spark-submit에서 호출할 때나 test case에서 호출할 때 사용함
 * 환경에 따라 달라지는 값은 생성자 파라미터로 받아서 처리함.
 *
 * @param adpostAbuseReportConfigList
 */
class AdpostAbuseAggregator(adpostAbuseReportConfigList: Seq[AdpostAbuseReportConfig])(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper {
	val LOG_PREFIX = ".......... [ADPOST-ABUSE]"

	private val abuseReportDao = new AbuseReportDao

	var keysByPubId: HashMap[String, Seq[String]] = new HashMap[String, Seq[String]]

	import spark.implicits._
	// https://www.scala-lang.org/api/2.13.3/scala/collection/JavaConverters$.html


	/**
	 * 집계에 필요한 정보 조회하기
	 */
	override def init(): Unit = {
		// 매체별 키값그룹 Key 목록 정보
		adpostAbuseReportConfigList.foreach(conf => {
			val sortedKey = abuseReportDao.getKeysByPublisherId(conf.publisherId, conf.keyGroupId) match {
				case Some(k) => k.getList("sortedKeys", classOf[String]).asScala
				case None => throw new Exception(s"$LOG_PREFIX $conf 에 해당 하는 키 정보가 없음")
			}
			keysByPubId = keysByPubId + (conf.publisherId -> sortedKey)
		})

		logger.debug(s"$LOG_PREFIX keysByPubId= ${keysByPubId.toString()}")
	}

	/**
	 * sortedKey 조회
	 * 	- 매체별 키그룹에 등록된 키 정보 (생성일 순으로 정렬된 키 목록)
	 *
	 * @return sortedKey
	 */
	def getSortedKey(publisherId: String): Seq[String] = {
		// Apollo :: bid,naverId,spaceId,adMediation,calp,blogGrade,influencerGrade,blogId
		val sortedKey = keysByPubId.get(publisherId) match {
			case Some(sortedKey) => sortedKey
			case _ => throw BusinessException(s"publisherId= $publisherId 에 해당 하는 sortedKey 정보 없음")
		}

		logger.debug(s"$LOG_PREFIX sortedKey= ${sortedKey.toString()}")

		sortedKey
	}

	/**
	 * rsKeyValue 스키마 동적 생성
	 * 	- sortedKey 기반의 스키마 정보
	 *
	 * @return rsKeyValueSchema
	 */
	def getRsKeyValueSchema(sortedKey: Seq[String]): StructType = {
		val rsKeyValueSchema = StructType(sortedKey.map(key => StructField(key, StringType, nullable = true)))

		logger.debug(s"$LOG_PREFIX rsKeyValueSchema= ${rsKeyValueSchema.toString()}")

		rsKeyValueSchema
	}

	/**
	 * SyncAdProviders 콜렉션의 _id, reportApi.type 정보 로드
	 *
	 * @return apMetaDf Dataset[Row]
	 */
	def loadAdProviders(): Dataset[Row] = {
		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdProviders")
		val apMetaDf = spark.read.mongo(readConfig)
			.select(
				$"_id.oid".as("adProviderId"),
				$"reportApi.type".as("reportApiType"),
			)
			.filter("reportApiType IS NOT NULL")

		apMetaDf
	}

	/**
	 * 데이터 집계
	 *
	 * @return finalDf Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		val (abuseDf, publisherId) = aggParam.get

		val groupByList: Seq[String] = Seq("date", "publisherId", "adProviderId", "adUnitId", "rsKeyValue")
		val selectList: Seq[String] = groupByList ++ Seq(
			// decimal 로 cast 를 이미 했기 때문에, SUM 할 때는 따로 처리 안 해줘도 됨
			"cast(netRevenueKRW as decimal(38,18)) AS netRevenueKRW"
		)
		val aggList = Seq(expr("NVL(SUM(netRevenueKRW), 0)").as("revenueKRW"))

		val apMetaDf = loadAdProviders()
		val sortedKey = getSortedKey(publisherId)
		val rsKeyValueSchema = getRsKeyValueSchema(sortedKey)

		// 어뷰즈 로그에서 가져온 데이터
		val finalDf = abuseDf
			.filter(s"publisherId == '$publisherId'")
			.selectExpr(selectList: _*)
			.groupBy(groupByList.head, groupByList.tail: _*)
			.agg(aggList.head, aggList.tail: _*)
			.join(apMetaDf, Seq("adProviderId"), "left_outer")
			// Decimal to String ( stripTrailingZeros 적용 )
			.withColumn("revenueKRW", decimalToString($"revenueKRW"))
			// serviceAmountKRW=0 추가
			.withColumn("serviceAmountKRW", lit(0).cast(StringType))
			.withColumn("rsKvJson", from_json($"rsKeyValue", rsKeyValueSchema))
			.selectExpr("*", "rsKvJson.*")
			.na.fill("", sortedKey)
			// 필드 순서 정렬 및 불필요한 필드 제거
			.selectExpr(Seq("date", "reportApiType", "adUnitId", "revenueKRW", "serviceAmountKRW") ++ sortedKey: _*)

		// logger.debug("finalDf schema >>>>>>")
		// finalDf.printSchema()
		// finalDf.show(30, false)
		// finalDf.explain()

		finalDf
	}

	type A = (Dataset[Row], String)

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	override def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		val (hdfs, tempPath, finalPath, successFilePath) = param.get

		logger.debug(s"$LOG_PREFIX tempPath= $tempPath, finalPath= $finalPath, successFilePath= $successFilePath")

		List(Future {
			HdfsUtil.delete(hdfs, tempPath)
			HdfsUtil.delete(hdfs, finalPath)
			HdfsUtil.delete(hdfs, successFilePath)

			Option(true)
		})
	}

	type T = (FileSystem, String, String, String) // ABSTRACT TYPE MEMBERS :: https://docs.scala-lang.org/tour/abstract-type-members.html

	/**
	 * 데이터 추가
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		val (hdfs, tempPath, finalPath) = writeParam.get

		logger.debug(s"$LOG_PREFIX tempPath= $tempPath, finalPath= $finalPath")

		this.writeCsvToHdfs(df, tempPath)
		this.moveFile(hdfs, tempPath, finalPath)
	}

	type W = (FileSystem, String, String)

	/**
	 * 파일 위치 변경. csv 파일이 1개일 때만 정상 동작
	 * /user/gfp-data/abuse-report/yyyy/mm/publisherId=xxx/temp 하위에 생성된 part-xxxx.csv 파일을
	 * /user/gfp-data/abuse-report/yyyy/mm/publisherId=xxx/gfp_yyyymm.csv 로 위치 변경함
	 *
	 * @param hdfs
	 * @param srcPath
	 * @param destPath
	 */
	override def moveFile(hdfs: FileSystem, srcPath: String, destPath: String): Unit = {
		logger.debug(s"$LOG_PREFIX srcPath=$srcPath, destPath=$destPath")

		val files = hdfs.globStatus(new Path(s"$srcPath/*.csv"))
		if (files.length == 1) {
			val srcFilePath = files.head.getPath.toString

			logger.debug(s"$LOG_PREFIX srcFilePath=$srcFilePath destPath=$destPath")

			val deleteRes = HdfsUtil.delete(hdfs, destPath)
			val renameRes = hdfs.rename(new Path(srcFilePath), new Path(destPath))

			logger.debug(s"$LOG_PREFIX deleteRes=$deleteRes renameRes=$renameRes")
		} else {
			throw new Exception(s"$LOG_PREFIX $srcPath 에 csv 파일이 1개가 아님")
		}
	}
}
