package com.navercorp.gfp.biz.gold

import java.util.Date
import java.util.concurrent.{ExecutorService, ForkJoinPool}
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, Future}

import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import org.apache.hadoop.fs.FileSystem
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{MapType, StringType, StructField, StructType}

import com.navercorp.gfp.biz.gold.AdUnitGoldAggType.AdUnitGoldAggType
import com.navercorp.gfp.biz.gold.AdUnitGoldsmith.LOG_PREFIX
import com.navercorp.gfp.biz.gold.GoldsmithIndexType.GoldsmithIndexType
import com.navercorp.gfp.biz.silver.SilverLogType
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.core.{AdProviderAggregator, BaseEnv, BizAggregator, DeleteFutureHelper}
import com.navercorp.gfp.util.{AggregatorUtil, HdfsUtil, NeloUtil, TimeUtil}

object AdUnitGoldsmith extends Goldsmith {
	val LOG_PREFIX = ".......... [GOLD-AU]"

	override def indexType: GoldsmithIndexType = GoldsmithIndexType.adunit

	val aggregator = new AdUnitGoldsmith()

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 대상 일시 설정
		val ymdh = args(0)

		try {
			logger.debug(s"$LOG_PREFIX target ymdh: $ymdh")

			// 이력쌓기 - IN_PROGRESS
			val sparkDocForInProgress = Spark(
				sparkAppId = Option(spark.sparkContext.applicationId),
				sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
				sparkStartedAt = Option(new Date)
			)
			val inProgressHist = SummaryHistory(
				datetime = Option(ymdh),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(sparkDocForInProgress)
			)
			this.upsertSummaryHistory(inProgressHist)

			aggregator.init()

			makeGoldIndex(ymdh)

			createGoldSuccessFile(ymdh, indexType)

			// 이력쌓기 - COMPLETE
			val sparkDocForComplete = Spark(
				sparkAppState = Option(SparkAppState.COMPLETE.toString),
				sparkEndedAt = Option(new Date)
			)
			val completeHist = SummaryHistory(
				spark = Option(sparkDocForComplete)
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val sparkDocForFailure = Spark(
					sparkAppState = Option(SparkAppState.FAILURE.toString),
					sparkAppError = Option(t.getMessage),
					sparkEndedAt = Option(new Date)
				)
				val failureHist = SummaryHistory(
					spark = Option(sparkDocForFailure)
				)
				this.upsertSummaryHistory(failureHist)
				logger.error(s"$LOG_PREFIX 광고유닛 골드 인덱스 생성 실패. sparkAppId=${spark.sparkContext.applicationId} ymdh=$ymdh summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")

				throw t
		} finally {
			NeloUtil.waitFor()
		}
	}

	def makeGoldIndex(ymdh: String): Unit = {
		// 시간대 디렉토리 삭제
		deleteYmdhPath(ymdh, indexType)

		logger.debug(s"$LOG_PREFIX $ymdh 생성 시작")

		val threadNum = getThreadNum() // 동시 처리 개수
		val forkJoinPool: ExecutorService = new ForkJoinPool(threadNum)
		implicit val ec: ExecutionContext = ExecutionContext.fromExecutorService(forkJoinPool)
		var futures = Vector[Future[Option[Boolean]]]()

		val allPubIds: Vector[String] = publisherDao.getPublisherIds().toVector

		for (pubId <- allPubIds) {
			// 로그 로딩
			val sPath = getSilverPath(ymdh, pubId, SilverLogType.ErSspServer)
			val cPath = getSilverPath(ymdh, pubId, SilverLogType.ErSspClient)

			if (sPath.isEmpty && cPath.isEmpty) {
				logger.warn(s"$LOG_PREFIX 서버 & 클라이언트 실버 로그가 존재하지 않음. sparkAppId=${spark.sparkContext.applicationId} ymdh=$ymdh pubId=$pubId sPaths=$sPath cPaths=$cPath")
			} else { // 둘 중 어느 하나 로그라도 있으면 진행
				val future = Future {
					try {
						process(ymdh, pubId, sPath, cPath)

						// 성공 시, true 리턴
						Option(true)
					} catch {
						case t: Throwable =>
							logger.error(s"$LOG_PREFIX 광고유닛 골드 인덱스 실패. sparkAppId=${spark.sparkContext.applicationId} ymdh=$ymdh pubId=$pubId ${t.getMessage}")
							Option(false) // 실패 시, false 리턴
					}
				}
				futures = futures :+ future
			}
		}

		if (futures.nonEmpty) {
			// 퍼블리셔별 결과 모으기
			val reduced = Future.reduceLeft(futures) { case (accu, value) =>
				Option(accu.getOrElse(false) && value.getOrElse(false))
			}

			// 모든 future 가 끝날 때까지 기다렸다가 결과 받기
			val result = Await.result(reduced, Duration.Inf)

			// 모두 성공적으로 끝났는지 확인
			result match {
				case Some(false) =>
					throw new Exception(s"$ymdh 생성 실패")
				case _ =>
					logger.debug(s"$LOG_PREFIX $ymdh 생성 완료")
			}
		} else {
			throw new Exception(s"$ymdh 생성 대상 없음. futures is empty")
		}
	}

	def process(ymdh: String, pubId: String, sPath: Option[String], cPath: Option[String]): Unit = {
		// 광고유닛 요청수 집계
		if (sPath.isDefined) {
			val serverDf = loadSilverLog(Seq(sPath.get))
			processByType(ymdh, pubId, Option(serverDf), None, AdUnitGoldAggType.aur)
		} else {
			logger.warn(s"$LOG_PREFIX ymdh=$ymdh pubId=$pubId 에 해당하는 서버 실버 로그가 존재하지 않아 광고유닛요청수 집계 생략")
		}

		// 딜리버리 지표 집계
		if (cPath.isDefined) {
			val clientDf = loadSilverLog(Seq(cPath.get))
			processByType(ymdh, pubId, None, Option(clientDf), AdUnitGoldAggType.dlv)
		} else {
			logger.warn(s"$LOG_PREFIX ymdh=$ymdh pubId=$pubId 에 해당하는 클라 실버 로그가 존재하지 않아 딜리버리 지표 집계 생략")
		}
	}

	def processByType(ymdh: String, pubId: String, serverDf: Option[Dataset[Row]], clientDf: Option[Dataset[Row]], aggType: AdUnitGoldAggType): Unit = {
		// 집계
		val aggDf = aggregator.aggregate(Option(AggParam(ymdh, serverDf, clientDf, Option(aggType))))

		// 경로 설정
		val splitYmdh = TimeUtil.getSplitYmdh(ymdh)
		val outputPath = s"${BaseEnv.GOLD_ROOT}/$indexType/${splitYmdh.yyyy}/${splitYmdh.mm}/${splitYmdh.dd}/${splitYmdh.hh}/publisherId=$pubId/$aggType"

		// 기존 데이터 삭제
		aggregator.delete(aggregator.getFuturesForDelete(Option(hdfs, outputPath)))

		// HDFS에 저장
		aggregator.write(aggDf, Option(outputPath))
	}
}

class AdUnitGoldsmith()(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper with AdProviderAggregator {

	import spark.implicits._

	val baseDimensions: Vector[String] = Vector(
		"publisherId",
		"serviceId",
		"adUnitId",
		"country",
		"area1",
		"area2",
		"area3",
		"gender",
		"age",
		"os",
		"osVer",
		"browser",
		"browserVer",
		"device",
	)
	val deliveryDimensions: Vector[String] = baseDimensions :+ "responseCreativeType"

	type A = AggParam

	/**
	 * 광고공급자의 추정수익집계기준(estimatedReportType)
	 *
	 * @return
	 */
	def loadAdProviders(): Dataset[Row] = {
		val oidStructType = StructType(List(StructField("oid", StringType, true)))
		val schema = StructType(List(
			StructField("_id", oidStructType, true),
			StructField("report", StructType(List(
				StructField("estimatedReportType", MapType(StringType, StringType), true),
			)))
		))

		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdProviders")
		val df = spark.read.schema(schema).mongo(readConfig)
			.select(
				$"_id.oid".as("adProviderId"),
				$"report.estimatedReportType"
			)

		applyDefaultEstRptType(df)
	}

	def aggregate(aggParamOption: Option[A]): Dataset[Row] = {
		if (aggParamOption.isEmpty) {
			spark.emptyDataFrame
		} else {
			val aggParam = aggParamOption.get
			val (ymdh, serverDfOpt, clientDfOpt, aggTypeOpt) = (aggParam.ymdh, aggParam.serverDf, aggParam.clientDf, aggParam.aggType)

			var df: Dataset[Row] = null
			if (aggTypeOpt.get == AdUnitGoldAggType.aur) {
				val sDf = prepareServerDf(serverDfOpt.get)
				df = getAdUnitRequestsStats(sDf)
			} else if (aggTypeOpt.get == AdUnitGoldAggType.dlv) {
				val cDf = prepareClientDf(clientDfOpt.get, broadcast(loadAdProviders()))
				df = getDeliveryStats(cDf)
			}

			df.withColumn("ymdh", lit(ymdh))
		}
	}

	/**
	 * 서버 데이터셋에서 필요한 필드만 추출
	 *
	 * @param df
	 * @return
	 */
	def prepareServerDf(df: Dataset[Row]): Dataset[Row] = {
		val df1 = filterTest(df)
			.select(
				"requestId",
				"publisherId",
				"serviceId",
				"adUnitId",
				"country",
				"area1",
				"area2",
				"area3",
				"gender",
				"age",
				"os",
				"osVer",
				"browser",
				"browserVer",
				"device"
			)

		df1
	}

	/**
	 * 광고유닛요청수(adUnitRequests) 집계
	 *
	 * countDistinct("requestId") -> count("requestId")로 기준 변경
	 * 		- https://wiki.navercorp.com/pages/viewpage.action?pageId=**********&focusedCommentId=**********#comment-**********
	 *
	 * countDistinct("requestId") 기준일 경우는 아래처럼 해야 함
	 * 		- 광고유닛요청수는 countDistinct()를 쓰기 때문에 rand를 줘서 중간집계를 하면 지표가 왜곡됨 !!
	 * 		- getDeliveryStats() 처럼 sum을 하는게 아니기 때문에 애초에 구하려는 디멘젼으로 countDistinct()를 해야 함 !!!
	 *
	 * rand를 써서 skewed data를 해결하면 안되고, repartition 정도가 최대의 튜닝
	 *
	 * @param df
	 * @return
	 */
	def getAdUnitRequestsStats(df: Dataset[Row]): Dataset[Row] = {
		val groupByCols = baseDimensions.map(dim => col(dim))

		val df1 = df
			.repartition(groupByCols: _*) // agg 전에 repartition을 해서 연산 시 skew를 미리 해결함
			.groupBy(baseDimensions.head, baseDimensions.tail: _*)
			.agg(
				count("requestId").as("adUnitRequests"), // 광고유닛 요청수
			)

		df1
	}

	/**
	 * 클라이언트 데이터셋에서 필요한 필드만 추출 후
	 *
	 * @param df
	 * @return
	 */
	def prepareClientDf(df: Dataset[Row], apDf: Dataset[Row]): Dataset[Row] = {
		val df1 = filterTest(df)
			.filter($"adProviderId".isNotNull && $"adProviderId" =!= "")
			.selectExpr(
				"requestId",
				AggregatorUtil.selectExprOfIsValid(),
				"eventId",
				"publisherId",
				"serviceId",
				"adUnitId",
				"country",
				"area1",
				"area2",
				"area3",
				"gender",
				"age",
				"os",
				"osVer",
				"browser",
				"browserVer",
				"device",
				"adProviderId",
				"connectionType",
				"responseCreativeType",
				"bidPrice",
			)

		// AdProviders 컬렉션의 estimatedReportType 추가
		// : 로그 내 AP 의 responseCreativeType 에 따른 수익집계 대상이 될 이벤트 기준
		val df2 = df1
			.join(apDf,
				(df1.col("adProviderId") === apDf.col("adProviderId")) &&
					(df1.col("responseCreativeType") === apDf.col("creativeType")), "left")
			.select(
				df1.col("*"),
				apDf.col("creativeType"),
				apDf.col("estimatedReportType")
			)
		df2
	}

	/**
	 * 딜리버리 지표 집계
	 *
	 * @param df
	 * @return
	 */
	def getDeliveryStats(df: Dataset[Row]): Dataset[Row] = {
		val groupByCols = deliveryDimensions.map(dim => col(dim))

		val estimatedImpressions =
			"""SUM(CASE
                 WHEN estimatedReportType == 'FILL' AND eventId == 1 THEN 1
                 WHEN estimatedReportType == 'IMP'  AND eventId == 11 AND isValid == '1' THEN 1
                 WHEN estimatedReportType == 'VIEW' AND eventId == 12 AND isValid == '1' THEN 1
                 ELSE 0 END)"""

		val df1 = df
			.repartition(groupByCols: _*) // agg 전에 repartition을 해서 연산 시 skew를 미리 해결함
			.groupBy(deliveryDimensions.head, deliveryDimensions.tail: _*)
			.agg(
				expr("SUM(CASE WHEN eventId == 1 THEN 1 ELSE 0 END)").as("filledRequests"), // 선출수
				expr("SUM(CASE WHEN (eventId == 11 AND isValid == '1') THEN 1 ELSE 0 END)").as("impressions"), // 노출수
				expr("SUM(CASE WHEN (eventId == 12 AND isValid == '1') THEN 1 ELSE 0 END)").as("viewableImpressions"), // 유효노출수
				expr(estimatedImpressions).as("estimatedImpressions"),
				expr("SUM(CASE WHEN (eventId == 3  AND isValid == '1') THEN 1 ELSE 0 END)").as("clicks"), // 클릭수
				expr("SUM(CASE WHEN (eventId == 4) THEN 1 ELSE 0 END)").as("completions"), // 보상수
				expr("SUM(CASE WHEN (eventId == 5) THEN 1 ELSE 0 END)").as("adMute"), // 광고끈수
				expr("SUM(CASE WHEN eventId == 1   AND connectionType == 'S2S' THEN bidPrice ELSE 0 END)").as("bidPriceSumBasedOnFilledRequests"), // 선출 기준 비딩가 합
				expr("SUM(CASE WHEN eventId == 11  AND connectionType == 'S2S' AND isValid == '1' THEN bidPrice ELSE 0 END)").as("bidPriceSumBasedOnImpressions") // 노출 기준 비딩가 합
			)

		df1
	}

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */

	import scala.concurrent.ExecutionContext.Implicits.global

	type T = (FileSystem, String) // ABSTRACT TYPE MEMBERS :: https://docs.scala-lang.org/tour/abstract-type-members.html

	override def getFuturesForDelete(helpableParam: Option[T] = None): List[Future[Option[Boolean]]] = {
		val (hdfs, path) = helpableParam.get

		List(Future {
			val result = HdfsUtil.delete(hdfs, path)
			logger.debug(s"$LOG_PREFIX 삭제 결과 $path ($result)")

			Option(true)
		})
	}

	/**
	 * HDFS에 쓰기
	 *
	 * @param df
	 * @return
	 */
	type W = String

	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		val outputPath = writeParam.get

		val c = spark.conf.get("spark.executor.cores").toInt
		val i = spark.conf.get("spark.executor.instances").toInt
		val parallelism = c * i * 4
		logger.debug(s"$LOG_PREFIX parallelism:$parallelism")

		df
			.coalesce(parallelism)
			.write
			.mode("overwrite")
			.option("dfs.blocksize", 64 * 1024 * 1024)
			.option("parquet.block.size", 64 * 1024 * 1024)
			.parquet(outputPath)

		logger.debug(s"$LOG_PREFIX HDFS 쓰기 완료. $outputPath")
	}
}

/*
[ hadoop 파일 개수 및 용량 확인 ]
hadoop fs -count -h hdfs://tesseract-dev/data/log/ssp/gfp-silver/er-ssp-client/20220118-*
    -count : DIR_COUNT, FILE_COUNT, CONTENT_SIZE FILE_NAME 을 보여줌
    -count -q : QUOTA, REMAINING_QUATA, SPACE_QUOTA, REMAINING_SPACE_QUOTA, DIR_COUNT, FILE_COUNT, CONTENT_SIZE, FILE_NAME 을 보여줌
    -h : Show sizes human readable format


[ 당첨된 실행 환경 ]
spark-submit --class %aggregator% ^
	--name %aggregator%-%dateTime%-%execTime% ^
	--executor-cores 2 ^
	--num-executors 12 ^
	--executor-memory 4g ^
	--conf spark.driver.memoryOverhead=1g ^
	--conf spark.sql.shuffle.partitions=240 ^
	--conf spark.sql.files.maxPartitionBytes=128mb ^
	--conf spark.scheduler.mode=FAIR ^
 	--conf spark.scheduler.allocation.file=hdfs://BizCloud/data/log/gfp/fairscheduler.xml ^
 	--conf spark.scheduler.pool=gold_index ^
	--deploy-mode cluster ^
	--master yarn ^
	--queue root.users.gfp ^
	--conf spark.driver.extraJavaOptions=-XX:+UseG1GC ^
	--conf spark.executor.extraJavaOptions=-XX:+UseG1GC ^
	--conf spark.eventLog.enabled=true ^
	--conf spark.sql.caseSensitive=true ^
	--conf spark.sql.parquet.mergeSchema=true ^
	--conf spark.serializer=org.apache.spark.serializer.KryoSerializer ^
	--conf spark.yarn.archive=hdfs://BizCloud/user/irteam/spark3LibArchives/spark-3.2.1-libs.jar ^
	%sparkling_home%/jar/sparkling-s.jar %dateTime%

[ 소요시간 ]
리얼 - 6분

 */
