package com.navercorp.gfp.biz.settlement

import java.util.Date
import scala.concurrent.Future
import scala.util.Try

import org.apache.spark.sql._
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, Period}

import com.navercorp.gfp.core.BaseConstant.DECIMAL_TYPE_17_6
import com.navercorp.gfp.core.BaseEnv._
import com.navercorp.gfp.core.BaseSchema.{SILVERGREY_SCHEMA, ZIRCON_B_GFP_SCHEMA, ZIRCON_B_SCHEMA}
import com.navercorp.gfp.core.BaseUdf.toObjectId
import com.navercorp.gfp.core.DataSource.DataSource
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.core.{BaseAggregator, BizAggregator, DataSource, DeleteFutureHelper}
import com.navercorp.gfp.exception.BusinessException
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, TimeUtil}


/*
	정산 데이터를 MongoDB 에 저장
	- input : silvergrey, ZBGFP, ZB
		- /user/gfp-data/silvergrey
		- /user/gfp-data/zircon/b/gfp/warehouse
		- /user/gfp-data/zircon/b/warehouse
	- output DB : SettlementMonthly

	spark 설정
		--num-executors 30
		--executor-cores 3
		--executor-memory 3g
		--conf spark.sql.shuffle.partitions=400
		--conf spark.sql.files.maxPartitionBytes=32mb
*/
object SettlementMonthlyAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [SETTLEMENT-MONTHLY]"

	private var sparkAppId: String = ""

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 처리 대상 월
		val month = args(0)

		try {
			// args 밸리데이션 체크
			// require 는 runtime 시점에 Exception 을 던진다.
			// Exception 없이, 인스턴스 생성 시점에 validation 체크를 하고 싶으면 apply 를 구현하도록 한다.
			//  - https://gist.github.com/jkpl/4932e8730c1810261381851b13dfd29d
			//  - https://www.47deg.com/blog/smart-constructors-in-scala/#smart-constructors-for-case-classes-0
			//  - https://stackoverflow.com/questions/5982484/scala-lift-check-if-date-is-correctly-formatted
			require(Try(DateTimeFormat.forPattern("yyyyMM").parseDateTime(month)).isSuccess, s"month($month) is invalid format (must be yyyyMM)")

			logger.debug(s"$LOG_PREFIX month= $month")

			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(month),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
			)
			this.upsertSummaryHistory(inProgressHist)

			// checkpointPath= /user/gfp-data/settlement/checkpoint/month={month}
			val checkpointPath = s"$HADOOP_NAME_SERVICE/user/gfp-data/settlement/checkpoint/month=$month"

			logger.debug(s"checkpointPath= $checkpointPath")

			// 집계 인스턴스 생성
			val aggregator = new SettlementMonthlyAggregator()

			val format = DateTimeFormat.forPattern("yyyyMM")
			val startDate = format.parseDateTime(month).withDayOfMonth(1)
			val endDate = format.parseDateTime(month).dayOfMonth().withMaximumValue()
			val dtRange = dateTimeRange(startDate, endDate, Period.days(8)).toList
			val checkpointedDf = dtRange.map { dt =>
				val start = dt.toString("yyyyMMdd")
				val end = if (dt.plus(Period.days(7)).isBefore(endDate)) dt.plus(Period.days(7)).toString("yyyyMMdd") else endDate.toString("yyyyMMdd")

				// SILVERGREY 로딩
				val sgPathList = getPathList(start, end, DataSource.SILVERGREY)
				val sgDf = if (sgPathList.nonEmpty) {
					loadParquetLog(sgPathList, SILVERGREY_SCHEMA) match {
						case Some(df) => df
						case _ => spark.createDataFrame(spark.sparkContext.emptyRDD[Row], SILVERGREY_SCHEMA)
					}
				} else spark.createDataFrame(spark.sparkContext.emptyRDD[Row], SILVERGREY_SCHEMA)

				// ZBGFP 로딩
				val zbgfpPathList = getPathList(start, end, DataSource.ZBGFP)
				val zbgfpDf = if (zbgfpPathList.nonEmpty) {
					loadParquetLog(zbgfpPathList) match {
						case Some(df) => df
						case _ => spark.createDataFrame(spark.sparkContext.emptyRDD[Row], ZIRCON_B_GFP_SCHEMA)
					}
				} else spark.createDataFrame(spark.sparkContext.emptyRDD[Row], ZIRCON_B_GFP_SCHEMA)

				// ZB 로딩
				val zbPathList = getPathList(start, end, DataSource.ZB)
				val zbDf = if (zbPathList.nonEmpty) {
					loadParquetLog(zbPathList) match {
						case Some(df) => df
						case _ => spark.createDataFrame(spark.sparkContext.emptyRDD[Row], ZIRCON_B_SCHEMA)
					}
				} else spark.createDataFrame(spark.sparkContext.emptyRDD[Row], ZIRCON_B_SCHEMA)

				aggregator.checkpoint(Option(sgDf, zbgfpDf, zbDf, start, end, checkpointPath))
			}.reduce(_ unionAll _)

			val aggregatedDf = aggregator.aggregate(Option(checkpointedDf))

			// 기존 데이터 삭제
			aggregator.delete(aggregator.getFuturesForDelete(Option(month)))

			// MongoDB에 저장
			aggregator.write(aggregatedDf)

			// HDFS - 체크포인트 삭제
			HdfsUtil.delete(hdfs, checkpointPath)

			// 이력쌓기 - 완료 (COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					))
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * month 에 해당하는 경로 가져오기
	 *
	 * @param startDate
	 * @param endDate
	 * @param dataType (SILVERGREY, ZBGFP, ZB)
	 * @return pathList
	 */
	def getPathList(startDate: String, endDate: String, dataType: DataSource): Seq[String] = {
		val format = DateTimeFormat.forPattern("yyyyMMdd")
		val dtRange = dateTimeRange(format.parseDateTime(startDate), format.parseDateTime(endDate), Period.days(1)).toList

		// 타임존 적용 시
		// val dtRange = dateTimeRange(format.parseDateTime(startDate).minus(Period.days(1)), format.parseDateTime(endDate).plus(Period.days(1)), Period.days(1)).toList

		val pathList = dtRange.map { dt =>
			// 참고 :: 마지막 하위 경로까지 풀어서 써야, Spark Stage 에서 한 단계로 처리됨
			val path = if (dataType.equals(DataSource.SILVERGREY)) {
				val RK = conf.getString("hadoop.nameservice.bizcloud.silvergrey.rk.path").replace("/", "")
				val NONRK = conf.getString("hadoop.nameservice.bizcloud.silvergrey.nonrk.path").replace("/", "")

				// /user/gfp-data/silvergrey/{rk,nonrk}/yyyy/MM/dd/adProviderId=*/publisherId=*
				s"$SILVERGREY_ROOT/{$RK,$NONRK}/${dt.toString("yyyy/MM/dd")}/adProviderId=*/publisherId=*"
			} else if (dataType.equals(DataSource.ZBGFP)) {
				// /user/gfp-data/zircon/b/gfp/warehouse/yyyy/MM/dd/*/_publisherId=*/_adProviderId=*
				s"$ZIRCON_B_GFP_WAREHOUSE_PATH/${dt.toString("yyyy/MM/dd")}/*/_publisherId=*/_adProviderId=*"
			} else {
				// /user/gfp-data/zircon/b/warehouse/yyyy/MM/dd/*/_publisherId=*/_adProviderId=*
				s"$ZIRCON_B_WAREHOUSE_PATH/${dt.toString("yyyy/MM/dd")}/*/_publisherId=*/_adProviderId=*"
			}

			// hdfs 에 존재하는 경로만 따로 추출
			if (HdfsUtil.existsPathPattern(hdfs, path)) path else ""
		}.filter(_.nonEmpty)

		logger.debug(s"pathList $pathList")

		pathList
	}
}

/**
 * 데이터 삭제 / 집계 / 추가 등의 작업을 한다.
 *
 * spark-submit에서 호출할 때나 test case에서 호출할 때 사용함
 * 환경에 따라 달라지는 값은 생성자 파라미터로 받아서 처리함.
 */
class SettlementMonthlyAggregator()(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper {
	val LOG_PREFIX = ".......... [SETTLEMENT-MONTHLY]"

	COLLECTION_NAME = "SettlementMonthly"

	import spark.implicits._

	val baseList: Seq[String] = Seq("date", "publisherId", "adProviderId")
	val weightBaseList: Seq[String] = baseList ++ Seq("placeKey", "dealId")

	val dimensionList: Seq[String] = Seq("month", "publisherId", "adProviderId", "serviceId")
	val metricList: Seq[String] = Seq("adjustedKrwRevenue", "adjustedUsdRevenue", "krwRevenue", "usdRevenue")

	// executors * cores = 30 * 3 = 90
	val COALESCE_CNT = 90

	def checkpoint(param: Option[C] = None): Dataset[Row] = {
		val (sgDf, zbgfpDf, zbDf, startDate, endDate, checkpointPath) = param.get

		val checkpointDir = s"$checkpointPath/${startDate}_$endDate"
		spark.sparkContext.setCheckpointDir(checkpointDir)

		logger.debug(s"$LOG_PREFIX checkpointDir= $checkpointDir")

		// 1. SILVERGREY 일단위 집계 ( 비중 대상 )
		// 	- DIM : date, publisherId, adProviderId, placeKey, dealId
		// 	- MTR : apKrwRevenue(= krwSales), apUsdRevenue(= usdSales)
		val sgMetricList = Seq(("krwSales", "apKrwRevenue"), ("usdSales", "apUsdRevenue"))
		val sgSelectList = baseList ++ Seq(
			"adProviderPlaceKey as placeKey",
			"CAST(COALESCE(krwSales, salesKrw) AS DECIMAL(15, 6)) AS krwSales",
			"CAST(COALESCE(usdSales, salesUsd) AS DECIMAL(15, 6)) AS usdSales"
		) ++ Seq(if (sgDf.columns.contains("dealId")) "NVL(dealId, '-') AS dealId" else "'-' AS dealId")
		val sgAggList = sgMetricList.map(metric => expr(s"SUM(${metric._1})").as(metric._2))
		val sgAggDf = sgDf
			.selectExpr(sgSelectList: _*)
			.groupBy(weightBaseList.head, weightBaseList.tail: _*)
			.agg(sgAggList.head, sgAggList.tail: _*)

		// 2. ZBGFP 월단위 집계 ( 비중 기준 )
		// 	- DIM : date, publisherId, adProviderId, placeKey, dealId, serviceId
		// 	- MTR : gfpEstimatedSalesWeight
		val zbgfpMetricList = Seq("gfpEstimatedSalesWeight")
		val zbgfpGroupByList = weightBaseList ++ Seq("serviceId")
		val zbgfpSelectList = zbgfpGroupByList ++ zbgfpMetricList
		val zbgfpAggList = zbgfpMetricList.map(metric => expr(s"SUM($metric)").as(metric))
		val weightDf = zbgfpDf.filter("adProviderId != '-'")
			.withColumn("date", date_format($"apTimestamp", "yyyyMMdd"))
			.selectExpr(zbgfpSelectList: _*)
			.groupBy(zbgfpGroupByList.head, zbgfpGroupByList.tail: _*)
			.agg(zbgfpAggList.head, zbgfpAggList.tail: _*)

		// 3. ZBGFP 기준으로 SILVERGREY 비중 분배 적용
		// 	- DIM : month, publisherId, adProviderId, serviceId
		// 	- MTR : adjustedKrwRevenue, adjustedUsdRevenue
		val weightedSgAggDf = applyWeight(sgAggDf, weightDf)

		// 4. ZB 월별 집계
		// 	- DIM : month, publisherId, adProviderId, serviceId
		// 	- MTR : krwRevenue(= apKrwSales), usdRevenue(= apUsdSales)
		val zbMetricList = Seq(("apKrwSales", "krwRevenue"), ("apUsdSales", "usdRevenue"))
		val zbSelectList = dimensionList ++ zbMetricList.map(_._1)
		val zbAggList = zbMetricList.map(metric => expr(s"SUM(${metric._1})").as(metric._2))
		val zbAggDf = zbDf.filter("adProviderId != '-'")
			.withColumn("month", date_format($"apTimestamp", "yyyyMM"))
			.selectExpr(zbSelectList: _*)
			.groupBy(dimensionList.head, dimensionList.tail: _*)
			.agg(zbAggList.head, zbAggList.tail: _*)
			// 지표가 모두 0인 경우 제외 처리
			.filter(s"NOT(${zbMetricList.map(_._2).map(mtr => s"$mtr == 0").mkString(" AND ")})")

		// 5. SILVERGREY & ZB 머지
		// 	- DIM : month, publisherId, adProviderId, serviceId
		// 	- MTR : adjustedKrwRevenue, adjustedUsdRevenue, krwRevenue, usdRevenue
		val checkpointedDf = merge(weightedSgAggDf, zbAggDf)
			// 지표가 모두 0인 경우 제외 처리
			.filter(s"NOT(${metricList.map(mtr => s"$mtr == 0").mkString(" AND ")})")
			.coalesce(COALESCE_CNT).checkpoint()

		// logger.debug("checkpointedDf schema >>>>>>")
		// checkpointedDf.printSchema()
		// checkpointedDf.show(100, truncate = false)

		checkpointedDf
	}

	type C = (Dataset[Row], Dataset[Row], Dataset[Row], String, String, String)

	/**
	 * 데이터 집계
	 *
	 * @return Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		val unionDf = aggParam.get

		// 월단위 재집계
		// 	- DIM : month, publisherId, adProviderId, serviceId
		// 	- MTR : adjustedKrwRevenue, adjustedUsdRevenue, krwRevenue, usdRevenue
		val aggList = metricList.map(metric => expr(s"SUM($metric)").as(metric))
		val finalDf = unionDf
			.groupBy(dimensionList.head, dimensionList.tail: _*)
			.agg(aggList.head, aggList.tail: _*)
			// 생성한 날짜
			.withColumn("createdAt", functions.current_timestamp)
			// MongoDB TTL Index 에 의해 삭제될 날짜
			.withColumn("expiredAt", TimeUtil.getExpiredAtMonthlyAsColumn(to_date($"month", "yyyyMM")))
			// 몽고디비 ObjectId 타입으로 변환
			.withColumn("publisher_id", toObjectId($"publisherId"))
			.withColumn("adProvider_id", toObjectId($"adProviderId"))
			// 불필요한 필드 제거
			.drop("publisherId", "adProviderId")

		// logger.debug("finalDf schema >>>>>>")
		// finalDf.printSchema()
		// finalDf.show(100, truncate = false)

		finalDf
	}

	type A = Dataset[Row]


	/**
	 * ZBGFP 기준으로 SILVERGREY 비중 분배 적용
	 *
	 * @param sgDf     date, publisherId, adProviderId, placeKey, dealId, apKrwRevenue, apUsdRevenue
	 * @param weightDf date, publisherId, adProviderId, placeKey, dealId, serviceId, gfpEstimatedSalesWeight
	 * @return weightedSgDf 	month, publisherId, adProviderId, serviceId, adjustedKrwRevenue, adjustedUsdRevenue
	 */
	def applyWeight(sgDf: Dataset[Row], weightDf: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX SILVERGREY 수익 비중 분배 적용 ( weightBaseList= $weightBaseList )")

		val metricList = Seq("adjustedKrwRevenue", "adjustedUsdRevenue")
		val aggList = metricList.map(metric => expr(s"SUM($metric)").as(metric))

		// date, publisherId, adProviderId, placeKey, dealId
		val commonWin = Window.partitionBy(weightBaseList.head, weightBaseList.tail: _*)

		val weightedSgDf = sgDf.as("sg").join(weightDf.as("w"), weightBaseList, "full")
			// 수익 비중이 없을 경우, 분배율 1로 설정 (AP의 지표를 그대로 사용)
			.na.fill(1L, Seq("gfpEstimatedSalesWeight"))

			/* 비중 분배된 AP의 KRW 수익 */
			// 소수점 자리수
			.withColumn("apKrwFractionalPartLen", when($"apKrwRevenue" === 0, 0).otherwise(length($"apKrwRevenue".cast("string").substr(instr($"apKrwRevenue", ".") + 1, length($"apKrwRevenue")))))
			// 수익을 정수로 변환 하기 위해 곱해야할 값
			.withColumn("apKrwVolume", pow(10, $"apKrwFractionalPartLen"))
			// 수익을 정수로 변환
			.withColumn("apKrwRevenueInt", $"apKrwRevenue" * $"apKrwVolume")
			// 비중 분배 정수부 ( 소수부 버림 처리 )
			.withColumn("weightedApKrwRevenueInt", floor($"apKrwRevenueInt" * $"gfpEstimatedSalesWeight"))
			// 비중 분배 정수부 총합
			.withColumn("weightedApKrwRevenueSumInt", sum($"weightedApKrwRevenueInt").over(commonWin))
			// AP 수익 총합과 비중 분배 정수부 수익 총합의 차이 ( 남은 금액을 추가로 분배 하기 위함 )
			.withColumn("apKrwRevenueDiff", $"apKrwRevenueInt" - $"weightedApKrwRevenueSumInt")
			// 비중 분배 소수부
			.withColumn("apKrwRevenueRest", expr("CASE WHEN weightedApKrwRevenueInt == 0 THEN (apKrwRevenueInt * gfpEstimatedSalesWeight) ELSE (apKrwRevenueInt * gfpEstimatedSalesWeight) % weightedApKrwRevenueInt END"))
			// 소수부가 높은 순으로 번호 매기기
			.withColumn("apKrwRevenueRestRn", row_number.over(commonWin.orderBy($"apKrwRevenueRest".desc)))
			// 소수부가 높은 순으로 남은 금액 분배
			.withColumn("apRefinedKrwRevenue", expr("CASE WHEN apKrwRevenueDiff > 0 AND apKrwRevenueRestRn <= apKrwRevenueDiff THEN weightedApKrwRevenueInt + 1 ELSE weightedApKrwRevenueInt END"))
			// 소수점 7째 자리에서 반올림
			.withColumn("adjustedKrwRevenue", ($"apRefinedKrwRevenue" / $"apKrwVolume").cast(DECIMAL_TYPE_17_6))

			/* 비중 분배된 AP의 USD 수익 */
			// 소수점 자리수
			.withColumn("apUsdFractionalPartLen", when($"apUsdRevenue" === 0, 0).otherwise(length($"apUsdRevenue".cast("string").substr(instr($"apUsdRevenue", ".") + 1, length($"apUsdRevenue")))))
			// 수익을 정수로 변환 하기 위해 곱해야할 값
			.withColumn("apUsdVolume", pow(10, $"apUsdFractionalPartLen"))
			// 수익을 정수로 변환
			.withColumn("apUsdRevenueInt", $"apUsdRevenue" * $"apUsdVolume")
			// 비중 분배 정수부 ( 소수부 버림 처리 )
			.withColumn("weightedApUsdRevenueInt", floor($"apUsdRevenueInt" * $"gfpEstimatedSalesWeight"))
			// 비중 분배 정수부 총합
			.withColumn("weightedApUsdRevenueSumInt", sum($"weightedApUsdRevenueInt").over(commonWin))
			// AP 수익 총합과 비중 분배 정수부 수익 총합의 차이 ( 남은 금액을 추가로 분배 하기 위함 )
			.withColumn("apUsdRevenueDiff", $"apUsdRevenueInt" - $"weightedApUsdRevenueSumInt")
			// 비중 분배 소수부
			.withColumn("apUsdRevenueRest", expr("CASE WHEN weightedApUsdRevenueInt == 0 THEN (apUsdRevenueInt * gfpEstimatedSalesWeight) ELSE (apUsdRevenueInt * gfpEstimatedSalesWeight) % weightedApUsdRevenueInt END"))
			// 소수부가 높은 순으로 번호 매기기
			.withColumn("apUsdRevenueRestRn", row_number.over(commonWin.orderBy($"apUsdRevenueRest".desc)))
			// 소수부가 높은 순으로 남은 금액 분배
			.withColumn("apRefinedUsdRevenue", expr("CASE WHEN apUsdRevenueDiff > 0 AND apUsdRevenueRestRn <= apUsdRevenueDiff THEN weightedApUsdRevenueInt + 1 ELSE weightedApUsdRevenueInt END"))
			// 소수점 7째 자리에서 반올림
			.withColumn("adjustedUsdRevenue", ($"apRefinedUsdRevenue" / $"apUsdVolume").cast(DECIMAL_TYPE_17_6))

			// 수익 비중이 없는 경우, serviceId 를 - 로 설정
			.na.fill("-", Seq("serviceId"))
			.withColumn("month", substring($"date", 1, 6))

			// 월별 집계
			.selectExpr(dimensionList ++ metricList: _*)
			.groupBy(dimensionList.head, dimensionList.tail: _*)
			.agg(aggList.head, aggList.tail: _*)

		// logger.debug("weightedSgDf schema >>>>>>")
		// weightedSgDf.printSchema()
		// weightedSgDf.show(100, truncate = false)

		weightedSgDf
	}

	/**
	 * SILVERGREY & ZB 머지
	 *
	 * @param sgDf month, publisherId, adProviderId, serviceId, adjustedKrwRevenue, adjustedUsdRevenue
	 * @param zbDf month, publisherId, adProviderId, serviceId, krwRevenue, usdRevenue
	 * @return mergedDf 	month, publisherId, adProviderId, serviceId, adjustedKrwRevenue, adjustedUsdRevenue, krwRevenue, usdRevenue
	 */
	def merge(sgDf: Dataset[Row], zbDf: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX SILVERGREY & ZB 머지")

		val mergedDf = sgDf.join(zbDf, dimensionList, "full")
			.na.fill(0, metricList)

		mergedDf
	}

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		val month = param.get

		// DB 삭제
		super.getFuturesForDeleteByMonth(COLLECTION_NAME, month)
	}

	type T = String // ABSTRACT TYPE MEMBERS :: https://docs.scala-lang.org/tour/abstract-type-members.html

	/**
	 * 데이터 추가
	 *
	 * @param df
	 * @param writeParam
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		logger.debug(s"$LOG_PREFIX COLLECTION_NAME=$COLLECTION_NAME")

		val writeOptions = mdbDefaultWriteOptions
			.updated("spark.mongodb.output.collection", COLLECTION_NAME)
			// MongoDB 콜렉션의 샤드키를 의미하는게 아님. DataFrame 내에서의 유니크한 키를 의미함
			.updated("spark.mongodb.output.shardKey", "{ month:1, publisher_id:1, adProvider_id:1, serviceId:1 }")

		super.writeToMongoDB(df, writeOptions)
	}

	def write2(df: DataFrame, writeParam: Option[W] = None): Unit = {
		logger.debug(s"$LOG_PREFIX COLLECTION_NAME=$COLLECTION_NAME")

		val targetDf = df

		val csvpath = s"$HADOOP_NAME_SERVICE/user/gfp-data/temp/bitna/settlement/${DateTime.now().toString("yyyyMMddHHmmSS")}"
		targetDf.coalesce(1)
			.write
			.mode("overwrite")
			.option("header", true)
			.option("encoding", "UTF-8")
			.option("compression", "none")
			.csv(csvpath)
	}
}
