/**
 * java에서 mongodb 사용하는 법
 * https://mongodb.github.io/mongo-java-driver/
 * [p]
 * Mongo Java API Documentaton
 * https://mongodb.github.io/mongo-java-driver/4.0/apidocs/
 * [p]
 * Bson API Documentation
 * https://api.mongodb.com/java/3.0/index.html?org/bson/package-summary.html
 * [p]
 * MongoClient 인스턴스는 하나만 생성하고 이것을 전체 어플리케이션에서 사용해야 한다.
 * https://mongodb.github.io/mongo-java-driver/4.1/driver/getting-started/quick-start/#make-a-connection
 * The MongoClient instance represents a pool of connections to the database you will only need one instance of class MongoClient even with multiple threads.
 */
package com.navercorp.gfp.biz.revenuesharing

import com.mongodb.BasicDBObject
import com.mongodb.client.model.Filters
import com.mongodb.client.model.Updates.{combine, set}
import com.mongodb.client.{DistinctIterable, FindIterable, MongoCollection}
import com.navercorp.gfp.core.database.{CmsDatabase, Database}
import com.navercorp.gfp.meta.adprovider.AdProvider
import org.apache.logging.log4j.{LogManager, Logger}
import org.bson.Document
import org.bson.conversions.Bson
import org.bson.types.ObjectId

import scala.collection.JavaConverters._
import scala.collection.immutable.HashMap

/*
Mongo Java API Documentaton
	- https://mongodb.github.io/mongo-java-driver/4.8/apidocs/
Bson API Documentation
	- https://api.mongodb.com/java/3.0/index.html?org/bson/package-summary.html
*/

class RevenueSharingDao {
	private val logger: Logger = LogManager.getLogger(this.getClass)

	def getAdProviderTimezoneMap: HashMap[String, String] = {
		var timezoneMap: HashMap[String, String] = new HashMap[String, String]

		val collection: MongoCollection[Document] = Database.getDatabase().getCollection("SyncAdProviders")
		val aps: FindIterable[AdProvider] = collection.find(classOf[AdProvider])
		aps.forEach(ap => {
			timezoneMap = timezoneMap + (ap._id.toHexString -> ap.timezone.get)
		})

		timezoneMap.foreach(m => logger.info("----------------------- adProviderId: " + m._1 + "  timezone: " + m._2))
		timezoneMap
	}

	/**
	 * SummaryRevenueSharingSchedules.status = 'ON'인 매체만
	 *
	 * @return
	 */
	def getPublisherIdsToAggregate(): Vector[String] = {
        val coll: MongoCollection[Document] = CmsDatabase.getDatabase().getCollection("SummaryRevenueSharingSchedules")
        val publisher_ids: DistinctIterable[ObjectId] = coll.distinct("publisher_id", Filters.eq("status", "ON"), classOf[ObjectId])

        val publisherIdList = publisher_ids.map(sch => sch.toHexString).into(new java.util.ArrayList[String]()).asScala.toVector
        publisherIdList


		// 5b8f669428b373001fe56ea8(네이버서비스)
		// 5b5836acc951958e829dd696(네이버소상공인)
		// 5fbdb7c779012500176c9993(LINETODAY_TH)
//		Vector("5b8f669428b373001fe56ea8", "5b5836acc951958e829dd696", "5fbdb7c779012500176c9993")
	}

	/**
	 * SummaryRevenueSharingGfpStatsSchedules에서 date=${dt}에 해당하는 스케줄 한 건 가져오기
	 *
	 * @return
	 */
	def getGfpStatsScheduleByDate(dt: String): RevenueSharingGfpStatsSchedule = {
		val coll: MongoCollection[RevenueSharingGfpStatsSchedule] = CmsDatabase.getDatabase().getCollection("SummaryRevenueSharingGfpStatsSchedules", classOf[RevenueSharingGfpStatsSchedule])
		val schedule: RevenueSharingGfpStatsSchedule = coll.find(Filters.eq("date", dt)).first()
		schedule
	}

	/**
	 * SummaryRevenueSharingGfpStatsSchedules 상태, 에러내용, 시작일시, 종료일시 변경
	 *
	 * @param schedule
	 */
	def updateSparkAppState(schedule: RevenueSharingGfpStatsSchedule): Unit = {
		val coll: MongoCollection[Document] = CmsDatabase.getDatabase().getCollection("SummaryRevenueSharingGfpStatsSchedules")

		var update: Bson = null
		for (field <- schedule.getClass.getDeclaredFields) {
			if (field.getName != "_id") {
				field.setAccessible(true)
				val value = field.get(schedule)
				value match {
					case Some(s) => {
						if (Option(update).isDefined) {
							update = combine(update, set(field.getName, s))
						} else {
							update = set(field.getName, s)
						}
					} // 값이 있는 경우만 update
					case None =>
				}
			}
		}

		coll.updateOne(Filters.eq("_id", schedule._id), update)
	}
}
