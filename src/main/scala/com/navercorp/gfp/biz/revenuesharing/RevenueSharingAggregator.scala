/**
 * 설계서: https://wiki.navercorp.com/pages/viewpage.action?pageId=524918720#id-03.%EB%B9%84%EC%A7%80%EB%8B%88%EC%8A%A4%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8-BizCloudDruid
 *
 * Apache Commons Lang JAVA Doc
 * https://commons.apache.org/proper/commons-lang/apidocs/index.html
 *
 * 웍스 알림 연동: https://wiki.navercorp.com/pages/viewpage.action?pageId=525441819
 */
package com.navercorp.gfp.biz.revenuesharing

import java.time.format.DateTimeFormatter
import java.time.{LocalDate, LocalDateTime}
import java.util.Date
import scala.collection.mutable.ArrayBuffer
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._

import com.navercorp.gfp.biz.revenuesharing.RevenueSharingAggregator.{DAILY_DIR, DATE_TYPE_DAILY, LOG_PREFIX, MONTHLY_DIR, rsDao}
import com.navercorp.gfp.core.BaseEnv.SILVER_CLIENT_INPUT_PATH
import com.navercorp.gfp.core.BaseUdf.getDate
import com.navercorp.gfp.core._
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.util.{AggregatorUtil, HdfsUtil, NeloUtil}

object RevenueSharingAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [RVN-SHARING-GFP-STATS]"
	private val rsDao = new RevenueSharingDao()

	// /user/gfp-data/revenue-sharing-report-v2
	private val OUTPUT_DIR = conf.getString("hadoop.nameservice.bizcloud.rs.path")

	private val DAILY_DIR = s"$OUTPUT_DIR/daily"
	private val MONTHLY_DIR = s"$OUTPUT_DIR/monthly"

	private val DATE_TYPE_DAILY = 1
	private val DATE_TYPE_MONTHLY = 2

	def main(rawArgs: Array[String]): Unit = {
		rawArgs.foreach(a => logger.debug(s"rawArg = $a"))

		val args = initArgs(rawArgs)
		args.foreach(a => logger.debug(s"after initArgs() arg = $a"))

		// 날짜 설정(일 또는 월)
		val dt = args(0)

		var schedule: RevenueSharingGfpStatsSchedule = null

		try {
			logger.debug(s"$LOG_PREFIX target dt: $dt")

			// 이력쌓기 - IN_PROGRESS
			val sparkDocForInProgress = Spark(
				sparkAppId = Option(spark.sparkContext.applicationId),
				sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
				sparkStartedAt = Option(new Date)
			)
			val inProgressHist = SummaryHistory(
				datetime = Option(dt),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(sparkDocForInProgress)
			)
			this.upsertSummaryHistory(inProgressHist)

			// SummaryRevenueSharingSchedules.status = 'ON'인 매체만으로 구성
			val pubIds: Vector[String] = rsDao.getPublisherIdsToAggregate()
			println(s"$LOG_PREFIX pubIds=$pubIds")

			// dt에 해당하는 스케줄 조회
			schedule = rsDao.getGfpStatsScheduleByDate(dt)
			println(s"$LOG_PREFIX schedule=$schedule")

			// SummaryRevenueSharingGfpStatsSchedules.saprkAppState = "ONGOING"으로 변경
			rsDao.updateSparkAppState(RevenueSharingGfpStatsSchedule(_id = schedule._id, sparkAppState = Option("ONGOING"), begunAt = Option(new Date)))

			// 일별 집계인지, 월별 집계인지 구분
			var baseDf: Dataset[Row] = null
			val dateType = if (schedule.date.get.length == 6) DATE_TYPE_MONTHLY else DATE_TYPE_DAILY

			if (dateType == DATE_TYPE_DAILY) { // 일별
				val paths = getInputPathsForDaily(schedule.avroFrom.get, schedule.avroTo.get, pubIds)
				baseDf = loadSilverLog(paths)
			}
			else { // 월별
				val paths = getInputPathsForMonthly(schedule.csvFrom.get, schedule.csvTo.get, pubIds)
				baseDf = loadDailyDataInCsv(paths)
			}

			// 집계
			val aggregator = new RevenueSharingAggregator()
			val aggDf = aggregator.aggregate(Option(AggParam(schedule, baseDf)))

			// HDFS에 저장
			aggregator.write(aggDf, Option(WriteParam(dateType, schedule.date.get)))

			// SummaryRevenueSharingGfpStatsSchedules.saprkAppState = "COMPLETE"으로 변경
			rsDao.updateSparkAppState(RevenueSharingGfpStatsSchedule(_id = schedule._id, sparkAppState = Option("COMPLETE"), endedAt = Option(new Date)))

			// 이력쌓기 - COMPLETE
			val sparkDocForComplete = Spark(
				sparkAppState = Option(SparkAppState.COMPLETE.toString),
				sparkEndedAt = Option(new Date)
			)
			val completeHist = SummaryHistory(
				spark = Option(sparkDocForComplete)
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			case t: Throwable =>
				// SummaryRevenueSharingGfpStatsSchedules.saprkAppState = "FAILURE"으로 변경
				if (Option(schedule).isDefined) {
					rsDao.updateSparkAppState(RevenueSharingGfpStatsSchedule(_id = schedule._id, sparkAppState = Option("FAILURE"), endedAt = Option(new Date), error = Option(t.getMessage)))
				}

				// 이력쌓기 - FAILURE
				val sparkDocForFailure = Spark(
					sparkAppState = Option(SparkAppState.FAILURE.toString),
					sparkAppError = Option(t.getMessage),
					sparkEndedAt = Option(new Date)
				)
				val failureHist = SummaryHistory(
					spark = Option(sparkDocForFailure)
				)
				this.upsertSummaryHistory(failureHist)

				logger.error(s"$LOG_PREFIX 수익쉐어 GFP 통계 생성 실패. sparkAppId=${spark.sparkContext.applicationId} dt=$dt summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")
				throw t
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * 일별 집계를 위한 input 경로 설정. 실버 로그(parquet) 바탕.
	 *
	 * @param since
	 * @param until
	 * @param pubIds
	 * @return
	 */
	private def getInputPathsForDaily(since: String, until: String, pubIds: Vector[String]): ArrayBuffer[String] = {
		println(s"$LOG_PREFIX getInputPathsForDaily() :: since=$since until=$until pubIds=$pubIds")
		val pattern: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHH")
		val dashPattern: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd-HH")
		var current: LocalDateTime = LocalDateTime.parse(since, pattern)
		val end: LocalDateTime = LocalDateTime.parse(until, pattern)

		val paths: ArrayBuffer[String] = ArrayBuffer.empty[String]
		while (current.isBefore(end) || current.isEqual(end)) {
			val ymd_h = current.format(dashPattern)

			var path = s"$SILVER_CLIENT_INPUT_PATH/$ymd_h/_COMPACTION_SUCCESS"
			if (HdfsUtil.exists(hdfs, path)) {
				for (pubId <- pubIds) {
					path = s"$SILVER_CLIENT_INPUT_PATH/$ymd_h/publisherId=$pubId"
					println(s"$LOG_PREFIX getInputPathsForDaily() :: path=$path")
					if (HdfsUtil.exists(hdfs, path)) paths += path
				}
			} else {
				logger.warn(s"$LOG_PREFIX getInputPathsForDaily() :: $path 존재하지 않으므로 로딩 경로에서 제외")
			}

			current = current.plusHours(1L)
		}

		paths.foreach(p => logger.info(s"$LOG_PREFIX daily paths: $p"))
		paths
	}

	/**
	 * 월별 집계를 위한 input 경로 설정. 일별 통계(csv) 바탕.
	 *
	 * @param since
	 * @param until
	 * @param pubIds
	 * @return
	 */
	private def getInputPathsForMonthly(since: String, until: String, pubIds: Vector[String]): ArrayBuffer[String] = {
		val pattern: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd")
		var current: LocalDate = LocalDate.parse(since, pattern)
		val end: LocalDate = LocalDate.parse(until, pattern)

		val paths: ArrayBuffer[String] = ArrayBuffer.empty[String]
		while (current.isBefore(end) || current.isEqual(end)) {
			val ymd = current.format(pattern)

			var path = s"$DAILY_DIR/$ymd/_SUCCESS"
			if (HdfsUtil.exists(hdfs, path)) {
				for (pubId <- pubIds) {
					// 예: hdfs://tesseract-dev/data/log/gfp/gfp-revenue-sharing-report-v2/daily/20200731/_publisherId={pubId}
					path = s"$DAILY_DIR/$ymd/_publisherId=$pubId"
					if (HdfsUtil.exists(hdfs, path)) paths += path
				}
			} else {
				logger.warn(s"$LOG_PREFIX getInputPathsForMonthly() :: $path 존재하지 않으므로 로딩 경로에서 제외")
			}

			current = current.plusDays(1L)
		}

		paths.foreach(p => logger.info(s"$LOG_PREFIX monthly paths: $p"))
		paths
	}

	/**
	 * 일별 통계(csv) 로딩
	 *
	 * @param paths
	 * @return
	 */
	private def loadDailyDataInCsv(paths: Seq[String]): Dataset[Row] = {
		try {
			logger.debug(s"$LOG_PREFIX paths to load: $paths")
			val df = spark.read
				.format("csv")
				.schema(getDailyStatsSchema())
				.option("recursiveFileLookup", "true")
				.option("header", true)
				.load(paths: _*)
			df
		} catch {
			case ex: Exception =>
				logger.error(ex.getMessage, ex)
				if (ex.getMessage != null && ex.getMessage.indexOf("Path does not exist") > -1) {
					logger.error("Exception occured with path=[" + paths + "], exception=[" + ex.getMessage + "]", ex)
					spark.emptyDataFrame
				}
				else throw ex
		}
	}

	/**
	 * 일별 통계 스키마
	 *
	 * @return
	 */
	def getDailyStatsSchema(): StructType = {
		val fields = Array[StructField](
			StructField("date", DataTypes.StringType, false, Metadata.empty),
			StructField("publisherId", DataTypes.StringType, false, Metadata.empty),
			StructField("adUnitId", DataTypes.StringType, false, Metadata.empty),
			StructField("adProviderId", DataTypes.StringType, false, Metadata.empty),
			StructField("adProviderPlaceId", DataTypes.StringType, false, Metadata.empty),
			StructField("placeKey", DataTypes.StringType, false, Metadata.empty),
			StructField("rsKeyValue", DataTypes.StringType, false, Metadata.empty),

			StructField("filledRequest", DataTypes.LongType, false, Metadata.empty),
			StructField("imp", DataTypes.LongType, false, Metadata.empty),
			StructField("viewableImp", DataTypes.LongType, false, Metadata.empty),
			StructField("estimatedImpressions", DataTypes.LongType, false, Metadata.empty),
			StructField("clk", DataTypes.LongType, false, Metadata.empty),

			// 일별 통계에서 bidPrice는 decimal(38, 18)
			StructField("bidPriceForFilledRequest", DataTypes.createDecimalType(38, 18), false, Metadata.empty),
			StructField("bidPriceForImp", DataTypes.createDecimalType(38, 18), false, Metadata.empty),
			StructField("bidPriceForViewableImp", DataTypes.createDecimalType(38, 18), false, Metadata.empty),
			StructField("bidPriceForCreativeType", DataTypes.createDecimalType(38, 18), false, Metadata.empty)
		)
		new StructType(fields)
	}
}

case class AggParam(schedule: RevenueSharingGfpStatsSchedule, df: Dataset[Row])

case class WriteParam(dateType: Int, date: String)

class RevenueSharingAggregator()(implicit spark: SparkSession) extends BizAggregator with AdProviderAggregator with DeleteFutureHelper {

	import spark.implicits._

	private val COALESCE_CNT = 32
	private val MAX_RECORDS_PER_FILE = 30000

	type A = AggParam

	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		val schedule = aggParam.get.schedule
		val baseDf = aggParam.get.df

		var resultDf: Dataset[Row] = null
		if (schedule.date.get.length == 8) {
			val df = prepareDfForDaily(schedule.date.get, baseDf)
			resultDf = getDailyStats(df)
		}
		else {
			resultDf = getMonthlyStats(baseDf)
		}

		resultDf
	}

	/**
	 * 일별 집계를 위해 실버 로그에서 필요한 필드만 추출
	 *
	 * @param date
	 * @param df
	 * @return
	 */
	def prepareDfForDaily(date: String, df: Dataset[Row]): Dataset[Row] = {
		val apTimezoneMap = rsDao.getAdProviderTimezoneMap
		val apTimezoneMapCol = typedLit(apTimezoneMap)

		val df1 = filterTest(df)
		df1
			.filter($"adProviderId".isNotNull) // adProviderId가 null이 아닌 것만으로 필터링
			.filter("adProviderId != ''")
			.filter($"eventId".isin(1, 3, 11, 12)) // eventId 필터링. 1(filledRequets) 3(clk) 11(imp) 12(viewableImp)
			.withColumn("date", getDate($"eventTime", coalesce(apTimezoneMapCol($"adProviderId"), lit("Asia/Seoul")))) // AP 타임존 적용
			.filter(s"date == $date") // 집계 대상 날짜로만 필터링
			.selectExpr(
				AggregatorUtil.selectExprOfIsValid(),
				"date",
				"eventId",
				"publisherId",
				"adUnitId",
				"adProviderId",
				"adProviderPlaceId",
				"placeKey",
				"rsKeyValue",
				"responseCreativeType",
				"cast(bidPrice as decimal(38,18)) AS bidPrice", // 계산할 때는 decimal로 치환
			)
	}

	/**
	 * 광고공급자 estimatedReportType 정보 로드
	 */
	def loadEstimatedReportType(): Dataset[Row] = {
		val oidStructType = StructType(List(StructField("oid", StringType, true)))
		val schema = StructType(List(
			StructField("_id", oidStructType, true),
			StructField("report", StructType(
				List(
					StructField("estimatedReportType", MapType(StringType, StringType), nullable = true),
				)
			))
		))

		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdProviders")
		val df = spark.read.schema(schema).mongo(readConfig)
			.select($"_id.oid".as("adProviderId"), $"report.estimatedReportType")

		applyDefaultEstRptType(df)
	}

	/**
	 * 일별 통계 집계
	 *
	 * @param df
	 * @return
	 */
	def getDailyStats(df: Dataset[Row]): Dataset[Row] = {
		val estimatedReportTypeDf = loadEstimatedReportType()

		df
			// 1차 집계 ( responseCreativeType 포함된 버전 )
			.groupBy(
				"date",
				"publisherId",
				"adUnitId",
				"adProviderId",
				"adProviderPlaceId",
				"placeKey",
				"rsKeyValue",
				"responseCreativeType"
			)
			.agg( // ※ "adProvider is not null"은 prepareDfForDaily()단계에서 이미 걸렀으므로 수식 표현에서 제외
				sum(when($"eventId" === 1, 1L).otherwise(0L)).as("filledRequest"),
				sum(when($"eventId" === 11 && $"isValid" === '1', 1L).otherwise(0L)).as("imp"),
				sum(when($"eventId" === 12 && $"isValid" === '1', 1L).otherwise(0L)).as("viewableImp"),
				sum(when($"eventId" === 3 && $"isValid" === '1', 1L).otherwise(0L)).as("clk"),

				/*
					- bidPriceXx는 scala.math.BigDecimal(precision=38, scale=18)로 처리하여 최대한 정확도를 높임
					- scala.math.BigDecimal(precision, scale)
						- precision : 전체 자리수(정수부 자리수 + 소수부 자리수)
						- scale: 소수점 몇 째자리 까지 표현할 수 있는지(소수점 이하 자리수)
					- 참고
						- exponent: 소수를 표현하는 방법(지수 표기법)
							- 1.23E5  = 123000
							- 1.23E-5 = 0.0000123
				*/
				sum(when($"eventId" === 1, $"bidPrice").otherwise(BigDecimal("0"))).as("bidPriceForFilledRequest"),
				sum(when($"eventId" === 11 && $"isValid" === "1", $"bidPrice").otherwise(BigDecimal("0"))).as("bidPriceForImp"),
				sum(when($"eventId" === 12 && $"isValid" === "1", $"bidPrice").otherwise(BigDecimal("0"))).as("bidPriceForViewableImp"),
			)
			.as("stat")
			// adProviderId & creativeType 별 estimatedReportType 추가
			.join(estimatedReportTypeDf.as("ert"), $"stat.adProviderId" === $"ert.adProviderId" && $"stat.responseCreativeType" === $"ert.creativeType", "left_outer")
			// estimatedReportType 별 estimatedImpressions 추가
			.withColumn("estimatedImpressions", when($"estimatedReportType" === "FILL", $"filledRequest")
				.when($"estimatedReportType" === "IMP", $"imp")
				.when($"estimatedReportType" === "VIEW", $"viewableImp")
				.otherwise(0L))
			// estimatedReportType 별 bidPriceForCreativeType 추가
			.withColumn("bidPriceForCreativeType", when($"estimatedReportType" === "FILL", $"bidPriceForFilledRequest")
				.when($"estimatedReportType" === "IMP", $"bidPriceForImp")
				.when($"estimatedReportType" === "VIEW", $"bidPriceForViewableImp")
				.otherwise(BigDecimal("0")))
			// 2차 집계 ( responseCreativeType 제외 및 재집계 )
			.selectExpr("stat.*", "estimatedImpressions", "bidPriceForCreativeType")
			.groupBy(
				"date",
				"publisherId",
				"adUnitId",
				"adProviderId",
				"adProviderPlaceId",
				"placeKey",
				"rsKeyValue"
			)
			.agg(
				sum($"filledRequest").as("filledRequest"),
				sum($"imp").as("imp"),
				sum($"viewableImp").as("viewableImp"),
				sum($"estimatedImpressions").as("estimatedImpressions"),
				sum($"clk").as("clk"),

				sum($"bidPriceForFilledRequest").as("bidPriceForFilledRequest"),
				sum($"bidPriceForImp").as("bidPriceForImp"),
				sum($"bidPriceForViewableImp").as("bidPriceForViewableImp"),
				sum($"bidPriceForCreativeType").as("bidPriceForCreativeType"),
			)
	}

	/**
	 * 월별 통계 집계
	 *
	 * @param df
	 * @return
	 */
	def getMonthlyStats(df: Dataset[Row]): Dataset[Row] = {
		df
			.groupBy(
				"publisherId",
				"adUnitId",
				"adProviderId",
				"adProviderPlaceId",
				"placeKey",
				"rsKeyValue"
			)
			.agg(
				sum("filledRequest").as("filledRequest"),
				sum("imp").as("imp"),
				sum("viewableImp").as("viewableImp"),
				sum("estimatedImpressions").as("estimatedImpressions"),
				sum("clk").as("clk"),

				sum("bidPriceForFilledRequest").as("bidPriceForFilledRequest"),
				sum("bidPriceForImp").as("bidPriceForImp"),
				sum("bidPriceForViewableImp").as("bidPriceForViewableImp"),
				sum("bidPriceForCreativeType").as("bidPriceForCreativeType"),
			)
	}

	/**
	 * write 시 overwrite하기 때문에 사실상 필요 없으나, BizAggregator에서 abstract method로 선언되어 있으므로 더미 구현체.
	 *
	 * @param helpableParam
	 * @return
	 */
	def getFuturesForDelete(helpableParam: Option[T]): List[Future[Option[Boolean]]] = {
		val dummyFutures = List(Future {
			Option(true)
		})
		dummyFutures
	}

	type W = WriteParam

	/**
	 * HDFS에 저장
	 *
	 * @param df
	 * @param writeParam
	 */
	def write(df: DataFrame, writeParam: Option[W] = None) = {
		val dateType = writeParam.get.dateType
		val date = writeParam.get.date
		val outputPath = if (dateType == DATE_TYPE_DAILY) s"$DAILY_DIR/$date" else s"$MONTHLY_DIR/$date"
		val asIsPartitoinFields = Array[String]("publisherId", "adUnitId", "adProviderId", "adProviderPlaceId")
		val toBePartitionFields = new Array[String](4)

		var df2 = df

		// 기존 데이터 프레임에서 파티셔닝 컬럼에다가 언더바(_)를 붙인 데이터를 추가로 붙인다.
		// 언더바로 붙은 컬럼들을 디렉토리 파티셔닝 컬럼으로 사용
		for ((asIsField, idx) <- asIsPartitoinFields.zipWithIndex) {
			val toBeField = "_" + asIsField
			toBePartitionFields(idx) = toBeField
			df2 = df2.withColumn(toBeField, new Column(asIsField))
		}

		df2
			.coalesce(COALESCE_CNT)
			.write
			.partitionBy(toBePartitionFields: _*)
			.format("csv")
			.mode(SaveMode.Overwrite)
			.option("header", "true")
			.option("maxRecordsPerFile", MAX_RECORDS_PER_FILE) // 파일 한개당 레코드 수
			.save(outputPath)

		logger.debug(s"$LOG_PREFIX HDFS 쓰기 완료. $outputPath")
	}
}

/*
[ hadoop 파일 개수 및 용량 확인 ]
hadoop fs -count -h hdfs://tesseract-dev/data/log/ssp/gfp-silver/er-ssp-client/20220118-*
    -count : DIR_COUNT, FILE_COUNT, CONTENT_SIZE FILE_NAME 을 보여줌
    -count -q : QUOTA, REMAINING_QUATA, SPACE_QUOTA, REMAINING_SPACE_QUOTA, DIR_COUNT, FILE_COUNT, CONTENT_SIZE, FILE_NAME 을 보여줌
    -h : Show sizes human readable format
 */
