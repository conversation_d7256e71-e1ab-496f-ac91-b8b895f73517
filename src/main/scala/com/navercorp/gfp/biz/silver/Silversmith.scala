package com.navercorp.gfp.biz.silver

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Date

import org.apache.spark.sql.functions.{col, rand}
import org.apache.spark.sql.{Dataset, Row}
import org.bson.Document

import com.navercorp.gfp.core.BaseAggregator
import com.navercorp.gfp.core.BaseEnv.{BRONZE_ROOT, SILVER_ROOT}
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil}

object Silversmith extends BaseAggregator {

	import spark.implicits._

	val LOG_PREFIX = ".......... [SILVERSMITH]"
	val LOG_TYPE_SERVER = "er-ssp-server"
	val LOG_TYPE_CLIENT = "er-ssp-client"
	val logTypes = Vector(LOG_TYPE_SERVER, LOG_TYPE_CLIENT)

	// 크기 순 정렬
	// hadoop fs -du -s hdfs://BizCloud/data/log/ssp/gfp-silver/er-ssp-server/20220718-*/* |  sort -g | awk '{ numBytes = $1; numUnits = split("B K M G T P", unit); num = numBytes; iUnit = 0; while(num >= 1024 && iUnit + 1 < numUnits) { num = num / 1024; iUnit++; } $1 = sprintf( ((num == 0) ? "%6d%s " : "%6.1f%s "), num, unit[iUnit + 1]); print $0; }'
	var NUM_PARTITION = 480

	var datetime: String = null
	var logType: String = "*"
	var isCheckLibrarian = 0

	var serverFields: Vector[String] = null
	var clientFields: Vector[String] = null

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		logger.debug(s"$LOG_PREFIX applicationId: ${spark.sparkContext.applicationId} args:${args.mkString(",")}")

		// 날짜 설정
		if (args(0) == null) { // 파라미터 없을 경우 디폴트 3시간 전
			val formatter = DateTimeFormatter.ofPattern("yyyyMMdd-HH")
			datetime = formatter.format(LocalDateTime.now.minusHours(3))
		}
		else datetime = args(0).substring(0, 8) + "-" + args(0).substring(8, 10) // yyyyMMddHH -> yyyyMMdd-HH

		// 로그타입 설정
		if (args.length > 1 && args(1) != null) {
			val lt = args(1).toLowerCase()
			if (lt == "s") {
				logType = LOG_TYPE_SERVER
			} else if (lt == "c") {
				logType = LOG_TYPE_CLIENT
			} else {
				logType = "*"
			}
		} else {
			logType = "*"
		}

		try {
			// 이력쌓기 - IN_PROGRESS
			val sparkDocForInProgress = Spark(
				sparkAppId = Option(spark.sparkContext.applicationId),
				sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
				sparkStartedAt = Option(new Date)
			)
			val inProgressHist = SummaryHistory(
				datetime = Option(datetime),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(sparkDocForInProgress),
				detail = Option(Map("logType" -> logType))
			)
			this.upsertSummaryHistory(inProgressHist)

			initNumPartition()
			initIsCheckLibrarian()
			initFields()

			val (isKeepGoing, bronzePathPriority) = getBronzeSurroundings()

			if (isKeepGoing) {
				if (logType == "*" || logType == LOG_TYPE_SERVER) {
					writeSilver(LOG_TYPE_SERVER, bronzePathPriority)
					logger.info(s"$LOG_PREFIX 실버 로그 서버 적재 완료. $datetime")
				}

				if (logType == "*" || logType == LOG_TYPE_CLIENT) {
					writeSilver(LOG_TYPE_CLIENT, bronzePathPriority)
					logger.info(s"$LOG_PREFIX 실버 로그 클라 적재 완료. $datetime")
				}
			} else {
				logger.info(s"$LOG_PREFIX 실버 로그 서버/클라 적재 스킵. $datetime")
			}

			// 이력쌓기 - COMPLETE
			val sparkDocForComplete = Spark(
				sparkAppState = Option(SparkAppState.COMPLETE.toString),
				sparkEndedAt = Option(new Date)
			)
			val completeHist = SummaryHistory(
				spark = Option(sparkDocForComplete)
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val sparkDocForFailure = Spark(
					sparkAppState = Option(SparkAppState.FAILURE.toString),
					sparkAppError = Option(t.getMessage),
					sparkEndedAt = Option(new Date)
				)
				val failureHist = SummaryHistory(
					spark = Option(sparkDocForFailure)
				)
				this.upsertSummaryHistory(failureHist)

				logger.error(s"$LOG_PREFIX 실버 로그 적재 실패. sparkAppId=${spark.sparkContext.applicationId} datetime=$datetime summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")
				throw t;
		} finally {
			NeloUtil.waitFor()
		}
	}

	private def initNumPartition(): Unit = {
		val env: Option[Document] = baseDao.getEnvironment("silver-log-accumulation-partition-num")
		if (!env.isEmpty) {
			NUM_PARTITION = env.get.getInteger("value")
		}
		logger.debug(s"$LOG_PREFIX numPartition:$NUM_PARTITION")
	}

	private def initIsCheckLibrarian(): Unit = {
		val env: Option[Document] = baseDao.getEnvironment("silver-log-accumulation-is-check-librarian")
		if (!env.isEmpty) {
			isCheckLibrarian = env.get.getInteger("value")
		}
		logger.debug(s"$LOG_PREFIX isCheckLibrarian:$isCheckLibrarian")
	}

	private def initFields(): Unit = {
		val commonFields = Vector("requestId",
			"requestTime",
			"publisherId",
			"serviceId",
			"adUnitId",
			"country",
			"gender",
			"age",
			"area1",
			"area2",
			"area3",
			"os",
			"osVer",
			"browser",
			"browserVer",
			"device",
			"keyValue",
			"rsKeyValue",
			"param",
			"test"
		)
		val sFields = Vector("responses", "creativeTypes")
		val cFields = Vector("isValid",
			"adProviderId",
			"adProviderPlaceId",
			"bidPrice",
			"bidPriceInKRW",
			"bidPriceInUSD",
			"eventId",
			"eventTime",
			"placeKey",
			"stat",
			"rank",
			"connectionType",
			"dealId",
			"biddingGroupId",
			"requestCreativeTypes",
			"responseCreativeType",
			"requestSizes",
			"responseSize",
			"adStyleId",
			"webDecoId"
		)
		serverFields = commonFields ++ sFields
		clientFields = commonFields ++ cFields
	}

	/**
	 * 브론즈 상황 조회
	 *
	 * @return (Boolean, Int)
	 *         isKeepGoing: 실버 적재를 해되 되는지의 여부
	 *         테스트 환경: 로그가 안 쌓이는 경우가 있기 때문에 false로 리턴할 수 있음
	 *         리얼 환경: 브론즈가 정상이면 true, 그렇지 않으면 Exception발생시켜 airflow dag이 인지할 수 있게 함
	 *
	 *         bronzePathPriority: 실버 적재 시 어느 브론즈 경로를 봐야하는지의 우선순위
	 *         1: hdfs://BizCloud/data/log/ssp/parquet/er-ssp-server/yyyymmdd-hh
	 *         2: hdfs://BizCloud/data/log/ssp/parquet/er-ssp-server/evetnTimePartition=yyyymmdd-hh
	 */
	private def getBronzeSurroundings(): (Boolean, Int) = {
		var bronzePathPriority = 1
		var isKeepGoing = true

		// 브론즈 적재 정보 조회
		val bronzeAccuInfoList = getBronzeAccuInfoList()
		logger.debug(s"$LOG_PREFIX 브론즈 적재 정보 $bronzeAccuInfoList")
		val isReadyBronzeAccu = bronzeAccuInfoList.map(info => info._3).reduce((result, elem) => result && elem)
		if (isReadyBronzeAccu) {
			if (isCheckLibrarian == 1) {
				// 브론즈 primary 컴팩션 정보 조회
				val primaryInfoList = getBronzeCompactionInfoList(bronzePathPriority)
				logger.debug(s"$LOG_PREFIX 브론즈 컴팩션 정보 priority=$bronzePathPriority $primaryInfoList")
				val isReadyPrimaryCompaction = primaryInfoList.map(info => info._3).reduce((result, elem) => result && elem)
				if (isReadyPrimaryCompaction) {
					logger.debug(s"$LOG_PREFIX 브론즈 적재&컴팩션 모두 정상")
					isKeepGoing = true // true로 통과
				} else {
					// 브론즈 secondary 컴팩션 정보 조회
					logger.debug(s"$LOG_PREFIX 브론즈 primary 컴팩션이 존재하지 않아 secondary 컴팩션 확인 중...")
					bronzePathPriority = 2
					val secondaryInfoList = getBronzeCompactionInfoList(bronzePathPriority)
					logger.debug(s"$LOG_PREFIX 브론즈 컴팩션 정보 priority=$bronzePathPriority $secondaryInfoList")
					val isReadySecondaryCompaction = secondaryInfoList.map(info => info._3).reduce((result, elem) => result && elem)
					if (isReadySecondaryCompaction) {
						logger.debug(s"$LOG_PREFIX 브론즈 적재&컴팩션 모두 정상")
						isKeepGoing = true // true로 통과
					} else {
						if ("real" == conf.getString("profile")) {
							throw new Exception(s"브론즈 컴팩션이 준비되지 않았음. $datetime") // 불통. airflow dag이 감지할 수 있게 ex 발생시킴
						} else {
							// 테스트 환경에서는 컴팩션이 없으므로 true로 통과
							logger.warn(s"$LOG_PREFIX 실버 로그를 만들기 위한 브론즈 컴팩션이 존재하지 않지만. 리얼 환경이 아니므로 true. $datetime")
							isKeepGoing = true
						}
					}
				}
			} else {
				logger.debug(s"$LOG_PREFIX 브론즈 적재 정상. 라이브러리언 안 보기 때문에 컴팩션 상태 점검 스킵")
				isKeepGoing = true // true로 통과
			}
		} else {
			if ("real" == conf.getString("profile")) {
				throw new Exception(s"브론즈가 적재되지 않았음. $datetime") // 불통. airflow dag이 감지할 수 있게 ex 발생시킴
			} else {
				// 테스트 환경에서는 로그가 없는 경우가 있으므로 스킵
				logger.warn(s"$LOG_PREFIX 실버 로그를 만들기 위한 브론즈 존재하지 않으므로 스킵. $datetime")
				isKeepGoing = false // false로 통과
			}
		}

		logger.info(s"$LOG_PREFIX getBronzeSurroundings() :: datetime=$datetime isKeepGoing=$isKeepGoing bronzePathPriority=$bronzePathPriority")
		(isKeepGoing, bronzePathPriority)
	}

	/**
	 * 기본 적재 경로(2순위) 정보 조회
	 * eventTimePartition 경로: hdfs://BizCloud/data/log/ssp/parquet/er-ssp-server/evetnTimePartition={yyyymmdd-hh}
	 *
	 * @return
	 */
	private def getBronzeAccuInfoList(): Vector[(String, String, Boolean)] = {
		logTypes.map(logType => {
			val inputPath = getBronzePathByPriority(logType, 2)
			(logType, inputPath, HdfsUtil.exists(hdfs, inputPath))
		})
	}

	/**
	 * 컴팩션 경로(1, 2순위 모두 해당) 정보 조회
	 *
	 * @param priority
	 * @return
	 */
	private def getBronzeCompactionInfoList(priority: Int): Vector[(String, String, Boolean)] = {
		val infoList = logTypes.map(logType => {
			val librarianSuccessFilePath = getBronzePathByPriority(logType, priority) + "/_LIBRARIAN_SUCCESS"
			(logType, librarianSuccessFilePath, HdfsUtil.exists(hdfs, librarianSuccessFilePath))
		})

		logger.debug(s"$LOG_PREFIX 브론즈 컴팩션 정보 priority=$priority $infoList")
		infoList
	}

	/**
	 * 브론즈 경로
	 * 1순위) hdfs://BizCloud/data/log/ssp/parquet/er-ssp-server/{yyyymmdd-hh}
	 * 2순위) hdfs://BizCloud/data/log/ssp/parquet/er-ssp-server/evetnTimePartition={yyyymmdd-hh}
	 *
	 * @param logType
	 * @param priority
	 * @return
	 */
	private def getBronzePathByPriority(logType: String, priority: Int) = {
		var path = ""

		if (priority == 1) {
			path = s"$BRONZE_ROOT/$logType/$datetime"
		} else {
			path = s"$BRONZE_ROOT/$logType/eventTimePartition=$datetime"
		}

		path
	}

	/**
	 * 실버 적재
	 *
	 * @param logType
	 * @param bronzePathPriority
	 */
	private def writeSilver(logType: String, bronzePathPriority: Int): Unit = {
		val inputPath = getBronzePathByPriority(logType, bronzePathPriority)
		logger.debug(s"$LOG_PREFIX inputpath:$inputPath")

		// outputPath="hdfs://pg07/user/gfp-data/silver/er-ssp-server/20231225-00"
		val outputPath = s"$SILVER_ROOT/intermediate/$logType/$datetime"
		logger.debug(s"$LOG_PREFIX outputpath:$outputPath")

		if (logType == LOG_TYPE_SERVER) {
			write(filterValidServiceId(load(inputPath, serverFields)), outputPath)
		} else {
			write(filterValidResponseCreativeTypes(filterValidServiceId(load(inputPath, clientFields))), outputPath)
		}

		// intermediate -> warehouse 경로로 리네임
		val dstPath = s"$SILVER_ROOT/$logType/$datetime"
		rename(outputPath, dstPath)
	}

	/**
	 * 데이터 로드
	 *
	 * @param inputPath
	 * @param fields
	 * @return
	 */
	private def load(inputPath: String, fields: Vector[String]): Dataset[Row] = {
		spark.read.format("parquet")
			.load(inputPath)
			.select("value.*")
			.select(fields.head, fields.tail: _*)
	}

	/**
	 * 2024.08.26
	 * 	- 실버 클라이언트 로그에서 비정상적인 responseCreativeType 제거 (https://jira.navercorp.com/browse/GFP-127)
	 * 	- creativeType이 null이거나 정상적인 것 만으로 필터링
	 *
	 * @param df
	 * @return
	 */
	private def filterValidResponseCreativeTypes(df: Dataset[Row]): Dataset[Row] = {
		val creativeTypes = Vector("BANNER", "VIDEO", "NATIVE", "COMBINED")
		df.filter($"responseCreativeType".isNull || $"responseCreativeType".isin(creativeTypes: _*))
	}

	/**
	 * 2025.02.28
	 * 	- [DATA] 실버 로그에 serviceId가 null인 것 제외 (https://jira.navercorp.com/browse/GFP-932)
	 * 	- serviceId는 null이면 안됨
	 * 	- 간혹 serviceId가 null인 것이 존재하는데, 주로 아주 오래된 requestTime(몇 년 전)으로 들어옴.
	 * 	- 전송 서버 초창기 시절의 버그로 추정됨.
	 *
	 * @param df
	 * @return
	 */
	private def filterValidServiceId(df: Dataset[Row]): Dataset[Row] = {
		df.filter($"serviceId".isNotNull)
	}

	/**
	 * HDFS에 쓰기
	 *
	 * @param inputPath
	 * @param outputPath
	 * @param fields
	 */
	private def write(df: Dataset[Row], outputPath: String): Unit = {
		df
			.repartition(NUM_PARTITION, col("publisherId"), rand) // 데이터 치우침을 피하기 위해 리파티션
			.write
			.mode("overwrite")
			.option("dfs.blocksize", 64 * 1024 * 1024)
			.option("parquet.block.size", 64 * 1024 * 1024)
			.partitionBy("publisherId")
			.parquet(outputPath)
	}
}
