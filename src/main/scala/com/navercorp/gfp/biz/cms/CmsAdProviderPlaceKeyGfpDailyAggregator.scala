package com.navercorp.gfp.biz.cms

import com.navercorp.gfp.core.BaseEnv.{SILVER_CLIENT_INPUT_PATH, cmsMdbDefaultWriteOptions}
import com.navercorp.gfp.core.BaseUdf.toObjectId
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory, SummaryHistoryDetail}
import com.navercorp.gfp.core.{BaseAggregator, BizAggregator, DeleteFutureHelper}
import com.navercorp.gfp.exception.BusinessException
import com.navercorp.gfp.meta.adproviderinfo.{AdProviderInfo, AdProviderInfoDao}
import com.navercorp.gfp.meta.summarytargetcountry.SummaryTargetCountryDao
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, ObjectIdUtil}
import org.apache.spark.sql.functions.{expr, lit, when}
import org.apache.spark.sql._
import org.bson.types.ObjectId
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTimeZone, Period}
import org.mongodb.scala.model.Filters

import java.util.Date
import scala.collection.JavaConverters._
import scala.concurrent.Future
import scala.util.Try
import scala.util.control.Breaks.{break, breakable}

/*
	실버 로그에서 요약통계를 MongoDB에저장 (AdProviderPlace 단위)
	- /user/gfp-data/silver
	- allowPlaceKeyDataPulling=1 적용됨

	publisher_ids=[N], adProvider_ids=[N]

	spark 설정
		--num-executors 12
		--executor-cores 3
		--executor-memory 1g
		--conf spark.sql.shuffle.partitions=800
		--conf spark.sql.files.maxPartitionBytes=192mb
*/
object CmsAdProviderPlaceKeyGfpDailyAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [DAILY-GFP-PK-CMS]"

	private var sparkAppId: String = ""

	private val adProviderInfoDao = new AdProviderInfoDao

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 처리 대상 일자
		val endDate = args(0)

		val publisherIds: Seq[String] = args(1).split(",").distinct
		val adProviderIds: Seq[String] = args(2).split(",").distinct

		// 이력쌓기 상세정보
		val detail = summaryHistoryId match {
			case Some(_) => None
			case None => Option(SummaryHistoryDetail(
				publisherIds = Option(publisherIds), adProviderIds = Option(adProviderIds)
			))
		}

		try {
			// args 밸리데이션 체크
			// require 는 runtime 시점에 Exception 을 던진다.
			// Exception 없이, 인스턴스 생성 시점에 validation 체크를 하고 싶으면 apply 를 구현하도록 한다.
			//  - https://gist.github.com/jkpl/4932e8730c1810261381851b13dfd29d
			//  - https://www.47deg.com/blog/smart-constructors-in-scala/#smart-constructors-for-case-classes-0
			//  - https://stackoverflow.com/questions/5982484/scala-lift-check-if-date-is-correctly-formatted
			require(ObjectIdUtil.isValidObjectIds(publisherIds), s"publisherIds($publisherIds) is invalid ObjectIds")
			require(ObjectIdUtil.isValidObjectIds(adProviderIds), s"adProviderIds($adProviderIds) is invalid ObjectIds")
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(endDate)).isSuccess, s"endDate($endDate) is invalid format (must be yyyyMMdd)")


			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(endDate),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
				detail = detail
			)
			this.upsertSummaryHistory(inProgressHist)

			// adProviderIds 에 해당하는 timezone 정보 가져오기
			// 	- adProviderIds 는 동일한 timezone 을 갖는 ap 들 목록임
			val timezone = adProviderDao.getAdProviderCurrencyAndTimezone(adProviderIds.head) match {
				case Some(ap) if ap.timezone.getOrElse("") != "" => ap.timezone.get
				case _ => throw BusinessException(s"adProviderIds= $adProviderIds does not exist in DB")
			}

			logger.debug(s"$LOG_PREFIX timezone= $timezone")
			logger.debug(s"$LOG_PREFIX adProviderIds= $adProviderIds")
			logger.debug(s"$LOG_PREFIX publisherIds= $publisherIds")

			// Gfp 매체만 처리하도록 필터
			val gfpPublisherIds = filterGfpPublisherIds(publisherIds)

			logger.debug(s"$LOG_PREFIX gfpPublisherIds= $gfpPublisherIds")

			// 집계 인스턴스 생성
			val aggregator = new CmsAdProviderPlaceKeyGfpDailyAggregator(endDate)

			aggregator.init()

			// breakable 을 loop 안에 넣으면 break 가 continue 로 동작하고, loop 밖에 넣으면 break 로 동작한다.
			// 	- https://m.blog.naver.com/jiwon2772/221317139127
			gfpPublisherIds.foreach { publisherId =>
				breakable {
					logger.debug(s"$LOG_PREFIX 매체별 처리 시작 ( publisherId= $publisherId )")

					// AdProviderInfo 정보 가져오기 (allowPlaceKeyDataPulling=1)
					val adProviderInfos = adProviderInfoDao.getAdProviderInfos(publisherId, adProviderIds).into(new java.util.ArrayList[AdProviderInfo]()).asScala.filter(_.allowPlaceKeyDataPulling.getOrElse(0).equals(1))
					val validAdProviderIds = adProviderInfos.map(_.adProvider_id.toString)

					// 처리할 adProviderIds 가 없는 경우, break
					if (validAdProviderIds.length < 1) {
						logger.warn(s"$LOG_PREFIX 처리할 adProviderIds 가 없음 ( publisherId= $publisherId )")
						break
					}

					// 타임존에 해당하는 한국시간대 경로 가져오기
					val pathList = getPathList(publisherId, endDate, timezone)

					// 실버 로그 로딩
					val clientDf = loadParquetLog(pathList) match {
						case Some(df) => df
						case _ =>
							logger.warn(s"$LOG_PREFIX publisherId= $publisherId 에 해당하는 HDFS 로그 경로가 없음")
							break
					}

					val aggregatedDf = aggregator.aggregate(Option(clientDf, publisherId, validAdProviderIds))

					// 기존 데이터 삭제
					aggregator.delete(aggregator.getFuturesForDelete(Option(publisherId, validAdProviderIds)))

					// MongoDB에 저장
					aggregator.write(aggregatedDf)
				}
			}

			// 이력쌓기 - 완료(COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					)),
					detail = detail
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * 타임존 적용된 실버로그 경로 구하기
	 *    - 실버로그는 한국시간으로 되어있음
	 *    - AP 데이터는 타임존이 적용되어야 함
	 *
	 * @param publisherId
	 * @param targetDate
	 * @param timezone
	 * @return pathList
	 */
	def getPathList(publisherId: String, targetDate: String, timezone: String): List[String] = {
		// 타임존 적용된 날짜의 시간대별 경로 추출 (실버로그는 한국시간으로 되어있어 타임존 변환하여 경로를 가져와야 한다)
		val dtRange = getDateTimeRange(targetDate, Period.hours(1), DateTimeZone.forID(timezone))
		val pathList = dtRange.map { dt =>
			val path: String = s"$SILVER_CLIENT_INPUT_PATH/${dt.toString("yyyyMMdd-HH")}/publisherId=$publisherId"

			// hdfs 에 존재하는 경로만 따로 추출
			if (HdfsUtil.exists(hdfs, path)) path else ""
		}.filter(_.nonEmpty)

		//    logger.debug(s"pathList $pathList")

		pathList
	}

	/**
	 * 로그 로딩
	 *
	 * @param pathList
	 * @return Dataset[Row]
	 */
	override def loadParquetLog(pathList: Seq[String]): Option[Dataset[Row]] = {
		// HDFS 경로가 하나도 없는 경우, None
		if (pathList.isEmpty) {
			None
		} else {
			super.loadParquetLog(pathList) match {
				case Some(df) =>
					Option(PROFILE match {
						case "stage" | "real" =>
							df.filter("test == 0")
						case "local" | "dev" | "test" =>
							// 로컬/테스트 환경에서 isValid 를 모두 1로 처리한다.
							df.withColumn("isValid", lit('1'))
						case _ => df
					})
				case _ => None
			}
		}
	}

	/**
	 * GFP 매체로 필터
	 *
	 * @param publisherIds
	 * @return Seq[String]
	 */
	def filterGfpPublisherIds(publisherIds: Seq[String]): Seq[String] = {
		publisherIds.filter(publisherDao.getGfpPublisherIds().contains)
	}
}

/**
 * 데이터 삭제 / 집계 / 추가 등의 작업을 한다.
 *
 * spark-submit에서 호출할 때나 test case에서 호출할 때 사용함
 * 환경에 따라 달라지는 값은 생성자 파라미터로 받아서 처리함.
 *
 * @param date
 */
class CmsAdProviderPlaceKeyGfpDailyAggregator(date: String)(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper {
	val LOG_PREFIX = ".......... [DAILY-GFP-PK-CMS]"

	COLLECTION_NAME = "SummaryGfpStatsByPlaceKey"

	private val summaryTargetCountryDao = new SummaryTargetCountryDao()

	// Option[Seq[String]] 보다 Seq[Option[String]] 를 사용하는게 좋다.
	//  - https://stackoverflow.com/questions/32371382/scala-optionseqstring-vs-seqoptionstring/32371849#32371849
	private var countryList: Seq[String] = Seq()

	// country 는 aggregate 시 쓰이는데, countryList 는 init 할 때 생성 된다.
	// country 를 lazy 없이 쓰면, countryList 가 empty 이기 때문에, country 구문이 원하는 대로 생성 되지 않는다.
	// 물론 country 를 var 로 선언 하고, init 시점에 수정할 수는 있으나, 공통 쿼리 구문을 상단에 모아두고 싶어서 lazy 를 사용하였다.
	private lazy val country = s"CASE WHEN country IN ( ${countryList.mkString("'", "','", "'")} ) THEN country ELSE '-' END AS country"

	import spark.implicits._
	// https://www.scala-lang.org/api/2.13.3/scala/collection/JavaConverters$.html

	/**
	 * 집계에 필요한 정보 조회하기
	 *    - SummaryTargetCountries 에서 countryCd 정보 조회
	 */
	override def init(): Unit = {
		// SummaryTargetCountries 에서 countryCd 정보 조회하기
		val countries = summaryTargetCountryDao.getCountries

		countryList = countries.map(_.countryCd).into(new java.util.ArrayList[String]()).asScala

		logger.debug(s"$LOG_PREFIX countries= ${countryList.toString()}")
	}

	/**
	 * 데이터 집계
	 *
	 * @return finalDf Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX country= $country")

		val (logDf, publisherId, adProviderIds) = aggParam.get

		val baseList: Seq[String] = Seq("adProviderId", "placeKey", "dealId")
		val groupByList: Seq[String] = baseList ++ Seq("country")
		val selectList: Seq[String] = baseList ++ Seq(country, "eventId", "isValid")

		val aggList = Seq(
			expr("SUM(CASE WHEN (eventId == 11 AND adProviderId IS NOT NULL AND isValid == '1') THEN 1 ELSE 0 END)").as("imp"),
			expr("SUM(CASE WHEN (eventId == 12 AND adProviderId IS NOT NULL AND isValid == '1') THEN 1 ELSE 0 END)").as("viewableImp"),
			expr("SUM(CASE WHEN (eventId == 3 AND isValid == '1') THEN 1 ELSE 0 END)").as("clk")
		)

		// 실버 로그에서 가져온 데이터
		val finalDf = logDf
			.filter($"adProviderId".isin(adProviderIds: _*))
			.filter(s"placeKey IS NOT NULL")
			.selectExpr(selectList: _*)
			.groupBy(groupByList.head, groupByList.tail: _*)
			.agg(aggList.head, aggList.tail: _*)
			// 지표가 모두 0인 경우 제외 처리
			.filter("NOT(imp == 0 AND viewableImp == 0 AND clk == 0)")
			// 요약 대상 날짜
			.withColumn("ymd", lit(date))
			// dealId 추가
			.withColumn("dealId", when($"dealId".isNull, lit(null: String)).otherwise($"dealId"))
			// 생성한 날짜
			.withColumn("createdAt", functions.current_timestamp)
			// 몽고디비 ObjectId 타입으로 변환
			.withColumn("publisher_id", toObjectId(lit(publisherId)))
			.withColumn("adProvider_id", toObjectId($"adProviderId"))
			.withColumnRenamed("placeKey", "adProviderPlaceKey")
			// 불필요한 필드 제거
			.drop("adProviderId")

		//		logger.debug("finalDf schema >>>>>>")
		//		finalDf.printSchema()
		//		finalDf.show(30)
		finalDf.explain("formatted")

		finalDf
	}

	type A = (Dataset[Row], String, Seq[String])

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		val (publisherId, adProviderIds) = param.get

		logger.debug(s"$LOG_PREFIX date= $date, publisherId= $publisherId, adProviderIds= $adProviderIds")

		val futures: Seq[Future[Option[Boolean]]] = adProviderIds.flatMap { adProviderId =>
			val additionalFilters = Seq(
				Filters.eq("publisher_id", new ObjectId(publisherId)),
				Filters.eq("adProvider_id", new ObjectId(adProviderId)),
			)

			super.getFuturesForDeleteByDateRangeForCms(COLLECTION_NAME, List(date), additionalFilters)
		}

		futures.toList
	}

	type T = (String, Seq[String])

	/**
	 * 데이터 추가
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		logger.debug(s"$LOG_PREFIX COLLECTION_NAME=$COLLECTION_NAME")

		val writeOptions = cmsMdbDefaultWriteOptions
			.updated("spark.mongodb.output.collection", COLLECTION_NAME)

		super.writeToMongoDB(df, writeOptions)
	}
}
