package com.navercorp.gfp.biz.cms

import java.util.Date
import scala.concurrent.Future
import scala.util.Try

import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import org.apache.hadoop.fs.FileSystem
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.bson.types.ObjectId
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTimeZone, Period}
import org.mongodb.scala.model.Filters

import com.navercorp.gfp.biz.silvergrey.SilvergreyUdf.{toGfpNetRevenue, toNetRevenue, toSales}
import com.navercorp.gfp.core.BaseEnv.{SILVER_CLIENT_INPUT_PATH, cmsMdbDefaultWriteOptions}
import com.navercorp.gfp.core.BaseUdf.toObjectId
import com.navercorp.gfp.core._
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory, SummaryHistoryDetail}
import com.navercorp.gfp.exception.{BusinessException, BusinessExceptionType}
import com.navercorp.gfp.meta.gfpfeerate.GfpFeeRateDao
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil}

/*
	- 실버로그를 바탕으로 NonRK 통계를 만들어 CMS DB AdProviderNonRkStats 에 저장
	- 매출의 원천이 GFP 인 경우

	spark 설정
		--num-executors 6
		--executor-cores 3
		--executor-memory 2g
		--conf spark.sql.shuffle.partitions=200
		--conf spark.sql.files.maxPartitionBytes=192mb
 */
object CmsNonRkGfpDailyAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [DAILY-NONRK-GFP-CMS]"

	private var sparkAppId: String = ""

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 처리 대상 일자
		val endDate = args(0)

		val reportApiType = args(1)
		val adProviderId = args(2)
		val publisherId = args(3)

		// 이력쌓기 상세정보
		val detail = summaryHistoryId match {
			case Some(_) => None
			case None => Option(SummaryHistoryDetail(
				publisherId = Option(publisherId), adProviderId = Option(adProviderId)
			))
		}

		try {
			// args 밸리데이션 체크
			// require 는 runtime 시점에 Exception 을 던진다.
			// Exception 없이, 인스턴스 생성 시점에 validation 체크를 하고 싶으면 apply 를 구현하도록 한다.
			//  - https://gist.github.com/jkpl/4932e8730c1810261381851b13dfd29d
			//  - https://www.47deg.com/blog/smart-constructors-in-scala/#smart-constructors-for-case-classes-0
			//  - https://stackoverflow.com/questions/5982484/scala-lift-check-if-date-is-correctly-formatted
			require(ObjectId.isValid(publisherId), s"publisherId($publisherId) is invalid ObjectId")
			require(ObjectId.isValid(adProviderId), s"adProviderId($adProviderId) is invalid ObjectId")
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(endDate)).isSuccess, s"endDate($endDate) is invalid format (must be yyyyMMdd)")


			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(endDate),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
				detail = detail
			)
			this.upsertSummaryHistory(inProgressHist)

			// adProviderId 에 해당 하는 timezone 정보 가져오기
			val timezone = adProviderDao.getTimezone(adProviderId) match {
				case Some(tz) => tz
				case _ => throw BusinessException(s"adProviderId= $adProviderId 의 타임존 정보가 DB에 존재 하지 않음")
			}

			logger.debug(s"$LOG_PREFIX timezone= $timezone")
			logger.debug(s"$LOG_PREFIX reportApiType= $reportApiType, adProviderId= $adProviderId, publisherId= $publisherId")

			// 타임존에 해당하는 한국시간대 경로 가져오기
			val pathList = getPathList(publisherId, endDate, timezone)

			// 실버 로그 로딩
			val clientDf = loadParquetLog(pathList) match {
				case Some(df) => df
				case _ => spark.emptyDataFrame
			}

			// 집계 인스턴스 생성
			val aggregator = new CmsNonRkGfpDailyAggregator(hdfs, clientDf, reportApiType, endDate, publisherId, adProviderId)

			// 집계에 필요한 정보 조회하기
			//    - GFP 수수료 정보 조회
			//    - AP 수수료 정보 조회 (TTD)
			aggregator.init()

			val aggregatedDf = aggregator.aggregate()

			// 집계 총 건수
			val aggCount = aggregatedDf.count()

			// 기존 데이터 삭제
			aggregator.delete(aggregator.getFuturesForDelete())

			// MongoDB에 저장
			aggregator.write(aggregatedDf)

			// 캐시 제거
			aggregator.unpersist()

			// 이력쌓기 - 완료(COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				)),
				detail = Option(SummaryHistoryDetail(aggCount = Option(aggCount)))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					)),
					detail = detail
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * 타임존 적용된 실버로그 경로 구하기
	 *    - 실버로그는 한국시간으로 되어있음
	 *    - AP 데이터는 타임존이 적용되어야 함
	 *
	 * @param publisherId
	 * @param targetDate
	 * @param timezone
	 * @return pathList
	 */
	def getPathList(publisherId: String, targetDate: String, timezone: String): List[String] = {
		// 타임존 적용된 날짜의 시간대별 경로 추출 (실버로그는 한국시간으로 되어있어 타임존 변환하여 경로를 가져와야 한다)
		val dtRange = getDateTimeRange(targetDate, Period.hours(1), DateTimeZone.forID(timezone))
		val pathList = dtRange.map { dt =>
			val path: String = s"$SILVER_CLIENT_INPUT_PATH/${dt.toString("yyyyMMdd-HH")}/publisherId=$publisherId"

			// hdfs 에 존재하는 경로만 따로 추출
			if (HdfsUtil.exists(hdfs, path)) path else ""
		}.filter(_.nonEmpty)

		//    logger.debug(s"pathList $pathList")

		pathList match {
			case List() => throw BusinessException("pathList is empty", BusinessExceptionType.EmptyPath)
			case _ => pathList
		}
	}

	/**
	 * 로그 로딩
	 *
	 * @param pathList
	 * @return Dataset[Row]
	 */
	override def loadParquetLog(pathList: Seq[String]): Option[Dataset[Row]] = {
		super.loadParquetLog(pathList) match {
			case Some(df) =>
				Option(PROFILE match {
					case "stage" | "real" =>
						df.filter("test == 0")
					case "local" | "dev" | "test" =>
						// 로컬/테스트 환경에서 isValid 를 모두 1로 처리한다.
						df.withColumn("isValid", lit('1'))
					case _ => df
				})
			case _ => None
		}
	}
}

/**
 * 데이터 삭제 / 집계 / 추가 등의 작업을 한다.
 *
 * spark-submit에서 호출할 때나 test case에서 호출할 때 사용함
 * 환경에 따라 달라지는 값은 생성자 파라미터로 받아서 처리함.
 *
 * @param hdfs
 * @param logDf
 * @param reportApiType
 * @param date 처리할 데이터의 날짜
 * @param publisherId
 * @param adProviderId
 */
class CmsNonRkGfpDailyAggregator(hdfs: FileSystem, logDf: Dataset[Row], reportApiType: String, date: String, publisherId: String, adProviderId: String)(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper with AdProviderAggregator {
	val LOG_PREFIX = ".......... [DAILY-NONRK-GFP-CMS]"

	COLLECTION_NAME = "AdProviderNonRkStats"

	private val gfpFeeRateDao = new GfpFeeRateDao()

	// GFP 수수료율 정보
	private var gfpFeeRate: BigDecimal = BigDecimal("0")

	// AP 수수료율 정보 (TTD)
	private var apFeeRate: BigDecimal = BigDecimal("0")

	private var finalDf: Dataset[Row] = _

	import spark.implicits._


	/**
	 * 집계에 필요한 정보 조회하기
	 *    - GFP 수수료 정보 조회
	 *    - AP 수수료 정보 조회 (TTD)
	 */
	override def init(): Unit = {
		// GFP 수수료율 정보 조회
		gfpFeeRate = gfpFeeRateDao.getGfpFeeRate(adProviderId, publisherId, date) match {
			case Some(gfr) => gfr.feeRate.bigDecimalValue
			case _ => throw BusinessException(s"GfpFeeRate does not exist in DB (adProviderId= $adProviderId, publisherId= $publisherId, date= $date)")
		}

		logger.debug(s"$LOG_PREFIX [GfpFeeRate] adProviderId= $adProviderId, publisherId= $publisherId, date= $date, feeRate= $gfpFeeRate")


		// AP 수수료율 정보 조회 (TTD)
		if (reportApiType == "TTD") {
			val env = baseDao.getEnvironment("report-api-ttd-fee-rate")
			if (env.isDefined) {
				apFeeRate = BigDecimal(env.get.getString("value"))
			}
		}

		logger.debug(s"$LOG_PREFIX apFeeRate= $apFeeRate")
	}

	/**
	 * 광고공급자 estimatedReportType 정보 로드
	 */
	def loadEstimatedReportType(): Dataset[Row] = {
		val oidStructType = StructType(List(StructField("oid", StringType, nullable = true)))
		val schema = StructType(List(
			StructField("_id", oidStructType, nullable = true),
			StructField("report", StructType(List(
				StructField("estimatedReportType", MapType(StringType, StringType), nullable = true),
			)))
		))

		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdProviders")
		val df = spark.read.schema(schema).mongo(readConfig)
			.filter(s"_id.oid == '$adProviderId'")
			.select($"report.estimatedReportType")

		applyDefaultEstRptType(df)
	}

	/**
	 * 데이터 집계
	 *
	 * @return Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		val estimatedReportTypeDf = loadEstimatedReportType()

		val groupByList: Seq[String] = Seq(
			"publisherId", "adProviderId", "placeKey", "dealId",
			"os", "country", "responseCreativeType", "adUnitId", "adProviderPlaceId"
		)
		val selectList: Seq[String] = groupByList ++ Seq(
			"eventId", "isValid", "connectionType",
			"cast(cast(bidPriceInKRW as string) as decimal(38,18)) AS bidPriceInKRW",
			"cast(cast(bidPriceInUSD as string) as decimal(38,18)) AS bidPriceInUSD"
		)
		val aggList = Seq(
			expr("SUM(CASE WHEN (eventId == 1 AND adProviderId IS NOT NULL) THEN 1 ELSE 0 END)").as("filledRequest"),
			expr("SUM(CASE WHEN (eventId == 11 AND adProviderId IS NOT NULL AND isValid == '1') THEN 1 ELSE 0 END)").as("impression"),
			expr("SUM(CASE WHEN (eventId == 12 AND adProviderId IS NOT NULL AND isValid == '1') THEN 1 ELSE 0 END)").as("viewableImpression"),

			expr("SUM(CASE WHEN (eventId == 3 AND isValid == '1') THEN 1 ELSE 0 END)").as("clicks"),

			// decimal 로 cast 를 이미 했기 때문에, SUM 할 때는 따로 처리 안 해줘도 됨
			// bidPriceInKRW
			expr("SUM(CASE WHEN eventId == 1 AND adProviderId IS NOT NULL AND connectionType == 'S2S' THEN bidPriceInKRW ELSE 0 END)").as("bidPriceInKRWForFilledRequest"),
			expr("SUM(CASE WHEN eventId == 11 AND adProviderId IS NOT NULL AND isValid == '1' AND connectionType == 'S2S' THEN bidPriceInKRW ELSE 0 END)").as("bidPriceInKRWForImpression"),
			expr("SUM(CASE WHEN eventId == 12 AND adProviderId IS NOT NULL AND isValid == '1' AND connectionType == 'S2S' THEN bidPriceInKRW ELSE 0 END)").as("bidPriceInKRWForViewableImpression"),

			// bidPriceInUSD
			expr("SUM(CASE WHEN eventId == 1 AND adProviderId IS NOT NULL AND connectionType == 'S2S' THEN bidPriceInUSD ELSE 0 END)").as("bidPriceInUSDForFilledRequest"),
			expr("SUM(CASE WHEN eventId == 11 AND adProviderId IS NOT NULL AND isValid == '1' AND connectionType == 'S2S' THEN bidPriceInUSD ELSE 0 END)").as("bidPriceInUSDForImpression"),
			expr("SUM(CASE WHEN eventId == 12 AND adProviderId IS NOT NULL AND isValid == '1' AND connectionType == 'S2S' THEN bidPriceInUSD ELSE 0 END)").as("bidPriceInUSDForViewableImpression"),
		)

		// 실버로그에서 가져온 데이터
		finalDf = logDf
			.filter(s"adProviderId == '$adProviderId'")
			// publisherId 추가
			.withColumn("publisherId", lit(publisherId))
			.selectExpr(selectList: _*)
			.groupBy(groupByList.head, groupByList.tail: _*)
			.agg(aggList.head, aggList.tail: _*)
			// responseCreativeType as creativeType
			.withColumnRenamed("responseCreativeType", "creativeType")
			// creativeType 별 estimatedReportType 추가
			.join(estimatedReportTypeDf, Seq("creativeType"), "left_outer")
			// estimatedReportType 별 impressions 추가
			.withColumn("impressions", when($"estimatedReportType" === "FILL", $"filledRequest")
				.when($"estimatedReportType" === "IMP", $"impression")
				.when($"estimatedReportType" === "VIEW", $"viewableImpression")
				.otherwise(0))
			// estimatedReportType 별 bidPriceInKRW 추가
			.withColumn("bidPriceInKRW", when($"estimatedReportType" === "FILL", $"bidPriceInKRWForFilledRequest")
				.when($"estimatedReportType" === "IMP", $"bidPriceInKRWForImpression")
				.when($"estimatedReportType" === "VIEW", $"bidPriceInKRWForViewableImpression")
				.otherwise(0))
			// estimatedReportType 별 bidPriceInUSD 추가
			.withColumn("bidPriceInUSD", when($"estimatedReportType" === "FILL", $"bidPriceInUSDForFilledRequest")
				.when($"estimatedReportType" === "IMP", $"bidPriceInUSDForImpression")
				.when($"estimatedReportType" === "VIEW", $"bidPriceInUSDForViewableImpression")
				.otherwise(0))
			// 지표가 모두 0인 경우 제외 처리
			.filter("NOT(impressions == 0 AND clicks == 0 AND bidPriceInKRW == 0 AND bidPriceInUSD == 0)")
			// 요약 대상 날짜
			.withColumn("ymd", lit(date))
			// placeKey as adProviderPlaceKey
			.withColumnRenamed("placeKey", "adProviderPlaceKey")
			// impressions as imp
			.withColumnRenamed("impressions", "imp")
			// clicks as clk
			.withColumnRenamed("clicks", "clk")
			// netRevenueUSD :: bidPriceInUSD 에서 AP 관련 수수료 제외 & 수익 변환 ( eCpm/1000 )
			.withColumn("netRevenueUSD", toNetRevenue($"bidPriceInUSD", lit(apFeeRate)))
			// netRevenueKRW :: bidPriceInKRW 에서 AP 관련 수수료 제외 & 수익 변환 ( eCpm/1000 )
			.withColumn("netRevenueKRW", toNetRevenue($"bidPriceInKRW", lit(apFeeRate)))
			// gfpNetRevenueUSD :: netRevenueUSD 에서 GFP 수수료 제외한 수익
			.withColumn("gfpNetRevenueUSD", toGfpNetRevenue($"netRevenueUSD", lit(gfpFeeRate)))
			// gfpNetRevenueKRW :: netRevenueKRW 에서 GFP 수수료 제외한 수익
			.withColumn("gfpNetRevenueKRW", toGfpNetRevenue($"netRevenueKRW", lit(gfpFeeRate)))
			// usdSales :: netRevenueUSD 소수점 6째 자리 ( 7째 자리에서 반올림 )
			.withColumn("usdSales", toSales($"netRevenueUSD"))
			// krwSales :: netRevenueKRW 소수점 6째 자리 ( 7째 자리에서 반올림 )
			.withColumn("krwSales", toSales($"netRevenueKRW"))
			// adProviderPlace_ids, adUnitIds 단건 배열 처리
			.withColumn("adProviderPlace_ids", array(toObjectId($"adProviderPlaceId")))
			.withColumn("adUnitIds", array($"adUnitId"))
			// 몽고디비 ObjectId 타입으로 변환
			.withColumn("publisher_id", toObjectId($"publisherId"))
			.withColumn("adProvider_id", toObjectId($"adProviderId"))
			// 생성한 날짜
			.withColumn("createdAt", functions.current_timestamp)
			// 불필요한 컬럼 제거
			.drop("filledRequest", "impression", "viewableImpression",
				"bidPriceInKRW", "bidPriceInKRWForFilledRequest", "bidPriceInKRWForImpression", "bidPriceInKRWForViewableImpression",
				"bidPriceInUSD", "bidPriceInUSDForFilledRequest", "bidPriceInUSDForImpression", "bidPriceInUSDForViewableImpression",
				"estimatedReportType", "adProviderId", "publisherId", "adUnitId", "adProviderPlaceId")

		// logger.debug("finalDf schema >>>>>>")
		// finalDf.printSchema()
		// finalDf.show(10)
		// finalDf.explain()

		finalDf.cache()
	}

	/**
	 * 데이터 삭제를 위한 futures 생성
	 */
	def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		logger.debug(s"$LOG_PREFIX date= $date, adProviderId= $adProviderId, publisherId= $publisherId")

		val additionalFilters = Seq(
			Filters.eq("publisher_id", new ObjectId(publisherId)),
			Filters.eq("adProvider_id", new ObjectId(adProviderId)),
		)

		super.getFuturesForDeleteByDateRangeForCms(COLLECTION_NAME, List(date), additionalFilters)
	}

	/**
	 * 데이터 추가
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		logger.debug(s"$LOG_PREFIX COLLECTION_NAME=$COLLECTION_NAME")

		val writeOptions = cmsMdbDefaultWriteOptions
			.updated("spark.mongodb.output.collection", COLLECTION_NAME)

		super.writeToMongoDB(df, writeOptions)
	}

	/**
	 * 캐시 제거
	 */
	def unpersist(): Unit = {
		if (finalDf != null) finalDf.unpersist()
	}
}
