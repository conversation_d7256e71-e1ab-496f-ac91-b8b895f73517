package com.navercorp.gfp.biz.cms

import java.util.Date
import scala.collection.mutable
import scala.concurrent.Future

import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{MapType, StringType, StructField, StructType}
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, DateTimeZone, Period}

import com.navercorp.gfp.biz.silver.SilverLogType
import com.navercorp.gfp.biz.silver.SilverLogType.{ErSspServer, SilverLogType}
import com.navercorp.gfp.core.BaseConstant.{DECIMAL_TYPE_15_6, DECIMAL_TYPE_17_6}
import com.navercorp.gfp.core.BaseEnv.{SILVER_CLIENT_INPUT_PATH, SILVER_SERVER_INPUT_PATH}
import com.navercorp.gfp.core.BaseUdf.{getEstimatedRevenue, toObjectId}
import com.navercorp.gfp.core._
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.util.{AggregatorUtil, HdfsUtil, NeloUtil, TimeUtil}

object CmsAdProviderPlaceBiddingGroupAggregator extends BaseAggregator {
    val LOG_PREFIX = ".......... [CMS-HOURLY-GFP-BG]"

    def main(rawArgs: Array[String]): Unit = {
        val args = initArgs(rawArgs)

        // 대상 일자 설정
        val datetime = args(0)

        try {
            logger.debug(s"$LOG_PREFIX target datetime: $datetime")

            // 이력쌓기 - IN_PROGRESS
            val inProgressHist = SummaryHistory(
                datetime = Option(datetime),
                aggregatorName = Option(this.getClass.getName),
                spark = Option(Spark(
                    sparkAppId = Option(spark.sparkContext.applicationId),
                    sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
                    sparkStartedAt = Option(new Date)
                ))
            )
            this.upsertSummaryHistory(inProgressHist)

            val gfpPubIds: Vector[String] = publisherDao.getGfpPublisherIds()

            // 로그 로딩
            val sPaths = getPaths(datetime, SilverLogType.ErSspServer, gfpPubIds)
            val cPaths = getPaths(datetime, SilverLogType.ErSspClient, gfpPubIds)

            if (sPaths.nonEmpty && cPaths.nonEmpty) {
                val serverDf = loadSilverLog(sPaths)
                val clientDf = loadSilverLog(cPaths)

                // 집계
                val aggregator = new CmsAdProviderPlaceBiddingGroupAggregator(datetime, serverDf, clientDf, gfpPubIds)
                val aggDf = aggregator.aggregate()

                // 기존 데이터 삭제
                aggregator.delete(aggregator.getFuturesForDelete())

                // MongoDB에 저장
                aggregator.write(aggDf)
            } else {
                logger.warn(s"$LOG_PREFIX 서버 또는 클라이언트 경로가 존재하지 않음. datetime:$datetime sPaths.length:${sPaths.length} cPaths.length:${cPaths.length}")
            }

            // 이력쌓기 - COMPLETE
            val completeHist = SummaryHistory(
                spark = Option(Spark(
                    sparkAppState = Option(SparkAppState.COMPLETE.toString),
                    sparkEndedAt = Option(new Date)
                ))
            )
            this.upsertSummaryHistory(completeHist)
        } catch {
            case t: Throwable =>
                // 이력쌓기 - FAILURE
                val failureHist = SummaryHistory(
                    spark = Option(Spark(
                        sparkAppState = Option(SparkAppState.FAILURE.toString),
                        sparkAppError = Option(t.getMessage),
                        sparkEndedAt = Option(new Date)
                    ))
                )
                this.upsertSummaryHistory(failureHist)
                logger.error(s"$LOG_PREFIX GFP 비딩그룹 지표 생성 실패. sparkAppId=${spark.sparkContext.applicationId} datetime=$datetime summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}", t)

                throw t
        } finally {
            NeloUtil.waitFor()
        }
    }

    def getPaths(datetime: String, logType: SilverLogType, gfpPubIds: Seq[String]): Seq[String] = {
        val paths = mutable.ArrayBuffer.empty[String]
        val dtRange = getDateTimeRangeBetweenMidnight(datetime)

        for (dt <- dtRange) {
            for (pubId <- gfpPubIds) {
                val path =
                    if (logType == ErSspServer)
                        s"$SILVER_SERVER_INPUT_PATH/${dt.toString("yyyyMMdd-HH")}/publisherId=$pubId"
                    else
                        s"$SILVER_CLIENT_INPUT_PATH/${dt.toString("yyyyMMdd-HH")}/publisherId=$pubId"

                if (HdfsUtil.exists(hdfs, path)) {
                    paths += path
                }
            }
        }

        logger.debug(s"$LOG_PREFIX 대상경로..........................................")
        paths.foreach(path => logger.debug(s"$LOG_PREFIX 대상경로: $path"))
        paths
    }

    /**
     * datetime 이 속한 날짜에서
     * yyyymmdd00 부터 yyyymmddHH 까지의 datetime 리스트를 반환
     * 실버로그 컴팩션이 완료된 datetime 에 해당하는 로그만 사용하기 위한 목적
     *
     * @param datetime
     * @return
     */
    def getDateTimeRangeBetweenMidnight(datetime: String): List[DateTime] = {
        // 날짜 포맷
        val tFormat = DateTimeFormat.forPattern("yyyyMMddHH")
        val tMidnightDt = tFormat.parseDateTime(s"${datetime.slice(0, 8)}00")
        val tEndDt = tFormat.parseDateTime(s"$datetime")

        val midnightDateTime = new DateTime(tMidnightDt, DateTimeZone.forID("Asia/Seoul"))
        val endDateTime = new DateTime(tEndDt, DateTimeZone.forID("Asia/Seoul"))

        dateTimeRange(midnightDateTime, endDateTime, Period.hours(1)).toList
    }
}

class CmsAdProviderPlaceBiddingGroupAggregator(datetime: String, serverDf: Dataset[Row], clientDf: Dataset[Row], gfpPubIds: Seq[String])(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper with AdProviderAggregator {
    val LOG_PREFIX = ".......... [CMS-HOURLY-GFP-BG]"

    import spark.implicits._

    COLLECTION_NAME = "SummaryAdProviderPlaceStats"

    val date = datetime.slice(0, 8)
    val dimensions = List("publisherId", "adProviderId", "adProviderPlaceId", "biddingGroupId")
    val metrics = List("apRequest", "filledApRequest", "imp", "filledRequest", "bidPrice", "sspEstimatedImpressions", "sspEstimatedNetRevenue")

    def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
        // 0. 집계를 위한 Df 준비
        val apDf = broadcast(applyDefaultEstRptType(loadAdProviders()))
        val feeRateDf = broadcast(loadGfpFeeRate())
        val sDf = prepareServerDf(serverDf)
        val cDf = prepareClientDf(clientDf, apDf)

        // 1. 집계
        val df1 = getFillRateStats(sDf, cDf)
        val df2 = getImpressionCpm(cDf)
        val df3 = getFilledRequestCpm(cDf)
        val df4 = getEstimatedStats(cDf, feeRateDf)

        val bgStatsDf = df1
            .unionByName(df2, true)
            .unionByName(df3, true)
            .unionByName(df4, true)

        // 2. DB 스키마에 맞게 수정
        val finalDf = filterAllZeroMetrics(bgStatsDf, metrics)
            // 집계 대상 날짜
            .withColumn("ymd", lit(date))
            // 생성한 날짜
            .withColumn("createdAt", functions.current_timestamp)
            // MongoDB TTL Index 에 의해 삭제될 날짜
            .withColumn("expiredAt", TimeUtil.getExpiredAtDailyAsColumn($"ymd", 7))
            // 몽고디비 ObjectId 타입으로 변환 및 중복 필드 제거
            .withColumn("publisher_id", toObjectId($"publisherId"))
            .withColumn("adProvider_id", toObjectId($"adProviderId"))
            .withColumn("adProviderPlace_id", toObjectId($"adProviderPlaceId"))
            .withColumn("biddingGroup_id", toObjectId($"biddingGroupId"))
            .drop(dimensions: _*)

        println(s"$LOG_PREFIX 최종 스키마 ........")
        finalDf.printSchema
        // finalDf.show(allStat2.count().toInt, false)

        finalDf
    }

    /**
     * 서버 데이터셋에서 필요한 필드만 추출
     *
     * @param df
     * @return
     */
    def prepareServerDf(df: Dataset[Row]): Dataset[Row] = {
        val df1 = filterTest(df)
            .withColumn("res", explode_outer($"responses"))
            .filter($"res.adProviderId".isNotNull && $"res.adProviderId" =!= "")
            .filter($"res.biddingGroupId".isNotNull && $"res.biddingGroupId" =!= "")
            .selectExpr(
                "publisherId",
                "res.adProviderId",
                "res.adProviderPlaceId",
                "res.biddingGroupId",
                "res.stat",
                "res.connectionType",
                "NVL(res.requestCreativeTypes[0], '-') AS requestCreativeType",
                "NVL(res.responseCreativeType, '-') AS responseCreativeType",
            )

        df1
    }

    /**
     * 클라이언트 데이터셋에서 필요한 필드만 추출
     *
     * @param df
     * @return
     */
    def prepareClientDf(df: Dataset[Row], apDf: Dataset[Row]): Dataset[Row] = {
        val df1 = filterTest(df)
            .filter($"adProviderId".isNotNull && $"adProviderId" =!= "")
            .filter($"biddingGroupId".isNotNull && $"biddingGroupId" =!= "")
            .selectExpr(
                AggregatorUtil.selectExprOfIsValid(),
                "eventId",
                "publisherId",
                "adProviderId",
                "adProviderPlaceId",
                "biddingGroupId",
                "bidPrice",
                "stat",
                "connectionType",
                "NVL(requestCreativeTypes[0], '-') AS requestCreativeType",
                "NVL(responseCreativeType, '-') AS responseCreativeType",
            )
            .withColumn("sspEstimatedRevenue", getEstimatedRevenue($"bidPrice").cast(DECIMAL_TYPE_15_6))

        // 추정 과금 노출수 및 관련 메트릭을 구하기 위해 estimatedReportType 추가
        val df2 = df1
            .join(apDf,
                (df1.col("adProviderId") === apDf.col("adProviderId")) &&
                    (df1.col("responseCreativeType") === apDf.col("creativeType")), "left")
            .select(
                df1.col("*"),
                apDf.col("estimatedReportType")
            )

        df2
    }

    def loadAdProviders(): Dataset[Row] = {
        val oidStructType = StructType(List(StructField("oid", StringType, true)))
        val schema = StructType(List(
            StructField("_id", oidStructType, true),
            StructField("report", StructType(List(
                StructField("estimatedReportType", MapType(StringType, StringType), true),
            )))
        ))
        val readConfig = BaseEnv.mdbDefaultReadConfig
            .withOption("spark.mongodb.input.collection", "SyncAdProviders")

        spark.read.schema(schema).mongo(readConfig)
            .select(
                $"_id.oid".as("adProviderId"),
                $"report.estimatedReportType"
            )
    }

    def loadGfpFeeRate(): Dataset[Row] = {
        val readConfig = BaseEnv.mdbDefaultReadConfig
            .withOption("spark.mongodb.input.collection", "GfpFeeRate")
            .withOption("spark.mongodb.input.batchSize", "2000")

        spark.read
            .mongo(readConfig)
            .filter(s"date == '$date'")
            .filter($"publisher_id.oid".isin(gfpPubIds: _*))
            .select(
                $"publisher_id.oid".as("publisherId"),
                $"adProvider_id.oid".as("adProviderId"),
                $"feeRate"
            )
    }

    /**
     * 서버, 클라이언트 로그로부터 선출률 관련 지표 계산
     *
     * @param sDf
     * @param cDf
     * @return
     */
    def getFillRateStats(sDf: Dataset[Row], cDf: Dataset[Row]): Dataset[Row] = {
        // AdProviderPlaces.creativeType=COMBINED 인 경우에 대한 처리를 위해 디멘전 추가
        val tmpDims = dimensions ++ Seq("requestCreativeType", "responseCreativeType")

        // refineApReqResForCombinedReqCt 를 적용하기 위해 adProviderRequests, adProviderResponses 로 집계하지만
        // DB 적재 시엔 apRequest, filledApRequest 로 변경
        val unionDf = sDf.unionByName(cDf, true)
            .groupBy(tmpDims.head, tmpDims.tail: _*)
            .agg(
                // 광고공급자 호출수
                expr("SUM(CASE WHEN (eventId IS NULL AND connectionType == 'S2S') OR (eventId ==  1 AND connectionType == 'C2S') OR (eventId == 91 AND connectionType == 'C2S') THEN 1 ELSE 0 END)").as("adProviderRequests"),
                // 광고공급자 응답수
                expr("SUM(CASE WHEN (eventId IS NULL AND connectionType == 'S2S' AND stat == 1) OR (eventId == 1 AND connectionType == 'C2S') THEN 1 ELSE 0 END)").as("adProviderResponses"),
            )

        // 광고공급자 호출수, 응답수 조정
        val refinedDf = refineApReqResForCombinedReqCt(unionDf, dimensions :+ "requestCreativeType")

        // responseCreativeType, responseCreativeType 이 포함된 디멘전을 리포트 디멘전만으로 재합산
        refinedDf.groupBy(dimensions.head, dimensions.tail: _*)
            .agg(
                sum("adProviderRequests").as("adProviderRequests"),
                sum("adProviderResponses").as("adProviderResponses")
            )
            .withColumnRenamed("adProviderRequests", "apRequest")
            .withColumnRenamed("adProviderResponses", "filledApRequest")
            .withColumn("column", lit("FillRate"))
            .na.fill(0, Vector("apRequest", "filledApRequest"))
    }

    /**
     * 클라이언트 로그로부터 노출 수 기반 CPM 관련 지표 계산
     * eventId == 11 인 경우만 집계 하는데, 서버로그엔 해당하는 eventId 가 없다.
     *
     * @param cDf
     * @return
     */
    def getImpressionCpm(cDf: Dataset[Row]): Dataset[Row] = {
        val df1 = cDf
            .filter("connectionType == 'S2S'")
            .groupBy(dimensions.head, dimensions.tail: _*)
            .agg(
                // 노출수
                expr("SUM(CASE WHEN (eventId == 11 AND isValid == '1') THEN 1 ELSE 0 END)").as("imp"),
                // 노출수 기준 bidPrice 합
                expr("SUM(CASE WHEN (eventId == 11 AND isValid == '1') THEN bidPrice ELSE 0 END)").as("bidPrice")
            )
            .withColumn("connectionType", lit("S2S"))
            .withColumn("column", lit("ImpressionCpm"))
            .na.fill(0, Vector("imp", "bidPrice"))

        df1
    }

    /**
     * 클라이언트 로그로부터 선출 수 CPM 관련 지표 계산
     * eventId == 1 인 경우만 집계 하는데, 서버로그엔 해당하는 eventId 가 없다.
     *
     * @param cDf
     * @return
     */
    def getFilledRequestCpm(cDf: Dataset[Row]): Dataset[Row] = {
        val df1 = cDf
            .filter("connectionType == 'S2S'")
            .groupBy(dimensions.head, dimensions.tail: _*)
            .agg(
                // 선출수
                expr("SUM(CASE WHEN eventId == 1 THEN 1 ELSE 0 END)").as("filledRequest"),
                // 선출수 기준 bidPrice 합
                expr("SUM(CASE WHEN eventId == 1 THEN bidPrice ELSE 0 END)").as("bidPrice")
            )
            .withColumn("connectionType", lit("S2S"))
            .withColumn("column", lit("FilledRequestCpm"))
            .na.fill(0, Vector("filledRequest", "bidPrice"))

        df1
    }

    /**
     * 서버, 클라이언트 로그로부터 추정입찰가 관련 지표 계산
     * NAM-API 로 제공되는 AdProviderPlaceBiddingGroupDailyAggregator 와 달리 cpm 까지 집계하여 DB 에 저장
     *
     * [2024. 12. 03. https://jira.navercorp.com/browse/GFP-449 ] 거래/비딩그룹 netBidPrice 적용
     *
     * 기존 '노출/선출 기준 bidPrice 합' 은 nam-api 에서 1000 으로 나누어 나가기 때문에 sparkling-s 집계에선 처리하지 않았으나
     * '추정 수익' 의 경우 api 에서 나누지 않고 모두 처리하여 DB 로 쌓는 것으로 변경하여 getEstimatedRevenue() 를 적용.
     *
     * 추후 노출/선출 기준 bidPrice 합은 fade out 되므로 용어 통일 및 최신 로직을 따름
     * bidPriceSum -> 로그 원본 bidPrice 를 단순히 합한 값 (cpm 기준)
     * ~ revenue -> bidPrice(Sum) 을 1000 으로 나눈 값 (cpm 기준이 아님)
     * ~ cpm -> bidPriceSum 에서 구하려면 1000 을 곱할 필요 없음
     * -> revenue 에서 구하려면 1000 을 곱해야 함
     *
     * @param sDf
     * @param cDf
     * @return
     */
    def getEstimatedStats(df: Dataset[Row], feeRateDf: Dataset[Row]): Dataset[Row] = {
        df.groupBy(dimensions.head, dimensions.tail: _*)
            .agg(
                // 추정 과금 노출수
                sum(when($"estimatedReportType" === "FILL" && $"eventId" === 1, 1L)
                    .when($"estimatedReportType" === "IMP" && $"eventId" === 11 && $"isValid" === "1", 1L)
                    .when($"estimatedReportType" === "VIEW" && $"eventId" === 12 && $"isValid" === "1", 1L)
                    .otherwise(0L)).as("sspEstimatedImpressions"),
                // 추정 수익
                sum(when($"estimatedReportType" === "FILL" && $"eventId" === 1, $"sspEstimatedRevenue")
                    .when($"estimatedReportType" === "IMP" && $"eventId" === 11 && $"isValid" === "1", $"sspEstimatedRevenue")
                    .when($"estimatedReportType" === "VIEW" && $"eventId" === 12 && $"isValid" === "1", $"sspEstimatedRevenue")
                    .otherwise(0L)).as("sspEstimatedRevenue"),
            )
            // 추정 순수익 (GFP 수수료 적용)
            .join(feeRateDf, Seq("publisherId", "adProviderId"), "left")
            .withColumn("sspEstimatedNetRevenue", coalesce($"sspEstimatedRevenue" * (lit(1) - coalesce($"feeRate", lit(0))), lit(0)).cast(DECIMAL_TYPE_17_6))
            // 추정 낙찰가 (추정 cpm)
            .withColumn("sspEstimatedNetCpm", when($"sspEstimatedImpressions" > 0, $"sspEstimatedNetRevenue" / $"sspEstimatedImpressions" * 1000).otherwise(0).cast(DECIMAL_TYPE_17_6))
            .withColumn("column", lit("EstimatedNetCpm"))
            .drop("feeRate", "bidPrice", "sspEstimatedRevenue")
            .na.fill(0, Vector("sspEstimatedImpressions", "sspEstimatedNetRevenue", "sspEstimatedNetCpm"))
    }

    def getFuturesForDelete(param: Option[T] = None): List[Future[Option[Boolean]]] = {
        super.getFuturesForDeleteByGfpPublisherIdAndDateForCms(COLLECTION_NAME, date)
    }

    /**
     * MongoDB에 쓰기
     *
     * @param df
     * @return
     */
    def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
        logger.debug(s"$LOG_PREFIX COLLECTION_NAME=$COLLECTION_NAME")

        val writeOptions = BaseEnv.cmsMdbDefaultWriteOptions
            .updated("spark.mongodb.output.collection", COLLECTION_NAME)

        super.writeToMongoDB(df, writeOptions)
    }
}
