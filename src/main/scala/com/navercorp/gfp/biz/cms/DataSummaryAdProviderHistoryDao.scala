package com.navercorp.gfp.biz.cms

import com.mongodb.client.MongoCollection
import com.mongodb.client.model.Updates.{combine, set, setOnInsert}
import com.mongodb.client.model.{Filters, UpdateOptions}
import com.navercorp.gfp.biz.adprovider.AdProviderDailyType
import com.navercorp.gfp.core.database.CmsDatabase
import com.navercorp.gfp.meta.adproviderinfo.AdProviderInfo
import org.apache.logging.log4j.{LogManager, Logger}
import org.bson.Document
import org.bson.types.ObjectId

import java.util.Date

class DataSummaryAdProviderHistoryDao {
	private val logger: Logger = LogManager.getLogger(this.getClass)

	/**
	 * 광고공급자 통계 실행 정보 업데이트
	 * @param dailyType
	 * @param date
	 * @param adProviderInfos
	 */
	def upsertDataSummaryAdProviderHistory(dailyType: AdProviderDailyType.Value, date: String, adProviderInfos: Seq[AdProviderInfo]): Unit = {
		logger.debug(s"DataSummaryAdProviderHistoryDao.upsertDataSummaryAdProviderHistory() 실행됨 ( dailyType=$dailyType, date=$date, adProviderInfos=$adProviderInfos )")

		val coll: MongoCollection[Document] = CmsDatabase.getDatabase.getCollection("DataSummaryAdProviderHistory")

		var dateType = ""
		Option(dailyType) match {
			case Some(AdProviderDailyType.AP) =>
				dateType = "apSummaryLastDate"
			case Some(AdProviderDailyType.GFP) =>
				dateType = "gfpSummaryLastDate"
			case _ => throw new Exception(s"DataSummaryAdProviderHistory 업데이트 실패 (dailyType= $dailyType)")
		}

		adProviderInfos.map { adProviderInfo =>
			val filters = Filters.and(
				Filters.eq("publisher_id", adProviderInfo.publisher_id),
				Filters.eq("adProvider_id", adProviderInfo.adProvider_id),
			)

			val update = combine(set(dateType, date), set("modifiedAt", new Date()), setOnInsert("createdAt", new Date()))

			// 데이터가 없는 경우 insert, 데이터가 있는 경우 update
			val exists = findDataSummaryAdProviderHistory(adProviderInfo.publisher_id, adProviderInfo.adProvider_id)
			exists match {
				case Some(hist) if hist.get(dateType).eq(null) || date.compareTo(hist.get(dateType).toString) > 0 =>
					coll.updateOne(filters, update)
				case None =>
					val options: UpdateOptions = new UpdateOptions().upsert(true)
					coll.updateOne(filters, update, options)
				case _ =>
			}
		}
	}

	def findDataSummaryAdProviderHistory(publisher_id: ObjectId, adProvider_id: ObjectId): Option[Document] = {
		val coll: MongoCollection[Document] = CmsDatabase.getDatabase.getCollection("DataSummaryAdProviderHistory")

		val filters = Filters.and(
			Filters.eq("publisher_id", publisher_id),
			Filters.eq("adProvider_id", adProvider_id),
		)

		Option(coll.find(filters).first())
	}
}
