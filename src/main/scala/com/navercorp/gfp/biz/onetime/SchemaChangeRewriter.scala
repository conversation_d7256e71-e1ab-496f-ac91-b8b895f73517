package com.navercorp.gfp.biz.onetime

import java.util.Date
import scala.collection.mutable
import scala.util.Try

import org.apache.hadoop.fs.{FileStatus, Path}
import org.apache.spark.sql._
import org.apache.spark.sql.types._
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat

import com.navercorp.gfp.core.BaseAggregator
import com.navercorp.gfp.core.BaseEnv.{AMBER_ROOT, SILVERGREY_ROOT}
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.exception.BusinessException
import com.navercorp.gfp.util.NeloUtil

/*
	Silvergrey 스키마 변경
		- 대상 : /user/gfp-data/silvergrey/{rk, nonrk}
		- revenue 타입 double -> string

	Amber 스키마 변경
		- 대상 : /user/gfp-data/amber/{ap, gfp}
		- revenue 타입 double -> string
*/


object SilvergreySchemaChangeAggregator extends BaseAggregator {
	val LOG_PREFIX = s".......... [SILVERGREY-SCHEMA-CHANGE]"

	import spark.implicits._

	private var sparkAppId: String = ""

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		val yyyymmdd = args(0)

		// RK, NONRK
		val logType = args(1)

		try {
			// args 밸리데이션 체크
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(yyyymmdd)).isSuccess, s"yyyymmdd($yyyymmdd) is invalid format (must be yyyyMMdd)")

			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(yyyymmdd),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
			)
			this.upsertSummaryHistory(inProgressHist)

			logger.debug(s"$LOG_PREFIX logType= $logType, yyyymmdd= $yyyymmdd")

			val dt: DateTime = DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(yyyymmdd)

			// /user/gfp-data/silvergrey/rk/2024/01/01
			val rootOutputPath = s"$SILVERGREY_ROOT/${logType.toLowerCase()}/${dt.toString("yyyy/MM/dd")}"
			val rootInputPath = s"${SILVERGREY_ROOT}_backup/${logType.toLowerCase()}/${dt.toString("yyyy/MM/dd")}"

			// ap 목록 추출
			val apPathNames = getSubPathNames(rootInputPath)

			logger.debug(s"target apPathNames = $apPathNames")

			for (apPathName <- apPathNames) {
				// inputPath= /user/gfp-data/silvergrey_backup/rk/2024/01/01/adProviderId=xxxxxx
				val inputPath = s"$rootInputPath/$apPathName"
				val outputPath = s"$rootOutputPath/$apPathName"

				val pubPathNames = getSubPathNames(inputPath)

				if (pubPathNames.nonEmpty) {
					// AP별 실버그레이 로그 로딩
					val sgDf = loadParquetLog(Seq(inputPath)) match {
						case Some(df) => df
						case _ => spark.emptyDataFrame
					}

					// rk/nonrk 스키마가 다르므로 logType 으로 구분
					val finalDf = changeSchema(sgDf, logType)

					rewrite(finalDf, outputPath)

					// pub 목록 추출
					for (pubPathName <- pubPathNames) {
						hdfs.rename(new Path(s"$outputPath/_$pubPathName"), new Path(s"$outputPath/$pubPathName"))
					}
				}
			}

			// 이력쌓기 - 완료 (COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					)),
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	def getSubPathNames(path: String): Seq[String] = {
		val pathNames = mutable.ArrayBuffer.empty[String]

		// path = /user/gfp-data/temp/ap_summary/2024/01/01/
		val fsPath = new Path(path)
		val fileStatusList: Array[FileStatus] = hdfs.listStatus(fsPath)
		for (fileStat <- fileStatusList) {
			if (fileStat.isDirectory) {
				// _adProviderId=xxxxxx or _publisherId=xxxxxx
				pathNames += fileStat.getPath.getName
			}
		}

		pathNames
	}

	def changeSchema(logDf: Dataset[Row], logType: String): Dataset[Row] = {
		val sgDf = logDf
			.withColumn("revenueKRW", $"revenueKRW".cast(StringType))
			.withColumn("revenueUSD", $"revenueUSD".cast(StringType))
			.withColumn("netRevenueKRW", $"netRevenueKRW".cast(StringType))
			.withColumn("netRevenueUSD", $"netRevenueUSD".cast(StringType))
			.withColumn("gfpNetRevenueKRW", $"gfpNetRevenueKRW".cast(StringType))
			.withColumn("gfpNetRevenueUSD", $"gfpNetRevenueUSD".cast(StringType))

		val finalDf = if (logType.equals("RK"))
			sgDf
				.withColumn("serviceAmountKRW", $"serviceAmountKRW".cast(StringType))
				.withColumn("serviceAmountUSD", $"serviceAmountUSD".cast(StringType))
		else
			sgDf

		logger.debug("finalDf schema >>>>>>")
		finalDf.printSchema()
		finalDf.show(10, false)

		finalDf
	}

	def rewrite(logDf: Dataset[Row], outputPath: String): Unit = {
		logDf
			.withColumn("_publisherId", $"publisherId")
			.write
			.mode("overwrite")
			.option("dfs.blocksize", 32 * 1024 * 1024)
			.option("parquet.block.size", 32 * 1024 * 1024)
			.partitionBy("_publisherId")
			.parquet(outputPath)
	}

}

object AmberSchemaChangeAggregator extends BaseAggregator {
	val LOG_PREFIX = s".......... [AMBER-SCHEMA-CHANGE]"

	import spark.implicits._

	private var sparkAppId: String = ""

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		val yyyymmdd = args(0)

		// AP, GFP
		val logType = args(1)

		try {
			// args 밸리데이션 체크
			require(Try(DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(yyyymmdd)).isSuccess, s"yyyymmdd($yyyymmdd) is invalid format (must be yyyyMMdd)")

			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(yyyymmdd),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				)),
			)
			this.upsertSummaryHistory(inProgressHist)

			logger.debug(s"$LOG_PREFIX logType= $logType, yyyymmdd= $yyyymmdd")

			val dt: DateTime = DateTimeFormat.forPattern("yyyyMMdd").parseDateTime(yyyymmdd)

			// /user/gfp-data/amber/ap/2024/01/01
			val rootOutputPath = s"$AMBER_ROOT/${logType.toLowerCase()}/${dt.toString("yyyy/MM/dd")}"
			val rootInputPath = s"${AMBER_ROOT}_backup/${logType.toLowerCase()}/${dt.toString("yyyy/MM/dd")}"

			// ap 목록 추출
			val apPathNames = getSubPathNames(rootInputPath)

			logger.debug(s"target apPathNames = $apPathNames")

			for (apPathName <- apPathNames) {
				// inputPath= /user/gfp-data/amber_backup/ap/2024/01/01/adProviderId=xxxxxx
				val inputPath = s"$rootInputPath/$apPathName"
				val outputPath = s"$rootOutputPath/$apPathName"

				// AP별 앰버 로그 로딩
				val amDf = loadParquetLog(Seq(inputPath)) match {
					case Some(df) => df
					case _ => spark.emptyDataFrame
				}

				// ap/gfp 스키마가 다르므로 logType 으로 구분
				val finalDf = changeSchema(amDf, logType)

				rewrite(finalDf, outputPath)

				// pub 목록 추출
				val pubPathNames = getSubPathNames(inputPath)
				for (pubPathName <- pubPathNames) {
					hdfs.rename(new Path(s"$outputPath/_$pubPathName"), new Path(s"$outputPath/$pubPathName"))
				}
			}

			// 이력쌓기 - 완료 (COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					)),
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	def getSubPathNames(path: String): Seq[String] = {
		val pathNames = mutable.ArrayBuffer.empty[String]

		// path = /user/gfp-data/temp/ap_summary/2024/01/01/
		val fsPath = new Path(path)
		val fileStatusList: Array[FileStatus] = hdfs.listStatus(fsPath)
		for (fileStat <- fileStatusList) {
			if (fileStat.isDirectory) {
				// _adProviderId=xxxxxx or _publisherId=xxxxxx
				pathNames += fileStat.getPath.getName
			}
		}

		pathNames
	}

	def changeSchema(logDf: Dataset[Row], logType: String): Dataset[Row] = {
		val finalDf = if (logType.equals("AP"))
			logDf.withColumn("revenueKRW", $"revenueKRW".cast(StringType))
				.withColumn("revenueUSD", $"revenueUSD".cast(StringType))
				.withColumn("netRevenueKRW", $"netRevenueKRW".cast(StringType))
				.withColumn("netRevenueUSD", $"netRevenueUSD".cast(StringType))
				.drop("gfpImpressions", "gfpViewableImpressions", "gfpClicks", "gfpEstimatedImpressions")
		else
			logDf.drop("impressions", "clicks", "revenueKRW", "revenueUSD", "netRevenueKRW", "netRevenueUSD")

		logger.debug("finalDf schema >>>>>>")
		finalDf.printSchema()
		finalDf.show(10, false)

		finalDf
	}

	def rewrite(logDf: Dataset[Row], outputPath: String): Unit = {
		logDf
			.withColumn("_publisherId", $"publisherId")
			.write
			.mode("overwrite")
			.option("dfs.blocksize", 32 * 1024 * 1024)
			.option("parquet.block.size", 32 * 1024 * 1024)
			.partitionBy("_publisherId")
			.parquet(outputPath)
	}

}
