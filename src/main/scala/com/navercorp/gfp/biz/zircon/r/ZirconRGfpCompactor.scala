package com.navercorp.gfp.biz.zircon.r

import java.util.Date
import java.util.concurrent.{ExecutorService, ForkJoinPool}
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, Future}

import org.apache.hadoop.fs.Path
import org.bson.Document
import org.bson.types.ObjectId
import org.joda.time.DateTime

import com.navercorp.gfp.core.BaseAggregator
import com.navercorp.gfp.core.BaseEnv.{ZIRCON_R_GFP_COMPACTION_PATH, ZIRCON_R_GFP_INTERMEDIATE_PATH, ZIRCON_R_GFP_WAREHOUSE_PATH}
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, TimeUtil}

/**
 * [ fair scheduler 적용 ]
 * 참고
 * https://towardsdatascience.com/apache-spark-sharing-fairly-between-concurrent-jobs-d1caba6e77c2
 * https://coder-question-ko.com/cq-ko-blog/78615
 *
 * spark.sparkContext.setLocalProperty("spark.scheduler.mode", "FAIR")
 * 이 같은 형식은 무슨 이유에서인지 동작하지 않아 spark-submit 시 --conf 옵션으로 해결함.
 */

case class CompactionInfo(pubId: String, isCompacted: Boolean, intermediatePath: String, compactionPath: String, warehousePath: String)

object ZirconRGfpCompactor extends BaseAggregator {

	val LOG_PREFIX = ".......... [ZRGFP-COMPACTOR]"
	private val ONE_HUNDRED_MB = 100 * 1024 * 1024

	def main(rawArgs: Array[String]): Unit = {
		val startTime = DateTime.now()

		val args = initArgs(rawArgs)

		try {
			logger.debug(s"$LOG_PREFIX applicationId: ${spark.sparkContext.applicationId} args:${args.mkString(",")}")

			// kind: "regular" or "trace_silver" or "manual"
			val kind = args(0)

			// 대상 일자
			val ymd = args(1)

			// 매체. 특정 매체 또는 "*"
			val pubId = args(2)

			val sTraceId = if (args.length > 3 && Some(args(3)).nonEmpty) args(3) else "-"

			// 이력쌓기 - IN_PROGRESS
			val sparkDocForInProgress = Spark(
				sparkAppId = Option(spark.sparkContext.applicationId),
				sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
				sparkStartedAt = Option(new Date)
			)

			val publisher_id = if (pubId == "*") null else new ObjectId(pubId)
			val silverTrace_id = if (sTraceId == "-") null else new ObjectId(sTraceId)

			val inProgressHist = SummaryHistory(
				datetime = Option(ymd),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(sparkDocForInProgress),
				detail = Option(Map( // 처리유형 기술
					"kind" -> kind,
					"publisher_id" -> publisher_id,
					"silverTrace_id" -> silverTrace_id
				))
			)
			this.upsertSummaryHistory(inProgressHist)

			// 컴팩션
			optimize(ymd, pubId)

			/*
				- warehouse/{yyyy}/{mm}/{dd}/_COMPACTION_SUCCESS 파일 생성
				- write 시에는 {yyyy}/{mm}/{dd}/_publisherId={pubId}/_COMPACTION_SUCCESS가 자동으로 생기지만
				- 파일 브라우저를 통해 볼 때 완료 여부를 직관적으로 파악하기 위해 _SUCCESS 파일 생성함
				- 이미 생성된 경우는 삭제하고 다시 생성하여 생성일자 갱신
			 */
			createSuccessFile(ymd)

			// 이력쌓기 - COMPLETE
			val sparkDocForComplete = Spark(
				sparkAppState = Option(SparkAppState.COMPLETE.toString),
				sparkEndedAt = Option(new Date)
			)
			val completeHist = SummaryHistory(
				spark = Option(sparkDocForComplete)
			)
			this.upsertSummaryHistory(completeHist)

			logger.debug(s"$LOG_PREFIX 생성 완료. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get}")
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val sparkDocForFailure = Spark(
					sparkAppState = Option(SparkAppState.FAILURE.toString),
					sparkAppError = Option(t.getMessage),
					sparkEndedAt = Option(new Date)
				)
				val failureHist = SummaryHistory(
					spark = Option(sparkDocForFailure)
				)
				this.upsertSummaryHistory(failureHist)

				logger.error(s"$LOG_PREFIX 생성 실패. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")
				throw t;
		} finally {
			NeloUtil.waitFor()

			// 소요 시간 측정
			val endTime = DateTime.now()
			val elapsedTime = endTime.getMillis - startTime.getMillis
			logger.debug(s"$LOG_PREFIX 소요시간=${elapsedTime / 1000}sec\n\n")
		}
	}

	/**
	 * - PUB별 병렬처리
	 * - 기존 PUB warehouse 삭제
	 * - compaction PUB 경로를 warehouse PUB 경로로 리네임 및 _COMPACTION_SUCCESS 파일 생성
	 * - PUB intermediate 삭제
	 *
	 * @param ymd
	 * @param argPubId
	 */
	private def optimize(ymd: String, argPubId: String): Unit = {
		/*
		[ Executor Context ]

			// java의 Executor를 사용하는 방식
			implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))

			// java의 ForkJoinPool을 사용하는 방식. 스레드 개수 지정 없음
			implicit val ec = ExecutionContext.fromExecutorService(new scala.concurrent.forkjoin.ForkJoinPool())

			// java의 ForkJoinPool을 사용하는 방식. 스레드 개수 지정 가능
			val forkJoinPool: ExecutorService = new ForkJoinPool(16)
			implicit val ec: ExecutionContext = ExecutionContext.fromExecutorService(forkJoinPool)

			참고
				- 스칼라 강좌 (37) 동시성을 위한 ExecutorContext : https://hamait.tistory.com/768
				- SCALA BOOK > SCALA FUTURES : https://docs.scala-lang.org/overviews/scala-book/futures.html#inner-main
				- FUTURES > FUTURES AND PROMISES : https://docs.scala-lang.org/overviews/core/futures.html#inner-main
		 */
		// 동시 처리 개수
		val threadNum = getThreadNum()
		val forkJoinPool: ExecutorService = new ForkJoinPool(threadNum)
		val ec: ExecutionContext = ExecutionContext.fromExecutorService(forkJoinPool)

		val split = TimeUtil.getSplitYmd(ymd)

		// 경로 설정
		val ymdPath = s"${split.yyyy}/${split.mm}/${split.dd}"
		val intermediateRoot = s"$ZIRCON_R_GFP_INTERMEDIATE_PATH/$ymdPath"
		val compactionRoot = s"$ZIRCON_R_GFP_COMPACTION_PATH/$ymdPath"
		val warehouseRoot = s"$ZIRCON_R_GFP_WAREHOUSE_PATH/$ymdPath"

		var futures = Vector[Future[Option[Boolean]]]()
		var compactionInfos = Vector[CompactionInfo]()

		val allPubIds = if (argPubId.equals("*")) HdfsUtil.list(hdfs, intermediateRoot, true).filter(p => !p.endsWith("_SUCCESS")).map(p => getPubId(p)).toVector else Vector(argPubId)
		val allPubLength = allPubIds.length
		logger.debug(s"$LOG_PREFIX 컴팩션 대상 개수 = $allPubLength")

		/*
			1. 컴팩션 및 리네임 퓨처 생성 및 실행
		 */
		for ((pubId, idx) <- allPubIds.zipWithIndex) {
			val LOG_PREFIX2 = s"$LOG_PREFIX $ymd pubId=$pubId (${idx + 1}/$allPubLength)"

			/*
				PUB 경로 설정
				intermediatePath = "/user/gfp-data/zircon/r/gfp/intermediate/{yyyy}/{mm}/{dd}/_publisherId=xx"
				compactionPath   = "/user/gfp-data/zircon/r/gfp/compaction/{yyyy}/{mm}/{dd}/_publisherId=xx"
				warehousePath    = "/user/gfp-data/zircon/r/gfp/warehouse/{yyyy}/{mm}/{dd}/_publisherId=xx"
			 */
			val intermediatePath = s"$intermediateRoot/_publisherId=$pubId"
			val compactionPath = s"$compactionRoot/_publisherId=$pubId"
			val warehousePath = s"$warehouseRoot/_publisherId=$pubId"

			if (HdfsUtil.exists(hdfs, s"$intermediatePath/_SUCCESS")) { // _SUCCESS 파일이 있는 경우만 컴팩션
				val isCompacted = isLessThanOneHundredMbOfLargestFileLength(intermediatePath)
				val compactionInfo = CompactionInfo(pubId, isCompacted, intermediatePath, compactionPath, warehousePath)

				if (isCompacted) {
					val numPartition = getNumPartitions(intermediatePath)
					val future = Future {
						try {
							logger.debug(s"$LOG_PREFIX2 컴팩션 ...")
							compact(intermediatePath, numPartition, compactionPath)
							Option(true)
						} catch {
							case t: Throwable =>
								logger.error(s"$LOG_PREFIX2 컴팩션 실패. ${t.getMessage}", t)
								Option(false)
						}
					}(ec)
					futures = futures :+ future
				}

				compactionInfos = compactionInfos :+ compactionInfo
			} else {
				if ("real" == conf.getString("profile")) { // 리얼 환경에서만 에러 등급으로 남김
					logger.error(s"$LOG_PREFIX2 컴팩션 건너띔. $intermediatePath/_SUCCESS 파일 없음")
				} else {
					logger.debug(s"$LOG_PREFIX2 컴팩션 건너띔. $intermediatePath/_SUCCESS 파일 없음")
				}
			}
		}

		/*
			2. 컴팩션이 모두 성공적으로 끝난 경우에만 리네임

			   이렇게 하는 이유:

			   - [컴팩션 -> 리네임 -> intermediate 삭제]를 매체별로 병렬 진행하면
			     일부 매체는 성공하고, 일부 매체는 실패할 경우 성공한 매체의 intermediate는 삭제된다.

			   - 정규 처리 시에는 보통 ZRGFP를 매체별로 요청하지 않고, 통으로 요청한다.
			     그래서 특정 매체 컴팩션 실패에 의해 통으로 ZRGFP의 재시도가 들어가는데,
			     컴팩션에 성공한 매체는 컴팩션할 대상인 intermediate가 없으므로 컴팩션을 진행할 수 없게 된다.

			   - 따라서 개별 매체의 컴팩션을 병렬로 먼저 처리하고,
			     모든 매체의 컴팩션이 성공한 경우에만 [리네임 -> intermediate 삭제]를 순차 처리한다.
		 */
		if (futures.nonEmpty) {
			// 매체별 컴팩션 결과 모으기
			val reduced = Future.reduceLeft(futures) { case (accu, value) =>
				Option(accu.getOrElse(false) && value.getOrElse(false))
			}(ec)

			// 모든 future 가 끝날 때까지 기다리기
			val result = Await.result(reduced, Duration.Inf)

			// 모두 성공이면 리네임
			result match {
				case Some(false) =>
					throw new Exception(s"$LOG_PREFIX $ymd argPubId=$argPubId 컴팩션 실패")
				case _ =>
					processCompactionInfos(ymd, compactionInfos)
			}

			logger.debug(s"$LOG_PREFIX $ymd argPubId=$argPubId futureCnt=${futures.length} 컴팩션 완료.")
		} else {
			logger.debug(s"$LOG_PREFIX $ymd argPubId=$argPubId futureCnt=${futures.length} 컴팩션 대상 없음")
		}
	}

	/**
	 * 동시 실행할 스레드 개수 조회
	 *
	 * @return
	 */
	private def getThreadNum(): Int = {
		val envDoc: Option[Document] = baseDao.getEnvironment("zircon-r-gfp-compaction-thread-num")
		val threadNum = envDoc.get.getInteger("value")
		logger.debug(s"$LOG_PREFIX future threadNum:$threadNum")
		threadNum
	}

	private def getPubId(input: String): String = {
		// 정규식을 사용하여 필요한 부분 추출
		val pattern = """.*/_publisherId=([^/]+)""".r

		input match {
			case pattern(pubId) =>
				// warehouse 경로로 치환. 시간 정보를 date 아래에 붙임. _publisherId 유지.
				pubId
			case _ =>
				// 패턴이 맞지 않는 경우 원래 문자열 반환
				input
		}
	}

	/**
	 * 해당 경로 아래 파일의 최대 크기가 100 MB 미만인지
	 *
	 * @param path
	 * @return
	 */
	private def isLessThanOneHundredMbOfLargestFileLength(path: String): Boolean = {
		var maxLength = 0L
		val listStatus = hdfs.listStatus(new Path(path))
		for (status <- listStatus) {
			maxLength = Math.max(maxLength, status.getLen)
		}
		if (maxLength < ONE_HUNDRED_MB) {
			true
		} else {
			false
		}
	}

	/**
	 * 퍼블리셔 경로 아래에 있는 파케이 파일의 총 사이즈에 따른 파티션 개수 구하기
	 * 100 MB 단위로 잘랐을 때의 파티션 개수
	 *
	 * @param path
	 * @return
	 */
	private def getNumPartitions(path: String): Int = {
		// Calculates disk usage without pay attention to replication factor.
		// Result will be the same with hadopp fs -du /hdfs/path/to/directory
		val totalFileSize = hdfs.getContentSummary(new Path(path)).getLength
		var numPartition = totalFileSize / ONE_HUNDRED_MB
		val rest = totalFileSize % ONE_HUNDRED_MB
		if (rest > 0) {
			numPartition += 1
		}
		if (numPartition < 1) {
			numPartition = 1
		}
		numPartition.toInt
	}

	/**
	 * 컴팩트
	 *
	 * @param inputPath
	 * @param numPartition
	 * @param outputPath
	 */
	private def compact(inputPath: String, numPartition: Int, outputPath: String): Unit = {
		val df = loadLog(Vector(inputPath))
		df
			.repartition(numPartition)
			.write
			.mode("overwrite")
			.parquet(outputPath)
	}

	/**
	 * compactionInfos에 따라서 isCompacted가 true인 경우에는 compactionPath를 warehousePath로 리네임하고 _COMPACTION_SUCCESS 파일 생성
	 * 그렇지 않은 경우에는 intermediatePath를 warehousePath로 리네임하고 _COMPACTION_SUCCESS 파일 생성
	 *
	 * @param compactionInfos
	 */
	private def processCompactionInfos(ymd: String, compactionInfos: Vector[CompactionInfo]): Unit = {
		val compactionInfoLength = compactionInfos.length
		for ((info, idx) <- compactionInfos.zipWithIndex) {
			logger.debug(s"$LOG_PREFIX $ymd pubId=$info.pubId (${idx + 1}/$compactionInfoLength) 리네임 ...")
			if (info.isCompacted) {
				// compaction -> warehouse 리네임
				rename(info.compactionPath, info.warehousePath)

				// intermediate 삭제
				 HdfsUtil.delete(hdfs, info.intermediatePath)
			} else {
				// intermediate -> warehouse 리네임
				rename(info.intermediatePath, info.warehousePath)
			}
			HdfsUtil.create(hdfs, s"${info.warehousePath}/_COMPACTION_SUCCESS")
		}
	}

	/**
	 * warehouse/{yyyy}/{mm}/{dd}/_COMPACTION_SUCCESS 생성
	 * 실질적인 _COMPACTION_SUCCESS는 warehouse/{yyyy}/{mm}/{dd}/_publisherId={pubId}/_COMPACTION_SUCCESS 에 생기지만
	 * 사람이 파일 브라우저로 들어가서 최근 컴팩션이 일어난 시각을 확인하는 용도로써 생성한다.
	 *
	 * @param ymd
	 */
	private def createSuccessFile(ymd: String): Unit = {
		try {
			val splitYmd = TimeUtil.getSplitYmd(ymd)
			val path = s"$ZIRCON_R_GFP_WAREHOUSE_PATH/${splitYmd.yyyy}/${splitYmd.mm}/${splitYmd.dd}/_COMPACTION_SUCCESS"
			if (HdfsUtil.exists(hdfs, path)) HdfsUtil.delete(hdfs, path) // 이미 있으면 삭제 후
			HdfsUtil.create(hdfs, path) // 생성
		} catch {
			/*
				- 수동으로 동시에 여러 매체가 돌 경우 충돌 가능성 있음.
				- 정규에서는 날짜 단위로 돌리므로 충돌 가능성 없음.
				- 지표에 영향을 미치는 일이 아니므로 무시.
			 */
			case t: Throwable =>
				logger.error(s"$LOG_PREFIX createSuccessFile() failed. but you can ignore. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")
		}
	}
}
