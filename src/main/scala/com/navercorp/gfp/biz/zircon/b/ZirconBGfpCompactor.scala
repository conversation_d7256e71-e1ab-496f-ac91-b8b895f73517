package com.navercorp.gfp.biz.zircon.b

import java.util.Date
import java.util.concurrent.{ExecutorService, ForkJoinPool}
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, Future}

import org.apache.hadoop.fs.Path
import org.bson.Document
import org.bson.types.ObjectId
import org.joda.time.DateTime

import com.navercorp.gfp.core.BaseAggregator
import com.navercorp.gfp.core.BaseEnv.{ZIRCON_B_GFP_COMPACTION_PATH, ZIRCON_B_GFP_INTERMEDIATE_PATH, ZIRCON_B_GFP_WAREHOUSE_PATH}
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, TimeUtil}

/**
 * [ fair scheduler 적용 ]
 * 참고
 * https://towardsdatascience.com/apache-spark-sharing-fairly-between-concurrent-jobs-d1caba6e77c2
 * https://coder-question-ko.com/cq-ko-blog/78615
 *
 * spark.sparkContext.setLocalProperty("spark.scheduler.mode", "FAIR")
 * 이 같은 형식은 무슨 이유에서인지 동작하지 않아 spark-submit 시 --conf 옵션으로 해결함.
 */
object ZirconBGfpCompactor extends BaseAggregator {
	val LOG_PREFIX = ".......... [ZBGFP-COMPACTOR]"
	private val ONE_HUNDRED_MB = 100 * 1024 * 1024

	def main(rawArgs: Array[String]): Unit = {
		val startTime = DateTime.now()

		val args = initArgs(rawArgs)
		args.foreach(println)

		try {
			logger.debug(s"$LOG_PREFIX applicationId: ${spark.sparkContext.applicationId} args:${args.mkString(",")}")

			// kind: "regular" or "trace_silver" or "manual"
			val kind = args(0)

			// 대상 일자
			val ymd = args(1)

			// 매체. 특정 매체 또는 "*"
			val pubId = args(2)

			val sTraceId = if (args.length > 3 && Some(args(3)).nonEmpty) args(3) else "-"

			// 이력쌓기 - IN_PROGRESS
			val sparkDocForInProgress = Spark(
				sparkAppId = Option(spark.sparkContext.applicationId),
				sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
				sparkStartedAt = Option(new Date)
			)

			val publisher_id = if (pubId == "*") null else new ObjectId(pubId)
			val silverTrace_id = if (sTraceId == "-") null else new ObjectId(sTraceId)

			val inProgressHist = SummaryHistory(
				datetime = Option(ymd),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(sparkDocForInProgress),
				detail = Option(Map( // 처리유형 기술
					"kind" -> kind,
					"publisher_id" -> publisher_id,
					"silverTrace_id" -> silverTrace_id
				))
			)
			this.upsertSummaryHistory(inProgressHist)

			// 컴팩션
			optimize(ymd, pubId)

			/*
				- warehouse/{yyyy}/{mm}/{dd}/_COMPACTION_SUCCESS 파일 생성
				- write 시에는 {yyyy}/{mm}/{dd}/{hh}/_publisherId={pubId}/_COMPACTION_SUCCESS가 자동으로 생기지만
				- 파일 브라우저를 통해 볼 때 완료 여부를 직관적으로 파악하기 위해 _SUCCESS 파일 생성함
				- 이미 생성된 경우는 삭제하고 다시 생성하여 생성일자 갱신
			 */
			createSuccessFile(ymd)

			// 이력쌓기 - COMPLETE
			val sparkDocForComplete = Spark(
				sparkAppState = Option(SparkAppState.COMPLETE.toString),
				sparkEndedAt = Option(new Date)
			)
			val completeHist = SummaryHistory(
				spark = Option(sparkDocForComplete)
			)
			this.upsertSummaryHistory(completeHist)

			logger.debug(s"$LOG_PREFIX 생성 완료. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get}")
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val sparkDocForFailure = Spark(
					sparkAppState = Option(SparkAppState.FAILURE.toString),
					sparkAppError = Option(t.getMessage),
					sparkEndedAt = Option(new Date)
				)
				val failureHist = SummaryHistory(
					spark = Option(sparkDocForFailure)
				)
				this.upsertSummaryHistory(failureHist)

				logger.error(s"$LOG_PREFIX 생성 실패. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")
				throw t;
		} finally {
			NeloUtil.waitFor()

			// 소요 시간 측정
			val endTime = DateTime.now()
			val elapsedTime = endTime.getMillis - startTime.getMillis
			logger.debug(s"$LOG_PREFIX 소요시간=${elapsedTime / 1000}sec\n\n")
		}
	}

	/**
	 * 동시 실행할 스레드 개수 조회
	 *
	 * @return
	 */
	private def getThreadNum(): Int = {
		val envDoc: Option[Document] = baseDao.getEnvironment("zircon-b-gfp-compaction-thread-num")
		val threadNum = envDoc.get.getInteger("value")
		logger.debug(s"$LOG_PREFIX future threadNum:$threadNum")
		threadNum
	}

	private def optimize(ymd: String, pubId: String): Unit = {
		/*
		[ Executor Context ]

			// java의 Executor를 사용하는 방식
			implicit val ec = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(20))

			// java의 ForkJoinPool을 사용하는 방식. 스레드 개수 지정 없음
			implicit val ec = ExecutionContext.fromExecutorService(new scala.concurrent.forkjoin.ForkJoinPool())

			// java의 ForkJoinPool을 사용하는 방식. 스레드 개수 지정 가능
			val forkJoinPool: ExecutorService = new ForkJoinPool(16)
			implicit val ec: ExecutionContext = ExecutionContext.fromExecutorService(forkJoinPool)

			참고
				- 스칼라 강좌 (37) 동시성을 위한 ExecutorContext : https://hamait.tistory.com/768
				- SCALA BOOK > SCALA FUTURES : https://docs.scala-lang.org/overviews/scala-book/futures.html#inner-main
				- FUTURES > FUTURES AND PROMISES : https://docs.scala-lang.org/overviews/core/futures.html#inner-main
		 */
		// 동시 처리 개수
		val threadNum = getThreadNum()
		val forkJoinPool: ExecutorService = new ForkJoinPool(threadNum)
		implicit val ec: ExecutionContext = ExecutionContext.fromExecutorService(forkJoinPool)

		val split = TimeUtil.getSplitYmd(ymd)

		// 경로 설정
		val ymdPath = s"${split.yyyy}/${split.mm}/${split.dd}"
		val intermediateRoot = s"$ZIRCON_B_GFP_INTERMEDIATE_PATH/$ymdPath"
		val compactionRoot = s"$ZIRCON_B_GFP_COMPACTION_PATH/$ymdPath"
		val warehouseRoot = s"$ZIRCON_B_GFP_WAREHOUSE_PATH/$ymdPath"

		/*
		 	- 매체별로 순차 처리하기때문에 AP 개수가 많고 처리해야 할 파일 개수가 많다면 코어를 충분히 활용한다.
			- 전체 매체를 병렬로 처리하면 소요시간은 빨라지겠으나 중간에 실패하는 경우 어떤 퍼블리셔가 실패했는지 알기 어려워 매체별 순차 처리 로직을 유지한다.
			- 또한 매체를 지정해서 처리하는 경우가 있기 때문에 매체별 순차 처리 로직이 직관성이 좋다.

			- PUB별 순차 실행
			- PUB 내의 AP들을 병렬 컴팩션
			- PUB 내 모든 AP 병렬 컴팩션 완료 후
				- 기존 PUB warehouse 삭제
					- 기존에 특정 AP 경로가 있었으나 재처리 시 없을 수도 있으므로 PUB warehouse 전체를 삭제함
					- 이 이유 때문에 병렬 컴팩션 안에서 리네임 및 _COMPACTION_SUCCESS 파일을 생성하지 않고 renameList를 만들어 따로 하는 것임
					- 이를 병렬 컴팩션 안에서 하면 재처리 시 삭제되어야 할 AP 경로가 포함될 수 있기 때문
				- compaction AP 경로들을 warehouse AP 경로로 리네임 및 _COMPACTION_SUCCESS 파일 생성
				- 기존 PUB compaction 삭제
				- 기존 PUB intermediate 삭제
		 */

		val allPubIds = if (pubId.equals("*")) HdfsUtil.list(hdfs, intermediateRoot, true).filter(p => !p.endsWith("_SUCCESS")).map(p => getPubId(p)).toVector else Vector(pubId)
		for ((pubId, idx) <- allPubIds.zipWithIndex) {
			val intermediatePubPath = s"$intermediateRoot/_publisherId=$pubId"
			if (HdfsUtil.exists(hdfs, s"$intermediatePubPath/_SUCCESS")) { // _SUCCESS 파일이 있는 경우만 컴팩션
				var futuresForComp = Vector[Future[Option[Boolean]]]()
				var renameListForComp = Vector[(String, String)]()
				var renameListForNonComp = Vector[(String, String)]()

				/*
					컴팩션 퓨처 및 리네임 목록 생성
				 */
				// PUB 경로 > 시간 경로
				HdfsUtil.list(hdfs, intermediatePubPath, true).filter(p => !p.endsWith("_SUCCESS")).foreach(intermediateHourPath => {
					// PUB 경로 > 시간 경로 > AP 경로
					HdfsUtil.list(hdfs, intermediateHourPath, true).foreach(intermediateApPath => {
						// 해당 경로 아래 파일의 최대 크기가 100 MB 미만인 경우만 컴팩션
						if (isLessThanOneHundredMbOfLargestFileLength(intermediateApPath)) {
							/*
								파티션 개수 및 경로 설정
								intermediateApPath = "/user/gfp-data/zircon/b/gfp/intermediate/{yyyy}/{mm}/{dd}/_publisherId=xx/_hour={hour}/_adProviderId=yy"
								compactionApPath   = "/user/gfp-data/zircon/b/gfp/compaction/{yyyy}/{mm}/{dd}/_publisherId=xx/_hour={hour}/_adProviderId=yy"
								warehouseApPath    = "/user/gfp-data/zircon/b/gfp/warehouse/{yyyy}/{mm}/{dd}/{hour}/_publisherId=xx/_adProviderId=yy"
							 */
							val numPartition = getNumPartitions(intermediateApPath)
							val compactionApPath = getCompactionPath(intermediateApPath)
							val warehouseApPath = getWarehousePath(compactionApPath)
							logger.debug(
								s"""$LOG_PREFIX 컴팩션 대상
								   |	numPartition: $numPartition
								   |	intermediatePath: $intermediateApPath
								   |	compactionPath:   $compactionApPath
								   |	warehousePath:    $warehouseApPath
								""".stripMargin)

							// AP별 컴팩션 퓨처 만들기
							val future = Future {
								try {
									// 컴팩트
									compact(intermediateApPath, numPartition, compactionApPath)

									// 컴팩션 성공 시, true 리턴
									Option(true)
								} catch {
									case t: Throwable =>
										logger.error(s"$LOG_PREFIX ${t.getMessage}", t)
										Option(false) // 컴팩션 실패 시, false 리턴
								}
							}
							futuresForComp = futuresForComp :+ future

							// 리네임 목록 만들기
							renameListForComp = renameListForComp :+ (compactionApPath, warehouseApPath)
						} else {
							// 컴팩션 비대상 리네임 목록 만들기. 컴팩션은 하지 않지만 리네임은 해야 함.
							val warehouseApPath = getWarehousePath(intermediateApPath)
							logger.debug(
								s"""$LOG_PREFIX 컴팩션 비대상
								   |	intermediatePath: $intermediateApPath
								   |	warehousePath:    $warehouseApPath
								""".stripMargin)
							renameListForNonComp = renameListForNonComp :+ (intermediateApPath, warehouseApPath)
						}
					})
				})

				/*
					컴팩션 퓨처 결과 확인 및 리네임 실행
				 */
				val LOG_PREFIX2 = s"$LOG_PREFIX $ymd pubId=$pubId(${idx + 1}/${allPubIds.length})"
				if (futuresForComp.nonEmpty) {
					logger.info(s"$LOG_PREFIX2 futureCnt=${futuresForComp.length} 컴팩션 시작 ...")

					// 해당 매체 아래 시간별/AP별 컴팩션 결과 모으기
					val reduced = Future.reduceLeft(futuresForComp) { case (accu, value) =>
						Option(accu.getOrElse(false) && value.getOrElse(false))
					}

					// 모든 future 가 끝날 때까지 기다렸다가 결과 받기
					val result = Await.result(reduced, Duration.Inf)

					// 컴팩션이 모두 성공적으로 끝났는지 확인
					result match {
						case Some(false) =>
							throw new Exception(s"$ymd pubId=$pubId 컴팩션 실패")
						case _ =>
							logger.info(s"$LOG_PREFIX2 futureCnt=${futuresForComp.length} 컴팩션 성공")

							/*
								기존 warehouse 삭제 (PUB 단위 삭제)
								재처리를 하면서 기존에 존재했던 AP 경로가 없는 경우도 생길 수 있기 때문에 해당 일자, 해당 매체의 모든 시간대 경로를 삭제함
							 */
							HdfsUtil.list(hdfs, warehouseRoot, true).foreach(hourPath => {
								val pubPath = s"$hourPath/_publisherId=$pubId"
								if (HdfsUtil.exists(hdfs, pubPath)) HdfsUtil.delete(hdfs, pubPath)
							})

							/*
								컴팩션 대상 리네임. compaction -> warehouse
							 */
							logger.info(s"$LOG_PREFIX2 컴팩션 대상 경로 리네임 시작 ...")
							renameListForComp.foreach(item => {
								// compaction -> warehouse 리네임
								val compactionApPath = item._1
								val warehouseApPath = item._2
								rename(compactionApPath, warehouseApPath)

								// warehouse에 _COMPACTION_SUCCESS 파일 생성
								HdfsUtil.create(hdfs, s"$warehouseApPath/_COMPACTION_SUCCESS")
							})
							logger.info(s"$LOG_PREFIX2 컴팩션 대상 경로 리네임 성공")

							/*
								compaction 삭제 (PUB 단위)
							 */
							val compactionPubPath = s"$compactionRoot/_publisherId=$pubId"
							if (HdfsUtil.exists(hdfs, compactionPubPath)) {
								HdfsUtil.delete(hdfs, compactionPubPath)
								logger.info(s"$LOG_PREFIX2 $compactionPubPath 삭제")
							}

							/*
								 intermediate 삭제(PUB 단위)

								 날짜 경로 전체를 삭제하지 않고 pubId별로 삭제하는 이유는
								 동일 날짜에 대해 개별 매체가 여러 개 실행중일 때 날짜 경로 전체를 삭제하면
								 진행중인 데이터를 잃을 수 있기 때문이다.
							 */
							val intermediatePubPath = s"$intermediateRoot/_publisherId=$pubId"
							if (HdfsUtil.exists(hdfs, intermediatePubPath)) {
								HdfsUtil.delete(hdfs, intermediatePubPath)
								logger.info(s"$LOG_PREFIX2 $intermediatePubPath 삭제")
							}
					}

					logger.info(s"$LOG_PREFIX2 컴팩션 완료.")
				} else {
					logger.debug(s"$LOG_PREFIX2 컴팩션 대상 없음")
				}


				/*
					컴팩션 비대상 리네임. intermediate -> warehouse
				 */
				if (renameListForNonComp.nonEmpty) {
					logger.info(s"$LOG_PREFIX2 컴팩션 비대상 경로 리네임 시작 ...")
					renameListForNonComp.foreach(item => {
						// intermediate -> warehouse 리네임
						val intermediateApPath = item._1
						val warehouseApPath = item._2
						rename(intermediateApPath, warehouseApPath)

						// warehouse에 _COMPACTION_SUCCESS 파일 생성
						HdfsUtil.create(hdfs, s"$warehouseApPath/_COMPACTION_SUCCESS")
					})
					logger.info(s"$LOG_PREFIX2 컴팩션 비대상 경로 리네임 성공")
				}
			} else {
				if ("real" == conf.getString("profile")) {
					// 리얼 환경에서만 에러 등급으로 남김
					logger.error(s"$LOG_PREFIX 컴팩션 건너띔. $ymd ${idx + 1}/${allPubIds.length} $intermediatePubPath/_SUCCESS 파일 없음")
				} else {
					logger.debug(s"$LOG_PREFIX 컴팩션 건너띔. $ymd ${idx + 1}/${allPubIds.length} $intermediatePubPath/_SUCCESS 파일 없음")
				}
			}
		}
	}

	/**
	 * 해당 경로 아래 파일의 최대 크기가 100 MB 미만인지
	 *
	 * @param path
	 * @return
	 */
	private def isLessThanOneHundredMbOfLargestFileLength(path: String): Boolean = {
		var maxLength = 0L
		val listStatus = hdfs.listStatus(new Path(path))
		for (status <- listStatus) {
			maxLength = Math.max(maxLength, status.getLen)
		}
		if (maxLength < ONE_HUNDRED_MB) {
			true
		} else {
			false
		}
	}

	/**
	 * 퍼블리셔 경로 아래에 있는 파케이 파일의 총 사이즈에 따른 파티션 개수 구하기
	 * 100 MB 단위로 잘랐을 때의 파티션 개수
	 *
	 * @param path
	 * @return
	 */
	private def getNumPartitions(path: String): Int = {
		// Calculates disk usage without pay attention to replication factor.
		// Result will be the same with hadopp fs -du /hdfs/path/to/directory
		val totalFileSize = hdfs.getContentSummary(new Path(path)).getLength
		var numPartition = totalFileSize / ONE_HUNDRED_MB
		val rest = totalFileSize % ONE_HUNDRED_MB
		if (rest > 0) {
			numPartition += 1
		}
		if (numPartition < 1) {
			numPartition = 1
		}
		numPartition.toInt
	}

	private def getPubId(input: String): String = {
		// 정규식을 사용하여 필요한 부분 추출
		val pattern = """.*/_publisherId=([^/]+)""".r

		input match {
			case pattern(pubId) =>
				// warehouse 경로로 치환. 시간 정보를 date 아래에 붙임. _publisherId 유지.
				pubId
			case _ =>
				// 패턴이 맞지 않는 경우 원래 문자열 반환
				input
		}
	}

	/**
	 * compaction 경로 얻기
	 *
	 * @param input
	 * "/user/gfp-data/zircon/b/gfp/intermediate/{yyyy}/{mm}/{dd}/_publisherId=xx/_hour={hour}/_adProviderId=yy"
	 * @return
	 * "/user/gfp-data/zircon/b/gfp/compaction/{yyyy}/{mm}/{dd}/_publisherId=xx/_hour={hour}/_adProviderId=yy"
	 */
	private def getCompactionPath(input: String): String = {
		// 정규식을 사용하여 필요한 부분 추출
		val pattern = s"""$ZIRCON_B_GFP_INTERMEDIATE_PATH/(.*)""".r

		input match {
			case pattern(rest) =>
				// target 경로로 치환. 시간 정보를 date 아래에 붙임
				s"$ZIRCON_B_GFP_COMPACTION_PATH/$rest"
			case _ =>
				// 패턴이 맞지 않는 경우 원래 문자열 반환
				input
		}
	}

	/**
	 * warehouse 경로 얻기
	 *
	 * @param input
	 * "/user/gfp-data/zircon/b/gfp/compaction/{yyyy}/{mm}/{dd}/_publisherId=xx/_hour={hour}/_adProviderId=yy"
	 * @return
	 * "/user/gfp-data/zircon/b/gfp/compaction/{yyyy}/{mm}/{dd}/{hour}/_publisherId=xx/_adProviderId=yy"
	 */
	private def getWarehousePath(input: String): String = {
		// 정규식을 사용하여 필요한 부분 추출
		val pattern = """.*/(\d{4}/\d{2}/\d{2})/_publisherId=([^/]+)/_hour=(\d{2})/_adProviderId=([^/]+)""".r

		input match {
			case pattern(ymd, pubId, hour, apId) =>
				// target 경로로 치환. 시간 정보를 date 아래에 붙임
				s"$ZIRCON_B_GFP_WAREHOUSE_PATH/$ymd/$hour/_publisherId=$pubId/_adProviderId=$apId"
			case _ =>
				// 패턴이 맞지 않는 경우 원래 문자열 반환
				input
		}
	}

	/**
	 * 컴팩트(리파티션)
	 *
	 * @param inputPath
	 * @param numPartition
	 * @param outputPath
	 */
	private def compact(inputPath: String, numPartition: Int, outputPath: String): Unit = {
		val df = loadLog(Vector(inputPath))
		df
			.repartition(numPartition)
			.write
			.mode("overwrite")
			.parquet(outputPath)
	}

	/**
	 * warehouse/{yyyy}/{mm}/{dd}/_COMPACTION_SUCCESS 생성
	 * 실질적인 _COMPACTION_SUCCESS는 warehouse/{yyyy}/{mm}/{dd}/{hh}/_publisherId={pubId}/_adProviderId={apId}/_COMPACTION_SUCCESS 에 생기지만
	 * 사람이 파일 브라우저로 들어가서 최근 컴팩션이 일어난 시각을 확인하는 용도로써 생성한다.
	 *
	 * @param ymd
	 */
	private def createSuccessFile(ymd: String): Unit = {
		try {
			val splitYmd = TimeUtil.getSplitYmd(ymd)
			val path = s"$ZIRCON_B_GFP_WAREHOUSE_PATH/${splitYmd.yyyy}/${splitYmd.mm}/${splitYmd.dd}/_COMPACTION_SUCCESS"
			if (HdfsUtil.exists(hdfs, path)) HdfsUtil.delete(hdfs, path) // 이미 있으면 삭제 후
			HdfsUtil.create(hdfs, path) // 생성
		} catch {
			/*
				- 수동으로 동시에 여러 매체가 돌 경우 충돌 가능성 있음.
				- 정규에서는 날짜 단위로 돌리므로 충돌 가능성 없음.
				- 지표에 영향을 미치는 일이 아니므로 무시.
			 */
			case t: Throwable =>
				logger.error(s"$LOG_PREFIX createSuccessFile() failed. but you can ignore. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")
		}
	}
}
