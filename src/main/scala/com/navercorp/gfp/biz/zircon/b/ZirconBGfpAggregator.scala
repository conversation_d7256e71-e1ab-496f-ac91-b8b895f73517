package com.navercorp.gfp.biz.zircon.b

import java.util.Date
import java.util.concurrent.{ExecutorService, ForkJoinPool}
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, Future}

import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import org.apache.spark.sql._
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.bson.Document
import org.bson.types.ObjectId
import org.joda.time.DateTime

import com.navercorp.gfp.biz.silver.SilverLogType
import com.navercorp.gfp.biz.silver.SilverLogType.SilverLogType
import com.navercorp.gfp.biz.zircon.ZirconProcessKind
import com.navercorp.gfp.biz.zircon.b.ZirconBConstant.{AP_REQ_RES_DIMS, COMMON_DIMS, GFP_DIMS}
import com.navercorp.gfp.biz.zircon.b.ZirconBGfpAggregator.{AggParam, WriteParam}
import com.navercorp.gfp.biz.zircon.b.gfd.GfdTimezoneHelper
import com.navercorp.gfp.core.BaseConstant.DEVICE_OS
import com.navercorp.gfp.core.BaseEnv._
import com.navercorp.gfp.core.BaseUdf.getEstimatedRevenue
import com.navercorp.gfp.core._
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.meta.adprovider.AdProviderDao
import com.navercorp.gfp.meta.adproviderinfo.AdProviderInfoDao
import com.navercorp.gfp.util.{AggregatorUtil, HdfsUtil, NeloUtil, TimeUtil}

object ZirconBGfpAggregator extends BaseAggregator with GfdTimezoneHelper {
	var LOG_PREFIX = ".......... [ZBGFP]"

	case class AggParam(ymd: String, pubId: String, sDf: Option[Dataset[Row]], cDf: Option[Dataset[Row]])

	case class WriteParam(outputPath: String)

	def main(rawArgs: Array[String]): Unit = {
		val startTime = DateTime.now()

		val args = initArgs(rawArgs)
		args.foreach(println)

		// kind: "regular" or "trace_silver" or "manual"
		val kind = args(0)

		// 대상 일자
		val ymd = args(1)

		// 매체. 특정 매체 또는 "*"
		val pubId = args(2)

		/*
			- kind="trace_silver"일 때만 의미 있음
			- SilverTrace._id
			- SummaryHistory.detail.silverTraceId 기록 용. 집계 로직에서 쓰지는 않음
		 */
		val sTraceId = if (args.length > 3 && Some(args(3)).nonEmpty) args(3) else "-"

		// 파라미터 유효성 검사
		validateParam(kind, pubId, sTraceId)

		LOG_PREFIX = s"$LOG_PREFIX ymd=$ymd kind=$kind pubId=$pubId sTraceId=$sTraceId"
		logger.debug(s"$LOG_PREFIX 생성 시작")

		try {
			// 이력쌓기 - IN_PROGRESS
			val sparkDocForInProgress = Spark(
				sparkAppId = Option(spark.sparkContext.applicationId),
				sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
				sparkStartedAt = Option(new Date)
			)

			val publisher_id = if (pubId == "*") null else new ObjectId(pubId)
			val silverTrace_id = if (sTraceId == "-") null else new ObjectId(sTraceId)

			val inProgressHist = SummaryHistory(
				datetime = Option(ymd),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(sparkDocForInProgress),
				detail = Option(Map( // 처리유형 기술
					"kind" -> kind,
					"publisher_idˆ" -> publisher_id,
					"silverTrace_id" -> silverTrace_id
				))
			)
			this.upsertSummaryHistory(inProgressHist)

			// Zircon GFP 생성
			makeZirconBGfp(ymd, kind, pubId, sTraceId)

			/*
				_SUCCESS 파일 생성
					- write 시에는 {yyyy}/{mm}/{dd}/_publisherId={pubId}/_SUCCESS가 자동으로 생기지만
					- 파일 브라우저를 통해 볼 때 완료 여부를 직관적으로 파악하기 위해 _SUCCESS 파일 생성함
					- 이미 생성된 경우는 삭제하고 다시 생성하여 생성일자 갱신
			 */
			createSuccessFile(ymd)

			// 이력쌓기 - COMPLETE
			val sparkDocForComplete = Spark(
				sparkAppState = Option(SparkAppState.COMPLETE.toString),
				sparkEndedAt = Option(new Date)
			)
			val completeHist = SummaryHistory(
				spark = Option(sparkDocForComplete)
			)
			this.upsertSummaryHistory(completeHist)

			logger.debug(s"$LOG_PREFIX 생성 완료. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get}")
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val sparkDocForFailure = Spark(
					sparkAppState = Option(SparkAppState.FAILURE.toString),
					sparkAppError = Option(t.getMessage),
					sparkEndedAt = Option(new Date)
				)
				val failureHist = SummaryHistory(
					spark = Option(sparkDocForFailure)
				)
				this.upsertSummaryHistory(failureHist)

				logger.error(s"$LOG_PREFIX 생성 실패. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")
				throw t;
		} finally {
			NeloUtil.waitFor()

			// 소요 시간 측정
			val endTime = DateTime.now()
			val elapsedTime = endTime.getMillis - startTime.getMillis
			logger.debug(s"$LOG_PREFIX 소요시간=${elapsedTime / 1000}sec\n\n")
		}
	}

	/**
	 * 파라미터 유효성 검사
	 *
	 * @param kind
	 * @param pubId
	 * @param sTraceId
	 */
	private def validateParam(kind: String, pubId: String, sTraceId: String): Unit = {
		if (!pubId.equals("*") && !ObjectId.isValid(pubId)) {
			throw new Exception("\"pubId\" is not hexadecimal")
		}
		if (kind != ZirconProcessKind.REGULAR && kind != ZirconProcessKind.TRACE_SILVER && kind != ZirconProcessKind.MANUAL) {
			throw new Exception(s"'kind' should be '${ZirconProcessKind.REGULAR}' or '${ZirconProcessKind.TRACE_SILVER}' or '${ZirconProcessKind.MANUAL}'")
		}
		if (kind == ZirconProcessKind.TRACE_SILVER && (sTraceId.isEmpty || sTraceId.equals("-"))) {
			throw new Exception("\"sTraceId\" is required")
		}
	}

	private def makeZirconBGfp(ymd: String, kind: String, pubId: String, sTraceId: String): Unit = {
		val futures = makeFuture(ymd, kind, pubId, sTraceId)
		if (futures.nonEmpty) {
			runFuture(ymd, futures)
		}
	}

	private def makeFuture(ymd: String, kind: String, pubId: String, sTraceId: String): Vector[Future[Option[Boolean]]] = {
		val threadNum = getThreadNum() // 동시 처리 개수

		val forkJoinPool: ExecutorService = new ForkJoinPool(threadNum)
		implicit val ec: ExecutionContext = ExecutionContext.fromExecutorService(forkJoinPool)
		var futures = Vector[Future[Option[Boolean]]]()

		val split = TimeUtil.getSplitYmd(ymd)

		val allPubIds = if (pubId.equals("*")) {
			// pubId = "*"인 경우 시간이 오래 걸려 에러를 유발하는 5b8f669428b373001fe56ea8(네이버서비스) 매체는 맨 마지막에 하도록 함
			val longRunPubId = "5b8f669428b373001fe56ea8" // 네이버서비스
			publisherDao.getPublisherIds().toVector.filterNot(_ == longRunPubId) :+ longRunPubId
		} else Vector(pubId)

		for (pubId <- allPubIds) {
			val apTimezones1 = adProviderDao.getUniqueApTimezones(pubId) // 이 매체와 연관된 AP의 타임존 목록 가져오기(유니크 타임존)

			// [DATA] GFD 연동 리포트 타임존 변경 영향 검토 (https://jira.navercorp.com/browse/GFP-1008)
			// 이 매체에 GFD가 엮여 있고 타임존 변경 날짜 이전이라면 Etc/GMT 타임존 추가
			val apTimezones = adjustTimezone(pubId, ymd, apTimezones1)

			val ymdhList = getYmdhList(ymd, apTimezones, "yyyyMMdd-HH") // 타임존에 해당하는 시간대 경로 얻기

			val sPaths = getSilverPaths(pubId, SilverLogType.ErSspServer, ymdhList) // AP 타임존에 따른 실버 서버 경로 구하기
			val cPaths = getSilverPaths(pubId, SilverLogType.ErSspClient, ymdhList) // AP 타임존에 따른 실버 클라 경로 구하기

			// 경로가 정상적으로 존재하는지
			if (sPaths.nonEmpty && cPaths.nonEmpty) {
				val future = Future {
					try {
						val aggregator = new ZirconBGfpAggregator(ymd, kind, sTraceId)
						aggregator.init()

						// 실버 서버 로드
						logger.debug(s"$LOG_PREFIX pubId=$pubId - 실버 서버 로드 ..")
						val sDf = Option(loadSilverLog(sPaths))

						// 실버 클라 로드
						logger.debug(s"$LOG_PREFIX pubId=$pubId - 실버 클라 로드 ..")
						val cDf = Option(loadSilverLog(cPaths))

						// 집계
						val aggDf = aggregator.aggregate(Option(AggParam(ymd, pubId, sDf, cDf)))

						/*
							- intermediate/{yyyy}/{mm}/{dd}/_publisherId={pubId}에 _hour, _adProviderId 단위로 파티션하여 저장
							- intermediate/{yyyy}/{mm}/{dd}/_publisherId={pubId}/_SUCCESS 파일 자동 생성
						 */
						val intermediatePath = s"$ZIRCON_B_GFP_INTERMEDIATE_PATH/${split.yyyy}/${split.mm}/${split.dd}/_publisherId=$pubId"
						aggregator.write(aggDf, Option(WriteParam(intermediatePath)))

						// 성공 시, true 리턴
						Option(true)
					} catch {
						case t: Throwable =>
							logger.error(s"$LOG_PREFIX pubId=$pubId 생성 실패. sparkAppId=${spark.sparkContext.applicationId} ymd=$ymd pubId=$pubId ${t.getMessage}", t)
							Option(false) // 실패 시, false 리턴
					}
				}(ec)
				futures = futures :+ future
			} else {
				logger.warn(s"$LOG_PREFIX pubId=$pubId 로드 대상 중 일부 또는 전부가 존재하지 않아 집계 스킵. sparkAppId=${spark.sparkContext.applicationId}" +
					s"\n\tsPaths=$sPaths\n\tcPaths=$cPaths")
			}
		}

		futures
	}

	/**
	 * 동시 실행할 스레드 개수 조회
	 *
	 * @return
	 */
	private def getThreadNum(): Int = {
		val envDoc: Option[Document] = baseDao.getEnvironment("zircon-b-gfp-accumulation-thread-num")
		val threadNum = envDoc.get.getInteger("value")
		logger.debug(s"$LOG_PREFIX future threadNum:$threadNum")
		threadNum
	}

	private def runFuture(ymd: String, futures: Vector[Future[Option[Boolean]]]): Unit = {
		// 퍼블리셔별 결과 모으기
		val reduced = Future.reduceLeft(futures) { case (accu, value) =>
			Option(accu.getOrElse(false) && value.getOrElse(false))
		}

		// 모든 future 가 끝날 때까지 기다렸다가 결과 받기
		val result = Await.result(reduced, Duration.Inf)

		// 모두 성공적으로 끝났는지 확인
		result match {
			case Some(false) =>
				throw new Exception(s"$ymd 생성 실패")
			case _ =>
				logger.debug(s"$LOG_PREFIX 생성 완료")
		}
	}

	/**
	 * 로드할 실버 경로 생성
	 *
	 * @param pubId
	 * @param logType
	 * @param ymdhList
	 * @return
	 */
	private def getSilverPaths(pubId: String, logType: SilverLogType, ymdhList: Vector[String]): Vector[String] = {
		var paths = Vector[String]()

		for (ymdh <- ymdhList) { // ymdh = yyyyMMdd-HH
			val ymdhPath = s"$SILVER_ROOT/$logType/$ymdh"
			val pubPath = s"$ymdhPath/publisherId=$pubId"

			// 컴팩션이 완료되었는지
			if (HdfsUtil.exists(hdfs, s"$ymdhPath/_COMPACTION_SUCCESS") &&
				HdfsUtil.exists(hdfs, s"$pubPath")) {
				paths = paths :+ pubPath
			}
		}

		logger.debug(s"$LOG_PREFIX pubId=$pubId 로드할 실버 경로 $logType")
		paths.foreach(path => logger.debug(s"\t\t$path"))

		paths
	}

	/**
	 * intermeidate/{yyyy}/{mm}/{dd}/_SUCCESS 생성
	 * 실질적인 _SUCCESS는 intermeidate/{yyyy}/{mm}/{dd}/{hh}/_publisherId={pubId}/_SUCCESS 에 생기지만
	 * 사람이 파일 브라우저로 들어가서 최근 적재가 일어난 시각을 확인하는 용도로써 생성한다.
	 *
	 * @param ymd
	 */
	private def createSuccessFile(ymd: String): Unit = {
		try {
			val splitYmd = TimeUtil.getSplitYmd(ymd)
			val path = s"$ZIRCON_B_GFP_INTERMEDIATE_PATH/${splitYmd.yyyy}/${splitYmd.mm}/${splitYmd.dd}/_SUCCESS"
			if (HdfsUtil.exists(hdfs, path)) HdfsUtil.delete(hdfs, path) // 이미 존재하면 삭제 후
			HdfsUtil.create(hdfs, path) // 생성
		} catch {
			/*
				- 수동으로 동시에 여러 매체가 돌 경우 충돌 가능성 있음.
				- 정규에서는 날짜 단위로 돌리므로 충돌 가능성 없음.
				- 지표에 영향을 미치는 일이 아니므로 무시.
			 */
			case t: Throwable =>
				logger.error(s"$LOG_PREFIX createSuccessFile() failed. but you can ignore. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")
		}
	}
}

class ZirconBGfpAggregator(ymd: String, kind: String, sTraceId: String)(implicit spark: SparkSession) extends BizAggregator with AdProviderAggregator with DeleteFutureHelper with GfdTimezoneHelper {

	import spark.implicits._

	type A = AggParam
	type W = WriteParam

	private val LOG_PREFIX = s".......... [ZBGFP] $ymd kind=$kind sTraceId=$sTraceId"

	private val adProviderDao = new AdProviderDao()
	private val adProviderInfoDao = new AdProviderInfoDao()

	private val commonWin = Window.partitionBy(COMMON_DIMS.head, COMMON_DIMS.tail: _*)

	/*
		Zircon B GFP 구할 때 사용하는 디멘젼은 모두 GFP_DIMS2
			- https://oss.navercorp.com/da-ssp/bts/issues/2508#issuecomment-13426870
			- 이래야 addApReqRes()와의 join에서 광고공급자응답수=0일 때의 row가 포함됨
	 */
	private val GFP_DIMS2 = GFP_DIMS ++ Seq("currency", "estimatedReportType", "distTypeForImp", "distTypeForRev")

	// 광고유닛요청수 디멘젼
	private val AUR_DIMS = Vector("seoulTimestamp", "publisherId", "country", "deviceOs", "adUnitId")

	// 광고공급자별 딜리버리 지표
	private val DLV_METRICS = Vector(
		"gfpFilledRequests"
		, "gfpImpressions"
		, "gfpViewableImpressions"
		, "gfpClicks"
		, "gfpCompletions"
		, "gfpEstimatedKrwSalesForFilledRequests"
		, "gfpEstimatedKrwSalesForImpressions"
		, "gfpEstimatedKrwSalesForViewableImpressions"
		, "gfpEstimatedUsdSalesForFilledRequests"
		, "gfpEstimatedUsdSalesForImpressions"
		, "gfpEstimatedUsdSalesForViewableImpressions"
		, "gfpEstimatedImpressions"
		, "gfpEstimatedKrwSales"
		, "gfpEstimatedUsdSales"
	)

	// 광고공급자 호출수/응답수
	private val AP_REQ_RES_METRICS = Vector("adProviderRequestsOrg", "adProviderResponsesOrg"
		, "adProviderRequests", "adProviderResponses")

	//	// 광고유닛요청수
	//	private val AUR_METRIC = "adUnitRequests"
	//
	//	// 비중 지표
	//	private val WEIGHT_METRICS = Vector(
	//		  "gfpEstimatedImpressionsSum"
	//		, "gfpClicksSum"
	//		, "singleCreativeType"
	//		, "cntInPartition"
	//		, "gfpKrwFractionalPartLen"
	//		, "gfpKrwVolume"
	//		, "gfpEstimatedKrwSalesInt"
	//		, "gfpEstimatedKrwSalesSumInt"
	//		, "gfpUsdFractionalPartLen"
	//		, "gfpUsdVolume"
	//		, "gfpEstimatedUsdSalesInt"
	//		, "gfpEstimatedUsdSalesSumInt"
	//		, "gfpEstimatedImpressionsWeight"
	//		, "gfpClicksWeight"
	//		, "gfpEstimatedSalesWeight"
	//	)
	//
	//	// 비중 계산에 필요한 중간 집계 필드를 포함한 전체 메트릭
	//	private val ALL_METRICS = (AUR_METRIC +: DLV_METRICS) ++ AP_REQ_RES_METRICS ++ WEIGHT_METRICS
	//
	//	// 중간 집계 필드 빼고, 비중분배를 위해 필요한 필수 메트릭(Zircon B에서 사용됨)
	//	val FINAL_REQUIRED_METRICS = GFP_DIMS ++ (AUR_METRIC +: DLV_METRICS) ++ AP_REQ_RES_METRICS ++ Vector(
	//		  "singleCreativeType"
	//		, "cntInPartition"
	//		, "gfpEstimatedImpressionsWeight"
	//		, "gfpClicksWeight"
	//		, "gfpEstimatedSalesWeight"
	//	)

	override def init(): Unit = {
		super.init()
	}

	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		val pubId = aggParam.get.pubId

		// [DATA] GFD 연동 리포트 타임존 변경 영향 검토 (https://jira.navercorp.com/browse/GFP-1008)
		val isGfdTzChanged = isGfdTimezoneChangedDate(ymd)
		// 아래 2개 변수는 isGfdTzChanged == true일 때만 사용됨
		val gfdIds = adProviderDao.getGfdAdProviderIds()
		val (dupTsStart, dupTsEnd) = getDupGfdTimestamp(ymd)

		/*
			- 광고공급자 메타 정보 로드
			- cache() 적용하지 않음
				- 동일한 dataframedp 여러 번의 action이 일어날 때 재사용의 의미가 있음.
				- 본 로직은 action이 write할 때 한 번만 동작하므로 적용하지 않음.
				- 주의: cache()를 쓸 경우 action이 호출되어야 cache()가 동작하므로 Companion Object에서 write()를 한 후 unpersist()를 해야 함.
		 */
		val apDf = broadcast(loadAdProviders(pubId))

		// 광고공급자 딜리버리 통계 구하기
		val cDf1 = prepareClientDf(aggParam.get.cDf.get, apDf) // 실버 준비(AP 메타 정보 추가됨)
		val cDf2 = if (isGfdTzChanged) exceptDupGfdTimestamp(cDf1, gfdIds, dupTsStart, dupTsEnd) else cDf1 // [DATA] GFD 연동 리포트 타임존 변경 영향 검토 (https://jira.navercorp.com/browse/GFP-1008)
		val cDf = filterYmd(cDf2) // AP타임존에 해당하는 일자로 필터
		val dlvDf = getGfpStats(cDf)

		// 광고공급자 호출수/응답수 구하기
		val explodedSDf1 = prepareExplodedServerDf(aggParam.get.sDf.get, apDf) // 실버 준비(AP 메타 정보 추가됨)
		val explodedSDf2 = if (isGfdTzChanged) exceptDupGfdTimestamp(explodedSDf1, gfdIds, dupTsStart, dupTsEnd) else explodedSDf1 // [DATA] GFD 연동 리포트 타임존 변경 영향 검토 (https://jira.navercorp.com/browse/GFP-1008)
		val explodedSDf = filterYmd(explodedSDf2) // AP타임존에 해당하는 일자로 필터

		// storage memory에 저장된 broadcast 메모리를 해제하여 다른 작업에 사용할 메모리 확보
		// 매체별로 여러 번 수행되기 때문에 쌓이지 않게 관리함
		apDf.unpersist()

		val s2sStats = getAdProviderStats(explodedSDf)
		val c2sStats = getAdProviderStats(cDf)
		val apReqResDf = getUnionedAdProviderStats(s2sStats, c2sStats) // 데이터 볼륨을 줄인 stats 들로 union

		// 광고공급자 호출수/응답수 추가
		val gfpDlvDf2 = addApReqRes(dlvDf, apReqResDf)

		// 비중 구하기
		val weightDf = getWeight(gfpDlvDf2)

		// 광고유닛 요청수 구하기
		val notExplodedSDf = prepareNotExplodedServerDf(aggParam.get.sDf.get)
		val aurDf = getAdUnitRequestsStats(notExplodedSDf)

		// 광고유닛 요청수 추가
		val aurAddedDf = addAdUnitRequests(weightDf, aurDf)

		// 광고유닛요청수, 광고공급자호출수, 그 외 주요 딜리버리 지표가 모두 0인 경우 해당 row 제외
		val finalDf = excludeRowsHaveAllZeroMetrics(aurAddedDf)

		println(s"$LOG_PREFIX\n최종 스키마 ...")
		finalDf.printSchema()

		finalDf
	}

	/**
	 * 광고공급자 메타 정보 로드
	 *
	 * @return
	 */
	private def loadAdProviders(pubId: String): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 광고공급자 메타 정보 로드")

		// 이 매체에 관련된 AP만
		val apIds = adProviderInfoDao.getAdProviderIdsByPublisherId(pubId)

		// SyncAdProviders 스키마 정의
		val oidStructType = StructType(List(StructField("oid", StringType, nullable = true)))
		val schema = StructType(List(
			StructField("_id", oidStructType, nullable = true),
			StructField("timezone", StringType, nullable = true),
			StructField("currency", StringType, nullable = true),
			StructField("report", StructType(
				List(
					StructField("estimatedReportType", MapType(StringType, StringType), nullable = true),
					StructField("distributionType", MapType(StringType, StringType), nullable = true),
				)
			))
		))

		// AdProvider 메타 정보 로드
		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdProviders")
		val df = spark.read
			.schema(schema)
			.mongo(readConfig)
			.select(
				$"_id.oid".as("adProviderId"),
				$"timezone",
				$"currency",
				$"report.estimatedReportType",
				$"report.distributionType".getItem("impression").as("distTypeForImp"),
				$"report.distributionType".getItem("revenue").as("distTypeForRev"),
			)
			.filter($"adProviderId".isin(apIds: _*))
			/*
				AP에 비중분배기준이 정의되어 있지 않을 경우 디폴트 설정
					- OSS: https://oss.navercorp.com/da-ssp/bts/issues/1902#issuecomment-12980448
					- WIKI: https://wiki.navercorp.com/pages/viewpage.action?pageId=**********#id-%EB%A7%A4%EC%B2%B4%EC%84%B1%EA%B3%BC%EB%A6%AC%ED%8F%AC%ED%8A%B8API-%EB%B9%84%EC%A4%91%EB%B6%84%EB%B0%B0%EA%B8%B0%EC%A4%80%ED%95%AD%EB%AA%A9%EC%B6%94%EA%B0%80
					- DB Schema AdProviders: https://wiki.navercorp.com/pages/viewpage.action?pageId=870371825
			 */
			.na.fill("IMP", List("distTypeForImp"))
			.na.fill("REV", List("distTypeForRev"))

		// [DATA] GFD 연동 리포트 타임존 변경 영향 검토 (https://jira.navercorp.com/browse/GFP-1008)
		// 처리하려는 날짜에 따라 reportApi.type == 'GFD' AP들의 타임존 설정
		val df2 = decideGfdTimezone(ymd, df)

		// 추정수익 기준 적용
		val estRptTypeAddedDf = applyDefaultEstRptType(df2)

		// adProviderId = '' 또는 null 인 경우를 위한 House AP 추가
		val houseApDf = Seq(("-", "Asia/Seoul", "KRW", "IMP", "REV", "-", "-"))
			.toDF(
				"adProviderId",
				"timezone",
				"currency",
				"distTypeForImp",
				"distTypeForRev",
				"creativeType",
				"estimatedReportType"
			)

		// 추정수익기준 & House AP 적용된 최종 AP 데이터셋
		estRptTypeAddedDf.union(houseApDf)
	}

	/**
	 * 광고유닛요청수 구하기 위한 데이터프레임 준비
	 *
	 * @param sDf
	 * @return
	 */
	private def prepareNotExplodedServerDf(sDf: Dataset[Row]): Dataset[Row] = {
		val sDf2 = filterTest(sDf)
			.filter($"serviceId".isNotNull) // serviceId가 null로 남는 현상 (https://jira.navercorp.com/browse/GFP-215)
			.selectExpr(
				"requestTime",
				"requestId",
				"publisherId",
				"NVL(country, '-') AS country",
				DEVICE_OS,
				"adUnitId",
			)
			// 서울 기준 일시 (TimestampType). 초 단위(integer)의 unix time을 local time zone, 즉 Asia/Seoul의 timestamp로 바꿈
			.withColumn("seoulTimestamp", from_unixtime($"requestTime" / 1000, "yyyy-MM-dd HH").cast("timestamp"))
		sDf2
	}

	/**
	 * 광고공급자 호출수/응답수 구하기 위한 데이터프레임 준비
	 *
	 * @param sDf
	 * @return
	 */
	private def prepareExplodedServerDf(sDf: Dataset[Row], apDf: Dataset[Row]): Dataset[Row] = {
		val sDf2 = filterTest(sDf)
			.filter($"serviceId".isNotNull) // serviceId가 null로 남는 현상 (https://jira.navercorp.com/browse/GFP-215)
			.withColumn("res", explode_outer($"responses"))
			.drop("responses")
			.selectExpr(
				"requestTime",
				"requestId",
				"publisherId",
				"serviceId",
				"adUnitId",
				"NVL(country, '-') AS country",
				DEVICE_OS,
				"CASE WHEN res.adProviderId is null OR res.adProviderId = '' THEN '-' ELSE res.adProviderId END AS adProviderId",
				"res.adProviderPlaceId",
				"res.placeKey",
				"NVL(res.requestCreativeTypes[0], '-') AS requestCreativeType",
				"NVL(res.responseCreativeType, '-') AS responseCreativeType",
				"NVL(res.biddingGroupId, '-') AS biddingGroupId",
				"NVL(res.dealId, '-') AS dealId",
				"res.connectionType",
				"NULL AS eventId",
				"res.stat"
			)
		val sDf3 = addMetaOfAdProvider(sDf2, apDf) // timezone을 위한 메타 추가

		// 날짜 필드 추가
		val sDf4 = sDf3
			// 서울 기준 일시 (TimestampType). 초 단위(integer)의 unix time을 local time zone, 즉 Asia/Seoul의 timestamp로 바꿈
			.withColumn("seoulTimestamp", from_unixtime($"requestTime" / 1000, "yyyy-MM-dd HH").cast("timestamp"))
			// Asia/Seoul의 timestamp를 UTC 일시로 바꿈
			.withColumn("utcTimestamp", to_utc_timestamp($"seoulTimestamp", "Asia/Seoul"))
			// AP 타임존 기준 일시 (TimestampType). UTC 일시를 AP 타임존 시간대로 바꿈
			.withColumn("apTimestamp", from_utc_timestamp($"utcTimestamp", $"timezone"))
			// AP 타임존 기준 일시 (StringType). 포맷 = yyyyMMddHH 에서 HH만 추출. 저장 시 파티션 정보로 사용.
			.withColumn("apHour", substring(date_format($"apTimestamp", "yyyyMMddHH"), -2, 2))
			// 집계일자 필터링을 위해 추가
			.withColumn("apDate", date_format($"apTimestamp", "yyyyMMdd"))

		sDf4
	}

	/**
	 * 노출, 클릭, 수익(sales), 순수익(profit) 구하기 위한 데이터프레임 준비
	 *
	 * @param cDf
	 * @return
	 */
	private def prepareClientDf(cDf: Dataset[Row], apDf: Dataset[Row]): Dataset[Row] = {
		val cDf2 = filterTest(cDf)
			.filter($"serviceId".isNotNull) // serviceId가 null로 남는 현상 (https://jira.navercorp.com/browse/GFP-215)
			.selectExpr(
				"eventTime",
				"publisherId",
				"serviceId",
				"adUnitId",
				"NVL(country, '-') AS country",
				DEVICE_OS,
				"CASE WHEN adProviderId IS NULL OR adProviderId = '' THEN '-' ELSE adProviderId END AS adProviderId",
				"adProviderPlaceId",
				"placeKey",
				"NVL(requestCreativeTypes[0], '-') AS requestCreativeType",
				"NVL(responseCreativeType, '-') AS responseCreativeType",
				"NVL(biddingGroupId, '-') AS biddingGroupId",
				"NVL(dealId, '-') AS dealId",
				"bidPriceInKRW",
				"bidPriceInUSD",
				"connectionType",
				"eventId",
				AggregatorUtil.selectExprOfIsValid(),
				"stat",
			)

			/*
				추정수익 필드 추가(소수점 7째 자리에서 반올림)
					- bidPriceXx는 scala.math.BigDecimal(precision=38, scale=6)로 처리하여 최대한 정확도를 높임
					- scala.math.BigDecimal(precision, scale)
						- precision : 전체 자리수(정수부 자리수 + 소수부 자리수)
						- scale: 소수점 몇 째자리 까지 표현할 수 있는지(소수점 이하 자리수)
					- 참고
						- exponent: 소수를 표현하는 방법(지수 표기법)
							- 1.23E5  = 123000
							- 1.23E-5 = 0.0000123
			*/
			.withColumn("gfpEstimatedKrwSales", getEstimatedRevenue($"bidPriceInKRW").cast(BaseConstant.DECIMAL_TYPE_15_6))
			.withColumn("gfpEstimatedUsdSales", getEstimatedRevenue($"bidPriceInUSD").cast(BaseConstant.DECIMAL_TYPE_15_6))
			.drop("bidPriceInKRW", "bidPriceInUSD")


		// AP의 메타 필드 추가
		val cDf3 = addMetaOfAdProvider(cDf2, apDf)

		// 날짜 필드 추가
		val cDf4 = cDf3
			// 서울 기준 일시 (TimestampType). 초 단위(integerz)의 unix time을 local time zone, 즉 Asia/Seoul의 timestamp로 바꿈
			.withColumn("seoulTimestamp", from_unixtime($"eventTime", "yyyy-MM-dd HH").cast("timestamp"))
			// Asia/Seoul의 timestamp를 UTC 일시로 바꿈
			.withColumn("utcTimestamp", to_utc_timestamp($"seoulTimestamp", "Asia/Seoul"))
			// AP 타임존 기준 일시 (TimestampType). UTC 일시를 AP 타임존 시간대로 바꿈
			.withColumn("apTimestamp", from_utc_timestamp($"utcTimestamp", $"timezone"))
			// AP 타임존 기준 일시 (StringType). 포맷 = yyyyMMddHH 에서 HH만 추출. 저장 시 파티션 정보로 사용.
			.withColumn("apHour", substring(date_format($"apTimestamp", "yyyyMMddHH"), -2, 2))
			// 집계일자 필터링을 위해 추가
			.withColumn("apDate", date_format($"apTimestamp", "yyyyMMdd"))

		cDf4
	}

	/**
	 * join 성능 튜닝 포인트
	 *
	 * 1. salting
	 * 		- join 시 데이터 치우침 현상(skewed data)으로 인해 특정 파티션의 사이즈가 커짐
	 * 		- 이를 골고루 퍼뜨리기 위해 salting 기법 사용
	 * 		- 지속적인 테스트를 통해 적절한 saltRange를 찾아야 한다.
	 * 		- 여기서는 saltRange = 4000
	 *
	 * 2. apDf를 broadcast
	 * 		- join 시 기본 join인 SortMergeJoin이 아닌 BroadcastHashJoin이 일어나도록 하여 worker node 간 shuffle이 일어나지 않게 한다.
	 * 		- 한 쪽 데이터프레임이 훨씬 작은 경우만 가능한 정책이다.
	 *
	 * 3. left, right를 특정 개수로 강제 repartition(num) 하는 것은 도움이 안된다.
	 * 		- spark.sql.adaptive.coalescePartitions.enabled=true 에 의해 파티션 수를 spark가 알아서 적절한 수로 줄이기 때문에 소용이 없다.
	 * 		- spark.sql.adaptive.enabled=true 끄는 것은 전반적인 성능 저하로 이어진다.
	 * 			- spark가 하는 내부 튜닝이 생각보다 좋으므로 이건 쓰는 것이 좋다.
	 *
	 * @param silverDf
	 * @param apDf
	 * @return
	 */
	private def addMetaOfAdProvider(silverDf: Dataset[Row], apDf: Dataset[Row]): Dataset[Row] = {
		val joinedDf = silverDf
			.join(apDf, silverDf("adProviderId") === apDf("adProviderId") && silverDf("responseCreativeType") === apDf("creativeType"), "left")
			.select(
				silverDf("*"),
				apDf("timezone"),
				apDf("currency"),
				apDf("estimatedReportType"),
				apDf("distTypeForImp"),
				apDf("distTypeForRev")
			)
			.withColumn("distTypeForImp", expr("CASE WHEN distTypeForImp IS NULL THEN 'IMP' ELSE distTypeForImp END"))
			.withColumn("distTypeForRev", expr("CASE WHEN distTypeForRev IS NULL THEN 'REV' ELSE distTypeForRev END"))
			.drop("saltKey")

		joinedDf
	}

	/**
	 * AP 타임존 기준 ymd로만 필터링
	 *
	 * @param df
	 * @param ymd
	 * @return
	 */
	private def filterYmd(df: Dataset[Row]): Dataset[Row] = {
		df.filter(s"apDate = '$ymd'")
	}

	/**
	 * 광고공급자 호출수/응답수 구하기
	 * 요청소재가 COMBINED인 경우 광고공급자 호출수/응답수 조정하기
	 *
	 * @param df
	 * @return
	 */
	private def getAdProviderStats(df: Dataset[Row]): Dataset[Row] = {
		// 계산식
		val aggExprs = Vector(
			expr("SUM(CASE WHEN (eventId IS NULL AND connectionType == 'S2S') OR (eventId ==  1 AND connectionType == 'C2S') OR (eventId == 91 AND connectionType == 'C2S') THEN 1 ELSE 0 END)").as("adProviderRequests"),
			expr("SUM(CASE WHEN (eventId IS NULL AND connectionType == 'S2S' AND stat == 1) OR (eventId == 1 AND connectionType == 'C2S') THEN 1 ELSE 0 END)").as("adProviderResponses"),
		)

		// 광고공급자 호출수/응답수 구하기
		val apReqResDf = df
			// responseCreativeType == '-' 또는 null 이면 requestCreativeType 으로 설정
			.withColumn("responseCreativeType", when($"responseCreativeType" === "-", $"requestCreativeType").otherwise($"responseCreativeType"))
			.groupBy(GFP_DIMS2.head, GFP_DIMS2.tail: _*)
			.agg(aggExprs.head, aggExprs.tail: _*)

		// 요청소재가 COMBINED인 경우 광고공급자 호출수/응답수 조정하기
		// AP_REQ_RES_DIMS
		// 	- 광고공급자 호출수를 조정하기 위한 윈도우 파티션 용 디멘젼 리스트
		// 	- GFP_DIMS2에서 responseCreativeType가 빠진 디멘젼
		val resultDf = refineApReqResForCombinedReqCt(apReqResDf, AP_REQ_RES_DIMS)

		resultDf
	}

	/**
	 * 서버/클라 union 하여 최종 광고공급자 호출수/응답수 구하기
	 *
	 * @param s2sDf
	 * @param c2sDf
	 * @return
	 */
	private def getUnionedAdProviderStats(s2sDf: Dataset[Row], c2sDf: Dataset[Row]): Dataset[Row] = {
		val aggExprs = AP_REQ_RES_METRICS.map(metric => sum(metric).as(metric))

		val df1 = s2sDf
			.union(c2sDf)
			.groupBy(GFP_DIMS2.head, GFP_DIMS2.tail: _*)
			.agg(aggExprs.head, aggExprs.tail: _*)

		df1
	}

	/**
	 * Zircon B GFP 통계 구하기
	 * 	- 비중을 구하기 위한 기본 데이터
	 * 	- 돈과 관련된 필드(salesXx)는 소수점 7째 자리에서 반올림하여 6째자리까지 표현
	 *      - decimal(15, 6)을 sum했을 때 spark가 알아서 decimal(25, 6)으로 캐스팅한다.
	 *      - 캐스팅 로직: https://github.com/apache/spark/blob/master/sql/catalyst/src/main/scala/org/apache/spark/sql/catalyst/analysis/DecimalPrecision.scala
	 *
	 * @param cDf
	 * @return
	 */
	private def getGfpStats(cDf: Dataset[Row]): Dataset[Row] = {
		val resultDf = cDf
			.groupBy(GFP_DIMS2.head, GFP_DIMS2.tail: _*)
			.agg(
				coalesce(sum(when($"eventId" === 1, 1L).otherwise(0L)), lit(0L)).as("gfpFilledRequests"),
				coalesce(sum(when($"eventId" === 11 && $"isValid" === '1', 1L).otherwise(0L)), lit(0L)).as("gfpImpressions"),
				coalesce(sum(when($"eventId" === 12 && $"isValid" === '1', 1L).otherwise(0L)), lit(0L)).as("gfpViewableImpressions"),
				coalesce(sum(when($"eventId" === 3 && $"isValid" === '1', 1L).otherwise(0L)), lit(0L)).as("gfpClicks"),
				coalesce(sum(when($"eventId" === 4, 1L).otherwise(0L)), lit(0L)).as("gfpCompletions"),

				coalesce(sum(when($"eventId" === 1, $"gfpEstimatedKrwSales").otherwise(BigDecimal("0"))), lit(0L)).as("gfpEstimatedKrwSalesForFilledRequests"),
				coalesce(sum(when($"eventId" === 11 && $"isValid" === "1", $"gfpEstimatedKrwSales").otherwise(BigDecimal("0"))), lit(0L)).as("gfpEstimatedKrwSalesForImpressions"),
				coalesce(sum(when($"eventId" === 12 && $"isValid" === "1", $"gfpEstimatedKrwSales").otherwise(BigDecimal("0"))), lit(0L)).as("gfpEstimatedKrwSalesForViewableImpressions"),

				coalesce(sum(when($"eventId" === 1, $"gfpEstimatedUsdSales").otherwise(BigDecimal("0"))), lit(0L)).as("gfpEstimatedUsdSalesForFilledRequests"),
				coalesce(sum(when($"eventId" === 11 && $"isValid" === "1", $"gfpEstimatedUsdSales").otherwise(BigDecimal("0"))), lit(0L)).as("gfpEstimatedUsdSalesForImpressions"),
				coalesce(sum(when($"eventId" === 12 && $"isValid" === "1", $"gfpEstimatedUsdSales").otherwise(BigDecimal("0"))), lit(0L)).as("gfpEstimatedUsdSalesForViewableImpressions"),
			)

			// estimatedImpressions 추가
			.withColumn("gfpEstimatedImpressions", when($"estimatedReportType" === "FILL", $"gfpFilledRequests")
				.when($"estimatedReportType" === "IMP", $"gfpImpressions")
				.when($"estimatedReportType" === "VIEW", $"gfpViewableImpressions")
				.otherwise(0L))

			// 추정수익
			// 	 - decimal(15, 6)인 gfpEstimatedKrw(Usd)Sales를 더하면 더한 값의 범위에 따라 spark가 알아서 precision을 늘림
			//   - 합산 값이 최대 999억까지 나올 수 있다고 보고 정수부 11자리(999억), 소수부6자리로 하여 precision=17, scale=6 으로 캐스팅한다.

			// 추정수익 KRW
			.withColumn("gfpEstimatedKrwSales", when($"estimatedReportType" === "FILL", $"gfpEstimatedKrwSalesForFilledRequests")
				.when($"estimatedReportType" === "IMP", $"gfpEstimatedKrwSalesForImpressions")
				.when($"estimatedReportType" === "VIEW", $"gfpEstimatedKrwSalesForViewableImpressions")
				.otherwise(BigDecimal("0")).cast(BaseConstant.DECIMAL_TYPE_17_6))

			// 추정수익 USD
			.withColumn("gfpEstimatedUsdSales", when($"estimatedReportType" === "FILL", $"gfpEstimatedUsdSalesForFilledRequests")
				.when($"estimatedReportType" === "IMP", $"gfpEstimatedUsdSalesForImpressions")
				.when($"estimatedReportType" === "VIEW", $"gfpEstimatedUsdSalesForViewableImpressions")
				.otherwise(BigDecimal("0")).cast(BaseConstant.DECIMAL_TYPE_17_6))

		resultDf
	}

	/**
	 * 비중 구하기. 필요한 필드만 추출함.
	 *
	 * @param df
	 * @return
	 */
	private def getWeight(df: Dataset[Row]): Dataset[Row] = {
		val df2 = df
			// 노출
			.withColumn("gfpEstimatedImpressionsSum", sum($"gfpEstimatedImpressions").over(commonWin))

			// 클릭
			.withColumn("gfpClicksSum", sum($"gfpClicks").over(commonWin))

			/*
				- 단일 소재유형인지의 여부
				- responseCreativeType == 'COMBINED'가 균등분배 몫을 가져가는 것을 막기 위함
			 */
			.withColumn("singleCreativeType", expr("CASE WHEN responseCreativeType == 'COMBINED' THEN 0 ELSE 1 END"))

			/*
				- GFP 수익이 0일 경우 AP의 수익을 균등 분배하기 위해 파티션별 개수 구함
				- responseCreativeType == 'COMBINED'인 경우를 제외한 개수
				- https://oss.navercorp.com/da-ssp/bts/issues/2504#issuecomment-13421103
			 */
			.withColumn("cntInPartition", sum($"singleCreativeType").over(commonWin))

			// 소수점 자리수
			.withColumn("gfpKrwFractionalPartLen", when($"gfpEstimatedKrwSales" === 0, 0).otherwise(length($"gfpEstimatedKrwSales".cast("string").substr(instr($"gfpEstimatedKrwSales", ".") + 1, length($"gfpEstimatedKrwSales")))))
			// 정수로 표현하기 위해 (10 x 소수점 자리수)
			.withColumn("gfpKrwVolume", pow(10, $"gfpKrwFractionalPartLen"))
			// GFP의 추정수익을 실수에서 정수로 변환
			.withColumn("gfpEstimatedKrwSalesInt", $"gfpEstimatedKrwSales" * $"gfpKrwVolume")
			// 파티션 별 GFP 추정수익의 합
			.withColumn("gfpEstimatedKrwSalesSumInt", sum($"gfpEstimatedKrwSalesInt").over(commonWin))

			// 소수점 자리수
			.withColumn("gfpUsdFractionalPartLen", when($"gfpEstimatedUsdSales" === 0, 0).otherwise(length($"gfpEstimatedUsdSales".cast("string").substr(instr($"gfpEstimatedUsdSales", ".") + 1, length($"gfpEstimatedUsdSales")))))
			// 정수로 표현하기 위해 (10 x 소수점 자리수)
			.withColumn("gfpUsdVolume", pow(10, $"gfpUsdFractionalPartLen"))
			// GFP의 추정수익을 실수에서 정수로 변환
			.withColumn("gfpEstimatedUsdSalesInt", $"gfpEstimatedUsdSales" * $"gfpUsdVolume")
			// 파티션 별 GFP 추정수익의 합
			.withColumn("gfpEstimatedUsdSalesSumInt", sum($"gfpEstimatedUsdSalesInt").over(commonWin))

			/*
				GFP 노출 분배율 - 노출 분배 기준 적용
					- distTypeForImp == 'IMP' 이면 노출을 노출로 분배
					- distTypeForImp == 'REV' 이면 노출을 수익으로 분배
					- 분모가 0이면 균등분배
					- responseCreativeType == 'COMBINED'는 분배 대상 아님
			 */
			.withColumn("gfpEstimatedImpressionsWeight", expr(
				"""CASE
				  |     WHEN responseCreativeType == 'COMBINED' THEN 0
				  |     WHEN responseCreativeType != 'COMBINED' AND distTypeForImp == 'IMP' AND gfpEstimatedImpressionsSum == 0 THEN 1 / cntInPartition
				  |     WHEN responseCreativeType != 'COMBINED' AND distTypeForImp == 'IMP' AND gfpEstimatedImpressionsSum != 0 THEN gfpEstimatedImpressions / gfpEstimatedImpressionsSum
				  |     WHEN responseCreativeType != 'COMBINED' AND distTypeForImp == 'REV' AND currency = 'KRW' AND gfpEstimatedKrwSalesSumInt == 0 THEN 1 / cntInPartition
				  |     WHEN responseCreativeType != 'COMBINED' AND distTypeForImp == 'REV' AND currency = 'KRW' AND gfpEstimatedKrwSalesSumInt != 0 THEN gfpEstimatedKrwSalesInt / gfpEstimatedKrwSalesSumInt
				  |     WHEN responseCreativeType != 'COMBINED' AND distTypeForImp == 'REV' AND currency = 'USD' AND gfpEstimatedUsdSalesSumInt == 0 THEN 1 / cntInPartition
				  |     WHEN responseCreativeType != 'COMBINED' AND distTypeForImp == 'REV' AND currency = 'USD' AND gfpEstimatedUsdSalesSumInt != 0 THEN gfpEstimatedUsdSalesInt / gfpEstimatedUsdSalesSumInt
				  |END
				  |""".stripMargin))

			/*
				GFP 클릭 분배율
					- 분모가 0이면 균등분배
					- responseCreativeType == 'COMBINED'는 분배 대상 아님
			 */
			.withColumn("gfpClicksWeight", expr(
				"""CASE
				  |		WHEN responseCreativeType == 'COMBINED' THEN 0
				  |		WHEN responseCreativeType != 'COMBINED' AND gfpClicksSum == 0 THEN 1 / cntInPartition
				  |		ELSE gfpClicks / gfpClicksSum
				  |END
				 """.stripMargin))

			/*
				GFP 수익 분배율 - AP 통화 기준 & 수익 분배 기준 적용
					- AP 통화가 KRW이면 USD도 KRW 비중으로 분배
					- AP 통화가 USD이면 KRW도 USD 비중으로 분배
					- distTypeForRev == 'IMP' 이면 수익을 노출로 분배
					- distTypeForRev == 'REV' 이면 수익을 수익으로 분배
					- 분모가 0이면 균등분배
					- responseCreativeType == 'COMBINED'는 분배 대상 아님
			 */
			.withColumn("gfpEstimatedSalesWeight", expr(
				"""CASE
				  |     WHEN responseCreativeType == 'COMBINED' THEN 0
				  |     WHEN responseCreativeType != 'COMBINED' AND distTypeForRev == 'IMP' AND gfpEstimatedImpressionsSum == 0 THEN 1 / cntInPartition
				  |     WHEN responseCreativeType != 'COMBINED' AND distTypeForRev == 'IMP' AND gfpEstimatedImpressionsSum != 0 THEN gfpEstimatedImpressions / gfpEstimatedImpressionsSum
				  |     WHEN responseCreativeType != 'COMBINED' AND distTypeForRev == 'REV' AND currency = 'KRW' AND gfpEstimatedKrwSalesSumInt == 0 THEN 1 / cntInPartition
				  |     WHEN responseCreativeType != 'COMBINED' AND distTypeForRev == 'REV' AND currency = 'KRW' AND gfpEstimatedKrwSalesSumInt != 0 THEN gfpEstimatedKrwSalesInt / gfpEstimatedKrwSalesSumInt
				  |     WHEN responseCreativeType != 'COMBINED' AND distTypeForRev == 'REV' AND currency = 'USD' AND gfpEstimatedUsdSalesSumInt == 0 THEN 1 / cntInPartition
				  |     WHEN responseCreativeType != 'COMBINED' AND distTypeForRev == 'REV' AND currency = 'USD' AND gfpEstimatedUsdSalesSumInt != 0 THEN gfpEstimatedUsdSalesInt / gfpEstimatedUsdSalesSumInt
				  |END
				  |""".stripMargin))


		// sylph :: add
		// 필요한 필드만 추출
		//			.select(FINAL_REQUIRED_METRICS.head, FINAL_REQUIRED_METRICS.tail: _*)

		df2
	}

	/**
	 * 광고공급자 응답수가 0인 경우도 지표에 포함시키기 위해 "left" join
	 * 	- https://oss.navercorp.com/da-ssp/bts/issues/2508#issuecomment-13426870
	 *
	 * @param dlvDf
	 * @param apReqResDf
	 * @return
	 */
	private def addApReqRes(dlvDf: Dataset[Row], apReqResDf: Dataset[Row]): Dataset[Row] = {
		apReqResDf.join(dlvDf, GFP_DIMS2, "left").na.fill(0, DLV_METRICS)
	}

	/**
	 * 서울 기준 광고유닛요청수 구하기
	 *
	 * @param df
	 * @return
	 */
	def getAdUnitRequestsStats(df: Dataset[Row]): Dataset[Row] = {
		df.groupBy(AUR_DIMS.head, AUR_DIMS.tail: _*).agg(count("requestId").as("adUnitRequests"))
	}

	/**
	 * 광고유닛요청수 추가
	 *
	 * @param weightDf
	 * @param aurDf
	 * @return
	 */
	def addAdUnitRequests(weightDf: Dataset[Row], aurDf: Dataset[Row]): Dataset[Row] = {
		val adUnitSaltRange = 50
		val adUnitSaltValues = (0 until adUnitSaltRange).toArray

		/*
			- 데이터가 큰 데이터프레임의 skewed adUnitId에 random salting
			- gfpDf의 saltedAdUnitId = {adUnitId}_{salt}
		 */
		val weightDf2 = weightDf.withColumn("saltedAdUnitId", concat($"adUnitId", lit("_"), lit(floor(rand * adUnitSaltRange))))

		/*
			- 데이터가 작은 데이터프레임에 salt 심기
			- join 시 자동으로 boradcast hash join으로 바뀌는 디폴트 사이즈: spark.sql.autoBroadcastJoinThreshold = 10MB
			- join 시 broadcast 할 최대 사이즈 디폴트: spark.sql.adaptive.autoBroadcastJoinThreshold = (none)
			- auReqDf의 saltedAdUnitId = {adUnitId}_{salt}
		 */
		val aurDf2 = aurDf
			.withColumn("salt", explode(lit(adUnitSaltValues)))
			.withColumn("saltedAdUnitId", concat($"adUnitId", lit("_"), $"salt"))
			.drop("salt")

		// 광고유닛요청수 붙이기
		val joinedDf = weightDf2.as("g")
			.join(aurDf2.as("a"), Vector("seoulTimestamp", "publisherId", "country", "deviceOs", "saltedAdUnitId"), "left")
			.select(
				$"g.*",
				expr("NVL(a.adUnitRequests, 0) AS adUnitRequests"),
			)
			.na.fill("-", GFP_DIMS2)
			.drop("saltedAdUnitId")

		joinedDf
	}

	/**
	 * 광고유닛요청수, 광고공급자호출수 그 외 주요 딜리버리 지표가 모두 0인 경우 해당 row 제외
	 * 	- 관련 Jira Ticket
	 * 		- https://jira.navercorp.com/browse/GFP-63
	 * 		- https://jira.navercorp.com/browse/GFP-130?focusedId=8149614&page=com.atlassian.jira.plugin.system.issuetabpanels:comment-tabpanel#comment-8149614
	 * 	- 관련 구 OSS Ticket: https://oss.navercorp.com/da-ssp/bts/issues/2622
	 *
	 * 원인
	 * 	- 지연 이벤트의 isValid != 1 인 경우 광고유닛요청수도 없고, isValid 조건에 의해 집계 결과도 0인 경우 발생.
	 *
	 * 해결
	 * 	- 로직상 정상이나 사용자가 보기에 이상하므로 이런 경우엔 해당 row를 제외시킨다.
	 *
	 * @param df
	 * @return
	 */
	private def excludeRowsHaveAllZeroMetrics(df: Dataset[Row]): Dataset[Row] = {
		val metricsToCheck = Vector("adUnitRequests", "adProviderRequests", "gfpFilledRequests", "gfpImpressions", "gfpViewableImpressions", "gfpClicks", "gfpCompletions")
		val filter = metricsToCheck.map(m => s"$m == 0").mkString(" and ")
		val refinedDf = df.filter(expr(s"not($filter)"))
		refinedDf
	}

	def getFuturesForDelete(helpableParam: Option[T] = None): List[Future[Option[Boolean]]] = {
		val dummyFutures = List(Future {
			Option(true)
		})
		dummyFutures
	}

	/**
	 * intermediate/{yyyy}/{mm}/{dd}/_publisherId={pubId}에 _hour, _adProviderId 단위로 파티션하여 저장
	 * outputPath = intermediate/{yyyy}/{mm}/{dd}/_publisherId={pubId}/_hour={hour}/_adProviderId={apId}
	 *
	 * @param df
	 * @param writeParam
	 */
	def write(df: Dataset[Row], writeParam: Option[W] = None): Unit = {
		val outputPath = writeParam.get.outputPath
		df
			.withColumn("_hour", $"apHour")
			.withColumn("_adProviderId", $"adProviderId")
			.write
			.mode("overwrite")
			.partitionBy("_hour", "_adProviderId")
			.parquet(outputPath)

		logger.debug(s"$LOG_PREFIX HDFS 쓰기 완료. $outputPath")
	}
}
