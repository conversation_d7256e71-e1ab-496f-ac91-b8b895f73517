/**
 * checkpoint
 * 16. Cache and checkpoint: enhancing Spark’s performances
 * https://livebook.manning.com/book/spark-in-action-second-edition/16-cache-and-checkpoint-enhancing-spark-s-performances/v-14/1
 */
package com.navercorp.gfp.biz.zircon.b

import java.util.Date
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import org.apache.spark.sql._
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.bson.types.ObjectId
import org.joda.time.DateTime

import com.navercorp.gfp.biz.zircon.ZirconProcessKind
import com.navercorp.gfp.biz.zircon.b.ZirconBAggregator.{AggParam, WriteParam}
import com.navercorp.gfp.biz.zircon.b.ZirconBConstant.{COMMON_DIMS, DT_DIMS, GFP_DIMS}
import com.navercorp.gfp.biz.zircon.b.gfd.GfdTimezoneHelper
import com.navercorp.gfp.core.BaseEnv._
import com.navercorp.gfp.core._
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory}
import com.navercorp.gfp.meta.adproviderinfo.AdProviderInfoDao
import com.navercorp.gfp.util.TimeUtil.SplitYmd
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil, TimeUtil}

object ZirconBAggregator extends BaseAggregator {
	var LOG_PREFIX = ".......... [ZB]"

	case class AggParam(greyDf: Dataset[Row], gfpDf: Dataset[Row])

	case class WriteParam(outputPath: String)

	def main(rawArgs: Array[String]): Unit = {
		val startTime = DateTime.now()

		val args = initArgs(rawArgs)
		args.foreach(println)

		// 처리유형:
		//		regular: 정규처리
		//		trace_silver: 실버 재처리에 의한 지르콘 재처리
		//		trace_silvergrey: 실버그레이 재처리에 의한 지르콘 재처리
		//		manual: 수동처리
		val kind = args(0)

		// 대상 일자: yyyyMMdd
		val ymd = args(1)

		// publisherId: "*" 또는 특정 publisherId
		val pubId = args(2)

		// adProviderId: "*" 또는 특정 adProviderId
		val apId = args(3)

		// kind=trace_silver 일 때 SilverTrace._id
		val sTraceId = if (args.length > 4 && Some(args(4)).nonEmpty) args(4) else "-"

		// - kind=trace_silvergrey 일 때 ZirconTrace._id
		// - 실버그레이 재처리에 의한 지르콘 재처리이기 때문에 kind=trace_silvergrey 이지만
		// 	 실제 남기는 _id는 ZirconTrace._id 임을 주의
		val zbTraceId = if (args.length > 5 && Some(args(5)).nonEmpty) args(5) else "-"

		// 계산을 위한 중간 지표를 결과에 포함시킬지
		val isFullField: Int = if (args.length > 6 && Some(args(6)).nonEmpty) args(6).toInt else 0

		LOG_PREFIX = s"$LOG_PREFIX ymd=$ymd kind=$kind pubId=$pubId apId=$apId sTraceId=$sTraceId zbTraceId=$zbTraceId isFullField=$isFullField"

		// 파라미터 밸리데이션
		validateParam(kind, pubId, apId, sTraceId, zbTraceId)

		logger.debug(s"$LOG_PREFIX 생성 시작")

		try {
			// 이력쌓기 - IN_PROGRESS
			val sparkDocForInProgress = Spark(
				sparkAppId = Option(spark.sparkContext.applicationId),
				sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
				sparkStartedAt = Option(new Date)
			)

			val publisher_id = if (pubId == "*") null else new ObjectId(pubId)
			val adProvider_id = if (apId == "*") null else new ObjectId(apId)
			val silverTrace_id = if (sTraceId == "-") null else new ObjectId(sTraceId)
			val zirconTrace_id = if (zbTraceId == "-") null else new ObjectId(zbTraceId)

			val inProgressHist = SummaryHistory(
				datetime = Option(ymd),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(sparkDocForInProgress),
				detail = Option(Map( // 처리 정보 기술
					"kind" -> kind,
					"publisher_id" -> publisher_id,
					"adProvider_id" -> adProvider_id,
					"silverTrace_id" -> silverTrace_id,
					"zirconTrace_id" -> zirconTrace_id,
					"isFullField" -> isFullField,
				))
			)
			this.upsertSummaryHistory(inProgressHist)

			// Zircon B 생성
			// tempIntermediatePath  = s"$ZIRCON_B_INTERMEDIATE_PATH/${split.yyyy}/${split.mm}/${split.dd}/pubApId=${pubId}_${apId}"
			val tempIntermediatePath = makeZirconB(ymd, kind, pubId, apId, sTraceId, zbTraceId, isFullField)

			if (tempIntermediatePath.isDefined) {
				// 기존 경로 삭제
				deleteIntermediatePath(ymd, pubId, apId)

				/*
					- 저장:     intermediate/{yyyy}/{mm}/{dd}/pubApId={pubId}_{apId}에 _hour={hour}/_publisherId={pubId}/_adProviderId={apId}로 파티셔닝
					- 리네임:   intermediate/{yyyy}/{mm}/{dd}/{hour}/_publisherId={pubId}/_adProviderId={apId}로 리네임
					- _SUCCESS: intermediate/{yyyy}/{mm}/{dd}/{hour}/_publisherId={pubId}/_adProviderId={apId}/_SUCCESS

					참고
					- ZBGFP는 _SUCCESS 파일이 intermediate/{yyyy}/{mm}/{dd}/{hour}/_publisherId={pubId}에 생기는 반면
					- ZB는    _SUCCESS 파일이 intermediate/{yyyy}/{mm}/{dd}/{hour}/_publisherId={pubId}/_adProviderId={apId}에 생김
				 */
				renameAndCreateSuccess(tempIntermediatePath.get)

				// intermediate/{yyyy}/{mm}/{dd}/pubApId={pubId}_{apId} 삭제
				HdfsUtil.delete(hdfs, tempIntermediatePath.get)
			}

			// 이력쌓기 - COMPLETE
			val sparkDocForComplete = Spark(
				sparkAppState = Option(SparkAppState.COMPLETE.toString),
				sparkEndedAt = Option(new Date)
			)
			val completeHist = SummaryHistory(
				spark = Option(sparkDocForComplete)
			)
			this.upsertSummaryHistory(completeHist)

			logger.debug(s"$LOG_PREFIX 생성 완료. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get}")
		} catch {
			case t: Throwable =>
				// 이력쌓기 - FAILURE
				val sparkDocForFailure = Spark(
					sparkAppState = Option(SparkAppState.FAILURE.toString),
					sparkAppError = Option(t.getMessage),
					sparkEndedAt = Option(new Date)
				)
				val failureHist = SummaryHistory(
					spark = Option(sparkDocForFailure)
				)
				this.upsertSummaryHistory(failureHist)

				logger.error(s"$LOG_PREFIX 생성 실패. sparkAppId=${spark.sparkContext.applicationId} summaryHistoryId=${summaryHistoryId.get} ${t.getMessage}")
				throw t;
		} finally {
			NeloUtil.waitFor()

			// 소요 시간
			val endTime = DateTime.now()
			val elapsedTime = endTime.getMillis - startTime.getMillis
			logger.debug(s"$LOG_PREFIX 소요시간=${elapsedTime / 1000}sec\n\n")
		}
	}

	/**
	 * 지원 가능한 pubId:apId 관계
	 * 		- 1:1
	 * 		- 1:N
	 * 		- N:1
	 *
	 * N:N 지원하지 않음
	 *
	 * @param pubId
	 * @param apId
	 */
	private def validateParam(kind: String, pubId: String, apId: String, sTraceId: String, zbTraceId: String): Unit = {
		if (!pubId.equals("*") && !ObjectId.isValid(pubId)) {
			throw new Exception("pubId is not hexadecimal")
		}
		if (!apId.equals("*") && !ObjectId.isValid(apId)) {
			throw new Exception("apId is not hexadecimal")
		}
		if (pubId.equals("*") && apId.equals("*")) {
			throw new Exception("Both pubId and apId cannot be *")
		}
		if (kind == ZirconProcessKind.TRACE_SILVER && (sTraceId.isEmpty || sTraceId.equals("-"))) {
			throw new Exception("sTraceId is required")
		}
		if (kind == ZirconProcessKind.TRACE_SILVERGREY && (zbTraceId.isEmpty || zbTraceId.equals("-"))) {
			throw new Exception("zbTraceId is required")
		}
	}

	/**
	 *
	 * @param ymd
	 * @param kind
	 * @param pubId
	 * @param apId
	 * @param sTraceId
	 * @param zbTraceId
	 * @param isFullField
	 * @return 실버그레이가 존재하고, 집계가 완료된 경우 tempIntermediate 경로( {yyyy}/{mm}/{dd}/{hour}/pubApId=xx )에 집계 결과 저장
	 */
	private def makeZirconB(ymd: String, kind: String, pubId: String, apId: String, sTraceId: String, zbTraceId: String, isFullField: Int): Option[String] = {
		val split: SplitYmd = TimeUtil.getSplitYmd(ymd)

		// 실버그레이 경로
		val greyPaths: Option[List[String]] = getSilvergreyPaths(split.yyyy, split.mm, split.dd, pubId, apId)

		// Zircon B GFP 경로
		val gfpPaths: Option[List[String]] = getGfpPaths(split.yyyy, split.mm, split.dd, pubId, apId)

		// 실버그레이 경로가 존재하면 진행
		if (greyPaths.isDefined && greyPaths.get.size > 0) {
			// 실버그레이 로드
			logger.debug(s"$LOG_PREFIX - 실버그레이 로드 ..")
			val silvergreyDf = loadParquetLog(greyPaths.get, BaseSchema.SILVERGREY_SCHEMA).get // 스키마 적용해서 로드

			// Zircon B GFP 로드
			logger.debug(s"$LOG_PREFIX - Zircon B GFP 로드 ..")
			val gfpDf = gfpPaths match {
				case Some(paths) => loadLog(paths)

				// [DATA] 지르콘 생성 시 실버가 없어도 실버그레이가 있으면 집계하도록 함 (https://jira.navercorp.com/browse/GFP-1087)
				// 	- ZBGFP 경로가 존재하지 않으면 빈 DataFrame 생성
				// 	- 이 후 집계 시에 full join 되므로 ZBGFP가 없어도 실버그레이만 있다면 실버그레이 값으로 반영됨
				case None => spark.createDataFrame(spark.sparkContext.emptyRDD[Row], BaseSchema.ZIRCON_B_GFP_SCHEMA)
			}

			val aggregator = new ZirconBAggregator(ymd, kind, pubId, apId, sTraceId, zbTraceId, isFullField)
			aggregator.init()

			// Zircon B 집계
			val aggDf = aggregator.aggregate(Some(AggParam(silvergreyDf, gfpDf)))

			/*
				Zircon B 저장
				- 병렬로 돌리기 때문에 날짜 경로만 주면 conflict이 발생하면서 에러남
				- 따라서 .../pubApId={pubId_{apId} 아래에 write
			 */
			val tempIntermediatePath = s"$ZIRCON_B_INTERMEDIATE_PATH/${split.yyyy}/${split.mm}/${split.dd}/pubApId=${pubId}_${apId}"
			aggregator.write(aggDf, Option(WriteParam(tempIntermediatePath)))

			Some(tempIntermediatePath)
		} else {
			logger.warn(s"$LOG_PREFIX 실버그레이가 존재하지 않아 집계 스킵. sparkAppId=${spark.sparkContext.applicationId} greyPaths=$greyPaths")
			None
		}
	}

	/**
	 * 실버 그레이 경로 얻기
	 * adProviderId={adProviderId}/publisherId={publisherId}/_SUCCESS 파일이 있는 경우만 포함
	 *
	 * 예) /user/gfp-data/silvergrey/{rk,nonrk}/2024/03/02/adProviderId=xx/publisherId=yy/_SUCCESS
	 *
	 * @param sgType
	 * @param yyyy
	 * @param mm
	 * @param dd
	 * @param pubId
	 * @return
	 */
	def getSilvergreyPaths(yyyy: String, mm: String, dd: String, pubId: String, apId: String): Option[List[String]] = {
		var paths = List[String]()

		// 1 Depth 경로 - adProvider
		var apPaths = List[String]()
		if (apId.equals("*")) {
			apPaths = HdfsUtil.list(hdfs, s"$SILVERGREY_ROOT/rk/$yyyy/$mm/$dd", true) ++
				HdfsUtil.list(hdfs, s"$SILVERGREY_ROOT/nonrk/$yyyy/$mm/$dd", true)
		} else {
			val isRkUseOpt: Option[Boolean] = adProviderDao.isRkUse(apId)
			if (isRkUseOpt.isDefined) {
				apPaths = List(s"$SILVERGREY_ROOT/${if (isRkUseOpt.get) "rk" else "nonrk"}/$yyyy/$mm/$dd/adProviderId=$apId")
			} else {
				throw new Exception(s"$LOG_PREFIX - AdProviders.reportApi.rkUse가 정의되어 있지 않아 데이터 생성 불가")
			}
		}
		logger.debug(s"$LOG_PREFIX 실버그레이 1 Depth 경로(AP) ..")
		apPaths.foreach(path => logger.debug(s"\t\t$path"))

		// 2 Depth 경로 - publisherId
		logger.debug(s"$LOG_PREFIX 실버그레이 2 Depth 경로(PUB) ..")
		if (pubId.equals("*")) {
			for (apPath <- apPaths) {
				val pubPaths = HdfsUtil.list(hdfs, apPath, true)
				for (pubPath <- pubPaths) {
					if (pubPath.lastIndexOf("/_SUCCESS") < 0) {
						val pubSucPath = s"$pubPath/_SUCCESS"
						if (HdfsUtil.exists(hdfs, pubSucPath)) {
							logger.debug(s"\t\t$pubSucPath 존재 O")
							paths = paths :+ pubPath
						} else {
							logger.debug(s"\t\t$pubSucPath 존재 X")
						}
					}
				}
			}
		} else {
			for (apPath <- apPaths) {
				val pubPath = s"$apPath/publisherId=$pubId"
				val pubSucPath = s"$pubPath/_SUCCESS"
				if (HdfsUtil.exists(hdfs, pubSucPath)) {
					logger.debug(s"\t\t$pubSucPath 존재 O")
					paths = paths :+ pubPath
				} else {
					logger.debug(s"\t\t$pubSucPath 존재 X")
				}
			}
		}

		if (paths.size < 1) {
			logger.warn(s"$LOG_PREFIX 로드할 실버그레이 경로 없음")
			None
		} else {
			logger.debug(s"$LOG_PREFIX 로드할 실버그레이 최종 경로 ..")
			paths.foreach(path => logger.debug(s"\t\t$path"))
			Some(paths)
		}
	}

	/**
	 * Zircon B GFP warehouse 경로 얻기.
	 * {yyyy}/{mm}/{dd}/{hour}/_publisherId={pubId}/_adProviderId={apId}/_COMPACTION_SUCCESS 파일이 있는 경우만 포함
	 *
	 * @param yyyy
	 * @param mm
	 * @param dd
	 * @param pubId
	 * @param apId
	 * @return
	 */
	private def getGfpPaths(yyyy: String, mm: String, dd: String, pubId: String, apId: String): Option[List[String]] = {
		var paths = List[String]()

		/*
			지르콘 B GFP warehouse의 경로
			hourPath = /user/gfp-data/zircon/b/gfp/warehouse/{yyyy}/{mm}/{dd}/{hour}
			pubPath  = /user/gfp-data/zircon/b/gfp/warehouse/{yyyy}/{mm}/{dd}/{hour}/_publisherId={pubId}
			apPath   = /user/gfp-data/zircon/b/gfp/warehouse/{yyyy}/{mm}/{dd}/{hour}/_publisherId={pubId}/_adProviderId={apId}
		 */
		HdfsUtil.list(hdfs, s"$ZIRCON_B_GFP_WAREHOUSE_PATH/$yyyy/$mm/$dd", true).foreach(hourPath => {
			if (pubId.equals("*")) { // pubId = * && apId = xx
				HdfsUtil.list(hdfs, hourPath, true).foreach(pubPath => {
					val apPath = s"$pubPath/_adProviderId=$apId"
					if (HdfsUtil.exists(hdfs, s"$apPath/_COMPACTION_SUCCESS")) paths = paths :+ apPath
				})
			} else {
				val pubPath = s"$hourPath/_publisherId=$pubId"
				if (HdfsUtil.exists(hdfs, s"$pubPath")) {
					if (apId.equals("*")) { // pubId = xx && apId = *
						HdfsUtil.list(hdfs, pubPath, true).foreach(apPath => {
							if (HdfsUtil.exists(hdfs, s"$apPath/_COMPACTION_SUCCESS")) paths = paths :+ apPath
						})
					} else { // pubId == xx && apId = xx
						val apPath = s"$pubPath/_adProviderId=$apId"
						if (HdfsUtil.exists(hdfs, s"$apPath/_COMPACTION_SUCCESS")) paths = paths :+ apPath
					}
				}
			}
		})

		if (paths.size < 1) {
			logger.warn(s"$LOG_PREFIX 로드할 Zircon GFP 경로 없음")
			None
		} else {
			logger.debug(s"$LOG_PREFIX 로드할 Zircon GFP 최종 경로 ..")
			paths.foreach(path => logger.debug(s"\t\t$path"))
			Some(paths)
		}
	}

	/**
	 * 기존에 적재되어 있던 intermediate 또는 warehouse 경로 삭제
	 * 재처리를 하면서 기존에 존재했던 시간 경로가 없는 경우도 생길 수 있기 때문에 해당 일자의 모든 시간대 경로를 삭제함
	 *
	 * @param split
	 * @param pubId
	 */
	private def deleteIntermediatePath(ymd: String, pubId: String, apId: String): Unit = {
		val split: SplitYmd = TimeUtil.getSplitYmd(ymd)

		// 지르콘 B의 시간 경로
		HdfsUtil.list(hdfs, s"$ZIRCON_B_INTERMEDIATE_PATH/${split.yyyy}/${split.mm}/${split.dd}", true).foreach(hourPath => {
			// hourPath = /user/gfp-data/zircon/b/intermediate/{yyyy}/{mm}/{dd}/{hour}
			if (pubId.equals("*")) { // pubId = * && apId = xx
				HdfsUtil.list(hdfs, hourPath, true).foreach(pubPath => {
					// apPath = /user/gfp-data/zircon/b/intermediate/{yyyy}/{mm}/{dd}/{hour}/_publisherId={publisherId}/_adProviderId={adProviderId}
					val apPath = s"$pubPath/_adProviderId=$apId"
					if (HdfsUtil.exists(hdfs, apPath)) HdfsUtil.delete(hdfs, apPath)
				})
			} else {
				val pubPath = s"$hourPath/_publisherId=$pubId"
				if (HdfsUtil.exists(hdfs, pubPath)) {
					if (apId.equals("*")) { // pubId = xx && apId = *
						HdfsUtil.delete(hdfs, pubPath)
					} else { // pubId == xx && apId = xx
						val apPath = s"$hourPath/_publisherId=$pubId/_adProviderId=$apId"
						if (HdfsUtil.exists(hdfs, apPath)) HdfsUtil.delete(hdfs, apPath)
					}
				}
			}
		})
	}

	/**
	 * 시간대 경로 이동 및 _SUCCESS 파일 생성
	 *
	 * @param intermediateTempPath : s"$ZIRCON_B_INTERMEDIATE_PATH/${yyyy}/${mm}/${dd}/pubApId={pubId}_{apId}"
	 *
	 */
	private def renameAndCreateSuccess(intermediateTempPath: String): Unit = {
		HdfsUtil.list(hdfs, intermediateTempPath, true).filter(hourPath => !hourPath.endsWith("_SUCCESS")).foreach(hourPath => {
			// hourPath = "/user/gfp-data/zircon/b/gfp/intermediate/{yyyy}/{mm}/{dd}/pubApId={pubId}_{apId}/_hour=00",
			HdfsUtil.list(hdfs, hourPath, true).foreach(pubPath => {
				// pubPath = "/user/gfp-data/zircon/b/gfp/intermediate/{yyyy}/{mm}/{dd}/pubApId={pubId}_{apId}/_hour={hour}/_publisherId={pubId}",
				HdfsUtil.list(hdfs, pubPath, true).foreach(apPath => {
					/*
						   apPath = "/user/gfp-data/zircon/b/gfp/intermediate/{yyyy}/{mm}/{dd}/pubApId={pubId}_{apId}/_hour={hour}/_publisherId={pubId}/_adProviderId={apId}"
						newApPath = "/user/gfp-data/zircon/b/gfp/intermediate/{yyyy}/{mm}/{dd}/{hour}/_publisherId={pubId}/_adProviderId={apId}"
					 */
					val newApPath = transformPath(apPath)
					if (HdfsUtil.exists(hdfs, newApPath)) HdfsUtil.delete(hdfs, newApPath)
					rename(apPath, newApPath)

					/*
						intermediate 경로에 _SUCCESS 파일 생성
						_SUCCESS = "/user/gfp-data/zircon/b/intermediate/{yyyy}/{mm}/{dd}/{hour}/_publisherId={pubId}/_adProviderId={apId}/_SUCCESS"
				 	*/
					HdfsUtil.create(hdfs, s"$newApPath/_SUCCESS") // 생성
				})
			})
		})
	}

	private def transformPath(input: String): String = {
		// 정규식을 사용하여 필요한 부분 추출
		val pattern = """.*/(\d{4}/\d{2}/\d{2})/pubApId=.*/_hour=(\d{2})/_publisherId=([^/]+)/_adProviderId=([^/]+)""".r

		input match {
			case pattern(date, hour, pubId, apId) =>
				// 시간 정보를 date 아래에 붙임
				s"$ZIRCON_B_INTERMEDIATE_PATH/$date/$hour/_publisherId=$pubId/_adProviderId=$apId"
			case _ =>
				// 패턴이 맞지 않는 경우 원래 문자열 반환
				input
		}
	}
}

class ZirconBAggregator(ymd: String, kind: String, pubId: String, apId: String, sTraceId: String, zbTraceId: String, isFullField: Int)(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper with GfdTimezoneHelper {

	import spark.implicits._

	type A = AggParam
	type W = WriteParam

	private val LOG_PREFIX = s".......... [ZB] ymd=$ymd kind=$kind pubId=$pubId apId=$apId sTraceId=$sTraceId zbTraceId=$zbTraceId"

	private val TERMS = GFP_DIMS.map((dim: String) => (dim, dim)).toVector ++
		Vector[(String, String)](
			// AP METRIC
			("apFinalImpressions", "apImpressions"),
			("apFinalClicks", "apClicks"),
			("apFinalKrwSales", "apKrwSales"),
			("apFinalUsdSales", "apUsdSales"),
			("apFinalKrwProfit", "apKrwProfit"),
			("apFinalUsdProfit", "apUsdProfit"),

			// GFP METRIC
			("adProviderRequests", "adProviderRequests"),
			("adProviderResponses", "adProviderResponses"),
			("gfpFilledRequests", "gfpFilledRequests"),
			("gfpImpressions", "gfpImpressions"),
			("gfpViewableImpressions", "gfpViewableImpressions"),
			("gfpClicks", "gfpClicks"),
			("gfpCompletions", "gfpCompletions"),
			("gfpEstimatedImpressions", "gfpEstimatedImpressions"),
			("gfpEstimatedKrwSales", "gfpEstimatedKrwSales"),
			("gfpEstimatedUsdSales", "gfpEstimatedUsdSales"),
			("gfpEstimatedKrwProfit", "gfpEstimatedKrwProfit"),
			("gfpEstimatedUsdProfit", "gfpEstimatedUsdProfit")
		)

	private val adProviderInfoDao = new AdProviderInfoDao()

	override def init(): Unit = {
		super.init()
	}

	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		// Zircon AP 구하기
		val silvergreyDf = prepareSilvergreyDf(aggParam.get.greyDf)
		val apStatsDf = getApStats(silvergreyDf) // 일별 집계
		val apStatsDf2 = addMetaFields(apStatsDf) // 시간 필드 추가. 해당 일자 00시로 설정

		// 분배 비중 가져오기
		val gfpDf: Dataset[Row] = prepareGfpDf(aggParam.get.gfpDf)

		// 분배 비중 적용
		val weightedDf = distributeApStats(apStatsDf2, gfpDf)
		//		println(s"$LOG_PREFIX \n분배 비중 적용 후 스키마...........")
		//		weightedDf.printSchema

		// GFP 수수료율 로드 및 적용
		val weightedDf2 = applyGfpFeeRate(weightedDf)
		//		println(s"$LOG_PREFIX \n수수료율 적용 후 스키마...........")
		//		weightedDf2.printSchema

		// 최종 컬럼명으로 변경
		var finalDf: Dataset[Row] = null
		if (isFullField == 1) { // 디버깅 용으로 계산에 필요한 중간 필드 모두를 저장
			//			println(s"$LOG_PREFIX \n컬럼명 변경 전 스키마...........")
			//			weightedDf2.printSchema

			val finalTerms = getFullTerms(weightedDf2)
			finalDf = renameColumn(weightedDf2, finalTerms)
		} else { // production 용으로 계산에 필요한 중간 필드 모두 빼고 필수 필드만으로 저장
			finalDf = renameColumn(weightedDf2, TERMS)
		}
		//		println(s"$LOG_PREFIX \n최종 컬럼명으로 변경 후 스키마...........")
		//		termsAppliedDf.printSchema

		println(s"$LOG_PREFIX \n최종 스키마...........")
		finalDf.printSchema

		finalDf

		/*
		root
		 |-- publisherId: string (nullable = false)
		 |-- adProviderId: string (nullable = false)
		 |-- placeKey: string (nullable = false)
		 |-- dealId: string (nullable = false)
		 |-- country: string (nullable = false)
		 |-- deviceOs: string (nullable = false)
		 |-- adUnitId: string (nullable = false)
		 |-- adUnitRequests: long (nullable = true)
		 |-- serviceId: string (nullable = false)
		 |-- biddingGroupId: string (nullable = false)
		 |-- connectionType: string (nullable = false)
		 |-- requestCreativeType: string (nullable = true)
		 |-- responseCreativeType: string (nullable = false)
		 |-- adProviderPlaceId: string (nullable = false)
		 |-- currency: string (nullable = true)
		 |-- estimatedReportType: string (nullable = true)
		 |-- distTypeForImp: string (nullable = true)
		 |-- distTypeForRev: string (nullable = true)
		 |-- adProviderRequests: long (nullable = false)
		 |-- adProviderResponses: long (nullable = false)
		 |-- gfpFilledRequests: long (nullable = false)
		 |-- gfpImpressions: long (nullable = false)
		 |-- gfpViewableImpressions: long (nullable = false)
		 |-- gfpClicks: long (nullable = false)
		 |-- gfpCompletions: long (nullable = false)
		 |-- gfpEstimatedKrwSalesForFilledRequests: decimal(26,6) (nullable = true)
		 |-- gfpEstimatedKrwSalesForImpressions: decimal(26,6) (nullable = true)
		 |-- gfpEstimatedKrwSalesForViewableImpressions: decimal(26,6) (nullable = true)
		 |-- gfpEstimatedUsdSalesForFilledRequests: decimal(26,6) (nullable = true)
		 |-- gfpEstimatedUsdSalesForImpressions: decimal(26,6) (nullable = true)
		 |-- gfpEstimatedUsdSalesForViewableImpressions: decimal(26,6) (nullable = true)
		 |-- gfpEstimatedImpressions: long (nullable = false)
		 |-- gfpEstimatedKrwSales: decimal(17,6) (nullable = true)
		 |-- gfpEstimatedUsdSales: decimal(17,6) (nullable = true)
		 |-- gfpEstimatedImpressionsSum: long (nullable = true)
		 |-- gfpClicksSum: long (nullable = true)
		 |-- singleCreativeType: integer (nullable = true)
		 |-- cntInPartition: long (nullable = true)
		 |-- gfpKrwFractionalPartLen: integer (nullable = true)
		 |-- gfpKrwVolume: double (nullable = true)
		 |-- gfpEstimatedKrwSalesInt: double (nullable = true)
		 |-- gfpEstimatedKrwSalesSumInt: double (nullable = true)
		 |-- gfpUsdFractionalPartLen: integer (nullable = true)
		 |-- gfpUsdVolume: double (nullable = true)
		 |-- gfpEstimatedUsdSalesInt: double (nullable = true)
		 |-- gfpEstimatedUsdSalesSumInt: double (nullable = true)
		 |-- gfpEstimatedImpressionsWeight: double (nullable = false)
		 |-- gfpClicksWeight: double (nullable = false)
		 |-- gfpEstimatedSalesWeight: double (nullable = false)
		 |-- orgApImpressions: long (nullable = false)
		 |-- orgApClicks: long (nullable = false)
		 |-- orgApKrwSales: decimal(25,6) (nullable = true)
		 |-- orgApUsdSales: decimal(25,6) (nullable = true)
		 |-- zeroHourSeoulTimestamp: timestamp (nullable = true)
		 |-- seoulTimestamp: timestamp (nullable = true)
		 |-- apTimestamp: timestamp (nullable = true)
		 |-- apHour: string (nullable = false)
		 |-- timezone: string (nullable = false)
		 |-- weightedApImp: long (nullable = true)
		 |-- weightedApImpSum: long (nullable = true)
		 |-- apImpDiff: long (nullable = true)
		 |-- impRest: double (nullable = true)
		 |-- impRestRn: integer (nullable = false)
		 |-- apImpressions: long (nullable = true)
		 |-- weightedApClk: long (nullable = true)
		 |-- weightedApClkSum: long (nullable = true)
		 |-- apClkDiff: long (nullable = true)
		 |-- clkRest: double (nullable = true)
		 |-- clkRestRn: integer (nullable = false)
		 |-- apClicks: long (nullable = true)
		 |-- apKrwFractionalPartLen: integer (nullable = true)
		 |-- apKrwVolume: long (nullable = true)
		 |-- apKrwSalesInt: long (nullable = true)
		 |-- weightedApKrwSalesInt: long (nullable = true)
		 |-- weightedApKrwSalesSumInt: long (nullable = true)
		 |-- apKrwSalesDiff: long (nullable = true)
		 |-- apKrwSalesRest: double (nullable = true)
		 |-- apKrwSalesRestRn: integer (nullable = false)
		 |-- apRefinedKrwSales: long (nullable = true)
		 |-- apKrwSales: decimal(17,6) (nullable = true)
		 |-- apUsdFractionalPartLen: integer (nullable = true)
		 |-- apUsdVolume: long (nullable = true)
		 |-- apUsdSalesInt: long (nullable = true)
		 |-- weightedApUsdSalesInt: long (nullable = true)
		 |-- weightedApUsdSalesSumInt: long (nullable = true)
		 |-- apUsdSalesDiff: long (nullable = true)
		 |-- apUsdSalesRest: double (nullable = true)
		 |-- apUsdSalesRestRn: integer (nullable = false)
		 |-- apRefinedUsdSales: long (nullable = true)
		 |-- apUsdSales: decimal(17,6) (nullable = true)
		 |-- apKrwProfit: decimal(17,6) (nullable = true)
		 |-- apUsdProfit: decimal(17,6) (nullable = true)
		 |-- gfpEstimatedKrwProfit: decimal(17,6) (nullable = true)
		 |-- gfpEstimatedUsdProfit: decimal(17,6) (nullable = true)
		 */
	}

	private def prepareSilvergreyDf(df: Dataset[Row]): Dataset[Row] = {
		var cols = Vector(
			"publisherId",
			"adProviderId",
			"adProviderPlaceKey AS placeKey",
			"CAST(COALESCE(krwSales, salesKrw) AS DECIMAL(15, 6)) AS krwSales",
			"CAST(COALESCE(usdSales, salesUsd) AS DECIMAL(15, 6)) AS usdSales",
			"impressions",
			"clicks")

		// 컬럼 목록에 dealId 추가
		if (df.columns.contains("dealId")) {
			cols = cols :+ "NVL(dealId, '-') AS dealId"
		} else {
			cols = cols :+ "'-' AS dealId" // RK는 dealId가 없으므로 디폴트값 설정
		}

		df.selectExpr(cols: _*)
	}

	/**
	 * - Zircon B GFP 준비
	 * - [DATA] 시간별 지르콘 COMBINED의 adProviderRequests 처리 검토 요청 (https://jira.navercorp.com/browse/GFP-139)
	 * placeCreativeType -> requestCreativeType 으로 변경
	 * GFP-139 적용 전에 만들어진 ZBGFP를 재처리할 때는 이름을 바꾸어 줌
	 *
	 * @param df
	 * @return
	 */
	private def prepareGfpDf(df: Dataset[Row]): Dataset[Row] = {
		if (!df.columns.contains("requestCreativeType")) {
			df.withColumnRenamed("placeCreativeType", "requestCreativeType")
		} else {
			df
		}
	}

	/**
	 * AP 통계 구하기
	 *
	 * @param df
	 * @return
	 */
	private def getApStats(df: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX - AP 통계 구하기..")
		val aggList = Seq(
			expr("NVL(SUM(impressions), 0L)").as("orgApImpressions"),
			expr("NVL(SUM(clicks), 0L)").as("orgApClicks"),
			expr("NVL(SUM(krwSales), 0)").as("orgApKrwSales"), // 합산을 하면 decimal(25,6)이 됨
			expr("NVL(SUM(usdSales), 0)").as("orgApUsdSales"), // 합산을 하면 decimal(25,6)이 됨
		)
		val resultDf = df
			.groupBy(COMMON_DIMS.head, COMMON_DIMS.tail: _*)
			.agg(aggList.head, aggList.tail: _*)

		println(s"$LOG_PREFIX\nAP 통계 스키마...")
		resultDf.printSchema()
		resultDf
	}

	/**
	 * 실버그레이에 메타 필드 추가
	 *
	 * @param silvergreyDf
	 * @param apDf
	 * @return
	 */
	private def addMetaFields(silvergreyDf: Dataset[Row]): Dataset[Row] = {
		// 광고공급자 메타 정보 로드
		val apDf = broadcast(loadAdProviders())

		// broadcast hash join으로 풀림
		val joinedDf = silvergreyDf.as("sg")
			.join(apDf.as("a"), $"sg.adProviderId" === $"a.adProviderId")
			.selectExpr(
				"sg.*",
				"a.timezone",
				"a.connectionType",
			)

		// [DATA] GFD 연동 리포트 타임존 변경 영향 검토 (https://jira.navercorp.com/browse/GFP-1008)
		// 처리하려는 날짜에 따라 reportApi.type == 'GFD' AP들의 타임존 설정
		val df2 = decideGfdTimezone(ymd, joinedDf)

		df2
			/*
				- distributeApStats() 단계에서 비중 데이터와 join 시 full join을 할 것이므로
				  비중 데이터프레임에 데이터가 없고 AP 데이터만 있는 경우를 00시로 설정
				- AP 데이터는 일별이기 때문에 시간대를 알 수 없어 00시로 몰아줌
			 */
			.withColumn("apTimestamp", to_timestamp(lit(ymd + "00"), "yyyyMMddHH"))
			.withColumn("apHour", substring(date_format($"apTimestamp", "yyyyMMddHH"), -2, 2))
			.withColumn("utcTimestamp", to_utc_timestamp($"apTimestamp", $"timezone")) // 해당일자 00시에 해당하는 UTC 타임스캠프
			.withColumn("seoulTimestamp", from_utc_timestamp($"utcTimestamp", lit("Asia/Seoul"))) // 해당일자 00시에 해당하는 서울 타임스캠프
	}

	/**
	 * 광고공급자 메타 정보 로드
	 *
	 * @return
	 */
	private def loadAdProviders(): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 광고공급자 메타 정보 로드")

		var apIds = Vector[String]()
		if (apId != "*") {
			// 사용자가 선택한 AP만
			apIds = Vector(apId)
		} else {
			// 이 매체에 관련된 AP만
			apIds = adProviderInfoDao.getAdProviderIdsByPublisherId(pubId)
		}

		// SyncAdProviders 스키마 정의
		val oidStructType = StructType(List(StructField("oid", StringType, nullable = true)))
		val schema = StructType(List(
			StructField("_id", oidStructType, nullable = true),
			StructField("timezone", StringType, nullable = true),
			StructField("connectionType", StringType, nullable = true),
		))

		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdProviders")
		val df = spark.read
			.schema(schema)
			.mongo(readConfig)
			.select(
				$"_id.oid".as("adProviderId"),
				$"timezone",
				$"connectionType",
			)
			.filter($"adProviderId".isin(apIds: _*))
		df
	}

	/**
	 * 비중 분배
	 *
	 * 1. AP의 노출을 가져가는 대상이 먼저 정해지고(gfpEstimatedImpressionsWeight > 0)
	 *
	 * 2. 그 안에서 소수점을 나눠 갖는다.
	 * 		- apImpDiff는 gfpEstimatedImpressionsWeight > 0 인 개수보다 적거나 같다.
	 *
	 * 3. impRestRn은 AP의 노출을 가져가는 대상과 관계 없이 모든 row에 부여된다.
	 *
	 * 4. COMBINED는 AP 노출을 가져가는 대상이 아니기(gfpEstimatedImpressionsWeight = 0) 때문에 apImpDiff 안에 들어올 수 없다.
	 * 		- 이를 표시하기 위해 xxRest = -1로 설정(https://oss.navercorp.com/da-ssp/bts/issues/2504#issuecomment-13406984)
	 *
	 * @param apDf
	 * @param gfpDf
	 * @return
	 */
	private def distributeApStats(apDf: Dataset[Row], gfpDf: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 분배 비중 적용 ..")

		val commonWin = Window.partitionBy(COMMON_DIMS.head, COMMON_DIMS.tail: _*)
		val impRestWin = commonWin.orderBy($"impRest".desc)
		val clkRestWin = commonWin.orderBy($"clkRest".desc)
		val usdRvRestWin = commonWin.orderBy($"apUsdSalesRest".desc)
		val krwRvRestWin = commonWin.orderBy($"apKrwSalesRest".desc)

		/*
			full join
				- skewed data를 해결하기 위해 apDf를 salting 했을 때 성능이 좋아지는 매체도 있고,
				  오히려 역효화를 내어 spill이 발생하는 매체도 있다.
				- 모든 매체를 만족하는 saltRange를 찾을 수 없는 상황이므로 salting은 하지 않는 것으로.

			join 이후 windowing(연산) 성능
				- spark.sql.adaptive.advisoryPartitionSizeInBytes=16m (default 64m)
					- https://spark.apache.org/docs/3.2.1/configuration.html
					  The advisory size in bytes of the shuffle partition during adaptive optimization (when spark.sql.adaptive.enabled is true).
					  It takes effect when Spark coalesces small shuffle partitions or splits skewed shuffle partition.
					- 위 옵션은 AQE(Adative Query Execution)에 의해 파티션 크기가 조정될 때 이 값보다 크면 나누고, 작으면 합친다.
					- salting 하지 않고 spill이 발생하지 않는 선에서 위 옵션을 조정하여 튜닝
		 */
		val distributedDf = gfpDf.as("g")
			.join(apDf.as("a"), COMMON_DIMS, "full")

			// "full join"이므로 어느 한 쪽에 데이터가 없는 경우를 위해 1순위는 비중 데이터로, 2순위는 AP 데이터로 설정
			// 서울 기준 00 시
			.withColumn("seoulTimestamp2", coalesce($"g.seoulTimestamp", $"a.seoulTimestamp"))
			.withColumn("utcTimestamp2", coalesce($"g.utcTimestamp", $"a.utcTimestamp"))
			.withColumn("apTimestamp2", coalesce($"g.apTimestamp", $"a.apTimestamp"))
			.withColumn("apHour2", coalesce($"g.apHour", $"a.apHour"))
			.withColumn("timezone2", coalesce($"g.timezone", $"a.timezone"))
			.withColumn("connectionType2", coalesce($"g.connectionType", $"a.connectionType"))

			// ambigous를 없애기 위해 drop 및 rename
			.drop((DT_DIMS :+ "connectionType"): _*)
			.withColumnRenamed("timezone2", "timezone")
			.withColumnRenamed("apTimestamp2", "apTimestamp")
			.withColumnRenamed("apHour2", "apHour")
			.withColumnRenamed("utcTimestamp2", "utcTimestamp")
			.withColumnRenamed("seoulTimestamp2", "seoulTimestamp")
			.withColumnRenamed("connectionType2", "connectionType")

			// full join에 의해 null이 된 필드는 디폴트 값으로 채우기
			.na.fill("-", GFP_DIMS)
			.na.fill(0L, List("adProviderRequests", "adProviderResponses", "adUnitRequests", "gfpFilledRequests", "gfpImpressions", "gfpViewableImpressions", "gfpClicks", "gfpCompletions", "gfpEstimatedImpressions", "gfpEstimatedKrwSales", "gfpEstimatedUsdSales", "orgApImpressions", "orgApClicks", "orgApKrwSales", "orgApUsdSales"))
			.na.fill(1L, List("gfpEstimatedImpressionsWeight", "gfpClicksWeight", "gfpEstimatedSalesWeight")) // GFP 통계가 없을 경우 분배율 1로 설정(AP의 지표를 그대로 사용)

			// 비중 분배된 AP의 노출
			.withColumn("weightedApImp", floor($"orgApImpressions" * $"gfpEstimatedImpressionsWeight"))
			.withColumn("weightedApImpSum", sum($"weightedApImp").over(commonWin))
			.withColumn("apImpDiff", $"orgApImpressions" - $"weightedApImpSum")
			.withColumn("impRest", expr("CASE WHEN responseCreativeType = 'COMBINED' THEN -1 WHEN weightedApImp == 0 THEN (orgApImpressions * gfpEstimatedImpressionsWeight) ELSE (orgApImpressions * gfpEstimatedImpressionsWeight) % weightedApImp END"))
			.withColumn("impRestRn", row_number.over(impRestWin))
			.withColumn("apFinalImpressions", expr("CASE WHEN apImpDiff > 0 AND impRestRn <= apImpDiff THEN weightedApImp + 1 ELSE weightedApImp END"))

			// 비중 분배된 AP의 클릭
			.withColumn("weightedApClk", floor($"orgApClicks" * $"gfpClicksWeight"))
			.withColumn("weightedApClkSum", sum($"weightedApClk").over(commonWin))
			.withColumn("apClkDiff", $"orgApClicks" - $"weightedApClkSum")
			.withColumn("clkRest", expr("CASE WHEN responseCreativeType = 'COMBINED' THEN -1 WHEN weightedApClk == 0 THEN (orgApClicks * gfpClicksWeight) ELSE (orgApClicks * gfpClicksWeight) % weightedApClk END"))
			.withColumn("clkRestRn", row_number.over(clkRestWin))
			.withColumn("apFinalClicks", expr("CASE WHEN apClkDiff > 0 AND clkRestRn <= apClkDiff THEN weightedApClk + 1 ELSE weightedApClk END"))

			// AP의 KRW 수익을 실수에서 정수로 변환
			.withColumn("apKrwFractionalPartLen", when($"orgApKrwSales" === 0, 0).otherwise(length($"orgApKrwSales".cast("string").substr(instr($"orgApKrwSales", ".") + 1, length($"orgApKrwSales")))))
			.withColumn("apKrwVolume", pow(10, $"apKrwFractionalPartLen").cast(LongType))
			.withColumn("apKrwSalesInt", ($"orgApKrwSales" * $"apKrwVolume").cast(LongType))

			// 비중 분배된 AP의 수익 - KRW
			// 소수점 이하 버림
			.withColumn("weightedApKrwSalesInt", floor($"apKrwSalesInt" * $"gfpEstimatedSalesWeight"))
			.withColumn("weightedApKrwSalesSumInt", sum($"weightedApKrwSalesInt").over(commonWin))
			// 원래 AP 수익에서 비중 분배된 AP 수익의 차이. 남은 금액을 소수점 이하 금액이 높은 아이에게 차례로 분배하기 위해 구함.
			.withColumn("apKrwSalesDiff", $"apKrwSalesInt" - $"weightedApKrwSalesSumInt")
			// 비중 분배된 수익의 소수부를 구하고
			.withColumn("apKrwSalesRest", expr("CASE WHEN responseCreativeType = 'COMBINED' THEN -1 WHEN weightedApKrwSalesInt == 0 THEN (apKrwSalesInt * gfpEstimatedSalesWeight) ELSE (apKrwSalesInt * gfpEstimatedSalesWeight) % weightedApKrwSalesInt END"))
			// 소수점 이하 높은 금액 순으로 정렬하여 row number 매기기
			.withColumn("apKrwSalesRestRn", row_number.over(krwRvRestWin))
			// 소수점 이하 금액이 높은 아이에게 차례로 남은 금액 분배
			.withColumn("apRefinedKrwSales", expr("CASE WHEN apKrwSalesDiff > 0 AND apKrwSalesRestRn <= apKrwSalesDiff THEN weightedApKrwSalesInt + 1 ELSE weightedApKrwSalesInt END"))
			// 소수점 7째 자리에서 반올림
			.withColumn("apFinalKrwSales", ($"apRefinedKrwSales" / $"apKrwVolume").cast(DataTypes.createDecimalType(17, 6)))

			// AP의 USD 수익을 실수에서 정수로 변환
			.withColumn("apUsdFractionalPartLen", when($"orgApUsdSales" === 0, 0).otherwise(length($"orgApUsdSales".cast("string").substr(instr($"orgApUsdSales", ".") + 1, length($"orgApUsdSales")))))
			.withColumn("apUsdVolume", pow(10, $"apUsdFractionalPartLen").cast(LongType))
			.withColumn("apUsdSalesInt", ($"orgApUsdSales" * $"apUsdVolume").cast(LongType))

			// 비중 분배된 AP의 수익 - USD
			// 소수점 이하 버림
			.withColumn("weightedApUsdSalesInt", floor($"apUsdSalesInt" * $"gfpEstimatedSalesWeight"))
			.withColumn("weightedApUsdSalesSumInt", sum($"weightedApUsdSalesInt").over(commonWin))
			// 원래 AP 수익에서 비중 분배된 AP 수익의 차이. 남은 금액을 소수점 이하 금액이 높은 아이에게 차례로 분배하기 위해 구함.
			.withColumn("apUsdSalesDiff", $"apUsdSalesInt" - $"weightedApUsdSalesSumInt")
			// 비중 분배된 수익의 소수부를 구하고
			.withColumn("apUsdSalesRest", expr("CASE WHEN responseCreativeType = 'COMBINED' THEN -1 WHEN weightedApUsdSalesInt == 0 THEN (apUsdSalesInt * gfpEstimatedSalesWeight) ELSE (apUsdSalesInt * gfpEstimatedSalesWeight) % weightedApUsdSalesInt END"))
			// 소수점 이하 높은 금액 순으로 정렬하여 row number 매기기
			.withColumn("apUsdSalesRestRn", row_number.over(usdRvRestWin))
			// 소수점 이하 금액이 높은 아이에게 차례로 남은 금액 분배
			.withColumn("apRefinedUsdSales", expr("CASE WHEN apUsdSalesDiff > 0 AND apUsdSalesRestRn <= apUsdSalesDiff THEN weightedApUsdSalesInt + 1 ELSE weightedApUsdSalesInt END"))
			// 소수점 7째 자리에서 반올림
			.withColumn("apFinalUsdSales", ($"apRefinedUsdSales" / $"apUsdVolume").cast(DataTypes.createDecimalType(17, 6)))

		distributedDf
	}

	/**
	 * GFP 수수료 적용
	 *
	 * @param statsDf
	 * @param feeRateDf
	 * @return
	 */
	private def applyGfpFeeRate(statsDf: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 수수료율 적용 ...")

		val saltRange = 2000
		val saltValues = (0 until saltRange).toArray

		/*
			- 데이터가 큰 데이터프레임의 skewed adProviderId에 salting
			- 실버의 saltKey = {publisherId}_{adProviderId}_{salt}
		 */
		val statsDf2 = statsDf.withColumn("saltKey", concat($"publisherId", lit("_"), $"adProviderId", lit("_"), lit(floor(rand * saltRange))))

		/*
			- 데이터가 작은 데이터프레임에 salt 심기
			- join 시 자동으로 boradcast hash join으로 바뀌는 디폴트 사이즈: spark.sql.autoBroadcastJoinThreshold = 10MB
			- join 시 broadcast 할 최대 사이즈 디폴트: spark.sql.adaptive.autoBroadcastJoinThreshold = (none)
			- 아래는 강제로 broadcast함
		 */
		val feeRateDf = loadGfpFeeRate()
		val feeRateDf2 = broadcast(feeRateDf
			.withColumn("salt", explode(lit(saltValues)))
			// feeRateDf의 saltKey = {publisherId}_{adProviderId}_{salt}
			.withColumn("saltKey", concat($"publisherId", lit("_"), $"adProviderId", lit("_"), $"salt"))
			.drop("salt")
		)

		// broadcast hash join으로 풀림
		val df = statsDf2.as("s")
			.join(feeRateDf2.as("f"), Vector("saltKey"), "left")
			.na.fill(0, List("feeRate"))
			.select(
				$"s.*",
				expr("CAST(ROUND(s.apFinalKrwSales * (1 - feeRate), 6) AS DECIMAL(17, 6))").as("apFinalKrwProfit"),
				expr("CAST(ROUND(s.apFinalUsdSales * (1 - feeRate), 6) AS DECIMAL(17, 6))").as("apFinalUsdProfit"),
				expr("CAST(ROUND(s.gfpEstimatedKrwSales  * (1 - feeRate), 6) AS DECIMAL(17, 6))").as("gfpEstimatedKrwProfit"),
				expr("CAST(ROUND(s.gfpEstimatedUsdSales  * (1 - feeRate), 6) AS DECIMAL(17, 6))").as("gfpEstimatedUsdProfit"),
			) // f.feeRate으로 하면 컬럼을 못 찾음. join 뒤 스키마에 f.feeRate가 아니라 feeRate로 나옴
			.drop("saltKey")

		df
	}

	/**
	 * GFP 수수료율 로드
	 *
	 * @param ymd
	 * @return
	 */
	def loadGfpFeeRate(): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX 수수료율 로드 ...")
		val readConfig = BaseEnv.mdbDefaultReadConfig
			.withOption("spark.mongodb.input.collection", "GfpFeeRate")
			.withOption("spark.mongodb.input.batchSize", "2000")

		val df = spark.read
			.mongo(readConfig)
			.filter(s"date == '$ymd'")

		var df2 = df

		if (!pubId.equals("*")) {
			df2 = df2.filter(s"publisher_id.oid == '$pubId'")
		}
		if (!apId.equals("*")) {
			df2 = df2.filter(s"adProvider_id.oid == '$apId'")
		}

		df2 = df2.select(
			$"publisher_id.oid".as("publisherId"),
			$"adProvider_id.oid".as("adProviderId"),
			$"feeRate",
		)

		df2
	}

	/**
	 * 개발용 필드명을 사용자향 필드명으로 바꾸기 위한 tuple 구성
	 *
	 * @param df
	 * @return
	 */
	private def getFullTerms(df: Dataset[Row]): Vector[(String, String)] = {
		val fieldNames: Array[String] = df.schema.fieldNames
		val result = fieldNames.flatMap { fieldName =>
			if (TERMS.exists(_._1 == fieldName)) { // terms에 있다면 그 용어로
				val (key, value) = TERMS.find(_._1 == fieldName).get
				Vector((key, value))
			} else { // terms에 없다면 필드명 그대로
				Vector((fieldName, fieldName))
			}
		}
		result.toVector
	}

	def getFuturesForDelete(helpableParam: Option[T] = None): List[Future[Option[Boolean]]] = {
		val dummyFutures = List(Future {
			Option(true)
		})
		dummyFutures
	}

	def write(df: Dataset[Row], writeParam: Option[W] = None): Unit = {
		val outputPath = writeParam.get.outputPath

		df
			.withColumn("_hour", $"apHour")
			.withColumn("_publisherId", $"publisherId")
			.withColumn("_adProviderId", $"adProviderId")
			.write
			.format("parquet")
			.mode("overwrite")
			.partitionBy("_hour", "_publisherId", "_adProviderId")
			.save(outputPath)
		logger.debug(s"$LOG_PREFIX  HDFS 쓰기 완료. $outputPath")
	}
}

/*
[ 2024.01.01 ]
	* 병렬도를 높이면 하나의 태스크가 일하는 양이 줄어들고 더 골고루 처리하기때문에 메모리 사용량이 균등해짐
	* salting 기법은 SortMergeJoin이 BroadcastHashJoin으로 풀릴 확률이 높고, 이로 인해 셔플이 없어지므로 스필 방지를 위해서는 언제나 옳다.

	* 설정된 리소스 (엑셀 105 ~ 107번)
		--num-executors 48 \
		--executor-cores 2 \
		--executor-memory 1g \
		--driver-memory 2g \
		--conf spark.executor.memoryOverhead=1g \
		--conf spark.driver.memoryOverhead=1g \
		--conf spark.sql.files.maxPartitionBytes=64mb \
		--conf spark.sql.shuffle.partitions=1000 \

	* 환경 및 파라미터 값
		* SALT_RANGE = 10000
 */
