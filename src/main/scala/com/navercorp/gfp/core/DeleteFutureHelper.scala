package com.navercorp.gfp.core

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

import org.apache.logging.log4j.{LogManager, Logger}
import org.bson.conversions.Bson
import org.bson.types.ObjectId
import org.mongodb.scala.model.Filters

import com.navercorp.gfp.meta.publisher.PublisherDao

trait DeleteFutureHelper {
    private val logger: Logger = LogManager.getLogger(this.getClass)

    private val baseDao = new BaseDao()
    private val publisherDao = new PublisherDao()

    /**
     * 몽고디비 샤드키의 일부인 publisher_id + date 로 기존 데이터 삭제
     * 프로세스 개수만큼 매체 삭제 병렬처리
     * ExecutorContext는 import scala.concurrent.ExecutionContext.Implicits.global 사용
     *
     * 참고
     *      - 컬렉션의 이해(collection overview): https://docs.scala-lang.org/overviews/collections/overview.html
     *      - 컬렉션 성능(collection performance): https://docs.scala-lang.org/overviews/collections/performance-characteristics.html
     *      - 병렬처리(Future): https://docs.scala-lang.org/overviews/core/futures.html
     */
    def getFuturesForDeleteByNamPublisherIdAndDate(collName: String, date: String): List[Future[Option[Boolean]]] = {
        val pub_ids: Vector[ObjectId] = publisherDao.getNamPublisher_ids
        pub_ids.map(pub_id =>
            Future {
                // MongoDB Shard Keys: https://docs.mongodb.com/v4.2/core/sharding-shard-key/#shard-key-indexes
                // MongoDB Compound Indexes > Prefixes: https://docs.mongodb.com/v4.2/core/index-compound/#prefixes
                val filter = Filters.and(
                    Filters.eq("publisher_id", pub_id),
                    Filters.eq("date", date),
                )

                try {
                    baseDao.deleteStats(collName, filter)
                    Option(true) // 기존 데이터 삭제 성공 시, true 리턴
                } catch {
                    case t: Throwable =>
                        logger.error(t.getMessage, t)
                        Option(false) // 기존 데이터 삭제 실패 시, false 리턴
                }
            }
        ).toList
    }

    /**
     * 일자(yyyyMMdd) 리스트를 기준으로 DB 데이터 삭제
     *
     * @param collName          삭제할 Collection 명
     * @param dateRange         삭제할 기간 범위 (yyyyMMdd)
     * @param additionalFilters 추가 필터
     * @return futures
     */
    def getFuturesForDeleteByDateRange(collName: String, dateRange: List[String], additionalFilters: Seq[Bson] = Seq()): List[Future[Option[Boolean]]] = {
        dateRange.map { date =>
            Future {
                logger.debug(s"삭제할 date= $date")

                val filters = Seq(
                    Filters.eq("date", date)
                ) ++ additionalFilters

                val filter: Bson = Filters.and(filters: _*)

                try {
                    val start = System.currentTimeMillis()
                    val deletedCount = baseDao.deleteStats(collName, filter)
                    val end = System.currentTimeMillis()
                    logger.debug(s"date: $date, deletedCount: $deletedCount, 소요시간: ${end - start} ms")

                    Option(true) // 기존 데이터 삭제 성공 시, true 리턴
                } catch {
                    case t: Throwable =>
                        logger.error(s"삭제 실패 :: date: $date")
                        logger.error(t.getMessage, t)
                        Option(false) // 기존 데이터 삭제 실패 시, false 리턴
                }
            }
        }
    }

    /**
     * 일자(yyyyMMdd) 리스트를 기준으로 DB 데이터 삭제
     *
     * @deprecated CMS 에서 NAM API 를 조회할 때까지만 유지
     * @param collName          삭제할 Collection 명
     * @param dateRange         삭제할 기간 범위 (yyyyMMdd)
     * @param additionalFilters 추가 필터
     * @return futures
     */
    def getFuturesForDeleteByDateRangeForCms(collName: String, dateRange: List[String], additionalFilters: Seq[Bson] = Seq()): List[Future[Option[Boolean]]] = {
        dateRange.map { date =>
            Future {
                logger.debug(s"삭제할 ymd= $date")

                val filters = Seq(
                    Filters.eq("ymd", date)
                ) ++ additionalFilters

                val filter: Bson = Filters.and(filters: _*)

                try {
                    val start = System.currentTimeMillis()
                    val deletedCount = baseDao.deleteStatsForCms(collName, filter)
                    val end = System.currentTimeMillis()
                    logger.debug(s"ymd: $date, deletedCount: $deletedCount, 소요시간: ${end - start} ms")

                    Option(true) // 기존 데이터 삭제 성공 시, true 리턴
                } catch {
                    case t: Throwable =>
                        logger.error(s"삭제 실패 :: ymd: $date")
                        logger.error(t.getMessage, t)
                        Option(false) // 기존 데이터 삭제 실패 시, false 리턴
                }
            }
        }
    }

    /**
     * GFP publisher id 및 datetime 을 기준으로 DB 데이터 삭제
     *
     * @deprecated CMS 에서 NAM API 를 조회할 때까지만 유지
     * @param collName          삭제할 Collection 명
     * @param datetime          삭제할 시간대
     * @return
     */
    def getFuturesForDeleteByGfpPublisherIdAndDatetimeForCms(collName: String, datetime: String) = {
        val pub_ids: Vector[String] = publisherDao.getGfpPublisherIds()
        pub_ids.map(pub_id =>
            Future {
                val filter = Filters.and(
                    Filters.eq("publisherId", pub_id),
                    Filters.eq("ymdhh", datetime),
                )

                try {
                    baseDao.deleteStatsForCms(collName, filter)
                    Option(true) // 기존 데이터 삭제 성공 시, true 리턴
                } catch {
                    case t: Throwable =>
                        logger.error(t.getMessage, t)
                        Option(false) // 기존 데이터 삭제 실패 시, false 리턴
                }
            }
        ).toList
    }

    /**
     * GFP publisher id 및 date 을 기준으로 DB 데이터 삭제
     *
     * @deprecated CMS 에서 NAM API 를 조회할 때까지만 유지
     * @param collName 삭제할 Collection 명
     * @param date     삭제할 시간대
     * @return
     */
    def getFuturesForDeleteByGfpPublisherIdAndDateForCms(collName: String, date: String) = {
        val pub_ids: Vector[String] = publisherDao.getGfpPublisherIds()
        pub_ids.map(pub_id =>
            Future {
                val filter = Filters.and(
                    Filters.eq("publisher_id", new ObjectId(pub_id)),
                    Filters.eq("ymd", date),
                )

                try {
                    baseDao.deleteStatsForCms(collName, filter)
                    Option(true) // 기존 데이터 삭제 성공 시, true 리턴
                } catch {
                    case t: Throwable =>
                        logger.error(t.getMessage, t)
                        Option(false) // 기존 데이터 삭제 실패 시, false 리턴
                }
            }
        ).toList
    }


	/**
	 * 연월(yyyyMM)을 기준으로 DB 데이터 삭제
	 *
	 * @param collName          삭제할 Collection 명
	 * @param month             삭제할 연월 (yyyyMM)
	 * @param additionalFilters 추가 필터
	 * @return futures
	 */
	def getFuturesForDeleteByMonth(collName: String, month: String, additionalFilters: Seq[Bson] = Seq()): List[Future[Option[Boolean]]] = {
		List(Future {
			logger.debug(s"삭제할 month= $month")

			val filters = Seq(
				Filters.eq("month", month)
			) ++ additionalFilters

			val filter: Bson = Filters.and(filters: _*)

			try {
				val start = System.currentTimeMillis()
				val deletedCount = baseDao.deleteStats(collName, filter)
				val end = System.currentTimeMillis()
				logger.debug(s"month: $month, deletedCount: $deletedCount, 소요시간: ${end - start} ms")

				Option(true) // 기존 데이터 삭제 성공 시, true 리턴
			} catch {
				case t: Throwable =>
					logger.error(s"삭제 실패 :: month: $month")
					logger.error(t.getMessage, t)
					Option(false) // 기존 데이터 삭제 실패 시, false 리턴
			}
		})
	}

}
