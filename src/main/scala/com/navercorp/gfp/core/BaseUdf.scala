package com.navercorp.gfp.core

import java.time.format.DateTimeFormatter
import java.time.{Instant, LocalDateTime, ZoneId}

import com.mongodb.spark.sql.fieldTypes.ObjectId
import org.apache.spark.sql.functions.udf
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, DateTimeZone}

object BaseUdf {
	val toObjectId = udf[ObjectId, String] { id => {
		val res = Option(id) match {
			case Some(value) if org.bson.types.ObjectId.isValid(value) => ObjectId.apply(new org.bson.types.ObjectId(value))
			case _ => null
		}
		res
	}
	}

	val toObjectIds = udf[Seq[ObjectId], Seq[String]] { ids => {
		val res: Seq[ObjectId] = ids.map { id =>
			Option(id) match {
				case Some(value) if org.bson.types.ObjectId.isValid(value) => ObjectId.apply(new org.bson.types.ObjectId(value))
				case _ => null
			}
		}
		res
	}
	}

	val oidsToString = udf[Seq[String], Seq[ObjectId]] { ids => {
		val res: Seq[String] = ids.map { id =>
			Option(id) match {
				case Some(_) => id.oid
				case _ => null
			}
		}
		res
	}
	}

	// BigDecimal => String ( stripTrailingZeros 적용 )
	//  - 40217.000000000000000000 -> 40217
	//  - 2.913200000000000000 -> 2.9132
	//  - 20.000000000000000000 -> 20
	val decimalToString = udf[String, BigDecimal]((bigDecimal: BigDecimal) => {
		bigDecimal.underlying.stripTrailingZeros.toPlainString
	})

	// eventTime 으로부터 AP 기준의 date 구하기
	val getDate = udf[String, Long, String]((eventTime: Long, tz: String) => {
		// eventTime = 1683697437
		// gfpDateTime(Asia/Seoul) = 2023-05-10T14:43:57
		// apDateTime(America/Los_Angeles) = 2023-05-09T22:43:57
		// date = 20230509

		val timezone = Option(tz).getOrElse("Asia/Seoul")
		val apDateTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(eventTime), ZoneId.of(timezone))
		val date = apDateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"))

		date
	})

	/**
	 * GFP eventTime/requestTime을 AP 타임존이 적용된 현지 시간(yyyyMMddHH)으로 변경
	 * client 로그의 eventTime의 경우 1000을 곱해서 timestamp로 전달해야 함
	 *
	 * 예)
	 * server's requestTime = "1707861161234"
	 * client's eventTime   = "1707861161"
	 *
	 * AP의 타임존이 "America/Los_Angeles"일 때
	 *
	 *
	 * logType=er-ssp-server
	 * timestampL=1707861161234
	 * seoul=2024.02.14 06:52:41:234 +0900 2024021406 seconds=1707861161 millis=1707861161234
	 * zoned=2024.02.13 13:52:41:234 -0800 2024021313 seconds=1707861161 millis=1707861161234
	 * zonedYmdh=202402131352 or 2024021313 or 20240213 or 202402 or 2024
	 *
	 * logType=er-ssp-client
	 * timestampL=1707861161234
	 * seoul=2024.02.14 06:52:41:000 +0900 2024021406 seconds=1707861161 millis=1707861161000
	 * zoned=2024.02.13 13:52:41:000 -0800 2024021313 seconds=1707861161 millis=1707861161000
	 * zonedYmdh=202402131352 or 2024021313 or 20240213 or 202402 or 2024
	 */
	val getDateTime = udf((timestamp: Long, tz: String, pattern: String) => {
		val zdt = new DateTime(timestamp, DateTimeZone.forID(tz))
		val zonedYmdh = DateTimeFormat.forPattern(pattern).print(zdt)
		//		println(s"zoned=$zonedYmdh timestamp=$timestamp seconds=${zdt.getMillis / 1000} millis=${zdt.getMillis}")

		zonedYmdh
	})

	/**
	 * 리턴되는 데이터 타입 decimal(38, 6)
	 */
	val getEstimatedRevenue = udf((bidPrice: Float) => {

		// 로그 내의 bidPrice 가 표현 범위를 넘는 경우 음수로 들어오는데, 이 경우 0 으로 처리
		val nonNegativeBidPrice = if (bidPrice < 0) 0 else bidPrice

		// float 타입의 bidPrice를 decimal(38, 6)로 치환 => cpm을 cpi로 표현하기 위해 1000으로 나누고 => 소수점 7째 자리에서 반올림하여 6째 자리까지 표현
		(BigDecimal("" + nonNegativeBidPrice).setScale(6) / BigDecimal("1000").setScale(6)).setScale(6, BigDecimal.RoundingMode.HALF_UP)
	})
}
