package com.navercorp.gfp.core.nclavis

import com.typesafe.config.Config
import org.apache.http.client.methods.{CloseableHttpResponse, HttpPost}
import org.apache.http.entity.StringEntity
import org.apache.http.util.EntityUtils
import org.apache.logging.log4j.LogManager

import com.navercorp.gfp.core.conf.Conf
import com.navercorp.gfp.util.{JsonUtil, SimpleHttpClient, SimpleHttpClientStatus}

/*
nClavis v3 가이드
    https://yobi.navercorp.com/nClavis_guide

nClavis 2.0 에서 3.0으로 전환하기
    https://yobi.navercorp.com/nClavis_guide/posts/26#yb-header-nclavis-2-0-%EC%97%90%EC%84%9C-3-0%EC%9C%BC%EB%A1%9C-%EC%A0%84%ED%99%98%ED%95%98%EA%B8%B0
        3. 여러분의 기밀정보를 nClavis 3.0 API를 사용해 암호화합니다.
            기밀정보가 개인정보 암호화키(DEK, 대칭키)이면, wrap API를 사용합니다.
            기밀정보가 DEK 이외의 정보이면, encrypt API를 사용합니다.
        4. 암호화한 결과를 서비스 인스턴스가 접근할 수 있는 곳에 저장합니다. #
        5. 서비스 인스턴스가 기밀정보가 필요할 때, 복호화할 수 있도록 nClavis 3.0 API를 이용해 서비스 로직을 개발합니다. #
            기밀정보가 개인정보 암호화키(DEK, 대칭키)이면, unwrap API를 사용합니다.
            기밀정보가 DEK 이외의 정보이면, decrypt API를 사용합니다.

nClavis 3.0에서 DB연결정보는 어떻게 관리하나요? (feat. 2.0 User FAQ)
    https://yobi.navercorp.com/nClavis_guide/posts/25#yb-header-nclavis-3-0%EC%97%90%EC%84%9C-db%EC%97%B0%EA%B2%B0%EC%A0%95%EB%B3%B4%EB%8A%94-%EC%96%B4%EB%96%BB%EA%B2%8C-%EA%B4%80%EB%A6%AC%ED%95%98%EB%82%98%EC%9A%94-feat-2-0-user-faq-
 */
object NClavis {
    private val logger = LogManager.getLogger(this.getClass)

    val conf: Config = Conf.getConf()

    def decryptInlineText(inlineCipherText: String): Option[String] = {
        var result: Option[String] = None

        SimpleHttpClient().withHttpClient { httpClient =>
            // "https://dev-apis.nclavis.navercorp.com/kms/consumer/jF7QhngykYISI1fkV30aLao0JNk=/decrypt/inline"
            val url = s"${conf.getString("nclavis.url")}/${conf.getString("nclavis.keyResourceId")}/decrypt/inline"
            val body = JsonUtil.toJson(Map("inlineCipherText" -> inlineCipherText))
            logger.debug(s"[NCLAVIS_REQ]: url=$url")
            logger.debug(s"[NCLAVIS_REQ]: body=$body")
            logger.debug(s"[NCLAVIS_REQ]: phase=${conf.getString("profile")}")

            val httpPost = new HttpPost(url)
            val requestEntity = new StringEntity(body, "utf-8")
            httpPost.setEntity(requestEntity)
            httpPost.addHeader("Accept", "application/json;charset=UTF-8")
            httpPost.addHeader("Content-Type", "application/json")

            // Token-Based 인증 설정 https://yobi.navercorp.com/nClavis_guide/posts/4#yb-header--token-based-%EC%9D%B8%EC%A6%9D-%EC%84%A4%EC%A0%95
            // NEOID USER TOKEN은 유효기간이 있으므로 만료되면 다시 발급 받아야 함.
            // local 환경에서만 access token 사용, 그 외 환경에서는 IP GROUP HOST 또는 IP GROUP ACG로 관리할 것임
//            if (conf.getString("profile") == "local" || conf.getString("profile") == "test") {
                httpPost.addHeader("nclavis-access-token", conf.getString("nclavis.accessToken"))
//            }

            val httpResponse: CloseableHttpResponse = httpClient.execute(httpPost)
            if (httpResponse != null) {
                val statusCode = httpResponse.getStatusLine.getStatusCode
                logger.debug(s"[NCLAVIS_REQ] status=$statusCode")

                if (statusCode == 200) {
                    // {"plainText":"*************************************************************************************************************"}
                    val json = EntityUtils.toString(httpResponse.getEntity, "UTF-8")
                    val resBody = JsonUtil.toMap(json)
                    result = Option(resBody("plainText").toString)
                    logger.debug(s"[NCLAVIS_REQ] json=$json")
                }

                httpClient.close()
                statusCode
            } else {
                logger.debug(s"[NCLAVIS_REQ] ERROR_RES_IS_NULL]")
                SimpleHttpClientStatus.STATUS_ERROR
            }
        }

        result
    }
}
