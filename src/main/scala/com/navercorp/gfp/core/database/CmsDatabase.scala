package com.navercorp.gfp.core.database

import com.mongodb.client.{MongoClient, MongoClients, MongoDatabase}
import com.typesafe.config.Config
import org.apache.logging.log4j.LogManager

import com.navercorp.gfp.core.conf.Conf
import com.navercorp.gfp.core.nclavis.NClavis
import com.navercorp.gfp.util.NeloUtil

object CmsDatabase {
	private val logger = LogManager.getLogger(this.getClass)

	/*
	▶ POJO codec을 등록하는 간단한 방법
		https://mongodb.github.io/mongo-java-driver/4.2/driver/getting-started/quick-start-pojo/

		val pojoCodecRegistry: CodecRegistry = fromRegistries(MongoClientSettings.getDefaultCodecRegistry(),
			fromProviders(PojoCodecProvider.builder().automatic(true).build()))

		val mongoClient: MongoClient = MongoClients.create(conf.getString("mongodb.cms.uri"))
		val database: MongoDatabase = mongoClient.getDatabase(conf.getString("mongodb.cms.name")).withCodecRegistry(pojoCodecRegistry)

	▶ codec을 못 찾는 에러 발생
		org.bson.codecs.configuration.CodecConfigurationException: Can't find a codec for class com.navercorp.gfp.model.meta.Publisher.

		codec을 java driver로 등록하면 찾을 수 없다고 나온다.
		scala driver를 이용해 scala 버전으로 등록해야 한다.

	▶ scala driver를 이용한 버전
		val codecRegistry = fromRegistries(CodecRegistries.fromProviders(
				Macros.createCodecProvider[Publisher](),
				Macros.createCodecProvider[AdUnit](),
				Macros.createCodecProvider[Sylph](),
			), DEFAULT_CODEC_REGISTRY)
		val mongoClient: MongoClient = MongoClients.create(conf.getString("mongodb.cms.uri"))
		val database: MongoDatabase = mongoClient.getDatabase(conf.getString("mongodb.cms.name"))
			.withCodecRegistry(codecRegistry)
	*/

	val conf: Config = Conf.getConf()
	//
	//	val res = NClavis.decryptInlineText(conf.getString("mongodb.cms.encryptedUri"))
	//	if (res.isEmpty) {
	//		logger.error("fail to initialize cms database. decrypted uri is empty")
	//		NeloUtil.waitFor()
	//		System.exit(1)
	//	}

	//	private val decryptedDatabaseUri = res.get
	println(s"................................. profile = ${conf.getString("profile")}")
	val test = "************************************************************************************************************"
	val real = "**************************************************************************************************************************************************"
	val decryptedDatabaseUri = if (conf.getString("profile") == "local" || conf.getString("profile") == "dev" || conf.getString("profile") == "test") test else real

	val mongoClient: MongoClient = MongoClients.create(decryptedDatabaseUri)
	val database: MongoDatabase = mongoClient.getDatabase(conf.getString("mongodb.cms.name"))
		.withCodecRegistry(CodecRegistry.codecRegistry)

	def getDatabase(): MongoDatabase = {
		database
	}

	def getDecryptedDatabaseUri: String = {
		decryptedDatabaseUri
	}
}
