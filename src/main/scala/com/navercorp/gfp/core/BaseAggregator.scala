package com.navercorp.gfp.core

import com.mongodb.client.result.InsertOneResult
import com.typesafe.config.Config
import org.apache.hadoop.fs.{FileSystem, Path}
import org.apache.logging.log4j.{LogManager, Logger}
import org.apache.spark.sql.functions.{input_file_name, regexp_extract}
import org.apache.spark.sql.types.StructType
import org.apache.spark.sql.{Dataset, Row, SparkSession}
import org.bson.types.ObjectId
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, DateTimeZone, Period}

import com.navercorp.gfp.core.BaseEnv.HADOOP_NAME_SERVICE
import com.navercorp.gfp.core.conf.Conf
import com.navercorp.gfp.core.summaryhistory.SummaryHistory
import com.navercorp.gfp.meta.adprovider.AdProviderDao
import com.navercorp.gfp.meta.publisher.PublisherDao
import com.navercorp.gfp.util.HdfsUtil

trait BaseAggregator {
	protected val logger: Logger = LogManager.getLogger(this.getClass)
	val conf: Config = Conf.getConf()
	val PROFILE: String = conf.getString("profile")
	implicit lazy val spark: SparkSession = createSparkSession()
	lazy val hdfs: FileSystem = initHadoopFileSystem()

	val baseDao = new BaseDao()
	val publisherDao = new PublisherDao()
	lazy val adProviderDao = new AdProviderDao()

	var summaryHistoryId: Option[String] = None

	/**
	 * args 셋팅
	 *  - args[0] 이 ObjectId(24 hex string) 에 해당 하는 경우
	 *      - args[0] 을 summaryHistoryId로 셋팅
	 *      - args[1:N] 을 반환함
	 *  - [주의사항] 수동으로 처리할 때, args[0] 은 ObjectId(24 hex string) 가 아니어야 정상적으로 동작한다.
	 *      - args[0] 은 반드시 날짜 형식일 것
	 *
	 * @return
	 */
	def initArgs(args: Array[String]): Array[String] = {
		var refined = args.clone()

		// summaryHistoryId 설정하고 해당 매개변수 제거
		if (ObjectId.isValid(refined.head)) {
			summaryHistoryId = Option(refined.head)
			logger.debug(s"-------------------- summaryHistoryId = $summaryHistoryId")
			refined = refined.slice(1, refined.length)
		}

		refined
	}

	/** 스파크 세션 생성 */
	def createSparkSession(): SparkSession = {
		val sparkSession = SparkSession.builder.getOrCreate()

		val hadoopConf = sparkSession.sparkContext.hadoopConfiguration
		hadoopConf.set("fs.defaultFS", HADOOP_NAME_SERVICE)
		hadoopConf.setBoolean("fs.hdfs.impl.disable.cache", true)

		sparkSession
	}

	/**
	 * hdfs 초기화
	 *
	 * @return FileSystem
	 */
	def initHadoopFileSystem(): FileSystem = {
		val hadoopConf = spark.sparkContext.hadoopConfiguration
		// hadoopConf.set("fs.defaultFS", HADOOP_NAME_SERVICE)
		// hadoopConf.setBoolean("fs.hdfs.impl.disable.cache", true)

		try {
			FileSystem.get(hadoopConf)
		} catch {
			case ex: Exception =>
				logger.error(ex.getMessage, ex)
				throw ex;
		}
	}

	/**
	 * 시간 범위 구하기
	 * - 참고 : https://www.ostack.cn/qa/?qa=818257/
	 *
	 * @param start DateTime
	 * @param end   DateTime (Include)
	 * @param step  Period ( ex > Period.hours(1))
	 * @return Iterator[DateTime]
	 */
	def dateTimeRange(start: DateTime, end: DateTime, step: Period): Iterator[DateTime] = Iterator.iterate(start)(_.plus(step)).takeWhile(!_.isAfter(end))

	/**
	 * targetDate 에 해당하는 DateTime 리스트 가져오기 (timezone 적용)
	 *
	 * @param targetDate
	 * @param step
	 * @param timezone
	 * @return List[DateTime]
	 */
	def getDateTimeRange(targetDate: String, step: Period, timezone: DateTimeZone): List[DateTime] = {
		this.getDateTimeRange(targetDate, targetDate, step, timezone)
	}

	/**
	 * startDate ~ endDate 에 해당하는 DateTime 리스트 가져오기 (timezone 적용)
	 *
	 * @param startDate
	 * @param endDate
	 * @param step
	 * @param timezone
	 * @return List[DateTime]
	 */
	def getDateTimeRange(startDate: String, endDate: String, step: Period, timezone: DateTimeZone = DateTimeZone.forID("Asia/Seoul")): List[DateTime] = {
		// 타임존 정보
		val fromTimezone = timezone
		val toTimezone = DateTimeZone.forID("Asia/Seoul")

		// 날짜 포맷
		val tFormat = DateTimeFormat.forPattern("yyyyMMdd HH")

		// AP 타임존 시간 datetime
		val tStartDt = tFormat.parseDateTime(s"$startDate 00").withZoneRetainFields(fromTimezone)
		val tEndDt = tFormat.parseDateTime(s"$endDate 23").withZoneRetainFields(fromTimezone)

		logger.debug(s"-------------------- ${timezone.getID} tStartDt ${tStartDt.toString("yyyyMMdd HH")}")
		logger.debug(s"-------------------- ${timezone.getID} tEndDt ${tEndDt.toString("yyyyMMdd HH")}")

		// 한국시간 datetime
		val kStartDt = new DateTime(tStartDt, toTimezone)
		val kEndDt = new DateTime(tEndDt, toTimezone)

		logger.debug(s"-------------------- Asia/Seoul startDt ${kStartDt.toString("yyyyMMdd HH")}")
		logger.debug(s"-------------------- Asia/Seoul endDt ${kEndDt.toString("yyyyMMdd HH")}")

		dateTimeRange(kStartDt, kEndDt, step).toList
	}

	/**
	 * 로그 로딩
	 *
	 * @param paths
	 * @return
	 */
	def loadLog(paths: Seq[String]): Dataset[Row] = {
		var fineDf: Dataset[Row] = null
		try {
			//            logger.debug(s"---------------------------------------- paths to load: $paths")
			fineDf = spark.read
				.format("parquet")
				.option("recursiveFileLookup", "true")
				.load(paths: _*)
		} catch {
			case ex: Exception =>
				logger.error(ex.getMessage, ex)
				if (ex.getMessage != null && ex.getMessage.indexOf("Path does not exist") > -1) {
					logger.error("Exception occured with path=[" + paths + "], exception=[" + ex.getMessage + "]", ex)
					fineDf = spark.emptyDataFrame
				}
				else throw ex
		}

		fineDf
	}

	/**
	 * 실버 로그 로딩
	 *
	 * 경로에서 publisherId 추출하여 컬럼으로 추가
	 *
	 * @param paths
	 * @return
	 */
	def loadSilverLog(paths: Seq[String]): Dataset[Row] = {
		var fineDf: Dataset[Row] = null
		try {
			//            logger.debug(s"---------------------------------------- paths to load: $paths")
			val df = spark.read
				.format("parquet")
				.option("recursiveFileLookup", "true")
				.load(paths: _*)
			fineDf = df.withColumn("publisherId", regexp_extract(input_file_name(), "=(\\w*)/", 1))
		} catch {
			case ex: Exception =>
				logger.error(ex.getMessage, ex)
				if (ex.getMessage != null && ex.getMessage.indexOf("Path does not exist") > -1) {
					logger.error("Exception occured with path=[" + paths + "], exception=[" + ex.getMessage + "]", ex)
					fineDf = spark.emptyDataFrame
				}
				else throw ex
		}

		fineDf
	}

	/**
	 * 로그 로딩
	 *
	 * @param paths hdfs 파일 경로 리스트
	 * @return Dataset[Row]
	 */
	def loadParquetLog(paths: Seq[String]): Option[Dataset[Row]] = {
		var resultDf: Option[Dataset[Row]] = None
		try {
			logger.debug(s"input paths: $paths")

			resultDf = Some(
				spark.read
					.format("parquet")
					.option("recursiveFileLookup", "true")
					.load(paths: _*)
			)
		} catch {
			case ex: Exception =>
				logger.error(ex.getMessage, ex)

				// Option(null) 은 None 임
				Option(ex.getMessage) match {
					case Some(msg) if msg.indexOf("Path does not exist") > -1 =>
						logger.error("Exception occured with paths=[" + paths + "], exception=[" + msg + "]", ex)
					case _ => throw ex
				}
		}
		resultDf
	}

	def loadParquetLog(paths: Seq[String], schema: StructType): Option[Dataset[Row]] = {
		var resultDf: Option[Dataset[Row]] = None
		try {
			logger.debug(s"input paths: $paths")

			resultDf = Some(
				spark.read
					.format("parquet")
					.schema(schema)
					.option("recursiveFileLookup", "true")
					.load(paths: _*)
			)
		} catch {
			case ex: Exception =>
				logger.error(ex.getMessage, ex)

				// Option(null) 은 None 임
				Option(ex.getMessage) match {
					case Some(msg) if msg.indexOf("Path does not exist") > -1 =>
						logger.error("Exception occured with paths=[" + paths + "], exception=[" + msg + "]", ex)
					case _ => throw ex
				}
		}
		resultDf
	}

	/**
	 * csv 파일 로딩
	 *
	 * @param paths hdfs 파일 경로 리스트
	 * @return Dataset[Row]
	 */
	def loadCsvFile(paths: Seq[String], header: Boolean = true): Option[Dataset[Row]] = {
		var resultDf: Option[Dataset[Row]] = None
		try {
			logger.debug(s"input paths: $paths")

			resultDf = Some(
				spark.read
					.format("csv")
					.option("recursiveFileLookup", "true")
					.option("header", if (header) "true" else "false")
					.load(paths: _*)
			)
		} catch {
			case ex: Exception =>
				logger.error(ex.getMessage, ex)

				// Option(null) 은 None 임
				Option(ex.getMessage) match {
					case Some(msg) if msg.indexOf("Path does not exist") > -1 =>
						logger.error("Exception occured with paths=[" + paths + "], exception=[" + msg + "]", ex)
					case _ => throw ex
				}
		}
		resultDf
	}

	/**
	 * 이력 쌓기
	 *
	 * @param hist
	 */
	def upsertSummaryHistory(hist: SummaryHistory): Unit = {
		summaryHistoryId match {
			case Some(_) =>
				// summaryHistoryId가 있으면 update
				baseDao.updateSummaryHistoryByProduct(summaryHistoryId.get, hist)
			case None =>
				// summaryHistoryId가 없으면 insert
				val insertResult: InsertOneResult = baseDao.insertSummaryHistoryByProduct(hist)
				summaryHistoryId = Option(insertResult.getInsertedId) match {
					case Some(value) => Option(value.asObjectId().getValue.toHexString)
					case _ => None
				}
		}
	}

	/**
	 * ymd에 해당하는 각 timezone의 일시를 모두 포함하는 목록 조회
	 * 예로 특정 timezone에서의 20240215가 한국시간으로 몇시부터 몇시까지인지를 알아내고
	 * 가장 빠른 시간부터 가장 늦은 시간까지의 시간 단위 일시 목록을 반환
	 * AP 타임존에 따른 실버 로그 로딩 경로를 알아내는데 사용됨
	 *
	 * @param ymd
	 * @param timezones
	 * @return
	 */
	def getYmdhList(ymd: String, timezones: Vector[String], pattern: String = "yyyyMMdd-HH"): Vector[String] = {
		var ymdhList = Vector[String]()

		val tFormat = DateTimeFormat.forPattern("yyyyMMddHH")

		// 각 timezone에 맞춘 yyyyMMdd-HH를 모두 포함하는 한국 시간 기준 [가장 빠른 시간 ~ 가장 늦은 시간] 으로 확장
		val seoulTimezone = DateTimeZone.forID("Asia/Seoul")
		var currentDt = tFormat.parseDateTime("2900123123").withZoneRetainFields(seoulTimezone)
		var endDt = tFormat.parseDateTime("1900010100").withZoneRetainFields(seoulTimezone)

		for (timezone <- timezones) {
			// timezone의 ymd가 한국시간으로 언제부터 언제까지인지
			val (startDtInSeoul, endDtInSeoul) = getStartEndDateTimeInSeoul(ymd, DateTimeZone.forID(timezone))
			logger.debug(s"${timezone}의 ${ymd}는 한국 시간으로 $startDtInSeoul ~ $endDtInSeoul")

			// 기존 비교군보다 시작시간이 빠르다면 비교군의 범위를 앞으로 확장
			if (startDtInSeoul.isBefore(currentDt)) {
				currentDt = startDtInSeoul
			}
			// 기존 비교군보다 끝시간이 느리다면 비교군의 범위를 뒤로 확장
			if (endDtInSeoul.isAfter(endDt)) {
				endDt = endDtInSeoul
			}
		}

		logger.debug(s"로드할 실버 시간대 ${currentDt.toString(pattern)} ~ ${endDt.toString(pattern)}")
		while (currentDt.isBefore(endDt) || currentDt.isEqual(endDt)) {
			ymdhList = ymdhList :+ currentDt.toString(pattern) // "20210106-23" or "2021/01/06/23" 등
			currentDt = currentDt.plusHours(1)
		}

		ymdhList
	}

	/**
	 * timezone의 ymd가 한국시간으로 언제부터 언제까지인지
	 *
	 * @param ymd
	 * @param timezone
	 * @return
	 */
	private def getStartEndDateTimeInSeoul(ymd: String, timezone: DateTimeZone = DateTimeZone.forID("Asia/Seoul")): (DateTime, DateTime) = {
		// 타임존 정보
		val fromTimezone = timezone
		val toTimezone = DateTimeZone.forID("Asia/Seoul")

		// 날짜 포맷
		val pattern = "yyyyMMdd-HH"
		val tFormat = DateTimeFormat.forPattern(pattern)

		// AP 타임존 시간 datetime
		val tStartDt = tFormat.parseDateTime(s"$ymd-00").withZoneRetainFields(fromTimezone)
		val tEndDt = tFormat.parseDateTime(s"$ymd-23").withZoneRetainFields(fromTimezone)

		//		    logger.debug(s"$LOG_PREFIX -------------------- ${timezone.getID} startDt ${tStartDt.toString(pattern)}")
		//		    logger.debug(s"$LOG_PREFIX -------------------- ${timezone.getID} endDt   ${tEndDt.toString(pattern)}")

		// 한국시간 datetime
		val kStartDt = new DateTime(tStartDt, toTimezone)
		val kEndDt = new DateTime(tEndDt, toTimezone)

		//		    logger.debug(s"$LOG_PREFIX -------------------- Asia/Seoul          startDt ${kStartDt.toString(pattern)}")
		//		    logger.debug(s"$LOG_PREFIX -------------------- Asia/Seoul          endDt   ${kEndDt.toString(pattern)}")

		(kStartDt, kEndDt)
	}

	def rename(src: String, dest: String): Unit = {
		// src  = "hdfs://pg07/user/gfp-data/zircon/b/compaction/2024/05/30/11/{logType}/_adProviderId={adProviderId}/_publisherId={publisherId}"
		// dest = "hdfs://pg07/user/gfp-data/zircon/b/warehouse/2024/05/30/11/{logType}/_adProviderId={adProviderId}/_publisherId={publisherId}"

		if (HdfsUtil.exists(hdfs, src)) {
			// 새로 쓰기를 위해 dest가 이미 존재하면 지우고
			if (HdfsUtil.exists(hdfs, dest)) {
				val delResult = HdfsUtil.delete(hdfs, dest) // 원래 디렉토리 지우기
				if (!delResult) {
					throw new Exception(s"dest 경로 삭제 실패. $dest")
				}
			}

			// dest가 위치할 부모 경로가 없으면 생성
			val destParentPath = new Path(dest).getParent
			if (!HdfsUtil.exists(hdfs, destParentPath.toString)) hdfs.mkdirs(destParentPath)

			// 리네임
			val renameResult = HdfsUtil.rename(hdfs, src, dest)
			if (renameResult) {
				logger.debug(s"rename 성공.\n\tsrc  = $src\n\tdest = $dest")
			} else {
				// destPath가 존재하지 않는 경우 false로 떨어지기도 함
				throw new Exception(s"rename 실패.\n\tsrc  = $src\n\tdest = $dest")
			}
		} else {
			throw new Exception(s"rename 실패. src 경로 존재하지 않음. $src")
		}
	}
}
