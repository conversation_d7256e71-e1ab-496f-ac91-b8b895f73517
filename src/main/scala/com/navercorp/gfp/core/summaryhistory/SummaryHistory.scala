package com.navercorp.gfp.core.summaryhistory

import java.util.Date

import org.bson.types.ObjectId

case class SummaryHistory(
							  _id: Option[ObjectId] = None, // insert 시에는 _id가 없으므로 Option 처리
							  datetime: Option[String] = None, // yyyymmddhh, yyyymmdd, yyyymm, yyyy
							  aggregatorName: Option[String] = None,
							  comment: Option[String] = None,
							  spark: Option[Spark] = None,
							  detail: Option[Any] = None,
							  createdAt: Option[Date] = None, // insert 시에만 필요하고 update 시에는 필요 없으므로 Option 처리
							  modifiedAt: Date = new Date()
							  // dag, kube는 기술하지 않음. 나중에 필요 시 기술.
						  ) {}

case class Spark(sparkAppId: Option[String] = None,
				 sparkAppName: Option[String] = None,
				 sparkSubmittedAt: Option[Date] = None,
				 sparkSubmitInfo: Option[String] = None,
				 sparkAppState: Option[String] = None,
				 trackingUrl: Option[String] = None,
				 sparkAppError: Option[String] = None,
				 sparkStartedAt: Option[Date] = None,
				 sparkEndedAt: Option[Date] = None
				)

case class SummaryHistoryDetail(publisherId: Option[String] = None,
								adProviderId: Option[String] = None,
								publisherIds: Option[Seq[String]] = None,
								adProviderIds: Option[Seq[String]] = None,
								rkUse: Option[Long] = None,
								aggCount: Option[Long] = None,
								startDate: Option[String] = None,
								endDate: Option[String] = None,
								filePaths: Option[Seq[String]] = None,
								reportId: Option[String] = None,
							   )
