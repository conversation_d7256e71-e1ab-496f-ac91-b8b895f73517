package com.navercorp.gfp.core

import com.mongodb.spark.config.ReadConfig
import com.typesafe.config.Config

import com.navercorp.gfp.core.conf.Conf
import com.navercorp.gfp.core.database.{CmsDatabase, Database}

object BaseEnv {
	val conf: Config = Conf.getConf()

	val HADOOP_NAME_SERVICE: String = conf.getString("hadoop.nameservice.bizcloud.host")

	// BRONZE
	val BRONZE_ROOT: String = conf.getString("hadoop.nameservice.bizcloud.host") + conf.getString("hadoop.nameservice.bizcloud.bronze.path")
	val BRONZE_SERVER_INPUT_PATH: String = BRONZE_ROOT + conf.getString("hadoop.nameservice.bizcloud.bronze.server.path")
	val BRONZE_CLIENT_INPUT_PATH: String = BRONZE_ROOT + conf.getString("hadoop.nameservice.bizcloud.bronze.client.path")

	// SILVER
	val SILVER_ROOT: String = conf.getString("hadoop.nameservice.bizcloud.host") + conf.getString("hadoop.nameservice.bizcloud.silver.path")
	val SILVER_SERVER_INPUT_PATH: String = SILVER_ROOT + conf.getString("hadoop.nameservice.bizcloud.silver.server.path")
	val SILVER_CLIENT_INPUT_PATH: String = SILVER_ROOT + conf.getString("hadoop.nameservice.bizcloud.silver.client.path")

	// ZIRCON
	val ZIRCON_ROOT: String = conf.getString("hadoop.nameservice.bizcloud.host") + conf.getString("hadoop.nameservice.bizcloud.zircon.path")

	// ZIRCON B
	val ZIRCON_B_ROOT: String = ZIRCON_ROOT + conf.getString("hadoop.nameservice.bizcloud.zircon.b.path")
	val ZIRCON_B_INTERMEDIATE_PATH: String = ZIRCON_B_ROOT + conf.getString("hadoop.nameservice.bizcloud.zircon.b.intermediate.path")
	val ZIRCON_B_COMPACTION_PATH: String = ZIRCON_B_ROOT + conf.getString("hadoop.nameservice.bizcloud.zircon.b.compaction.path")
	val ZIRCON_B_WAREHOUSE_PATH: String = ZIRCON_B_ROOT + conf.getString("hadoop.nameservice.bizcloud.zircon.b.warehouse.path")

	// ZIRCON B GFP
	val ZIRCON_B_GFP_PATH: String = ZIRCON_B_ROOT + conf.getString("hadoop.nameservice.bizcloud.zircon.b.gfp.path")
	val ZIRCON_B_GFP_INTERMEDIATE_PATH: String = ZIRCON_B_GFP_PATH + conf.getString("hadoop.nameservice.bizcloud.zircon.b.gfp.intermediate.path")
	val ZIRCON_B_GFP_COMPACTION_PATH: String = ZIRCON_B_GFP_PATH + conf.getString("hadoop.nameservice.bizcloud.zircon.b.gfp.compaction.path")
	val ZIRCON_B_GFP_WAREHOUSE_PATH: String = ZIRCON_B_GFP_PATH + conf.getString("hadoop.nameservice.bizcloud.zircon.b.gfp.warehouse.path")

	// ZIRCON R GFP
	val ZIRCON_R_ROOT: String = ZIRCON_ROOT + conf.getString("hadoop.nameservice.bizcloud.zircon.r.path")
	val ZIRCON_R_GFP_PATH: String = ZIRCON_R_ROOT + conf.getString("hadoop.nameservice.bizcloud.zircon.r.gfp.path")
	val ZIRCON_R_GFP_INTERMEDIATE_PATH: String = ZIRCON_R_GFP_PATH + conf.getString("hadoop.nameservice.bizcloud.zircon.r.gfp.intermediate.path")
	val ZIRCON_R_GFP_COMPACTION_PATH: String = ZIRCON_R_GFP_PATH + conf.getString("hadoop.nameservice.bizcloud.zircon.r.gfp.compaction.path")
	val ZIRCON_R_GFP_WAREHOUSE_PATH: String = ZIRCON_R_GFP_PATH + conf.getString("hadoop.nameservice.bizcloud.zircon.r.gfp.warehouse.path")

	// GOLD
	val GOLD_ROOT: String = conf.getString("hadoop.nameservice.bizcloud.host") + conf.getString("hadoop.nameservice.bizcloud.gold.path")
	val GOLD_ADUNIT: String = GOLD_ROOT + conf.getString("hadoop.nameservice.bizcloud.gold.adunit.path")
	val GOLD_ADPROVIDER_PATH: String = GOLD_ROOT + conf.getString("hadoop.nameservice.bizcloud.gold.adprovider.path")

	// MONTHLY
	val MONTHLY_ROOT: String = conf.getString("hadoop.nameservice.bizcloud.host") + conf.getString("hadoop.nameservice.bizcloud.monthly.path")

	// SILVERGREY - HDFS ADPROVIDER RK, NONRK
	val SILVERGREY_ROOT: String = HADOOP_NAME_SERVICE + conf.getString("hadoop.nameservice.bizcloud.silvergrey.path")
	val SILVERGREY_RK_PATH: String = SILVERGREY_ROOT + conf.getString("hadoop.nameservice.bizcloud.silvergrey.rk.path")
	val SILVERGREY_NONRK_PATH: String = SILVERGREY_ROOT + conf.getString("hadoop.nameservice.bizcloud.silvergrey.nonrk.path")

	// AMBER
	val AMBER_ROOT: String = HADOOP_NAME_SERVICE + conf.getString("hadoop.nameservice.bizcloud.amber.path")
	val AMBER_AP_PATH: String = AMBER_ROOT + conf.getString("hadoop.nameservice.bizcloud.amber.ap.path")
	val AMBER_GFP_PATH: String = AMBER_ROOT + conf.getString("hadoop.nameservice.bizcloud.amber.gfp.path")

	// ABUSE - HDFS ADPROVIDER ABUSE
	val HDFS_ADPROVIDER_ABUSE_ROOT: String = HADOOP_NAME_SERVICE + conf.getString("hadoop.nameservice.bizcloud.adprovider_abuse.path")

	// MongoDB Input Configuration Option : https://docs.mongodb.com/spark-connector/current/configuration/#input-configuration
	val mdbDefaultReadConfig = ReadConfig(Map(
		"spark.mongodb.input.uri" -> Database.getDecryptedDatabaseUri,
		"spark.mongodb.input.database" -> Database.getDatabase().getName,
		"spark.mongodb.input.collection" -> "SomeCollection",
		"spark.mongodb.input.readPreference.name" -> "secondaryPreferred"))

	// MongoDB Output Configuration Option : https://docs.mongodb.com/spark-connector/current/configuration/#output-configuration
	//  - "spark.mongodb.output.shardKey" -> "{publisher_id:1, date:1, adUnitId:1}"
	val mdbDefaultWriteOptions = Map(
		"spark.mongodb.output.uri" -> Database.getDecryptedDatabaseUri,
		"spark.mongodb.output.database" -> Database.getDatabase().getName,
		"spark.mongodb.output.collection" -> "SomeCollection",
		"spark.mongodb.output.writeConcern.w" -> "majority")

	// CMS MongoDB 접속 정보 - 추후 제거 예정
	val cmsMdbDefaultReadConfig = ReadConfig(Map(
		"spark.mongodb.input.uri" -> CmsDatabase.getDecryptedDatabaseUri,
		"spark.mongodb.input.database" -> CmsDatabase.getDatabase().getName,
		"spark.mongodb.input.collection" -> "SomeCollection",
		"spark.mongodb.input.readPreference.name" -> "secondaryPreferred"))

	val cmsMdbDefaultWriteOptions = Map(
		"spark.mongodb.output.uri" -> CmsDatabase.getDecryptedDatabaseUri,
		"spark.mongodb.output.database" -> CmsDatabase.getDatabase().getName,
		"spark.mongodb.output.collection" -> "SomeCollection",
		"spark.mongodb.output.writeConcern.w" -> "majority")
}
