package com.navercorp.gfp.core

import org.apache.spark.sql.types.{DataTypes, DecimalType}

object BaseConstant {
	// 로그 내 bidPrice 관련 필드에 공통으로 사용될 데이터 타입 (소수점 7자리 에서 반올림하여 6자리까지 표현, 정수부는 9자리 즉 9억까지)
	val DECIMAL_TYPE_15_6: DecimalType = DataTypes.createDecimalType(15, 6)

	// 수익 등에 공통으로 사용될 데이터 타입 (소수점 7자리 에서 반올림하여 6자리까지 표현, 정수부는 11자리 즉 999억까지)
	// 로그 내 bidPrice 의 합은 decimal(15 ,6) 을 넘어설 수 있으므로 이를 사용
	val DECIMAL_TYPE_17_6: DecimalType = DataTypes.createDecimalType(17, 6)

	val DEVICE_OS =
		"""NVL(CASE
		  |     WHEN UPPER(os) == 'ANDROID' THEN 'ANDROID'
		  |     WHEN UPPER(os) == 'IPHONEOS' THEN 'IOS'
		  |     WHEN UPPER(os) == 'MAC' OR UPPER(os) == 'MACOS' THEN 'MACOS'
		  |     WHEN UPPER(os) LIKE 'WIN%' THEN 'WINDOWS'
		  |     WHEN UPPER(os) == 'LINUX' OR UPPER(os) == 'UNIX' THEN 'LINUX'
		  |     ELSE 'OTHERS'
		  | END, '-') AS deviceOs
		  |""".stripMargin
}
