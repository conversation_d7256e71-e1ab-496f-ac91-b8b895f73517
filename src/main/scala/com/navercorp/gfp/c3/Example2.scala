/**
 * Apache Commons Lang JAVA Doc
 * https://commons.apache.org/proper/commons-lang/apidocs/index.html
 * <p>
 * 웍스 알림 연동: https://wiki.navercorp.com/pages/viewpage.action?pageId=525441819
 */
package com.navercorp.gfp.c3

import com.mongodb.spark.config.ReadConfig
import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import com.navercorp.gfp.core.conf.Conf
import com.navercorp.gfp.core.database.{Database, DatabaseInitializer}
import com.navercorp.gfp.core.{BaseDao, BaseEnv}
import com.typesafe.config.Config
import org.apache.spark.sql.SparkSession


object Example2 extends DatabaseInitializer {
	val baseDao = new BaseDao()

	def main(rawArgs: Array[String]): Unit = {
		val conf: Config = Conf.getConf()
		val namespace = conf.getString("hadoop.nameservice.bizcloud.host")
		println(s"........................................namespace=${namespace}")

		val spark = SparkSession.builder
			.getOrCreate()

		import spark.implicits._

		rawArgs.foreach(a => println(s"rawArg = $a"))
		initDB(rawArgs)

		// ----------- 1. MongoDB > AdProviderRevenueDaily_202308 확인
		val readConfig = ReadConfig(Map(
			"spark.mongodb.input.uri" -> Database.getDecryptedDatabaseUri,
			"spark.mongodb.input.database" -> Database.getDatabase().getName,
			"spark.mongodb.input.collection" -> "AdProviderRevenueDaily",
			"spark.mongodb.input.readPreference.name" -> "secondaryPreferred",
			"spark.mongodb.input.batchSize" -> "2000",
			"spark.mongodb.input.partitionerOptions.shardKey" -> "{ adProvider_id:1, date:1, publisher_id:1, dealYn:1 }"))

		val df = spark.read
			.mongo(readConfig)
			.filter("date == '20230817'")
		//			.select(
		//				$"publisher_id.oid".as("publisherId"),
		//				$"adProvider_id.oid".as("adProviderId"),
		//				$"revenueKRW",
		//				$"revenueUSD",
		//			)
		println(s"................................. MongoDB > AdProviderRevenueDaily 확인")
		df.show(false)


		// ----------- 2. test c3 hdfs 로부터 df 생성
		val path = "hdfs://bizcloud/user/gfp-data/silver/er-ssp-client/20230818-12/publisherId=5b74d94bc36eef272090ca56"
		val df2 = spark.read
			.format("parquet")
			.option("recursiveFileLookup", "true")
			.load(path)

		println(s"................................. c3 hdfs path 확인:$path")
		df2.show(false)


		val writeOptions = BaseEnv.mdbDefaultWriteOptions
		println(s"......................... writeOptions=$writeOptions")
	}
}
