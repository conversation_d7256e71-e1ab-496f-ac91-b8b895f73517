package com.navercorp.gfp.util

import scala.collection.mutable.ListBuffer

import org.apache.hadoop.fs._

object HdfsUtil {
	/**
	 * hdfs 에 path 에 해당하는 파일이 있는지 확인
	 *
	 * @return Boolean
	 */
	def exists(hdfs: FileSystem, path: String): Boolean = {
		Option(hdfs.exists(new Path(path))) match {
			case Some(true) => true
			case _ => false
		}
	}

	/**
	 * hdfs 에 pathPattern(*) 에 해당하는 파일이 있는지 확인
	 *
	 * @return Boolean
	 */
	def existsPathPattern(hdfs: FileSystem, pathPattern: String): Boolean = {
		hdfs.globStatus(new Path(pathPattern)).length > 0
	}

	def create(hdfs: FileSystem, s: String): Boolean = {
		hdfs.createNewFile(new org.apache.hadoop.fs.Path(s))
	}

	def create(hdfs: FileSystem, s: String, overwrite: Boolean): FSDataOutputStream = {
		hdfs.create(new org.apache.hadoop.fs.Path(s), overwrite)
	}

	def delete(hdfs: FileSystem, path: String): Boolean = {
		val movedToTrash = if (!exists(hdfs, path))
			true
		else
			Trash.moveToAppropriateTrash(hdfs, new org.apache.hadoop.fs.Path(path), hdfs.getConf())

		/*if (movedToTrash) {
			println(s"File $path moved to trash successfully.")
		} else {
			println(s"File $path is already in trash or trash is disabled.")
		}*/

		movedToTrash
	}

	def move(hdfs: FileSystem, from: String, dest: String): Boolean = {
		this.delete(hdfs, dest)
		hdfs.rename(new org.apache.hadoop.fs.Path(from), new org.apache.hadoop.fs.Path(dest))
	}

	def rename(hdfs: FileSystem, from: String, dest: String): Boolean = {
		hdfs.rename(new org.apache.hadoop.fs.Path(from), new org.apache.hadoop.fs.Path(dest))
	}

	def copy(hdfs: FileSystem, srcDir: String, dstDir: String): Boolean = {
		// 이미 있으면 지우고
		if (hdfs.exists(new Path(dstDir))) {
			this.delete(hdfs, dstDir)
		}

		FileUtil.copy(
			hdfs, new Path(srcDir),
			hdfs, new Path(dstDir),
			false,
			hdfs.getConf
		)
	}

	def list(hdfs: FileSystem, dir: String, absolute: Boolean): List[String] = {
		var items = new ListBuffer[String]()
		if (hdfs.exists(new org.apache.hadoop.fs.Path(dir))) {
			val list = hdfs.listStatus(new org.apache.hadoop.fs.Path(dir))
			list.foreach(l => {
				if (absolute) {
					items += l.getPath.toString
				} else {
					items += l.getPath.getName
				}

			})
		}
		items.toList
	}
}
