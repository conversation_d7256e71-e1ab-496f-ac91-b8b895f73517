package com.navercorp.gfp.util

import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.{Column, functions}

object TimeUtil {
	def measureTime(f: => Unit) = {
		val start = System.currentTimeMillis()
		f
		val end = System.currentTimeMillis()
		println("Evaluation took " + (end - start) + " milliseconds")
	}

	/**
	 * TTL Index(1년 보관)를 위한 필드. 13달 후 1일 0시로 설정됨
	 * 예)
	 * date=20220118 -> 2023.02.01 0시
	 * date=20220131 -> 2023.02.01 0시
	 * date=20240229 -> 2025.03.01 0시
	 *
	 * db.AdUnitPerformanceDaily.createIndex({"expiredAt": 1}, {expireAfterSeconds:0})
	 * expiredAt 필드 기준으로 해당 시점과 현재 시점이 같을 경우 삭제
	 *
	 * @param date
	 * @return
	 */
	def getExpiredAtAsColumn(date: String): Column = {
		val expiredAt = functions.trunc(
			functions.add_months(
				functions.from_unixtime(
					functions.unix_timestamp(lit(date), "yyyyMMdd")
				), 13
			), "month")
		expiredAt
	}

	def getExpiredAtMonthlyAsColumn(date: Column, months: Int = 13): Column = {
		functions.trunc(
			functions.add_months(
				functions.from_unixtime(
					functions.unix_timestamp(date, "yyyyMMdd")
				), months
			), "month"
		)
	}

	def getExpiredAtDailyAsColumn(date: Column, days: Int = 8): Column = {
		functions.date_add(
			functions.from_unixtime(
				functions.unix_timestamp(date, "yyyyMMdd")
			), days
		)
	}

	def getSplitYmd(date: String): SplitYmd = {
		if (date.length >= 8) {
			val yyyy = date.substring(0, 4)
			val mm = date.substring(4, 6)
			val dd = date.substring(6, 8)

			SplitYmd(yyyy, mm, dd)
		} else {
			throw new Exception("'date' format is invalid. Format must be 'yyyyMMdd'")
		}
	}

	def getSplitYmdh(datetime: String): SplitYmdh = {
		if (datetime.length >= 10) {
			val yyyy = datetime.substring(0, 4)
			val mm = datetime.substring(4, 6)
			val dd = datetime.substring(6, 8)
			val hh = datetime.substring(8, 10)

			SplitYmdh(yyyy, mm, dd, hh)
		} else {
			throw new Exception("'datetime' format is invalid. Format must be 'yyyyMMddHH'")
		}
	}

	def getSplitDateTime(date: String): SplitYmdh = {
		if (date.length < 4) throw new Exception(s"'date'($date) format is invalid.")

		val yyyy = date.substring(0, 4)
		val mm = if (date.length >= 6) date.substring(4, 6) else "01"
		val dd = if (date.length >= 8) date.substring(6, 8) else "01"
		val hh = if (date.length >= 10) date.substring(8, 10) else "00"

		SplitYmdh(yyyy, mm, dd, hh)
	}

	case class SplitYmd(yyyy: String, mm: String, dd: String)

	case class SplitYmdh(yyyy: String, mm: String, dd: String, hh: String)
}
