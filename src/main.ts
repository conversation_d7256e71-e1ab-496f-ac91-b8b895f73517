import { NestFactory } from '@nestjs/core';
import { VersioningType } from '@nestjs/common';
import { ValidationPipe } from "@nestjs/common";
import { AppModule } from './app.module';
import { Logger } from 'nestjs-pino';

async function bootstrap() {
	const app = await NestFactory.create(AppModule, {bufferLogs: true});

	app.enableShutdownHooks();

	app.useLogger(app.get(Logger));
	app.enableVersioning({
		type: VersioningType.URI,
		defaultVersion: '1',
	});
	app.useGlobalPipes(new ValidationPipe());

	await app.listen(process.env.NAM_API_PORT ?? 3002);
}

bootstrap();
