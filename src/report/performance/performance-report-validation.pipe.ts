import { ArgumentMetadata, BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';

import _ from 'lodash';

import { extractErrorMessages } from '../../error/error-util';

import { PerformanceReportParams, PerformanceReportFilters } from './performance-report-dto';


@Injectable()
export class PerformanceReportValidationPipe implements PipeTransform<PerformanceReportParams> {
	constructor() { }

	async transform(value: PerformanceReportParams, { metatype }: ArgumentMetadata) {
		// PerformanceReportParams, Object 타입만 허용함
		if (!metatype || !_.includes([PerformanceReportParams, Object] , metatype)) {
			return value;
		}

		// 1. Plain object 를 Class instance 로 변환
		// 	- performanceReportFilters 에 "excludeExtraneousValues: true" 를 적용하여 불필요한 요소들을 제거 한다.
		// 	  Filters 하위 프로퍼티에 @Expose() 를 적용해주어야 한다.
		const performanceReportParams: PerformanceReportParams = plainToInstance(PerformanceReportParams, value);
		performanceReportParams.filters = plainToInstance(PerformanceReportFilters, performanceReportParams.filters, { excludeExtraneousValues: true });

		// 2. 유효성 검사
		const errors = await validate(performanceReportParams);

		if (errors.length > 0) {
			// 에러 메시지를 BadRequestException 으로 던짐
			throw new BadRequestException(extractErrorMessages(errors));
		}

		return performanceReportParams;
	}
}
