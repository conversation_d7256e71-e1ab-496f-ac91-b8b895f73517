import { Inject, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';

import _ from 'lodash';
import moment from 'moment';
import { Model, Types } from 'mongoose';

import { Client } from '../../nubes/clients';
import { NUBES_CLIENT_NAM_API } from '../../nubes/constants';

import { ReportState } from '../../enum/report.enum';

import { CustomService } from '../custom/custom.service';
import { CustomReportType, MethodType } from '../custom/custom.enums';

import { PerformanceFilters, PerformanceParams } from '../custom/performance-dto';

import { PerformanceReportFilters, PerformanceReportParams } from './performance-report-dto';

import { Environment, EnvironmentDocument } from '../schema/environment.schema';
import { ZirconTrace, ZirconTraceDocument } from '../schema/zircon-trace.schema';
import { SyncPublisher, SyncPublisherDocument } from '../schema/sync-publisher.schema';
import { SyncPublisherService, SyncPublisherServiceDocument } from '../schema/sync-publisher-service.schema';


@Injectable()
export class PerformanceReportService {
	private readonly logger = new Logger(PerformanceReportService.name);

	constructor(
		@InjectModel(ZirconTrace.name)
		private zirconTraceModel: Model<ZirconTraceDocument>,

		@InjectModel(SyncPublisher.name)
		private syncPublisherModel: Model<SyncPublisherDocument>,

		@InjectModel(SyncPublisherService.name)
		private syncPublisherServiceModel: Model<SyncPublisherServiceDocument>,

		@InjectModel(Environment.name)
		private environmentModel: Model<EnvironmentDocument>,

		@Inject(NUBES_CLIENT_NAM_API)
		private nubesClient: Client,

		private customService: CustomService,
	) { }

	/**
	 * 매체 성과 리포트 생성 요청
	 *
	 * @param userId
	 * @param publisherCd
	 * @param params { startDate, endDate, dimensions, metrics, filters }
	 */
	async create(userId: string, publisherCd: string, params: PerformanceReportParams) {
		const { startDate, endDate, dimensions, metrics, filters } = params;

		const syncPub = await this.syncPublisherModel.findOne({ publisherCd });

		const customParams = plainToInstance(PerformanceParams, {
			publisherId: syncPub._id.toString(),
			type: CustomReportType.PERF,
			timezone: '-', startDate, endDate,
			dimensions, metrics,
		}, { groups: [MethodType.CREATE] });
		customParams.filters = await this.transformPerformanceFilters(syncPub._id, filters);

		// cmsUse, publisherCd 는 @Expose 이므로 별도로 적용해준다.
		customParams.cmsUse = 0;
		customParams.publisherCd = publisherCd;

		return this.customService.createCustomReport(userId, customParams, { reqBody: params });
	}

	/**
	 * 매체 성과 리포트 상태 조회
	 *
	 * @param publisherCd
	 * @param report_id
	 */
	async status(publisherCd: string, report_id: Types.ObjectId) {
		const { publisher_id, state, taskState } = await this.getCustomReport(publisherCd, report_id);

		if (_.isEqual(ReportState.COMPLETE, state)) {
			const apRecentUpdate = await this.zirconTraceModel.aggregate([
				{ $match: {
					publisher_id,
					date: { $gte: moment().subtract(90, 'days').format('YYYYMMDD') },
					zirconBTouchedAt: { $ne: null },
				} },

				{ $project: {
					date: 1, adProvider_id: 1,
					zirconBSilvergreyAt: { $ifNull: [ { $dateToString: { date: '$zirconBSilvergreyAt', timezone: '+09:00', format: '%Y-%m-%dT%H:%M:%S%z' } }, '' ] },
					zirconBSucceededAt: { $ifNull: [ { $dateToString: { date: '$zirconBSucceededAt', timezone: '+09:00', format: '%Y-%m-%dT%H:%M:%S%z' } }, '' ] },
				} },

				{ $sort: { date: 1 } },

				{ $group: {
					_id: '$adProvider_id',
					completedSet: {
						$push: {
							$cond: {
								if: { $ne: [ '$zirconBSilvergreyAt', '' ] },
								then: { date: '$date', updatedAt: '$zirconBSucceededAt' },
								else: '$$REMOVE'
							}
						}
					},
					missingDates: { $push: { $cond: { if: { $eq: ['$zirconBSilvergreyAt', ''] }, then: '$date', else: '$$REMOVE' } } }
				} },

				{ $project: {
					_id: 0, adProvider_id: '$_id', missingDates: 1,
					recentCompletedSet: { $arrayElemAt: [ '$completedSet', -1 ] }
				} },

				{ $lookup: { from: 'SyncAdProviders', foreignField: '_id', localField: 'adProvider_id', as: 'ap' } },

				{ $unwind: { path: '$ap', preserveNullAndEmptyArrays: false } },

				{ $project: { name: '$ap.name', recentDate: '$recentCompletedSet.date', updatedAt: '$recentCompletedSet.updatedAt', missingDates: 1 } },

				{ $sort: { name: 1 } }
			]);

			return { status: state, apRecentUpdate };
		} else if (_.isEqual(ReportState.FAILURE, state)) {
			const errorMessage = _.includes(taskState, ReportState.FAILURE) ? taskState : ReportState.FAILURE;

			return { status: state, errorMessage };
		} else {
			// READY, IN_PROGRESS 인 경우
			return { status: state };
		}
	}

	/**
	 * 매체 성과 리포트 다운로드
	 *
	 * @param publisherCd
	 * @param report_id
	 */
	async download(publisherCd: string, report_id: Types.ObjectId) {
		const { filePath } = await this.getCustomReport(publisherCd, report_id);

		return this.nubesClient.download(filePath);
	}

	async transformPerformanceFilters(publisher_id: Types.ObjectId, filters: PerformanceReportFilters): Promise<PerformanceFilters> {
		const filtersPlainObj = {};

		for (const key in filters) {
			if (!_.isNil(filters[key])) {
				if (key === 'service') {
					const syncPubSvc = await this.syncPublisherServiceModel.find({ publisher_id, name: { $in: filters[key] } }, { name: 1 });

					// svcNameIdMap = { serviceName : serviceId, ... }
					const svcNameIdMap = {};
					syncPubSvc.map(svc => {
						svcNameIdMap[svc.name] = svc._id.toString();
					});

					// service 는 이름으로 들어오기 때문에, serviceId 로 변경
					// 서비스 이름은 중복 생성 불가하며 필수 입력 값이다.
					filtersPlainObj['serviceId'] = { in: filters[key].map(svcName => svcNameIdMap[svcName]) };
				} else {
					filtersPlainObj[key] = { in: filters[key] };
				}
			}
		}

		return plainToInstance(PerformanceFilters, filtersPlainObj, { groups: [MethodType.CREATE] });
	}

	async getRecentReportsByUserId(userId: string) {
		// 최근 24시간 이내 요청 온 리포트 조회
		return this.customService.getCustomReports({ userId, createdAt: { $gte: moment().subtract(1, 'days') } });
	}

	async getCustomReport(publisherCd: string, report_id: Types.ObjectId) {
		return this.customService.getCustomReport({ publisherCd, _id: report_id });
	}

	async checkServiceExists(publisherCd: string, serviceFilter: string[]) {
		const syncPub = await this.syncPublisherModel.findOne({ publisherCd: publisherCd }, { _id: 1 });
		const syncPubSvc = await this.syncPublisherServiceModel.find({ publisher_id: syncPub._id }, { name: 1 });

		// ex> [ '기본서비스', 'test', 'PC지면' ]
		const allServiceNames = _.map(syncPubSvc, 'name');

		const notExists = serviceFilter.filter(svc => !_.includes(allServiceNames, svc));

		return notExists.length === 0;
	}

	async getPublisher(publisherCd: string) {
		return this.syncPublisherModel.findOne({ publisherCd }, { cmsType: 1 });
	}

	/**
	 * 매체 성과 리포트 최대 개수 조회 ( Environments = performance-report-max-count )
	 */
	async getMaxCount() {
		// value = { maxCount: 3, maxFailureCount: 10 }
		const env = await this.environmentModel.findOne({ name: 'performance-report-max-count' }, { value: 1 });

		return !_.isEmpty(env) && !_.isEmpty(env.value) ? env.value : { maxCount: 3, maxFailureCount: 10 };
	}

	/**
	 * 매체 성과 리포트 최대 파일 크기 조회 ( Environments = performance-report-max-file-size )
	 */
	async getMaxFileSize() {
		// value = 200
		const env = await this.environmentModel.findOne({ name: 'performance-report-max-file-size' }, { value: 1 });

		// 200 MB
		return !_.isEmpty(env) && _.isNumber(env.value) ? env.value : 200;
	}

	async checkNubesFilePathExists(filePath: string) {
		try {
			await this.nubesClient.stat(filePath);

			return true;
		} catch (err) {
			return false;
		}
	}
}
