import { Body, Controller, Get, Logger, Param, Post, Query, Res, StreamableFile, UseFilters, UseGuards, UsePipes, ValidationPipe } from '@nestjs/common';

import _ from 'lodash';
import path from 'path';
import { Types } from 'mongoose';
import { Response } from 'express';

import { UserId } from '../../decorator/user.decorator';
import { AuthorizationGuard } from '../../guard/authorization.guard';
import { HttpExceptionFilter } from '../../exception/http-exception.filter';

import { ObjectIdPipe } from '../../pipe/object-id.pipe';
import { PublisherCdPipe } from '../../pipe/publisher-cd.pipe';

import { PerformanceReportService } from './performance-report.service';
import { PerformanceReportValidator } from './performance-report.validator';
import { PerformanceReportValidationPipe } from './performance-report-validation.pipe';


/**
 * ValidationPipe Option
 * 	- whitelist: DTO 에 없은 속성은 무조건 거른다.
 * 	- forbidNonWhitelisted: 전달하는 요청 값 중에 정의 되지 않은 값이 있으면 Error 를 발생시킨다.
 * 	- transform: 네트워크를 통해 들어오는 데이터는 일반 JavaScript 객체이다. 객체를 자동으로 DTO 로 변환을 원하면 transform 값을 true 로 설정한다.
 * 	- disableErrorMessages: Error 가 발생 했을 때 Error Message 표시 여부 설정 (true: 표시하지 않음, false: 표시함)
 */

@Controller('performance-report/')
@UseGuards(AuthorizationGuard)
@UseFilters(HttpExceptionFilter)
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class PerformanceReportController {
	private readonly logger = new Logger(PerformanceReportController.name);

	constructor(private readonly performanceReportService: PerformanceReportService) { }

	/**
	 * 매체 성과 리포트 생성 요청
	 *
	 * @param userId
	 * @param publisherCd
	 * @param params { startDate, endDate, dimensions?, metrics, filters?: { service?, channelType?, creativeType?, productType?, responseCreativeType?, deviceOs?, country? } }
	 */
	@Post(':publisherCd')
	async create(@UserId() userId: string,
				 @Param('publisherCd', PublisherCdPipe) publisherCd: string,
				 @Body(new PerformanceReportValidationPipe()) params: any) {
		this.logger.debug(`PerformanceReportController.create() 호출 (userId= ${userId}, publisherCd= ${publisherCd})`);
		this.logger.debug(`params = ${JSON.stringify(params)}`);

		// [Validation 1] 날짜
		PerformanceReportValidator.validateDate(params);

		// [Validation 2] 서비스 명
		if (!_.isEmpty(params.filters) && !_.isEmpty(params.filters.service)) {
			const exist = await this.performanceReportService.checkServiceExists(publisherCd, params.filters.service);
			PerformanceReportValidator.validateServices(exist);
		}

		// [Validation 3] 추정 지표
		const publisher = await this.performanceReportService.getPublisher(publisherCd);
		PerformanceReportValidator.validateEstimatedMetrics(params, publisher);

		// [Validation 4] 광고유닛 요청수 지표
		PerformanceReportValidator.validateAdUnitRequests(params);

		// [Validation 5] 사용자별 요청 가능한 리포트 개수를 초과하였는지 체크
		const reports = await this.performanceReportService.getRecentReportsByUserId(userId);
		const maxCount = await this.performanceReportService.getMaxCount();
		PerformanceReportValidator.validateMaxCount(reports, maxCount);


		return { reportId: await this.performanceReportService.create(userId, publisherCd, params) };
	}

	/**
	 * 매체 성과 리포트 상태 조회
	 *
	 * @param userId
	 * @param publisherCd
	 * @param report_id
	 */
	@Get('status/:publisherCd')
	async status(@UserId() userId: string,
				 @Param('publisherCd', PublisherCdPipe) publisherCd: string,
				 @Query('reportId', ObjectIdPipe) report_id: Types.ObjectId) {
		this.logger.debug(`PerformanceReportController.status() 호출 (userId= ${userId}, publisherCd= ${publisherCd})`);
		this.logger.debug(`report_id = ${report_id}`);

		const report = await this.performanceReportService.getCustomReport(publisherCd, report_id);

		// [Validation 1] publisherCd, reportId 에 해당 하는 리포트가 있는지
		PerformanceReportValidator.validateReport(report);


		return await this.performanceReportService.status(publisherCd, report_id);
	}

	/**
	 * 매체 성과 리포트 다운로드
	 *
	 * @param userId
	 * @param publisherCd
	 * @param report_id
	 * @param res
	 */
	@Get('download/:publisherCd')
	// @Header('Content-Type', 'text/csv')
	// @Header('Content-Disposition', 'attachment')
	async download(@UserId() userId: string,
				   @Param('publisherCd', PublisherCdPipe) publisherCd: string,
				   @Query('reportId', ObjectIdPipe) report_id: Types.ObjectId,
				   @Res({ passthrough: true }) res: Response) {
		this.logger.debug(`PerformanceReportController.download() 호출 (userId= ${userId}, publisherCd= ${publisherCd})`);
		this.logger.debug(`report_id = ${report_id}`);

		const report = await this.performanceReportService.getCustomReport(publisherCd, report_id);

		// [Validation 1] publisherCd, reportId 에 해당 하는 리포트가 있는지
		PerformanceReportValidator.validateReport(report);

		// [Validation 2] 리포트가 생성 완료 되었는지
		PerformanceReportValidator.validateReportState(report);

		// [Validation 3] 리포트가 Nubes 에 존재하는지
		const exist = await this.performanceReportService.checkNubesFilePathExists(report.filePath);
		PerformanceReportValidator.validateReportFileExist(exist);

		// [Validation 4] 리포트가 아직 만료(24시간) 전인지
		PerformanceReportValidator.validateReportNotExpired(report);

		// [Validation 5] 리포트 파일 크기가 200 MB 기준을 초과 하였는지
		const maxFileSize = await this.performanceReportService.getMaxFileSize();
		PerformanceReportValidator.validateMaxFileSize(report, maxFileSize);


		const stream = await this.performanceReportService.download(publisherCd, report_id);

		res.set({
			'Content-Type': 'text/csv',
			'Content-Disposition': `attachment; filename="${path.basename(report.filePath)}"`,
		});

		return new StreamableFile(stream);
	}
}
