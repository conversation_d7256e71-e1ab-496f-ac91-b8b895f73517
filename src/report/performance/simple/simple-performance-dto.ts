import { IsDate<PERSON>tring, IsO<PERSON>, <PERSON><PERSON><PERSON>al, IsISO31661<PERSON><PERSON><PERSON>2, IsArray, ValidateNested, IsMongoId, IsNotEmpty, IsIn, Length } from 'class-validator';
import { Exclude, Expose, Transform, Type } from 'class-transformer';

import _ from 'lodash';
import { Types } from 'mongoose';

import { IsTimezone } from '../../../validator/is-timezone.validator';
import { ArrayDistinct } from '../../../validator/array-distinct.validator';
import { IsInCommonCode } from '../../../validator/is-in-common-code.validator';

import { toArrayUppercase } from '../../../util/util';

import { toStringReplacedErrorCode } from '../../../error/error-util';
import { CommonReportErrorCode } from '../../../error/error-code';

import { REPORT_PERIOD, ReportPeriods } from '../../../enum/report.enum';

import { MethodTypeObj } from '../performance-report.enums';

const allGroups = [MethodTypeObj.AP, MethodTypeObj.AU, MethodTypeObj.AU_CHART];
const auGroups = [MethodTypeObj.AU, MethodTypeObj.AU_CHART];


export class SimplePerformanceFilters {
	@Expose()
	@IsMongoId({ each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: allGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: allGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: allGroups })
	@IsOptional({ groups: allGroups })
	adProviderIds: string[];

	@Expose()
	@Transform(({ value }) => toArrayUppercase(value))
	@IsISO31661Alpha2({ each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: allGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: allGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: allGroups })
	@IsOptional({ groups: allGroups })
	countries: string[];

	@Expose({ groups: auGroups })
	@IsMongoId({ each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: auGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: auGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: auGroups })
	@IsOptional({ groups: auGroups })
	serviceIds: string[];

	@Expose({ groups: auGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: auGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: auGroups })
	@IsOptional({ groups: auGroups })
	adUnitIds: string[];

	@Expose({ groups: auGroups })
	@Transform(({ value }) => toArrayUppercase(value))
	@IsInCommonCode('channelType', { each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: auGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: auGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: auGroups })
	@IsOptional({ groups: auGroups })
	placeChannelTypes: string[];

	@Expose({ groups: auGroups })
	@Transform(({ value }) => toArrayUppercase(value))
	@IsInCommonCode('productType', { each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: auGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: auGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: auGroups })
	@IsOptional({ groups: auGroups })
	placeProductTypes: string[];
}

export class SimplePerformanceParams {
	@IsMongoId({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: allGroups })
	publisherId: string;

	@IsDateString( { strict: false }, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FORMAT_DATE), groups: allGroups })
	@Length(8, 8, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FORMAT_DATE), groups: allGroups })
	startDate: string;

	@IsDateString( { strict: false }, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FORMAT_DATE), groups: allGroups })
	@Length(8, 8, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FORMAT_DATE), groups: allGroups })
	endDate: string;

	@IsTimezone({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: allGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: allGroups })
	timezone: string;

	@Transform(({ value }) => !_.isEmpty(value) && _.isString(value) ? value.toUpperCase() : value)
	@IsIn(ReportPeriods, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: allGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: allGroups })
	period: REPORT_PERIOD;

	@Type(() => SimplePerformanceFilters)
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: allGroups })
	@IsObject({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: allGroups })
	@IsOptional({ groups: allGroups })
	filters: SimplePerformanceFilters = new SimplePerformanceFilters();

	@Exclude()
	mainTimezone: string;

	@Exclude()
	get publisher_id(): Types.ObjectId {
		return Types.ObjectId.isValid(this.publisherId) ? new Types.ObjectId(this.publisherId) : undefined;
	}
}
