import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import _ from 'lodash';
import moment from 'moment';
import { Model, Types } from 'mongoose';

import { FilesService } from '../../files/files.service';
import { CommonService } from '../../common/common.service';

import { AP_TIMEZONE, CmsType } from '../../../enum/report.enum';
import { SimplePerformanceParams } from './simple-performance-dto';

import { TimezoneMap, TimezoneMapDocument } from '../../schema/timezone-map.schema';
import { SyncPublisher, SyncPublisherDocument } from '../../schema/sync-publisher.schema';
import { SyncAdProviderInfo, SyncAdProviderInfoDocument } from '../../schema/sync-ad-provider-info.schema';
import { SyncPublisherService, SyncPublisherServiceDocument } from '../../schema/sync-publisher-service.schema';
import { PerformanceAdUnitDaily, PerformanceAdUnitDailyDocument } from '../../schema/performance-ad-unit-daily.schema';
import { PerformanceAdProviderDaily, PerformanceAdProviderDailyDocument } from '../../schema/performance-ad-provider-daily.schema';

type CommonLabel =
	| 'date' | 'adProviderId' | 'adProviderName' | 'timezone' | 'currency' | 'connectionType' | 'country'
	| 'impressions' | 'clicks' | 'usdNetRevenue' | 'krwNetRevenue' | 'usdRevenue' | 'krwRevenue'
	| 'adProviderRequests' | 'adProviderResponses'
	| 'sspFilledRequests' | 'sspImpressions' | 'sspViewableImpressions' | 'sspEstimatedImpressions' | 'sspClicks' | 'sspCompletions'
	| 'sspEstimatedUsdNetRevenue' | 'sspEstimatedKrwNetRevenue'
	| 'ctr' | 'usdNetCpm' | 'krwNetCpm' | 'usdNetCpc' | 'krwNetCpc'
	| 'sspCtr' | 'sspEstimatedUsdNetCpc' | 'sspEstimatedKrwNetCpc' | 'sspEstimatedUsdNetCpm' | 'sspEstimatedKrwNetCpm';

type AdProviderStatsLabel = CommonLabel;
type AdUnitStatsLabel = CommonLabel | 'apTimezone' | 'serviceId' | 'serviceName' | 'adUnitId' | 'placeChannelType' | 'placeProductType' | 'adUnitRequests';


@Injectable()
export class SimplePerformanceService {
	private readonly logger = new Logger(SimplePerformanceService.name);

	constructor(
		@InjectModel(PerformanceAdUnitDaily.name)
		private adUnitDailyModel: Model<PerformanceAdUnitDailyDocument>,

		@InjectModel(PerformanceAdProviderDaily.name)
		private adProviderDailyModel: Model<PerformanceAdProviderDailyDocument>,

		@InjectModel(TimezoneMap.name)
		private timezoneMapModel: Model<TimezoneMapDocument>,

		@InjectModel(SyncPublisher.name)
		private syncPublisherModel: Model<SyncPublisherDocument>,

		@InjectModel(SyncAdProviderInfo.name)
		private syncAdProviderInfoModel: Model<SyncAdProviderInfoDocument>,

		@InjectModel(SyncPublisherService.name)
		private syncPublisherServiceModel: Model<SyncPublisherServiceDocument>,

		private filesService: FilesService,
		private commonService: CommonService,
	) { }

	/**
	 * 광고유닛별 성과 리포트 API - 차트
	 *
	 * @param SimplePerformanceParams { publisher_id, startDate, endDate, mainTimezone, period, filters: { serviceIds, adUnitIds, adProviderIds, countries, placeChannelTypes, placeProductTypes } }
	 */
	async getAdUnitChart({ publisher_id, startDate, endDate, mainTimezone, period, filters: { serviceIds, adUnitIds, adProviderIds, countries, placeChannelTypes, placeProductTypes } }: SimplePerformanceParams) {
		const fixedTimezone = !_.isEqual(mainTimezone, AP_TIMEZONE);

		const match = { timezone: mainTimezone, date: { $gte: startDate, $lte: endDate }, publisher_id };

		if (!_.isEmpty(serviceIds)) { match['serviceId'] = { $in: serviceIds }; }
		if (!_.isEmpty(adUnitIds)) { match['adUnitId'] = { $in: adUnitIds }; }
		if (!_.isEmpty(countries)) { match['country'] = { $in: countries }; }

		// adProviderId: '' 인 경우, 광고유닛 요청수 row 에 해당됨
		const adUnitMatch = Object.assign({ adProviderId: '' }, match);

		// adProvider 필터가 없는 경우, 광고유닛 요청수 row 를 제외 하기 위해 adProviderId != '' 를 적용 한다.
		match['adProviderId'] = !_.isEmpty(adProviderIds) ? { $in: adProviderIds } : { $ne: '' };
		if (!_.isEmpty(placeChannelTypes)) { match['placeChannelType'] = { $in: placeChannelTypes }; }
		if (!_.isEmpty(placeProductTypes)) { match['placeProductType'] = { $in: placeProductTypes }; }

		// auData = [{ date, adUnitRequests }]
		// auDataMeta = { date: adUnitRequests }
		let auData = [];
		let auDataMeta = {};
		if (fixedTimezone) {
			auData = await this.adUnitDailyModel.aggregate([
				{ $match: adUnitMatch },
				{ $addFields: { date: (period === 'D') ? '$date' : { $substr: ['$date', 0, 6] } } },
				{ $group: { _id: { date: '$date' }, adUnitRequests: { $sum: '$adUnitRequests' } } },
				{ $project: { _id: 0, date: '$_id.date', adUnitRequests: 1 } },
				{ $sort: { date: 1 } }
			], { allowDiskUse: true });

			auDataMeta = auData.reduce((acc, { date, adUnitRequests }) => {
				acc[date] = adUnitRequests;
				return acc;
			}, {});
		}

		// apData = [{ date, impressions, krwNetRevenue, usdNetRevenue }]
		const apData = await this.adUnitDailyModel.aggregate([
			{ $match: match },

			{ $addFields: { date: (period === 'D') ? '$date' : { $substr: ['$date', 0, 6] } } },

			{ $group: {
				_id: { date: '$date' },
				impressions: { $sum: '$impressions' },
				krwNetRevenue: { $sum: '$krwNetRevenue' },
				usdNetRevenue: { $sum: '$usdNetRevenue' },
			}},

			{ $addFields: {
				date: '$_id.date',
				krwNetRevenue: { $toDouble: '$krwNetRevenue' },
				usdNetRevenue: { $toDouble: '$usdNetRevenue' },
			}},

			{ $project: { _id: 0 } },

			{ $sort: { date: 1 } }
		], { allowDiskUse: true });

		// au 는 있는데 ap 가 없는 경우를 찾기 위함
		const notMatchedAu = _.keys(auDataMeta);

		// data = [{ date, impressions, krwNetRevenue, usdNetRevenue, adUnitRequests }]
		const data = apData.map(ap => {
			// ap 와 매칭된 au 가 있는 경우, notMatched 에서 제거
			const index = notMatchedAu.findIndex(item => item === ap.date);
			if (index !== -1) {
				notMatchedAu.splice(index, 1);
			}

			// apTimezone 인 경우, adUnitRequests null 처리
			// apTimezone 이 아닌 경우, adUnitRequests 또는 0 처리
			const adUnitRequests = fixedTimezone ? auDataMeta[ap.date] || 0 : null;

			return { ...ap, adUnitRequests };
		});

		// 광고유닛 요청수만 존재하는 케이스
		if (!_.isEmpty(notMatchedAu)) {
			auData.map(au => {
				if (notMatchedAu.findIndex(item => item === au.date) !== -1) {
					data.push({ ...au, impressions: 0, krwNetRevenue: 0, usdNetRevenue: 0 });
				}
			});
		}

		return this.commonService.interpolateDateDimension<AdUnitStatsLabel>(
			_.orderBy(data, 'date'),
			[startDate, endDate],
			period === 'D' ? 'daily' : 'monthly',
			['date'],
			'',
			{ impressions: 0, usdNetRevenue: 0, krwNetRevenue: 0, adUnitRequests: 0 },
			'asc'
		);
	}

	/**
	 * 광고유닛별 성과 리포트 API - 화면
	 *
	 * @param SimplePerformanceParams { publisher_id, startDate, endDate, mainTimezone, filters: { serviceIds, adUnitIds, adProviderIds, countries, placeChannelTypes, placeProductTypes } }
	 */
	async getAdUnitStats({ publisher_id, startDate, endDate, mainTimezone, filters: { serviceIds, adUnitIds, adProviderIds, countries, placeChannelTypes, placeProductTypes } }: SimplePerformanceParams) {
		const fixedTimezone = !_.isEqual(mainTimezone, AP_TIMEZONE);

		const match = { date: { $gte: startDate, $lte: endDate }, publisher_id };

		if (!_.isEmpty(serviceIds)) {
			match['serviceId'] = { $in: serviceIds };
		}
		if (!_.isEmpty(adUnitIds)) {
			match['adUnitId'] = { $in: adUnitIds };
		}
		if (!_.isEmpty(countries)) {
			match['country'] = { $in: countries };
		}

		// adProviderId: '' 인 경우, 광고유닛 요청수 row 에 해당됨
		const adUnitMatch = Object.assign(fixedTimezone ? { adProviderId: '', timezone: mainTimezone } : { adProviderId: '' }, match);

		match['timezone'] = mainTimezone;
		// adProvider 필터가 없는 경우, 광고유닛 요청수 row 를 제외 하기 위해 adProviderId != '' 를 적용 한다.
		match['adProviderId'] = !_.isEmpty(adProviderIds) ? { $in: adProviderIds } : { $ne: '' };
		if (!_.isEmpty(placeChannelTypes)) {
			match['placeChannelType'] = { $in: placeChannelTypes };
		}
		if (!_.isEmpty(placeProductTypes)) {
			match['placeProductType'] = { $in: placeProductTypes };
		}


		// auData = [{ timezone, serviceId, adUnitId, adUnitRequests }]
		// 고정 타임존인 경우
		// 	auDataMeta = { serviceId_adUnitId: adUnitRequests }
		// AP 타임존인 경우
		// 	auDataMeta = { timezone_serviceId_adUnitId: adUnitRequests }
		const auData = await this.adUnitDailyModel.aggregate([
			{ $match: adUnitMatch },
			{ $group: { _id: { timezone: '$timezone', serviceId: '$serviceId', adUnitId: '$adUnitId' }, adUnitRequests: { $sum: '$adUnitRequests' } } },
			{ $project: { _id: 0, timezone: '$_id.timezone', serviceId: '$_id.serviceId', adUnitId: '$_id.adUnitId', adUnitRequests: 1 } },
			{ $sort: { timezone: 1, serviceId: 1, adUnitId: 1 } }
		], { allowDiskUse: true });
		const auDataMeta = auData.reduce((acc, { timezone, serviceId, adUnitId, adUnitRequests }) => {
			if (fixedTimezone) {
				acc[`${serviceId}_${adUnitId}`] = adUnitRequests;
			} else {
				acc[`${timezone}_${serviceId}_${adUnitId}`] = adUnitRequests;
			}

			return acc;
		}, {});

		// apData = [{
		// 		serviceId, adUnitId, adProviderId, apTimezone,
		// 		impressions, clicks, krwNetRevenue, usdNetRevenue,
		// 		sspFilledRequests, sspImpressions, sspViewableImpressions, sspEstimatedImpressions, sspClicks,
		// 		sspEstimatedKrwNetRevenue, sspEstimatedUsdNetRevenue
		// }]
		const apData = await this.adUnitDailyModel.aggregate([
			{ $match: match },

			{ $group: {
				_id: { serviceId: '$serviceId', adUnitId: '$adUnitId', adProviderId: '$adProviderId', apTimezone: '$apTimezone' },

				impressions: { $sum: '$impressions' },
				clicks: { $sum: '$clicks' },
				krwNetRevenue: { $sum: '$krwNetRevenue' },
				usdNetRevenue: { $sum: '$usdNetRevenue' },

				sspFilledRequests: { $sum: '$sspFilledRequests' },
				sspImpressions: { $sum: '$sspImpressions' },
				sspViewableImpressions: { $sum: '$sspViewableImpressions' },
				sspEstimatedImpressions: { $sum: '$sspEstimatedImpressions' },
				sspClicks: { $sum: '$sspClicks' },
				sspEstimatedKrwNetRevenue: { $sum: '$sspEstimatedKrwNetRevenue' },
				sspEstimatedUsdNetRevenue: { $sum: '$sspEstimatedUsdNetRevenue' },
			} },

			{ $addFields: {
				serviceId: '$_id.serviceId',
				adUnitId: '$_id.adUnitId',
				adProviderId: '$_id.adProviderId',
				apTimezone: '$_id.apTimezone',

				krwNetRevenue: { $toDouble: '$krwNetRevenue' },
				usdNetRevenue: { $toDouble: '$usdNetRevenue' },
				sspEstimatedKrwNetRevenue: { $toDouble: '$sspEstimatedKrwNetRevenue' },
				sspEstimatedUsdNetRevenue: { $toDouble: '$sspEstimatedUsdNetRevenue' },
			} },

			{ $project: { _id: 0 } },

			{ $sort: { serviceId: 1, adUnitId: 1, adProviderId: 1 } }
		], { allowDiskUse: true });

		// au 는 있는데 ap 가 없는 경우를 찾기 위함
		const notMatchedAu = _.keys(auDataMeta);

		// SubTotal 집계 & 광고유닛 요청수 머지 처리
		// subTotal = [{
		// 		serviceId, adUnitId,
		// 		impressions, clicks, krwNetRevenue, usdNetRevenue,
		// 		adUnitRequests, sspFilledRequests, sspImpressions, sspViewableImpressions, sspEstimatedImpressions, sspClicks,
		// 		sspEstimatedKrwNetRevenue, sspEstimatedUsdNetRevenue,
		// 		details: [ { adProviderId, metrics... } ]
		// }]
		const subTotal = apData.reduce((acc, curr) => {
			const { serviceId, adUnitId, adProviderId, apTimezone, ...rest } = curr;

			// ap 와 매칭된 au 가 있는 경우, notMatched 에서 제거
			const auKey = fixedTimezone ? `${serviceId}_${adUnitId}` : `${apTimezone}_${serviceId}_${adUnitId}`;
			const index = notMatchedAu.findIndex(item => item === auKey);
			if (index !== -1) {
				notMatchedAu.splice(index, 1);
			}

			const existing = acc.find(item => item.serviceId === serviceId && item.adUnitId === adUnitId);

			if (existing) {
				// subTotal 이 이미 있는 경우, 지표 합산 처리
				_.keys(rest).map(mtr => existing[mtr] += rest[mtr]);

				// details 에 추가
				existing.details.push({ adProviderId, adUnitRequests: auDataMeta[auKey] || 0, ...rest });
			} else {
				// subTotal 없는 경우 추가
				// subTotal 에서 adUnitRequests 는 dimension 으로 처리됨
				acc.push({
					serviceId,
					adUnitId,

					// apTimezone 인 경우, adUnitRequests null 처리
					// apTimezone 이 아닌 경우, adUnitRequests 또는 0 처리
					adUnitRequests: fixedTimezone ? auDataMeta[auKey] || 0 : null,

					// adUnitRequests 를 제외한 나머지 지표
					...rest,

					details: [{ adProviderId, adUnitRequests: auDataMeta[auKey] || 0, ...rest }]
				});
			}

			return acc;
		}, []);

		const defaultMetric  = {
			impressions: 0, clicks: 0, krwNetRevenue: 0, usdNetRevenue: 0,
			sspFilledRequests: 0, sspImpressions: 0, sspViewableImpressions: 0, sspEstimatedImpressions: 0, sspClicks: 0,
			sspEstimatedKrwNetRevenue: 0, sspEstimatedUsdNetRevenue: 0,
		};

		// 광고유닛 요청수만 존재하는 케이스
		if (fixedTimezone && !_.isEmpty(notMatchedAu)) {
			auData.map(au => {
				const { serviceId, adUnitId, timezone, adUnitRequests } = au;

				if (notMatchedAu.findIndex(item => item === `${au.serviceId}_${au.adUnitId}`) !== -1) {
					subTotal.push({ serviceId, adUnitId, adUnitRequests, ...defaultMetric, details: [] })
				}
			});
		}

		// Total 구하기
		// Total = {
		// 		impressions, clicks, krwNetRevenue, usdNetRevenue,
		// 		adUnitRequests, sspFilledRequests, sspImpressions, sspViewableImpressions, sspEstimatedImpressions, sspClicks,
		// 		sspEstimatedKrwNetRevenue, sspEstimatedUsdNetRevenue,
		// 		subTotal: [{
		// 			serviceId, adUnitId, metrics...
		// 			details: [ { adProviderId, metrics... } ]
		// 		}]
		// }
		let data: any = {};

		if (!_.isEmpty(subTotal)) {
			data = subTotal.reduce((acc, curr) => {
				if (fixedTimezone) {
					acc.adUnitRequests += curr.adUnitRequests;
				}
				_.keys(defaultMetric).map(mtr => acc[mtr] += curr[mtr]);

				return acc;
			}, { adUnitRequests: null, ...defaultMetric });

			data.subTotal = subTotal;
		}

		return data;
	}

	/**
	 * 광고유닛별 성과 리포트 API - CSV
	 *
	 * @param SimplePerformanceParams { publisher_id, startDate, endDate, mainTimezone, timezone, filters: { serviceIds, adUnitIds, adProviderIds, countries, placeChannelTypes, placeProductTypes } }
	 */
	async getAdUnitCsv({ publisher_id, startDate, endDate, mainTimezone, timezone, filters: { serviceIds, adUnitIds, adProviderIds, countries, placeChannelTypes, placeProductTypes } }: SimplePerformanceParams) {
		const fixedTimezone = !_.isEqual(mainTimezone, AP_TIMEZONE);

		const match = { date: { $gte: startDate, $lte: endDate }, publisher_id };

		if (!_.isEmpty(serviceIds)) { match['serviceId'] = { $in: serviceIds }; }
		if (!_.isEmpty(adUnitIds)) { match['adUnitId'] = { $in: adUnitIds }; }
		if (!_.isEmpty(countries)) { match['country'] = { $in: countries }; }

		// adProviderId: '' 인 경우, 광고유닛 요청수 row 에 해당됨
		// apTimezone 인 경우, 모든 고정 타임존 기준의 광고유닛 요청수를 조회함
		const adUnitMatch = Object.assign(fixedTimezone ? { adProviderId: '', timezone: mainTimezone } : { adProviderId: '' }, match);

		match['timezone'] = mainTimezone;

		// adProvider 필터가 없는 경우, 광고유닛 요청수 row 를 제외 하기 위해 adProviderId != '' 를 적용 한다.
		match['adProviderId'] = !_.isEmpty(adProviderIds) ? { $in: adProviderIds } : { $ne: '' };

		if (!_.isEmpty(placeChannelTypes)) { match['placeChannelType'] = { $in: placeChannelTypes }; }
		if (!_.isEmpty(placeProductTypes)) { match['placeProductType'] = { $in: placeProductTypes }; }


		// apDataStream = [{
		// 		serviceId, adUnitId, country, adProviderId, apTimezone, placeChannelType, placeProductType,
		// 		impressions, clicks, usdRevenue, krwRevenue, usdNetRevenue, krwNetRevenue,
		// 		adProviderRequests, adProviderResponses,
		// 		sspFilledRequests, sspImpressions, sspViewableImpressions, sspEstimatedImpressions, sspClicks, sspCompletions,
		// 		sspEstimatedUsdNetRevenue, sspEstimatedKrwNetRevenue,
		// 		ctr, usdNetCpc, krwNetCpc, usdNetCpm, krwNetCpm,
		// 		sspCtr, sspEstimatedUsdNetCpc, sspEstimatedKrwNetCpc, sspEstimatedUsdNetCpm, sspEstimatedKrwNetCpm
		// }]
		const apDataStream = this.adUnitDailyModel.aggregate([
			{ $match: match },

			{ $group: {
				_id: {
					serviceId: '$serviceId', adUnitId: '$adUnitId', country: '$country',
					adProviderId: '$adProviderId', apTimezone: '$apTimezone',
					placeChannelType: '$placeChannelType', placeProductType: '$placeProductType'
				},

				impressions: { $sum: '$impressions' },
				clicks: { $sum: '$clicks' },
				krwRevenue: { $sum: '$krwRevenue' },
				usdRevenue: { $sum: '$usdRevenue' },
				krwNetRevenue: { $sum: '$krwNetRevenue' },
				usdNetRevenue: { $sum: '$usdNetRevenue' },

				adProviderRequests: { $sum: '$adProviderRequests' },
				adProviderResponses: { $sum: '$adProviderResponses' },
				sspFilledRequests: { $sum: '$sspFilledRequests' },
				sspImpressions: { $sum: '$sspImpressions' },
				sspViewableImpressions: { $sum: '$sspViewableImpressions' },
				sspEstimatedImpressions: { $sum: '$sspEstimatedImpressions' },
				sspClicks: { $sum: '$sspClicks' },
				sspCompletions: { $sum: '$sspCompletions' },
				sspEstimatedKrwNetRevenue: { $sum: '$sspEstimatedKrwNetRevenue' },
				sspEstimatedUsdNetRevenue: { $sum: '$sspEstimatedUsdNetRevenue' },
			} },

			{ $project: {
				_id: 0,
				serviceId: '$_id.serviceId', adUnitId: '$_id.adUnitId', country: '$_id.country',
				adProviderId: '$_id.adProviderId', apTimezone: '$_id.apTimezone',
				placeChannelType: '$_id.placeChannelType',  placeProductType: '$_id.placeProductType',
				impressions: 1, clicks: 1, krwRevenue: 1, usdRevenue: 1, krwNetRevenue: 1, usdNetRevenue: 1,
				adProviderRequests: 1, adProviderResponses: 1,
				sspFilledRequests: 1, sspImpressions: 1, sspViewableImpressions: 1, sspEstimatedImpressions: 1, sspClicks: 1, sspCompletions: 1,
				sspEstimatedKrwNetRevenue: 1, sspEstimatedUsdNetRevenue: 1,
			} },

			{ $sort: { serviceId: 1, adUnitId: 1, country: 1, adProviderId: 1, placeChannelType: 1, placeProductType: 1 } }
		], { allowDiskUse: true });

		// auData = [{ timezone, serviceId, adUnitId, country, adUnitRequests }]
		// 고정 타임존인 경우
		// 	auDataMeta = { serviceId_adUnitId_country: adUnitRequests }
		// AP 타임존인 경우
		// 	auDataMeta = { timezone_serviceId_adUnitId_country: adUnitRequests }
		const auData = await this.adUnitDailyModel.aggregate([
			{ $match: adUnitMatch },
			{ $group: { _id: { timezone: '$timezone', serviceId: '$serviceId', adUnitId: '$adUnitId', country: '$country' }, adUnitRequests: { $sum: '$adUnitRequests' } } },
			{ $project: { _id: 0, timezone: '$_id.timezone', serviceId: '$_id.serviceId', adUnitId: '$_id.adUnitId', country: '$_id.country', adUnitRequests: 1 } },
			{ $sort: { timezone: 1, serviceId: 1, adUnitId: 1, country: 1 } }
		], { allowDiskUse: true });
		const auDataMeta = auData.reduce((acc, { timezone, serviceId, adUnitId, country, adUnitRequests }) => {
			if (fixedTimezone) {
				acc[`${serviceId}_${adUnitId}_${country}`] = adUnitRequests;
			} else {
				acc[`${timezone}_${serviceId}_${adUnitId}_${country}`] = adUnitRequests;
			}

			return acc;
		}, {});

		// svcMeta = { serviceId: servivceName }
		const services =  await this.syncPublisherServiceModel.find({ publisher_id }, { name: 1 });
		const svcMeta = services.reduce((acc, { _id, name }) => {
			acc[_id.toString()] = name;
			return acc;
		}, {});

		// apMeta = { adProviderId: { adProviderName, connectionType, currency, timezone } }
		const apCond = !_.isEmpty(adProviderIds) ? { adProvider_id: { $in: adProviderIds.map(id => new Types.ObjectId(id)) } }  : { };
		const adProviders = await this.syncAdProviderInfoModel.aggregate([
			{ $match: { publisher_id, ...apCond } },
			{ $lookup: { from: 'SyncAdProviders', foreignField: '_id', localField: 'adProvider_id', as: 'ap' } },
			{ $unwind: { path: '$ap', preserveNullAndEmptyArrays: false } },
			{ $project: { _id: '$ap._id', name: '$ap.name', timezone: '$ap.timezone', currency: '$ap.currency', connectionType: '$ap.connectionType' } },
		]);
		const apMeta = adProviders.reduce((acc, { _id, name, connectionType, currency, timezone }) => {
			acc[_id.toString()] = { adProviderName: name, connectionType, currency, timezone };
			return acc;
		}, {});


		const transformers = [];

		// 계산식 추가 스트림
		transformers.push(
			this.commonService.transformForAddCalculatedMetrics<AdUnitStatsLabel>([
				{ type: 'ctr', calculatedMetric: 'ctr', meta: { numerator: 'clicks', denominator: 'impressions' } },
				{ type: 'cpm', calculatedMetric: 'krwNetCpm', meta: { numerator: 'krwNetRevenue', denominator: 'impressions' } },
				{ type: 'cpm', calculatedMetric: 'usdNetCpm', meta: { numerator: 'usdNetRevenue', denominator: 'impressions' } },
				{ type: 'cpc', calculatedMetric: 'krwNetCpc', meta: { numerator: 'krwNetRevenue', denominator: 'clicks' } },
				{ type: 'cpc', calculatedMetric: 'usdNetCpc', meta: { numerator: 'usdNetRevenue', denominator: 'clicks' } },

				{ type: 'ctr', calculatedMetric: 'sspCtr', meta: { numerator: 'sspClicks', denominator: 'sspEstimatedImpressions' } },
				{ type: 'cpm', calculatedMetric: 'sspEstimatedKrwNetCpm', meta: { numerator: 'sspEstimatedKrwNetRevenue', denominator: 'sspEstimatedImpressions' } },
				{ type: 'cpm', calculatedMetric: 'sspEstimatedUsdNetCpm', meta: { numerator: 'sspEstimatedUsdNetRevenue', denominator: 'sspEstimatedImpressions' } },
				{ type: 'cpc', calculatedMetric: 'sspEstimatedKrwNetCpc', meta: { numerator: 'sspEstimatedKrwNetRevenue', denominator: 'sspClicks' } },
				{ type: 'cpc', calculatedMetric: 'sspEstimatedUsdNetCpc', meta: { numerator: 'sspEstimatedUsdNetRevenue', denominator: 'sspClicks' } },
			])
		);

		if (fixedTimezone) {
			// 광고유닛 요청수 나머지 row 추가 스트림 (AP 매칭 안 된 나머지 row 추가)
			transformers.push(
				this.commonService.transformForAddExtraRows<AdUnitStatsLabel>({
					targetFields: ['serviceId', 'adUnitId', 'country'],
					extraDataList: auData,
					defaultValue: {
						placeChannelType: '-', placeProductType: '-',
						impressions: 0, clicks: 0, krwRevenue: 0, usdRevenue: 0, krwNetRevenue: 0, usdNetRevenue: 0,
						adProviderRequests: 0, adProviderResponses: 0,
						sspFilledRequests: 0, sspImpressions: 0, sspViewableImpressions: 0, sspEstimatedImpressions: 0, sspClicks: 0, sspCompletions: 0,
						sspEstimatedKrwNetRevenue: 0, sspEstimatedUsdNetRevenue: 0,
						ctr: 0, krwNetCpm: 0, usdNetCpm: 0, krwNetCpc: 0, usdNetCpc: 0,
						sspCtr: 0, sspEstimatedKrwNetCpm: 0, sspEstimatedUsdNetCpm: 0, sspEstimatedKrwNetCpc: 0, sspEstimatedUsdNetCpc: 0,
					}
				})
			);
		}

		// 메타 추가 스트림
		transformers.push(this.commonService.transformForAddMeta<AdUnitStatsLabel>(
		[{
				targetFields: ['adProviderId'],
				additionalFields: ['adProviderName', 'timezone', 'currency', 'connectionType'],
				meta: apMeta
			},
			{
				targetFields: ['serviceId'],
				additionalFields: ['serviceName'],
				meta: svcMeta
			},
			{
				targetFields: fixedTimezone ? ['serviceId', 'adUnitId', 'country'] : ['apTimezone', 'serviceId', 'adUnitId', 'country'],
				additionalFields: ['adUnitRequests'],
				meta: auDataMeta,
				defaultValue: '0'
			}]
		));

		const publisher = await this.syncPublisherModel.findOne({ _id: publisher_id }, { name: 1, cmsType: 1 });

		// cmsType 이 GFP 인 경우에만 sspEstimatedXxx 추가
		let sspEstimatedImpField = [];
		let sspEstimatedField = [];
		if (publisher.cmsType === CmsType.GFP) {
			sspEstimatedImpField = [{ key: 'sspEstimatedImpressions', header: 'SSP Estimated Impressions' }];
			sspEstimatedField = [
				{ key: 'sspEstimatedKrwNetRevenue', header: 'SSP Estimated KRW Net Revenue' },
				{ key: 'sspEstimatedUsdNetRevenue', header: 'SSP Estimated USD Net Revenue' },
				{ key: 'sspEstimatedKrwNetCpm', header: 'SSP Estimated KRW Net CPM' },
				{ key: 'sspEstimatedUsdNetCpm', header: 'SSP Estimated USD Net CPM' },
				{ key: 'sspEstimatedKrwNetCpc', header: 'SSP Estimated KRW Net CPC' },
				{ key: 'sspEstimatedUsdNetCpc', header: 'SSP Estimated USD Net CPC' }
			];
		}

		return await this.filesService.saveAsCsvReport<AdUnitStatsLabel>(
			{
				'Publisher': publisher.name,
				'Report Type': 'Ad Unit Performance Report',
				'Creation Time': moment().format('YYYY-MM-DD HH:mm:ss Z'),
				'Timezone': _.isEqual(timezone, '-') ? 'Ad Provider Timezone' : timezone,
				'Period': `${moment(startDate).format('YYYY-MM-DD')} ~ ${moment(endDate).format('YYYY-MM-DD')}`,
			},
			[
				{ key: 'serviceName', header: 'Service' },
				{ key: 'adUnitId', header: 'Ad Unit' },
				{ key: 'country', header: 'Country' },
				{ key: 'adProviderName', header: 'Ad Provider' },
				{ key: 'connectionType', header: 'Ad Provider Connection Type' },
				{ key: 'timezone', header: 'Ad Provider Time Zone' },
				{ key: 'currency', header: 'Ad Provider Currency' },
				{ key: 'placeChannelType', header: 'Place Channel Type' },
				{ key: 'placeProductType', header: 'Place Product Type' },
				{ key: 'impressions', header: 'Impressions' },
				{ key: 'clicks', header: 'Clicks' },
				{ key: 'ctr', header: 'CTR' },
				{ key: 'krwRevenue', header: 'KRW Revenue' },
				{ key: 'usdRevenue', header: 'USD Revenue' },
				{ key: 'krwNetRevenue', header: 'KRW Net Revenue' },
				{ key: 'usdNetRevenue', header: 'USD Net Revenue' },
				{ key: 'krwNetCpm', header: 'KRW Net CPM' },
				{ key: 'usdNetCpm', header: 'USD Net CPM' },
				{ key: 'krwNetCpc', header: 'KRW Net CPC' },
				{ key: 'usdNetCpc', header: 'USD Net CPC' },
				{ key: 'adUnitRequests', header: 'Ad Unit Requests' },
				{ key: 'adProviderRequests', header: 'Ad Provider Requests' },
				{ key: 'adProviderResponses', header: 'Ad Provider Responses' },
				{ key: 'sspFilledRequests', header: 'SSP Filled Requests' },
				{ key: 'sspImpressions', header: 'SSP Impressions' },
				{ key: 'sspViewableImpressions', header: 'SSP Viewable Impressions' },
				...sspEstimatedImpField,
				{ key: 'sspClicks', header: 'SSP Clicks' },
				{ key: 'sspCtr', header: 'SSP CTR' },
				{ key: 'sspCompletions', header: 'SSP Completions' },
				...sspEstimatedField,
			],
			apDataStream,
			{ transformers }
		);
	}


	/**
	 * 광고공급자별 성과 리포트 API - 차트
	 *
	 * @param SimplePerformanceParams { publisher_id, startDate, endDate, mainTimezone, period, filters: { adProviderIds, countries } }
	 */
	async getAdProviderChart({ publisher_id, startDate, endDate, mainTimezone, period, filters: { adProviderIds, countries } }: SimplePerformanceParams) {
		const match = { timezone: mainTimezone, date: { $gte: startDate, $lte: endDate }, publisher_id };

		if (!_.isEmpty(adProviderIds)) { match['adProviderId'] = { $in: adProviderIds }; }
		if (!_.isEmpty(countries)) { match['country'] = { $in: countries }; }

		const defaultMetric  = {
			impressions: 0, clicks: 0, usdNetRevenue: 0, krwNetRevenue: 0,
			adProviderRequests: 0, adProviderResponses: 0,
			sspFilledRequests: 0, sspImpressions: 0, sspViewableImpressions: 0, sspEstimatedImpressions: 0, sspClicks: 0,
			sspEstimatedUsdNetRevenue: 0, sspEstimatedKrwNetRevenue: 0
		};

		// apData = [{ date, adProviderId, impressions, krwNetRevenue, usdNetRevenue }]
		const apData = await this.adProviderDailyModel.aggregate([
			{ $match: match },

			{ $addFields: { date: (period === 'D') ? '$date' : { $substr: ['$date', 0, 6] } } },

			{ $group: {
				_id: { adProviderId: '$adProviderId', date: '$date' },

				impressions: { $sum: '$impressions' },
				krwNetRevenue: { $sum: '$krwNetRevenue' },
				usdNetRevenue: { $sum: '$usdNetRevenue' },
			} },

			{ $addFields: {
				adProviderId: '$_id.adProviderId', date: '$_id.date',

				krwNetRevenue: { $toDouble: '$krwNetRevenue' },
				usdNetRevenue: { $toDouble: '$usdNetRevenue' },
			} },

			{ $project: { _id: 0 } },

			{ $sort: { adProviderId: 1, date: 1 } }
		], { allowDiskUse: true });

		// 빈 날짜 채우기
		return this.commonService.interpolateDateDimension<AdProviderStatsLabel>(
			apData,
			[startDate, endDate],
			period === 'D' ? 'daily' : 'monthly',
			['adProviderId', 'date'],
			'',
			defaultMetric,
			'asc'
		);
	}

	/**
	 * 광고공급자별 성과 리포트 API - 화면
	 *
	 * @param SimplePerformanceParams { publisher_id, startDate, endDate, mainTimezone, period, filters: { adProviderIds, countries } }
	 */
	async getAdProviderStats({ publisher_id, startDate, endDate, mainTimezone, period, filters: { adProviderIds, countries } }: SimplePerformanceParams) {
		const match = { timezone: mainTimezone, date: { $gte: startDate, $lte: endDate }, publisher_id };

		if (!_.isEmpty(adProviderIds)) { match['adProviderId'] = { $in: adProviderIds }; }
		if (!_.isEmpty(countries)) { match['country'] = { $in: countries }; }

		const defaultMetric  = {
			impressions: 0, clicks: 0, usdNetRevenue: 0, krwNetRevenue: 0,
			adProviderRequests: 0, adProviderResponses: 0,
			sspFilledRequests: 0, sspImpressions: 0, sspViewableImpressions: 0, sspEstimatedImpressions: 0, sspClicks: 0,
			sspEstimatedUsdNetRevenue: 0, sspEstimatedKrwNetRevenue: 0
		};

		// apData = [{
		// 		date, adProviderId,
		// 		impressions, clicks, krwNetRevenue, usdNetRevenue,
		// 		adProviderRequests, adProviderResponses,
		// 		sspFilledRequests, sspImpressions, sspViewableImpressions, sspEstimatedImpressions, sspClicks,
		// 		sspEstimatedKrwNetRevenue, sspEstimatedUsdNetRevenue
		// }]
		const apData = await this.adProviderDailyModel.aggregate([
			{ $match: match },

			{ $addFields: { date: (period === 'D') ? '$date' : { $substr: ['$date', 0, 6] } } },

			{ $group: {
				_id: { adProviderId: '$adProviderId', date: '$date' },

				impressions: { $sum: '$impressions' },
				clicks: { $sum: '$clicks' },
				krwNetRevenue: { $sum: '$krwNetRevenue' },
				usdNetRevenue: { $sum: '$usdNetRevenue' },

				adProviderRequests: { $sum: '$adProviderRequests' },
				adProviderResponses: { $sum: '$adProviderResponses' },
				sspFilledRequests: { $sum: '$sspFilledRequests' },
				sspImpressions: { $sum: '$sspImpressions' },
				sspViewableImpressions: { $sum: '$sspViewableImpressions' },
				sspEstimatedImpressions: { $sum: '$sspEstimatedImpressions' },
				sspClicks: { $sum: '$sspClicks' },
				sspEstimatedKrwNetRevenue: { $sum: '$sspEstimatedKrwNetRevenue' },
				sspEstimatedUsdNetRevenue: { $sum: '$sspEstimatedUsdNetRevenue' },
			} },

			{ $addFields: {
				adProviderId: '$_id.adProviderId', date: '$_id.date',

				krwNetRevenue: { $toDouble: '$krwNetRevenue' },
				usdNetRevenue: { $toDouble: '$usdNetRevenue' },
				sspEstimatedKrwNetRevenue: { $toDouble: '$sspEstimatedKrwNetRevenue' },
				sspEstimatedUsdNetRevenue: { $toDouble: '$sspEstimatedUsdNetRevenue' },
			} },

			{ $project: { _id: 0 } },

			{ $sort: { adProviderId: 1, date: 1 } }
		], { allowDiskUse: true });

		// 빈 날짜 채우기
		const interpolatedApData = this.commonService.interpolateDateDimension<AdProviderStatsLabel>(
			apData,
			[startDate, endDate],
			period === 'D' ? 'daily' : 'monthly',
			['adProviderId', 'date'],
			'',
			defaultMetric,
			'asc'
		);


		// subTotal = [{
		// 		adProviderId,
		// 		impressions, clicks, krwNetRevenue, usdNetRevenue,
		// 		adProviderRequests, adProviderResponses,
		// 		sspFilledRequests, sspImpressions, sspViewableImpressions, sspEstimatedImpressions, sspClicks,
		// 		sspEstimatedKrwNetRevenue, sspEstimatedUsdNetRevenue,
		// 		details: [ { date, metrics... } ]
		// }]
		const subTotal = interpolatedApData.reduce((acc, curr) => {
			const { adProviderId, date, ...rest } = curr;

			const existing = acc.find(item => item.adProviderId === adProviderId);

			if (existing) {
				// subTotal 이 이미 있는 경우, 지표 합산 처리
				_.keys(rest).map(mtr => existing[mtr] += rest[mtr]);

				// details 에 추가
				existing.details.push({ date, ...rest });
			} else {
				// subTotal 없는 경우 추가
				acc.push({ adProviderId, ...rest, details: [{ date, ...rest }] });
			}

			return acc;
		}, []);

		// Total 구하기
		// Total = {
		// 		impressions, clicks, krwNetRevenue, usdNetRevenue,
		// 		adProviderRequests, adProviderResponses,
		// 		sspFilledRequests, sspImpressions, sspViewableImpressions, sspEstimatedImpressions, sspClicks,
		// 		sspEstimatedKrwNetRevenue, sspEstimatedUsdNetRevenue,
		// 		subTotal: [{
		// 			adProviderId, metrics...
		// 			details: [ { date, metrics... } ]
		// 		}]
		// }
		let data: any = {};

		if (!_.isEmpty(subTotal)) {
			data = subTotal.reduce((acc, curr) => {
				_.keys(defaultMetric).map(mtr => acc[mtr] += curr[mtr]);

				return acc;
			}, { ...defaultMetric });

			data.subTotal = subTotal;
		}

		return data;
	}

	/**
	 * 광고공급자별 성과 리포트 API - CSV
	 *
	 * @param SimplePerformanceParams { publisher_id, startDate, endDate, mainTimezone, period, filters: { adProviderIds, countries } }
	 */
	async getAdProviderCsv({ publisher_id, startDate, endDate, mainTimezone, timezone, period, filters: { adProviderIds, countries } }: SimplePerformanceParams) {
		const match = { timezone: mainTimezone, date: { $gte: startDate, $lte: endDate }, publisher_id };

		if (!_.isEmpty(adProviderIds)) { match['adProviderId'] = { $in: adProviderIds }; }
		if (!_.isEmpty(countries)) { match['country'] = { $in: countries }; }

		const dataStream = this.adProviderDailyModel.aggregate([
			{ $match: match },

			{ $addFields: { date: (period === 'D') ? '$date' : { $substr: ['$date', 0, 6] } } },

			{ $group: {
				_id: { date: '$date', adProviderId: '$adProviderId', country: '$country' },

				impressions: { $sum: '$impressions' },
				clicks: { $sum: '$clicks' },
				krwRevenue: { $sum: '$krwRevenue' },
				usdRevenue: { $sum: '$usdRevenue' },
				krwNetRevenue: { $sum: '$krwNetRevenue' },
				usdNetRevenue: { $sum: '$usdNetRevenue' },

				adProviderRequests: { $sum: '$adProviderRequests' },
				adProviderResponses: { $sum: '$adProviderResponses' },
				sspFilledRequests: { $sum: '$sspFilledRequests' },
				sspImpressions: { $sum: '$sspImpressions' },
				sspViewableImpressions: { $sum: '$sspViewableImpressions' },
				sspEstimatedImpressions: { $sum: '$sspEstimatedImpressions' },
				sspClicks: { $sum: '$sspClicks' },
				sspCompletions: { $sum: '$sspCompletions' },
				sspEstimatedKrwNetRevenue: { $sum: '$sspEstimatedKrwNetRevenue' },
				sspEstimatedUsdNetRevenue: { $sum: '$sspEstimatedUsdNetRevenue' },
			} },

			{ $project: {
				_id: 0, date: '$_id.date', adProviderId: '$_id.adProviderId', country: '$_id.country',
				impressions: 1, clicks: 1, krwRevenue: 1, usdRevenue: 1, krwNetRevenue: 1, usdNetRevenue: 1,
				adProviderRequests: 1, adProviderResponses: 1,
				sspFilledRequests: 1, sspImpressions: 1, sspViewableImpressions: 1, sspEstimatedImpressions: 1, sspClicks: 1, sspCompletions: 1,
				sspEstimatedKrwNetRevenue: 1, sspEstimatedUsdNetRevenue: 1,
			} },

			{ $sort: { date: 1, adProviderId: 1 } }
		], { allowDiskUse: true });

		const apCond = !_.isEmpty(adProviderIds) ? { adProvider_id: { $in: adProviderIds.map(id => new Types.ObjectId(id)) } } : { };
		const adProviders = await this.syncAdProviderInfoModel.aggregate([
			{ $match: { publisher_id, ...apCond } },
			{ $lookup: { from: 'SyncAdProviders', foreignField: '_id', localField: 'adProvider_id', as: 'ap' } },
			{ $unwind: { path: '$ap', preserveNullAndEmptyArrays: false } },
			{ $project: { _id: '$ap._id', name: '$ap.name', timezone: '$ap.timezone', currency: '$ap.currency', connectionType: '$ap.connectionType' } },
		]);
		const apMeta = adProviders.reduce((acc, { _id, name, connectionType, currency, timezone }) => {
			acc[_id.toString()] = { adProviderName: name, connectionType, currency, timezone };
			return acc;
		}, {});

		// 계산식 추가 스트림
		const addCalculatedMetricsTrans = this.commonService.transformForAddCalculatedMetrics<AdProviderStatsLabel>(
			[
				{ type: 'ctr', calculatedMetric: 'ctr', meta: { numerator: 'clicks', denominator: 'impressions' } },
				{ type: 'cpm', calculatedMetric: 'krwNetCpm', meta: { numerator: 'krwNetRevenue', denominator: 'impressions' } },
				{ type: 'cpm', calculatedMetric: 'usdNetCpm', meta: { numerator: 'usdNetRevenue', denominator: 'impressions' } },
				{ type: 'cpc', calculatedMetric: 'krwNetCpc', meta: { numerator: 'krwNetRevenue', denominator: 'clicks' } },
				{ type: 'cpc', calculatedMetric: 'usdNetCpc', meta: { numerator: 'usdNetRevenue', denominator: 'clicks' } },

				{ type: 'ctr', calculatedMetric: 'sspCtr', meta: { numerator: 'sspClicks', denominator: 'sspEstimatedImpressions' } },
				{ type: 'cpm', calculatedMetric: 'sspEstimatedKrwNetCpm', meta: { numerator: 'sspEstimatedKrwNetRevenue', denominator: 'sspEstimatedImpressions' } },
				{ type: 'cpm', calculatedMetric: 'sspEstimatedUsdNetCpm', meta: { numerator: 'sspEstimatedUsdNetRevenue', denominator: 'sspEstimatedImpressions' } },
				{ type: 'cpc', calculatedMetric: 'sspEstimatedKrwNetCpc', meta: { numerator: 'sspEstimatedKrwNetRevenue', denominator: 'sspClicks' } },
				{ type: 'cpc', calculatedMetric: 'sspEstimatedUsdNetCpc', meta: { numerator: 'sspEstimatedUsdNetRevenue', denominator: 'sspClicks' } },
			]
		);

		// 메타 추가 스트림
		const addMetaTrans = this.commonService.transformForAddMeta<AdProviderStatsLabel>(
			[{
				targetFields: ['adProviderId'],
				additionalFields: ['adProviderName', 'timezone', 'currency', 'connectionType'],
				meta: apMeta,
				removeTargetField: true,
			}]
		);

		const publisher = await this.syncPublisherModel.findOne({ _id: publisher_id }, { name: 1, cmsType: 1 });

		// cmsType 이 GFP 인 경우에만 sspEstimatedXxx 추가
		let sspEstimatedImpField = [];
		let sspEstimatedField = [];
		if (publisher.cmsType === CmsType.GFP) {
			sspEstimatedImpField = [{ key: 'sspEstimatedImpressions', header: 'SSP Estimated Impressions' }];
			sspEstimatedField = [
				{ key: 'sspEstimatedKrwNetRevenue', header: 'SSP Estimated KRW Net Revenue' },
				{ key: 'sspEstimatedUsdNetRevenue', header: 'SSP Estimated USD Net Revenue' },
				{ key: 'sspEstimatedKrwNetCpm', header: 'SSP Estimated KRW Net CPM' },
				{ key: 'sspEstimatedUsdNetCpm', header: 'SSP Estimated USD Net CPM' },
				{ key: 'sspEstimatedKrwNetCpc', header: 'SSP Estimated KRW Net CPC' },
				{ key: 'sspEstimatedUsdNetCpc', header: 'SSP Estimated USD Net CPC' }
			];
		}

		return await this.filesService.saveAsCsvReport<AdProviderStatsLabel>(
			{
				'Publisher': publisher.name,
				'Report Type': 'Ad Provider Performance Report',
				'Creation Time': moment().format('YYYY-MM-DD HH:mm:ss Z'),
				'Timezone': _.isEqual(timezone, '-') ? 'Ad Provider Timezone' : timezone,
				'Period': `${moment(startDate).format('YYYY-MM-DD')} ~ ${moment(endDate).format('YYYY-MM-DD')}`,
			},
			[
				{ key: 'date', header: 'Date' },
				{ key: 'adProviderName', header: 'Ad Provider' },
				{ key: 'connectionType', header: 'Ad Provider Connection Type' },
				{ key: 'timezone', header: 'Ad Provider Time Zone' },
				{ key: 'currency', header: 'Ad Provider Currency' },
				{ key: 'country', header: 'Country' },
				{ key: 'impressions', header: 'Impressions' },
				{ key: 'clicks', header: 'Clicks' },
				{ key: 'ctr', header: 'CTR' },
				{ key: 'krwRevenue', header: 'KRW Revenue' },
				{ key: 'usdRevenue', header: 'USD Revenue' },
				{ key: 'krwNetRevenue', header: 'KRW Net Revenue' },
				{ key: 'usdNetRevenue', header: 'USD Net Revenue' },
				{ key: 'krwNetCpm', header: 'KRW Net CPM' },
				{ key: 'usdNetCpm', header: 'USD Net CPM' },
				{ key: 'krwNetCpc', header: 'KRW Net CPC' },
				{ key: 'usdNetCpc', header: 'USD Net CPC' },
				{ key: 'adProviderRequests', header: 'Ad Provider Requests' },
				{ key: 'adProviderResponses', header: 'Ad Provider Responses' },
				{ key: 'sspFilledRequests', header: 'SSP Filled Requests' },
				{ key: 'sspImpressions', header: 'SSP Impressions' },
				{ key: 'sspViewableImpressions', header: 'SSP Viewable Impressions' },
				...sspEstimatedImpField,
				{ key: 'sspClicks', header: 'SSP Clicks' },
				{ key: 'sspCtr', header: 'SSP CTR' },
				{ key: 'sspCompletions', header: 'SSP Completions' },
				...sspEstimatedField,
			],
			dataStream,
			{
				transformers: [addCalculatedMetricsTrans, addMetaTrans],
				castHooks: {
					date: (value) => period === 'D' ? moment(value).format('YYYY-MM-DD') : moment(value).format('YYYY-MM')
				}
			}
		);
	}


	// 대표 Timezone 조회
	async getMainTimezone(timezone: string) {
		return this.timezoneMapModel.findOne({
			$or: [
				{ timezone },
				{ relatedTimezones: { $in: [ timezone ] } }
			]
		}, { timezone: 1 });
	}
}
