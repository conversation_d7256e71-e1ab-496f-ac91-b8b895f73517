import { Body, Controller, Logger, Post, UseFilters, UseGuards, UseInterceptors, UsePipes } from '@nestjs/common';

import _ from 'lodash';

import { AuthorizationGuard } from '../../../guard/authorization.guard';
import { HttpExceptionFilter } from '../../../exception/http-exception.filter';
import { PublisherIdPipe } from '../../../pipe/publisher-id.pipe';

import { AP_TIMEZONE } from '../../../enum/report.enum';
import { MethodTypeObj as MethodType } from '../performance-report.enums';

import { SimplePerformanceService } from './simple-performance.service';
import { SimplePerformanceValidator } from './simple-performance.validator';
import { SimplePerformanceValidationPipe } from './simple-performance-validation.pipe';


@Controller('performance')
@UseGuards(AuthorizationGuard)
@UsePipes(PublisherIdPipe)
@UseFilters(HttpExceptionFilter)
export class SimplePerformanceController {
	private readonly logger = new Logger(SimplePerformanceController.name);

	constructor(private readonly simplePerformanceService: SimplePerformanceService) { }

	/**
	 * 광고유닛별 성과 리포트 API - 차트
	 *
	 * @param params { publisherId, startDate, endDate, timezone, period, filters?: { serviceIds, adUnitIds, adProviderIds, countries, placeChannelTypes, placeProductTypes } }
	 */
	@Post('adunit/chart')
	async getAdUnitChart(@Body(new SimplePerformanceValidationPipe([MethodType.AU_CHART])) params: any) {
		this.logger.debug(`SimplePerformanceController.getAdUnitChart() 호출`);
		this.logger.debug(`params = ${JSON.stringify(params)}`);

		const tz = await this.simplePerformanceService.getMainTimezone(params.timezone);

		SimplePerformanceValidator.validateParam(params, tz);

		params.mainTimezone = !_.isEqual(params.timezone, AP_TIMEZONE) ? tz.timezone : params.timezone;

		return { data: await this.simplePerformanceService.getAdUnitChart(params) };
	}

	/**
	 * 광고유닛별 성과 리포트 API - 화면
	 *
	 * @param params { publisherId, startDate, endDate, timezone, filters?: { serviceIds, adUnitIds, adProviderIds, countries, placeChannelTypes, placeProductTypes } }
	 */
	@Post('adunit/stats')
	async getAdUnitStats(@Body(new SimplePerformanceValidationPipe([MethodType.AU])) params: any) {
		this.logger.debug(`SimplePerformanceController.getAdUnitStats() 호출`);
		this.logger.debug(`params = ${JSON.stringify(params)}`);

		const tz = await this.simplePerformanceService.getMainTimezone(params.timezone);

		SimplePerformanceValidator.validateParam(params, tz);

		params.mainTimezone = !_.isEqual(params.timezone, AP_TIMEZONE) ? tz.timezone : params.timezone;

		return { data: await this.simplePerformanceService.getAdUnitStats(params) };
	}

	/**
	 * 광고유닛별 성과 리포트 API - CSV
	 *
	 * @param params { publisherId, startDate, endDate, timezone, filters?: { serviceIds, adUnitIds, adProviderIds, countries, placeChannelTypes, placeProductTypes } }
	 */
	@Post('adunit/csv')
	async getAdUnitCsv(@Body(new SimplePerformanceValidationPipe([MethodType.AU])) params: any) {
		this.logger.debug(`SimplePerformanceController.getAdUnitCsv() 호출`);
		this.logger.debug(`params = ${JSON.stringify(params)}`);

		const tz = await this.simplePerformanceService.getMainTimezone(params.timezone);

		SimplePerformanceValidator.validateParam(params, tz);

		params.mainTimezone = !_.isEqual(params.timezone, AP_TIMEZONE) ? tz.timezone : params.timezone;

		return { data : await this.simplePerformanceService.getAdUnitCsv(params) };
	}

	/**
	 * 광고공급자별 성과 리포트 API - 차트
	 *
	 * @param params { publisherId, startDate, endDate, timezone, period, filters?: { adProviderIds, countries } }
	 */
	@Post('adprovider/chart')
	async getAdProviderChart(@Body(new SimplePerformanceValidationPipe([MethodType.AP])) params: any) {
		this.logger.debug(`SimplePerformanceController.getAdProviderChart() 호출`);
		this.logger.debug(`params = ${JSON.stringify(params)}`);

		const tz = await this.simplePerformanceService.getMainTimezone(params.timezone);

		SimplePerformanceValidator.validateParam(params, tz);

		params.mainTimezone = !_.isEqual(params.timezone, AP_TIMEZONE) ? tz.timezone : params.timezone;

		return { data: await this.simplePerformanceService.getAdProviderChart(params) };
	}

	/**
	 * 광고공급자별 성과 리포트 API - 화면
	 *
	 * @param params { publisherId, startDate, endDate, timezone, period, filters?: { adProviderIds, countries } }
	 */
	@Post('adprovider/stats')
	async getAdProviderStats(@Body(new SimplePerformanceValidationPipe([MethodType.AP])) params: any) {
		this.logger.debug(`SimplePerformanceController.getAdProviderStats() 호출`);
		this.logger.debug(`params = ${JSON.stringify(params)}`);

		const tz = await this.simplePerformanceService.getMainTimezone(params.timezone);

		SimplePerformanceValidator.validateParam(params, tz);

		params.mainTimezone = !_.isEqual(params.timezone, AP_TIMEZONE) ? tz.timezone : params.timezone;

		return { data: await this.simplePerformanceService.getAdProviderStats(params) };
	}

	/**
	 * 광고공급자별 성과 리포트 API - CSV
	 *
	 * @param params { publisherId, startDate, endDate, timezone, period, filters?: { adProviderIds, countries } }
	 */
	@Post('adprovider/csv')
	async getAdProviderCsv(@Body(new SimplePerformanceValidationPipe([MethodType.AP])) params: any) {
		this.logger.debug(`SimplePerformanceController.getAdProviderCsv() 호출`);
		this.logger.debug(`params = ${JSON.stringify(params)}`);

		const tz = await this.simplePerformanceService.getMainTimezone(params.timezone);

		SimplePerformanceValidator.validateParam(params, tz);

		params.mainTimezone = !_.isEqual(params.timezone, AP_TIMEZONE) ? tz.timezone : params.timezone;

		return { data : await this.simplePerformanceService.getAdProviderCsv(params) };
	}
}
