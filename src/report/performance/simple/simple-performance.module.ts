import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { API_DB_NAME, CMS_DB_NAME } from '../../../db/constants';

import { AggregatorModule } from '../../aggregator/aggregator.module';
import { FilesModule } from '../../files/files.module';
import { CommonModule } from '../../common/common.module';

import { SimplePerformanceController } from './simple-performance.controller';
import { SimplePerformanceService } from './simple-performance.service';
import { SimplePerformanceValidator } from './simple-performance.validator';

import { ApiUserModel } from '../../schema/api-user.schema';
import { TimezoneMapModel } from '../../schema/timezone-map.schema';
import { SyncPublisherModel } from '../../schema/sync-publisher.schema';
import { SyncAdProviderInfoModel } from '../../schema/sync-ad-provider-info.schema';
import { SyncPublisherServiceModel } from '../../schema/sync-publisher-service.schema';
import { PerformanceAdUnitDailyModel } from '../../schema/performance-ad-unit-daily.schema';
import { PerformanceAdProviderDailyModel } from '../../schema/performance-ad-provider-daily.schema';


@Module({
	imports: [
		MongooseModule.forFeature([
			ApiUserModel,
		], CMS_DB_NAME),
		MongooseModule.forFeature([
			TimezoneMapModel,
			SyncPublisherModel,
			SyncAdProviderInfoModel,
			SyncPublisherServiceModel,
			PerformanceAdUnitDailyModel,
			PerformanceAdProviderDailyModel,
		], API_DB_NAME),
		AggregatorModule,
		FilesModule,
		CommonModule
	],
	controllers: [SimplePerformanceController],
	providers: [SimplePerformanceService, SimplePerformanceValidator],
	exports: [],
})
export class SimplePerformanceModule {}
