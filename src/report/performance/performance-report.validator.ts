import { BadRequestException, Injectable } from '@nestjs/common';

import _ from 'lodash';
import moment from 'moment';

import { BusinessException } from '../../exception/business-exception';
import { getReplacedErrorCode } from '../../error/error-util';
import { CommonReportErrorCode, PerformanceReportErrorCode } from '../../error/error-code';

import { AdUnitRequests, CmsType, EstimatedMetrics, ReportState } from '../../enum/report.enum';

import { CustomReportDocument } from '../schema/custom-report.schema';
import { SyncPublisherDocument } from '../schema/sync-publisher.schema';

import { PerformanceReportParams } from './performance-report-dto';


@Injectable()
export class PerformanceReportValidator {
	constructor() {}

	static validateDate({ startDate, endDate }: PerformanceReportParams) {
		const start = moment(startDate, 'YYYYMMDD');
		const end = moment(endDate, 'YYYYMMDD');

		// 시작일은 종료일 보다 이후일 수 없음
		if (start.isAfter(end)) {
			throw new BadRequestException([CommonReportErrorCode.START_DATE_BEFORE_END_DATE]);
		}

		// 종료일은 오늘자보다 이후일 수 없음
		if (end.isSameOrAfter(moment().startOf('day'))) {
			throw new BadRequestException([CommonReportErrorCode.END_DATE_BEFORE_TODAY]);
		}

		// 시작일 확인 - 90일 전 리포트는 조회 불가
		if (start.isBefore(moment().startOf('day').subtract(90, 'days'))) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.QUERYABLE_DATE, { value: 90, unit: 'days' }, false)]);
		}
	}

	static validateServices(exist: Boolean) {
		// 서비스 이름이 DB 에 존재하는지
		if (exist === false) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS, { property: 'service' }, false)]);
		}
	}

	static validateEstimatedMetrics({ metrics }: PerformanceReportParams, { cmsType }: SyncPublisherDocument) {
		// NAM 인 경우, 추정 지표 선택 불가
		if (_.isEqual(cmsType, CmsType.NAM) && _.some(metrics, mtr => _.includes(EstimatedMetrics, mtr))) {
			throw new BadRequestException([CommonReportErrorCode.INVALID_METRICS]);
		}
	}

	static validateAdUnitRequests({ metrics }: PerformanceReportParams) {
		// 광고유닛 요청수 단독 선택 불가
		if (metrics.length === 1 && _.includes(metrics, AdUnitRequests)) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.USED_WITH_OTHER_METRICS, { property: AdUnitRequests }, false)]);
		}
	}

	static validateMaxCount(reports: CustomReportDocument[], { maxCount = 3, maxFailureCount = 10 }) {
		// 최근 24시간 이내 실패한 리포트 건수가 10개 이상인 경우, 생성 요청 실패
		const failureCount = reports.filter(report=> _.isEqual(ReportState.FAILURE, report.state)).length;
		if (failureCount >= maxFailureCount) {
			throw new BusinessException([CommonReportErrorCode.CANNOT_REQUEST_REPORT]);
		}

		// 하루 생성 가능한 리포트 개수를 초과한 경우 ( state= FAILURE 인 경우는 제외함 )
		const reportCount = reports.filter(report=> _.isEqual(report.ext.PERF.reqDate, moment().format('YYYYMMDD')) && !_.isEqual(ReportState.FAILURE, report.state)).length;
		if (reportCount >= maxCount) {
			throw new BadRequestException([PerformanceReportErrorCode.EXCEEDED_DAILY_REPORT_MAX_COUNT]);
		}
	}

	static validateReport(report: CustomReportDocument) {
		if (_.isEmpty(report)) {
			throw new BadRequestException([CommonReportErrorCode.REPORT_NOT_EXIST]);
		}
	}

	static validateReportState(report: CustomReportDocument) {
		if (_.isEqual(ReportState.FAILURE, report.state)) {
			// 리포트 상태가 FAILURE 인 경우
			throw new BusinessException([PerformanceReportErrorCode.REPORT_FAILURE]);
		} else if (!_.isEqual(ReportState.COMPLETE, report.state)) {
			// 리포트 상태가 COMPLETE 이 아닌 경우 (즉, READY 또는 IN_PROGRESS)
			// 리포트 생성이 완료 되지 않았는데, 다운로드 요청을 한 케이스로 BadRequestException 으로 처리한다.
			throw new BadRequestException([PerformanceReportErrorCode.REPORT_NOT_COMPLETE]);
		}
	}

	static validateReportFileExist(exist: Boolean) {
		// 리포트 파일이 Nubes 에 존재하는지
		if (exist === false) {
			throw new BusinessException([CommonReportErrorCode.CANNOT_DOWNLOAD_REPORT]);
		}
	}

	static validateReportNotExpired(report: CustomReportDocument) {
		// 리포트가 만료된 경우 ( 리포트 생성 완료 시각 기준으로 24시간이 지난 경우 )
		if (moment(report.succeededAt).add(1, 'days').isBefore(moment())) {
			throw new BadRequestException([PerformanceReportErrorCode.EXPIRED_REPORT]);
		}
	}

	static validateMaxFileSize(report: CustomReportDocument, maxFileSize= 200) {
		// 리포트 파일 크기가 200 MB 기준을 초과한 경우
		if (_.isNumber(report.ext.PERF.fileSize) && report.ext.PERF.fileSize > maxFileSize * 1024 * 1024) {
			throw new BadRequestException([PerformanceReportErrorCode.EXCEEDED_MAX_FILE_SIZE]);
		}
	}
}

