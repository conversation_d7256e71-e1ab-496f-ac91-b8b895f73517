import {IsArray, IsNotEmpty} from 'class-validator';
import {Expose} from 'class-transformer';

// ArrayDistinct vs ArrayUnique
//  - ArrayDistinct 는 항목이 배열 값으로 들어온 경우에 대해서만 검증함 (null 로 들어온 경우 error 발생 안 함)
// 	- ArrayUnique 는 무조건 검증함 ( 항목이 필수인 경우, null 로 들어온 경우도 error 발생) -> ValidateIf 를 붙이면 ArrayDistinct 처럼 처리할 수 있겠음
import {toStringReplacedErrorCode} from '../../error/error-util';
import {CommonReportErrorCode} from '../../error/error-code';

import {MethodType as MethodType} from '../../enum/report.enum';
import {ScheduleParams} from "../schedule/schedule-dto";

export const allGroups = [MethodType.CREATE, MethodType.UPDATE, MethodType.LIST];
export const createListGroups = [MethodType.CREATE, MethodType.LIST];
export const createUpdateGroups = [MethodType.CREATE, MethodType.UPDATE];

export class PerformanceScheduleParams extends ScheduleParams {
	@Expose({groups: createUpdateGroups})
	@IsArray()
	@IsNotEmpty({message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: createUpdateGroups})
	dimensions: string[];

	@Expose({groups: createUpdateGroups})
	@IsArray()
	@IsNotEmpty({message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: createUpdateGroups})
	metrics: string[];
}