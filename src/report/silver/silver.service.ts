import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import moment from 'moment';
import { Model } from 'mongoose';

import { EnvironmentDocument, EnvironmentModel } from '../schema/environment.schema';

@Injectable()
export class SilverService {
	private readonly logger = new Logger(SilverService.name);

	constructor(
		@InjectModel(EnvironmentModel.name) private envModel: Model<EnvironmentDocument>,
	) {
	}

	async getRecentAccuYmdh() {
		const env = await this.envModel.findOne({name: 'silver-log-recent-accumulation-ymdh'}, {value: 1});
		return moment(env.value, 'YYYYMMDDHH').format("YYYY-MM-DDTHH:mm:ssZ")
	}

}

