import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { API_DB_NAME } from '../../db/constants';
import { EnvironmentModel } from '../schema/environment.schema';
import { CommonModule } from '../common/common.module';
import { SilverController } from './silver.controller';
import { SilverService } from './silver.service';

@Module({
	imports: [
		MongooseModule.forFeature([
			EnvironmentModel,
		], API_DB_NAME),
		CommonModule,
	],
	controllers: [SilverController],
	providers: [
		SilverService,
	]
})
export class SilverModule {
}
