import { Controller, Get, Logger } from '@nestjs/common';
import { SilverService } from './silver.service';

@Controller('silver')
export class SilverController {
	private readonly logger = new Logger(SilverController.name);

	constructor(
		private silverService: SilverService,
	) {}


	/**
	 * 최근 실버 로그 적재 일시 조회
	 * @param condition
	 */
	@Get('recent-accu-ymdh')
	async getRecentAccuYmdh() {
		const recentAccuYmdh = await this.silverService.getRecentAccuYmdh();
		return {data: recentAccuYmdh}
	}
}
