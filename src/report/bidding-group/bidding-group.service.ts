import {Injectable, Logger} from '@nestjs/common';
import {InjectModel} from '@nestjs/mongoose';
import {
	AdProviderPlaceBiddingGroupDaily,
	AdProviderPlaceBiddingGroupDailyDocument,
	AdProviderPlaceBiddingGroupDailyModel
} from '../schema/ad-provider-place-bidding-group-daily.schema';
import _ from 'lodash';
import {Model, Types} from 'mongoose';
import {AggregationSpec, AggregatorService} from '../aggregator/aggregator.service';
import {CommonService} from '../common/common.service';
import {DetailCondition} from './detail-condition';
import {RepresentativeCondition} from './representative-condition';
import {Condition} from './condition';


/*
virtualSubSum: true로 설정하기 위해서는 virtualDetail: true 여야 한다.

virtualDetail: true
	부모를 표시하지 않고 virtualDimension이 상세(대표)가 되겠다는 의미.
	virtualDetail: true는 디멘젼의 마지막 순서에 있을 때만 설정 가능하다. 중간 소계는 낼 수 없으므로.

dashboard (namGroup은 보여주고, adProvier는 안 보여주고)
	virtualSubSum: true
	virtualDetail: true

excel (namGroup O - not subSum, adProvider O)
	namGroup, adProvider 모두 보여주는 버전. 대신 namGroup을 subSum하지 않음.

	virtualSubSum: false
	virtualDetail: false

namGroup O - subsum, adProvider O (namGroup으로 subSum을 내고, adProvider도 보여줌)
	virtualSubSum: true
	virtualDetail: false  => virtualDimension의 부모를 없애지 않고 보여주며, 내가 대표가 되지 않는다.
*/

type Dim = 'date' | 'publisher_id' | 'adProvider_id' | 'adProviderPlace_id' | 'biddingGroup_id';

type Label =
	| 'date'
	| 'adProviderId'
	| 'adProviderPlaceId'
	| 'biddingGroupId'
	| 'adProviderRequests'
	| 'adProviderResponses'
	| 'filledRequestsOfAp'
	| 'krwBidPriceSum'
	| 'sspEstimatedImpressions'
	| 'sspEstimatedKrwNetRevenue'
	| 'adProviderResponseRate'
	| 'winRate'
	| 'estimatedRevenue'
	| 'ecpmBasedOnfilledRequestsOfAp'
	| 'sspEstimatedKrwNetCpm';

@Injectable()
export class BiddingGroupService {
	private readonly logger = new Logger(BiddingGroupService.name);

	constructor(
		@InjectModel(AdProviderPlaceBiddingGroupDailyModel.name)
		private bgDailyModel: Model<AdProviderPlaceBiddingGroupDailyDocument>,
		private aggregator: AggregatorService,
		private commonService: CommonService,
	) {
	}

	/**
	 * 대표 지표 - 선출 eCPM, 광고공급자 응답률, 낙찰률
	 * 기간 합산
	 * @param condition
	 */
	async getTotalStats(condition: RepresentativeCondition) {
		const match = this.getBaseMatch(condition);

		if (!_.isNil(condition.adProviderPlaceIds) && !_.isEmpty(condition.adProviderPlaceIds)) {
			match['adProviderPlace_id'] = {
				'$in': condition.adProviderPlaceIds.map(placeId => new Types.ObjectId(placeId))
			};
		}

		const metrics = {
			adProviderRequests: {$sum: '$adProviderRequests'},				// 광고공급자 호출수
			adProviderResponses: {$sum: '$adProviderResponses'},			// 광고공급자 응답수
			filledRequestsOfAp: {$sum: '$filledRequestsOfAp'},				// AP의 선출수
			krwBidPriceSum: {$sum: '$krwBidPriceSum'},						// 비딩가 합
			sspEstimatedImpressions: {$sum: '$sspEstimatedImpressions'},	// 추정 과금 노출수
			sspEstimatedKrwNetRevenue: {$sum: '$sspEstimatedKrwNetRevenue'}	// 추정 순수익
		};

		const addFields = {
			// 광고 응답율
			adProviderResponseRate: {
				$cond: {
					if: {$eq: ['$adProviderRequests', 0]},
					then: 0,
					else: {$round: [{$multiply: [{$divide: ['$adProviderResponses', '$adProviderRequests']}, 100]}, 1]}
				}
			},
			// 낙찰률
			winRate: {
				$cond: {
					if: {$eq: ['$adProviderResponses', 0]},
					then: 0,
					else: {$round: [{$multiply: [{$divide: ['$filledRequestsOfAp', '$adProviderResponses']}, 100]}, 1]}
				}
			},
			// 선출 eCPM
			ecpmBasedOnfilledRequestsOfAp: {
				$cond: {
					if: {$eq: ['$filledRequestsOfAp', 0]},
					then: 0,
					else: {$floor: {$divide: ['$krwBidPriceSum', '$filledRequestsOfAp']}}
				}
			},
			// 추정 입찰가 (Estimated Net CPM)
			// sspEstimatedKrwNetRevenue 를 구할 때 bidPriceInKrw / 1000 의 합으로 집계하므로 cpm 을 표현하기 위해 1000을 곱함
			// mongoDB 에서 NumberDecimal 타입인 sspEstimatedKrwNetRevenue 를 사용하여 계산하면
			// "sspEstimatedKrwNetCpm": { "$numberDecimal": "6325.036000" } 처럼 반환되므로
			// 일반 숫자로 표현하기 위해 toDouble 사용
			sspEstimatedKrwNetCpm: {
				$cond: {
					if: {$eq: ['$sspEstimatedImpressions', 0]},
					then: 0,
					else: {
						$toDouble: {
							$multiply: [{
								$divide: ['$sspEstimatedKrwNetRevenue', '$sspEstimatedImpressions']
							}, 1000]
						}
					}
				}
			}
		};

		const spec: AggregationSpec<AdProviderPlaceBiddingGroupDaily, Dim, Label> = {
			match: match,
			dimensions: [
				{
					name: 'adProviderPlace_id',
					alias: 'adProviderPlaceId',
				},
			],
			metrics: metrics,
			addFields: addFields,
			dropFields: ['krwBidPriceSum', 'filledRequestsOfAp', 'adProviderRequests', 'adProviderResponses', 'sspEstimatedImpressions', 'sspEstimatedKrwNetRevenue']
		};

		const result = await this.aggregator.aggregate(this.bgDailyModel, spec).AsMergedArray({concurrency: 1});

		return result
	}

	/**
	 * 상세 지표 - 선출 eCPM
	 * @param condition
	 */
	async getDetailStatsOfEcpmBasedOnFilledRequests(condition: DetailCondition) {
		const match = this.getMatchForDetail(condition);

		const metrics = {
			filledRequestsOfAp: {$sum: '$filledRequestsOfAp'},	// AP의 선출수
			krwBidPriceSum: {$sum: '$krwBidPriceSum'},			// 비딩가 합
		};

		const addFields = {
			// 추정수익 (소수점 절삭) - 원화이므로 절삭
			estimatedRevenue: {$floor: {$divide: ['$krwBidPriceSum', 1000]}},

			// 선출 eCPM (소수점 절삭) - 원화이므로 절삭
			ecpmBasedOnfilledRequestsOfAp: {
				$round: [
					{
						$cond: {
							if: {$eq: ['$filledRequestsOfAp', 0]},
							then: 0,
							else: {$floor: {$divide: ['$krwBidPriceSum', '$filledRequestsOfAp']}}
						}
					},
					1]
			}
		};

		const spec: AggregationSpec<AdProviderPlaceBiddingGroupDaily, Dim, Label> = {
			match: match,
			dimensions: [
				{
					name: 'date',
					alias: 'date',
					// orderBy: 'desc' // 디멘젼 자체의 정렬은 여기서 orderBy를 사용
				},
			],
			metrics: metrics,
			addFields: addFields,
			grandTotal: true,
			fieldPadding: true, // sub total 에서 missing field 을 공백 '' 값으로 채워넣기
			/*
			ordering: ['a', 'b', 'c', ['e', 'desc'], ['e', 'asc'], ['f', 'desc']]

				sql로 치면 groupBy 한 다음에 orderBy할 때 순서를 지정
				다만, 디멘젼은 이름만 명시하면 되지만, 메트릭은 ['impreesions', 'desc'] 이렇게 기술해 줘야 함.

				아래 쿼리와 같은 의미
					SELECT
						  a, b, c,
						, sum(d) as d
						, sum(e) as e
						, sum(f) as f,
					FROM table
					GROUPBY a, b, c
					ORDERBY a, c, b, f DESC, e ASC, d DESC

			detailSort: {serviceName:1, adUnitId:-1, date: -1},

				subSum이 없는 경우에 대한 정렬 순서
			 */
			detailSort: {date: -1},
			dropFields: ['krwBidPriceSum']
		};

		const result = await this.aggregator.aggregate(this.bgDailyModel, spec).AsMergedArray({concurrency: 1});

		return this.commonService.interpolateDateDimension<Label>(
			result,
			[condition.startDate, condition.endDate],
			'daily',
			['date'],
			'',
			[
				'filledRequestsOfAp',
				'estimatedRevenue',
				'ecpmBasedOnfilledRequestsOfAp',
			],
			'desc' // date에 대한 ordering이 어떻게 되어 있는지 알려줌
		);
	}

	/**
	 * 상세 지표 - 광고공급자 응답률
	 * @param condition
	 */
	async getDetailStatsOfAdProviderResponseRate(condition: DetailCondition) {
		const match = this.getMatchForDetail(condition);

		const metrics = {
			adProviderRequests: {$sum: '$adProviderRequests'},		// 광고공급자 호출수
			adProviderResponses: {$sum: '$adProviderResponses'},	// 광고공급자 응답수
		};

		const addFields = {
			// 광고공급자 응답률 (소수점 1째 자리까지 표현)
			adProviderResponseRate: {
				$round: [
					{
						$multiply: [{
							$cond: {
								if: {$eq: ['$adProviderRequests', 0]},
								then: 0,
								else: {$divide: ['$adProviderResponses', '$adProviderRequests']}
							}
						}, 100]
					},
					1]
			}
		};

		const spec: AggregationSpec<AdProviderPlaceBiddingGroupDaily, Dim, Label> = {
			match: match,
			dimensions: [
				{
					name: 'date',
					alias: 'date',
				},
			],
			metrics: metrics,
			addFields: addFields,
			grandTotal: true,
			fieldPadding: true, // sub total 에서 missing field 을 공백 '' 값으로 채워넣기
			detailSort: {date: -1},
		};

		const result = await this.aggregator.aggregate(this.bgDailyModel, spec).AsMergedArray({concurrency: 1});

		return this.commonService.interpolateDateDimension<Label>(
			result,
			[condition.startDate, condition.endDate],
			'daily',
			['date'],
			'',
			[
				'adProviderRequests',
				'adProviderResponses',
				'adProviderResponseRate',
			],
			'desc' // date에 대한 ordering이 어떻게 되어 있는지 알려줌
		);
	}

	/**
	 * 상세 지표 - 낙찰률
	 * @param condition
	 */
	async getDetailStatsOfWinRate(condition: DetailCondition) {
		const match = this.getMatchForDetail(condition);

		const metrics = {
			filledRequestsOfAp: {$sum: '$filledRequestsOfAp'},		// 선출수
			adProviderResponses: {$sum: '$adProviderResponses'},	// 광고응답수
		};

		const addFields = {
			// 낙찰률 (소수점 1째 자리까지 표현)
			winRate: {
				$round: [
					{
						$multiply: [{
							$cond: {
								if: {$eq: ['$adProviderResponses', 0]},
								then: 0,
								else: {$divide: ['$filledRequestsOfAp', '$adProviderResponses']}
							}
						}, 100]
					},
					1]
			}
		};

		const spec: AggregationSpec<AdProviderPlaceBiddingGroupDaily, Dim, Label> = {
			match: match,
			dimensions: [
				{
					name: 'date',
					alias: 'date',
				},
			],
			metrics: metrics,
			addFields: addFields,
			grandTotal: true,
			fieldPadding: true, // sub total 에서 missing field 을 공백 '' 값으로 채워넣기
			detailSort: {date: -1},
		};

		const result = await this.aggregator.aggregate(this.bgDailyModel, spec).AsMergedArray({concurrency: 1});

		return this.commonService.interpolateDateDimension<Label>(
			result,
			[condition.startDate, condition.endDate],
			'daily',
			['date'], // 디멘젼이 어떻게 구성되어 있는지 알려줌. 즉, 날짜를 어떤 그룹으로 자를건지 알려줌.
			'',
			[
				'filledRequestsOfAp',
				'adProviderResponses',
				'winRate',
			],
			'desc' // date에 대한 ordering이 어떻게 되어 있는지 알려줌
		);
	}

	/**
	 * 상세 지표 - 추정 입찰가
	 * @param condition
	 */
	async getDetailStatsOfSSPEstimatedKrwNetCpm(condition: DetailCondition) {
		const match = this.getMatchForDetail(condition);

		const metrics = {
			sspEstimatedImpressions: {$sum: '$sspEstimatedImpressions'},					// 추정 과금노출수
			sspEstimatedKrwNetRevenue: {$sum: {$toDouble: '$sspEstimatedKrwNetRevenue'}},	// 추정 순수익
		};

		const addFields = {
			// 추정 입찰가 (Estimated Net CPM)
			// bidPriceInKrw / 1000 의 합으로 집계하므로 cpm 을 표현하기 위해 1000 을 곱함
			// mongoDB 에서 NumberDecimal 타입인 sspEstimatedKrwNetRevenue 를 사용하여 계산하면
			// "sspEstimatedKrwNetCpm": { "$numberDecimal": "6325.036000" } 처럼 반환되므로
			// 일반 숫자로 표현하기 위해 toDouble 사용
			sspEstimatedKrwNetCpm: {
				$cond: {
					if: {$eq: ['$sspEstimatedImpressions', 0]},
					then: 0,
					else: {
						$toDouble: {
							$multiply: [{
								$divide: ['$sspEstimatedKrwNetRevenue', '$sspEstimatedImpressions']
							}, 1000]
						}
					}
				}
			}
		};

		const spec: AggregationSpec<AdProviderPlaceBiddingGroupDaily, Dim, Label> = {
			match: match,
			dimensions: [
				{
					name: 'date',
					alias: 'date',
				},
			],
			metrics: metrics,
			addFields: addFields,
			grandTotal: true,
			fieldPadding: true,
			detailSort: {date: -1}
		};

		const result = await this.aggregator.aggregate(this.bgDailyModel, spec).AsMergedArray({concurrency: 1});

		return this.commonService.interpolateDateDimension<Label>(
			result,
			[condition.startDate, condition.endDate],
			'daily',
			['date'],
			'',
			[
				'sspEstimatedImpressions',
				'sspEstimatedKrwNetRevenue',
				'sspEstimatedKrwNetCpm',
			],
			'desc' // date에 대한 ordering이 어떻게 되어 있는지 알려줌
		);
	}

	private getBaseMatch(condition: Condition) {
		return {
			$and: [{date: {$gte: condition.startDate}}, {date: {$lte: condition.endDate}}],
			publisher_id: new Types.ObjectId(condition.publisherId),
			biddingGroup_id: new Types.ObjectId(condition.biddingGroupId)
		};
	}

	private getMatchForDetail(condition: DetailCondition) {
		let match = this.getBaseMatch(condition);
		match['adProviderPlace_id'] = new Types.ObjectId(condition.adProviderPlaceId);

		return match;
	}
}
