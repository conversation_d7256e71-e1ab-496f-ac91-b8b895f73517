// import { Modu<PERSON> } from '@nestjs/common';
// import { BiddingGroupController } from './bidding-group.controller';
// import { MongooseModule } from '@nestjs/mongoose';
// import { AdProviderPlaceBiddingGroupDailyModel } from '../schema/ad-provider-place-bidding-group-daily.schema';
// import { API_DB_NAME } from '../../db/constants';
// import { AggregatorModule } from '../aggregator/aggregator.module';
// import { BiddingGroupService } from './bidding-group.service';
//
// @Module({
// 	imports: [
// 		MongooseModule.forFeature([AdProviderPlaceBiddingGroupDailyModel], API_DB_NAME),
// 		AggregatorModule,
// 	],
// 	controllers: [BiddingGroupController],
// 	providers: [
// 		BiddingGroupService,
// 	]
// })
// export class BiddingGroupModule {
// }
