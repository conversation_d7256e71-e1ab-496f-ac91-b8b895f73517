import { Modu<PERSON> } from '@nestjs/common';
import { AdUnitService } from './ad-unit.service';
import { MongooseModule } from '@nestjs/mongoose';
import { API_DB_NAME, CMS_DB_NAME } from '../../db/constants';
import { AdUnitModel, CmsAdUnitModel } from '../schema/ad-unit.schema';
import { AdUnitController } from './ad-unit.controller';

@Module({
	imports: [
		MongooseModule.forFeature([CmsAdUnitModel], CMS_DB_NAME),
		MongooseModule.forFeature([AdUnitModel], API_DB_NAME)
	],
	providers: [AdUnitService],
	exports: [AdUnitService],
	controllers: [AdUnitController],
})
export class AdUnitModule {
}
