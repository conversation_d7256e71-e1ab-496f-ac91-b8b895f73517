import {Logger} from '@nestjs/common';
import _ from 'lodash';
import {ArgumentMetadata, BadRequestException, Injectable, PipeTransform} from '@nestjs/common';
import {plainToInstance} from 'class-transformer';
import {validate} from 'class-validator';

import {extractErrorMessages} from '../../error/error-util';
import {RevenueSharingReportParams} from './revenue-sharing-report-dto';

@Injectable()
export class RevenueSharingReportValidationPipe implements PipeTransform<RevenueSharingReportParams> {
	constructor(private readonly groups?: string[]) {
	}

	async transform(value: RevenueSharingReportParams, {metatype}: ArgumentMetadata) {
		// RevenueSharingReportParams, Object 타입만 허용함
		if (!metatype || !_.includes([RevenueSharingReportParams, Object], metatype)) {
			return value;
		}

		// 1. Plain object 를 Class instance 로 변환
		// 2. groups 설정을 해줘야 Decorator 가 정상적으로 동작한다.
		const revenueSharingReportParams: RevenueSharingReportParams = plainToInstance(RevenueSharingReportParams, value, {groups: this.groups});
		Logger.debug(`RevenueSharingReportValidationPipe.transform() revenueSharingReportParams = ${JSON.stringify(revenueSharingReportParams, null, 2)}`);

		// 2. 유효성 검사
		const errors = await validate(revenueSharingReportParams);

		if (errors.length > 0) {
			// 에러 메시지를 BadRequestException 으로 던짐
			throw new BadRequestException(extractErrorMessages(errors));
		}

		return revenueSharingReportParams;
	}
}
