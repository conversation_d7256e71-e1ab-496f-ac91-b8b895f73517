import { BadRequestException, Injectable, Logger, UnauthorizedException } from '@nestjs/common';

import _ from 'lodash';
import { Types } from 'mongoose';
import { CommonReportErrorCode, CustomReportErrorCode, ScheduleErrorCode } from '../../error/error-code';

import { RevenueSharingReportParams } from './revenue-sharing-report-dto';

import { RevenueSharingReportDocument } from '../schema/revenue-sharing-report.schema';
import { ReportState } from "../../enum/report.enum";
import { BusinessException } from "../../exception/business-exception";
import moment from "moment/moment";
import { getReplacedErrorCode } from "../../error/error-util";

@Injectable()
export class RevenueSharingReportValidator {
	constructor() {
	}

	/**
	 * reportId 에 해당 하는 리포트가 없는 경우, 수정/삭제/상세 조회/파일 목록 조회 불가
	 */
	static validateReport(report: RevenueSharingReportDocument) {
		if (_.isEmpty(report)) {
			Logger.debug(`RevenueSharingReportValidator.validateReport() CommonReportErrorCode.REPORT_NOT_EXIST (${CommonReportErrorCode.REPORT_NOT_EXIST}) report = ${JSON.stringify(report, null, 2)}`);
			throw new BadRequestException([CommonReportErrorCode.REPORT_NOT_EXIST]);
		}
	}

	/**
	 * 리포트에 대한 권한 검증
	 * 리포트 리스트가 비어 있을 경우 권한이 없는 것임.
	 * @param reports
	 */
	static validateReportAuth(reports: RevenueSharingReportDocument[]) {
		if (_.isEmpty(reports)) {
			throw new BusinessException([CommonReportErrorCode.NO_AUTH_REPORT]);
		}
	}

	/**
	 * report count 검증 ( 파일 목록 조회 )
	 * 	- reportIds 가 20개를 넘은 경우, 파일 목록 조회 불가
	 * 	- reportIds 개수와 reports 개수가 일치 하지 않는 경우, 파일 목록 조회 불가
	 */
	static validateReportCount(report_ids: Types.ObjectId[], reports: RevenueSharingReportDocument[]) {
		const idCount = new Set(report_ids.map(_.toString)).size;

		if (idCount > 20) {
			throw new BadRequestException([CustomReportErrorCode.EXCEEDED_QUERYABLE_REPORT_COUNT]);
		}

		if (idCount !== reports.length) {
			throw new BadRequestException([CommonReportErrorCode.REPORT_NOT_EXIST]);
		}
	}

	/**
	 * 리포트 상태 검증
	 * - 리포트 상태가 FAILURE이거나 COMPLETE가 아닌 경우 다운로드 불가
	 * @param report
	 */
	static validateReportState(report: RevenueSharingReportDocument) {
		if (_.isEqual(ReportState.FAILURE, report.state)) {
			// 리포트 상태가 FAILURE 인 경우
			throw new BusinessException([CommonReportErrorCode.REPORT_FAILURE]);
		} else if (!_.isEqual(ReportState.COMPLETE, report.state)) {
			// 리포트 상태가 COMPLETE 이 아닌 경우 (즉, READY 또는 IN_PROGRESS)
			// 리포트 생성이 완료 되지 않았는데, 다운로드 요청을 한 케이스로 BadRequestException 으로 처리한다.
			throw new BusinessException([CommonReportErrorCode.REPORT_NOT_COMPLETE]);
		}
	}

	/**
	 * 존재하는 리포트인지 검증
	 * @param exist
	 */
	static validateReportFileExistInNubes(filePath: string, exist: Boolean) {
		// 리포트 파일이 Nubes 에 존재하는지
		if (exist === false) {
			Logger.debug(`RevenueSharingReportValidator.validateReportFileExistInNubes() CommonReportErrorCode.REPORT_NOT_EXIST (${CommonReportErrorCode.REPORT_NOT_EXIST}) 누베스에 파일이 없음. ${filePath}`);
			throw new BusinessException([CommonReportErrorCode.REPORT_NOT_EXIST]);
		}
	}

	/**
	 * 날짜 검증 (목록 조회)
	 * 	- 시작일이 종료일 보다 이후인 경우, 목록 조회 불가
	 * 	- 종료일이 오늘자 보다 이후인 경우, 목록 조회 불가
	 */
	static validatePeriodDate({ startDate, endDate }: RevenueSharingReportParams) {
		const start = moment(startDate, 'YYYYMMDD');
		const end = moment(endDate, 'YYYYMMDD');

		// 시작일은 종료일 보다 이후일 수 없음
		if (start.isAfter(end)) {
			throw new BadRequestException([CommonReportErrorCode.START_DATE_BEFORE_END_DATE]);
		}

		// 종료일은 오늘자 보다 이후일 수 없음
		if (end.isSameOrAfter(moment().startOf('day'))) {
			throw new BadRequestException([CommonReportErrorCode.END_DATE_BEFORE_TODAY]);
		}
	}

	static validateScheduleId(isAuthorized: Boolean) {
		if (!isAuthorized) {
			throw new UnauthorizedException([ScheduleErrorCode.NO_AUTH_SCHEDULE]);
		}
	}

	/**
	 * 최근 1년 안의 리포트인지
	 * @param startDate
	 */
	static validateAggStatusDate(startDate: string) {
		const start = moment(startDate, 'YYYYMMDD');

		// 시작일 확인 - 365일 전 리포트는 조회 불가
		if (start.isBefore(moment().startOf('day').subtract(365, 'days'))) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.QUERYABLE_DATE, { value: 365, unit: 'day' })]);
		}
	}
}

