import {Union} from '../../enum/report.enum';

export const RsrMethodType = {
	CREATE: 'CREATE',
	UPDATE: 'UPDATE',
	LIST: 'LIST',
	AGGSTATUS: 'AGGSTATUS',
	PARTNER: 'PARTNER',
} as const;

// // 스케줄 타입
// export const ScheduleType = {
// 	PERF: 'PERF',
// 	RS: 'RS'
// } as const;
// export const ScheduleTypeList = Object.keys(ScheduleType) as (keyof typeof ScheduleType)[];
// export type SCHEDULE_TYPE = Union<typeof ScheduleTypeList>;
//
// // 스케줄 상태
// export const StatusList = ['ON', 'OFF'];
// export type STATE = Union<typeof StatusList>;
//
// // 생성주기
// export const FrequencyList = ['DAILY', 'MONTHLY'];
// export type FREQUENCY = Union<typeof FrequencyList>;
//
// // 집계단위
// export const GranularityList = ['DAILY', 'MONTHLY'];
// export type GRANULARITY = Union<typeof GranularityList>;
//
