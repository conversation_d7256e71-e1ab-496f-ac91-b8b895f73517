import { Inject, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import _ from 'lodash';
import { FilterQuery, Model, Schema, Types } from 'mongoose';

import { ReportState, SilvergreyState } from '../../enum/report.enum';

import { Client } from '../../nubes/clients';
import { NUBES_CLIENT_NAM_API } from '../../nubes/constants';

import { RevenueSharingReportParams } from './revenue-sharing-report-dto';

import { Environment, EnvironmentDocument } from '../schema/environment.schema';
import { SyncPublisher, SyncPublisherDocument } from '../schema/sync-publisher.schema';
import { RevenueSharingReport, RevenueSharingReportDocument } from '../schema/revenue-sharing-report.schema';
import moment from "moment";

@Injectable()
export class RevenueSharingReportService {
	private readonly logger = new Logger(RevenueSharingReportService.name);

	constructor(
		@InjectModel(SyncPublisher.name)
		private syncPublisherModel: Model<SyncPublisherDocument>,
		@InjectModel(Environment.name)
		private environmentModel: Model<EnvironmentDocument>,
		@InjectModel(RevenueSharingReport.name)
		private revenueSharingReportModel: Model<RevenueSharingReportDocument>,
		@Inject(NUBES_CLIENT_NAM_API)
		private nubesClient: Client,
	) {
	}

	/**
	 * 수익쉐어 리포트 리스트 조회
	 *
	 * @param userId
	 * @param RevenueSharingReportParams { publisher_id, type, scheduleName?, timezone?, startDate?, endDate?, creatorId?, queryPeriodUse?, pageNo, pageSize }
	 */
	async list({
				   publisher_id,
				   scheduleName = '',
				   report_id,
				   frequency,
				   granularity,
				   startDate,
				   endDate,
				   pageNo,
				   pageSize
			   }: RevenueSharingReportParams) {

		// 1. 리포트 조건 설정
		const rptFilter = {};
		if (report_id) {
			rptFilter['_id'] = report_id;
		}
		if (!_.isEmpty(startDate)) {
			rptFilter['startDate'] = { $gte: startDate };
		}
		if (!_.isEmpty(endDate)) {
			rptFilter['endDate'] = { $lte: endDate };
		}

		// 2. 스케줄 조건 설정
		const schFilter = {
			'sch.publisher_id': publisher_id,
			'sch.type': 'RS',
		};
		if (!_.isEmpty(scheduleName)) {
			schFilter['sch.name'] = { $regex: `.*${scheduleName}.*`, $options: 'i' };
		}
		if (!_.isEmpty(frequency)) {
			schFilter['sch.frequency'] = frequency;
		}
		if (!_.isEmpty(granularity)) {
			schFilter['sch.granularity'] = granularity;
		}

		const basePipeline: any[] = [
			{ $match: rptFilter },
			{ $lookup: { from: 'Schedules', localField: 'schedule_id', foreignField: '_id', as: 'sch' } },
			{ $unwind: { path: '$sch', preserveNullAndEmptyArrays: false } },
			{ $match: schFilter },
			{
				$project: {
					schedule_id: '$sch._id',
					scheduleName: '$sch.name',
					frequency: '$sch.frequency',
					granularity: '$sch.granularity',
					_id: 1,
					startDate: 1,
					endDate: 1,
					state: 1,
					filePath: 1,
					succeededAt: 1,
					createdAt: 1,
				}
			}
		];


		// 3. 목록 조회
		const pagePipeline: any[] = basePipeline.concat([
			{ $sort: { state: -1, succeededAt: -1, createdAt: -1 } },
			{ $skip: pageNo * pageSize },
			{ $limit: pageSize }
		]);
		Logger.debug(`수익쉐어 리포트 리스트 조회. pagePipeline=${JSON.stringify(pagePipeline, null, 2)}`);
		const list: RevenueSharingReportDocument[] = await this.revenueSharingReportModel.aggregate(pagePipeline, { allowDiskUse: true });
		const transformedList = list
			.map(item => this.revenueSharingReportModel.hydrate(item))
			.map(doc => doc.toJSON());


		// 4. 총 개수 조회
		const totalCountPipeline = basePipeline.concat([
			{ $count: "totalCount" }
		]);
		Logger.debug(`수익쉐어 리포트 리스트 총 개수 조회. totalCountPipeline=${JSON.stringify(totalCountPipeline, null, 2)}`);
		const totalCountResult = await this.revenueSharingReportModel.aggregate(totalCountPipeline, { allowDiskUse: true });
		Logger.debug(`수익쉐어 리포트 리스트 총 개수 조회. totalCountResult=${JSON.stringify(totalCountResult, null, 2)}`);
		const totalCount = totalCountResult[0]?.totalCount || 0;


		// 5. 결과 리턴
		return { list: transformedList, totalCount };
	}

	/**
	 * 수익쉐어 리포트 파일 정보 조회
	 *
	 * @param report_ids
	 */
	async files(publisher_id: Types.ObjectId, report_ids: Types.ObjectId[]) {
		return this.getRevenueSharingReports(
			{ _id: { $in: report_ids }, state: ReportState.COMPLETE },
			{ _id: 1, filePath: 1 }
		);
	}

	/**
	 * 월별 수익쉐어 리포트 집계 현황 조회 (CMS, Partner 겸용)
	 *
	 * @param publisher_id	CMS + 파트너 겸용 (required)
	 * @param schedule_id	파트너 용 (required)
	 * @param scheduleName	CMS 용 (optional)
	 * @param reportId 		CMS 용 (optional)
	 * @param startDate 	CMS + 파트너 겸용 디폴트 D-365
	 * @param endDate		CMS + 파트너 겸용 디폴트 D-1
	 * @param pageNo		CMS + 파트너 겸용 디폴트 0
	 * @param pageSize		CMS + 파트너 겸용 디폴트 20
	 */
	async aggStatus({
						publisher_id,
						schedule_id = undefined, // 파트너 용
						scheduleName = undefined,
						report_id = undefined,
						startDate = moment().subtract(365, 'day').format('YYYYMMDD'),
						endDate = moment().subtract(1, 'day').format('YYYYMMDD'),
						pageNo = 0,
						pageSize = 20
					}: RevenueSharingReportParams) {

		// 1. 조건 설정
		const filter = {
			// 완료(COMPLETE) 상태의 리포트만 조회
			'state': ReportState.COMPLETE,

			// 실버그레이 상태에 READY, WAIT, IN_PROGRESS, FAILED 가 있다면
			'silvergreyDetails.state': {
				$in: [
					SilvergreyState.READY,
					SilvergreyState.WAIT,
					SilvergreyState.IN_PROGRESS,
					SilvergreyState.FAILED
				]
			},

			// startDate, endDate에 걸쳐 있는 리포트
			$or: [{ startDate: { $lte: endDate }, endDate: { $gte: startDate } }]
		};
		if (!_.isNil(report_id)) filter['_id'] = report_id;

		Logger.debug(`................. aggStatus() publisher_id=${publisher_id}, schedule_id=${schedule_id}, scheduleName=${scheduleName}, report_id=${report_id}, startDate=${startDate}, endDate=${endDate}, pageNo=${pageNo}, pageSize=${pageSize}`);

		const filter2 = {
			'sch.publisher_id': publisher_id,
			'sch.type': 'RS',
			'sch.frequency': 'MONTHLY', // 월별 리포트만
		};
		if (!_.isNil(schedule_id)) filter2['sch._id'] = schedule_id;
		if (!_.isNil(scheduleName)) filter2['sch.name'] = { $regex: `.*${scheduleName}.*`, $options: 'i' };

		const basePipeline: any[] = [
			{ $match: filter },
			{ $lookup: { from: 'Schedules', localField: 'schedule_id', foreignField: '_id', as: 'sch' } },
			{ $unwind: { path: '$sch', preserveNullAndEmptyArrays: false } },
			{ $match: filter2 },
			{
				$project: {
					scheduleName: '$sch.name',
					frequency: '$sch.frequency',
					granularity: '$sch.granularity',
					_id: 1,
					startDate: 1,
					endDate: 1,
					state: 1,
					succeededAt: 1,
					silvergreyDetails: 1,
				}
			}
		];
		const pagePipeline: any[] = basePipeline.concat([
			{ $sort: { succeededAt: -1, createdAt: -1 } },
			{ $skip: pageNo * pageSize },
			{ $limit: pageSize }
		]);


		// 2. 조회
		Logger.debug(`월별 수익쉐어 리포트 집계현황 조회. pagePipeline=${JSON.stringify(pagePipeline, null, 2)}`);
		const reports = await this.revenueSharingReportModel.aggregate(pagePipeline, { allowDiskUse: true });
		// Logger.debug(`......................... 월별 수익쉐어 리포트 집계현황 조회. reports=${JSON.stringify(reports, null, 2)}`);


		// 3. 조회 결과 가공
		for (const [index, report] of reports.entries()) {
			// 누락된 실버그레이 설정
			const missingSgDetails = await this._getMissingSilvergreyDetails(report.silvergreyDetails);
			report.missingSilvergreyDetails = missingSgDetails;
			delete report.silvergreyDetails; // silvergreyDetails 제거

			// report_id -> reportId
			report.reportId = report._id;
			delete report._id; // _id 제거

			// publisher_id -> publisherId
			report.publisherId = report.publisher_id;
			delete report.publisher_id; // publisher_id 제거

			reports[index] = report;
		}


		// 4. 전체 건 수 조회
		const totalCountPipeline = basePipeline.concat([{ $count: "totalCount" }]);
		Logger.debug(`월별 수익쉐어 리포트 집계현황 조회. totalCountPipeline=${JSON.stringify(totalCountPipeline, null, 2)}`);
		const totalCountResult = await this.revenueSharingReportModel.aggregate(totalCountPipeline, { allowDiskUse: true });
		Logger.debug(`월별 수익쉐어 리포트 집계현황 조회. totalCountResult=${JSON.stringify(totalCountResult, null, 2)}`);
		const totalCount = totalCountResult[0]?.totalCount || 0;


		// 5. 결과 리턴
		return { list: reports, totalCount };
	}

	private async _getMissingSilvergreyDetails(silvergreyDetails) {
		/*
			silvergreyDetails =  [
				 {
					"adProvider_id" : ObjectId("5b74d94bc36eef272090ca52"),
					"adProviderName" : "NCC 소상공인",
					"reportApiType" : "NCC",
					"reportApiStatus" : "ON",
					"date" : "20250101",
					"state" : "COMPLETE",
					"expectedCompleteDate" : ISODate("2025-01-01T06:10:00.000+0900")
				},
				{
					"adProvider_id" : ObjectId("5b74d94bc36eef272090ca52"),
					"adProviderName" : "NCC 소상공인",
					"reportApiType" : "NCC",
					"reportApiStatus" : "ON",
					"date" : "20250131",
					"state" : "COMPLETE",
					"expectedCompleteDate" : ISODate("2025-01-02T06:10:00.000+0900")
				},
				{
					"adProvider_id" : ObjectId("66f0e409c53f9c82acf91051"),
					"adProviderName" : "Moloco_DEAL",
					"reportApiType" : "MOLOCO",
					"offset" : NumberInt(-2),
					"reportApiStatus" : "ON",
					"date" : "20250101",
					"state" : "FAILED",
					"expectedCompleteDate" : ISODate("2025-01-01T05:10:00.000+0900")
				},
				{
					"adProvider_id" : ObjectId("66f0e409c53f9c82acf91051"),
					"adProviderName" : "Moloco_DEAL",
					"reportApiType" : "MOLOCO",
					"offset" : NumberInt(-2),
					"reportApiStatus" : "ON",
					"date" : "20250131",
					"state" : "COMPLETE",
					"expectedCompleteDate" : ISODate("2025-01-31T05:10:00.000+0900")
				}
			]

			state
				NOT_SPECIFIED: "NOT_SPECIFIED", // AP 리포트 연동 정보(AdProviders.reportApi.period.end)가 기술되어 있지 않음
				NOT_REGISTERED_ALL: "NOT_REGISTERED_ALL", // 모든 AP 리포트 스케줄이 없음. 오늘자로 등록된 AP 스케줄이 하나도 없음
				NOT_REGISTERED: "NOT_REGISTERED", // AP 리포트 스케줄이 없음. 이 AP에 해당하는 스케줄이 없음.
				OFF: "OFF", // AdProviderInfos.reportApiStatus = 'OFF'
				READY: "READY", // BatchReportJobSchedule.state = 'READY'
				WAIT: "WAIT", // BatchReportJobSchedule.state = 'READY' (배치 처리 대상이지만 아직 READY인 경우가 있는데(스케쥴 순서 상), 두 번 요청으로 인해 이중으로 연동 처리되지 않도록 WAIT 상태를 둠)
				IN_PROGRESS: "IN_PROGRESS", // BatchReportJobSchedule.state = 'IN_PROGRESS'
				COMPLETE: "COMPLETE", // BatchReportJobSchedule.state = 'COMPLETE'
				FAILED: "FAILED", // BatchReportJobSchedule.state = 'FAILED'
		 */

		// READY, WAIT, IN_PROGRESS, FAILED인 것만 필터링
		const sgDetailsFilter = silvergreyDetails.filter(sgDetail =>
			[SilvergreyState.READY, SilvergreyState.WAIT, SilvergreyState.IN_PROGRESS, SilvergreyState.FAILED].includes(sgDetail.state)
		).map(sgDetail => _.pick(sgDetail, ['adProvider_id', 'date']));

		/*
			adProvider_id로 그룹핑

			groupedSgDetails = {
			  "5b7a620277bd856e48012a8f": [
				{
				  "adProvider_id": "5b7a620277bd856e48012a8f",
				  "date": "20250128"
				},
				{
				  "adProvider_id": "5b7a620277bd856e48012a8f",
				  "date": "20250129"
				},
				{
				  "adProvider_id": "5b7a620277bd856e48012a8f",
				  "date": "20250130"
				}
			  ]
			}
		 */
		const groupedSgDetails = _.groupBy(sgDetailsFilter, 'adProvider_id');
		Logger.debug(`groupedSgDetails=${JSON.stringify(groupedSgDetails, null, 2)}`);

		/*
			groupedSgDetails를 아래와 같은 구조로 리매핑

			missingSgDetails = {
			  "adProvider_id": "5b7a620277bd856e48012a8f",
			  "missingDates": ["20250128", "20250129", "20250130"]
			}
		 */
		const missingSgDetails = _.map(groupedSgDetails, (sgDetails, adProvider_id) => {
			const dates = sgDetails.map(sgDetail => sgDetail.date);
			return { adProviderId: adProvider_id, dates };
		});
		Logger.debug(`missingSgDetails=${JSON.stringify(missingSgDetails, null, 2)}`);

		return missingSgDetails;
	}

	/**
	 * 수익쉐어 리포트 상태 조회
	 *
	 * @param schedule_id
	 * @param report_id
	 */
	async status(schedule_id: Types.ObjectId, date: string) {
		const { state, taskState } = await this.getRevenueSharingReport({ schedule_id, date });

		if (_.isEqual(ReportState.FAILURE, state)) {
			return { state, errorMessage: taskState };
		} else {
			// READY, IN_PROGRESS, COMPLETE 인 경우
			return { state };
		}
	}

	/**
	 * 수익쉐어 리포트 다운로드
	 *
	 * @param publisher_id
	 * @param schedule_id
	 */
	async download(schedule_id: Types.ObjectId, date: string) {
		const { filePath } = await this.getRevenueSharingReport({ schedule_id, date });

		return this.nubesClient.download(filePath);
	}

	/**
	 * 파일이 누베스에 있는지 확인
	 * @param filePath
	 */
	async checkNubesFilePathExists(filePath: string) {
		try {
			await this.nubesClient.stat(filePath);
			return true;
		} catch (err) {
			return false;
		}
	}

	/**
	 * 수익쉐어 리포트 조회
	 * @param filter
	 * @param projection
	 */
	async getRevenueSharingReport(filter: FilterQuery<RevenueSharingReportDocument>, projection: any = {}) {
		return this.revenueSharingReportModel.findOne(filter, projection);
	}

	async getRevenueSharingReports(filter: FilterQuery<RevenueSharingReportDocument>, projection: any = {}) {
		return this.revenueSharingReportModel.find(filter, projection);
	}

	/**
	 * 매체 정보로 수익쉐어 리포트 조회
	 * @param publisher_id
	 * @param report_ids
	 */
	async getRevenueSharingReportsByPublisherId(publisher_id: Types.ObjectId, report_ids: Types.ObjectId[]) {
		return await this._getReports(report_ids, { '$match': { 'pub._id': publisher_id } });
	}

	async getRevenueSharingReportsByPublisherCd(publisherCd: string, report_ids: Types.ObjectId[]) {
		return await this._getReports(report_ids, { '$match': { 'pub.publisherCd': publisherCd } });
	}

	private async _getReports(report_ids: Types.ObjectId[], secondMatch: any) {
		const pipeline = [
			{ '$match': { '_id': { '$in': report_ids } } },
			{ '$lookup': { 'from': 'Schedules', 'localField': 'schedule_id', 'foreignField': '_id', 'as': 'sch' } },
			{ '$unwind': { path: '$sch', preserveNullAndEmptyArrays: true } },
			{
				'$lookup': {
					'from': 'SyncPublishers',
					'localField': 'sch.publisher_id',
					'foreignField': '_id',
					'as': 'pub'
				}
			},
			{ '$unwind': { path: '$pub', preserveNullAndEmptyArrays: true } },
			secondMatch,
			{ '$project': { '_id': 1, 'sch._id': 1, 'sch.name': 1, 'sch.publisher_id': 1, 'pub._id': 1, 'pub.name': 1 } },
		];
		const results = await this.revenueSharingReportModel.aggregate(pipeline, { allowDiskUse: true });
		return results;
	}

	/**
	 * 매체 정보 조회
	 * @param publisherCd
	 */
	async getPublisher(publisherCd: string) {
		return this.syncPublisherModel.findOne({ publisherCd }, { _id: 1 });
	}
}
