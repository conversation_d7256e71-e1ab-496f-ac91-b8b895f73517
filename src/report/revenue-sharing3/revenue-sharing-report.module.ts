import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { API_DB_NAME, CMS_DB_NAME } from '../../db/constants';

import { ApiUserModel } from '../schema/api-user.schema';
import { EnvironmentModel } from '../schema/environment.schema';
import { ScheduleModel } from '../schema/schedule.schema';
import { SyncPublisherModel } from '../schema/sync-publisher.schema';

import { RevenueSharingReportController } from './revenue-sharing-report.controller';
import { RevenueSharingReportService } from './revenue-sharing-report.service';
import { RevenueSharingReportModel } from '../schema/revenue-sharing-report.schema';

@Module({
	imports: [
		MongooseModule.forFeature([
			ApiUserModel,
		], CMS_DB_NAME),
		MongooseModule.forFeature([
			EnvironmentModel,
			SyncPublisherModel,
			ScheduleModel,
			RevenueSharingReportModel,
		], API_DB_NAME),
	],
	controllers: [RevenueSharingReportController],
	providers: [RevenueSharingReportService],
	exports: []
})
export class RevenueSharingReportModule {
}
