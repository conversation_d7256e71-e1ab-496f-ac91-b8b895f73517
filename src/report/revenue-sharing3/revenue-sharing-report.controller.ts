/**
 * CMS와 파트너에게 수익쉐어 리포트 관련된 API를 제공하는 Controller
 */

import {
	Controller,
	Get,
	Logger,
	Param,
	Query,
	Res,
	StreamableFile,
	UseFilters,
	UseGuards,
	UsePipes,
	ValidationPipe,
	Version
} from '@nestjs/common';
import { Response } from "express";
import path from "path";
import { Model, Types } from 'mongoose';
import { InjectModel } from "@nestjs/mongoose";
import moment from "moment/moment";
import _ from "lodash";

import { ObjectIdPipe } from '../../pipe/object-id.pipe';
import { PublisherIdPipe } from '../../pipe/publisher-id.pipe';
import { ObjectIdArrayPipe } from '../../pipe/object-id-array.pipe';
import { PublisherCdPipe } from "../../pipe/publisher-cd.pipe";
import { AuthorizationGuard } from '../../guard/authorization.guard';
import { HttpExceptionFilter } from '../../exception/http-exception.filter';

import { SyncPublisher, SyncPublisherDocument } from "../schema/sync-publisher.schema";
import { Schedule, ScheduleDocument } from "../schema/schedule.schema";
import { RevenueSharingReport, RevenueSharingReportDocument } from "../schema/revenue-sharing-report.schema";

import { RsrMethodType } from "./revenue-sharing-report.enums";
import { RevenueSharingReportService } from './revenue-sharing-report.service';
import { RevenueSharingReportValidator } from './revenue-sharing-report.validator';
import { RevenueSharingReportValidationPipe } from './revenue-sharing-report-validation.pipe';

@Controller('revenue-sharing')
@UseGuards(AuthorizationGuard)
@UseFilters(HttpExceptionFilter)
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class RevenueSharingReportController {
	private readonly logger = new Logger(RevenueSharingReportController.name);

	constructor(
		@InjectModel(SyncPublisher.name)
		private SyncPublisherModel: Model<SyncPublisherDocument>,
		@InjectModel(Schedule.name)
		private ScheduleModel: Model<ScheduleDocument>,
		@InjectModel(RevenueSharingReport.name)
		private RevenueSharingReportModel: Model<RevenueSharingReportDocument>,
		private readonly revenueSharingReportService: RevenueSharingReportService) {
	}

	/*
		[참고사항] params 파라미터에 CustomParams 타입 대신 any 로 정의한 이유
			CustomParams 타입으로 정의할 경우, groups 가 적용 되지 않은 상태로 validation 이 1차 적용 된다.
			groups 가 적용 되지 않으면 validation 이 원하는 방식으로 동작하지 않는다.
			어차피 CustomValidationPipe 에서 validation 을 따로 적용 하기 때문에, 타입을 any 로 받아도 문제가 없다.
	*/

	/**
	 * CMS API - 수익쉐어 리포트 리스트 조회
	 *
	 * Spec : https://wiki.navercorp.com/spaces/GFP/pages/**********/01.+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+for+CMS#id-01.%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8APIforCMS-1.%EB%A6%AC%ED%8F%AC%ED%8A%B8%EB%AA%A9%EB%A1%9D%EC%A1%B0%ED%9A%8C
	 *
	 * @param userId
	 * @param params { publisherId, reportId?, scheduleName?, frequency?, granularity?, startDate, endDate, pageNo, pageSize }
	 */
	@Version('3')
	@Get('/list')
	async list(@Query(new RevenueSharingReportValidationPipe([RsrMethodType.LIST]), PublisherIdPipe) params: any) {
		this.logger.debug(`RevenueSharingReportController.list() 호출`);
		this.logger.debug(`params = ${JSON.stringify(params)}`);

		// 리포트 목록 조회
		return { data: await this.revenueSharingReportService.list(params) };
	}


	/**
	 * CMS API - 수익쉐어 리포트 파일 정보 조회
	 *
	 * Spec : https://wiki.navercorp.com/spaces/GFP/pages/**********/01.+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+for+CMS#id-01.%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8APIforCMS-2.%EB%A6%AC%ED%8F%AC%ED%8A%B8%ED%8C%8C%EC%9D%BC%EC%A0%95%EB%B3%B4%EC%A1%B0%ED%9A%8C
	 *
	 * @param userId
	 * @param publisher_id
	 * @param report_ids
	 */
	@Version('3')
	@Get('/files')
	async files(
		@Query('publisherId', PublisherIdPipe, ObjectIdPipe) publisher_id: Types.ObjectId,
		@Query('reportIds', ObjectIdArrayPipe) report_ids: Types.ObjectId[]) {
		this.logger.debug(`RevenueSharingReportController.files() 호출`);
		this.logger.debug(`publisher_id = ${publisher_id}, report_ids = ${report_ids}`);

		// [Validation] 이 publisher_id에 해당하는 report_ids 인지 검증
		const results = await this.revenueSharingReportService.getRevenueSharingReportsByPublisherId(publisher_id, report_ids);
		RevenueSharingReportValidator.validateReportAuth(results);

		// 리포트 ID에 대한 파일 정보 조회
		const reports = await this.revenueSharingReportService.files(publisher_id, report_ids);

		// [Validation] report 개수
		RevenueSharingReportValidator.validateReportCount(report_ids, reports);

		return { data: reports };
	}

	/**
	 * CMS API - 월별 수익쉐어 리포트 집계 현황 조회
	 *
	 * Spec : https://wiki.navercorp.com/spaces/GFP/pages/**********/01.+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+for+CMS#id-01.%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8APIforCMS-3.%EC%9B%94%EB%B3%84%EB%A6%AC%ED%8F%AC%ED%8A%B8%EC%A7%91%EA%B3%84%ED%98%84%ED%99%A9%EC%A1%B0%ED%9A%8C
	 *
	 * @param userId
	 * @param publisher_id
	 * @param report_ids
	 */
	@Version('3')
	@Get('/aggstatus')
	async aggStatus(@Query(new RevenueSharingReportValidationPipe([RsrMethodType.AGGSTATUS]), PublisherIdPipe) params: any) {
		this.logger.debug(`RevenueSharingReportController.aggStatus() 호출`);
		this.logger.debug(`params = ${params}`);

		// [Validation 1] startDate, endDate 검증. 두 날짜가 뒤바뀌면 안됨.
		RevenueSharingReportValidator.validatePeriodDate(params);

		// [Validation 2] startDate 검증. 최근 1년 안의 리포트만 현황 제공할 수 있음
		RevenueSharingReportValidator.validateAggStatusDate(params.startDate);

		// 집계 현황 조회
		return { data: await this.revenueSharingReportService.aggStatus(params) };
	}

	/**
	 * 파트너 API - 수익쉐어 리포트 상태 조회
	 *
	 * GFP Spec : https://wiki.navercorp.com/spaces/GFP/pages/**********/GFP+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+%EC%97%B0%EB%8F%99+%EA%B0%80%EC%9D%B4%EB%93%9C+-+V.3.0#GFP%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8API%EC%97%B0%EB%8F%99%EA%B0%80%EC%9D%B4%EB%93%9CV.3.0-1.%EB%A6%AC%ED%8F%AC%ED%8A%B8%EC%83%81%ED%83%9C%ED%99%95%EC%9D%B8
	 * NAM Spec :https://wiki.navercorp.com/spaces/GFP/pages/3289711358/NAM+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+%EC%97%B0%EB%8F%99+%EA%B0%80%EC%9D%B4%EB%93%9C+-+V.3.0#NAM%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8API%EC%97%B0%EB%8F%99%EA%B0%80%EC%9D%B4%EB%93%9CV.3.0-1.%EB%A6%AC%ED%8F%AC%ED%8A%B8%EC%83%81%ED%83%9C%ED%99%95%EC%9D%B8
	 *
	 * @param publisherCd header에 설정된 client_id와 publisherCd를 이용해 AuthorizationGuard 에서 권한 체크
	 * @param params
	 */
	@Version('3')
	@Get('/status/:publisherCd')
	async status(
		@Param('publisherCd', PublisherCdPipe) publisherCd: string,
		@Query(new RevenueSharingReportValidationPipe([RsrMethodType.PARTNER])) params: any) {
		this.logger.debug(`RevenueSharingReportController.status() 호출 (publisherCd= ${publisherCd}, schedule_id= ${params.schedule_id}, date=${params.date})`);

		// [Validation 1] scheduleId가 publisherCd에 해당하는 스케줄인지(권한이 있는지)
		const isAuthorized = await this._isAuthorizedSchedule(publisherCd, params.schedule_id);
		RevenueSharingReportValidator.validateScheduleId(isAuthorized);

		// [Validation 2] scheduleId, date 에 해당 하는 리포트가 있는지
		const report = await this.revenueSharingReportService.getRevenueSharingReport({
			schedule_id: params.schedule_id,
			date: params.date
		});
		RevenueSharingReportValidator.validateReport(report);

		// 리포트 상태 조회
		return await this.revenueSharingReportService.status(params.schedule_id, params.date);
	}

	/**
	 * 파트너 API - 수익쉐어 리포트 다운로드
	 *
	 * GFP Spec : https://wiki.navercorp.com/spaces/GFP/pages/**********/GFP+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+%EC%97%B0%EB%8F%99+%EA%B0%80%EC%9D%B4%EB%93%9C+-+V.3.0#GFP%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8API%EC%97%B0%EB%8F%99%EA%B0%80%EC%9D%B4%EB%93%9CV.3.0-2.%EB%A6%AC%ED%8F%AC%ED%8A%B8%EB%8B%A4%EC%9A%B4%EB%A1%9C%EB%93%9C
	 * NAM Spec :https://wiki.navercorp.com/spaces/GFP/pages/3289711358/NAM+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+%EC%97%B0%EB%8F%99+%EA%B0%80%EC%9D%B4%EB%93%9C+-+V.3.0#NAM%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8API%EC%97%B0%EB%8F%99%EA%B0%80%EC%9D%B4%EB%93%9CV.3.0-2.%EB%A6%AC%ED%8F%AC%ED%8A%B8%EB%8B%A4%EC%9A%B4%EB%A1%9C%EB%93%9C
	 *
	 * @param publisherCd header에 설정된 client_id와 publisherCd를 이용해 AuthorizationGuard 에서 권한 체크
	 * @param params
	 * @param res
	 */
	@Version('3')
	@Get('download/:publisherCd')
	async download(
		@Param('publisherCd', PublisherCdPipe) publisherCd: string,
		@Query(new RevenueSharingReportValidationPipe([RsrMethodType.PARTNER])) params: any,
		@Res({ passthrough: true }) res: Response) {
		this.logger.debug(`RevenueSharingReportController.download() 호출 (publisherCd= ${publisherCd}, schedule_id= ${params.schedule_id}, date=${params.date})`);

		// [Validation 1] scheduleId가 publisherCd에 해당하는 스케줄인지(권한이 있는지)
		const isAuthorized = await this._isAuthorizedSchedule(publisherCd, params.schedule_id);
		RevenueSharingReportValidator.validateScheduleId(isAuthorized);

		// [Validation 2] scheduleId, date 에 해당 하는 리포트가 있는지
		const report = await this.revenueSharingReportService.getRevenueSharingReport({
			schedule_id: params.schedule_id,
			date: params.date
		});
		RevenueSharingReportValidator.validateReport(report);

		// [Validation 3] 리포트가 생성 완료 되었는지
		RevenueSharingReportValidator.validateReportState(report);

		// [Validation 4] 리포트가 Nubes 에 존재하는지
		const exist = await this.revenueSharingReportService.checkNubesFilePathExists(report.filePath);
		RevenueSharingReportValidator.validateReportFileExistInNubes(report.filePath, exist);

		// 리포트 다운로드
		const stream = await this.revenueSharingReportService.download(params.schedule_id, params.date);
		res.set({
			'Content-Type': 'text/csv',
			'Content-Disposition': `attachment; filename="${path.basename(report.filePath)}"`,
		});

		return new StreamableFile(stream);
	}

	/**
	 * 파트너 API - 월별 수익쉐어 리포트 집계 현황 조회
	 *
	 * GFP Spec : https://wiki.navercorp.com/spaces/GFP/pages/**********/GFP+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+%EC%97%B0%EB%8F%99+%EA%B0%80%EC%9D%B4%EB%93%9C+-+V.3.0#GFP%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8API%EC%97%B0%EB%8F%99%EA%B0%80%EC%9D%B4%EB%93%9CV.3.0-3.%EC%9B%94%EB%B3%84%EB%A6%AC%ED%8F%AC%ED%8A%B8%EC%A7%91%EA%B3%84%ED%98%84%ED%99%A9%EC%A1%B0%ED%9A%8C
	 * NAM Spec : https://wiki.navercorp.com/spaces/GFP/pages/3289711358/NAM+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+%EC%97%B0%EB%8F%99+%EA%B0%80%EC%9D%B4%EB%93%9C+-+V.3.0#NAM%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8API%EC%97%B0%EB%8F%99%EA%B0%80%EC%9D%B4%EB%93%9CV.3.0-3.%EC%9B%94%EB%B3%84%EB%A6%AC%ED%8F%AC%ED%8A%B8%EC%A7%91%EA%B3%84%ED%98%84%ED%99%A9%EC%A1%B0%ED%9A%8C
	 *
	 * @param userId
	 * @param publisher_id
	 * @param report_ids
	 */
	@Version('3')
	@Get('/aggstatus/:publisherCd')
	async aggStatusForOutside(
		@Param('publisherCd', PublisherCdPipe) publisherCd: string,
		@Query(new RevenueSharingReportValidationPipe([RsrMethodType.PARTNER])) params: any) {
		this.logger.debug(`RevenueSharingReportController.aggStatusForOutside() 호출`);

		// 외부 API는 startDate, endDate를 받지 않고 date만 받으므로 date를 기준으로 startDate, endDate 설정
		const dt = moment(params.date, params.date.length == 6 ? 'YYYYMM' : 'YYYYMMDD');
		params.startDate = dt.clone().startOf('month').format('YYYYMMDD');
		params.endDate = dt.clone().endOf('month').format('YYYYMMDD');

		// publisherCd에 해당하는 publisher_id 설정
		const pub = await this.revenueSharingReportService.getPublisher(publisherCd);
		params.publisherId = pub._id.toHexString();

		// [Validation 1] scheduleId가 publisherCd에 해당하는 스케줄인지(권한이 있는지)
		const isAuthorized = await this._isAuthorizedSchedule(publisherCd, params.schedule_id);
		RevenueSharingReportValidator.validateScheduleId(isAuthorized);

		// [Validation 2] date 검증. 최근 1년 안의 리포트만 현황 제공할 수 있음
		RevenueSharingReportValidator.validateAggStatusDate(params.startDate);

		// 집계 현황 조회
		return { data: await this.revenueSharingReportService.aggStatus(params) };
	}

	private async _isAuthorizedSchedule(publisherCd: string, schedule_id: Types.ObjectId) {
		const pub = await this.SyncPublisherModel.findOne({ publisherCd }, { _id: 1, name: 1 });
		const sch = await this.ScheduleModel.findOne({ _id: schedule_id, publisher_id: pub._id }, { _id: 1 });

		return !_.isEmpty(sch);
	}
}
