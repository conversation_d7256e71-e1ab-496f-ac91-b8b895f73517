import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ExchangeRateAverageDocument, ExchangeRateAverageModel } from '../schema/exchange-rate-average.schema';
import moment from 'moment';

@Injectable()
export class ExchangeRateService {
	private readonly logger = new Logger(ExchangeRateService.name);

	constructor(@InjectModel(ExchangeRateAverageModel.name)
		private exchangeRateAveragModel: Model<ExchangeRateAverageDocument>,
	) {}

	async applyExchangeRateKrwIntoUsd(data, names) {
		const today = moment().format('YYYYMMDD');
		const rate = await this.getAverageExchangeRateOfKrwIntoUsd(today);

		this.logger.debug(`-------- before converting:${JSON.stringify(data, null, 2)}`);
		const result = data.map(datum => {
			// datum['estimatedRevenue'] = datum['estimatedRevenue'] * rate;
			// datum['ecpm'] = datum['ecpm'] * rate;
			names.forEach(name => {
				datum[name] = datum[name] * rate
				return datum;
			});
			return datum;
		});
		// this.logger.debug(`-------- converted:${JSON.stringify(result, null, 2)}`);

		return result;
	}

	async getAverageExchangeRateOfKrwIntoUsd(date) {
		const filter = {
			endDate: { '$lte': date},
			currencyCdFrom: 'KRW',
			currencyCdTo: 'USD',
		};
		const projection = { exchangeRate: 1};
		const doc: ExchangeRateAverageDocument = await this.exchangeRateAveragModel.findOne(filter, projection).lean();
		this.logger.debug(`-------------exchangeRate doc:${JSON.stringify(doc, null, 2)}`);
		this.logger.debug(`-------------exchangeRate rate:${doc.exchangeRate}`);
		return doc.exchangeRate;
	}

}
