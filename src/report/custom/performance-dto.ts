import { IsObject, IsOptional, IsArray, ValidateNested, IsIn, ArrayMinSize, ValidationOptions, registerDecorator, ValidationArguments, IsNotEmpty } from 'class-validator';
import { Expose, Transform, Type } from 'class-transformer';

import _ from 'lodash';

import { ArrayDistinct } from '../../validator/array-distinct.validator';

import { toArrayUppercase } from '../../util/util';
import { toStringReplacedErrorCode } from '../../error/error-util';

import { CommonReportErrorCode } from '../../error/error-code';

import { CustomParams, createUpdateGroups, OidOperations, Operations, CustomFilters } from './custom-dto';

import {
	PERF_DIMENSIONS, PerfDimensions,
	PERF_METRICS, PerfMetrics,
} from './custom.enums';

import { CommonCodeService } from '../../common-code/common-code.service';


function IsInParentProperty(validationOptions?: ValidationOptions): Function {
	const enumType = {
		placeChannelType: 'channelType',
		placeProductType: 'productType',
		placeCreativeType: 'creativeType',
		deviceOs: 'deviceOs',
		responseCreativeType: 'responseCreativeType',
	};

	// 적용 대상 :: EnumType 의 PerformanceFilters property
	// 	- parentProperty: { property: ['V1'] }
	// constraints enumType
	// 	- { property : ['V1', 'V2', ... ]}
	return (object: Object, propertyName: string): void => {
		registerDecorator({
			name: 'IsInParentProperty',
			target: object.constructor,
			async: false,
			propertyName,
			constraints: [],
			options: validationOptions,
			validator: {
				validate(value: any, args: ValidationArguments) {
					const obj = args.object as any;

					const commonCode = CommonCodeService.getInstance().getCommonCode(enumType[obj.parentProperty]);

					if (_.isEmpty(value) || _.isEmpty(commonCode)) { return true; }

					return _.includes(commonCode, value);
				},
				defaultMessage : toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS)
			},
		});
	};
}


export class EnumOperations<T> extends Operations<T> {
	@IsInParentProperty({ each: true, groups: createUpdateGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: createUpdateGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@IsOptional({ groups: createUpdateGroups })
	in?: T[];

	@IsInParentProperty({ each: true, groups: createUpdateGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: createUpdateGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@IsOptional({ groups: createUpdateGroups })
	notIn?: T[];
}

export class PerformanceFilters extends CustomFilters {
	@Expose()
	@Transform(({ obj }) => _.isObject(obj.adProviderId) && !_.isArray(obj.adProviderId) ? Object.assign(new OidOperations<string>('adProviderId'), obj.adProviderId) : obj.adProviderId)
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	adProviderId?: OidOperations<string>;

	@Expose()
	@Transform(({ obj }) => _.isObject(obj.dealId) && !_.isArray(obj.dealId) ? Object.assign(new Operations<string>('dealId'), obj.dealId) : obj.dealId)
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	dealId?: Operations<string>;

	@Expose()
	@Transform(({ obj }) => _.isObject(obj.biddingGroupId) && !_.isArray(obj.biddingGroupId) ? Object.assign(new OidOperations<string>('biddingGroupId'), obj.biddingGroupId) : obj.biddingGroupId)
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	biddingGroupId?: OidOperations<string>;

	@Expose()
	@Transform(({ obj }) => {
		if (_.isObject(obj.placeChannelType) && !_.isArray(obj.placeChannelType)) {
			obj.placeChannelType.in = toArrayUppercase(obj.placeChannelType.in);
			obj.placeChannelType.notIn = toArrayUppercase(obj.placeChannelType.notIn);

			return Object.assign(new EnumOperations<string>('placeChannelType'), obj.placeChannelType);
		} else {
			return obj.placeChannelType;
		}
	})
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	placeChannelType?: EnumOperations<string>;

	@Expose()
	@Transform(({ obj }) => {
		if (_.isObject(obj.placeProductType) && !_.isArray(obj.placeProductType)) {
			obj.placeProductType.in = toArrayUppercase(obj.placeProductType.in);
			obj.placeProductType.notIn = toArrayUppercase(obj.placeProductType.notIn);

			return Object.assign(new EnumOperations<string>('placeProductType'), obj.placeProductType);
		} else {
			return obj.placeProductType;
		}
	})
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	placeProductType?: EnumOperations<string>;

	@Expose()
	@Transform(({ obj }) => {
		if (_.isObject(obj.placeCreativeType) && !_.isArray(obj.placeCreativeType)) {
			obj.placeCreativeType.in = toArrayUppercase(obj.placeCreativeType.in);
			obj.placeCreativeType.notIn = toArrayUppercase(obj.placeCreativeType.notIn);

			return Object.assign(new EnumOperations<string>('placeCreativeType'), obj.placeCreativeType);
		} else {
			return obj.placeCreativeType;
		}
	})
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	placeCreativeType?: EnumOperations<string>;

	@Expose()
	@Transform(({ obj }) => {
		if (_.isObject(obj.deviceOs) && !_.isArray(obj.deviceOs)) {
			obj.deviceOs.in = toArrayUppercase(obj.deviceOs.in);
			obj.deviceOs.notIn = toArrayUppercase(obj.deviceOs.notIn);

			return Object.assign(new EnumOperations<string>('deviceOs'), obj.deviceOs);
		} else {
			return obj.deviceOs;
		}
	})
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	deviceOs?: EnumOperations<string>;

	@Expose()
	@Transform(({ obj }) => {
		if (_.isObject(obj.responseCreativeType) && !_.isArray(obj.responseCreativeType)) {
			obj.responseCreativeType.in = toArrayUppercase(obj.responseCreativeType.in);
			obj.responseCreativeType.notIn = toArrayUppercase(obj.responseCreativeType.notIn);

			return Object.assign(new EnumOperations<string>('responseCreativeType'), obj.responseCreativeType);
		} else {
			return obj.responseCreativeType;
		}
	})
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	responseCreativeType?: EnumOperations<string>;
}

export class PerformanceParams extends CustomParams {
	@IsIn(PerfDimensions, { each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_DIMENSIONS, false), groups: createUpdateGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: createUpdateGroups })
	@ArrayMinSize(1, { message: toStringReplacedErrorCode(CommonReportErrorCode.AT_LEAST_ONE_VALUE), groups: createUpdateGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: createUpdateGroups })
	dimensions: PERF_DIMENSIONS[];

	@IsIn(PerfMetrics, { each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_METRICS, false), groups: createUpdateGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: createUpdateGroups })
	@ArrayMinSize(1, { message: toStringReplacedErrorCode(CommonReportErrorCode.AT_LEAST_ONE_VALUE), groups: createUpdateGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: createUpdateGroups })
	metrics: PERF_METRICS[];

	@Type(() => PerformanceFilters)
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: createUpdateGroups })
	@IsObject({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: createUpdateGroups })
	@IsOptional({ groups: createUpdateGroups })
	filters?: PerformanceFilters;
}
