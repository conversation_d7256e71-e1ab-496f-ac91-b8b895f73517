import { BadRequestException, Injectable } from '@nestjs/common';

import _ from 'lodash';
import moment from 'moment';
import { Types } from 'mongoose';

import { BusinessException } from '../../exception/business-exception';
import { getReplacedErrorCode } from '../../error/error-util';
import { CommonReportErrorCode, CustomReportErrorCode } from '../../error/error-code';

import { CustomParams } from './custom-dto';

import { CustomReportDocument } from '../schema/custom-report.schema';
import { AdProvider, AdUnitRequests, AP_TIMEZONE, CmsType, EstimatedMetrics, ReportState } from '../../enum/report.enum';
import { AdProviderGroup, PerfValidationField, TimeUnits, TimeUnit } from './custom.enums';
import { SyncPublisherDocument } from '../schema/sync-publisher.schema';

@Injectable()
export class CustomValidator {
	constructor() {}

	/**
	 * 날짜 검증 (생성/수정)
	 * 	- 시작일이 종료일 보다 이후인 경우, 생성/수정 불가
	 * 	- 종료일이 오늘자 보다 이후인 경우, 생성/수정 불가
	 */
	static validateDate({ startDate, endDate }: CustomParams) {
		const start = moment(startDate, 'YYYYMMDD');
		const end = moment(endDate, 'YYYYMMDD');

		// 시작일은 종료일 보다 이후일 수 없음
		if (start.isAfter(end)) {
			throw new BadRequestException([CommonReportErrorCode.START_DATE_BEFORE_END_DATE]);
		}

		// 종료일은 오늘자 보다 이후일 수 없음
		if (end.isSameOrAfter(moment().startOf('day'))) {
			throw new BadRequestException([CommonReportErrorCode.END_DATE_BEFORE_TODAY]);
		}
	}

	/**
	 * TimeUnit 검증 (생성/수정)
	 * 	- TimeUnit 이 하나도 없는 경우, 생성/수정 불가
	 * 	- TimeUnit 이 여러 개인 경우, 생성/수정 불가
	 * 	- TimeUnit 별 조회기간 범위 확인
	 * 		- 시간별 리포트는 조회기간 7일을 넘을 수 없음
	 * 		- 일별 리포트는 조회기간 3개월을 넘을 수 없음
	 * 		- 월별 리포트는 조회기간 1년을 넘을 수 없음
	*/
	static validateTimeUnit({ dimensions, startDate, endDate }: CustomParams) {
		const start = moment(startDate, 'YYYYMMDD');
		const end = moment(endDate, 'YYYYMMDD');

		const timeUnits = dimensions.filter(value => TimeUnits.includes(value));

		// TimeUnit 이 하나도 없는 경우
		if (timeUnits.length < 1) {
			throw new BadRequestException([CustomReportErrorCode.REQUIRED_TIME_UNIT]);
		}

		// TimeUnit 이 여러 개인 경우
		if (timeUnits.length > 1) {
			throw new BadRequestException([CustomReportErrorCode.ALLOWED_ONLY_ONE_TIME_UNIT]);
		}

		const timeUnit = timeUnits.pop();

		// TimeUnit 별 조회기간 범위 확인
		// 	- 시간별 리포트는 조회기간 7일을 넘을 수 없음
		// 	- 일별 리포트는 조회기간 3개월을 넘을 수 없음
		// 	- 월별 리포트는 조회기간 1년을 넘을 수 없음
		const queryPeriod = { month: 12, date: 3, hour: 7 };
		if (_.isEqual(timeUnit, TimeUnit.hour)) {
			const validEnd = start.add(7, 'days').subtract(1, 'hours');
			if (end.isAfter(validEnd)) {
				throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.CANNOT_EXCEED_PERIOD, { value: 7, unit: 'days', queryPeriod })]);
			}
		} else if (_.isEqual(timeUnit, TimeUnit.date)) {
			const validEnd = start.add(3, 'months').subtract(1, 'days');
			if (end.isAfter(validEnd)) {
				throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.CANNOT_EXCEED_PERIOD, { value: 3, unit: 'months', queryPeriod })]);
			}
		} else if (_.isEqual(timeUnit, TimeUnit.month)) {
			const validEnd = start.add(12, 'months').subtract(1, 'days');
			if (end.isAfter(validEnd)) {
				throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.CANNOT_EXCEED_PERIOD, { value: 12, unit: 'months', queryPeriod })]);
			}
		}
	}

	/**
	 * reportId 에 해당 하는 리포트가 없는 경우, 수정/삭제/상세 조회/파일 목록 조회 불가
	*/
	static validateReport(report: CustomReportDocument) {
		if (_.isEmpty(report)) {
			throw new BadRequestException([CommonReportErrorCode.REPORT_NOT_EXIST]);
		}
	}

	/**
	 * 리포트 처리 상태(state)가 READY 가 아닌 경우, 수정 불가
	*/
	static validateReportStateForUpdate(report: CustomReportDocument) {
		if (!_.isEqual(ReportState.READY, report.state)) {
			throw new BusinessException([CustomReportErrorCode.CANNOT_UPDATE_REPORT]);
		}
	}

	/**
	 * 리포트 처리 상태(state)가 IN_PROGRESS 인 경우, 삭제 불가
	*/
	static validateReportStateForDelete(report: CustomReportDocument) {
		if (_.isEqual(ReportState.IN_PROGRESS, report.state)) {
			throw new BusinessException([CustomReportErrorCode.CANNOT_DELETE_REPORT]);
		}
	}

	/**
	 * report count 검증 ( 파일 목록 조회 )
	 * 	- reportIds 가 20개를 넘은 경우, 파일 목록 조회 불가
	 * 	- reportIds 개수와 reports 개수가 일치 하지 않는 경우, 파일 목록 조회 불가
	 */
	static validateReportCount(report_ids: Types.ObjectId[], reports: CustomReportDocument[]) {
		const idCount = new Set(report_ids.map(_.toString)).size;

		if (idCount > 20) {
			throw new BadRequestException([CustomReportErrorCode.EXCEEDED_QUERYABLE_REPORT_COUNT]);
		}

		if (idCount !== reports.length) {
			throw new BadRequestException([CommonReportErrorCode.REPORT_NOT_EXIST]);
		}
	}

	/**
	 * 맞춤 성과 Validation (생성/수정)
	 * 	- 최대 1년까지 조회 가능
	 * 	- GFP 인 경우, adProviderGroup 선택 불가
	 * 	- NAM 인 경우, 추정 지표 선택 불가
	 * 	- AP 타임존인 경우, adUnitRequests 는 adProvider 필수
	 * 	- AP 타임존인 경우, adUnitRequests 는 단독 선택 불가
	 * 	- adUnitRequests 는 adUnit 또는 service 필수
	 * 	- adProviderRequests 는 adProvider 또는 place 필수
	 * 	- adProviderResponses 는 adProvider 또는 place 필수
	 * 	- adProviderGroup 은 adProvider 필수
	*/
	static validatePerformanceReport({ timezone, startDate, dimensions, metrics }: CustomParams, { cmsType }: SyncPublisherDocument) {
		const start = moment(startDate, 'YYYYMMDD');

		// 시작일 확인 - 1년 전 리포트는 조회 불가
		if (start.isBefore(moment().startOf('day').subtract(1, 'years'))) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.QUERYABLE_DATE, { value: 1, unit: 'year' })]);
		}

		// GFP 인 경우, adProviderGroup 선택 불가
		if (_.isEqual(cmsType, CmsType.GFP) && _.includes(dimensions, AdProviderGroup)) {
			throw new BadRequestException([CommonReportErrorCode.INVALID_DIMENSIONS]);
		}

		// NAM 인 경우, 추정 지표 선택 불가
		if (_.isEqual(cmsType, CmsType.NAM) && _.some(metrics, mtr => _.includes(EstimatedMetrics, mtr))) {
			throw new BadRequestException([CommonReportErrorCode.INVALID_METRICS]);
		}

		// AP 타임존인 경우, 광고유닛 요청수 단독 선택 불가
		if (_.isEqual(timezone, AP_TIMEZONE) && metrics.length === 1 && _.includes(metrics, AdUnitRequests)) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.USED_WITH_OTHER_METRICS, { property: AdUnitRequests })]);
		}

		// AP 타임존인 경우, adUnitRequests 는 adProvider 필수
		if (_.isEqual(timezone, AP_TIMEZONE) && _.includes(metrics, PerfValidationField.AP_TIMEZONE.target) &&
			!_.some(dimensions, dim => _.includes(PerfValidationField.AP_TIMEZONE.fields, dim))) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.AP_TIMEZONE_REQUIRED_FIELDS, PerfValidationField.AP_TIMEZONE)]);
		}

		// adUnitRequests 는 adUnit 또는 service 필수
		if (_.includes(metrics, PerfValidationField.AD_UNIT_REQUESTS.target) &&
			!_.some(dimensions, dim => _.includes(PerfValidationField.AD_UNIT_REQUESTS.fields, dim))) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.REQUIRED_AT_LEAST_ONE_FIELDS, PerfValidationField.AD_UNIT_REQUESTS)]);
		}

		// adProviderRequests 는 adProvider 또는 place 필수
		if (_.includes(metrics, PerfValidationField.AD_PROVIDER_REQUESTS.target) &&
			!_.some(dimensions, dim => _.includes(PerfValidationField.AD_PROVIDER_REQUESTS.fields, dim))) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.REQUIRED_AT_LEAST_ONE_FIELDS, PerfValidationField.AD_PROVIDER_REQUESTS)]);
		}

		// adProviderResponses 는 adProvider 또는 place 필수
		if (_.includes(metrics, PerfValidationField.AD_PROVIDER_RESPONSES.target) &&
			!_.some(dimensions, dim => _.includes(PerfValidationField.AD_PROVIDER_RESPONSES.fields, dim))) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.REQUIRED_AT_LEAST_ONE_FIELDS, PerfValidationField.AD_PROVIDER_RESPONSES)]);
		}

		// adProviderGroup 은 adProvider 필수
		if (_.includes(dimensions, AdProviderGroup) && !_.includes(dimensions, AdProvider)) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.REQUIRED_FIELDS, PerfValidationField.AD_PROVIDER_GROUP)]);
		}
	}
}

