import {
	Is<PERSON><PERSON>y,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>efined,
	<PERSON>In,
	IsISO31661<PERSON><PERSON><PERSON>2,
	<PERSON><PERSON>ongoId,
	IsNotEmpty,
	IsN<PERSON>ber,
	IsOptional,
	IsUUID, Length,
	MinLength,
	ValidateIf,
	ValidateNested,
	ValidationArguments
} from 'class-validator';
import { Exclude, Expose, Transform } from 'class-transformer';

import _ from 'lodash';
import { Types } from 'mongoose';

import { IsTimezone } from '../../validator/is-timezone.validator';

// ArrayDistinct vs ArrayUnique
//  - ArrayDistinct 는 항목이 배열 값으로 들어온 경우에 대해서만 검증함 (null 로 들어온 경우 error 발생 안 함)
// 	- ArrayUnique 는 무조건 검증함 ( 항목이 필수인 경우, null 로 들어온 경우도 error 발생) -> ValidateIf 를 붙이면 ArrayDistinct 처럼 처리할 수 있겠음
import { ArrayDistinct } from '../../validator/array-distinct.validator';

import { toArrayUppercase } from '../../util/util';
import { toStringReplacedErrorCode } from '../../error/error-util';
import { CommonReportErrorCode, CustomReportErrorCode } from '../../error/error-code';

import { MethodType as MethodType, CUSTOM_REPORT_TYPE, CustomReportTypes } from './custom.enums';

export const allGroups = [MethodType.CREATE, MethodType.UPDATE, MethodType.LIST];
export const createListGroups = [MethodType.CREATE, MethodType.LIST];
export const createUpdateGroups = [MethodType.CREATE, MethodType.UPDATE];

// 원시 타입용 (string, number, boolean)
export class Operations<T> {
	// parentProperty:: Operations 이 적용된 filters 항목의 이름
	// 	- in, notIn 의 validator message 에 실제 filters 항목의 이름을 표현하기 위한 용도
	// 	- parentProperty 에 따라 적절한 EnumType 을 적용하기 위한 용도
	private parentProperty: string;
	constructor(parentProperty: string) {
		this.parentProperty = parentProperty;
	}

	// in, notIn 모두 없는 경우에 대한 검증
	@ValidateIf(o => _.isNil(o.in) && _.isNil(o.notIn), { groups: createUpdateGroups })
	@IsDefined({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: createUpdateGroups })
	private readonly nilCheck: undefined;

	// in, notIn 둘중 하나가 존재하는데 empty 인 경우에 대한 검증
	@ValidateIf(o => ((_.isNil(o.in) && !_.isNil(o.notIn) && _.isEmpty(o.notIn) && _.isArray(o.notIn)) || (_.isNil(o.notIn) && !_.isNil(o.in) && _.isEmpty(o.in) && _.isArray(o.in))), { groups: createUpdateGroups })
	@IsDefined({ message: toStringReplacedErrorCode(CommonReportErrorCode.AT_LEAST_ONE_VALUE), groups: createUpdateGroups })
	private readonly emptyCheck: undefined;

	// in, notIn 둘다 존재하는 경우에 대한 검증
	@ValidateIf(o => !_.isNil(o.in) && !_.isNil(o.notIn), { groups: createUpdateGroups })
	@IsDefined({ message: toStringReplacedErrorCode(CustomReportErrorCode.ALLOWED_ONLY_ONE_PROPERTY), groups: createUpdateGroups })
	private readonly onlyOneCheck: undefined;


	/* 상속 클래스에서 아래 프로퍼티를 재정의해서 사용할 것 */
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: createUpdateGroups })
	@IsNotEmpty({ each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@IsOptional({ groups: createUpdateGroups })
	in?: T[];

	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: createUpdateGroups })
	@IsNotEmpty({ each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@IsOptional({ groups: createUpdateGroups })
	notIn?: T[];
}

export class OidOperations<T> extends Operations<T> {
	@IsMongoId({ each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: createUpdateGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@IsOptional({ groups: createUpdateGroups })
	in?: T[];

	@IsMongoId({ each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: createUpdateGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@IsOptional({ groups: createUpdateGroups })
	notIn?: T[];
}

class CountryOperations<T> extends Operations<T> {
	@IsISO31661Alpha2({ each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: createUpdateGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@IsOptional({ groups: createUpdateGroups })
	in?: T[];

	@IsISO31661Alpha2({ each: true, message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@ArrayDistinct({ message: toStringReplacedErrorCode(CommonReportErrorCode.DUPLICATED_VALUE), groups: createUpdateGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	@IsOptional({ groups: createUpdateGroups })
	notIn?: T[];
}

/* CustomFilters (자식 Class 포함) 의 프로퍼티에는 반드시 @Expose() 를 적용한다. */
export class CustomFilters {
	@Expose()
	@Transform(({ obj }) => _.isObject(obj.serviceId) && !_.isArray(obj.serviceId) ? Object.assign(new OidOperations<string>('serviceId'), obj.serviceId) : obj.serviceId)
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	serviceId?: OidOperations<string>;

	@Expose()
	@Transform(({ obj }) => _.isObject(obj.adUnitId) && !_.isArray(obj.adUnitId) ? Object.assign(new Operations<string>('adUnitId'), obj.adUnitId) : obj.adUnitId)
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	adUnitId?: Operations<string>;

	@Expose()
	@Transform(({ obj }) => {
		if (_.isObject(obj.country) && !_.isArray(obj.country)) {
			obj.country.in = toArrayUppercase(obj.country.in);
			obj.country.notIn = toArrayUppercase(obj.country.notIn);

			return Object.assign(new CountryOperations<string>('country'), obj.country);
		} else {
			return obj.country;
		}
	})
	@ValidateNested({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FILTERS), groups: createUpdateGroups })
	country?: CountryOperations<string>;
}

export class CustomParams {
	// https://www.typescriptlang.org/docs/handbook/decorators.html#decorator-composition
	// 	- The expressions for each decorator are evaluated top-to-bottom.
	// 	- The results are then called as functions from bottom-to-top.
	// nestjs 처음 실행 시, decorator evaluation 은 정의한 순서대로 발생한다.
	// API 를 호출하면, validation 이 역순으로 실행한다. 따라서 에러 메시지도 역순으로 출력이 된다.

	@IsMongoId({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: allGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: allGroups })
	publisherId: string;

	@IsIn(CustomReportTypes, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: allGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: allGroups })
	type: CUSTOM_REPORT_TYPE;

	@Expose({ groups: [MethodType.UPDATE] })
	@IsMongoId({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: [MethodType.UPDATE] })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [MethodType.UPDATE] })
	reportId: string;

	@MinLength(1, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: [MethodType.LIST] })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: createUpdateGroups })
	@IsOptional({ groups: [MethodType.LIST] })
	name: string;

	@IsTimezone({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: allGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: createUpdateGroups })
	@IsOptional({ groups: [MethodType.LIST] })
	timezone: string;

	@IsDateString({ strict: false }, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FORMAT_DATE), groups: allGroups })
	@Length(8, 8, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FORMAT_DATE), groups: allGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: createUpdateGroups })
	@IsOptional({ groups: [MethodType.LIST] })
	startDate: string;

	@IsDateString({ strict: false }, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FORMAT_DATE), groups: allGroups })
	@Length(8, 8, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_FORMAT_DATE), groups: allGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: createUpdateGroups })
	@IsOptional({ groups: [MethodType.LIST] })
	endDate: string;

	@Expose({ groups: createListGroups })
	@IsMongoId({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: createListGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [MethodType.CREATE] })
	@IsOptional({ groups: [MethodType.LIST] })
	creatorId: string;


	/*
		dimensions, metrics, filters 의 decorator 는 자식 Class 에서 재정의 필요함
		decorator 가 없으면, whitelist 로 인해 걸러지므로 아래와 같이 추가함
		MethodType.LIST 인 경우, CustomParams 로 Validation 적용됨
	*/
	@Expose({ groups: createUpdateGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_DIMENSIONS, false), groups: createUpdateGroups })
	dimensions: any[];

	@Expose({ groups: createUpdateGroups })
	@IsArray({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_METRICS, false), groups: createUpdateGroups })
	metrics: any[];

	@Expose({ groups: createUpdateGroups })
	@IsOptional({ groups: createUpdateGroups })
	filters?: CustomFilters;


	@Expose({ groups: [MethodType.LIST] })
	@Transform(({ obj }) => !_.isEmpty(obj.queryPeriodUse) ? (_.isFinite(Number(obj.queryPeriodUse)) ? Number(obj.queryPeriodUse) : obj.queryPeriodUse) : 1)
	@IsIn([0, 1], { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: [MethodType.LIST] })
	@IsOptional({ groups: [MethodType.LIST] })
	queryPeriodUse: number;

	@Expose({ groups: [MethodType.LIST] })
	@Transform(({ obj }) => !_.isEmpty(obj.pageNo) ? (_.isFinite(Number(obj.pageNo)) ? Number(obj.pageNo) : obj.pageNo) : undefined)
	@IsNumber({}, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: [MethodType.LIST] })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [MethodType.LIST] })
	pageNo: number;

	@Expose({ groups: [MethodType.LIST] })
	@Transform(({ obj }) => !_.isEmpty(obj.pageSize) ? (_.isFinite(Number(obj.pageSize)) ? Number(obj.pageSize) : obj.pageSize) : undefined)
	@IsNumber({}, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: [MethodType.LIST] })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [MethodType.LIST] })
	pageSize: number;


	// 참고사항 :: API 를 통해서 입력 받는 값이 아님
	@Exclude()
	publisherCd: string;
	@Exclude()
	cmsUse: number;

	@Exclude()
	get publisher_id(): Types.ObjectId {
		return Types.ObjectId.isValid(this.publisherId) ? new Types.ObjectId(this.publisherId) : undefined;
	}

	@Exclude()
	get report_id(): Types.ObjectId {
		return Types.ObjectId.isValid(this.reportId) ? new Types.ObjectId(this.reportId) : undefined;
	}
}
