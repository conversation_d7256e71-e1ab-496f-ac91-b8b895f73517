import {
	AdProvider, Service, AdUnit, Country, Place, BiddingGroup, Deal, DeviceOs, ResponseCreativeType,
	Impressions, Clicks, UsdRevenue, KrwRevenue, UsdNetRevenue, KrwNetRevenue, Ctr, UsdCpm, KrwCpm, UsdCpc, KrwCpc, UsdNetCpm, KrwNetCpm, UsdNetCpc, KrwNetCpc,
	AdUnitRequests, AdProviderRequests, AdProviderResponses, SspFilledRequests, SspImpressions, SspViewableImpressions, SspClicks, SspCompletions, SspCtr,
	EstimatedMetrics, Union
} from '../../enum/report.enum';

/* COMMON */
export const MethodType = {
	CREATE: 'CREATE',
	UPDATE: 'UPDATE',
	LIST: 'LIST',
} as const;

export const CustomReportType = {
	PERF: 'PERF',
	MD_AP: 'MD_AP',
	MD_AU: 'MD_AU',
	MD: 'MD'
} as const;
export const CustomReportTypes = Object.keys(CustomReportType) as (keyof typeof CustomReportType)[];
export type CUSTOM_REPORT_TYPE = Union<typeof CustomReportType>;

export const TimeUnit = {
	month: 'month',
	date: 'date',
	hour: 'hour',
} as const;
export const TimeUnits = Object.keys(TimeUnit) as (keyof typeof TimeUnit)[];

// month, date, hour, service, adUnit, country
const CommonDimensions = [...TimeUnits, Service, AdUnit, Country];


/* PERF */
export const AdProviderGroup = 'adProviderGroup';
export const PerfValidationField = {
	AP_TIMEZONE: { target: AdUnitRequests, fields: [AdProvider] },
	AD_PROVIDER_GROUP: { target: AdProviderGroup, fields: [AdProvider] },
	AD_UNIT_REQUESTS: { target: AdUnitRequests, fields: [Service, AdUnit] },
	AD_PROVIDER_REQUESTS: { target: AdProviderRequests, fields: [AdProvider, Place] },
	AD_PROVIDER_RESPONSES: { target: AdProviderResponses, fields: [AdProvider, Place] },
} as const;

export const PerfDimensions = [
	...CommonDimensions,
	AdProviderGroup, AdProvider, Place, BiddingGroup, Deal, DeviceOs, ResponseCreativeType
] as const;
export type PERF_DIMENSIONS = Union<typeof PerfDimensions>;

export const PerfMetrics = [
	Impressions, Clicks, UsdRevenue, KrwRevenue, UsdNetRevenue, KrwNetRevenue,
	Ctr, UsdCpm, KrwCpm, UsdCpc, KrwCpc, UsdNetCpm, KrwNetCpm, UsdNetCpc, KrwNetCpc,
	AdUnitRequests, AdProviderRequests, AdProviderResponses,
	SspFilledRequests, SspImpressions, SspViewableImpressions, SspClicks, SspCompletions, SspCtr,
	...EstimatedMetrics
] as const;
export type PERF_METRICS = Union<typeof PerfMetrics>;


/* MD */
