import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>In, <PERSON><PERSON>ong<PERSON><PERSON>d, IsNot<PERSON>mpty, <PERSON><PERSON><PERSON>ber, IsO<PERSON>al, Min<PERSON>ength } from 'class-validator';
import { Exclude, Expose, Transform } from 'class-transformer';

import _ from 'lodash';
import { Types } from 'mongoose';

import { IsTimezone } from '../../validator/is-timezone.validator';

// ArrayDistinct vs ArrayUnique
//  - ArrayDistinct 는 항목이 배열 값으로 들어온 경우에 대해서만 검증함 (null 로 들어온 경우 error 발생 안 함)
// 	- ArrayUnique 는 무조건 검증함 ( 항목이 필수인 경우, null 로 들어온 경우도 error 발생) -> ValidateIf 를 붙이면 ArrayDistinct 처럼 처리할 수 있겠음
import { ArrayDistinct } from '../../validator/array-distinct.validator';
import { toStringReplacedErrorCode } from '../../error/error-util';
import { CommonReportErrorCode } from '../../error/error-code';

import { SchMethodType } from './schedule.enums';
import {
	FREQUENCY,
	FrequencyList,
	G<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>ityList,
	SCHEDULE_TYPE,
	ScheduleTypeList, STATUS, StatusList
} from "./schedule.enums";

export const allGroups = [SchMethodType.CREATE, SchMethodType.UPDATE, SchMethodType.LIST, SchMethodType.HAS, SchMethodType.DELETE];
export const culGroups = [SchMethodType.CREATE, SchMethodType.UPDATE, SchMethodType.LIST];
export const clGroups = [SchMethodType.CREATE, SchMethodType.LIST];
export const cuGroups = [SchMethodType.CREATE, SchMethodType.UPDATE];

export class ScheduleParams {
	// https://www.typescriptlang.org/docs/handbook/decorators.html#decorator-composition
	// 	- The expressions for each decorator are evaluated top-to-bottom.
	// 	- The results are then called as functions from bottom-to-top.
	// nestjs 처음 실행 시, decorator evaluation 은 정의한 순서대로 발생한다.
	// API 를 호출하면, validation 이 역순으로 실행한다. 따라서 에러 메시지도 역순으로 출력이 된다.

	@IsMongoId({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: allGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: allGroups })
	publisherId: string;

	@IsIn(ScheduleTypeList, {
		message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE),
		groups: culGroups
	})
	@IsNotEmpty({
		message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE),
		groups: culGroups
	})
	type: SCHEDULE_TYPE;

	@Expose({ groups: [SchMethodType.UPDATE, SchMethodType.DELETE] })
	@IsMongoId({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: [SchMethodType.UPDATE, SchMethodType.DELETE] })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [SchMethodType.UPDATE, SchMethodType.DELETE] })
	scheduleId: string;

	@Expose({ groups: culGroups })
	@MinLength(1, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: [SchMethodType.LIST] })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [SchMethodType.CREATE] })
	@IsOptional({ groups: [SchMethodType.UPDATE, SchMethodType.LIST] })
	name: string;

	@IsTimezone({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: culGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: cuGroups })
	@IsOptional({ groups: culGroups })
	description: string;

	@IsIn(FrequencyList, {
		message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE),
		groups: [SchMethodType.CREATE]
	})
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [SchMethodType.CREATE] })
		// @IsOptional({groups: [SchMethodType.LIST]})
	frequency: FREQUENCY;

	@IsIn(GranularityList, {
		message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE),
		groups: [SchMethodType.CREATE]
	})
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [SchMethodType.CREATE] })
		// @IsOptional({groups: [SchMethodType.LIST]})
	granularity: GRANULARITY;

	@Expose({ groups: [SchMethodType.UPDATE, SchMethodType.LIST] })
	@IsIn(StatusList, {
		message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE),
		// groups: createUpdateGroups
	})
	@IsNotEmpty({
		message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE),
		groups: [SchMethodType.UPDATE]
	})
	@IsOptional({ groups: [SchMethodType.CREATE, SchMethodType.LIST] })
	status: STATUS;

	@Expose({ groups: [SchMethodType.CREATE, SchMethodType.UPDATE, SchMethodType.DELETE] })
	@IsArray()
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [SchMethodType.CREATE, SchMethodType.UPDATE, SchMethodType.DELETE] })
	alarmReceiverIds: string[];

	@Expose({ groups: [SchMethodType.LIST] })
	@Transform(({ obj }) => !_.isEmpty(obj.pageNo) ? (_.isFinite(Number(obj.pageNo)) ? Number(obj.pageNo) : obj.pageNo) : 0)
	@IsNumber({}, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: [SchMethodType.LIST] })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [SchMethodType.LIST] })
	pageNo: number;

	@Expose({ groups: [SchMethodType.LIST] })
	@Transform(({ obj }) => !_.isEmpty(obj.pageSize) ? (_.isFinite(Number(obj.pageSize)) ? Number(obj.pageSize) : obj.pageSize) : 20)
	@IsNumber({}, { message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: [SchMethodType.LIST] })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [SchMethodType.LIST] })
	pageSize: number;

	@Expose({ groups: clGroups })
	@IsMongoId({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: clGroups })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [SchMethodType.CREATE] })
	@IsOptional({ groups: [SchMethodType.LIST] })
	creatorId: string;

	@Expose({ groups: [SchMethodType.UPDATE, SchMethodType.DELETE] })
	@IsMongoId({ message: toStringReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE), groups: [SchMethodType.UPDATE, SchMethodType.DELETE] })
	@IsNotEmpty({ message: toStringReplacedErrorCode(CommonReportErrorCode.REQUIRED_VALUE), groups: [SchMethodType.UPDATE, SchMethodType.DELETE] })
	modifierId: string;

	@Exclude()
	get publisher_id(): Types.ObjectId {
		return Types.ObjectId.isValid(this.publisherId) ? new Types.ObjectId(this.publisherId) : undefined;
	}

	@Exclude()
	get schedule_id(): Types.ObjectId {
		return Types.ObjectId.isValid(this.scheduleId) ? new Types.ObjectId(this.scheduleId) : undefined;
	}
}
