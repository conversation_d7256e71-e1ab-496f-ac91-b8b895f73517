import {ArgumentMetadata, BadRequestException, Injectable, PipeTransform} from '@nestjs/common';
import {plainToInstance} from 'class-transformer';
import {validate} from 'class-validator';

import _ from 'lodash';

import {extractErrorMessages} from '../../error/error-util';

import {ScheduleType} from './schedule.enums';
import {ScheduleParams} from './schedule-dto';
import {RevenueSharingScheduleParams} from "../revenue-sharing-schedule/revenue-sharing-schedule-dto";


@Injectable()
export class ScheduleValidationPipe implements PipeTransform<ScheduleParams> {
	constructor(private readonly groups?: string[]) {
	}

	async transform(value: ScheduleParams, {metatype}: ArgumentMetadata) {
		// ScheduleParams, Object 타입만 허용함
		if (!metatype || !_.includes([ScheduleParams, Object], metatype)) {
			return value;
		}

		const {type} = value;

		// type 에 따라 사용할 Class 결정
		let paramsClass = ScheduleParams;
		if (_.isEqual(ScheduleType.RS, type)) {
			paramsClass = RevenueSharingScheduleParams;
		} else if (_.isEqual(ScheduleType.PERF, type)) {
			// paramsClass = PerformanceParams;
		}

		// 1. Plain object 를 Class instance 로 변환
		// 	- reportScheduleFilters 에 "excludeExtraneousValues: true" 를 적용하여 불필요한 요소들을 제거 한다.
		// 	  Filters 하위 프로퍼티에 @Expose() 를 적용해주어야 한다.
		// 2. groups 설정을 해줘야 Decorator 가 정상적으로 동작한다.
		const scheduleParams: ScheduleParams = plainToInstance(paramsClass, value, {groups: this.groups});

		// 3. 유효성 검사
		const errors = await validate(scheduleParams, {groups: this.groups});

		if (errors.length > 0) {
			// 에러 메시지를 BadRequestException 으로 던짐
			throw new BadRequestException(extractErrorMessages(errors));
		}

		return scheduleParams;
	}
}
