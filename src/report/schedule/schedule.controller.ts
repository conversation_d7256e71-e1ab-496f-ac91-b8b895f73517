/**
 * API Spec
 * 	- 수익쉐어 리포트 API for CMS : https://wiki.navercorp.com/spaces/GFP/pages/**********/01.+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+for+CMS
 * 	- 수익쉐어 리포트 API for Partner : https://wiki.navercorp.com/spaces/GFP/pages/**********/V.3.0
 */
import {
	Body,
	Controller,
	Get,
	Logger,
	Post,
	Query,
	UseFilters,
	UseGuards,
	UsePipes,
	ValidationPipe,
	Version
} from '@nestjs/common';

import _ from 'lodash';
import { Model, Types } from 'mongoose';

import { UserId } from '../../decorator/user.decorator';
import { ObjectIdPipe } from '../../pipe/object-id.pipe';
import { PublisherIdPipe } from '../../pipe/publisher-id.pipe';
import { AuthorizationGuard } from '../../guard/authorization.guard';
import { HttpExceptionFilter } from '../../exception/http-exception.filter';

import { ScheduleService } from './schedule.service';
import { ScheduleValidator } from './schedule.validator';
import { ScheduleValidationPipe } from './schedule-validation.pipe';
import { ScheduleType, SchMethodType } from './schedule.enums';
import { RevenueSharingScheduleValidator } from "../revenue-sharing-schedule/revenue-sharing-schedule.validator";
import { RevenueSharingScheduleParams } from "../revenue-sharing-schedule/revenue-sharing-schedule-dto";
import { PerformanceScheduleParams } from "../performance-schedule/performance-schedule-dto";
import { Schedule, ScheduleDocument } from "../schema/schedule.schema";
import { InjectModel } from "@nestjs/mongoose";

@Controller('schedule')
@UseGuards(AuthorizationGuard)
@UseFilters(HttpExceptionFilter)
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class ScheduleController {
	private readonly logger = new Logger(ScheduleController.name);

	constructor(
		@InjectModel(Schedule.name)
		private scheduleModel: Model<ScheduleDocument>,
		private readonly scheduleService: ScheduleService) {
	}

	/*
		[참고사항] params 파라미터에 ScheduleParams 타입 대신 any 로 정의한 이유
			ScheduleParams 타입으로 정의할 경우, groups 가 적용 되지 않은 상태로 validation 이 1차 적용 된다.
			groups 가 적용 되지 않으면 validation 이 원하는 방식으로 동작하지 않는다.
			어차피 ScheduleValidationPipe 에서 validation 을 따로 적용 하기 때문에, 타입을 any 로 받아도 문제가 없다.
	*/

	/**
	 * 스케줄 생성
	 *
	 * RS Spec : https://wiki.navercorp.com/spaces/GFP/pages/**********/01.+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+for+CMS#id-01.%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8APIforCMS-1.%EC%8A%A4%EC%BC%80%EC%A4%84%EC%83%9D%EC%84%B1
	 *
	 * @param userId
	 * @param params 공통: publisherId, type, name, description?, status, frequency, granularity, alarmReceiverIds, creatorId
	 * 				 RS  : keys, includeAllServiceIds, serviceIds?, adUnitIds?
	 * 	             PERF: dimensions, metrics, filters?
	 *
	 */
	@Version('3')
	@Post('/create')
	async create(
		@UserId() userId: string,
		@Body(new ScheduleValidationPipe([SchMethodType.CREATE]), PublisherIdPipe) params: any) {
		this.logger.debug(`ScheduleController.create() 호출`);
		this.logger.debug(`userId = ${userId}, params = ${JSON.stringify(params)}`);

		// [type 별 Validation]
		if (_.isEqual(ScheduleType.RS, params.type)) {
			// 수익쉐어 스케줄 Validation
			const myParams = params as RevenueSharingScheduleParams;
			this.logger.debug(`myParams = ${JSON.stringify(myParams)}`);

			// [Validation] 중복된 이름인지 검사
			await RevenueSharingScheduleValidator.validateName(this.scheduleModel, myParams.publisher_id, myParams.name);

			// [Validation] 등록할 수 있는 키는 최대 15개를 초과했는지 검사
			RevenueSharingScheduleValidator.validateKeyCount(myParams.keys);

			// [Validation] includeAllServiceIds == false인 경우에는 serviceIds, adUnitIds 둘 중 하나는 필수
			RevenueSharingScheduleValidator.validateIncludeAllServiceIds(myParams.includeAllServiceIds, myParams.serviceIds, myParams.adUnitIds);

			// includeAllServiceIds 가 true 인 경우, serviceIds, adUnitIds 는 비워야 함
			if (myParams.includeAllServiceIds) {
				params.serviceIds = [];
				params.adUnitIds = [];
			}

		} else if (_.isEqual(ScheduleType.PERF, params.type)) {
			// 성과 스케줄 Validation
			const myParams = params as PerformanceScheduleParams;
			this.logger.debug(`myParams = ${JSON.stringify(myParams)}`);
		}

		return { data: await this.scheduleService.create(userId, params) };
	}

	/**
	 * 스케줄 수정
	 *
	 * RS Spec : https://wiki.navercorp.com/spaces/GFP/pages/**********/01.+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+for+CMS#id-01.%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8APIforCMS-2.%EC%8A%A4%EC%BC%80%EC%A4%84%EB%B3%80%EA%B2%BD
	 *
	 * @param userId
	 * @param params 공통: publisherId, type, name, description?, status, frequency, granularity, alarmReceiverIds, creatorId
	 * 				 RS  : keys, includeAllServiceIds, serviceIds?, adUnitIds?
	 * 	             PERF: dimensions, metrics, filters?
	 */
	@Version('3')
	@Post('/update')
	async update(@UserId() userId: string, @Body(new ScheduleValidationPipe([SchMethodType.UPDATE]), PublisherIdPipe) params: any) {
		this.logger.debug(`ScheduleController.update() 호출`);
		this.logger.debug(`params = ${JSON.stringify(params)}`);

		const schedule = await this.scheduleService.getSchedule({
			publisher_id: params.publisher_id,
			_id: params.schedule_id,
		});

		// [Validation] scheduleId 에 해당 하는 스케줄이 있는지
		ScheduleValidator.validateSchedule(schedule);

		// [type 별 Validation]
		if (_.isEqual(ScheduleType.RS, params.type)) {
			// 수익쉐어 스케줄 Validation
			const myParams = params as RevenueSharingScheduleParams;

			// [Validation] 중복된 이름인지 검사
			await RevenueSharingScheduleValidator.validateName(this.scheduleModel, myParams.publisher_id, myParams.name, myParams.schedule_id);

			// [Validation] 등록할 수 있는 키는 최대 15개를 초과했는지 검사
			RevenueSharingScheduleValidator.validateKeyCount(myParams.keys);

			// [Validation] includeAllServiceIds == false인 경우에는 serviceIds, adUnitIds 둘 중 하나는 필수
			RevenueSharingScheduleValidator.validateIncludeAllServiceIds(myParams.includeAllServiceIds, myParams.serviceIds, myParams.adUnitIds);

			// [Validation] 한 번 지정된 키는 삭제할 수 없음
			RevenueSharingScheduleValidator.validateKeys(schedule.ext.RS.keys, myParams.keys);

			// includeAllServiceIds 가 true 인 경우, serviceIds, adUnitIds 는 비워야 함
			if (myParams.includeAllServiceIds) {
				params.serviceIds = [];
				params.adUnitIds = [];
			}
		} else {
			// 성과 스케줄 Validation
			const myParams = params as PerformanceScheduleParams;
			this.logger.debug(`perfScheduleParams = ${JSON.stringify(myParams)}`);
		}

		return { data: await this.scheduleService.update(userId, params) };
	}

	/**
	 * 스케줄 리스트 조회
	 *
	 * RS Spec : https://wiki.navercorp.com/spaces/GFP/pages/**********/01.+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+for+CMS#id-01.%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8APIforCMS-3.%EC%8A%A4%EC%BC%80%EC%A4%84%EB%AA%A9%EB%A1%9D%EC%A1%B0%ED%9A%8C
	 *
	 * @param userId
	 * @param params { publisherId, type, name?, timezone?, startDate?, endDate?, creatorId?, queryPeriodUse?, pageNo, pageSize }
	 */
	@Version('3')
	@Get('/list')
	async list(@UserId() userId: string, @Query(new ScheduleValidationPipe([SchMethodType.LIST]), PublisherIdPipe) params: any) {
		this.logger.debug(`ScheduleController.list() 호출`);
		this.logger.debug(`params = ${JSON.stringify(params)}`);

		const { totalCount, list } = await this.scheduleService.list(userId, params);
		return { data: { totalCount, list: list.map(this._toInterfaced) } };
	}

	/**
	 * 스케줄 상세 조회
	 * RS Spec: https://wiki.navercorp.com/spaces/GFP/pages/**********/01.+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+for+CMS#id-01.%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8APIforCMS-4.%EC%8A%A4%EC%BC%80%EC%A4%84%EC%83%81%EC%84%B8%EC%A1%B0%ED%9A%8C
	 *
	 * @param userId
	 * @param publisher_id
	 * @param schedule_id
	 */
	@Version('3')
	@Get('/detail')
	async detail(
		@UserId() userId: string,
		@Query('publisherId', PublisherIdPipe, ObjectIdPipe) publisher_id: Types.ObjectId,
		@Query('scheduleId', ObjectIdPipe) schedule_id: Types.ObjectId) {
		this.logger.debug(`ScheduleController.detail() 호출`);
		this.logger.debug(`userId = ${userId} publisher_id = ${publisher_id}, schedule_id = ${schedule_id}`);

		// [Validation] scheduleId 에 해당 하는 스케줄이 있는지
		const schedule = await this.scheduleService.detail(userId, publisher_id, schedule_id);
		this.logger.debug(`schedule = ${JSON.stringify(schedule, null, 2)}`);
		ScheduleValidator.validateSchedule(schedule);

		return { data: this._toInterfaced(schedule) };
	}

	/**
	 * 특정 서비스/광고가 있는 스케줄 목록 조회
	 *
	 * RS Spec : https://wiki.navercorp.com/spaces/GFP/pages/**********/01.+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+for+CMS#id-01.%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8APIforCMS-5.%ED%8A%B9%EC%A0%95%EC%84%9C%EB%B9%84%EC%8A%A4%2F%EA%B4%91%EA%B3%A0%EA%B0%80%EC%9E%88%EB%8A%94%EC%8A%A4%EC%BC%80%EC%A4%84%EB%AA%A9%EB%A1%9D%EC%A1%B0%ED%9A%8C
	 *
	 * @param userId
	 * @param params { publisherId, type, name?, timezone?, startDate?, endDate?, creatorId?, queryPeriodUse?, pageNo, pageSize }
	 */
	@Version('3')
	@Get('/has')
	async has(@UserId() userId: string, @Query(new ScheduleValidationPipe([SchMethodType.HAS]), PublisherIdPipe) params: any) {
		this.logger.debug(`ScheduleController.has() 호출`);
		this.logger.debug(`params = ${JSON.stringify(params)}`);

		// [type 별 Validation]
		if (_.isEqual(ScheduleType.RS, params.type)) {
			// 수익쉐어 스케줄 Validation
			const myParams = params as RevenueSharingScheduleParams;

			// [Validation] serviceIds, adUnitIds 둘 중 하나는 필수
			RevenueSharingScheduleValidator.validateServiceIdsAndAdUnitIds(myParams.serviceIds, myParams.adUnitIds);
		} else {

		}

		const { list } = await this.scheduleService.has(userId, params);
		return { data: { list } };
	}

	/**
	 * 알림 수신자 삭제
	 *
	 * RS Spec : APIhttps://wiki.navercorp.com/spaces/GFP/pages/**********/01.+%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4+%EB%A6%AC%ED%8F%AC%ED%8A%B8+API+for+CMS#id-01.%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8APIforCMS-6.%EC%95%8C%EB%A6%BC%EC%88%98%EC%8B%A0%EC%9E%90%EC%82%AD%EC%A0%9C
	 *
	 * @param userId
	 * @param params
	 */
	@Version('3')
	@Post('/alarmreceivers/delete')
	async deleteAlarmReceivers(@UserId() userId: string, @Body(new ScheduleValidationPipe([SchMethodType.DELETE]), PublisherIdPipe) params: any) {
		this.logger.debug(`ScheduleController.deleteAlarmReceivers() 호출`);
		this.logger.debug(`params = ${JSON.stringify(params)}`);

		// [Validation] scheduleId 에 해당 하는 스케줄이 있는지
		const schedule = await this.scheduleService.detail(userId, params.publisher_id, params.schedule_id);
		this.logger.debug(`schedule = ${JSON.stringify(schedule, null, 2)}`);
		ScheduleValidator.validateSchedule(schedule);

		// 수신자 삭제
		const alarmReceiverIds = params.alarmReceiverIds;
		if (_.isEmpty(alarmReceiverIds)) {
			return { data: { 'scheduleId': params.schedule_id } };
		} else {
			return { data: await this.scheduleService.deleteAlarmReceiverIds(params.schedule_id, alarmReceiverIds, params.modifierId) };
		}
	}

	/**
	 * ext.RS, ext.PERF 아래 필드들을 1단계로 끌어 올리고, ext 필드 삭제
	 *
	 * @param doc
	 * @private
	 */
	private _toInterfaced(doc: ScheduleDocument) {
		let doc2 = doc.toObject();

		doc2 = { ...doc2, ...doc2.ext[doc.type as keyof typeof doc2.ext] };
		delete doc2.ext;

		return doc2;
	}
}
