import { Controller, Get, Logger, Query, UseFilters, UseGuards } from '@nestjs/common';

import { AuthorizationGuard } from '../../guard/authorization.guard';
import { HttpExceptionFilter } from '../../exception/http-exception.filter';

import { SettlementService } from './settlement.service';
import { SettlementValidator } from './settlement.validator';

import { DateStringFormatPipe } from '../../pipe/date-string-format.pipe';


@Controller('settlement')
@UseGuards(AuthorizationGuard)
@UseFilters(HttpExceptionFilter)
export class SettlementController {
	private readonly logger = new Logger(SettlementController.name);

	constructor(private readonly settlementService: SettlementService) { }

	/**
	 * 정산 데이터 조회
	 *
	 * @param month
	 */
	@Get('')
	async get(@Query('month', new DateStringFormatPipe('YYYYMM')) month: string) {
		this.logger.debug(`SettlementController.get() 호출`);
		this.logger.debug(`month = ${month}`);

		// [Validation] 요청한 년월의 정산 데이터 생성이 완료 되었는지
		const recentYm = await this.settlementService.getSettlementRecentYm();
		SettlementValidator.validateReportComplete(month, recentYm);

		return { data: await this.settlementService.get(month) };
	}

	/**
	 * 전월 지르콘 누락일자 조회
	 */
	@Get('/zircon-missing-dates')
	async getZirconMissingDates() {
		this.logger.debug(`SettlementController.getZirconMissingDates() 호출`);

		return  { data : await this.settlementService.getZirconMissingDates() };
	}
}
