import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import _ from 'lodash';
import moment from 'moment';
import { Model } from 'mongoose';

import { Environment, EnvironmentDocument } from '../schema/environment.schema';
import { ZirconTrace, ZirconTraceDocument } from '../schema/zircon-trace.schema';
import { SettlementMonthly, SettlementMonthlyDocument } from '../schema/settlement-monthly.schema';


@Injectable()
export class SettlementService {
	private readonly logger = new Logger(SettlementService.name);

	constructor(
		@InjectModel(Environment.name)
		private environmentModel: Model<EnvironmentDocument>,

		@InjectModel(ZirconTrace.name)
		private zirconTraceModel: Model<ZirconTraceDocument>,

		@InjectModel(SettlementMonthly.name)
		private settlementMonthlyModel: Model<SettlementMonthlyDocument>,
	) { }

	/**
	 * 정산 데이터 조회
	 *
	 * @param month
	 */
	async get(month: string) {
		return this.settlementMonthlyModel.aggregate([
			{ $match: { month } },
			{ $project: {
				_id: 0, month: 1, publisherId: '$publisher_id', adProviderId: '$adProvider_id', serviceId: 1,
				adjustedUsdRevenue: { $toString: '$adjustedUsdRevenue' },
				adjustedKrwRevenue: { $toString: '$adjustedKrwRevenue' },
				usdRevenue: { $toString: '$usdRevenue' },
				krwRevenue: { $toString: '$krwRevenue' }
			}}
		]);
	}

	/**
	 * 전월 지르콘 누락일자 조회
	 */
	async getZirconMissingDates() {
		return this.zirconTraceModel.aggregate([
			{ $match: {
				date: {
					$gte: moment().startOf('month').subtract(1, 'months').format('YYYYMMDD'),
					$lt: moment().startOf('month').format('YYYYMMDD'),
				},
			} },

			{ $project: {
				date: 1, publisher_id: 1, adProvider_id: 1,
				zirconBSilvergreyAt: { $ifNull: [ { $dateToString: { date: '$zirconBSilvergreyAt', timezone: '+09:00', format: '%Y-%m-%dT%H:%M:%S%z' } }, '' ] },
			} },

			{ $sort: { date: 1 } },

			{ $group: {
				_id: { publisher_id: '$publisher_id', adProvider_id: '$adProvider_id' },
				missingDates: { $push: { $cond: { if: { $eq: ['$zirconBSilvergreyAt', ''] }, then: '$date', else: '$$REMOVE' } } }
			} },

			{ $project: { _id: 0, publisherId: '$_id.publisher_id', adProviderId: '$_id.adProvider_id', missingDates: 1 } },
		]);
	}


	/**
	 * 정산 데이터 최근 적재 완료 년월 조회 ( Environments = settlement-recent-ym )
	 */
	async getSettlementRecentYm() {
		const env = await this.environmentModel.findOne({ name: 'settlement-recent-ym' }, { value: 1 });
		return !_.isEmpty(env) ? env.value : '';
	}
}
