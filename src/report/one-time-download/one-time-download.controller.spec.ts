import { Test, TestingModule } from '@nestjs/testing';
import { OneTimeDownloadController } from './one-time-download.controller';

describe('OneTimeDownloadController', () => {
  let controller: OneTimeDownloadController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OneTimeDownloadController],
    }).compile();

    controller = module.get<OneTimeDownloadController>(OneTimeDownloadController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
