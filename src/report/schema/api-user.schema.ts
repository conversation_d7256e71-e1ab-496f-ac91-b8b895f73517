import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ApiUserDocument = ApiUser & Document;

@Schema({
	collection: 'ApiUsers',
	versionKey: false
})
export class ApiUser {
	@Prop()
	userId: string;

	@Prop()
	clientId: string;

	@Prop()
	publisherCds: Array<string>;

	@Prop()
	apiType: string;

	@Prop()
	checkPublisherAuthorization: boolean;
}

export const ApiUserSchema = SchemaFactory.createForClass(ApiUser);

export const ApiUserModel: ModelDefinition = {
	name: ApiUser.name,
	schema: ApiUserSchema,
	collection: 'ApiUsers',
};
