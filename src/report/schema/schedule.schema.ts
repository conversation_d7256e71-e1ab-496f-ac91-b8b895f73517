import { ModelD<PERSON>inition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

import _ from 'lodash';

import {
	FREQUENC<PERSON>,
	FrequencyList,
	GRANULARITY,
	GranularityList,
	SCHEDULE_TYPE,
	ScheduleType,
	STATUS,
	StatusList
} from '../schedule/schedule.enums';

export type ScheduleDocument = Schedule & Document;

@Schema({ _id: false, strict: false })
class ExtRs {
	@Prop({ set: (value: Array<string>) => (value.length === 0 ? new Array<string>() : value) })
	keys: Array<string>;

	@Prop({ set: (value: Array<string>) => (value.length === 0 ? new Array<string>() : value) })
	serviceIds: Array<string>;

	@Prop({ set: (value: Array<string>) => (value.length === 0 ? new Array<string>() : value) })
	adUnitIds: Array<string>;

	@Prop({ set: (value: Boolean) => (_.isEmpty(value) ? false: true) })
	includeAllServiceIds: Boolean = false;
}

@Schema({ _id: false, strict: false })
class ExtPerf {
	@Prop({ set: (value: Array<string>) => (value.length === 0 ? new Array<string>() : value) })
	dimensions: Array<string>;

	@Prop({ set: (value: Array<string>) => (value.length === 0 ? new Array<string>() : value) })
	metrics: Array<string>;
}

@Schema({ _id: false })
export class Ext {
	constructor(type: SCHEDULE_TYPE, options: Object) {
		if (_.isEqual(type, ScheduleType.RS) && _.isEmpty(this.RS)) {
			this.RS = new ExtRs();
			this.RS = Object.assign(this.RS, options);
		}
	}

	@Prop()
	RS: ExtRs

	@Prop()
	PERF: ExtPerf
}

@Schema({ collection: 'Schedules', versionKey: false, timestamps: { updatedAt: 'modifiedAt' } })
export class Schedule extends Document {

	@Prop({ type: String, enum: ScheduleType, required: true })
	type: SCHEDULE_TYPE;

	@Prop({ required: true })
	userId: string;

	@Prop({ required: true })
	publisher_id: Types.ObjectId;

	@Prop({ required: true, default: '' })
	name: string;

	@Prop()
	description: string;

	@Prop({ type: String, enum: StatusList, required: true, default: 'ON' })
	status: STATUS;

	@Prop({ type: String, enum: FrequencyList, required: true, default: 'DAILY' })
	frequency: FREQUENCY;

	@Prop({ type: String, enum: GranularityList, required: false, default: 'DAILY' })
	granularity: GRANULARITY;

	@Prop({ default: {} })
	ext: Ext;

	@Prop({ required: true })
	alarmReceiverIds: Array<string>;

	@Prop({ default: '' })
	creatorId: string;

	@Prop()
	createdAt: Date;

	@Prop({ default: '' })
	modifierId: string;

	@Prop()
	modifiedAt: Date;
}

export const ScheduleSchema = SchemaFactory.createForClass(Schedule);

ScheduleSchema.set('toJSON', {
	transform: (doc, ret) => {
		if (ret._id) {
			ret.scheduleId = ret._id.toHexString();
			delete ret._id;
		}
	}
});
ScheduleSchema.set('toObject', {
	transform: (doc, ret) => {
		if (ret._id) {
			ret.scheduleId = ret._id.toHexString();
			delete ret._id;
		}
	}
});

export const ScheduleModel: ModelDefinition = {
	name: Schedule.name,
	schema: ScheduleSchema,
	collection: 'Schedules',
};
