import {ModelDefinition, Prop, Schema, SchemaFactory} from '@nestjs/mongoose';
import {Document} from 'mongoose';

export type EnvironmentDocument = Environment & Document;

@Schema({ versionKey: false, collection: 'Environments' })
export class Environment {
	@Prop()
	name: string;

	@Prop({type: Object})
	value: any;
}

export const EnvironmentSchema = SchemaFactory.createForClass(Environment);

export const EnvironmentModel: ModelDefinition = {
	name: Environment.name,
	schema: EnvironmentSchema,
	collection: 'Environments',
};
