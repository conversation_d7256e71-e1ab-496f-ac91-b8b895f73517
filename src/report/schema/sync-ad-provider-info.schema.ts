import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import {Document, Types} from 'mongoose';

export type SyncAdProviderInfoDocument = SyncAdProviderInfo & Document;

@Schema({ versionKey: false, timestamps: { updatedAt: 'modifiedAt' } })
export class SyncAdProviderInfo {
	@Prop()
	publisher_id: Types.ObjectId;

	@Prop()
	adProvider_id: Types.ObjectId;
}

export const SyncAdProviderInfoSchema = SchemaFactory.createForClass(SyncAdProviderInfo);

export const SyncAdProviderInfoModel: ModelDefinition = {
	name: SyncAdProviderInfo.name,
	schema: SyncAdProviderInfoSchema,
	collection: 'SyncAdProviderInfos',
};
