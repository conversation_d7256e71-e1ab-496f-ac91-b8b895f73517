import { ModelDefinition, Prop, raw, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type OneTimeDownloadHelpDocument = OneTimeDownloadHelp & Document;

export type Payload = {
  kind: string;
  path: string;
  contentType: string;
  filename: string;
};

const payloadNested = {
  kind: { type: String, required: true },
  path: { type: String, required: true },
  contentType: { type: String, required: true },
  filename: { type: String, required: true },
};

@Schema({ versionKey: false, timestamps: { updatedAt: 'modifiedAt' } })
export class OneTimeDownloadHelp {
  @Prop({
    required: true,
    type: raw(payloadNested),
  })
  payload: Payload;

  @Prop({ required: true, type: Date })
  expiredAt: Date;

  @Prop({ required: true })
  allowedNumAccess: number;

  @Prop({ type: [Date] })
  accessDates: Date[];
}

export const OneTimeDownloadHelpSchema = SchemaFactory.createForClass(OneTimeDownloadHelp);

export const OneTimeDownloadHelpModel: ModelDefinition = {
  name: OneTimeDownloadHelp.name,
  schema: OneTimeDownloadHelpSchema,
  collection: 'OneTimeDownloadHelps',
};
