import {ModelDefinition, Prop, Schema, SchemaFactory} from '@nestjs/mongoose';
import {Document} from 'mongoose';

export type CommonCodeDocument = CommonCode & Document;

@Schema({ versionKey: false, collection: 'CommonCodes' })
export class CommonCode {
	@Prop()
	category: string;

	@Prop()
	code: string;
}

export const CommonCodeSchema = SchemaFactory.createForClass(CommonCode);

export const CommonCodeModel: ModelDefinition = {
	name: CommonCode.name,
	schema: CommonCodeSchema,
	collection: 'CommonCodes',
};
