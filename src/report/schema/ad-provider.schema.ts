import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { DefaultProjection } from './util';

export type AdProviderDocument = AdProvider & Document;

@Schema({ versionKey: false, timestamps: { updatedAt: 'modifiedAt' } })
export class AdProvider {
  @Prop()
  name: string;

  @Prop()
  namGroup: string;
}

export const AdProviderSchema = SchemaFactory.createForClass(AdProvider);

export const AdProviderProjection: DefaultProjection<AdProvider> = {
  name: 1,
  namGroup: 1,
};

export const CmsAdProviderModel: ModelDefinition = {
  name: 'CmsAdProvider',
  schema: AdProviderSchema,
  collection: 'AdProviders',
};

export const AdProviderModel: ModelDefinition = {
  name: AdProvider.name,
  schema: AdProviderSchema,
  collection: 'AdProviders2',
};

export const AdProviderSyncModel: ModelDefinition = {
  name: 'AdProviderSync',
  schema: AdProviderSchema,
  collection: 'AdProviders',
};

// TODO:
// export const AdProviderModel: ModelDefinition = {
//   name: AdProvider.name,
//   schema: AdProviderSchema,
//   collection: 'AdProviders',
// };

// export const AdProviderSyncModel = AdProviderModel;
