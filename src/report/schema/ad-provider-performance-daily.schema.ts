import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type AdProviderPerformanceDailyDocument = AdProviderPerformanceDaily & Document;

@Schema({
	collection: 'AdProviderPerformanceDaily',
	versionKey: false,
	timestamps: {updatedAt: 'modifiedAt'}
})
export class AdProviderPerformanceDaily {
	@Prop()
	date: string;

	@Prop()
	publisher_id: Types.ObjectId;

	@Prop()
	service_id: Types.ObjectId;

	@Prop()
	adUnitId: string;

	@Prop()
	namGroup: string;

	@Prop()
	adProvider_id: Types.ObjectId;

	@Prop()
	adProviderRequests: number;

	@Prop()
	adProviderResponses: number;

	@Prop()
	filledRequestsOfAp: number;

	@Prop()
	impressions: number;

	@Prop()
	estimatedImpressions: number;

	@Prop()
	krwBidPriceSum: number;

	@Prop()
	usdBidPriceSum: number;

	@Prop()
	krwNetBidPriceSum: number;

	@Prop()
	usdNetBidPriceSum: number;
}

export const AdProviderPerformanceDailySchema = SchemaFactory.createForClass(AdProviderPerformanceDaily);

export const AdProviderPerformanceDailyModel: ModelDefinition = {
	name: AdProviderPerformanceDaily.name,
	schema: AdProviderPerformanceDailySchema,
	collection: 'AdProviderPerformanceDaily',
};
