import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { Types } from 'mongoose';
// import Decimal from 'decimal.js';

export type PerformanceAdUnitDailyDocument = PerformanceAdUnitDaily & Document;

@Schema({
	collection: 'PerformanceAdUnitDaily'
})
export class PerformanceAdUnitDaily {
	@Prop()
	timezone: string;

	@Prop()
	date: string;

	@Prop()
	publisher_id: Types.ObjectId;

	@Prop()
	serviceId: string;

	@Prop()
	adUnitId: string;

	@Prop()
	adProviderId: string;

	@Prop()
	country: string;

	@Prop()
	placeChannelType: string;

	@Prop()
	placeProductType: string;

	@Prop()
	apTimezone: string;


	/* AP 지표 */
	@Prop()
	impressions: number;

	@Prop()
	clicks: number;

	@Prop()
	usdNetRevenue: number;

	@Prop()
	krwNetRevenue: number;

	@Prop()
	usdRevenue: number;

	@Prop()
	krwRevenue: number;


	/* GFP 지표 */
	@Prop()
	adUnitRequests: number;

	@Prop()
	adProviderRequests: number;

	@Prop()
	adProviderResponses: number;

	@Prop()
	gfpFilledRequests: number;

	@Prop()
	gfpImpressions: number;

	@Prop()
	gfpViewableImpressions: number;

	@Prop()
	gfpEstimatedImpressions: number;

	@Prop()
	gfpClicks: number;

	@Prop()
	gfpCompletions: number;

	@Prop()
	gfpEstimatedUsdNetRevenue: number;

	@Prop()
	gfpEstimatedKrwNetRevenue: number;
}

export const PerformanceAdUnitDailySchema = SchemaFactory.createForClass(PerformanceAdUnitDaily);

export const PerformanceAdUnitDailyModel: ModelDefinition = {
	name: PerformanceAdUnitDaily.name,
	schema: PerformanceAdUnitDailySchema,
	collection: 'PerformanceAdUnitDaily',
};
