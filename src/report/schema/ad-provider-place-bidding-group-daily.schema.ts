import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type AdProviderPlaceBiddingGroupDailyDocument = AdProviderPlaceBiddingGroupDaily & Document;

@Schema({
	collection: 'AdProviderPlaceBiddingGroupDaily',
	versionKey: false,
	timestamps: {updatedAt: 'modifiedAt'}
})
export class AdProviderPlaceBiddingGroupDaily {
	@Prop()
	date: string;

	@Prop()
	publisher_id: Types.ObjectId;

	@Prop()
	adProvider_id: Types.ObjectId;

	@Prop()
	adProviderPlace_id: Types.ObjectId;

	@Prop()
	biddingGroup_id: string;

	@Prop()
	adProviderResponses: number;

	@Prop()
	filledRequests: number;

	@Prop()
	krwBidPriceSum: number;

	@Prop()
	adResponseRate: number;

	@Prop()
	winRate: number;

	@Prop()
	estimatedRevenue: number;

	@Prop()
	ecpm: number;
}

export const AdProviderPlaceBiddingGroupDailySchema = SchemaFactory.createForClass(AdProviderPlaceBiddingGroupDaily);

export const AdProviderPlaceBiddingGroupDailyModel: ModelDefinition = {
	name: AdProviderPlaceBiddingGroupDaily.name,
	schema: AdProviderPlaceBiddingGroupDailySchema,
	collection: 'AdProviderPlaceBiddingGroupDaily',
};
