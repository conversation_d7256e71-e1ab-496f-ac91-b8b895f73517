import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type AdUnitPerformanceDailyDocument = AdUnitPerformanceDaily & Document;

@Schema({
	collection: 'AdUnitPerformanceDaily',
	versionKey: false,
	timestamps: { updatedAt: 'modifiedAt' }
})
export class AdUnitPerformanceDaily {
	@Prop()
	date: string;

	@Prop()
	publisher_id: Types.ObjectId;

	@Prop()
	service_id: Types.ObjectId;

	@Prop()
	adUnitId: string;

	@Prop()
	adUnitRequests: number;

	@Prop()
	filledRequests: number;

	@Prop()
	impressions: number;

	@Prop()
	viewableImpressions: number;

	@Prop()
	clicks: number;

	@Prop()
	krwBidPriceSum: number;

	@Prop()
	krwNetBidPriceSum: number;
}

export const AdUnitPerformanceDailySchema = SchemaFactory.createForClass(AdUnitPerformanceDaily);

export const AdUnitPerformanceDailyModel: ModelDefinition = {
  name: AdUnitPerformanceDaily.name,
  schema: AdUnitPerformanceDailySchema,
  collection: 'AdUnitPerformanceDaily',
};
