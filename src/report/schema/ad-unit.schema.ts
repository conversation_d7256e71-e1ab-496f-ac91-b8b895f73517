import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { DefaultProjection } from './util';

export type AdUnitDocument = AdUnit & Document;

@Schema({ versionKey: false, timestamps: { updatedAt: 'modifiedAt' } })
export class AdUnit {
  @Prop()
  name: string;

  @Prop()
  namGroup: string;

  @Prop()
  service_id: Types.ObjectId;
}

export const AdUnitSchema = SchemaFactory.createForClass(AdUnit);

export const AdUnitProjection: DefaultProjection<AdUnit> = {
  name: 1,
  namGroup: 1,
  service_id: 1,
};

export const CmsAdUnitModel: ModelDefinition = {
  name: 'CmsAdUnit',
  schema: AdUnitSchema,
  collection: 'AdUnits',
};

export const AdUnitModel: ModelDefinition = {
  name: AdUnit.name,
  schema: AdUnitSchema,
  collection: 'AdUnitsSylph',
};

export const AdUnitSyncModel: ModelDefinition = {
  name: 'AdUnitSync',
  schema: AdUnitSchema,
  collection: 'AdUnits',
};
