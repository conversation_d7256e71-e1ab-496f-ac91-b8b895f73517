import { ModelDefini<PERSON>, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type SyncPublisherServiceDocument = SyncPublisherService & Document;

@Schema({ versionKey: false })
export class SyncPublisherService {
	@Prop()
	_id: Types.ObjectId;

	@Prop()
	name: string;

	@Prop()
	publisher_id: Types.ObjectId;
}

export const SyncPublisherServiceSchema = SchemaFactory.createForClass(SyncPublisherService);

export const SyncPublisherServiceModel: ModelDefinition = {
	name: SyncPublisherService.name,
	schema: SyncPublisherServiceSchema,
	collection: 'SyncPublisherServices',
};
