import { Injectable, Logger } from '@nestjs/common';

import _ from 'lodash';
import moment from 'moment';
import { PassThrough, Transform, TransformCallback } from 'stream';

import { toString } from 'lodash';

type DateGranularity = 'daily' | 'monthly';
type OrderBy = 'asc' | 'desc';

class DateInterpolation<Label extends string = string> {
	private higherDims: Label[];
	private dateDim: Label;
	private dtArr: string[];
	private dtIdxMap: Map<string, number>;

	constructor(
		dateRange: [string, string],
		granularity: DateGranularity,
		dimensionRoute: Label[],
		private dimensionSentinel: Label | '',
		private metrics: Partial<Record<Label, any>>,
		ordering: OrderBy,
	) {
		[this.higherDims, this.dateDim] = [dimensionRoute.slice(0, -1), dimensionRoute.slice(-1)[0]];

		this.dtArr = [];
		this.dtIdxMap = new Map();

		const [dtUnit, dtFormat, toDt] = (() => {
			if (granularity === 'daily') {
				return ['days', 'YYYYMMDD', (dtStr: string) => moment(dtStr, 'YYYYMMDD')];
			}
			return ['months', 'YYYYMM', (dtStr: string) => moment(dtStr.substring(0, 6), 'YYYYMM')];
		})();

		const beginDt = toDt(dateRange[0]);
		const endDt = toDt(dateRange[1]);

		let dtIdx = 0;
		if (ordering === 'asc') {
			for (; beginDt <= endDt; beginDt.add({ [dtUnit]: 1 })) {
				const dtStr = beginDt.format(dtFormat);

				this.dtArr.push(dtStr);
				this.dtIdxMap.set(dtStr, dtIdx++);
			}
		} else {
			for (; endDt >= beginDt; endDt.add({ [dtUnit]: -1 })) {
				const dtStr = endDt.format(dtFormat);

				this.dtArr.push(dtStr);
				this.dtIdxMap.set(dtStr, dtIdx++);
			}
		}
	}

	getDateArr() {
		return this.dtArr;
	}

	isDateRow(row: Partial<Record<Label, any>>) {
		return row[this.dateDim] && (!this.dimensionSentinel || !row[this.dimensionSentinel]);
	}

	isSameGrp(prev: Partial<Record<Label, any>>, curr: Partial<Record<Label, any>>) {
		for (const dim of this.higherDims) {
			if (toString(prev[dim]) !== toString(curr[dim])) {
				return false;
			}
		}
		return true;
	}

	fillAbove(row: Partial<Record<Label, any>>) {
		const fillArr = [];

		for (const dt of this.dtArr) {
			if (dt == row[this.dateDim]) {
				fillArr.push(row);
				break;
			}

			fillArr.push(Object.assign({}, row, { [this.dateDim]: dt }, this.metrics));
		}

		return fillArr;
	}

	fillBelow(row: Partial<Record<Label, any>>) {
		const fillArr = [];
		const dtIdx = this.dtIdxMap.get(row[this.dateDim]);

		this.dtArr.slice(dtIdx + 1).forEach((dt) => {
			fillArr.push(Object.assign({}, row, { [this.dateDim]: dt }, this.metrics));
		});

		return fillArr;
	}

	fillBetween(prevRow: Partial<Record<Label, any>>, currRow: Partial<Record<Label, any>>) {
		const fillArr = [];
		const prevDtIdx = this.dtIdxMap.get(prevRow[this.dateDim]);

		for (const dt of this.dtArr.slice(prevDtIdx + 1)) {
			if (dt == currRow[this.dateDim]) {
				fillArr.push(currRow);
				break;
			}

			fillArr.push(Object.assign({}, currRow, { [this.dateDim]: dt }, this.metrics));
		}

		return fillArr;
	}
}

class DateInterpolationTransform extends Transform {
	private dateIntp: DateInterpolation;
	private buf: object[];
	private prevRow: object;

	constructor(dateIntp: DateInterpolation, highWaterMark = 16) {
		super({ objectMode: true, highWaterMark });

		this.dateIntp = dateIntp;
		this.buf = [];
		this.prevRow = null;
	}

	_pushAll(arr: object[]) {
		arr.forEach((row) => this.push(row));
	}

	_transform(row: any, encoding: BufferEncoding, callback: TransformCallback): void {
		if (!this.dateIntp.isDateRow(row)) {
			this.buf.push(row);
			return callback();
		}

		if (this.prevRow === null) {
			this._pushAll(this.buf);
			this._pushAll(this.dateIntp.fillAbove(row));
		} else if (this.dateIntp.isSameGrp(this.prevRow, row)) {
			this._pushAll(this.buf);
			this._pushAll(this.dateIntp.fillBetween(this.prevRow, row));
		} else {
			this._pushAll(this.dateIntp.fillBelow(this.prevRow));
			this._pushAll(this.buf);
			this._pushAll(this.dateIntp.fillAbove(row));
		}

		this.prevRow = row;
		this.buf = [];

		callback();
	}

	_flush(callback: TransformCallback): void {
		if (this.prevRow !== null) {
			this._pushAll(this.dateIntp.fillBelow(this.prevRow));
		}
		this._pushAll(this.buf);

		callback();
	}
}


// ex1 >
// 	targetFields = ['f1', 'f2']
// 	additionalFields = ['a1', 'a2']
// 	meta = { f1_f2 : { a1: value, a2: value } } }
// ex2 >
// 	targetFields = ['f1', 'f2']
// 	additionalFields = ['a1']
// 	meta = { f1_f2 : value } } }
type AddMetaSpec<Label extends string> = {
	targetFields: Label[];
	additionalFields: Label[];
	meta: object;
	removeTargetField?: boolean;
	defaultValue?: string;
};

class AddMetaTransform<Label extends string = string> extends Transform {
	private spec: AddMetaSpec<Label>[];

	constructor(spec: AddMetaSpec<Label>[], highWaterMark = 16) {
		super({ objectMode: true, highWaterMark });

		this.spec = spec;
	}

	_transform(row: any, encoding: BufferEncoding, callback: TransformCallback): void {
		const newRow = Object.assign({}, row);

		this.spec.map(({ targetFields, additionalFields, meta, removeTargetField, defaultValue= '-' }) => {
			const key = targetFields.map(targetField => newRow[targetField]).join('_');

			additionalFields.map(additionalField => {
				if (meta[key]) {
					if (typeof meta[key] === 'object') {
						newRow[additionalField] = meta[key][additionalField] || defaultValue;
					} else {
						newRow[additionalField] = meta[key] || defaultValue;
					}
				} else {
					newRow[additionalField] = defaultValue;
				}
			});

			if (removeTargetField) {
				targetFields.map(targetField => delete newRow[targetField]);
			}
		});

		this.push(newRow);

		callback();
	}
}


// ex >
// targetFields = ['f1', 'f2', 'f3']
// extraDataList = [{ f1, f2, f3, ... }]
// defaultValue = { f4: '-', f5: 0 }
type AddExtraRowsSpec<Label extends string> = {
	targetFields: Label[];
	extraDataList: object[];
	defaultValue?: object;
};

class AddExtraRowsTransform<Label extends string = string> extends Transform {
	private spec: AddExtraRowsSpec<Label>;
	private totalCount: number = 0;
	private index: number = -1;
	private extraData = null;

	constructor(spec: AddExtraRowsSpec<Label>, highWaterMark = 16) {
		super({ objectMode: true, highWaterMark });

		this.spec = spec;

		if (this.spec.extraDataList.length > 0) {
			this.totalCount = this.spec.extraDataList.length;
			this.index = 0;
			this.extraData = _.pick(this.spec.extraDataList[this.index], this.spec.targetFields);
		}
	}

	_transform(row: any, encoding: BufferEncoding, callback: TransformCallback): void {
		// 삽입할 데이터의 기준키가 row 데이터와 같거나 이전인 경우
		if (_.isMatch(row, this.extraData) || this.isBefore(this.extraData, row)) {
			this.pushExtraDataRows(row);
		}

		this.push(row);

		callback();
	}

	// target 이 compare 보다 앞인지 체크
	isBefore(target: any, compare: any) {
		for (const key of this.spec.targetFields) {
			const targetValue = target[key];
			const compareValue = compare[key];

			// target 이 compare 보다 앞인 경우
			if (targetValue < compareValue) {
				return true;
			}

			// target 이 compare 보다 뒤인 경우
			if (targetValue > compareValue) {
				return false;
			}
		}

		return false;
	}

	// 삽입할 데이터의 기준키가 row 데이터와 같은 경우, index 만 갱신 (row 에 이미 있으므로 추가는 하지 않음)
	// 삽입할 데이터의 기준키가 row 데이터보다 앞인 경우, extraData push (loop)
	pushExtraDataRows(row: any) {
		if (_.isMatch(row, this.extraData)) {
			// 삽입할 데이터 index 및 extraData 변경
			this.index++;
			this.extraData = _.pick(this.spec.extraDataList[this.index], this.spec.targetFields);
		} else if (this.isBefore(this.extraData, row)) {
			// 삽입할 데이터가 row 데이터보다 정렬 순서상 앞인 경우
			const newRow = Object.assign(this.extraData, this.spec.defaultValue);
			this.push(newRow);

			// 삽입할 데이터 index 및 extraData 를 다음 순서로 변경
			this.index++;
			this.extraData = _.pick(this.spec.extraDataList[this.index], this.spec.targetFields);

			this.pushExtraDataRows(row);
		}
	}

	_flush(callback: Function) {
		// 남아 있는 extraData 가 있는 경우, 일괄 push
		if (this.index < this.totalCount) {
			for (; this.index < this.totalCount; this.index++) {
				if (!_.isEmpty(this.spec.extraDataList[this.index])) {
					this.push(Object.assign(_.pick(this.spec.extraDataList[this.index], this.spec.targetFields), this.spec.defaultValue));
				}
			}
		}

		callback();
	}
}


// ex >
//  type = 'cpm' // ctr/cpm/cpc
// 	calculatedMetric = 'krwNetCpm'
// 	meta = { numerator: 'krwNetRevenue', denominator: 'impressions', precision: 6 }
// 	defaultValue = 0
type AddCalculatedMetricsSpec<Label extends string> = {
	type: 'ctr'|'cpm'|'cpc';
	calculatedMetric: Label;
	meta: CalculatedMetaSpec<Label>;
	defaultValue?: string;
};

type CalculatedMetaSpec<Label extends string> = {
	numerator: Label;
	denominator: Label;
	precision?: number;
}

class AddCalculatedMetricsTransform<Label extends string = string> extends Transform {
	private spec: AddCalculatedMetricsSpec<Label>[];

	constructor(spec: AddCalculatedMetricsSpec<Label>[], highWaterMark = 16) {
		super({ objectMode: true, highWaterMark });

		this.spec = spec;
	}

	_transform(row: any, encoding: BufferEncoding, callback: TransformCallback): void {
		const newRow = Object.assign({}, row);

		this.spec.map(({ type, calculatedMetric, meta, defaultValue }) => {
			if (type === 'ctr') {
				newRow[calculatedMetric] = this.getCtr(newRow, meta, defaultValue);
			} else if (type === 'cpm') {
				newRow[calculatedMetric] = this.getCpm(newRow, meta, defaultValue);
			} else if (type === 'cpc') {
				newRow[calculatedMetric] = this.getCpc(newRow, meta, defaultValue);
			}
		});

		this.push(newRow);

		callback();
	}

	// 계산식 : round((clicks / impressions) * 100, 6)
	getCtr(row: any, meta: CalculatedMetaSpec<Label>, defaultValue = '0'): string {
		if (row[meta.denominator] === 0) return defaultValue;
		return ((row[meta.numerator] / row[meta.denominator]) * 100).toFixed(meta.precision || 6);
	}

	// 계산식 : round((revenue / impressions) * 1000, 6)
	getCpm(row: any, meta: CalculatedMetaSpec<Label>, defaultValue = '0'): string {
		if (row[meta.denominator] === 0) return defaultValue;
		return ((row[meta.numerator] / row[meta.denominator]) * 1000).toFixed(meta.precision || 6);
	}

	// 계산식 : round(revenue / clicks, 6)
	getCpc(row: any, meta: CalculatedMetaSpec<Label>, defaultValue = '0'): string {
		if (row[meta.denominator] === 0) return defaultValue;
		return (row[meta.numerator] / row[meta.denominator]).toFixed(meta.precision || 6);
	}
}


@Injectable()
export class CommonService {
	private readonly logger = new Logger(CommonService.name);

	// dataArr: [{ ... }, { ... }, ... { ... }] (which is sorted)
	// dateRange: ['20220220', '20220303']
	// dimensionRoute: ['ServiceId', 'AdUnit', 'NamGroup', 'date'] // [ ...higherDimenstions, targetDateDim ]
	// dimensionSentinel: '' | lowerDimensionName
	// metrics: ['metric1', 'metric2', 'metric3'] (which are set to zeros)
	//       or { metrics1: fill_val1, metrics2: fill_val2, ... }
	interpolateDateDimension<Label extends string = string>(
		dataArr: Partial<Record<Label, any>>[],
		dateRange: [string, string],
		granularity: DateGranularity,
		dimensionRoute: Label[],
		dimensionSentinel: Label | '',
		metrics: Label[] | Partial<Record<Label, any>>,
		ordering: OrderBy = 'asc',
	) {
		let metricsFill = metrics;
		if (metrics instanceof Array) {
			metricsFill = metrics.reduce((acc, mtr) => {
				return Object.assign(acc, { [mtr]: 0 });
			}, {});
		}

		const dtIntp = new DateInterpolation < Label > (
			dateRange,
			granularity,
			dimensionRoute,
			dimensionSentinel,
			metricsFill as Partial<Record<Label, any>>,
			ordering,
		);

		if (dtIntp.getDateArr().length < 2) {
			return dataArr;
		}

		const newDataArr = [];

		let prevIdx = undefined;
		dataArr.forEach((row, idx) => {
			if (!dtIntp.isDateRow(row)) {
				return;
			}

			if (prevIdx === undefined) {
				newDataArr.push(...dataArr.slice(0, idx));
				newDataArr.push(...dtIntp.fillAbove(row));
			} else if (dtIntp.isSameGrp(dataArr[prevIdx], row)) {
				newDataArr.push(...dataArr.slice(prevIdx + 1, idx));
				newDataArr.push(...dtIntp.fillBetween(dataArr[prevIdx], row));
			} else {
				newDataArr.push(...dtIntp.fillBelow(dataArr[prevIdx]));
				newDataArr.push(...dataArr.slice(prevIdx + 1, idx));
				newDataArr.push(...dtIntp.fillAbove(row));
			}

			prevIdx = idx;
		});

		// finale
		if (prevIdx === undefined) {
			return dataArr;
		} else {
			newDataArr.push(...dtIntp.fillBelow(dataArr[prevIdx]));
			newDataArr.push(...dataArr.slice(prevIdx + 1));
		}

		return newDataArr;
	}

	// 날짜 보간 처리 스트림
	transformForDateInterpolation<Label extends string = string>(
		dateRange: [string, string],
		granularity: DateGranularity,
		dimensionRoute: Label[],
		dimensionSentinel: Label | '',
		metrics: Label[] | Partial<Record<Label, any>>,
		ordering: OrderBy = 'asc',
		highWaterMark = 128,
	): Transform {
		let metricsFill = metrics;
		if (metrics instanceof Array) {
			metricsFill = metrics.reduce((acc, mtr) => {
				return Object.assign(acc, { [mtr]: 0 });
			}, {});
		}

		const dtIntp = new DateInterpolation < Label > (
			dateRange,
			granularity,
			dimensionRoute,
			dimensionSentinel,
			metricsFill as Partial<Record<Label, any>>,
			ordering,
		);

		if (dtIntp.getDateArr().length < 2) {
			return new PassThrough({ objectMode: true, highWaterMark });
		}

		return new DateInterpolationTransform(dtIntp, highWaterMark);
	}

	// 메타 추가 스트림
	transformForAddMeta<Label extends string = string>(
		spec: AddMetaSpec<Label>[],
		highWaterMark = 128,
	): Transform {
		return new AddMetaTransform<Label>(spec, highWaterMark);
	}

	// 기본 스트림 데이터와 매칭 되지 않는 extra row 를 추가하는 스트림
	// 반드시 두 데이터 셋이 targetFields 를 대상으로 동일한 기준과 순서로 정렬 되어있어야 함
	transformForAddExtraRows<Label extends string = string>(
		spec: AddExtraRowsSpec<Label>,
		highWaterMark = 128,
	): Transform {
		return new AddExtraRowsTransform<Label>(spec, highWaterMark);
	}

	// 계산식 추가 스트림
	transformForAddCalculatedMetrics<Label extends string = string>(
		spec: AddCalculatedMetricsSpec<Label>[],
		highWaterMark = 128,
	): Transform {
		return new AddCalculatedMetricsTransform<Label>(spec, highWaterMark);
	}
}
