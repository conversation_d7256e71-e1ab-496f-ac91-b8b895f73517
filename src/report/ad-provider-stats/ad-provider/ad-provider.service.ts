import { Injectable } from '@nestjs/common';
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";

import { AggregationSpec, AggregatorService } from "../../aggregator/aggregator.service";

import { AdProviderRevenueDaily, AdProviderRevenueDailyDocument } from "../../schema/ad-provider-revenue-daily.schema";

import { AdProviderStatsRevenueParams } from "./ad-provider-dto";


@Injectable()
export class AdProviderService {
	constructor(
		@InjectModel(AdProviderRevenueDaily.name)
		private aprDailyModel: Model<AdProviderRevenueDailyDocument>,
		private aggregator: AggregatorService,
	) {}

	/**
	 * 매체별-AP별 수익 조회
	 *  - GFP 정산 배치 에서 월별 조회
	 *
	 * @param AdProviderStatsRevenueParams { adProviderIds, startDate, endDate, period }
	 */
	async getAdProviderStatsRevenue({ adProviderIds, startDate, endDate, period }: AdProviderStatsRevenueParams) {
		const adProvider_ids = adProviderIds.map(adProviderId => new Types.ObjectId(adProviderId))
		const dateExpr = (period === 'M') ? { $substr: ['$date', 0, 6] } : '$date';

		const spec: AggregationSpec<
			AdProviderRevenueDaily,
			'date' | 'publisher_id' | 'adProvider_id' | 'dealYn',
			| 'date'
			| 'publisher_id'
			| 'adProvider_id'
			| 'dealYn'
			| 'revenueUSD'
			| 'revenueKRW'
			> = {
			grandTotal: false,
			fieldPadding: false,
			match: {
				date: { $gte: startDate, $lte: endDate },
				adProvider_id: { $in: adProvider_ids }
			},
			dimensions: [
				{ name: 'date', alias: 'date', expr: dateExpr },
				{ name: 'publisher_id', alias: 'publisher_id' },
				{ name: 'adProvider_id', alias: 'adProvider_id' },
				{ name: 'dealYn', alias: 'dealYn' },
			],
			metrics: {
				revenueUSD: { $sum: '$revenueUSD' },
				revenueKRW: { $sum: '$revenueKRW' },
			},
		};

		return await this.aggregator.aggregate(this.aprDailyModel, spec).AsMergedArray({ concurrency: 1 });
	}
}
