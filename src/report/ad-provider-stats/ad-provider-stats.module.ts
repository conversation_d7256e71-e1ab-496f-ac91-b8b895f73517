import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { API_DB_NAME } from '../../db/constants';

import { AggregatorModule } from "../aggregator/aggregator.module";

import { AdProviderStatsController } from "./ad-provider-stats.controller";
import { TimeoutService } from "./timeout/timeout.service";
import { AdProviderService } from "./ad-provider/ad-provider.service";
import { AdProviderValidator } from "./ad-provider/ad-provider.validator";

import { AdProviderRevenueDailyModel } from "../schema/ad-provider-revenue-daily.schema";
import { AdProviderTimeoutDailyModel } from "../schema/ad-provider-timeout-daily.schema";

@Module({
	imports: [
		MongooseModule.forFeature([
			AdProviderRevenueDailyModel,
			AdProviderTimeoutDailyModel,
		], API_DB_NAME),
		AggregatorModule,
	],
	controllers: [AdProviderStatsController],
	providers: [TimeoutService, AdProviderService, AdProviderValidator],
	exports: [],
})
export class AdProviderStatsModule {}
