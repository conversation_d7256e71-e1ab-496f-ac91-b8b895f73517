class SspError extends Error {
	constructor(errorStatus, errorName, errorCode, additionalParams={}) {
		super();
		this.name = errorName;
		this.code = errorCode.code;
		this.message = errorCode.message;
		this.param = additionalParams;

		const params = Object.entries(additionalParams).map(([key, value]) => ({key,value}));
		if (params != {}) {
			params.forEach(p => this.message = this.message.replace(`{{${p.key}}}`, p.value));
		}

		// this.expose = true;
		this.status = errorStatus;
	}
}

export class BusinessError extends SspError {
	constructor(...args) {
		super(500, 'BusinessError', ...args);
		Error.captureStackTrace(this, BusinessError);
	}
}

export class BadRequestError extends SspError {
	constructor(...args) {
		super(400, 'BadRequestError', ...args);
		Error.captureStackTrace(this, BadRequestError);
	}
}

export class ForbiddenError extends SspError {
	constructor(...args) {
		super(403, 'ForbiddenError', ...args);
		Error.captureStackTrace(this, ForbiddenError);
	}
}

export class NotFoundError extends SspError {
	constructor(...args) {
		super(404, 'NotFoundError', ...args);
		Error.captureStackTrace(this, NotFoundError);
	}
}
