'use strict';

import * as logger from '../utils/logger.util';
import _ from 'lodash';

import { NotFoundError } from './error';
import SBE from './error-code';

process.on('unhandledRejection', err => {
	logger.error(`[unhandledRejection] Error :: \n ${err.stack} \n`, { err });
});

process.on('uncaughtException', err => {
	logger.error(`[uncaughtException] Error :: \n ${err.stack} \n`, { err });
});


export default (app) => {
	app.use(async (ctx, next) => {
		try {
			await next();
			if (ctx.status === 404) {
				throw new NotFoundError(SBE.Common.NotFound, { url: ctx.request.url });
			}
		} catch (err) {
			ctx.status = err.status || 500;

			if (_.isNil(err.code)) {
				ctx.body = {
					code: SBE.Common.Default.code,
					message: SBE.Common.Default.message.replace('{{msg}}', err.message)
				};
			} else {
				ctx.body = {
					code: err.code,
					message: err.message,
					param: err.param
				};
			}

			ctx.app.emit('error', err, ctx);
		}
	});

	// error logging
	app.on('error', (err, ctx) => {
		const state = ctx.state;

		logger.error(`[error-handler] Error :: \n ${err.stack} \n`, { err, state });

		// switch (err.name) {
		// 	case 'BusinessError':
		// 		logger.info(err.stack, JSON.stringify({ state }, null, 2));
		// 		break;
		// 	default:
		// 		logger.error(err.stack, JSON.stringify({ state }, null, 2));
		// 		break;
		// }
	});
}
