'use strict';

import fs from 'fs';
import path from 'path';
import mongoose from 'mongoose';
import logger from '../utils/logger.util';
import _ from 'lodash';

mongoose.Promise = Promise;

let database = {};

let getFileNames = (dir, extension='.js') => {
	return fs
		.readdirSync(dir)
		.filter(
			item =>
				fs.statSync(path.join(dir, item)).isFile() &&
				(_.isNil(extension) || path.extname(item) === extension) &&
				item.includes('schema')
		);
};

let createSchema = (config) => {
	let schemaDir = path.resolve(process.cwd() + config.schema_root);
	let fileNames = getFileNames(schemaDir);

	for (let fileName of fileNames) {
		let createSchemaAndModel = require(path.resolve(schemaDir, fileName)).default;
		createSchemaAndModel(database.db);

		logger.info('[mongoose] %s 스키마 정의함', fileName);
	}
};

let initDatabase = async (config) => {
	// db connection
	try {
		const conn = await mongoose.createConnection(config.db_url, {
			poolSize: config.db_poolsize, // poolSize default : 5
			autoIndex: false,
			useNewUrlParser: true,
			useCreateIndex: true, // DeprecationWarning: collection.ensureIndex is deprecated. Use createIndexes instead.
			useFindAndModify: false, //DeprecationWarning: collection.findAndModify is deprecated. Use findOneAndUpdate, findOneAndReplace or findOneAndDelete instead.

			// MongoDB ReplicaSetNoPrimary 에러 대응 (https://oss.navercorp.com/da-ssp/bts/issues/960)
			useUnifiedTopology: false, //DeprecationWarning: current Server Discovery and Monitoring engine is deprecated, and will be removed in a future version. To use the new Server Discover and Monitoring engine, pass option { useUnifiedTopology: true } to the MongoClient constructor.

			connectTimeoutMS: 30 * 60 * 1000, // 30분
			socketTimeoutMS: 30 * 60 * 1000, // 30분

			writeConcern: { w: 'majority', j: true }
		});
		logger.info('[mongoose] 데이터베이스에 연결됨.');

		database.db = conn;

		// logger.info('[mongoose] CMS Side - ', database.db.client);
		createSchema(config);
	} catch(err) {
		logger.error('[mongoose] mongoose connection error', err);
	}

	// DB 연결이 종료되면 다시 연결을 시도한다. => 시도 하지 않음.. 무한 요청하다가 서버가 다운됨
	mongoose.connection.on('disconnected', () => {
		logger.info('[mongoose] 데이터베이스 연결 종료...');
	});

	return database;
};

let getDatabase = () => database;
export { getDatabase };

export default initDatabase;
