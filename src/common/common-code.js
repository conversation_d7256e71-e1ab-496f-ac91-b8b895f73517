'use strict';

import * as _ from "lodash";
import moment from 'moment-timezone';
import * as logger from "../utils/logger.util";
import {CommonCode} from "../models/data/common-code.schema";

let codeMap = new Map();
module.exports.load = async () => {
	const result = await CommonCode.aggregate([
		{
			$group: {
				_id: "$category",
				codes: { $push: "$code" }
			}
		},
		{
			$project: {
				_id: 0,
				category: "$_id",
				codes: 1
			}
		}
	]);
	const codesByCategory = result.reduce((acc, item) => {
		acc[item.category] = item.codes;
		return acc;
	}, {});
	const tempMap = new Map(Object.entries(codesByCategory));
	console.log(tempMap);
	codeMap = tempMap;
}

module.exports.getCodes = async (category) => {
	return codeMap.get(category);
}

module.exports.getCode = async (category, code) => {
	return codeMap.get(category).find(item => item === code);
}

module.exports.getStatic = async (category) => {
	return codeMap.get(category).map(code => camelToUpperSnakeCase(category + code));
}

const camelToUpperSnakeCase = (str) => {
	return str.replace(/([a-z])([A-Z])/g, '$1_$2').toUpperCase();
}

const camelCaseString = "apRptStateNotSpecified";
const upperSnakeCaseString = camelToUpperSnakeCase(camelCaseString);
console.log(upperSnakeCaseString); // "AP_RPT_STATE_NOT_SPECIFIED"

export {codeMap};
