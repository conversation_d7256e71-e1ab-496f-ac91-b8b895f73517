export const InvoicePaymentStatus = Object.freeze({
  Pending: "PENDING",
  Confirm: "CONFIRM",
  InProgress: "IN_PROGRESS",
  CarriedOver: "CARRIED_OVER",
  Excluded: "EXCLUDED",
});
export const InvoiceIssueStatus = Object.freeze({
  Pending: "PENDING",
  Transmitting: "TRANSMITTING",
  Transmitted: "TRANSMITTED",
  ToBeRevised: "TO_BE_REVISED",
  Uploaded: "UPLOADED",
  Failed: "FAILED",
});
export const InvoiceIssueType = Object.freeze({
  HandWritten: "HAND_WRITTEN",
  Invoice: "INVOICE",
});
export const InvoiceIssueUnit = Object.freeze({
  Corporation: "CORPORATION",
  Publisher: "PUBLISHER",
});
export const InvoiceIssueItem = Object.freeze({
  Publisher: "PUBLISHER",
  BillingGroup: "BILLING_GROUP",
  Service: "SERVICE",
  CarriedOver: "CARRIED_OVER",
  Etc: "ETC",
});
export const CorporationType = Object.freeze({
  AdProvider: "AP",
  Publisher: "PUBLISHER",
});
export const CorporationClientType = Object.freeze({
  Outside: "OUTSIDE",
  Family: "FAMILY",
  Naver: "NAVER"
})
