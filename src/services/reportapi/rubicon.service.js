'use strict';

import _ from 'lodash';
import moment from 'moment-timezone';

import { BusinessError } from '../../common/error';

import * as logger from '../../utils/logger.util';

import * as reportApiService from '../../services/reportapi/report-api.service';

const NON_RK_LOGGER = 'report_api_non_rk';

// 요청당 최대 데이터 수
const DATA_LIMIT = 50000;


/**
 * [PROCESS 3:: TASK 2] processReportApi : RUBICON 리포트 API 연동 및 리포트 파일 다운로드
 *  SUBTASK 1. initCsvReport : csv 파일 초기화
 *  SUBTASK 2. _getCredential : credential 생성하기
 *  SUBTASK 3. 리포트 데이터 생성 요청 및 다운로드
 * 		3-1. _getDateList : 시작일-종료일 간 날짜 리스트 가져오기
 * 		3-2. _requestReport : 리포트 데이터 생성 요청하기
 * 		3-3. _requestReportStatus : 리포트 데이터 생성 확인 요청하기
 * 		3-4. _requestReportDownload : 리포트 파일 다운로드하기
 * 		3-5. _getDataList : 리포트 데이터 가공하기
 * 		3-6. writeCsvReport : 리포트 데이터 csv 파일 쓰기
 *
 * @RequestMapping(value='/batch/day/outside/rubicon')
 *
 * @param {Object} adProviderMetaInfo
 * {
		reportInfos: { ACCOUNT_ID, API_KEY, API_SECRET },
		reportInfoExt: { apiResultPath, dimensions, metrics, fields, report_request_api, report_status_api, report_download_api },
		period: { startDate, endDate },
		fda, countryMappings
 * }
 */
module.exports.processReportApi = async ({ reportInfos: { ACCOUNT_ID, API_KEY, API_SECRET }, reportInfoExt: { apiResultPath, dimensions, metrics, fields, report_request_api, report_status_api, report_download_api }, period: { startDate, endDate }, fda, countryMappings }) => {
	logger.debug(NON_RK_LOGGER, '[rubicon.service :: processReportApi] 호출됨');


	/* SUBTASK 1. initCsvReport : csv 파일 초기화 */
	// 필드 순서 : ymd, zoneId, country, os, size, imp, net_revenue
	await reportApiService.initCsvReport({ fda, fields });

	/* SUBTASK 2. _getCredential : credential 생성하기 */
	const credential = await _getCredential({ API_KEY, API_SECRET });


	/*
		[SUBTASK 3] 리포트 데이터 생성 요청 및 다운로드
	*/

	// SUBTASK 3-1. _getDateList : 시작일-종료일 간 날짜 리스트 가져오기
	const dateList = await _getDateList(startDate, endDate);

	let dataList = new Array();

	for (let date of dateList) {
		// SUBTASK 3-2. _requestReport : 리포트 데이터 생성 요청하기
		const reportId = await _requestReport({ credential, ACCOUNT_ID, dimensions, metrics, report_request_api, date });

		// SUBTASK 3-3. _requestReportStatus : 리포트 데이터 생성 확인 요청하기
		await _requestReportStatus({ credential, ACCOUNT_ID, reportId, report_status_api });

		// SUBTASK 3-4. _requestReportDownload : 리포트 파일 다운로드하기
		const rawDataList = await _requestReportDownload({ credential, ACCOUNT_ID, reportId, report_download_api });

		// SUBTASK 3-5. _getDataList : 리포트 데이터 가공하기
		const subDataList = await _getDataList({ fields, rawDataList, countryMappings, date });

		dataList = _.concat(dataList, subDataList);
	}

	// SUBTASK 3-6. writeCsvReport : 리포트 데이터 csv 파일 쓰기
	await reportApiService.writeCsvReport({ fda, apiResultPath, fields, dataList });
};


/**
 * SUBTASK 2. _getCredential : credential 생성하기
 *
 * @param {Object} { API_KEY, API_SECRET }
 * @return {String} credential
 */
const _getCredential = async ({ API_KEY, API_SECRET }) => {
	logger.debug(NON_RK_LOGGER, `[rubicon.service :: _getCredential] credential 생성하기`);

	const targetStr = `${API_KEY}:${API_SECRET}`;

	// base64 encoding
	const credential = Buffer.from(targetStr).toString('base64');

	return credential;
};


/**
 * SUBTASK 3-1. _getDateList : 시작일-종료일 간 날짜 리스트 가져오기
 *
 * @param {Date} startDate
 * @param {Date} endDate
 * @return {Array} dateList 날짜 리스트 ['2019-01-01', '2019-01-02', ... ]
 */
const _getDateList = async (startDate, endDate) => {
	// [ERROR] 날짜 값이 없을 경우, 에러 처리
	if(_.isNil(startDate) || _.isEmpty(startDate) || _.isNil(endDate) || _.isEmpty(endDate)) {
		throw new BusinessError({ message: `[rubicon.service :: _getDateList] 날짜 정보 없음` });
	}

	// 날짜 리스트
	let dateList = new Array();

	// 날짜 리스트 생성(시작일 ~ 종료일)
	let tempDate = moment(startDate);
	while (tempDate.isSameOrBefore(endDate)) {
		dateList.push(tempDate.format('YYYY-MM-DD'));
		tempDate.add(1, 'days');
	}

	return dateList;
};


/**
 * SUBTASK 3-2. _requestReport : 리포트 데이터 생성 요청하기
 *
 * @param {Object} 리포트 조회 조건 { credential, ACCOUNT_ID, dimensions, metrics, report_request_api, date }
 * @return {Array} reportId
 */
const _requestReport = async ({ credential, ACCOUNT_ID, dimensions, metrics, report_request_api, date }) => {
	logger.debug(NON_RK_LOGGER, `[rubicon.service :: _requestReport] 리포트 데이터 생성 요청`);

	const headers = {
		Authorization: `Basic ${credential}`,
		'Content-Type': 'application/json'
	};

	const body = {
		criteria: {
			// 날짜 조회 범위 : start <= DATE <= end
			// PST(Pacific Standard Time) 조회 시 : start=2020-01-01T00:00:00-08:00&end=2020-01-01T23:59:59-08:00
			// PDT(Pacific Daylight Time) 조회 시 : start=2020-05-05T00:00:00-07:00&end=2020-05-05T23:59:59-07:00
			// PDT(Pacific Daylight Time) 시작일 조회 시 (PST -> PDT) : start=2020-03-08T00:00:00-08:00&end=2020-03-08T23:59:59-07:00
			// PDT(Pacific Daylight Time) 종료일 조회 시 (PDT -> PST) : start=2020-11-01T00:00:00-07:00&end=2020-11-01T23:59:59-08:00
			start: moment.tz(`${date}T00:00:00`, 'America/Los_Angeles').format(),
			end: moment.tz(`${date}T23:59:59`, 'America/Los_Angeles').format(),

			dimension: dimensions.join(','),
			metric: metrics.join(','),
			limit: 500000
		}
	};

	const requestApiUrl = `${report_request_api}?account=publisher/${ACCOUNT_ID}`;

	// res : { message:'', data: { items: [{ date }] } }
	const res = await reportApiService.requestApi({ url: requestApiUrl, method: 'POST', headers, body });

	// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
	if (!_.isNil(res.err)) {
		throw new BusinessError({ message: `[rubicon.service :: _requestReport] 리포트 데이터 생성 요청 실패 ::: statusCode(${res.status}), url(${res.url})` }, { err: res });
	}

	// [ERROR] 응답 결과에 message가 있는 경우, 에러 처리
	if (_.isEmpty(res.offline_report_id.toString())) {
		throw new BusinessError({ message: `[rubicon.service :: _requestReport] 리포트 데이터 생성 요청 실패 ::: ${JSON.stringify(res, null, 2)}` }, { err: res, detail: JSON.stringify(res, null, 2)});
	}

	const reportId = res.offline_report_id.toString();

	logger.debug(NON_RK_LOGGER, `[rubicon.service :: _requestReport] 리포트 데이터 생성 요청 완료 ( reportId = ${reportId} )`);

	return reportId;
};


/**
 * SUBTASK 3-3. _requestReportStatus : 리포트 데이터 생성 확인 요청하기
 *
 * @param {Object} { credential, ACCOUNT_ID, reportId, report_status_api }
 */
const _requestReportStatus = async ({ credential, ACCOUNT_ID, reportId, report_status_api }) => {
	logger.debug(NON_RK_LOGGER, `[rubicon.service :: _requestReportStatus] 리포트 데이터 생성 확인 요청 ( reportId = ${reportId} )`);

	const TIMEOUT = moment().add(60, 'minutes');

	const headers = {
		Authorization: `Basic ${credential}`,
	};

	const url = `${_.replace(report_status_api, '{{REPORT_ID}}', reportId)}?account=publisher/${ACCOUNT_ID}`;

	// res : { offline_report_id: 431119, status: 'queued', created: '2024-09-04T06:57:36Z', updated: '2024-09-04T06:57:36Z' }
	let res = await reportApiService.requestApi({ url, headers });

	// [ERROR] 응답 결과에 err 가 있는 경우, 에러 처리
	if (!_.isNil(res.err)) {
		throw new BusinessError({ message: `[rubicon.service :: _requestReportStatus] 리포트 데이터 생성 확인 요청 실패 ::: statusCode(${res.status}), url(${res.url})` }, { err: res });
	}

	// [ERROR] 응답 결과에 status 가 error 인 경우, 에러 처리
	if (!_.isNil(res.status) && _.isEqual(res.status, 'error')) {
		throw new BusinessError({ message: `[rubicon.service :: _requestReportStatus] 리포트 데이터 생성 확인 요청 실패 ::: ${JSON.stringify(res, null, 2)}` });
	}


	// 리포트 다운로드 요청중 (loop)
	// res.status = queued / success / error / pending / canceled
	while (!_.isEqual(res.status, 'success')) {
		logger.debug(NON_RK_LOGGER, `[rubicon.service :: _requestReportStatus] 리포트 데이터 생성 확인 재요청중... ( reportId = ${reportId}, status = ${res.status} )`);

		// [ERROR] TIMEOUT 에러 처리
		if(TIMEOUT.isSameOrBefore(moment())) {
			throw new BusinessError({ message: `[rubicon.service :: _requestReportStatus] 리포트 데이터 생성 확인 요청 실패. TIMEOUT 60분` });
		}

		// 1분 sleep
		await reportApiService.sleep(60 * 1000);

		res = await reportApiService.requestApi({ url, headers });

		// [ERROR] 응답 결과에 err 가 있는 경우, 에러 처리
		if (!_.isNil(res.err)) {
			throw new BusinessError({ message: `[rubicon.service :: _requestReportStatus] 리포트 데이터 생성 확인 재요청 실패 ::: statusCode(${res.status}), url(${res.url})` }, { err: res });
		}

		// [ERROR] 응답 결과에 status 가 error 인 경우, 에러 처리
		if (!_.isNil(res.status) && _.isEqual(res.status, 'error')) {
			throw new BusinessError({ message: `[rubicon.service :: _requestReportStatus] 리포트 데이터 생성 확인 재요청 실패 ::: ${JSON.stringify(res, null, 2)}` });
		}
	}

	logger.debug(NON_RK_LOGGER, `[rubicon.service :: _requestReportStatus] 리포트 데이터 생성 확인 요청 완료 ( reportId = ${reportId}, status = ${res.status} )`);
};


/**
 * SUBTASK 3-4. _requestReportDownload : 리포트 다운로드 요청하기
 *
 * @param {Object} { credential, ACCOUNT_ID, reportId, report_download_api }
 * @returns {Array} rawDataList
 */
const _requestReportDownload = async ({ credential, ACCOUNT_ID, reportId, report_download_api }) => {
	logger.debug(NON_RK_LOGGER, `[rubicon.service :: _requestReportDownload] 리포트 다운로드 요청 ( reportId = ${reportId} )`);

	// 리포트 데이터
	let rawDataList = new Array();

	const headers = {
		Authorization: `Basic ${credential}`,
	};

	const requestApiUrl = `${_.replace(report_download_api, '{{REPORT_ID}}', reportId)}?account=publisher/${ACCOUNT_ID}&format=json&size=${DATA_LIMIT}`;

	// 페이징 정보
	let page = 1;

	// loop 가 true인 경우, 계속 반복
	while(true) {
		const url = `${requestApiUrl}&page=${page}`;

		// res : { pageInfo:{ rows: 1000, size: 1000, total_elements: 6424, total_pages: 7, number: 1 }, content: [ { date: '2024-08-31', zone_id: '2606244', country: 'India', device_os_name: 'Android', size: 'Mobile Banner 1 (320x50)', paid_impression: 80299, seller_net_revenue: 2.*************** }, ...] }
		const res = await reportApiService.requestApi({ url, headers });

		// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
		if (!_.isNil(res.err)) {
			throw new BusinessError({ message: `[rubicon.service :: _requestReportDownload] API 요청 실패 ::: statusCode(${res.status}), url(${res.url}), err(${res.err})` }, { err: res });
		}

		const pageInfo = res.pageInfo;
		const isLastPage= pageInfo.total_pages === pageInfo.number;

		// 데이터가 있으면, rawDataList 및 page 셋팅
		if (pageInfo.rows > 0) {
			logger.debug(NON_RK_LOGGER, `[rubicon.service :: _requestReportDownload] data offset = ( ${((page - 1) * DATA_LIMIT) + 1} ~ ${isLastPage? pageInfo.total_elements : page * DATA_LIMIT} )`);

			// rawDataList 셋팅
			rawDataList = _.concat(rawDataList, res.content);

			// 다음 페이지 셋팅
			page++;
		}

		// 마지막 페이지이거나 또는 res.rows 가 0인 경우, loop 종료
		if (isLastPage || pageInfo.rows === 0) {
			break;
		}
	}

	logger.debug(NON_RK_LOGGER, `[rubicon.service :: _requestReportDownload] 리포트 다운로드 요청 완료`);

	return rawDataList;
};


/**
 * SUBTASK 3-5. _getDataList : 리포트 데이터 가공하기
 *
 * @param {Object} { fields, rawDataList, countryMappings, date }
 * @return {Array} dataList
 */
const _getDataList = async ({ fields: { ymd, country, imp, net_revenue }, rawDataList, countryMappings, date }) => {
	logger.debug(NON_RK_LOGGER, `[rubicon.service :: _getDataList] 리포트 데이터 가공 ( date= ${date} )`);

	const isStartDtDst = moment.tz(`${date}T00:00:00`, 'America/Los_Angeles').isDST();
	const isEndDtDst = moment.tz(`${date}T23:59:59`, 'America/Los_Angeles').isDST();

	logger.debug(NON_RK_LOGGER, `[rubicon.service :: _getDataList] DST 여부 ( isStartDtDst= ${isStartDtDst} / isEndDtDst= ${isEndDtDst} )`);

	let dataList = new Array();

	rawDataList.map(rawData => {
		// imp, net_revenue 가 모두 0인 건들은 걸러낸다.
		if (_.isEqual(rawData[imp], 0) && _.isEqual(rawData[net_revenue], 0.0)) {
			return;
		}

		let data = Object.assign({}, rawData);

		// PDT 시작일/종료일에 해당하는 경우, 응답 데이터의 날짜 정보를 요청 기준 날짜로 변경함
		// 	- PST -> PDT 는 이슈 없음
		// 	- PDT -> PST 로 변경 되는 날짜의 경우, 날짜 데이터가 쪼개져서 오는 경우가 있음
		// 		- ex > 2023/11/05 이 PDT -> PST 로 적용 되는 날인데, 응답 결과 2023/11/05, 2023/11/06 로 리포트 데이터를 쪼개서 줌
		// 			- startDate = 2023-11-05T00:00:00-07:00
		// 			- endDate = 2023-11-05T23:59:59-08:00
		// 				- 2023-11-05T23:59:59-08:00 = 2023-11-06T00:00:00-07:00 라서 2023-11-06 이 포함 되어 나오는 것으로 추정됨
		// 				- 따라서 2023-11-05 로 강제 셋팅해줌
		if (isStartDtDst !== isEndDtDst) {
			data[ymd] = date;
		}

		// GFP에서 지원하는 타겟 국가인 경우, 국가 코드로 변환
		data[country] = (!_.isNil(countryMappings[data[country]])) ? countryMappings[data[country]] : data[country];

		dataList.push(data);
	});

	return dataList;
};
