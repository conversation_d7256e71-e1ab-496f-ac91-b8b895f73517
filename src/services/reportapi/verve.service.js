'use strict';

import _ from 'lodash';
import moment from 'moment';

import { BusinessError } from '../../common/error';

import * as logger from '../../utils/logger.util';

import * as reportApiService from '../reportapi/report-api.service';

const NON_RK_LOGGER = 'report_api_non_rk';


/**
 * [PROCESS 3:: TASK 2] processReportApi : VERVE 리포트 API 연동 및 리포트 파일 다운로드
 *  SUBTASK 1. initCsvReport : csv 파일 초기화
 *  SUBTASK 2. 리포트 데이터 생성 요청 및 다운로드
 *        2-1. _requestReport : 리포트 데이터 생성 요청하기
 *        2-2. _getDataList : 리포트 데이터 가공하기
 *        2-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
 *
 * @RequestMapping(value='/batch/day/outside/verve')
 *
 * @param {Object} adProviderMetaInfo
 * {
		reportInfos: { AUTH_TOKEN },
		reportInfoExt: { apiResultPath, fields, group_by, report_request_api },
		period: { startDate, endDate },
		fda
 * }
 */
module.exports.processReportApi = async ({ reportInfos: { AUTH_TOKEN }, reportInfoExt: { apiResultPath, fields, group_by, report_request_api }, period: { startDate, endDate }, fda }) => {
	logger.debug(NON_RK_LOGGER, '[verve.service :: processReportApi] 호출됨');


	/* SUBTASK 1. initCsvReport : csv 파일 초기화 */
	// 필드 순서 : ymd, app_id, country, imp, clk, net_revenue
	await reportApiService.initCsvReport({ fda, fields });


	/*
		SUBTASK 2. 리포트 데이터 생성 요청 및 다운로드
	*/

	// SUBTASK 2-1. _requestReport : 리포트 데이터 생성 요청하기
	const rawDataList = await _requestReport({ report_request_api, group_by, period: { startDate, endDate }, reportInfos: { AUTH_TOKEN } });

	// SUBTASK 2-2. _getDataList : 리포트 데이터 가공하기
	const dataList = await _getDataList({ fields, rawDataList });

	// SUBTASK 2-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
	await reportApiService.writeCsvReport({ fda, apiResultPath, fields, dataList });
};


/**
 * SUBTASK 2-1. _requestReport : 리포트 데이터 생성 요청하기
 *
 * @param {Object} 리포트 조회 조건 { report_request_api, group_by, period: { startDate, endDate }, reportInfos: { AUTH_TOKEN } }
 * @return {String} reportId
 */
const _requestReport = async ({ report_request_api, group_by, period: { startDate, endDate }, reportInfos: { AUTH_TOKEN } }) => {
	logger.debug(NON_RK_LOGGER, `[verve.service :: _requestReport] 리포트 데이터 생성 요청`);

	const startDt = moment(startDate).format('YYYY-MM-DD');
	const endDt = moment(endDate).format('YYYY-MM-DD');

	// https://dashboard.pubnative.net/api/reports?account_auth_token=4dda17e344da70b7701c2330e6b2233f7973956c81f1aed3181abb98c0ca6d88&date_format=yyyy-mm-dd&start_date=2025-01-01&end_date=2025-01-07&group_by[]=date&group_by[]=store_app_id&group_by[]=country_code
	const url = `${report_request_api}?account_auth_token=${AUTH_TOKEN}&date_format=yyyy-mm-dd&start_date=${startDt}&end_date=${endDt}&${group_by.map(dim => `group_by[]=${dim}`).join('&')}`;

	// res : { status: 'success', reports: [{ date: '2025-01-01', store_app_id: '**********', country_code: 'AW', impressions: 29373, clicks: 8, revenues: 1.94, ctr, ad_server_fill_rate, ad_server_requests, ad_server_filled_request, ecpm }]}
	const res = await reportApiService.requestApi({ url });

	// [ERROR] 응답 결과에 err 가 있는 경우, 에러 처리
	if (!_.isNil(res.err)) {
		throw new BusinessError({ message: `[verve.service :: _requestReport] API 요청 실패 ::: statusCode(${res.status}), url(${res.url})` }, { err: res });
	}

	const rawDataList = res.reports;

	logger.debug(NON_RK_LOGGER, `[verve.service :: _requestReport] 리포트 데이터 생성 요청 완료`);

	return rawDataList;
};


/**
 * SUBTASK 2-2. _getDataList : 리포트 데이터 가공하기
 *
 * @param {Object} { fields: { ymd, place_keys: { placement_id }, country, clk }, rawDataList }
 * @return {Array} dataList
 */
const _getDataList = async ({ fields: { imp, clk, net_revenue }, rawDataList }) => {
	logger.debug(NON_RK_LOGGER, `[verve.service :: _getDataList] 리포트 데이터 가공`);

	const dataList = new Array();

	// [{ date: '2025-01-01', store_app_id: '**********', country_code: 'AW', impressions: 29373, clicks: 8, revenues: 1.94 }]
	rawDataList.map(rawData => {
		// imp, clk, net_revenue 가 모두 0인 건들은 걸러낸다.
		if (_.isEqual(rawData[imp], 0) && _.isEqual(rawData[clk], 0) && _.isEqual(rawData[net_revenue], 0.0)) {
			return;
		}

		let data = Object.assign({}, rawData);

		// 리포트 파일(csv) 생성 시, clk 은 optional 이라 0인 경우 공백 처리되므로, 스트링으로 변환해준다.
		data[clk] = data[clk] + '';

		dataList.push(data);
	});

	return dataList;
};
