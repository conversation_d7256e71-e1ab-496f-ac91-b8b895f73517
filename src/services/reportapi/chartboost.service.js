'use strict';

import _ from 'lodash';
import moment from 'moment';

import { BusinessError } from '../../common/error';

import * as logger from '../../utils/logger.util';

import * as reportApiService from '../reportapi/report-api.service';

const NON_RK_LOGGER = 'report_api_non_rk';


/**
 * [PROCESS 3:: TASK 2] processReportApi : CHARTBOOST 리포트 API 연동 및 리포트 파일 다운로드
 *  SUBTASK 1. initCsvReport : csv 파일 초기화
 *  SUBTASK 2. _getAccessToken : 토큰 생성하기
 *  SUBTASK 3. 리포트 데이터 생성 요청 및 다운로드
 * 		3-1. _requestReport : 리포트 데이터 생성 요청하기
 * 		3-2. _getDataList : 리포트 데이터 가공하기
 * 		3-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
 * 
 * @RequestMapping(value='/batch/day/outside/chartboost')
 * 
 * @param {Object} adProviderMetaInfo 
 * { 
		reportInfos: { CLIENT_ID, CLIENT_SECRET },
		reportInfoExt: { apiResultPath, fields, token_request_api, report_request_api },
		period: { startDate, endDate },
		fda
 * }
 */
module.exports.processReportApi = async ({ reportInfos: { CLIENT_ID, CLIENT_SECRET }, reportInfoExt: { apiResultPath, fields, token_request_api, report_request_api }, period: { startDate, endDate }, fda }) => {
	logger.debug(NON_RK_LOGGER, '[chartboost.service :: processReportApi] 호출됨');


	/* SUBTASK 1. initCsvReport : csv 파일 초기화 */
	// 필드 순서 : ymd, appId, adLocation, country, os, imp, clk, net_revenue
	await reportApiService.initCsvReport({ fda, fields });


	/* SUBTASK 2. _getAccessToken : 토큰 생성하기 */
	const accessToken = await _getAccessToken({ CLIENT_ID, CLIENT_SECRET, token_request_api });


	/*
		SUBTASK 3. 리포트 데이터 생성 요청 및 다운로드
	*/

	// SUBTASK 3-1. _requestReport : 리포트 데이터 생성 요청하기
	const rawDataList = await _requestReport({ report_request_api, period: { startDate, endDate }, accessToken });

	// SUBTASK 3-2. _getDataList : 리포트 데이터 가공하기
	const dataList = await _getDataList({ fields, rawDataList });

	// SUBTASK 3-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
	await reportApiService.writeCsvReport({ fda, apiResultPath, fields, dataList });
};


/**
 * SUBTASK 2. _getAccessToken : 토큰 요청하기
 *
 * @param {Object} { CLIENT_ID, CLIENT_SECRET, token_request_api }
 * @return {String} accessToken
 */
const _getAccessToken = async ({ CLIENT_ID, CLIENT_SECRET, token_request_api }) => {
	logger.debug(NON_RK_LOGGER, `[chartboost.service :: _getAccessToken] 토큰 요청하기`);

	const body = {
		client_id: CLIENT_ID,
		client_secret: CLIENT_SECRET,
		audience: 'https://public.api.gateway.chartboost.com',
		grant_type: 'client_credentials'
	};

	// res : { access_token: 'xxxx', expires_in: 86400, token_type: 'Bearer', scope }
	const res = await reportApiService.requestApi({ url: token_request_api, method: 'POST', body, headers: { 'Content-Type': 'application/json' } });

	// [ERROR] 응답 결과에 에러가 있는 경우, 에러 처리
	if (!_.isNil(res) && !_.isNil(res.err)) {
		throw new BusinessError({ message: `[chartboost.service :: _getAccessToken] 토큰 요청 에러`}, { err: res, detail: JSON.stringify(res, null, 0) });
	}

	const accessToken = res.access_token;

	// [ERROR] accessToken가 없는 경우, 에러 처리
	if (_.isNil(accessToken) || _.isEmpty(accessToken)) {
		throw new BusinessError({ message: `[chartboost.service :: _getAccessToken] 토큰 정보 없음` });
	}

	logger.debug(NON_RK_LOGGER, `[chartboost.service :: _getAccessToken] 토큰 요청 완료 ( accessToken= ${accessToken} )`);

	return accessToken;
};


/**
 * SUBTASK 3-1. _requestReport : 리포트 데이터 생성 요청하기
 *
 * @param {Object} { report_request_api, period: { startDate, endDate }, accessToken }
 * @return {Array} rawDataList
 */
const _requestReport = async ({ report_request_api, period: { startDate, endDate }, accessToken }) => {
	logger.debug(NON_RK_LOGGER, `[chartboost.service :: _requestReport] 리포트 데이터 생성 요청하기`);

	const startDt = moment(startDate).format('YYYY-MM-DD');
	const endDt = moment(endDate).format('YYYY-MM-DD');

	// dateMin <= date <= dateMax
	// 'adLocation=all' : 상위 100개까지만 제공하며, 나머지 데이터는 adLocation='Default' 로 집계 처리됨
	const requestApiUrl = `${report_request_api}?dateMin=${startDt}&dateMax=${endDt}&adLocation=all`;

	const headers = { 'Authorization': `Bearer ${accessToken}` };

	// res : [{ dt: '2025-03-19', appId: '5715903443150f36006ca8fd', adLocation: 'overall', countryCode: 'SG', platform: 'iOS', impressionsDelivered: 6, clicksDelivered: 0, moneyEarned: 0.006500000134110451, adType: 'rewarded_video', app: 'Wattpad - Free Books and eBook Reader', campaignType: 'network', cpcvEarned: 0.001083333355685075, ctrDelivered: 0.0, ecpmEarned: 1.083333355685075, installRateDelivered: 0.0, installsDelivered: 0, videoCompletedDelivered: 6 }]
	const res = await reportApiService.requestApi({ url: requestApiUrl, headers });

	// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
	if (!_.isNil(res.err)) {
		throw new BusinessError({ message: `[chartboost.service :: _requestReport] 리포트 데이터 생성 요청 실패` }, { err: res, detail: JSON.stringify(res, null, 0) });
	}

	const rawDataList = res;

	logger.debug(NON_RK_LOGGER, `[chartboost.service :: _requestReport] 리포트 데이터 생성 요청 완료`);

	return rawDataList;
};


/**
 * SUBTASK 3-2. _getDataList : 리포트 데이터 가공하기
 *
 * @param {Object} { fields: { imp, clk, net_revenue }, rawDataList }
 * @return {Array} dataList
 */
const _getDataList = async ({ fields: { imp, clk, net_revenue }, rawDataList }) => {
	logger.debug(NON_RK_LOGGER, `[chartboost.service :: _getDataList] 리포트 데이터 가공하기`);

	let dataList = new Array();

	rawDataList.map(rawData => {
		// imp, clk, net_revenue 가 모두 0인 건들은 걸러낸다.
		if (_.isEqual(rawData[imp], 0) && _.isEqual(rawData[clk], 0) && _.isEqual(rawData[net_revenue], 0.0)) {
			return;
		}

		let data = Object.assign({}, rawData);

		// 리포트 파일(csv) 생성 시, clk 은 optional 이라 0인 경우 공백 처리되므로, 스트링으로 변환해준다.
		data[clk] = data[clk] + '';

		dataList.push(data);
	});

	return dataList;
};
