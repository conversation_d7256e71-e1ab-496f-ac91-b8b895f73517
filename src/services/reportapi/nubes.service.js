'use strict';

import fs from 'fs';
import _ from 'lodash';
import moment from 'moment';

import * as logger from '../../utils/logger.util';

import { BusinessError } from '../../common/error';
import * as NubesClient from "../../nubes/clients";

const REPORT_API_LOGGER = 'report_api';


/**
 * processReportApi : Nubes 연동 정보 가져오기
 * 
 * @param {Object} adProviderMetaInfo 
 * GFD Sample =	{ 
		reportInfoExt: {
			nubes: {
				gatewayAddress: 'a-dev.nubes.sto.navercorp.com:8000',
				bucket: 'gfd-share-storage',
			}
		}
 * 	}
 * @return {Object} nubes
 */
module.exports.processReportApi = async ({ reportInfoExt: { nubes: { gatewayAddress, bucket } } }) => {
	logger.debug(REPORT_API_LOGGER, '[nubes.service :: processReportApi] 호출됨');

	// NUBES
	const nubesClient = new NubesClient.Client(gatewayAddress, bucket);

	return nubesClient;
};


/**
 * exists : nubes 에 파일이 존재하는지 체크
 * 
 * @param {Object} nubes nubes 클라이언트
 * @param {String} filePath 파일 경로
 * @return {Boolean} exists
 */
module.exports.exists = async (nubes, filePath) => {
	logger.debug(REPORT_API_LOGGER, `[nubes.service :: exists] 호출됨 (filePath= ${filePath})`);

	return await _exists(nubes, filePath);
};


/**
 * waitForNubesReady : NUBES 에 리포트 파일이 있는지 30분 동안 체크
 * 
 * @param {Object} nubes nubes 클라이언트
 * @param {String} filePath 파일 경로
 */
module.exports.waitForNubesReady = async (nubes, filePath) => {
	logger.debug(REPORT_API_LOGGER, `[nubes.service :: waitForNubesReady] 호출됨 ( filePath=${filePath} )`);

	// TIMEOUT 30분
	const TIMEOUT = moment().add(30, 'minutes');

	// true / false
	let isExists = await _exists(nubes, filePath);

	// NUBES 에 리포트 파일이 있는지 30분 동안 30초 간격으로 체크
	while(_.isEqual(isExists, false)) {
		// [ERROR] TIMEOUT 에러 처리
		if(TIMEOUT.isSameOrBefore(moment())) {
			throw new BusinessError({ message: `[nubes.service :: waitForNubesReady] TIMEOUT(30분) - NUBES 에 파일이 없음 ( filePath=${filePath} )` });
		}

		// 30초간 sleep
		await _sleep(30 * 1000);

		isExists = await _exists(nubes, filePath);

		logger.debug(REPORT_API_LOGGER, `[nubes.service :: waitForNubesReady] 재요청 ::: ${moment().format('YYYY-MM-DD HH:mm:ss')}`);
	}

	logger.debug(REPORT_API_LOGGER, `[nubes.service :: waitForNubesReady] 대기 종료 ( filePath=${filePath} )`);
};


/**
 * [Promise]
 * _exists : NUBES 에 파일이 있는지 확인
 * 
 * @param {Object} nubes nubes 클라이언트
 * @param {String} filePath 파일 경로
 * @return {Boolean} isExists
 */
const _exists = async (nubes, filePath) => {
	try {
		const stat = await nubes.stat(filePath);

		if (!_.isEmpty(stat)) {
			return true;
		} else {
			return false;
		}
	} catch (e) {
		return false;
	}
};


/**
 * [Promise]
 * _sleep : ms 만큼 sleep
 *
 * @param {int} ms
 */
const _sleep = ms => {
	return new Promise(resolve => {
		setTimeout(resolve, ms);
	});
};


/**
 * getFileTotalLineCount : 파일 총 라인수 가져오기
 * 
 * @param {Object} nubes nubes 클라이언트
 * @param {String} filePath 파일 경로
 * @return {Number} totalLineCount
 */
module.exports.getFileTotalLineCount = async (nubes, filePath) => {
	logger.debug(REPORT_API_LOGGER, '[nubes.service :: getFileTotalLineCount] 호출됨');

	return await _getFileTotalLineCount(nubes, filePath);
};


/**
 * [Promise]
 * _getFileTotalLineCount : 파일 총 라인수 가져오기
 * 
 * @param {Object} nubes nubes 클라이언트
 * @param {String} filePath
 * @return {Number} totalLineCount
 */
const _getFileTotalLineCount = (nubes, filePath) => {
	logger.debug(REPORT_API_LOGGER, `[nubes.service :: _getFileTotalLineCount] filePath=${filePath}`);

	return new Promise(async (resolve, reject) => {
		const NEW_LINE = '\n'.charCodeAt(0);
	
		let index = 0;
		let last_chunk = '';
		let totalLineCount = -1; // 헤더 정보 제외
		const nubesReadStream = await nubes.download(filePath);

		nubesReadStream.on('data', chunk => {
			try {
				last_chunk = chunk[chunk.length - 1];

				for (index = 0; index < chunk.length; ++index) {
					if (chunk[index] === NEW_LINE) totalLineCount++;
				}
			} catch (err) {
				reject({ message: `[nubes.service :: _getFileTotalLineCount] nubesReadStream data 에러`, err });
			}
		})
		.on('end', () => {
			// 파일 끝에 개행문자가 없는 경우, 1건 추가
			if(last_chunk !== NEW_LINE) {
				totalLineCount++;
			}

			resolve(totalLineCount);
		})
		.on('error', err => {
			reject({ message: `[nubes.service :: _getFileTotalLineCount] nubesReadStream 에러`, err });

			if (nubesReadStream && nubesReadStream.close) nubesReadStream.close();
		});
	});
};


/**
 * downloadNubesFile : 파일 다운로드하기
 * 
 * @param {Object} nubes nubes 클라이언트
 * @param {String} remotePath nubes 파일 경로
 * @param {String} localPath 로컬 파일 경로
 */
module.exports.downloadNubesFile = async (nubes, remotePath, localPath) => {
	logger.debug(REPORT_API_LOGGER, '[nubes.service :: downloadNubesFile] 호출됨');

	await _downloadNubesFile(nubes, remotePath, localPath);
};


const _downloadNubesFile = (nubes, remotePath, localPath) => {
	logger.debug(REPORT_API_LOGGER, `[nubes.service :: _downloadNubesFile] 호출됨 (remotePath= ${remotePath}, localPath= ${localPath})`);

	return new Promise(async (resolve, reject) => {
		const nubesReadStream = await nubes.download(remotePath);
		const fsWriteStream = fs.createWriteStream(localPath);

		nubesReadStream.on('error', err => {
			reject({ message: `[nubes.service :: _downloadNubesFile] nubesReadStream 에러`, err });
			closeStreams();
		});

		fsWriteStream.on('close', () => {
			logger.debug(REPORT_API_LOGGER, '[nubes.service :: _downloadNubesFile] fsWriteStream end 완료');

			resolve();
		})
		.on('error', err => {
			reject({ message: '[nubes.service :: _downloadNubesFile] fsWriteStream 에러', err });
			closeStreams();
		});

		nubesReadStream.pipe(fsWriteStream);

		// 모든 stream 종료 처리
		const closeStreams = () => {
			if (nubesReadStream && nubesReadStream.close) nubesReadStream.close();
			if (fsWriteStream) fsWriteStream.close();
		};
	});
}
