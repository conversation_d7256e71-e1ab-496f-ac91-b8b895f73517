'use strict';

import _ from 'lodash';

import { BusinessError } from '../../common/error';

import * as logger from '../../utils/logger.util';

import * as reportApiService from '../../services/reportapi/report-api.service';

const NON_RK_LOGGER = 'report_api_non_rk';


/**
 * [PROCESS 3:: TASK 2] processReportApi : IRONSOURCE 리포트 API 연동 및 리포트 파일 다운로드
 *  SUBTASK 1. initCsvReport : csv 파일 초기화
 *  SUBTASK 2. _getToken : 토큰 요청하기
 *  SUBTASK 3. 리포트 데이터 생성 요청 및 다운로드
 *        3-1. _requestReport : 리포트 데이터 생성 요청하기
 *        3-2. _getDataList : 리포트 데이터 가공하기
 *        3-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
 * 
 * @RequestMapping(value='/batch/day/outside/ironsource')
 * 
 * @param {Object} adProviderMetaInfo 
 * { 
		reportInfos: { SECRET_KEY, REFRESH_TOKEN },
		reportInfoExt: { apiResultPath, breakdowns, metrics, fields, token_request_api, report_request_api },
		period: { startDate, endDate },
		fda
 * }
*/
module.exports.processReportApi = async ({ reportInfos: { SECRET_KEY, REFRESH_TOKEN }, reportInfoExt: { apiResultPath, breakdowns, metrics, fields, token_request_api, report_request_api }, period: { startDate, endDate }, fda }) => {
	logger.debug(NON_RK_LOGGER, '[ironsource.service :: processReportApi] 호출됨');


	/* SUBTASK 1. initCsvReport : csv 파일 초기화 */
	// 필드 순서 : ymd, appKey, instanceId, country, os, imp, clk, net_revenue
	await reportApiService.initCsvReport({ fda, fields });

	/* SUBTASK 2. _getToken : 토큰 요청하기 */
	const token = await _getToken({ SECRET_KEY, REFRESH_TOKEN, token_request_api });


	/*
		SUBTASK 3. 리포트 데이터 생성 요청 및 다운로드
	*/

	// SUBTASK 3-1. _requestReport : 리포트 데이터 생성 요청하기
	const rawDataList = await _requestReport({ report_request_api, breakdowns, metrics, period: { startDate, endDate }, token });

	// SUBTASK 3-2. _getDataList : 리포트 데이터 가공하기
	const dataList = await _getDataList({ fields, rawDataList });

	// SUBTASK 3-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
	await reportApiService.writeCsvReport({ fda, apiResultPath, fields, dataList });
};


/**
 * SUBTASK 2. _getToken : 토큰 요청하기
 *
 * @param {Object} { SECRET_KEY, REFRESH_TOKEN, token_request_api }
 * @return {String} token
 */
const _getToken = async ({ SECRET_KEY, REFRESH_TOKEN, token_request_api }) => {
	logger.debug(NON_RK_LOGGER, `[ironsource.service :: _getToken] 토큰 요청하기`);

	const headers = {
		secretkey: SECRET_KEY,
		refreshToken: REFRESH_TOKEN
	};

	// res : '"xxxxxxxxxxxxxxxxxx"'
	const res = await reportApiService.requestApi({ url: token_request_api, headers });

	// [ERROR] 응답 결과에 에러가 있는 경우, 에러 처리
	if (!_.isNil(res) && !_.isNil(res.err)) {
		throw new BusinessError({ message: `[ironsource.service :: _getToken] 토큰 요청 에러`}, { err: res, detail: JSON.stringify(res, null, 0) });
	}

	// 토큰에 쌍따옴표가 포함되어 있어 제거함
	const token = res.replace(/"/g, '');

	// [ERROR] token 가 없는 경우, 에러 처리
	if (_.isNil(token) || _.isEmpty(token)) {
		throw new BusinessError({ message: `[ironsource.service :: _getToken] 토큰 정보 없음` });
	}

	logger.debug(NON_RK_LOGGER, `[ironsource.service :: _getToken] 토큰 요청 완료 ( token= ${token} )`);

	return token;
};


/**
 * SUBTASK 3-1. _requestReport : 리포트 데이터 생성 요청하기
 *
 * @param {Object} 리포트 조회 조건 { report_request_api, breakdowns, metrics, period: { startDate, endDate }, token }
 * @return {String} rawDataList
 */
const _requestReport = async ({ report_request_api, breakdowns, metrics, period: { startDate, endDate }, token }) => {
	logger.debug(NON_RK_LOGGER, `[ironsource.service :: _requestReport] 리포트 데이터 생성 요청`);

	// https://platform.ironsrc.com/partners/publisher/mediation/applications/v6/stats?startDate=2024-01-01&endDate=2024-01-10&breakdowns=date,app,instance,country,platform&metrics=impressions,clicks,revenue
	const requestApiUrl = `${report_request_api}?startDate=${startDate.format('YYYY-MM-DD')}&endDate=${endDate.format('YYYY-MM-DD')}&breakdowns=${breakdowns.join(',')}&metrics=${metrics.join(',')}`;

	const headers = {
		Authorization: `Bearer ${token}`
	};

	// res : [{ date:'2024-01-01', appKey:'e6006ce1', platform:'Android', instanceId:'6433415', adUnits, instanceName, bundleId, appName, providerName, data: [{ impressions: 314, clicks: 226, revenue: 18.66, countryCode:'US' }, ...] }, ...]
	const res = await reportApiService.requestApi({ url: requestApiUrl, headers });

	// [ERROR] 응답 결과에 err 가 있는데, statusCode 204 No Content 가 아닌 경우, 에러 처리
	// 		리포트 데이터가 없는 경우, 204 No Content 응답이 온다. ( Criteo 와 다른 케이스임 )
	if (!_.isNil(res.err) && !_.isEqual(res.status, 204)) {
		throw new BusinessError({ message: `[ironsource.service :: _requestReport] API 요청 실패 ::: statusCode(${res.status}), url(${res.url})` }, { err: res });
	}

	const rawDataList = _.isEqual(res.status, 204) ? [] : res;

	logger.debug(NON_RK_LOGGER, `[ironsource.service :: _requestReport] 리포트 데이터 생성 요청 완료`);

	return rawDataList;
};


/**
 * SUBTASK 3-2. _getDataList : 리포트 데이터 가공하기
 *
 * @param {Object} { fields: { ymd, place_keys, country, os, imp, clk, net_revenue }, rawDataList }
 * @return {Array} dataList
 */
const _getDataList = async ({ fields: { ymd, place_keys, country, os, imp, clk, net_revenue }, rawDataList }) => {
	logger.debug(NON_RK_LOGGER, `[ironsource.service :: _getDataList] 리포트 데이터 가공하기`);

	const dataList = new Array();

	rawDataList.map(rawData => {
		// [ERROR] rawData.data 가 없는 경우, 에러 처리
		if (_.isEmpty(rawData.data)) {
			throw new BusinessError({ message: `[ironsource.service :: _getDataList] 리포트 데이터 가공 실패 - data 가 없음` });
		}

		const commonData = {};

		commonData[ymd] = rawData[ymd];
		commonData[os] = rawData[os];

		// place_keys: { appKey: 'appKey', instanceId: 'instanceId', },
		// _.values({ a:1, b:2 }) => [1, 2]
		_.values(place_keys).map(placeKey => {
			commonData[placeKey] = rawData[placeKey];
		});

		rawData.data.map(metric => {
			const data = Object.assign({}, commonData);

			data[country] = metric[country];
			data[imp] = metric[imp];
			data[net_revenue] = metric[net_revenue];

			// 리포트 파일(csv) 생성 시, clk 은 optional 이라 0인 경우 공백 처리되므로, 스트링으로 변환해준다.
			data[clk] = metric[clk] + '';

			dataList.push(data);
		});
	});

	return dataList;
};
