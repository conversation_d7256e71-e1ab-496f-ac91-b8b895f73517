'use strict';

import _ from 'lodash';
import moment from 'moment';

import { BusinessError } from '../../common/error';

import * as logger from '../../utils/logger.util';

import * as reportApiService from '../reportapi/report-api.service';

const NON_RK_LOGGER = 'report_api_non_rk';


/**
 * [PROCESS 3:: TASK 2] processReportApi : DISPLAYIO 리포트 API 연동 및 리포트 파일 다운로드
 *  SUBTASK 1. initCsvReport : csv 파일 초기화
 *  SUBTASK 2. 리포트 데이터 생성 요청 및 다운로드
 * 		2-1. _requestReport : 리포트 데이터 생성 요청하기
 * 		2-2. _getDataList : 리포트 데이터 가공하기
 * 		2-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
 * 
 * @RequestMapping(value='/batch/day/outside/displayio')
 * 
 * @param {Object} adProviderMetaInfo 
 * { 
		reportInfos: { API_KEY },
		reportInfoExt: { apiResultPath, fields, dimensions, report_request_api },
		period: { startDate, endDate },
		fda
 * }
 */
module.exports.processReportApi = async ({ reportInfos: { API_KEY }, reportInfoExt: { apiResultPath, fields, dimensions, report_request_api }, period: { startDate, endDate }, fda }) => {
	logger.debug(NON_RK_LOGGER, '[displayio.service :: processReportApi] 호출됨');


	/* SUBTASK 1. initCsvReport : csv 파일 초기화 */
	// 필드 순서 : ymd, inventoryId, placementId, country, os, imp, clk, net_revenue
	await reportApiService.initCsvReport({ fda, fields });


	/*
		SUBTASK 2. 리포트 데이터 생성 요청 및 다운로드
	*/

	// SUBTASK 2-1. _requestReport : 리포트 데이터 생성 요청하기
	const rawDataList = await _requestReport({ report_request_api, dimensions, period: { startDate, endDate }, reportInfos: { API_KEY } });

	// SUBTASK 2-2. _getDataList : 리포트 데이터 가공하기
	const dataList = await _getDataList({ fields, rawDataList });

	// SUBTASK 2-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
	await reportApiService.writeCsvReport({ fda, apiResultPath, fields, dataList });
};


/**
 * SUBTASK 2-1. _requestReport : 리포트 데이터 생성 요청하기
 *
 * @param {Object} { report_request_api, dimensions, period: { startDate, endDate }, reportInfos: { API_KEY } }
 * @return {Array} rawDataList
 */
const _requestReport = async ({ report_request_api, dimensions, period: { startDate, endDate }, reportInfos: { API_KEY } }) => {
	logger.debug(NON_RK_LOGGER, `[displayio.service :: _requestReport] 리포트 데이터 생성 요청하기`);

	// endDate 가 조회 기간에 포함이 됨
	const startDt = moment(startDate).format('YYYY-MM-DD 00:00:00');
	const endDt = moment(endDate).format('YYYY-MM-DD 23:59:59');

	// https://api.brand.display.io/api/developerAPI?method=getReport&key=XXXX&dimensions=day,inventoryId,placementId,platform,country,countryCode&timerangefrom=2023-01-02%2000:00:00&timerangeto=2023-01-02%2023:59:59
	const requestApiUrl = `${report_request_api}?method=getReport&key=${API_KEY}&timerangefrom=${startDt}&timerangeto=${endDt}&dimensions=${dimensions.join(',')}`;

	// error res
	// 	- { status: 'error', errors: [ 'Bad timerangefrom property format, required format 2018-05-05 00:00:00' ] }
	// 	- { status: 'error', errors: [ 'Must provide at least one grouping dimension' ] }
	// res (JSON) : { status: 'success', data: { rows: [ { day: '2022-10-04', inventoryId:8945, placementId:7936, platform: 'ios', countryCode: 'KR', clicks:'0', imps:'0', revenue: '$0.00' } ], sums: { clicks, imps, revenue }, resNumRows: 1 }, warnings: [] }
	const res = await reportApiService.requestApi({ url: requestApiUrl });

	// [ERROR] 응답 결과에 err 가 있는 경우, 에러 처리
	if (!_.isNil(res.err)) {
		throw new BusinessError({ message: `[displayio.service :: _requestReport] API 요청 실패 ::: statusCode(${res.status}), url(${res.url})` }, { err: res });
	}

	// [ERROR] 응답 결과에 error 가 포함된 경우, 에러 처리
	// - res (text) : {"status":"error","errors":["account inactive"]}
	if (!_.isEmpty(res) && _.includes(res, 'error')) {
		throw new BusinessError({ message: `[displayio.service :: _requestReport] 리포트 데이터 생성 요청 실패 ::: ${JSON.stringify(res, null, 2)}` });
	}

	const rawDataList = res.data.rows;

	logger.debug(NON_RK_LOGGER, `[displayio.service :: _requestReport] 리포트 데이터 생성 요청 완료`);

	return rawDataList;
};


/**
 * SUBTASK 2-2. _getDataList : 리포트 데이터 가공하기
 *
 * @param {Object} { fields: { imp, clk, net_revenue }, rawDataList }
 * @return {Array} dataList
 */
const _getDataList = async ({ fields: { imp, clk, net_revenue }, rawDataList }) => {
	logger.debug(NON_RK_LOGGER, `[displayio.service :: _getDataList] 리포트 데이터 가공하기`);

	let dataList = new Array();

	// [ { clicks:'0', revenue: '$0.00' } ]
	rawDataList.map(rawData => {
		// imp, clk, revenue 에 콤마 제거
		// revenue = '$0.00' 을 '0.00' 으로 변경
		rawData[imp] = rawData[imp].toString().replace(/,/g, '');
		rawData[clk] = rawData[clk].toString().replace(/,/g, '');
		rawData[net_revenue] = rawData[net_revenue].toString().replace(/[,\\$]/g, '');

		// imp, clk, net_revenue 가 모두 0인 건들은 걸러낸다.
		if (_.isEqual(rawData[imp], '0') && _.isEqual(rawData[clk], '0') && _.isEqual(rawData[net_revenue], '0.00')) {
			return;
		}

		let data = Object.assign({}, rawData);

		// 리포트 파일(csv) 생성 시, clk 은 optional 이라 0인 경우 공백 처리되므로, 스트링으로 변환해준다.
		data[clk] = data[clk] + '';

		dataList.push(data);
	});

	return dataList;
};
