'use strict';

import fs from 'fs';
import _ from 'lodash';
import path from 'path';
import util from 'util';
import moment from 'moment';
import csv from 'csv-parser';
import jwt from 'jsonwebtoken';

import { DataEnvironments } from '../../models/data/data-environments.schema';

import { BusinessError } from '../../common/error';

import * as logger from '../../utils/logger.util';

import * as reportApiService from '../reportapi/report-api.service';

const NON_RK_LOGGER = 'report_api_non_rk';


/**
 * [PROCESS 3:: TASK 2] processReportApi : APP NEXUS 리포트 API 연동 및 리포트 파일 다운로드
 *  SUBTASK 1. initCsvReport : csv 파일 초기화
 *  SUBTASK 2. _getToken : 인증 토큰 가져오기
 *  SUBTASK 3. 리포트 데이터 생성 요청 및 다운로드
 * 		3-1. _requestReport : 리포트 데이터 생성 요청하기
 * 		3-2. _requestReportStatus : 리포트 데이터 생성 확인 요청하기
 * 		3-3. _requestReportDownload : 리포트 파일 다운로드하기
 * 		3-4. _writeCsvReport : 리포트 데이터 csv 파일 쓰기
 *
 * @RequestMapping(value='/batch/day/outside/app_nexus')
 *
 * @param {Object} adProviderMetaInfo
 * { 
		reportInfos: { USERNAME, PRIVATE_KEY, PUBLIC_KEY_NAME },
		reportInfoExt: { apiResultPath, separator, columns, fields, token_env_name, report_type, check_valid_token_api, report_jwt_auth_api, report_request_api, report_download_api },
		period: { startDate, endDate },
		fda
 * }
 */
module.exports.processReportApi = async ({ reportInfos: { USERNAME, PRIVATE_KEY, PUBLIC_KEY_NAME }, reportInfoExt: { apiResultPath, separator, columns, fields, token_env_name, report_type, check_valid_token_api, report_jwt_auth_api, report_request_api, report_download_api }, period: { startDate, endDate }, fda }) => {
	logger.debug(NON_RK_LOGGER, '[app-nexus.service :: processReportApi] 호출됨');


	/* SUBTASK 1. initCsvReport : csv 파일 초기화 */
	// 필드 순서 : ymd, placement_id, dealId, country, size, imp, clk, net_revenue
	await reportApiService.initCsvReport({ fda, fields });


	/* SUBTASK 2. _getToken : 인증 토큰 가져오기 */
	const token = await _getToken({ reportInfos: { USERNAME, PRIVATE_KEY, PUBLIC_KEY_NAME }, token_env_name, check_valid_token_api, report_jwt_auth_api });


	/* 
		SUBTASK 3. 리포트 데이터 생성 요청 및 다운로드
	*/

	// SUBTASK 3-1. _requestReport : 리포트 데이터 생성 요청하기
	const report_id = await _requestReport({ token, report_request_api, report_type, columns, startDate, endDate });

	// SUBTASK 3-2. _requestReportStatus : 리포트 데이터 생성 확인 요청하기
	await _requestReportStatus({ report_id, token, report_request_api });

	// SUBTASK 3-3. _requestReportDownload : 리포트 파일 다운로드하기
	const originFilePath = await _requestReportDownload({ report_id, token, report_download_api, apiResultPath });

	// SUBTASK 3-4. _writeCsvReport : 리포트 데이터 csv 파일 쓰기
	await _writeCsvReport({ fda, originFilePath, apiResultPath, separator, fields });
};


/**
 * SUBTASK 2. _getToken : 인증 토큰 가져오기
 * 		1. DB에 유효한 토큰 정보(expired 안 된 토큰)가 있으면 가져온다. (Environments 콜렉션의 report-api-app-nexus-token)
 * 		2. 유효한 토큰이 없으면, 재발급한다.
 * 		3. 재발급한 토큰 정보를 DB에 저장한다.
 *
 * @param {Object} 인증 조건 { reportInfos: { USERNAME, PRIVATE_KEY, PUBLIC_KEY_NAME }, token_env_name, check_valid_token_api, report_jwt_auth_api }
 * @return {String} token
 */
const _getToken = async ({ reportInfos: { USERNAME, PRIVATE_KEY, PUBLIC_KEY_NAME }, token_env_name, check_valid_token_api, report_jwt_auth_api }) => {
	logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _getToken] 인증 토큰 요청`);

	/* 1. Environments 에 저장된 토큰 정보 가져오기 */
	let token = await _getTokenEnvInfo({ reportInfos: { USERNAME, PUBLIC_KEY_NAME }, token_env_name });

	// DB에 토큰 정보가 있는 경우, 유효한 토큰인지 한 번 더 체크하고 반환
	if (!_.isNil(token)) {
		// res : { status }
		const isValidToken = await _requestApi({ url: check_valid_token_api, headers: { Authorization: token } });

		// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
		if (!_.isNil(isValidToken.err)) {
			throw new BusinessError({ message: `[app-nexus.service :: _getToken] 유효 토큰 확인 요청 실패` }, { err: isValidToken, detail: JSON.stringify(isValidToken, null, 0) });
		}

		if (!_.isNil(isValidToken) && !_.isNil(isValidToken.status) && _.isEqual(_.upperCase(isValidToken.status), 'OK')) {
			logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _getToken] DB 인증 토큰 ( token= ${token} )`);

			return token;
		}
	}


	/* 2. 토큰 정보 발급하기 */
	const epoch_time = moment().unix(); // 에포크 시간
	const jsonWebToken = jwt.sign({ sub: USERNAME, iat: epoch_time }, PRIVATE_KEY, { algorithm: 'RS256', header: { kid: PUBLIC_KEY_NAME, alg: 'RS256' } });

	// res : { status:'OK', token:'authn:243495:a8aac99294793:lax1' }
	const res = await _requestApi({ url: report_jwt_auth_api, method: 'POST', body: jsonWebToken, headers: { 'Content-Type': 'text/plain' } });

	// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
	if (!_.isNil(res.err)) {
		throw new BusinessError({ message: `[app-nexus.service :: _getToken] 토큰 발급 요청 실패` }, { err: res, detail: JSON.stringify(res, null, 0) });
	}

	// [ERROR] 응답 결과에 token이 없는 경우, 에러 처리
	if (_.isNil(res.token) || _.isEmpty(res.token)) {
		throw new BusinessError({ message: `[app-nexus.service :: _getToken] token 없음 ::: ${JSON.stringify(res, null, 2)}` }, { err: res, detail: JSON.stringify(res, null, 2) });
	}

	token = res.token;


	/* 3. 토큰 정보 DB에 저장하기 */
	await _upsertTokenEnvInfo({ reportInfos: { USERNAME, PUBLIC_KEY_NAME }, token_env_name, token });


	logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _getToken] 인증 토큰 요청 완료 ( token= ${token} )`);

	return token;
};


/**
 * SUBTASK 2-1. _getTokenEnvInfo : Environments report-api-app-nexus-token에 저장된 토큰 정보 가져오기
 * 		- 토큰 환경 정보가 없는 경우, 새로 추가하고 null 반환
 * 		- 토큰 환경 정보가 있는 경우, 맞는 토큰 정보를 찾아서 반환
 *
 * @param {Object} { reportInfos: { USERNAME, PUBLIC_KEY_NAME }, token_env_name }
 * @return {String} token
 */
const _getTokenEnvInfo = async ({ reportInfos: { USERNAME, PUBLIC_KEY_NAME }, token_env_name }) => {
	logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _getTokenEnvInfo] Environments report-api-app-nexus-token 정보 가져오기 ( USERNAME= ${USERNAME}, PUBLIC_KEY_NAME= ${PUBLIC_KEY_NAME} )`);

	// [ERROR] token_env_name가 없으면, 에러
	if (_.isNil(token_env_name) || _.isEmpty(token_env_name)) {
		throw new BusinessError({ message: `[app-nexus.service :: _getTokenEnvInfo] token_env_name 정보 없음` });
	}

	const now = moment();

	// 토큰 환경 정보 : { name: 'report-api-app-nexus-token', value: [{ reportInfos: { USERNAME, PUBLIC_KEY_NAME }, token, expiredAt, createdAt }, ... ] }
	const tokenEnv = await DataEnvironments.findOne({ name: token_env_name }).exec();

	// 토큰 환경 정보 없는 경우, 새로 추가하고 null 반환
	if (_.isNil(tokenEnv)) {
		// 환경 정보 추가
		await DataEnvironments.findOneAndUpdate({ name: token_env_name }, {
			$set: {
				value: [], createdAt: now, modifiedAt: now
			}
		}, { new: true, runValidators: true, upsert: true }).exec();

		return null;
	}


	// 토큰 정보
	const tokenInfo = tokenEnv.value.filter(({ reportInfos, expiredAt }) => {
		// expiredAt이 현재 시간 이전인 경우, true
		if (now.isBefore(expiredAt) && _.isEqual(USERNAME, reportInfos.USERNAME) && _.isEqual(PUBLIC_KEY_NAME, reportInfos.PUBLIC_KEY_NAME)) {
			return true;
		}
	}).pop();


	let token = null;

	// 토큰 정보 있는 경우
	if (!_.isNil(tokenInfo)) {
		token = tokenInfo.token || null;
	}

	logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _getTokenEnvInfo] Environments report-api-app-nexus-token 정보 ( token= ${token} )`);

	return token;
};


/**
 * SUBTASK 2-2. _upsertTokenEnvInfo : Environments report-api-app-nexus-token(token_env_name)에 토큰 정보 업데이트하기
 * 		- 토큰 만료 2시간
 *
 * @param {Object} { reportInfos: { USERNAME, PUBLIC_KEY_NAME }, token_env_name, token }
 */
const _upsertTokenEnvInfo = async ({ reportInfos: { USERNAME, PUBLIC_KEY_NAME }, token_env_name, token }) => {
	logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _upsertTokenEnvInfo] Environments report-api-app-nexus-token 정보 업데이트 ( USERNAME= ${USERNAME}, PUBLIC_KEY_NAME= ${PUBLIC_KEY_NAME} )`);

	// [ERROR] token_env_name가 없으면, 에러
	if (_.isNil(token_env_name) || _.isEmpty(token_env_name)) {
		throw new BusinessError({ message: `[app-nexus.service :: _getTokenEnvInfo] token_env_name 정보 없음` });
	}

	let result = null;

	const now = moment();

	// USERNAME, PUBLIC_KEY_NAME 에 해당하는 토큰 정보 가져오기
	const isExist = await DataEnvironments.findOne({ name: token_env_name, 'value.reportInfos.USERNAME': USERNAME, 'value.reportInfos.PUBLIC_KEY_NAME': PUBLIC_KEY_NAME }).exec();

	// 토큰 정보가 있으면 수정, 없으면 추가
	if (!_.isNil(isExist)) {
		// 토큰 정보 수정
		result = await DataEnvironments.findOneAndUpdate({ name: token_env_name }, {
			$set: {
				'value.$[el].token': token, 'value.$[el].expiredAt': moment(now).add(-5, 'minutes').add(2, 'hours').toDate(), 'value.$[el].modifiedAt': now.toDate(), modifiedAt: now
			}
		}, {
			new: true, runValidators: true, arrayFilters: [
				{ 'el.reportInfos.USERNAME': USERNAME, 'el.reportInfos.PUBLIC_KEY_NAME': PUBLIC_KEY_NAME, }
			]
		}).exec();
	} else {
		// 토큰 정보 추가
		result = await DataEnvironments.findOneAndUpdate({ name: token_env_name }, {
			$push: {
				value: {
					reportInfos: { USERNAME, PUBLIC_KEY_NAME }, token, expiredAt: moment(now).add(-5, 'minutes').add(2, 'hours').toDate(), modifiedAt: now.toDate()
				}
			}, $set: { modifiedAt: now },
		}, { new: true, runValidators: true }).exec();
	}

	// [ERROR] 결과가 없는 경우, 에러 처리
	if (_.isNil(result) && _.isEmpty(result)) {
		throw new BusinessError({ message: `[app-nexus.service :: _upsertTokenEnvInfo] Environments DB 저장 오류` }, { err: result, detail: JSON.stringify(result, null, 2) });
	}
};


/**
 * SUBTASK 3-1. _requestReport : 리포트 데이터 생성 요청하기
 *
 * @param {Object} 리포트 조회 조건 { token, report_request_api, report_type, columns, startDate, endDate }
 * @return {String} report_id
 */
const _requestReport = async ({ token, report_request_api, report_type, columns, startDate, endDate }) => {
	logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _requestReport] 리포트 데이터 생성 요청`);

	const report = {
		report_type, columns, start_date: startDate.format('YYYY-MM-DD HH:mm:ss'), end_date: moment(endDate).add(1, 'day').format('YYYY-MM-DD HH:mm:ss'), format: 'csv'
	};

	// res : { status:'OK', report_id:'3328e01fe2faef7a64686e324c31b288' }
	const res = await _requestApi({ url: report_request_api, method: 'POST', body: { report }, headers: { Authorization: token } });

	// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
	if (!_.isNil(res.err)) {
		throw new BusinessError({ message: `[app-nexus.service :: _requestReport] 리포트 데이터 생성 요청 실패` }, { err: res, detail: JSON.stringify(res, null, 0) });
	}

	// [ERROR] 응답 결과에 report_id가 없는 경우, 에러 처리
	if (_.isNil(res.report_id) || _.isEmpty(res.report_id)) {
		throw new BusinessError({ message: `[app-nexus.service :: _requestReport] report_id 없음 ::: ${JSON.stringify(res, null, 2)}` }, { err: res, detail: JSON.stringify(res, null, 2) });
	}

	const report_id = res.report_id;

	logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _requestReport] 리포트 데이터 생성 요청 완료 ( report_id = ${report_id} )`);

	return report_id;
};


/**
 * SUBTASK 3-2. _requestReportStatus : 리포트 데이터 생성 확인 요청하기
 *
 * @param {Object} { report_id, token, report_request_api }
 */
const _requestReportStatus = async ({ report_id, token, report_request_api }) => {
	logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _requestReportStatus] 리포트 데이터 생성 확인 요청 ( report_id = ${report_id} )`);

	const TIMEOUT = moment().add(30, 'minutes');
	let currentTime;

	// res : { status:'OK', report: { id, name, created_on, json_request, url }, execution_status }
	let res = await _requestApi({ url: `${report_request_api}?id=${report_id}`, headers: { Authorization: token } });

	// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
	if (!_.isNil(res.err)) {
		throw new BusinessError({ message: `[app-nexus.service :: _requestReportStatus] 리포트 데이터 생성 확인 요청 실패` }, { err: res, detail: JSON.stringify(res, null, 0) });
	}

	// [ERROR] 응답 결과에 report, execution_status 정보가 없는 경우, 에러 처리
	if (_.isNil(res.report) || _.isEmpty(res.report) || _.isNil(res.execution_status) || _.isEmpty(res.execution_status)) {
		throw new BusinessError({ message: `[app-nexus.service :: _requestReportStatus] report 정보 없음 ::: ${JSON.stringify(res, null, 2)}` }, { err: res, detail: JSON.stringify(res, null, 2) });
	}

	logger.debug(NON_RK_LOGGER, `[app-nexus.service] 리포트 파일 생성 결과 \n - execution_status: ${res.execution_status}\n - report: ${JSON.stringify(res.report, null, 2)}`);

	// 파일 생성 진행중 (loop)
	while (!_.isEqual(_.upperCase(res.execution_status), 'READY')) {
		currentTime = moment();

		// [ERROR] TIMEOUT 에러 처리
		if (TIMEOUT.isSameOrBefore(currentTime)) {
			throw new BusinessError({ message: `[app-nexus.service :: _requestReportStatus] 리포트 파일 생성 실패. TIMEOUT 30분` });
		}

		res = await _requestApi({ url: `${report_request_api}?id=${report_id}`, headers: { Authorization: token } });

		// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
		if (!_.isNil(res.err)) {
			throw new BusinessError({ message: `[app-nexus.service :: _requestReportStatus] 리포트 데이터 생성 확인 요청 실패` }, { err: res, detail: JSON.stringify(res, null, 0) });
		}

		// [ERROR] 응답 결과에 report, execution_status 정보가 없는 경우, 에러 처리
		if (_.isNil(res.report) || _.isEmpty(res.report) || _.isNil(res.execution_status) || _.isEmpty(res.execution_status)) {
			throw new BusinessError({ message: `[app-nexus.service :: _requestReportStatus] report 정보 없음 ::: ${JSON.stringify(res, null, 2)}` }, { err: res, detail: JSON.stringify(res, null, 2) });
		}

		logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _requestReportStatus] 리포트 파일 생성 결과 \n - execution_status: ${res.execution_status}\n - report: ${JSON.stringify(res.report, null, 2)}`);
	}

	logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _requestReportStatus] 리포트 데이터 생성 완료 ( report_id = ${report_id} )`);
};


/**
 * [Promise]
 * SUBTASK 3-3. _requestReportDownload : 리포트 파일 다운로드하기
 *
 * @param {Object} { report_id, token, report_download_api, apiResultPath }
 * @returns {String} originFilePath 원본 리포트 파일 다운로드 경로
 */
const _requestReportDownload = ({ report_id, token, report_download_api, apiResultPath }) => {
	logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _requestReportDownload] 리포트 파일 다운로드 ( report_id = ${report_id} )`);

	return new Promise(async (resolve, reject) => {
		// originFilePath : 원본 리포트 파일 다운로드 경로
		// 	 - local_download/reportapi/app_nexus/2019/07/5bdf9aab77bd856e482a84d4_5bcd6979868338bd69d9df9f_20190429_00_origin.csv
		const originFilePath = path.join(path.dirname(apiResultPath), path.basename(apiResultPath, '.csv') + '_origin' + path.extname(apiResultPath));


		let res = await _requestApi({ url: `${report_download_api}?id=${report_id}`, headers: { Authorization: token }, raw: true });

		// [ERROR] 에러가 있는 경우, 에러 처리
		if (!_.isNil(res.err)) {
			reject({ message: '[app-nexus.service :: _requestReportDownload] 리포트 파일 다운로드 요청 에러', err: JSON.stringify(res, null, 0) });

			return;
		}

		// 리포트 파일 Write Stream 생성
		const fileStream = fs.createWriteStream(originFilePath);

		// 파일 쓰기 종료 이벤트
		fileStream.on('close', () => {
			logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _requestReportDownload] 리포트 파일 다운로드 완료 ::: ${apiResultPath}`);

			resolve(originFilePath);
		});

		// 파일 쓰기 에러 이벤트
		fileStream.on('error', err => {
			// [ERROR] 리포트 파일 쓰기 에러 처리
			reject({ message: '[app-nexus.service :: _requestReportDownload] 리포트 파일 쓰기 에러', err });

			fileStream.end();
		});

		res.body.on('end', () => {
			logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _requestReportDownload] 리포트 파일 다운로드 요청 종료`);
		});

		res.body.on('error', err => {
			fileStream.end();

			// [ERROR] 리포트 파일 다운로드 요청 에러 처리
			reject({ message: '[app-nexus.service :: _requestReportDownload] 리포트 파일 다운로드 에러', err });
		});

		res.body.pipe(fileStream);
	});
};


/**
 * [Promise]
 * SUBTASK 3-4. _writeCsvReport : 리포트 데이터 csv 파일 쓰기
 *
 * @param {Object} { fda, originFilePath, apiResultPath, separator, fields }
 */
const _writeCsvReport = ({ fda, originFilePath, apiResultPath, separator, fields: { ymd, place_keys: { PLACEMENT_ID }, dealId, country, size, imp, clk, net_revenue } }) => {
	logger.debug(NON_RK_LOGGER, `[app_nexus.service :: _writeCsvReport] 리포트 데이터 csv 파일 쓰기 시작 ( apiResultPath= ${apiResultPath} )`);

	return new Promise(async (resolve, reject) => {
		// [ERROR] fda가 없으면, 에러 처리
		if (_.isNil(fda)) {
			reject({ message: `[app_nexus.service :: _writeCsvReport] fda 객체 없음` });
			return;
		}

		// file read Stream 생성
		const fileStream = fs.createReadStream(originFilePath);

		// csv Stream 생성
		const csvStream = csv({ separator });

		// 헤더 순서: day, placement_id, deal_code, geo_country, size, imps_resold, clicks, reseller_revenue
		const rowFormat = '%s,%s,%s,%s,%s,%s,%s,%s\n';
		let row = '';

		const onData = dt => {
			try {
				// imp, clk, net_revenue 가 모두 0인 건들은 걸러낸다.
				if (_.isEqual(dt[imp], '0') && _.isEqual(dt[clk], '0') && _.isEqual(dt[net_revenue], '0.000000')) {
					return;
				}

				// dealId가 '--'인 경우, deal이 없는 것이므로 공백 처리
				const deal = (_.isEqual(dt[dealId], '--')) ? '' : dt[dealId];

				row = util.format(rowFormat, dt[ymd], dt[PLACEMENT_ID], deal, dt[country], dt[size], dt[imp], dt[clk], dt[net_revenue]);
				fs.appendFileSync(fda, row, 'utf8');
			} catch (err) {
				reject({ message: `[app_nexus.service :: _writeCsvReport] onData 에러`, err });

				csvStream.removeListener('data', onData);
				csvStream.end();
				fileStream.close();
			}
		};

		csvStream.on('data', onData);

		csvStream.on('end', async () => {
			logger.debug(NON_RK_LOGGER, `[app_nexus.service :: _writeCsvReport] 리포트 데이터 csv 파일 쓰기 완료`);

			fileStream.close();

			resolve();
		});

		csvStream.on('error', err => {
			csvStream.removeListener('data', onData);
			csvStream.end();
			fileStream.close();

			// [ERROR] csvStream 에러 처리
			reject({ message: `[app_nexus.service :: _writeCsvReport] csvStream 에러`, err });
		});

		fileStream.on('error', err => {
			csvStream.removeListener('data', onData);
			csvStream.end();
			fileStream.close();

			// [ERROR] fileStream 에러 처리
			reject({ message: `[app_nexus.service :: _writeCsvReport] fileStream 에러`, err });
		});

		fileStream.pipe(csvStream);
	});
};


/**
 * _requestApi : request api 요청하기
 *
 * @param {Object} { url, method, headers, body, raw }
 * @return {Object} result
 */
const _requestApi = async ({ url, method = 'GET', headers = {}, body = {}, raw = false }) => {
	// logger.debug(NON_RK_LOGGER, `[app-nexus.service :: _requestApi] ${method} 요청 URL ::: ${url}`);

	let res = await reportApiService.requestApi({ url, method, headers, body, raw });

	if (raw) {
		// 리포트 결과 데이터가 오는 경우(_requestReportDownload 호출 시), Content-type이 text/html 이다.
		// stream 처리를 위해 text 변환 없이, res를 그대로 반환 한다.
		if (!_.isNil(res.headers) && res.headers.get('Content-Type').indexOf('text/html') > -1) {
			return res;
		} else if (!_.isNil(res.headers) && res.headers.get('Content-Type').indexOf('application/json') > -1) {
			res = await res.json();
		}
	}

	// [ERROR] Status가 401 이고 응답 결과에 NOAUTH 가 포함된 경우, 에러 처리
	if (_.isEqual(res.status, 401) && _.includes(_.upperCase(res.err), 'NOAUTH')) {
		return { message: `[app-nexus.service :: _requestApi] 토큰 재발급 필요 ::: statusCode(${res.status}), url(${url})`, err: res };
	}

	// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
	if (!_.isNil(res.err)) {
		return { message: `[app-nexus.service :: _requestApi] API 요청 실패 ::: statusCode(${res.status}), url(${url})`, err: res };
	}


	let result = res.response;

	// [ERROR] status 가 OK 가 아닌 경우, 에러 처리
	if (!_.isEqual(result.status, 'OK')) {
		return { message: `[app-nexus.service :: _requestApi] status not OK ::: status(${result.status}), url(${url})`, err: res };
	}

	return result;
};
