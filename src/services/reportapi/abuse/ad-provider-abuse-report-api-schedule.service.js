'use strict';

import _ from 'lodash';
import path from 'path';
import moment from 'moment';
import COMMON_CODE from '@ssp/ssp-common-code';

import config from '../../../config/config';

import * as mailer from '../../../utils/mail.util';
import * as logger from '../../../utils/logger.util';

import { BusinessError } from '../../../common/error';

// CMS DB
import { AdProvider } from '../../../models/ad-providers.schema';
import { Environments } from '../../../models/environments.schema';

// Data DB
import { ReportApiResult } from '../../../models/data/ad-provider-report-api-results.schema';
import { AdProviderAbuseReportApiSchedule } from '../../../models/data/ad-provider-abuse-report-api-schedule.schema';

const AD_PROVIDER_TYPE = COMMON_CODE.codeEncAvailable()['AdProviderType'];

const REPORT_API_LOGGER = 'report_api';
const ERROR_LOGGER = 'report_api_error';

const CHUNK_SIZE = config.report_api_chunk_size;

const STATE = {
	IN_PROGRESS: 'IN_PROGRESS',
	COMPLETE: 'COMPLETE',
	FAILURE: 'FAILURE',
};


/**
 * makeAdProviderAbuseReportApiSchedules : AdProviderAbuseReportApiSchedules 생성
 * 	- 어뷰즈 리포트 연동은 IN_NAVER 만 해당됨
 * 
 *  PROCESS 0. 어뷰즈 리포트 연동 대상 AdProvider 정보 가져오기
 *  PROCESS 1. 어뷰즈 리포트 연동 스케쥴 정보 만들기
 *  PROCESS 2. AdProviderAbuseReportApiSchedules 에 스케쥴 추가
 *  PROCESS 3. 문제가 있는 AdProvider 가 있는 경우, 해당 AdProvider 의 어뷰즈 리포트 연동 스케쥴 생성 실패 알림 메일
 * 
 * @param {Object} { ymd, reportApiType }
 * @param {Array} abuseReportApiConfig
 * @return {Array} failedAdProviders
*/
module.exports.makeAdProviderAbuseReportApiSchedules = async ({ ymd, reportApiType }, abuseReportApiConfig) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api-schedule.service :: makeAdProviderAbuseReportApiSchedules] 호출됨 ( ymd= ${ymd}, reportApiType= ${reportApiType} )`);


	// [PROCESS 0] 어뷰즈 리포트 연동 대상 AdProvider 정보 가져오기
	const adProviders = await _getAdProviders(reportApiType);


	// [PROCESS 1] 어뷰즈 리포트 연동 스케쥴 정보 만들기
	const { abuseReportApiSchedules, failedAdProviders } = await _makeAbuseReportApiSchedules(ymd, adProviders, abuseReportApiConfig);


	// [PROCESS 2] AdProviderAbuseReportApiSchedules 에 스케쥴 추가
	await _upsertAbuseReportApiSchedules(abuseReportApiSchedules);

	return failedAdProviders;
};


/**
 * [PROCESS 0] _getAdProviders : 어뷰즈 리포트 연동 대상 AdProvider 정보 가져오기
 *
 * @param {String} reportApiType
 * @return {Array} adProviders
 * [{ // rk
		reportApiType:'GFA',
		adProviderType:'IN_NAVER',
		rkUse:1,
		timezone: 'Asia/Seoul',
	}]
*/
const _getAdProviders = async (reportApiType='') => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api-schedule.service :: _getAdProviders] 호출됨');

	// adProviderType=IN_NAVER / rkUse=1
	const adProviders = await AdProvider.aggregate()
		.match({
			status: 'ON',
			adProviderType: AD_PROVIDER_TYPE.IN_NAVER.code,
			'reportApi.abuse': 1,
			'reportApi.rkUse': 1,
			'reportApi.type': (_.isEmpty(reportApiType)) ? { $regex: `.*${reportApiType}.*` } : { $eq: reportApiType },
		})
		.project({
			_id: 0,
			reportApiType: '$reportApi.type',
			rkUse: '$reportApi.rkUse',
			adProviderType: 1,
			timezone: 1
		})
		.group({
			_id: null,
			uniqueValues: { $addToSet: {
				reportApiType: '$reportApiType',
				rkUse: '$rkUse',
				adProviderType: '$adProviderType',
				timezone:'$timezone'
			} }
		})
		.unwind('uniqueValues')
		.project({
			_id: 0,
			reportApiType: '$uniqueValues.reportApiType',
			rkUse: '$uniqueValues.rkUse',
			adProviderType: '$uniqueValues.adProviderType',
			timezone: '$uniqueValues.timezone',
		})
		.exec();

	return adProviders;
};


/**
 * [PROCESS 1] _makeAbuseReportApiSchedules : 어뷰즈 리포트 연동 스케쥴 정보 만들기
 * 
 * @param {String} ymd
 * @param {Array} adProviders :: 어뷰즈 리포트 연동 대상인 IN_NAVER AdProviders 정보
 *	[{ // rk
		reportApiType:'GFA',
		adProviderType:'IN_NAVER',
		timezone: 'Asia/Seoul',
		rkUse:1,
 * 	}]
 * @param {Array} abuseReportApiConfig :: AP 어뷰즈 리포트 연동 설정 정보
 *	[{
		reportApiType : 'GFA',
		apiResult : 'HDFS',
		apiInfo : { period: { start, end, unit } },
		fileInfo : { filePath, fileName, separator },
		scheduleInfo : { period : 'MONTH', day : 2, hour : 0 },
		fields : { ymd, rk, imp, clk, netRevenue, revenue }
 * 	}]
 * @return {Array} abuseReportApiSchedules
 * [{ // rk
 		ymd: '20230702',
		reportApiType: 'GFA',
		adProviderType: 'IN_NAVER',
		timezone: 'Asia/Seoul',
		state: 'READY',
		targetDate: '202306',
		rkUse: 1,
		apiResultPath: '/user/naver-pa-dmp/gfp_report/revenue_refund/2023/06/gfa_202306.tsv',
		retryCount: 0,
		scheduledAt: moment('2023-07-02T00:00:00.000'),
		begunAt, endedAt,
		createdAt, modifiedAt
 * }]
*/
const _makeAbuseReportApiSchedules = async (ymd, adProviders, abuseReportApiConfig) => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api-schedule.service :: _makeAbuseReportApiSchedules] 호출됨');

	// 어뷰즈 리포트 연동 스케쥴 정보
	const abuseReportApiSchedules = new Array();


	// [PROCESS 1:: TASK 1] 스케쥴 생성 실패한 AdProvider 리스트 추출하기
	let failedAdProviders = new Array();

	// IN_NAVER 의 경우, 스케쥴이 하나만 나와야 한다. 그렇지 않은 경우, 이슈가 있는 것이다.
	// 	- GFA / NCC / NDP / GFD
	abuseReportApiConfig.map(({ reportApiType }) => {
		const failed = adProviders.filter(adProvider => {
			return _.isEqual(adProvider.reportApiType, reportApiType);
		});

		if(failed.length > 1) {
			failedAdProviders = _.concat(failedAdProviders, failed);

			_.remove(adProviders, adProvider => {
				return _.isEqual(adProvider.reportApiType, reportApiType);
			});
		}
	});


	// [PROCESS 1:: TASK 2] 정상적인 AdProvider 에 대해 스케쥴 생성하기
	for(const { reportApiType, adProviderType, timezone, rkUse } of adProviders) {
		// [PROCESS 1:: TASK 2-1] reportApiType 별 abuseReportApiConfig 정보 조회
		const reportApiConfig = _.filter(abuseReportApiConfig, conf =>
			_.isEqual(conf.reportApiType, reportApiType)
		).pop() || {};

		// abuseReportApiConfig 정보가 없는 경우, pass
		if(_.isEmpty(reportApiConfig)) {
			continue;
		}


		// [PROCESS 1:: TASK 2-2] scheduledAt, startDate, endDate, apiResultPath 정보 셋팅
		const { filePath, fileName } = reportApiConfig.fileInfo;
		const { start, end, unit } = reportApiConfig.apiInfo.period;
		const { period, day, hour } = reportApiConfig.scheduleInfo;

		// 스케쥴 시간 정보 설정
		let scheduledAt;
		if (_.isEqual(period, 'MONTH')) {
			scheduledAt = moment(ymd, 'YYYYMMDD').startOf('months').add(day-1, 'days').add(hour, 'hours');

			// scheduledAt 의 날짜가 ymd 와 다른 경우, pass ( 배치 주기가 아님 )
			if(!_.isEqual(ymd, scheduledAt.format('YYYYMMDD'))) {
				continue;
			}
		} else {
			scheduledAt = moment(ymd, 'YYYYMMDD').startOf('days').add(hour, 'hours');
		}


		// 연동 리포트 기간 정보 설정 ( 포맷 : YYYYMMDD )
		let targetDate;
		// let startDate, endDate;

		if (_.isEqual(unit, 'months')) {
			targetDate = moment(ymd, 'YYYYMMDD').add(end, unit).format('YYYYMM');
			// startDate = moment(ymd, 'YYYYMMDD').add(start, unit).startOf('month').format('YYYYMMDD');
			// endDate = moment(ymd, 'YYYYMMDD').add(end, unit).endOf('month').format('YYYYMMDD');
		} else {
			targetDate = moment(ymd, 'YYYYMMDD').add(end, unit).format('YYYYMMDD');
			// startDate = moment(ymd, 'YYYYMMDD').add(start, unit).format('YYYYMMDD');
			// endDate = moment(ymd, 'YYYYMMDD').add(end, unit).format('YYYYMMDD');
		}

		// 연동 리포트 원본 경로 설정
		// 	- GFA SAMPLE = '/user/naver-pa-dmp/gfp_report/revenue_refund/{{YYYY}}/{{MM}}/gfa_{{YYYY}}{{MM}}.tsv'
		let apiResultPath;
		if(_.isEqual(reportApiConfig.apiResult, config.api_result_type.hdfs)) {
			const dt = moment(targetDate);
			apiResultPath = path.join(filePath, fileName).replace(/{{YYYY}}/g, dt.format('YYYY')).replace(/{{MM}}/g, dt.format('MM')).replace(/{{DD}}/g, dt.format('DD')).replace(/\s/g,'');
		}


		// [PROCESS 1:: TASK 2-3] abuseReportApiSchedules 정보 추가하기
		abuseReportApiSchedules.push({
			ymd,
			reportApiType,
			adProviderType,
			timezone,
			rkUse,
			retryCount: -1,
			state: 'READY',
			targetDate,
			// startDate,
			// endDate,
			apiResultPath,
			scheduledAt
		});
	}

	// [ERROR] abuseReportApiSchedules 가 없는 경우, 에러 처리
	if(_.isEmpty(abuseReportApiSchedules)) {
		logger.error(ERROR_LOGGER, '[ad-provider-abuse-report-api-schedule.service :: _makeAbuseReportApiSchedules] abuseReportApiSchedules No Result');
		// throw new BusinessError({ message: `[ad-provider-abuse-report-api-schedule.service :: _makeAbuseReportApiSchedules] abuseReportApiSchedules No Result` });
	}

	return { abuseReportApiSchedules, failedAdProviders };
};


/**
 * [PROCESS 2] _upsertAbuseReportApiSchedules : AdProviderAbuseReportApiSchedules 에 스케쥴 추가
 *
 * @param {Array} abuseReportApiSchedules [{ ymd, reportApiType, adProviderType, timezone, rkUse, state, targetDate, startDate, endDate, apiResultPath, retryCount, scheduledAt }]
*/
const _upsertAbuseReportApiSchedules = async (abuseReportApiSchedules) => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api-schedule.service :: _upsertAbuseReportApiSchedules] 호출됨');

	const today = moment();

	// bulkWrite operations
	const operations = abuseReportApiSchedules.map(({ ymd, reportApiType, adProviderType, timezone, rkUse, state, targetDate, startDate, endDate, apiResultPath, retryCount, scheduledAt }) => {
		return {
			updateOne: {
				filter : { ymd, reportApiType, adProviderType, rkUse },
				update : {
					$set: {
						retryCount,
						state,
						targetDate,
						startDate, endDate,
						timezone,
						apiResultPath,
						scheduledAt,
						modifiedAt: today
					},

					// insert 시에만
					$setOnInsert: { 
						createdAt: today
					},
				},
				upsert : true,
			}
		};
	});

	if (!_.isNil(operations) && !_.isEmpty(operations)) {
		const result = await AdProviderAbuseReportApiSchedule.bulkWrite(operations);

		// [ERROR] 응답 오류가 있는 경우, 에러 처리
		if(result.ok !== 1 || !_.isEmpty(result.writeErrors)) {
			throw new BusinessError({ message: `[ad-provider-abuse-report-api-schedule.service :: _upsertAbuseReportApiSchedules] DB 저장 오류` }, { err: result, detail: JSON.stringify(result, null, 2)});
		}
	}
};


/**
 * [PROCESS 3] sendBatchFailureMail : 스케쥴 생성 실패 메일 보내기
 *
 * @param {Object} failedAdProviders [{ reportApiType, timezone, rkUse, adProviderType }]
*/
module.exports.sendBatchFailureMail = async (failedAdProviders) => {
	try {
		logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api-schedule.service :: sendBatchFailureMail] 호출됨');

		let reportApiTypes = new Set();

		failedAdProviders.map(({ reportApiType }) => {
			reportApiTypes.add(reportApiType);
		});

		reportApiTypes = Array.from(reportApiTypes);

		const env_name = config.report_api_result_receiver_env_name;
		const to = await _getReceiverInfo(env_name);

		const subject = `AP 어뷰즈 리포트 연동 스케쥴 생성 실패 알림 메일 (${reportApiTypes.toString()})`;

		const html = `상세 정보<br><br>`
				+ `${JSON.stringify(failedAdProviders)}<br>`;

		mailer.sendMail({ to, subject, html });
	} catch(e) {
		logger.error(ERROR_LOGGER, `[ad-provider-abuse-report-api-schedule.service :: sendBatchFailureMail] Error :: \n ${e.stack}\n\n`, e);
	}
};


const _getReceiverInfo = async env_name => {
	// [ERROR] env_name 가 없으면, 에러
	if(_.isNil(env_name) || _.isEmpty(env_name)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api-schedule.service :: _getReceiverInfo] env_name 정보 없음` });
	}

	// 수신자 환경 정보 : { name: 'report-api-result-receiver', value: { COMMON: ['<EMAIL>'], NDP: ['<EMAIL>;']} }
	const receiverEnv = await Environments.findOne({ name: env_name }).exec();


	// [ERROR] 수신자 환경 정보 없는 경우, 에러
	if(_.isNil(receiverEnv)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api-schedule.service :: _getReceiverInfo] Environments ${env_name} 에 수신자 정보 없음` });
	}

	const commonTo = receiverEnv.value.common || [];
	const to = Array.from(new Set([...commonTo])).join(';');

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api-schedule.service :: _getReceiverInfo] Environments ${env_name} = ${receiverEnv.value}`);

	return to;
};


/**
 * getAdProviderAbuseReportApiSchedules : reportApiType 에 해당 하는 것 중, state 가 READY 인 abuseReportApiSchedules 가져오기
 *
 * @param {Object} { reportApiType, state, ymd }
 * @return {Array} abuseReportApiSchedules [{ _id, ymd, reportApiType, retryCount, targetDate, apiResultPath, scheduledAt, modifiedAt }]
*/
module.exports.getAdProviderAbuseReportApiSchedules = async ({ reportApiType, state='READY', ymd }) => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api-schedule.service :: getAdProviderAbuseReportApiSchedules] 호출됨');

	const params = {
		scheduledAt: { $lte : moment().toDate() },
	};

	if(!_.isEmpty(ymd)) params.ymd = ymd;
	if(!_.isEmpty(state)) params.state = state;
	if(!_.isEmpty(reportApiType)) params.reportApiType = reportApiType;

	const abuseReportApiSchedules = await AdProviderAbuseReportApiSchedule.aggregate()
		.match(params)
		.project({
			_id: 1,
			ymd: 1,
			reportApiType: 1,
			retryCount: 1,
			targetDate: 1,
			apiResultPath: 1,
			scheduledAt: 1,
			modifiedAt: 1,
		})
		.sort({ scheduledAt : 1 })
		.exec();

	return abuseReportApiSchedules;
};


/**
 * updateAbuseReportApiSchedulesState : abuseReportApiSchedules state 변경 ( N건 )
 *
 * @param {Object} { abuseReportApiSchedules, state }
*/
module.exports.updateAbuseReportApiSchedulesState = async ({ abuseReportApiSchedules, state }) => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api-schedule.service :: updateAbuseReportApiSchedulesState] 호출됨');

	const schedule_ids = _.map(abuseReportApiSchedules, '_id');

	const option = {};

	if (!_.isNil(state)) option['state'] = state;

	const result = await AdProviderAbuseReportApiSchedule.updateMany(
		{ _id: { $in: schedule_ids } },
		{ $set: option },
		{ multi: true, runValidators: true }
	).exec();

	// [ERROR] update 결과가 없는 경우, 에러 처리
	if(_.isEmpty(result)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api-schedule.service :: updateAbuseReportApiSchedulesState] AdProviderAbuseReportApiSchedules DB 저장 오류` }, { err: result, detail: JSON.stringify(result, null, 2)});
	}
};


/**
 * updateAbuseReportApiScheduleState : abuseReportApiSchedule 상태 업데이트 하기 ( 1건 )
 * 	- isComplete 가 없으면 시작 처리, 있으면 종료 처리
 *
 * @param {Object} abuseReportApiSchedule { _id, retryCount }
 * @param {Boolean} isComplete 배치 처리 결과
*/
module.exports.updateAbuseReportApiScheduleState = async ({ _id, retryCount }, isComplete=null) => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api-schedule.service :: updateAbuseReportApiScheduleState] 호출됨');

	const today = moment();

	const option = {
		modifiedAt : today
	};

	// isComplete 가 없으면 시작 처리, 있으면 종료 처리
	if (_.isNil(isComplete)) {
		// WAIT -> IN_PROGRESS 로 변경
		option.state = STATE.IN_PROGRESS;
		option.begunAt = today;
	} else {
		// IN_PROGRESS -> COMPLETE / FAILURE 로 변경
		option.state = (isComplete) ? STATE.COMPLETE : STATE.FAILURE;
		option.retryCount = ++retryCount;
		option.endedAt = today;
	}

	const result = await AdProviderAbuseReportApiSchedule.findOneAndUpdate(
		{ _id },
		{ $set: option },
		{ new: true, runValidators: true }
	).exec();


	// [ERROR] update 결과가 없는 경우, 에러 처리
	if (_.isEmpty(result)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api-schedule.service :: updateAbuseReportApiScheduleState] AdProviderAbuseReportApiSchedules DB 저장 오류` }, { err: result, detail: JSON.stringify(result, null, 2)});
	}
};


/**
 * updateAbuseReportApiSchedulePeriod : abuseReportApiSchedule 기간 정보 업데이트 하기
 *
 * @param {Object} adProviderMetaInfo { schedule_id, startDate, endDate }
*/
module.exports.updateAbuseReportApiSchedulePeriod = async ({ schedule_id, startDate, endDate }) => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api-schedule.service :: updateAbuseReportApiSchedulePeriod] 호출됨');

	const result = await AdProviderAbuseReportApiSchedule.findOneAndUpdate(
		{ _id: schedule_id },
		{ $set: {
			startDate: startDate.format('YYYYMMDD'),
			endDate: endDate.format('YYYYMMDD')
		} },
		{ new: true, runValidators: true }
	).exec();


	// [ERROR] update 결과가 없는 경우, 에러 처리
	if (_.isEmpty(result)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api-schedule.service :: updateAbuseReportApiSchedulePeriod] AdProviderAbuseReportApiSchedules DB 저장 오류` }, { err: result, detail: JSON.stringify(result, null, 2)});
	}
};


/**
 * upsertReportApiResults : 리포트 파일 정보를 CHUNK_SIZE 단위로 끊어서 AdProviderReportApiResults DB 에 로깅
 *
 * @param {Object} metaInfo
 * {
		schedule_id,
		ymd: '20230722',
		reportApiType: 'GFA,
		apiResultPath: '/user/naver-pa-dmp/gfp_report/revenue_refund/2023/06/gfa_202306.tsv',
		totalLineCount: 100000,
		type: 'HDFS',
		abuse: 1
 * }
 * @return {Object} { reportApiResult_id, chunkInfos: [{ chunkNumber, startLine, endLine, state, successCount }] }
*/
module.exports.upsertReportApiResults = async ({ schedule_id, ymd, reportApiType, apiResultPath, totalLineCount, type= 'HDFS', abuse= 1 }) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api-schedule.service :: upsertReportApiResults] 리포트 파일 정보를 ${CHUNK_SIZE} 단위로 끊어서 DB에 로깅`);

	const today = moment();

	// 반환할 chunkInfo 리스트
	const chunkInfos = new Array();

	// 파일 쪼개기
	const chunkCount = _.floor(totalLineCount / CHUNK_SIZE); // 총 청크 수
	const chunkMod = totalLineCount % CHUNK_SIZE; // 마지막 청크 사이즈(나머지들)

	// chunk 정보 생성하기
	for(let i = 0; i <= chunkCount; i++) {
		if(i === chunkCount && chunkMod === 0) break;

		chunkInfos.push({
			chunkNumber: i + 1,
			startLine: (i * CHUNK_SIZE) + 1,
			endLine: (i !== chunkCount) ? (i * CHUNK_SIZE) + CHUNK_SIZE : (i * CHUNK_SIZE) + chunkMod,
			state: 'READY',
			successCount: 0,
			filteredCount: {
				notExistRk: 0,
				invalidEncodedRk: 0,
				invalidEncodedRsKeyValue: 0,
				notExistRequired: 0,
				notExistMeta: 0
			},
		});
	}

	const result = await ReportApiResult.findOneAndUpdate(
		{ schedule_id, ymd, reportApiType, apiResultPath, type, abuse },
		{
			$set: {
				totalLineCount,
				totalSuccessCount: 0,
				totalFilteredCount: {
					notExistRk: 0,
					invalidEncodedRk: 0,
					invalidEncodedRsKeyValue: 0,
					notExistRequired: 0,
					notExistMeta: 0
				},
				chunkInfos,
				state: _.isEmpty(chunkInfos) ? 'NO_DATA' : 'READY',
				begunAt: today,
				modifiedAt: today
			},

			// insert 시
			$setOnInsert: { createdAt: today },
		},
		{ new: true, runValidators: true, upsert: true }
	).exec();

	return { reportApiResult_id: result._id, chunkInfos };
};


/**
 * updateReportApiResults : 처리 완료 상태 AdProviderReportApiResults DB 업뎃
 * 	- 부분/전체 처리 종료
 *
 * @param {Object} chunkInfo { reportApiResult_id, chunkNumber, successCount, filteredCount }
*/
module.exports.updateReportApiResults = async ({ reportApiResult_id, chunkNumber, successCount = 0, filteredCount, isSuccess = true }) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api-schedule.service :: updateReportApiResults] ChunkNumber #${chunkNumber} 처리 완료 상태 DB 업뎃`);

	const {
		notExistRk= 0, invalidEncodedRk= 0, invalidEncodedRsKeyValue= 0, notExistRequired= 0, notExistMeta= 0
	} = filteredCount || {};

	const result = await ReportApiResult.findOneAndUpdate(
		{ _id: reportApiResult_id, 'chunkInfos.chunkNumber': chunkNumber },
		{
			$set: {
				'chunkInfos.$.filteredCount' : filteredCount,
				'chunkInfos.$.successCount' : successCount,
				'chunkInfos.$.state' : (isSuccess) ? STATE.COMPLETE : STATE.FAILURE,
			},
			$inc: {
				'totalFilteredCount.notExistRk' : notExistRk,
				'totalFilteredCount.invalidEncodedRk' : invalidEncodedRk,
				'totalFilteredCount.invalidEncodedRsKeyValue' : invalidEncodedRsKeyValue,
				'totalFilteredCount.notExistRequired' : notExistRequired,
				'totalFilteredCount.notExistMeta' : notExistMeta,
				'totalSuccessCount' : successCount,
			}
		},
		{ new: true, runValidators: true }
	).exec();

	// [ERROR] update 결과가 없는 경우, 에러 처리
	if(_.isNil(result) || _.isEmpty(result) || _.isNil(result.chunkInfos)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api-schedule.service :: updateReportApiResults] ChunkNumber #${chunkNumber} BatchReportProcessingResult DB 저장 오류` }, { err: result, detail: JSON.stringify(result, null, 2)});
	}

	const failedCnt = result.chunkInfos.filter(({ state }) => _.isEqual(state, STATE.FAILURE)).length;
	const completeCnt = result.chunkInfos.filter(({ state }) => _.isEqual(state, STATE.COMPLETE)).length;

	// 모든 CHUNK 상태가 COMPLETE 인 경우, 전체 상태도 COMPLETE 으로 변경한다.
	// CHUNK 상태가 하나라도 FAILURE 가 있는 경우, 전체 상태도 FAILURE 로 변경한다.
	if (result.chunkInfos.length === completeCnt) {
		await ReportApiResult.findOneAndUpdate(
			{ _id: reportApiResult_id },
			{ $set: { state: STATE.COMPLETE, endedAt: moment() } },
			{ new: true, runValidators: true }
		).exec();
	} else if (failedCnt > 0) {
		await ReportApiResult.findOneAndUpdate(
			{ _id: reportApiResult_id },
			{ $set: { state: STATE.FAILURE, endedAt: moment() } },
			{ new: true, runValidators: true }
		).exec();
	}
};


/**
 * getReportApiResults : 배치 처리 결과 가져오기
 *
 * @param {Object} abuseReportApiSchedule { ymd, reportApiType, apiResultPath }
 * @return {Object} result
*/
module.exports.getReportApiResults = async ({ ymd, reportApiType, apiResultPath }) => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api-schedule.service :: getReportApiResults] 호출됨');

	let result = await ReportApiResult.aggregate()
		.match({ ymd, reportApiType, apiResultPath, type:'HDFS' })
		.project({
			_id: 0,
			ymd: 1,
			reportApiType: 1,
			apiResultPath: 1,
			totalLineCount: 1,
			totalSuccessCount: 1,
			totalFilteredCount: 1,
			state: 1,
		})
		.exec();


	// result 없는 경우, 종료
	if(_.isNil(result) || _.isEmpty(result)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api-schedule.service :: getReportApiResults] No Result` });
	}

	result = result.pop();

	return result;
};
