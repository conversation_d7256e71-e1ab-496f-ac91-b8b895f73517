'use strict';

import fs from 'fs';
import _ from 'lodash';
import path from 'path';
import moment from 'moment';
import csv from 'csv-parser';
import COMMON_CODE from '@ssp/ssp-common-code';

import config from '../../../config/config';

import * as file from '../../../utils/file.util';
import * as mailer from '../../../utils/mail.util';
import * as logger from '../../../utils/logger.util';

import { BusinessError } from '../../../common/error';

// CMS DB
import { AdProvider } from '../../../models/ad-providers.schema';
import { AdProviderInfo } from '../../../models/ad-provider-infos.schema';
import { Environments } from '../../../models/environments.schema';

// Data DB
import { GfpFeeRate } from '../../../models/data/gfp-fee-rate.schema';
import { DataEnvironments } from '../../../models/data/data-environments.schema';

import * as c3HdfsApi from '../../../c3/hdfs-api';
import * as c3HdfsCli from '../../../c3/hdfs-cli';

import * as exchangeRateService from '../exchange-rate.service';

import * as abuseHdfsService from './ad-provider-abuse-report-api-hdfs.service';
import * as abuseScheduleService from './ad-provider-abuse-report-api-schedule.service';

const CURRENCY = COMMON_CODE.codeEncAvailable()['Currency'];
const AD_PROVIDER_TYPE = COMMON_CODE.codeEncAvailable()['AdProviderType'];

const REPORT_API_LOGGER = 'report_api';
const ERROR_LOGGER = 'report_api_error';

const LOCAL_ROOT = config.local_root;
const ABUSE_REPORT_API_PATH = config.abuse_report_api_path;
const ADPROVIDER_ABUSE_HDFS_COMMON_PATH = config.adprovider_abuse.hdfs.common;
const ADPROVIDER_ABUSE_HDFS_ROOT_DIR = config.adprovider_abuse.hdfs.root_dir;


/**
 * getAbuseReportApiConfig : 어뷰즈 리포트 연동 설정 정보 가져오기 ( DataEnvironments abuse-report-api-config )
 *
 * @param {String} reportApiType
 * @return {Object} abuseReportApiConfig
 	[{
		reportApiType: 'GFA',
		apiResult: 'HDFS',
		apiInfo: { period: { start, end, unit }, checkSuccessFile },
		fileInfo: { filePath, fileName, separator },
		scheduleInfo: { period: 'MONTH', day: 2, hour: 0 },
		fields: { ymd, rk, imp, clk, netRevenue, revenue }
	}]
*/
module.exports.getAbuseReportApiConfig = async (reportApiType= '') => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api.service :: getAbuseReportApiConfig] 호출됨');

	const env = await DataEnvironments.findOne({ name: 'abuse-report-api-config' }, { 'value': 1 }).exec();

	if (_.isEmpty(env) || _.isEmpty(env.value)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api.service :: getAbuseReportApiConfig] DataEnvironments abuse-report-api-config 가 존재하지 않음` });
	}

	let abuseReportApiConfig = env.value;
	if (!_.isEmpty(reportApiType)) {
		abuseReportApiConfig = _.filter(abuseReportApiConfig, conf => _.isEqual(conf.reportApiType, reportApiType));
	}

	if (_.isEmpty(abuseReportApiConfig)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api.service :: getAbuseReportApiConfig] DataEnvironments abuse-report-api-config 에 ${reportApiType} 설정 정보가 존재하지 않음` });
	}

	return abuseReportApiConfig;
};


/******************************************************************************************/
/******************************************************************************************/
/******************************************************************************************/


/**
 * [PROCESS 4:: TASK 0] getBaseMetaInfo : 어뷰즈 리포트 연동에 필요한 기본 메타 정보 가져오기
 *
 * @param {Object} abuseReportApiSchedule { _id, ymd, reportApiType, targetDate, apiResultPath }
 * @param {Object} abuseReportApiConfig reportApiType 별 config
 * {
		reportApiType : 'GFA',
 		apiResult : 'HDFS',
 		apiInfo : {
 			period : { start : -1, end : -1, unit : 'months' },
 			checkSuccessFile: true
 		},
 		fileInfo : {
 			filePath : '/user/naver-pa-dmp/gfp_report/revenue_refund/{{YYYY}}/{{MM}}',
 			fileName : 'gfa_{{YYYY}}{{MM}}.tsv',
 			separator : '\t'
 		},
 		scheduleInfo : { period : 'MONTH', day : 2, hour : 0 },
 		fields : { ymd, rk, imp, clk, netRevenue, revenue }
 * }
 * @return {Object} baseMetaInfo
 * {
		schedule_id,
		ymd: '20230922',
		reportApiType: 'GFA',
		apiResult: 'HDFS',
		targetDate: '202309',
		localFilePath: 'local_download/abusereportapi/gfa/YYYY/MM/gfa_{{YYYY}}{{MM}}_{{HH}}.tsv',
		reportApiInfo: {
			checkSuccessFile: true,
			separator,
			apiResultPath: '/user/naver-pa-dmp/gfp_report/revenue_refund/2023/09/gfa_202309.tsv',
			fields: {
				ymd: 'created_at',
				country: 'country',
				os: 'os',
				imp: 'imp',
				clk: 'click',
				rk: 'rs_key',
				netRevenue: 'revenue_usd',
			}
		}
 * }
*/
module.exports.getBaseMetaInfo = async ({ _id, ymd, reportApiType, targetDate, apiResultPath }, { apiResult, apiInfo, fileInfo: { separator }, fields }) => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api.service :: getAdProviderMetaInfo] 호출됨');

	// 리포트 연동 부가 정보
	const reportApiInfo = {};
	reportApiInfo.apiResultPath = apiResultPath;
	reportApiInfo.separator = separator;
	reportApiInfo.fields = fields;

	for(const key in apiInfo) {
		reportApiInfo[key] = apiInfo[key];
	}

	// AP 어뷰즈 원본 로컬 다운로드 경로
	const localFilePath = _getLocalFilePath({ ymd, reportApiType, apiResultPath, targetDate });

	const baseMetaInfo = {
		schedule_id: _id,
		ymd, reportApiType,

		// api 결과 타입정보 (HDFS)
		apiResult,

		// 어뷰즈 처리 대상 일자
		targetDate,

		// 리포트 연동에 필요한 정보
		reportApiInfo,

		// AP 어뷰즈 원본 로컬 다운로드 경로
		localFilePath
	};

	return baseMetaInfo;
};


/**
 * _getLocalFilePath : AP 어뷰즈 원본 로컬 다운로드 경로
 *
 * @param {Object} { ymd, reportApiType, apiResultPath, targetDate }
 * @return {String} localFilePath
 */
const _getLocalFilePath = ({ ymd, reportApiType, apiResultPath, targetDate }) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _getLocalFilePath] 호출됨 ( ymd=${ymd}, reportApiType=${reportApiType} )`);

	const monthly = targetDate.length === 6;

	// ymdDt : 디렉토리 경로에서 사용
	// targetDt : 파일명에서 사용
	const ymdDt = moment(ymd);
	const targetDt = moment(targetDate);
	const fileNameFormat = '{{REPORT_API_TYPE}}_{{YYYY}}{{MM}}{{DD}}_{{HHmmss}}{{EXT}}';

	// local_root: local_download
	// abuse_report_api_path: abusereportapi
	// basePath: local_download/abusereportapi/reportApiType/YYYY/MM
	const basePath = file.mkdirIfNotExist(path.join(LOCAL_ROOT, ABUSE_REPORT_API_PATH, reportApiType.toLowerCase(), ymdDt.format('YYYY'), ymdDt.format('MM')));

	// apiResultPath = /user/naver-pa-dmp/gfp_report/revenue_refund/2023/09/gfa_202309.tsv
	// parsed = { root: '/', dir: '/user/naver-pa-dmp/gfp_report/revenue_refund/2023/09', base: 'gfa_202309.tsv', ext: '.tsv', name: 'gfa_202309' }
	const parsed = path.parse(apiResultPath);

	// fileName = '{{REPORT_API_TYPE}}_{{YYYY}}{{MM}}{{DD}}_{{HH}}.{{EXT}}'
	const fileName = fileNameFormat.replace('{{REPORT_API_TYPE}}', reportApiType.toLowerCase())
		.replace('{{YYYY}}', targetDt.format('YYYY'))
		.replace('{{MM}}', targetDt.format('MM'))
		.replace('{{DD}}', monthly ? '' : targetDt.format('DD'))
		.replace('{{HHmmss}}', moment().format('HHmmss'))
		.replace('{{EXT}}', parsed.ext)
		.replace(/\s/g,'');

	// localFilePath = local_download/abusereportapi/{{REPORT_API_TYPE}}/YYYY/MM/{{REPORT_API_TYPE}}_{{YYYY}}{{MM}}{{DD}}_{{HH}}.{{csv/tsv}}
	const localFilePath = path.join(basePath, fileName);

	return localFilePath;
};


/**
 * [PROCESS 4:: TASK 1] waitForReady : AP 가 리포트 연동 가능한 상태인지 체크
 * 	- 1. _SUCCESS 파일 존재 유무로 체크
 * 	- 2. hdfs 파일 상태로 체크
 *
 * @param {Object} baseMetaInfo
 * 	{
 		schedule_id,
 		ymd, reportApiType,
 		apiResult,
 		reportApiInfo : { checkSuccessFile, apiResultPath }
 * }
*/
module.exports.waitForReady = async ({ ymd, reportApiType, apiResult, reportApiInfo: { apiResultPath, checkSuccessFile } }) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: waitForReady] 호출됨 ( ymd=${ymd}, reportApiType=${reportApiType}, apiResult=${apiResult} )`);
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: waitForReady] ${apiResult} 파일 체크 시작`);

	// [PROCESS 4:: TASK 1] AP 가 연동 가능한 상태인지 30분 동안 체크
	if (_.isEqual(apiResult, config.api_result_type.hdfs)) {
		await _waitForHdfsReady({ ymd, reportApiType, reportApiInfo: { apiResultPath, checkSuccessFile } });
	}

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: waitForReady] 대기 종료 ( ymd=${ymd}, reportApiType=${reportApiType}, apiResult=${apiResult} )`);
};


/**
 * _waitForHdfsReady : AP 가 연동 가능한 상태인지 30분 동안 체크 ( HDFS )
 *
 * @param {Object} baseMetaInfo
 *	{
		ymd, reportApiType,
		reportApiInfo: { apiResultPath, checkSuccessFile }
 *	}
 */
const _waitForHdfsReady = async ({ ymd, reportApiType, reportApiInfo: { apiResultPath, checkSuccessFile } }) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _waitForHdfsReady] 호출됨 ( ymd=${ymd}, reportApiType=${reportApiType} )`);

	// apiResultPath = /user/naver-pa-dmp/gfp_report/revenue_refund/2023/09/gfa_202309.tsv
	// parsedFile.dir = /user/naver-pa-dmp/gfp_report/revenue_refund/2023/09
	// successFile = /user/naver-pa-dmp/gfp_report/revenue_refund/2023/09/_SUCCESS
	const parsedFile = path.parse(apiResultPath);
	const checkFilePath = (checkSuccessFile) ? `${parsedFile.dir}/_SUCCESS` : apiResultPath;

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _waitForHdfsReady] checkFilePath= ${checkFilePath}`);

	// TIMEOUT 30분
	const TIMEOUT = moment().add(30, 'minutes');

	// true / false
	let isExists = await c3HdfsApi.exists(checkFilePath);

	// HDFS에 리포트 파일이 있는지 30분 동안 30초 간격으로 체크
	while(_.isEqual(isExists, false)) {
		// [ERROR] TIMEOUT 에러 처리
		if(TIMEOUT.isSameOrBefore(moment())) {
			throw new BusinessError({ message: `[ad-provider-abuse-report-api.service :: _waitForHdfsReady] TIMEOUT(30분) - HDFS 에 파일이 없음 ( checkFilePath= ${checkFilePath} )` });
		}

		// 30초간 sleep
		await _sleep(30 * 1000);

		isExists = await c3HdfsApi.exists(checkFilePath);

		logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _waitForHdfsReady] 재요청 ::: ${moment().format('YYYY-MM-DD HH:mm:ss')}`);
	}

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _waitForHdfsReady] 대기 종료 ( ymd=${ymd}, reportApiType=${reportApiType} )`);
};


/**
 * [Promise]
 * _sleep : ms 만큼 sleep
 *
 * @param {int} ms
 */
const _sleep = ms => {
	return new Promise(resolve => {
		setTimeout(resolve, ms);
	});
};


/**
 * [PROCESS 4:: TASK 2] downloadToLocal : AP 어뷰즈 원본 파일 로컬 다운로드
 * 	- HDFS 인 경우, cli 로 다운로드
 *
 * @param {Object} baseMetaInfo
 * 	{
		ymd, reportApiType, apiResult, localFilePath,
		reportApiInfo: { apiResultPath }
 * }
 */
module.exports.downloadToLocal = async ({ ymd, reportApiType, apiResult, localFilePath, reportApiInfo: { apiResultPath } }) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: downloadToLocal] AP 원본 파일 다운로드 시작 ( ymd=${ymd}, reportApiType=${reportApiType}, apiResult=${apiResult} )`);

	if (_.isEqual(apiResult, config.api_result_type.hdfs)) {
		await c3HdfsCli.download(apiResultPath, localFilePath);
	}

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: downloadToLocal] AP 원본 파일 다운로드 종료 ( ymd=${ymd}, reportApiType=${reportApiType}, apiResult=${apiResult} )`);
};


/**
 * [PROCESS 4:: TASK 3] getAbuseReportFileInfo : 어뷰즈 리포트 파일 정보 추출 ( 데이터 기간 및 총 건수 정보 )
 *
 * @param {Object} baseMetaInfo { ymd, reportApiType, apiResult, localFilePath, reportApiInfo : { separator, fields } }
 * @return {Object} abuseReportFileInfo { totalLineCount, dateList }
*/
module.exports.getAbuseReportFileInfo = async ({ ymd, reportApiType, apiResult, localFilePath, reportApiInfo: { separator, fields } }) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: getAbuseReportFileInfo] 호출됨 ( ymd=${ymd}, reportApiType=${reportApiType}, apiResult=${apiResult} )`);

	const { totalLineCount, dateList } = await _getAbuseReportFileInfo({ localFilePath, separator, fields });

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: getAbuseReportFileInfo] totalLineCount= ${totalLineCount}, dateList.length= ${dateList.length}`);

	return { totalLineCount, dateList };
};


/**
 * [Promise]
 * _getAbuseReportFileInfo : 어뷰즈 리포트 파일 정보 추출 ( 데이터 기간 및 총 건수 정보 )
 *
 * @param {Object} { localFilePath, separator, fields: { ymd } }
 * @return {Object} { totalLineCount, dateList }
 */
const _getAbuseReportFileInfo = ({ localFilePath, separator, fields: { ymd } }) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _getAbuseReportFileInfo] 호출됨`);

	return new Promise((resolve, reject) => {
		// AP 어뷰즈 원본 로컬 파일 읽기 스트림
		const fileStream = fs.createReadStream(localFilePath);

		let totalLineCount = 0;
		const dateSet = new Set();

		/* csv 읽기 */
		const csvStream = csv({ separator });

		const onData = async (data) => {
			try {
				totalLineCount++;
				dateSet.add(data[ymd]);
			} catch(err) {
				reject({ message: `[ad-provider-abuse-report-api.service :: _getAbuseReportFileInfo] csvStream data 에러`, err });

				closeStreams();
			}
		};

		csvStream.on('data', onData);

		csvStream.on('end', () => {
			try {
				logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _getAbuseReportFileInfo] csvStream end 완료`);

				csvStream.removeListener('data', onData);

				if (fileStream && _.isFunction(fileStream.close)) fileStream.close();

				const dateList = Array.from(dateSet).map(date => moment(date).format('YYYYMMDD')).sort();

				resolve({ totalLineCount, dateList });
			} catch (err) {
				reject({ message: `[ad-provider-abuse-report-api.service :: _getAbuseReportFileInfo] csvStream end 에러`, err });

				closeStreams();
			}
		});

		csvStream.on('error', err => {
			reject({ message: `[ad-provider-abuse-report-api.service :: _getAbuseReportFileInfo] csvStream 에러`, err });

			closeStreams();
		});

		fileStream.on('end', () => {
			logger.debug(REPORT_API_LOGGER, `fileStream end 완료`);
		});

		fileStream.on('error', err => {
			reject({ message: `[ad-provider-abuse-report-api.service :: _getAbuseReportFileInfo] fileStream 에러`, err });

			closeStreams();
		});

		fileStream.pipe(csvStream);

		// 모든 stream 종료 처리
		const closeStreams = () => {
			logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: closeStreams]`);

			if (csvStream) {
				csvStream.removeListener('data', onData);
				csvStream.end();
			}

			if (fileStream && _.isFunction(fileStream.close)) fileStream.close();
		};
	});
};


/**
 * [PROCESS 4:: TASK 4] getAdProviderMetaInfo : AdProvider-AdProviderInfos 메타 정보 가져오기
 *  TASK 4-0. adProvider_id 에 해당 하는 AdProvider 추출
 *  TASK 4-1. adProvider_id 에 해당 하는 AdProviderInfo 가져오기
 *  TASK 4-2. 통화 및 환율 정보 셋팅
 *  TASK 4-3. 리포트 데이터 기간 정보 셋팅
 *  TASK 4-4. GFP 수수료 정보 가져오기
 *  TASK 4-5. 메타 정보 셋팅
 *
 * @param {Object} baseMetaInfo { schedule_id, ymd, reportApiType, apiResult, targetDate, localFilePath, reportApiInfo }
 * @param {Object} abuseReportFileInfo { totalLineCount, dateList }
 * @return {Object} adProviderMetaInfo
 * GFA example = {
		schedule_id,
		ymd: '20230702',
		reportApiType: 'GFA',
		apiResult: 'HDFS',
		localFilePath: 'local_download/abusereportapi/gfa/YYYY/MM/gfa_{{YYYY}}{{MM}}_{{HH}}.tsv',
		exchangeRate: { endDate: '20230701', startDate: '20230601', from: 'KRW', to: 'USD', rate: '0.0008837155955739682' },
		targetDate: '202306',
		startDate: moment('2023-06-01T00:00:00.000'), endDate: moment('2023-06-30T00:00:00.000'),
		reportApiInfo: {
			checkSuccessFile: true,
			separator,
			apiResultPath: '/user/naver-pa-dmp/gfp_report/revenue_refund/2023/06/gfa_202306.tsv',
			fields: {
				ymd: 'paymentDate',
				rk: 'reportKey',
				imp: 'impCount',
				clk: 'clickCount',
				netRevenue: 'rev1',
				revenue: 'rev2'
			},
		},
		adProviderInfos: [{
				publisher_id: '5bcd6979868338bd69d9df9f',
				adProvider_id: '5bebc62b77bd856e48ae2111',
				adProviderInfo_id: '5bebcd6f72172e4db374b53e',
				place_ids: ['5bebe3041f2ff071a14e0227','5bebe51e1f2ff071a14e0229','5befc147c6c1ae1c5247c836','5befc147c6c1ae1c5247c837']
			},
			{
				publisher_id: '5b3f2e7a73001509b777c20e',
				adProvider_id: '5bebc62b77bd856e48ae2111',
				adProviderInfo_id: '5bee832ea3cf1243f858cda1',
				place_ids: ['5bee8453a3cf1243f858cda4','5bee8453a3cf1243f858cda5','5bee8453a3cf1243f858cda6']
		}],
		gfpFeeRate: [{
			adProvider_id: 5bdf9aab77bd856e482a84d4,
			publisher_id: 5bcd6979868338bd69d9df9f,
			date: '20230330',
			feeRate: 0.2
		}, ... ],
		abuseReportFileInfo: { totalLineCount, dateList }
	}
*/
module.exports.getAdProviderMetaInfo = async (baseMetaInfo, abuseReportFileInfo) => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api.service :: getAdProviderMetaInfo] 호출됨');


	// [PROCESS 4:: TASK 4-0] adProvider_id에 해당 하는 AdProvider 추출
	const {
		currency, adProvider_ids
	} = await _getAdProviders(baseMetaInfo.reportApiType);


	// [PROCESS 4:: TASK 4-1] adProvider_ids 에 해당 하는 AdProviderInfo 가져오기
	// adProviderInfos = [{ adProvider_id, publisher_id, adProviderInfo_id, place_ids }]
	const adProviderInfos = await _getAdProviderInfos(adProvider_ids);


	/*
		[PROCESS 4:: TASK 4-2] 통화 및 환율 정보 셋팅
	*/

	// 통화 정보
	const currencyFrom = currency;
	const currencyTo = (_.upperCase(currencyFrom) === CURRENCY.KRW.code) ? CURRENCY.USD.code : CURRENCY.KRW.code;

	// 평균 환율 정보
	const yesterday = moment().add(-1, 'days').format('YYYYMMDD'); // 어제 날짜
	const before30days = moment(yesterday).add(-29, 'days').format('YYYYMMDD'); // 30일 전 날짜
	const exchangeRate = await exchangeRateService.getExchangeRateAverage(before30days, yesterday, currencyFrom, currencyTo);


	/*
		[PROCESS 4:: TASK 4-3] 리포트 데이터 기간 정보 셋팅
	*/
	const startDate = moment(_.head(abuseReportFileInfo.dateList));
	const endDate = moment(_.last(abuseReportFileInfo.dateList));


	// [PROCESS 4:: TASK 4-4] GFP 수수료 정보 가져오기
	const gfpFeeRate = await _getGfpFeeRate({ adProvider_ids, startDate, endDate });


	// [PROCESS 4:: TASK 4-5] 메타 정보 셋팅
	const adProviderMetaInfo = Object.assign({
		adProvider_ids,

		/* adProviderInfos 하위에 있는 place_ids 정보 (adProvider_id, publisher_id, adProviderInfo_id, place_ids) */
		adProviderInfos,

		/* 평균 환율 정보 { from, to, rate } */
		exchangeRate,

		/* 리포트 데이터 기간 정보 */
		startDate, endDate,

		/* GFP 수수료 정보 */
		gfpFeeRate,

		/* 어뷰즈 리포트 파일 정보 { totalLineCount, dateList } */
		abuseReportFileInfo
	}, baseMetaInfo);

	return adProviderMetaInfo;
};


/**
 * [PROCESS 4:: TASK 4-0] _getAdProviders : adProvider_id에 해당 하는 AdProvider 추출
 *
 * @param {String} reportApiType
 * @return {Object} adProvider
 * GFD example = {
		currency:'USD',
		adProvider_ids:['5bdf9aab77bd856e482a84d4', '5c04827977bd856e48fbf0e3'],
 * 	}
*/
const _getAdProviders = async reportApiType => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api.service :: _getAdProviders] 호출됨');

	let adProviders = await AdProvider.aggregate()
		.match({
			'reportApi.type': reportApiType,
			'reportApi.rkUse': 1,
			adProviderType: AD_PROVIDER_TYPE.IN_NAVER.code
		})
		.project({
			_id: 1,
			currency:1,
		})
		.group({
			_id: { currency: '$currency' },
			adProvider_ids: { $push: '$_id' },
		})
		.project({
			_id: 0,
			currency:'$_id.currency',
			adProvider_ids:1,
		})
		.exec();


	// [ERROR] AdProvider 정보가 없는 경우, 에러 처리
	if(_.isEmpty(adProviders)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api.service :: _getAdProviders] AdProvider(${reportApiType}) No Result` });
	}

	// [ERROR] currency 가 하나가 아닌 경우, 에러 처리
	if(adProviders.length > 1) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api.service :: _getAdProviders] AdProvider(${reportApiType}) Multi Currency` });
	}

	adProviders = adProviders.pop();

	return adProviders;
};


/**
 * [PROCESS 4:: TASK 4-1] _getAdProviderInfos : adProvider_id 에 해당 하는 AdProviderInfo 가져오기
 *
 * @param {Array} adProvider_ids
 * @return {Array} adProviderInfos
 * GFP example = [{
		adProvider_id: '5bebc62b77bd856e48ae2111',
		publisher_id: '5bcd6979868338bd69d9df9f',
		adProviderInfo_id: '5bebcd6f72172e4db374b53e',
		place_ids: ['5bebe3041f2ff071a14e0227','5bebe51e1f2ff071a14e0229','5befc147c6c1ae1c5247c836','5befc147c6c1ae1c5247c837']
	},
	{
		adProvider_id: '5bebc62b77bd856e48ae2111',
		publisher_id: '5b3f2e7a73001509b777c20e',
		adProviderInfo_id: '5bee832ea3cf1243f858cda1',
		place_ids: ['5bee8453a3cf1243f858cda4','5bee8453a3cf1243f858cda5','5bee8453a3cf1243f858cda6']
	},
	{
		adProvider_id: '5c04827977bd856e48fbf0e3',
		publisher_id: '5bcd6979868338bd69d9df9f',
		adProviderInfo_id: '5c04879f684ea5f884e01be0',
		place_ids: [ '5c0487cb684ea5f884e01be1' ]
 * 	}]
*/
const _getAdProviderInfos = async adProvider_ids => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api.service :: _getAdProviderInfos] 호출됨');

	const adProviderInfos = await AdProviderInfo.aggregate()
		.match({ adProvider_id: { $in: adProvider_ids } })
		.project({
			_id:0,
			adProviderInfo_id: '$_id',
			adProvider_id: 1,
			publisher_id: 1,
		})
		.lookup({
			from: 'AdProviderPlaces',
			foreignField: 'adProviderInfo_id',
			localField: 'adProviderInfo_id',
			as: 'places'
		})
		.project({
			_id: 0,
			adProviderInfo_id: 1,
			adProvider_id: 1,
			publisher_id: 1,
			place_ids: '$places._id'
		})
		.exec();


	// [ERROR] adProvider_id 에 해당 하는 adProviderInfos 가 없는 경우, 에러 처리
	if(_.isEmpty(adProviderInfos)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api.service :: _getAdProviderInfos] AdProviderInfos(${adProvider_id}) No Result` });
	}

	return adProviderInfos;
};


/**
 * [PROCESS 5:: TASK 4-4] _getGfpFeeRate : GFP 수수료 정보 가져오기
 *
 * @param {Object} { adProvider_ids, startDate, endDate}
 * @return {Array} gfpFeeRate
 [{
		adProvider_id: 5bdf9aab77bd856e482a84d4,
		publisher_id: 5bcd6979868338bd69d9df9f,
		date: '20230330',
		feeRate: 0.2
	}, ... ]
*/
const _getGfpFeeRate = async ({ adProvider_ids, startDate, endDate }) => {
	const startDt = startDate.format('YYYYMMDD');
	const endDt = endDate.format('YYYYMMDD');

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _getGfpFeeRate] 호출됨 ( ${startDt} ~ ${endDt} )`);

	let gfpFeeRate = await GfpFeeRate.aggregate()
		.match({ adProvider_id: { $in: adProvider_ids }, date: { $gte: startDt, $lte: endDt } })
		.project({ _id: 0, adProvider_id: 1, publisher_id: 1, date: 1, feeRate: 1 })

	// Decimal128 타입인 수수료율을 float 타입으로 변환
	gfpFeeRate = gfpFeeRate.map(data => {
		data.feeRate = parseFloat(data.feeRate);
		return data;
	});

	return gfpFeeRate;
};


/******************************************************************************************/
/******************************************************************************************/
/******************************************************************************************/


/**
 * [PROCESS 4:: TASK 6] processAdProviderAbuseStatHdfs : 리포트 파일 가공 및 HDFS 저장
 *  TASK 6-1. 리포트 파일 정보를 chunk_size 단위로 끊어서 AdProviderReportApiResults DB 에 로깅
 *  TASK 6-2. HDFS & LOCAL 일괄 삭제 처리
 *  TASK 6-3. 파케이 파일 생성에 필요한 파일 정보 셋팅
 *  TASK 6-4. 어뷰즈 리포트 연동 순차 처리
 *
 * @param {Object} adProviderMetaInfo
 * 	{
		schedule_id,
		ymd, reportApiType,
		adProvider_ids,
		localFilePath,
		exchangeRate: { from, to, rate },
		targetDate,
		adProviderInfos: [{ adProvider_id, publisher_id, adProviderInfo_id, place_ids }],
		reportApiInfo: { apiResultPath, separator, fields },
		gfpFeeRate: [{ adProvider_id, publisher_id, date, feeRate }],
		abuseReportFileInfo: { totalLineCount, dateList }
 * }
*/
module.exports.processAdProviderAbuseStatHdfs = async ({ schedule_id, ymd, reportApiType, exchangeRate, targetDate, adProvider_ids, adProviderInfos, localFilePath, reportApiInfo: { apiResultPath, separator, fields }, gfpFeeRate, abuseReportFileInfo: { totalLineCount, dateList } }) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: processAdProviderAbuseStatHdfs] 리포트 파일 처리 시작`);
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: processAdProviderAbuseStatHdfs] totalLineCount= ${totalLineCount}, apiResultPath= ${apiResultPath}`);


	// [PROCESS 4:: TASK 6-1] 리포트 파일 정보를 chunk_size 단위로 끊어서 AdProviderReportApiResults DB에 로깅
	/* chunkInfos = [{ chunkNumber, startLine, endLine, state }] */
	const { reportApiResult_id, chunkInfos } = await abuseScheduleService.upsertReportApiResults({ schedule_id, ymd, reportApiType, apiResultPath, totalLineCount });


	// [PROCESS 4:: TASK 6-2] HDFS & LOCAL 일괄 삭제 처리
	await _deleteAdProviderAbuseStat({ ymd, reportApiType, reportApiResult_id, adProvider_ids, targetDate });


	// [PROCESS 4:: TASK 6-3] 파케이 파일 생성에 필요한 파일 정보 셋팅
	const parquetFileInfo = await _getParquetFileInfo({ reportApiResult_id, ymd, reportApiType, targetDate, adProviderInfos, dateList });


	// [PROCESS 4:: TASK 6-4] 어뷰즈 리포트 연동 순차 처리
	await _process({ reportApiResult_id, chunkInfos, localFilePath, exchangeRate, adProviderInfos, gfpFeeRate, separator, fields, parquetFileInfo });


	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: processAdProviderAbuseStatHdfs] 리포트 파일 처리 완료`);
};


/**
 * [PROCESS 4:: TASK 6-2] _deleteAdProviderAbuseStat : HDFS & LOCAL 일괄 삭제 처리
 *
 * @param {Object} { ymd, reportApiType, reportApiResult_id, adProvider_ids, targetDate }
*/
const _deleteAdProviderAbuseStat = async ({ ymd, reportApiType, reportApiResult_id, adProvider_ids, targetDate }) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _deleteAdProviderAbuseStat] 데이터 일괄 삭제 처리 시작`);

	// HDFS 일괄 삭제 처리
	await _deleteAdProviderAbuseStatHdfs({ adProvider_ids, targetDate });

	// LOCAL 일괄 삭제 처리
	await _deleteAdProviderAbuseStatLocal({ ymd, reportApiType, reportApiResult_id });

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _deleteAdProviderAbuseStat] 데이터 일괄 삭제 처리 완료`);
};


/**
 * _deleteAdProviderAbuseStatHdfs : HDFS 일괄 삭제 처리
 * 	- created=targetDate 하위 adProvider_ids 에 해당 하는 디렉토리를 찾아서 삭제
 * 	- startDate, endDate 를 특정할 수 없으므로, adProvider_ids 로 찾아서 삭제 처리함
 *
 * @param {Object} { adProvider_ids, targetDate }
*/
const _deleteAdProviderAbuseStatHdfs = async ({ adProvider_ids, targetDate }) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _deleteAdProviderAbuseStatHdfs] created=${targetDate} 하위 ${adProvider_ids} 에 해당 하는 데이터 일괄 삭제 처리 시작`);

	// hdfsDeleteRootDir = /user/gfp-data/adprovider_abuse/cq/created=xxx
	const hdfsDeleteRootDir = path.join(ADPROVIDER_ABUSE_HDFS_ROOT_DIR, `created=${targetDate}`);

	// created=xxx 하위 adProviderId 까지의 모든 디렉토리 경로 조회하기
	// hdfsDeleteDirList= ['/user/gfp-data/adprovider_abuse/cq/created=xxx/yyyy/mm/dd/adProviderId=xxxx', ...]
	const hdfsDeleteDirList = await _getHdfsDeleteDirList(hdfsDeleteRootDir);

	const adProviderIds = adProvider_ids.map(_ => _.toString());

	const chunkSize = (hdfsDeleteDirList.length > 5) ? 5 : hdfsDeleteDirList.length;
	for (let i = 0; i < hdfsDeleteDirList.length; i += chunkSize) {
		const chunkList = hdfsDeleteDirList.slice(i, i + chunkSize);
		await Promise.all(chunkList.map(async hdfsDeleteDir => {
			// hdfsDeleteDir = /user/gfp-data/adprovider_abuse/cq/created=202306/yyyy/mm/dd/adProviderId=xxxx
			const adProviderId = hdfsDeleteDir.replace(/.*adProviderId=/, '');

			// adProvider_ids 에 adProviderId 가 존재 하지 않는 경우, 삭제 대상 아님
			if (!_.includes(adProviderIds, adProviderId)) {
				return;
			}

			// 디렉토리 경로가 HDFS 에 존재하는지 확인
			if (await c3HdfsApi.exists(hdfsDeleteDir)) {
				// 디렉토리 일괄 삭제하기
				const err = await c3HdfsApi.delete(hdfsDeleteDir, true);

				// 에러가 발생한 경우
				if (err) {
					throw new BusinessError({ message: `[ad-provider-abuse-report-api.service :: _deleteAdProviderAbuseStatHdfs] adProviderId( ${adProviderId} ) 데이터 일괄 삭제 실패` }, { err, detail: JSON.stringify(err, null, 2) });
				}
			}
		}));
	}

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _deleteAdProviderAbuseStatHdfs] created=${targetDate} 하위 ${adProvider_ids} 에 해당 하는 데이터 일괄 삭제 처리 완료`);
};


/**
 * _deleteAdProviderAbuseStatLocal : LOCAL 일괄 삭제 처리
 *
 * @param {Object} { ymd, reportApiType, reportApiResult_id }
*/
const _deleteAdProviderAbuseStatLocal = async ({ ymd, reportApiType, reportApiResult_id }) => {
	// 배치 처리 기준 날짜
	const ymdDate = moment(ymd, 'YYYYMMDD');

	// localDeleteDir= local_download/abusereportapi/gfa/2023/07/resultId=xxx
	const localDeleteDir = path.join(LOCAL_ROOT, ABUSE_REPORT_API_PATH, reportApiType.toLowerCase(), ymdDate.format('YYYY'), ymdDate.format('MM'), `resultId=${reportApiResult_id}`);

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _deleteAdProviderAbuseStatLocal] localDeleteDir=${localDeleteDir} 하위 데이터 일괄 삭제 처리 시작`);

	if (fs.existsSync(localDeleteDir)) {
		// 디렉토리 & 파일 삭제
		file.deleteDirectoryRecursively(localDeleteDir, true);
	}

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _deleteAdProviderAbuseStatLocal] localDeleteDir=${localDeleteDir} 하위 데이터 일괄 삭제 처리 완료`);
};


/**
 * [Traverse Function] _getHdfsDeleteDirList : 하위 adProviderId 까지의 모든 디렉토리 경로 조회하기
 *
 * @return {Array} targetDirList
 * 	['created=xxx/yyyy/mm/dd/adProviderId=xxxx', ... ]
*/
const _getHdfsDeleteDirList = async (targetPath) => {
	let dirList;

	// 디렉토리 경로가 HDFS 에 존재하는지 확인
	if (await c3HdfsApi.exists(targetPath)) {
		dirList = await c3HdfsApi.readDir(targetPath);
	}

	// 하위 디렉토리가 없는 경우
	if (_.isEmpty(dirList)) {
		return [];
	}

	const targetDirList = await Promise.all(dirList.filter(dir => dir.type === 'DIRECTORY').map(async ({ pathSuffix }) => {
		if (pathSuffix.indexOf('adProviderId=') > -1) {
			return path.join(targetPath, pathSuffix);
		} else {
			return await _getHdfsDeleteDirList(path.join(targetPath, pathSuffix));
		}
	}));

	return _.flatten(targetDirList);
};


/**
 * [PROCESS 4:: TASK 6-3] _getParquetFileInfo : 파케이 파일 생성에 필요한 파일 정보 셋팅
 *
 * @param {Object} metaInfo { reportApiResult_id, ymd, reportApiType, targetDate, adProviderInfos, dateList }
 * @return {Object} { localDirPath, dtApPubList, parquetFilePathInfo }
 * 	{
 * 		localDirPath: 'local_download/abusereportapi/gfa/2023/07/resultId=61839d582634cc216ae8bcf1/{{chunkNumber}}',
 * 		dtApPubList: ['date_apid_pubid', ... ],
 * 		parquetFilePathInfo: {
 * 			'date_apid_pubid': {
 * 				remoteDirPath: '/created=202306/2023/06/01/adProviderId=5b74d94bc36eef272090ca52/publisherId=5b74d94bc36eef272090ca56',
 *  			localFullPath: 'local_download/abusereportapi/gfa/2023/07/resultId=61839d582634cc216ae8bcf1/{{chunkNumber}}/created=202306/2023/06/01/adProviderId=5b74d94bc36eef272090ca52/publisherId=5b74d94bc36eef272090ca56/part-61839d582634cc216ae8bcf1-{{chunkNumber}}-20211207193137.parquet'
 * 			}, ...
 * 		}
 * 	}
*/
const _getParquetFileInfo = async ({ reportApiResult_id, ymd, reportApiType, targetDate, adProviderInfos, dateList }) => {
	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _getParquetFileInfo] 파케이 파일 생성에 필요한 파일 정보 셋팅`);

	// 배치 처리 기준 날짜
	const ymdDate = moment(ymd, 'YYYYMMDD');

	// localDirPath= local_download/abusereportapi/gfa/2023/07/resultId={{reportApiResult_id}}/{{chunkNumber}}
	const localDirPath = path.join(LOCAL_ROOT, ABUSE_REPORT_API_PATH, reportApiType.toLowerCase(), ymdDate.format('YYYY'), ymdDate.format('MM'), `resultId=${reportApiResult_id}`, '{{chunkNumber}}');

	// dtApPub 리스트
	// ['20230601_5bebc62b77bd856e48ae2111_5bcd6979868338bd69d9df9f', 'date_apid_pubid', ... ]
	const dtApPubList = [];

	// dtApPub 별 parquetFilePath 정보
	const parquetFilePathInfo = {};

	await Promise.all(dateList.map(async date => {
		const dt = moment(date);

		// commonPath= /created={{targetDate}}/YYYY/MM/DD/adProviderId={{adProviderId}}/publisherId={{publisherId}}
		const commonPath = ADPROVIDER_ABUSE_HDFS_COMMON_PATH.replace(/{{targetDate}}/g, targetDate).replace(/{{YYYY}}/g, dt.format('YYYY')).replace(/{{MM}}/g, dt.format('MM')).replace(/{{DD}}/g, dt.format('DD'));

		await Promise.all(adProviderInfos.map(async ({ place_ids, publisher_id, adProvider_id }) => {
			// 플레이스 연동된 게 없는 경우, 제외
			if (place_ids.length < 1) {
				return;
			}

			const dtApPub = `${date}_${adProvider_id}_${publisher_id}`;
			dtApPubList.push(dtApPub);

			// remoteDirPath= /created=xxx/YYYY/MM/DD/adProviderId=xxx/publisherId=xxx
			const remoteDirPath = commonPath.replace(/{{adProviderId}}/g, adProvider_id).replace(/{{publisherId}}/g, publisher_id);

			// localFullPath= local_download/abusereportapi/gfa/2023/07/resultId={{reportApiResult_id}}/{{chunkNumber}}/created=targetDate/YYYY/MM/DD/adProviderId=xxx/publisherId=xxx
			const localFullPath = path.join(localDirPath, remoteDirPath, `part-${reportApiResult_id}-{{chunkNumber}}-${moment().format('YYYYMMDDHHmmss')}.parquet`);

			parquetFilePathInfo[dtApPub] = { remoteDirPath, localFullPath };
		}));
	}));

	return { localDirPath, dtApPubList, parquetFilePathInfo };
};


/**
 * [PROCESS 4:: TASK 6-4] _process : 어뷰즈 리포트 연동 순차 처리
 *
 * @param {Object} metaInfo { reportApiResult_id, chunkInfos, localFilePath, exchangeRate, adProviderInfos, gfpFeeRate, separator, fields, parquetFileInfo }
*/
const _process = async metaInfo => {
	logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api.service :: _process] 호출됨');

	const reportApiResult_id = metaInfo.reportApiResult_id;
	const chunkInfos = metaInfo.chunkInfos;

	for (const chunkInfo of chunkInfos) {
		const { dataCount, filteredCount, err } = await abuseHdfsService.processChunkInfo(metaInfo, chunkInfo);

		// 부분/전체 처리 완료 상태 업데이트
		await abuseScheduleService.updateReportApiResults({ reportApiResult_id, chunkNumber: chunkInfo.chunkNumber, successCount: dataCount, filteredCount, isSuccess: _.isNil(err) });

		if (!_.isNil(err)) throw err;
	}
};


/******************************************************************************************/
/******************************************************************************************/
/******************************************************************************************/


/**
 * sendResultMail : 배치 처리 결과 알림 메일 보내기
 * 	1. 처리할 데이터가 없는 경우 (ALL)
 * 	2. 제외된 데이터가 있는 경우 (IN_NAVER)
 *
 * @param {Object} abuseReportApiSchedule { _id, ymd, reportApiType, targetDate, apiResultPath, retryCount }
 * @param {Object} result { state, totalLineCount, totalSuccessCount, totalFilteredCount }
*/
module.exports.sendResultMail = ({ _id, ymd, reportApiType, targetDate, apiResultPath, retryCount }, { state, totalLineCount, totalSuccessCount, totalFilteredCount }) => {
	setTimeout(async () => {
		try {
			logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api.service :: sendResultMail] 호출됨');

			const totalSkippedCount = totalLineCount - totalSuccessCount;

			const env_name = config.report_api_result_receiver_env_name;
			const to = await _getReceiverInfo(env_name, reportApiType);

			const subject = `어뷰즈 리포트 연동 배치 결과 알림 메일 (${reportApiType}) - ${ymd}`;

			let html = '';

			// 재실행 배치인 경우
			if (retryCount > -1) {
				html += '[배치 재실행 결과]<br><br>';
			}

			// 1. 처리할 데이터가 없는 경우
			if(!_.isNil(state) && _.isEqual(state, 'NO_DATA')) {
				html += `연동 리포트 파일에 데이터가 없습니다.<br><br>`;
			}

			// 2. 제외된 데이터가 있는 경우, 제외된 데이터 건수 정보 추가 (IN_NAVER)
			if(!_.isNil(totalSkippedCount) && totalSkippedCount > 0) {
				html += `총 ${totalSkippedCount} 건의 데이터가 제외 되었습니다.<br><br>`;

				const { notExistRk, invalidEncodedRk, invalidEncodedRsKeyValue, notExistRequired, notExistMeta } = totalFilteredCount;

				if (notExistRk > 0) html += `- notExistRk: ${notExistRk}<br>`;
				if (invalidEncodedRk > 0) html += `- invalidEncodedRk: ${invalidEncodedRk}<br>`;
				if (invalidEncodedRsKeyValue > 0) html += `- invalidEncodedRsKeyValue: ${invalidEncodedRsKeyValue}<br>`;
				if (notExistRequired > 0) html += `- notExistRequired: ${notExistRequired}<br>`;
				if (notExistMeta > 0) html += `- notExistMeta: ${notExistMeta}<br>`;

				html += '<br>';
			}

			if(!_.isEmpty(html)) {
				html += `- _id: ${_id}<br>`;
				html += `- ymd: ${ymd}<br>`;
				html += `- reportApiType: ${reportApiType}<br>`;
				html += `- targetDate: ${targetDate}<br>`;
				html += `- apiResultPath: ${apiResultPath}<br>`;

				mailer.sendMail({ to, subject, html });
			}
		} catch(e) {
			logger.error(ERROR_LOGGER, `[ad-provider-abuse-report-api.service :: sendResultMail] Error :: \n ${e.stack}\n\n`, e);
		}
	}, 0);
};


/**
 * sendFailureMail : 배치 실패 메일 보내기
 *
 * @param {Object} abuseReportApiSchedule { _id, ymd, reportApiType, targetDate, apiResultPath }
*/
module.exports.sendFailureMail = ({ _id, ymd, reportApiType, targetDate , apiResultPath }) => {
	setTimeout(async () => {
		try {
			logger.debug(REPORT_API_LOGGER, '[ad-provider-abuse-report-api.service :: sendFailureMail] 호출됨');

			const env_name = config.report_api_failure_receiver_env_name;
			const to = await _getReceiverInfo(env_name, reportApiType);

			const subject = `어뷰즈 리포트 연동 배치 실패 알림 메일 (${reportApiType}) - ${ymd}`;

			let html = `AbuseReportApiSchedule 정보<br><br>`
					+ `- _id: ${_id}<br>`
					+ `- ymd: ${ymd}<br>`
					+ `- reportApiType: ${reportApiType}<br>`
					+ `- targetDate: ${targetDate}<br>`
					+ `- apiResultPath: ${apiResultPath}<br>`;

			mailer.sendMail({ to, subject, html });
		} catch(e) {
			logger.error(ERROR_LOGGER, `[ad-provider-abuse-report-api.service :: sendFailureMail] Error :: \n ${e.stack}\n\n`, e);
		}
	}, 0);
};


/**
 * _getReceiverInfo : Environments 에서 메일 수신자 정보 가져오기
 *
 * @param {String} env_name
 * @param {String} reportApiType
 * @return {String} to
*/
const _getReceiverInfo = async (env_name, reportApiType) => {
	// [ERROR] env_name 가 없으면, 에러
	if(_.isNil(env_name) || _.isEmpty(env_name)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api.service :: _getReceiverInfo] env_name 정보 없음` });
	}

	// 수신자 환경 정보 : { name: 'report-api-result-receiver', value: { COMMON: ['<EMAIL>'], NDP: ['<EMAIL>;']} }
	const receiverEnv = await Environments.findOne({ name: env_name }).exec();


	// [ERROR] 수신자 환경 정보 없는 경우, 에러
	if(_.isNil(receiverEnv)) {
		throw new BusinessError({ message: `[ad-provider-abuse-report-api.service :: _getReceiverInfo] Environments ${env_name} 에 수신자 정보 없음` });
	}

	const commonTo = receiverEnv.value.common || [];
	const targetTo = receiverEnv.value[reportApiType] || [];

	let to = new Set([...commonTo, ...targetTo]);
	to = Array.from(to).join(';');

	logger.debug(REPORT_API_LOGGER, `[ad-provider-abuse-report-api.service :: _getReceiverInfo] Environments ${env_name}= ${to}`);

	return to;
};
