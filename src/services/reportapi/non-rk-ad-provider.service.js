'use strict';


import os from 'os';
import fs from 'fs';
import _ from 'lodash';
import path from 'path';
import moment from 'moment';
import { execSync, fork } from 'child_process';
import parallel_limit from 'run-parallel-limit';

import COMMON_CODE from '@ssp/ssp-common-code';

import config from '../../config/config';

import * as file from '../../utils/file.util';
import * as logger from '../../utils/logger.util';

import { BusinessError } from '../../common/error';

// Data DB
import { GfpFeeRate } from '../../models/data/gfp-fee-rate.schema';
import { ZirconTrace } from '../../models/data/zircon-trace.schema';

// CMS DB
import { Place } from '../../models/places.schema';
import { AdProvider } from '../../models/ad-providers.schema';
import { AdProviderInfo } from '../../models/ad-provider-infos.schema';
import { CountryMappings } from '../../models/country-mappings.schema';
import { AdProviderNonRkStat } from '../../models/ad-provider-non-rk-stats.schema';

/* [FADEOUT] 9월초 */
import { Publisher } from '../../models/publishers.schema';


import * as c3HdfsApi from '../../c3/hdfs-api';
import * as c3HdfsCli from '../../c3/hdfs-cli';

import * as exchangeRateService from '../../services/reportapi/exchange-rate.service';
import * as batchReportResultService from '../../services/reportapi/batch-report-processing-result.service';

const CURRENCY = COMMON_CODE.codeEncAvailable()['Currency'];

const NON_RK_LOGGER = 'report_api_non_rk';

const LOCAL_ROOT = config.local_root;
const REPORT_API_PATH = config.report_api_path;

// SILVERGREY_HDFS_COMMON_PATH = /{{YYYY}}/{{MM}}/{{DD}}/adProviderId={{adProviderId}}/publisherId={{publisherId}}
// SILVERGREY_HDFS_INTERMEDIATE_NONRK_DIR = /user/gfp-data/silvergrey/intermediate/nonrk
// SILVERGREY_HDFS_NONRK_DIR = /user/gfp-data/silvergrey/nonrk
const SILVERGREY_HDFS_COMMON_PATH = config.silvergrey.hdfs.common;
const SILVERGREY_HDFS_INTERMEDIATE_NONRK_DIR = path.join(config.silvergrey.hdfs.root_dir, config.silvergrey.hdfs.intermediate_dir, config.silvergrey.hdfs.nonrk_dir);
const SILVERGREY_HDFS_NONRK_DIR = path.join(config.silvergrey.hdfs.root_dir, config.silvergrey.hdfs.nonrk_dir);


/**
 * [PROCESS 3] getParallelSchedules : 병렬 처리를 위해 reportInfos 기준으로 reportJobSchedules 추출
 * 
 * @param {Array} reportJobSchedules
 * @return {Array} parallelSchedules
	[
		{
			reportInfo1: 'aaa',
			reportInfo2: 'bbb',
			reportInfo3: 'ccc',
			reportJobSchedules: [{ ymd, adProviderType, reportApiType, adProvider_id, publisher_id, period, apiResultPath, sourcePath }]
		},
		...
	]
 *
 */
module.exports.getParallelSchedules = async reportJobSchedules => {
	logger.debug(NON_RK_LOGGER, '[non-rk-ad-provider.service :: getParallelSchedules] 호출됨');

	let parallelSchedules = [];

	await Promise.all(reportJobSchedules.map(async reportJobSchedule => {
		// adProvider_id, publisher_id에 해당하는 AdProviderInfo 가져오기
		/* adProviderInfo = { // google example
			adProvider_id, publisher_id, adProviderInfo_id, 
			reportInfos: { NETWORK_CODE, CLIENT_EMAIL, PRIVATE_KEY },
		} */
		const {
			reportInfos = {}
		} = await _getAdProviderInfo(reportJobSchedule.adProvider_id, reportJobSchedule.publisher_id);

		const index = _.findIndex(parallelSchedules, reportInfos);

		if (index === -1) {
			const schedule = Object.assign(reportInfos, {
				reportJobSchedules: [ reportJobSchedule ]
			});
			parallelSchedules.push(schedule);
		} else {
			parallelSchedules[index].reportJobSchedules.push(reportJobSchedule);
		}
	}));

	return parallelSchedules;
};


/**
 * [PROCESS 5:: TASK 1] getNonRkAdProviderMetaInfo : AdProvider-AdProviderInfos 메타 정보 가져오기
 *  TASK 1-0. adProvider_id에 해당하는 AdProvider 추출
 *  TASK 1-1. adProvider_id, publisher_id에 해당하는 AdProviderInfo 가져오기
 *  TASK 1-2. adProviderPlaceKey별(placeInfos 파싱결과)로 adProviderPlace_ids, adUnitIds 정보 가져오기
 *  TASK 1-3. placeInfos 가져오기
 *  TASK 1-4. 통화 및 환율 정보 셋팅
 *  TASK 1-5. period 정보 셋팅
 *  TASK 1-6. 리포트 연동에 필요한 부가 정보 셋팅
 *  TASK 1-7. 국가코드 매핑 정보 셋팅
 *  TASK 1-8. GFP 수수료 정보 가져오기
 *  TASK 1-9. 메타 정보 셋팅
 * 
 * @param {Object} reportJobSchedule { ymd, adProviderType, reportApiType, adProvider_id, publisher_id, period, apiResultPath, sourcePath }
 * @param {Object} adProviderConfig Ad Provider별 config 
 * { 
		api_result, 
		file_info: { separator },
		ext: { dimensions, columns, version, scopes, fields } // google의 경우
 * }
 * @return {Object} adProviderMetaInfo
 * GOOGLE example = {
		ymd: '20181119',
		adProviderType: 'OUTSIDE',
		reportApiType: 'GOOGLE',
		adProvider_id: 5bdf9aab77bd856e482a84d4,
		publisher_id: 5bcd6979868338bd69d9df9f,
		adProviderInfo_id: 5bdf9b681194b4c411b4d6ab,
		api_result: 'LOCAL_FILE',
		chunk_size: 500000,
		exchangeRate: { endDate: '20181118', startDate: '20181020', from: 'USD', to: 'KRW', rate: '1131.7600000000004' },
		period: { startDate: moment("2018-09-18T00:00:00.000"), endDate: moment("2018-11-19T00:00:00.000") },
		sourcePath: '/user/gfp-data/silvergrey/source/2025/04/02/google/5c2436b438a0a3c9ea911556_5d11dc1a34480e001d31fb26_20250401_20250401_00.csv',
		placeKeyTemplates: ['AD_UNIT_CODE'],
		reportInfos: { 
			NETWORK_CODE:'21755352505', 
			CLIENT_EMAIL:'<EMAIL>',
			PRIVATE_KEY:'~~~~', 
		},
		reportInfoExt: { 
			apiResultPath: 'local_download/reportapi/google/2025/04/5c2436b438a0a3c9ea911556_5d11dc1a34480e001d31fb26_20250401_20250401_00.csv',
			dimensions: ['DATE', 'AD_UNIT_ID', 'AD_UNIT_CODE', 'COUNTRY_NAME', 'MOBILE_DEVICE_NAME', 'CREATIVE_SIZE'],
			columns: ['TOTAL_INVENTORY_LEVEL_IMPRESSIONS', 'TOTAL_INVENTORY_LEVEL_CLICKS', 'TOTAL_INVENTORY_LEVEL_ALL_REVENUE'],
			dimensionAttributes: [ 'AD_UNIT_CODE' ],
			fields: {
				place_keys: { AD_UNIT_CODE: 'DimensionAttribute.AD_UNIT_CODE' },
				ymd: 'Dimension.DATE',
				country: 'Dimension.COUNTRY_NAME',
				os: 'Dimension.MOBILE_DEVICE_NAME',
				size: 'Dimension.CREATIVE_SIZE',
				imp: 'Column.TOTAL_INVENTORY_LEVEL_IMPRESSIONS',
				clk: 'Column.TOTAL_INVENTORY_LEVEL_CLICKS',
				net_revenue: 'Column.TOTAL_INVENTORY_LEVEL_ALL_REVENUE',
			},
			version: 'v201808',
			scopes: ['https://www.googleapis.com/auth/dfp']
		},
		reportApiKeyValueSplitKey: 'contentId',
		placeInfos:	[{
			adProviderPlace_id: ObjectId('5be12d36edd9ca7f92054167'),
			placeInfos: { placementId: 'aaaaa', appId: 'aaaaa' },
		}],
		placeExt: [{
			adProviderPlaceKey:'AD_UNIT_CODE:adUnitCode1'
			adProviderPlace_ids:[ObjectId('5be12d36edd9ca7f92054167'),ObjectId('5be12d36edd9ca7f92054166')],
			adUnitIds:['dfp_adunit_aos2','dfp_adunit_ios2'],
			adProviderInfo_id:'5bdf9b681194b4c411b4d6ab',
		}],
		countryMappings: {
			Thailand: 'TH',
			Indonesia: 'ID',
			Taiwan: 'TW',
			'United States': 'US',
			'United Kingdom': 'GB',
			Canada: 'CA',
			'South Korea': 'KR',
			...
		},
		gfpFeeRate: [{
			adProvider_id: 5bdf9aab77bd856e482a84d4,
			publisher_id: 5bcd6979868338bd69d9df9f,
			date: '20230330',
			feeRate: 0.2
		}, ... ]
	}
 */
module.exports.getNonRkAdProviderMetaInfo = async ({ ymd, adProviderType, reportApiType, period: { startDate, endDate }, adProvider_id, publisher_id, apiResultPath, sourcePath }, { api_result, file_info: { separator }, ext }) => {
	logger.debug(NON_RK_LOGGER, '[non-rk-ad-provider.service :: getNonRkAdProviderMetaInfo] 호출됨');


	// [PROCESS 5:: TASK 1-0] adProvider_id에 해당하는 AdProvider 추출
	// adProvider = { currency, placeKeyTemplates }
	const { 
		currency, placeKeyTemplates
	} = await _getAdProvider(adProvider_id);


	// [PROCESS 5:: TASK 1-1] adProvider_id, publisher_id에 해당하는 AdProviderInfo 가져오기
	/* adProviderInfo = { // google example
		adProvider_id, publisher_id, adProviderInfo_id, 
		reportInfos: { NETWORK_CODE, CLIENT_EMAIL, PRIVATE_KEY },
		reportApiKeyValueSplitKey,
	} */
	const {
		adProviderInfo_id, reportInfos, reportApiKeyValueSplitKey
	} = await _getAdProviderInfo(adProvider_id, publisher_id);


	// [PROCESS 5:: TASK 1-2] adProviderPlaceKey별 adProviderPlace_ids, adUnitIds 정보 가져오기
	/* placeExt = { // google example
		adProviderInfo_id, adProviderPlaceKey, adProviderPlace_ids, adUnitIds
	} */
	const placeExt = await _getPlaceExt(adProviderInfo_id);


	// [PROCESS 5:: TASK 1-3] placeInfos 가져오기
	const placeInfos = await _getPlaceInfos(adProviderInfo_id);


	/* 
		[PROCESS 5:: TASK 1-4] 통화 및 환율 정보 셋팅 
	*/

	// 통화 정보
	const currencyFrom = currency;
	const currencyTo = (_.upperCase(currencyFrom) === CURRENCY.KRW.code) ? CURRENCY.USD.code : CURRENCY.KRW.code;

	// 평균 환율 정보 
	const yesterday = moment().add(-1, 'days').format('YYYYMMDD'); // 어제 날짜
	const before30days = moment(yesterday).add(-29, 'days').format('YYYYMMDD'); // 30일 전 날짜
	let exchangeRate = await exchangeRateService.getExchangeRateAverage(before30days, yesterday, currencyFrom, currencyTo);


	// [PROCESS 5:: TASK 1-5] period 정보 셋팅
	let period = {
		startDate: moment(startDate), 
		endDate: moment(endDate) // 기간 데이터인 경우 오늘 날짜까지, 아닌 경우 시작일과 동일
	};


	/* 
		[PROCESS 5:: TASK 1-6] 리포트 연동에 필요한 부가 정보 셋팅
	*/

	// 리포트 연동 부가 정보
	let reportInfoExt = {};
	reportInfoExt.apiResultPath = apiResultPath;
	reportInfoExt.separator = separator;

	for(const key in ext) {
		reportInfoExt[key] = ext[key];
	}


	// [PROCESS 5:: TASK 1-7] 국가코드 매핑 정보 가져오기
	const countryMappings = await _getCountryMappings();


	// [PROCESS 5:: TASK 1-8] GFP 수수료 정보 가져오기
	const gfpFeeRate = await _getGfpFeeRate({ adProvider_id, publisher_id, period: { startDate, endDate } });


	// [PROCESS 5:: TASK 1-9] 메타 정보 셋팅
	const adProviderMetaInfo = {
		ymd, adProviderType, reportApiType,
		adProvider_id, publisher_id,
		adProviderInfo_id,

		/* api 결과 타입정보 (LOCAL_FILE, HDFS) */
		api_result,

		/* 리포트 데이터 처리 단위 */
		chunk_size: config.report_api_chunk_size,

		/* 평균 환율 정보 { from, to, rate } */
		exchangeRate,

		/*
			startDate: 오늘 기준으로 몇 달 전(GOOGLE:2달전)
			endDate: 오늘
		*/
		period,

		/* HDFS 원본 파일 경로 */
		sourcePath,

		/* PlaceKey 정보 */
		placeKeyTemplates,

		/* 리포트 연동에 필요한 정보 */
		reportInfos,
		reportInfoExt,
		reportApiKeyValueSplitKey,

		/* placeInfos 정보 */
		placeInfos,

		/* place 부가정보 (adProviderPlaceKey, adProviderPlace_ids, adUnitIds) */
		placeExt,

		/* 국가코드 매핑 정보 */
		countryMappings,

		/* GFP 수수료 정보 */
		gfpFeeRate,
	};

	return adProviderMetaInfo;
};


/**
 * [PROCESS 5:: TASK 1-0] _getAdProvider : adProvider_id에 해당하는 AdProvider 추출
 *
 * @param {String} _id adProvider_id 
 * @return {Object} adProvider
 * GOOGLE example = { 
		adProvider_id:'5bdf9aab77bd856e482a84d4',
		currency:'USD',
		placeKeyTemplates:['AD_UNIT_CODE']
 * 	}
 */
const _getAdProvider = async _id => {
	// logger.debug(NON_RK_LOGGER, '[non-rk-ad-provider.service :: _getAdProvider] 호출됨');

	let adProvider = await AdProvider.aggregate()
		.match({ _id })
		.project({
			_id: 0,
			adProvider_id:'$_id',
			currency:1,
			placeKeyTemplates: {
				$filter: {
					input: '$placeTemplates',
					as: 'placeTemplate',
					cond: { $eq: [ '$$placeTemplate.placeKey', 1 ] }
				}
			}
		})
		.unwind('placeKeyTemplates')
		.group({ 
			_id: { 
				adProvider_id: '$adProvider_id', 
				currency: '$currency', 
			}, 
			placeKeyTemplates: { $push: '$placeKeyTemplates.key' }
		})
		.project({
			_id:0, 
			adProvider_id:'$_id.adProvider_id',
			currency:'$_id.currency',
			placeKeyTemplates:1,
		})
		.exec();


	// [ERROR] adProvider_id에 해당하는 AdProvider가 없는 경우, 에러 처리
	// 	- 만약 AdProvider의 placeKeyTemplates에 placeKey:1인 게 하나도 없을 경우에도 에러 처리
	if(_.isNil(adProvider) || _.isEmpty(adProvider)) {
		throw new BusinessError({ message: `[non-rk-ad-provider.service :: _getAdProvider] AdProvider No Result ( adProvider_id= ${_id} )` });
	}

	adProvider = adProvider.pop();

	return adProvider;
};


/**
 * [PROCESS 5:: TASK 1-1] _getAdProviderInfo : adProvider_id, publisher_id에 해당하는 AdProviderInfo 가져오기
 *		reportApiStatus 가 'ON' 이고, reportStat이 'NOTYET' 이 아닌 애들만 스케쥴로 등록되므로 별도 체크 안 함
 * @param {String} adProvider_id
 * @param {String} publisher_id
 * @return {Object} adProviderInfo
 * GOOGLE example = { 
		adProviderInfo_id':'5bdf9b681194b4c411b4d6ab',
		publisher_id:'5bcd6979868338bd69d9df9f',
		adProvider_id:'5bdf9aab77bd856e482a84d4',
		reportInfos: { NETWORK_CODE, CLIENT_EMAIL, PRIVATE_KEY }, 
		reportApiKeyValueSplitKey: 'contentId',
 * 	}
 */
const _getAdProviderInfo = async (adProvider_id, publisher_id) => {
	// logger.debug(NON_RK_LOGGER, '[non-rk-ad-provider.service :: _getAdProviderInfo] 호출됨');

	/* reportApiKeyValueSplitKey 적용 시, 주석 해제 */
	// let adProviderInfo = await AdProviderInfo.aggregate()
	// 	.match({ adProvider_id, publisher_id })
	// 	.project({
	// 		_id:0,
	// 		adProviderInfo_id: '$_id',
	// 		adProvider_id: 1,
	// 		publisher_id: 1,
	// 		reportInfos: { $mergeObjects: '$reportInfos' },
	// 		reportApiKeyValueSplitKey: 1
	// 	})
	// 	.exec();


	/**********************************************************************************/
	/* [FADEOUT] 9월초 */
	let adProviderInfo = await AdProviderInfo.aggregate()
		.match({ adProvider_id, publisher_id })
		.project({
			_id:0,
			adProviderInfo_id: '$_id',
			adProvider_id: 1,
			publisher_id: 1,
			reportInfos: { $mergeObjects: '$reportInfos' },
			reportApiKeyGroup_id: 1
		})
		.exec();
	/**********************************************************************************/


	// [ERROR] adProvider_id에 해당하는 adProviderInfo가 없는 경우, 에러 처리
	if(_.isNil(adProviderInfo) || _.isEmpty(adProviderInfo)) {
		throw new BusinessError({ message: `[non-rk-ad-provider.service :: _getAdProviderInfo] AdProviderInfo No Result ( adProvider_id= ${adProvider_id}, publisher_id= ${publisher_id} )` });
	}

	adProviderInfo = adProviderInfo.pop();


	/**********************************************************************************/
	/* [FADEOUT] 9월초 */
	if (!_.isNil(adProviderInfo.reportApiKeyGroup_id)) {
		let reportApiKeyGroup = await Publisher.aggregate([
			{ $match: { _id: publisher_id } },
			{ $project: {
				_id:0,
				reportApiKeyGroup: {
					$filter: {
						input: '$keyGroups',
						as: 'keyGroup',
						cond: { $eq: [ '$$keyGroup._id', adProviderInfo.reportApiKeyGroup_id ] }
					}
				}
			}},
			{ $unwind: { path: '$reportApiKeyGroup' }},
			{ $project: {
				reportApiKeyGroup_id: '$reportApiKeyGroup._id',
				name: '$reportApiKeyGroup.name',
				types: '$reportApiKeyGroup.types',
				keys: '$reportApiKeyGroup.keys',
			}},
		]);

		// [ERROR] Publisher에 reportApiKeyGroup_id에 해당하는 keyGroup 정보가 없는 경우, 에러 처리
		if(_.isNil(reportApiKeyGroup) || _.isEmpty(reportApiKeyGroup)) {
			throw new BusinessError({ message: `[non-rk-ad-provider.service :: _getAdProviderInfo] Publisher keyGroup No Result ( publisher_id= ${publisher_id}, reportApiKeyGroup_id= ${adProviderInfo.reportApiKeyGroup_id} )` });
		}

		reportApiKeyGroup = reportApiKeyGroup.pop();
		adProviderInfo.reportApiKeyValueSplitKey = reportApiKeyGroup.keys[0];
	}
	/**********************************************************************************/


	return adProviderInfo;
};


/**
 * [PROCESS 5:: TASK 1-2] _getPlaceExt : adProviderPlaceKey별 adProviderPlace_ids, adUnitIds 정보 가져오기
 * 		- Place - AdUnit 매핑
 * 		- Place - BiddingGroup - AdUnit 매핑
 * 
 * @param {ObjectId} adProviderInfo_id
 * @return {Array} placeExt
	[{
		adProviderInfo_id: '5bdf9b681194b4c411b4d6ab',
		adProviderPlaceKey: 'AD_UNIT_CODE:***********',
		adProviderPlace_ids: [ObjectId('5be12d36edd9ca7f92054167'), ObjectId('5be12d36edd9ca7f92054166')],
		adUnitIds: ['dfp_adunit_aos2', 'dfp_adunit_ios2'],
	},
	{
		adProviderInfo_id: '5bdf9b681194b4c411b4d6ab',
		adProviderPlaceKey: 'AD_UNIT_CODE:21753058070',
		adProviderPlace_ids: [ObjectId('5bdfa58dda1c94d004d05e2b'), ObjectId('5bdfa58dda1c94d004d05e2a')],
		adUnitIds: ['dfp_adunit_ios2', 'dfp_adunit_ios', 'dfp_adunit_aos2', 'dfp_adunit_aos'],
	}]
 */
const _getPlaceExt = async (adProviderInfo_id) => {
	// logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _getPlaceExt] adProviderPlace_ids, adUnitIds 정보 가져오기`);

	let placeExt = await Place.aggregate()
		.match({ adProviderInfo_id })
		.project({
			_id: 1,
			adProviderInfo_id: 1, 
			placeKey: 1,
		})
		.lookup({ // Place와 매핑된 MappingHistory 가져오기
			from: 'MappingHistory',
			localField: '_id',
			foreignField: 'adProviderPlace_id',
			as: 'mappingHistorys'
		})
		.project({ 
			adProviderPlace_id : '$_id',
			adProviderInfo_id:1,
			adProviderPlaceKey: '$placeKey',
			adUnitIds: {
				$map: {
					'input': '$mappingHistorys',
					as: 'mappingHistory',
					in: '$$mappingHistory.adUnitId'
				}
			}
		})
		.group({
			_id : {
				adProviderInfo_id: '$adProviderInfo_id',
				adProviderPlaceKey: '$adProviderPlaceKey'
			},
			adProviderPlace_ids: { $addToSet: '$_id' },
			adUnitIds: { $addToSet: '$adUnitIds' }
		})
		.project({ 
			_id : 0,
			adProviderInfo_id: '$_id.adProviderInfo_id',
			adProviderPlaceKey: '$_id.adProviderPlaceKey',
			adProviderPlace_ids:1,
			adUnitIds: { // flatten 처리
				$reduce: {
					input: "$adUnitIds",
					initialValue: [],
					in: { $setUnion : ["$$value", "$$this"] }
				}
			}
		})
		.exec();

	return placeExt;
};


/**
 * [PROCESS 5:: TASK 1-3] _getPlaceInfos : placeInfos 가져오기
 * 
 * @param {ObjectId} adProviderInfo_id
 * @return {Array} placeInfos
	[{
		adProviderPlace_id: ObjectId('5be12d36edd9ca7f92054167'),
		placeInfos: { placementId: 'aaaaa', appId: 'aaaaa' },
	},
	{
		adProviderPlace_id: ObjectId('5bdfa58dda1c94d004d05e2a'),
		placeInfos: { placementId: 'bbbb', appId: 'bbbb' },
	}]
 */
const _getPlaceInfos = async adProviderInfo_id => {
	// logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _getPlaceInfos] placeInfos 가져오기`);

	let placeInfos = await Place.aggregate()
		.match({ adProviderInfo_id })
		.project({
			_id: 0,
			adProviderPlace_id: '$_id',
			placeInfos: { $mergeObjects: '$placeInfos' },
		})
		.exec();

	return placeInfos;
};


/**
 * [PROCESS 5:: TASK 1-7] _getCountryMappings : 국가코드 매핑 정보 가져오기
 *
 * @return {Object} countryMappings
	{ 
		Thailand: 'TH',
		Indonesia: 'ID',
		Taiwan: 'TW',
		'United States': 'US',
		'United Kingdom': 'GB',
		Canada: 'CA',
		'South Korea': 'KR',
		...
	}
 */
const _getCountryMappings = async () => {
	// logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _getCountryMappings] 호출됨`);

	let countryMappings = {};

	const countryInfos = await CountryMappings.find().select('-_id').exec();

	countryInfos.map(({ countryCd, countryNm }) => {
		countryMappings[countryNm] = countryCd;
	});

	return countryMappings;
};


/**
 * [PROCESS 5:: TASK 1-8] _getGfpFeeRate : GFP 수수료 정보 가져오기
 *
 * @param {Object} { adProvider_id, publisher_id, period: { startDate, endDate } }
 * @return {Array} gfpFeeRate
	[{
		adProvider_id: 5bdf9aab77bd856e482a84d4,
		publisher_id: 5bcd6979868338bd69d9df9f,
		date: '20230330',
		feeRate: 0.2
	}, ... ]
 */
const _getGfpFeeRate = async ({ adProvider_id, publisher_id, period: { startDate, endDate } }) => {
	// logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _getGfpFeeRate] 호출됨`);

	let gfpFeeRate = await GfpFeeRate.aggregate()
		.match({ adProvider_id, publisher_id, date: { $gte: startDate, $lte: endDate } })
		.project({ _id: 0, adProvider_id: 1, publisher_id: 1, date: 1, feeRate: 1 })

	// Decimal128 타입인 수수료율을 float 타입으로 변환
	gfpFeeRate = gfpFeeRate.map(data => {
		data.feeRate = parseFloat(data.feeRate);
		return data;
	});

	return gfpFeeRate;
};


/*********************************************************************************************************************/


/**
 * [PROCESS 5:: TASK 2-1] openFileSync : File Sync 열기
 * 
 * @param {Object} { reportInfoExt: { apiResultPath } }
 * @return {Object} fda
 */
module.exports.openFileSync = async ({ reportInfoExt: { apiResultPath } }) => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: openFileSync] open File Sync ::: apiResultPath= ${apiResultPath}`);

	// [ERROR] apiResultPath 정보가 없으면, 에러 처리
	if(_.isNil(apiResultPath)) {
		throw new BusinessError({ message: `[non-rk-ad-provider.service :: openFileSync] apiResultPath 정보 없음` });
	}

	// 파일 디렉토리가 존재하지 않으면, 생성
	// apiResultPath = local_download/reportapi/app_nexus/2023/12/5d8ac41f0fe0f652a82f608d_5d11dc1a34480e001d31fb26_20231201_20231201_14.csv
	// parsed = { root: '', dir: 'local_download/reportapi/app_nexus/2023/12', base: '5d8ac41f0fe0f652a82f608d_5d11dc1a34480e001d31fb26_20231201_20231201_14.csv', ext: '.csv', name: '5d8ac41f0fe0f652a82f608d_5d11dc1a34480e001d31fb26_20231201_20231201_14' }
	const parsed = path.parse(apiResultPath);
	file.mkdirIfNotExist(parsed.dir);

	// 파일이 존재하면
	if(fs.existsSync(apiResultPath)) {
		// 파일명을 변경하고 작업 시작
		fs.renameSync(apiResultPath, apiResultPath + moment().format('YYYYMMDDHHmmss') + '.tmp');
	}

	// 파일 열기
	const fda = fs.openSync(apiResultPath, 'a+'); // append mode

	return fda;
};


/**
 * [PROCESS 5:: TASK 2-3] closeFileSync : FileSync 닫기
 * 
 * @param {Object} fda
 * @return null
 */
module.exports.closeFileSync = async fda => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: closeFileSync] close File Sync`);

	// 파일 닫기
	if (!_.isNil(fda)) {
		fs.closeSync(fda);
	}

	return null;
};


/**
 * [PROCESS 5:: TASK 2-4] uploadToHdfs : AP 원본 파일 LOCAL -> HDFS 로 업로드
 *
 * @param {Object} { sourcePath, reportInfoExt: { apiResultPath } }
 */
module.exports.uploadToHdfs = async ({ sourcePath, reportInfoExt: { apiResultPath } }) => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: uploadToHdfs] 호출됨 ( apiResultPath= ${apiResultPath}, sourcePath= ${sourcePath} ) `);

	try {
		// HDFS source 디렉토리 생성
		const sourceDir = path.parse(sourcePath).dir;
		await c3HdfsApi.mkdir(sourceDir);

		// apiResultPath = local_download/reportapi/google/2025/04/5c2436b438a0a3c9ea911556_5d11dc1a34480e001d31fb26_20250401_20250401_00.csv
		// sourcePath = /user/gfp-data/silvergrey/source/2025/04/02/google/5c2436b438a0a3c9ea911556_5d11dc1a34480e001d31fb26_20250401_20250401_00.csv
		await c3HdfsCli.upload(apiResultPath, sourcePath);

		// 파일 크기 검증 ( LOCAL VS HDFS )
		await _validateFileSize(apiResultPath, sourcePath);
	} catch (err) {
		throw err;
	}
};


/**
 * 파일 크기 검증 ( LOCAL vs HDFS )
 */
const _validateFileSize = async (localReportFile, hdfsReportFile) => {
	// LOCAL 파일 사이즈
	const localFileSize = fs.statSync(localReportFile).size.toString();

	// HDFS 파일 사이즈
	const status = await c3HdfsApi.stat(hdfsReportFile);
	const hdfsFileSize = status.length.toString();

	// 파일 크기가 같지 않은 경우, Error
	if (localFileSize !== hdfsFileSize) {
		throw new BusinessError({ message: `LOCAL vs HDFS 파일 사이즈 불일치 ( localFileSize= ${localFileSize}, hdfsFileSize= ${hdfsFileSize}, localReportFile= ${localReportFile}, hdfsReportFile= ${hdfsReportFile} )` });
	}
};


/**
 * [PROCESS 5:: TASK 3] processAdProviderNonRkStatHdfs : 리포트 파일 가공 및 HDFS 저장
 *  TASK 3-0. 파일 총 라인수 가져오기 & 데이터 날짜 리스트 구하기 ( startDate ~ endDate )
 *  TASK 3-1. 리포트 파일 정보를 chunk_size 단위로 끊어서 BatchReportProcessingResults DB에 로깅
 *  TASK 3-2. HDFS Intermediate & LOCAL 디렉토리 삭제
 *  TASK 3-3. HDFS 디렉토리 삭제 후, 종료 처리 ( chunk 가 없는 경우 )
 *  TASK 3-4. HDFS Intermediate 디렉토리 생성
 *  TASK 3-5. 파케이 파일 생성에 필요한 파일 정보 셋팅
 *  TASK 3-6. 남은 CPU 개수만큼 ChildProcess 생성하여 병렬 처리하기
 *  TASK 3-7. publisherId 하위에 _SUCCESS 파일 추가하기
 *  TASK 3-8. HDFS Intermediate 경로를 원본 경로로 변경하기
 *
 * @param {Object} adProviderMetaInfo
 * { 
		ymd, reportApiType, adProvider_id, publisher_id, 
		chunk_size, exchangeRate,
		period: { startDate, endDate }, 
		placeKeyTemplates, 
		reportInfoExt: { apiResultPath, separator, fields }, 
		placeExt,
		gfpFeeRate
 * }
*/
module.exports.processAdProviderNonRkStatHdfs = async ({ ymd, reportApiType, adProvider_id, publisher_id, chunk_size, exchangeRate, period: { startDate, endDate }, placeKeyTemplates, reportInfoExt: { apiResultPath, separator, fields }, placeExt, gfpFeeRate }) => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: processAdProviderNonRkStatHdfs] 리포트 파일 처리 시작`);
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: processAdProviderNonRkStatHdfs] api 결과 경로 ${apiResultPath}`);


	// [PROCESS 5:: TASK 3-0] 파일 총 라인수 가져오기 & 데이터 날짜 리스트 구하기 ( startDate ~ endDate )
	const totalLineCount = await _getFileTotalLineCount(apiResultPath);
	const dateList = _getDateList(startDate, endDate);

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: processAdProviderNonRkStatHdfs] totalLineCount :: ${totalLineCount}`);


	// [PROCESS 5:: TASK 3-1] 리포트 파일 정보를 chunk_size 단위로 끊어서 BatchReportProcessingResults DB에 로깅
	/* chunkInfos = [{ chunkNumber, startLine, endLine, state }] */
	const { reportProcessingResult_id, chunkInfos } = await batchReportResultService.upsertBatchReportProcessingResults({ totalLineCount, chunk_size, ymd, adProvider_id, publisher_id, reportApiType, apiResultPath, type: 'HDFS' });


	// [PROCESS 5:: TASK 3-2] HDFS Intermediate & LOCAL 디렉토리 삭제
	await _deleteDir({ reportProcessingResult_id, ymd, reportApiType });


	// [PROCESS 5:: TASK 3-3] HDFS 디렉토리 삭제 후, 종료 처리 ( chunk 가 없는 경우 )
	if (_.isEmpty(chunkInfos)) {
		await _deleteHdfsDir({ publisher_id, adProvider_id, dateList });

		logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: processAdProviderNonRkStatHdfs] 처리할 chunk 정보가 없으므로 종료 처리함`);

		return;
	}


	// [PROCESS 5:: TASK 3-4] HDFS Intermediate 디렉토리 생성
	await _makeHdfsIntermediateDir(reportProcessingResult_id);


	// [PROCESS 5:: TASK 3-5] 파케이 파일 생성에 필요한 파일 정보 셋팅
	const parquetFileInfo = await _getParquetFileInfo({ reportProcessingResult_id, ymd, reportApiType, publisher_id, adProvider_id, dateList });


	// [PROCESS 5:: TASK 3-6] 남은 CPU 개수만큼 ChildProcess 생성하여 병렬 처리하기
	const childProcessPath = '/src/nonRkHdfsApp.js';
	await _processParallelLimit({ reportProcessingResult_id, chunkInfos, adProvider_id, publisher_id, exchangeRate, placeKeyTemplates, placeExt, gfpFeeRate, apiResultPath, separator, fields, parquetFileInfo }, childProcessPath);


	// [PROCESS 5:: TASK 3-7] publisherId 하위에 _SUCCESS 파일 추가하기
	await _createHdfsSuccessFile({ reportProcessingResult_id, adProvider_id, publisher_id, dateList });


	// [PROCESS 5:: TASK 3-8] HDFS Intermediate 경로를 원본 경로로 복사하기
	await _copyHdfsDir({ reportProcessingResult_id, adProvider_id, publisher_id, dateList });


	// [PROCESS 5:: TASK 3-9] HDFS Intermediate & LOCAL 디렉토리 삭제
	await _deleteDir({ reportProcessingResult_id, ymd, reportApiType });


	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: processAdProviderNonRkStatHdfs] 리포트 파일 처리 완료`);
};


/**
 * [PROCESS 5:: TASK 3-0-1] _getFileTotalLineCount : 파일 총 라인수 가져오기
 * 
 * @param {String} apiResultPath api 결과 경로
 * @return {Number} totalLineCount
 */
const _getFileTotalLineCount = apiResultPath => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _getFileTotalLineCount] 호출됨`);

	return new Promise((resolve, reject) => {
		const NEW_LINE = '\n'.charCodeAt(0);
	
		let index = 0;
		let last_chunk = '';
		let totalLineCount = -1; // 헤더 정보 제외
		const fileStream = fs.createReadStream(apiResultPath);

		fileStream.on('data', chunk => {
			try {
				last_chunk = chunk[chunk.length - 1];

				for (index = 0; index < chunk.length; ++index) {
					if (chunk[index] === NEW_LINE) totalLineCount++;
				}
			} catch (err) {
				reject({ message: `[non-rk-ad-provider.service :: _getFileTotalLineCount] fileStream data 에러`, err });
			}
		})
		.on('end', () => {
			// 파일 끝에 개행문자가 없는 경우, 1건 추가
			if(last_chunk !== NEW_LINE) {
				totalLineCount++;
			}

			resolve(totalLineCount);
		})
		.on('error', err => {
			reject({ message: `[non-rk-ad-provider.service :: _getFileTotalLineCount] fileStream 에러`, err });

			if (fileStream) fileStream.close();
		});
	});
};


/**
 * [PROCESS 5:: TASK 3-0-1] _getDateList : 시작일-종료일 간 날짜 리스트 가져오기
 *
 * @param {Date} startDate
 * @param {Date} endDate
 * @return {Array} dateList 날짜 리스트 ['20190101', '20190102', ... ]
 */
const _getDateList = (startDate, endDate) => {
	// 날짜 리스트
	const dateList = [];

	// 날짜 리스트 생성(시작일 ~ 종료일)
	let tempDate = moment(startDate);
	while (tempDate.isSameOrBefore(endDate)) {
		dateList.push(tempDate.format('YYYYMMDD'));
		tempDate.add(1, 'days');
	}

	return dateList;
};


/**
 * [PROCESS 5:: TASK 3-2] _deleteDir : HDFS Intermediate & LOCAL 디렉토리 삭제
 *
 * @param {Object} { reportProcessingResult_id, ymd, reportApiType } }
 */
const _deleteDir = async ({ reportProcessingResult_id, ymd, reportApiType }) => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _deleteDir] HDFS Intermediate & LOCAL 경로 삭제 시작`);

	// HDFS Intermediate 디렉토리 삭제
	await _deleteHdfsIntermediateDir({ reportProcessingResult_id });

	// LOCAL 디렉토리 삭제
	await _deleteLocalDir({ reportProcessingResult_id, ymd, reportApiType });

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _deleteDir] HDFS Intermediate & LOCAL 경로 삭제 완료`);
};


/**
 * _deleteHdfsIntermediateDir : HDFS Intermediate 디렉토리 삭제
 * 
 * @param {Object} { reportProcessingResult_id }
 */
const _deleteHdfsIntermediateDir = async ({ reportProcessingResult_id }) => {
	// intermediate_nonrk_dir = /user/gfp-data/silvergrey/intermediate/nonrk
	// hdfsDeleteDir = /user/gfp-data/silvergrey/intermediate/nonrk/processingResultId=xxx(/yyyy/mm/dd/adProviderId=xxx/publisherId=xxx)
	const hdfsDeleteDir = path.join(SILVERGREY_HDFS_INTERMEDIATE_NONRK_DIR, `processingResultId=${reportProcessingResult_id}`);

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _deleteHdfsIntermediateDir] hdfsDeleteDir= ${hdfsDeleteDir}`);

	// HDFS Intermediate 디렉토리 삭제
	if (await c3HdfsApi.exists(hdfsDeleteDir)) {
		await c3HdfsApi.delete(hdfsDeleteDir, true);
	}

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _deleteHdfsIntermediateDir] HDFS Intermediate 디렉토리 삭제 완료 ( hdfsDeleteDir= ${hdfsDeleteDir} )`);
};


/**
 * _deleteLocalDir : LOCAL 디렉토리 삭제
 *
 * @param {Object} { reportProcessingResult_id, ymd, reportApiType }
 */
const _deleteLocalDir = async ({ reportProcessingResult_id, ymd, reportApiType }) => {
	// 배치 처리 기준 날짜
	const ymdDate = moment(ymd, 'YYYYMMDD');

	// localDeleteDir= local_download/reportapi/app_nexus/2023/07/processingResultId=xxx(/hdfs/chunkNumber/yyyy/mm/dd/adProviderId=xxx/publisherId=xxx)
	const localDeleteDir = path.join(LOCAL_ROOT, REPORT_API_PATH, reportApiType.toLowerCase(), ymdDate.format('YYYY'), ymdDate.format('MM'), `processingResultId=${reportProcessingResult_id}`);

	if (fs.existsSync(localDeleteDir)) {
		// 디렉토리 & 파일 삭제
		file.deleteDirectoryRecursively(localDeleteDir, true);
	}

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _deleteLocalDir] LOCAL 디렉토리 삭제 완료 ( localDeleteDir= ${localDeleteDir} )`);
};


/**
 * [PROCESS 5:: TASK 3-3] _deleteHdfsDir : HDFS 일괄 삭제 처리
 *
 * @param {Object} { publisher_id, adProvider_id, dateList }
 * @return {Array} monthPathList
 */
const _deleteHdfsDir = async ({ publisher_id, adProvider_id, dateList }) => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _deleteHdfsDir] HDFS 디렉토리 삭제 ( adProvider_id= ${adProvider_id}, publisher_id= ${publisher_id}, dateList= ${dateList[0]}~${dateList[dateList.length - 1]} )`);

	// commonPath = /{{YYYY}}/{{MM}}/{{DD}}/adProviderId=xxx/publisherId=xxx
	const commonPath = SILVERGREY_HDFS_COMMON_PATH.replace(/{{adProviderId}}/g, adProvider_id).replace(/{{publisherId}}/g, publisher_id);

	const monthPathSet = new Set();

	const chunkSize = (dateList.length > 5) ? 5 : dateList.length;
	for (let i = 0; i < dateList.length; i += chunkSize) {
		const chunkList = dateList.slice(i, i + chunkSize);
		await Promise.all(chunkList.map(async dt => {
			const date = moment(dt);

			monthPathSet.add(date.format('YYYY/MM'));

			// nonrk_dir = /user/gfp-data/silvergrey/nonrk
			// hdfsDeleteDir = /user/gfp-data/silvergrey/nonrk/2022/05/23/adProviderId=5b74d94bc36eef272090ca52/publisherId=5e705e9054e342001d6ed95a
			const hdfsDeleteDir = path.join(SILVERGREY_HDFS_NONRK_DIR, commonPath.replace(/{{YYYY}}/g, date.format('YYYY')).replace(/{{MM}}/g, date.format('MM')).replace(/{{DD}}/g, date.format('DD')));

			if (await c3HdfsApi.exists(hdfsDeleteDir)) {
				await c3HdfsApi.delete(hdfsDeleteDir, true);
			}
		}));
	}

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _deleteHdfsDir] HDFS 디렉토리 삭제 완료 ( adProvider_id= ${adProvider_id}, publisher_id= ${publisher_id}, dateList= ${dateList[0]}~${dateList[dateList.length - 1]} )`);

	// monthPathList = ['2024/08', '2024/07', '2024/06']
	const monthPathList = Array.from(monthPathSet).sort().reverse();

	return monthPathList;
};


/**
 * [PROCESS 5:: TASK 3-4] _makeHdfsIntermediateDir : HDFS Intermediate 디렉토리 생성
 *
 * @param {ObjectId} reportProcessingResult_id
 */
const _makeHdfsIntermediateDir = async reportProcessingResult_id => {
	// intermediate_nonrk_dir = /user/gfp-data/silvergrey/intermediate/nonrk
	// hdfsTargetDir = /user/gfp-data/silvergrey/intermediate/nonrk/processingResultId=xxx(/yyyy/mm/dd/adProviderId=xxx/publisherId=xxx)
	const hdfsTargetDir = path.join(SILVERGREY_HDFS_INTERMEDIATE_NONRK_DIR, `processingResultId=${reportProcessingResult_id}`);

	// HDFS Intermediate 디렉토리 생성
	await c3HdfsApi.mkdir(hdfsTargetDir);

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _makeHdfsIntermediateDir] HDFS Intermediate 디렉토리 생성 완료 ( hdfsTargetDir= ${hdfsTargetDir} )`);
};


/**
 * [PROCESS 5:: TASK 3-5] _getParquetFileInfo : 파케이 파일 생성에 필요한 파일 정보 셋팅
 *
 * @param {Object} metaInfo { reportProcessingResult_id, ymd, reportApiType, publisher_id, adProvider_id, dateList }
 * @return {Object} { localDirPath, dateList, parquetFilePathInfo }
 * 	{
 * 		localDirPath: 'local_download/reportapi/google/2021/12/processingResultId=61b026122634cc216aa77290/hdfs/{{chunkNumber}}',
 * 		dateList: ['YYYYMMDD', ... ],
 * 		parquetFilePathInfo: {
 * 			'YYYYMMDD': {
 * 				remoteDirPath: '/2021/12/07/adProviderId=5bf6500077bd856e48a03578/publisherId=5e705e9054e342001d6ed95a',
 *  			localFullPath: 'local_download/reportapi/google/2021/12/processingResultId=61b026122634cc216aa77290/hdfs/{{chunkNumber}}/2021/12/07/adProviderId=5bf6500077bd856e48a03578/publisherId=5e705e9054e342001d6ed95a/part-61b026122634cc216aa77290-{{chunkNumber}}-20211208122905.parquet'
 * 			}, ...
 * 		}
 * 	}
 */
const _getParquetFileInfo = async ({ reportProcessingResult_id, ymd, reportApiType, publisher_id, adProvider_id, dateList }) => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _getParquetFileInfo] 파케이 파일 생성에 필요한 파일 정보 셋팅`);

	// 배치 처리 기준 날짜
	const ymdDate = moment(ymd, 'YYYYMMDD');

	// localDirPath= local_download/reportapi/google/2021/12/processingResultId=xxx/hdfs/{{chunkNumber}}
	const localDirPath = path.join(LOCAL_ROOT, REPORT_API_PATH, reportApiType.toLowerCase(), ymdDate.format('YYYY'), ymdDate.format('MM'), `processingResultId=${reportProcessingResult_id}`, 'hdfs', '{{chunkNumber}}');

	// commonPath= /{{YYYY}}/{{MM}}/{{DD}}/adProviderId=xxx/publisherId=xxx
	const commonPath = SILVERGREY_HDFS_COMMON_PATH.replace(/{{adProviderId}}/g, adProvider_id).replace(/{{publisherId}}/g, publisher_id);


	// date 별 parquetfilePath 정보
	const parquetFilePathInfo = {};

	await Promise.all(dateList.map(async dt => {
		const date = moment(dt);

		// remoteDirPath= /YYYY/MM/DD/adProviderId=xxx/publisherId=xxx
		const remoteDirPath = commonPath.replace(/{{YYYY}}/g, date.format('YYYY')).replace(/{{MM}}/g, date.format('MM')).replace(/{{DD}}/g, date.format('DD'));

		// localFullPath= local_download/reportapi/google/2021/12/processingResultId=xxx/hdfs/{{chunkNumber}}/YYYY/MM/DD/adProviderId=xxx/publisherId=xxx/part-xxxxxx.parquet
		const localFullPath = path.join(localDirPath, remoteDirPath, `part-${reportProcessingResult_id}-{{chunkNumber}}-${moment().format('YYYYMMDDHHmmss')}.parquet`)

		parquetFilePathInfo[dt] = { remoteDirPath, localFullPath };
	}));

	return { localDirPath, dateList, parquetFilePathInfo };
};


/**
 * [Promise]
 * [PROCESS 5:: TASK 3-6] _processParallelLimit : 남은 CPU 개수만큼 ChildProcess 생성하여 병렬 처리하기
 * 
 * @param {Object} metaInfo { reportProcessingResult_id, chunkInfos, adProvider_id, publisher_id, exchangeRate, placeKeyTemplates, placeExt, gfpFeeRate, apiResultPath, separator, fields, parquetFileInfo }
 * @param {String} childProcessPath
 */
const _processParallelLimit = ({ reportProcessingResult_id, chunkInfos, adProvider_id, publisher_id, exchangeRate, placeKeyTemplates, placeExt, gfpFeeRate, apiResultPath, separator, fields, parquetFileInfo }, childProcessPath) => {
	logger.debug(NON_RK_LOGGER, '[non-rk-ad-provider.service :: _processParallelLimit] 호출됨');

	return new Promise((resolve, reject) => {
		// CPU 개수 구하기
		let cpuNum = os.cpus().length - 1; // 부모 프로세스 빼기
		if (chunkInfos.length < cpuNum) {
			cpuNum = chunkInfos.length;
		}

		logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _processParallelLimit] cpu 개수 : ${cpuNum}`);

		let tasks = chunkInfos.map(chunkInfo => {
			return (async (callback) => {
				// ChildProcess 생성하기
				const { err, dataCount } = await _callChildProcess({ reportProcessingResult_id, chunkInfo, adProvider_id, publisher_id, exchangeRate, placeKeyTemplates, placeExt, gfpFeeRate, apiResultPath, separator, fields, parquetFileInfo }, childProcessPath);

				// 처리 완료 상태 BatchReportProcessingResults DB 업뎃
				await batchReportResultService.updateBatchReportProcessingResults({ reportProcessingResult_id, chunkNumber: chunkInfo.chunkNumber, successCount: dataCount, isSuccess: _.isNil(err) });

				// err가 있으면 FAILED 처리됨
				callback(err);
			});
		});

		parallel_limit(tasks, cpuNum, (err) => {
			if (err) {
				// [ERROR] 에러 처리
				reject({ message: '[non-rk-ad-provider.service :: _processParallelLimit] 에러', err });
			} else {
				resolve();
			}
		});
	});
};


/**
 * [Promise]
 * _callChildProcess : ChildProcess 생성하기
 * 
 * @param {Object} metaInfo { reportProcessingResult_id, chunkInfo, adProvider_id, publisher_id, exchangeRate, placeKeyTemplates, placeExt, gfpFeeRate, apiResultPath, separator, fields, parquetFileInfo }
 * @param {String} childProcessPath
 */
const _callChildProcess = ({ reportProcessingResult_id, chunkInfo, adProvider_id, publisher_id, exchangeRate, placeKeyTemplates, placeExt, gfpFeeRate, apiResultPath, separator, fields, parquetFileInfo }, childProcessPath) => {

	return new Promise(resolve => {
		logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _callChildProcess] chunkNumber #${chunkInfo.chunkNumber} START :: ${moment().format('YYYY-MM-DD HH:mm:ss')}`);

		// 자식 프로세스 생성
		const childProcess = fork(path.join(process.cwd(), childProcessPath));

		// 자식 프로세스로부터 받은 처리 결과 메시지
		childProcess.on('message', (res) => {
			// 자식프로세스 종료
			childProcess.disconnect();

			// [ERROR] 에러 처리
			if(res.state === 'FAILED') {
				resolve({
					err: new BusinessError({ message: `[non-rk-ad-provider.service :: _callChildProcess] chunkNumber #${chunkInfo.chunkNumber} 처리 FAILED 에러` }, { err: res, detail: JSON.stringify(res, null, 2)})
				});
			}

			if (res.state === 'COMPLETE') {
				logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _callChildProcess] chunkNumber #${chunkInfo.chunkNumber} END :: ${moment().format('YYYY-MM-DD HH:mm:ss')}\n`);

				resolve({ dataCount: res.dataCount });
			}
		});

		childProcess.on('exit', (code, signal) => {
			logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _callChildProcess] chunkNumber #${chunkInfo.chunkNumber} exit ( code=${code}, signal=${signal} )`);
		});

		// [ERROR] 에러 처리
		childProcess.on('error', err => {
			resolve({
				err: new BusinessError({ message: `[non-rk-ad-provider.service :: _callChildProcess] chunkNumber #${chunkInfo.chunkNumber} 자식 프로세스 에러` }, { err: err, detail: JSON.stringify(err, null, 2)})
			});
		});

		// 프로세스가 준비될 때까지 기다렸다가 실행
		setTimeout(() => {
			childProcess.send({ reportProcessingResult_id, chunkInfo, adProvider_id, publisher_id, exchangeRate, placeKeyTemplates, placeExt, gfpFeeRate, apiResultPath, separator, fields, parquetFileInfo });
		}, 2000);
	});
};


/**
 * [PROCESS 5:: TASK 3-7] _createHdfsSuccessFile : publisherId 하위에 _SUCCESS 파일 추가하기
 *
 * @param {Object} { adProvider_id, publisher_id, dateList }
 */
const _createHdfsSuccessFile = async ({ reportProcessingResult_id, adProvider_id, publisher_id, dateList }) => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _createHdfsSuccessFile] publisherId 하위에 _SUCCESS 파일 추가하기 ( ${dateList[0]}~${dateList[dateList.length - 1]} adProvider_id=${adProvider_id}/publisher_id=${publisher_id} )`);

	// commonPath = /{{YYYY}}/{{MM}}/{{DD}}/adProviderId=xxx/publisherId=xxx
	const commonPath = SILVERGREY_HDFS_COMMON_PATH.replace(/{{adProviderId}}/g, adProvider_id).replace(/{{publisherId}}/g, publisher_id);

	const hdfsSuccessFileList = [];

	for (const dt of dateList) {
		const date = moment(dt);

		// intermediate_nonrk_dir = /user/gfp-data/silvergrey/intermediate/nonrk
		// hdfsTargetDir = /user/gfp-data/silvergrey/intermediate/nonrk/processingResultId=xxx/2022/05/23/adProviderId=5b74d94bc36eef272090ca52/publisherId=5e705e9054e342001d6ed95a
		// hdfsSuccessFile = /user/gfp-data/silvergrey/intermediate/nonrk/processingResultId=xxx/2022/05/23/adProviderId=5b74d94bc36eef272090ca52/publisherId=5e705e9054e342001d6ed95a/_SUCCESS
		const hdfsTargetDir = path.join(SILVERGREY_HDFS_INTERMEDIATE_NONRK_DIR, `processingResultId=${reportProcessingResult_id}`, commonPath.replace(/{{YYYY}}/g, date.format('YYYY')).replace(/{{MM}}/g, date.format('MM')).replace(/{{DD}}/g, date.format('DD')));
		const hdfsSuccessFile = path.join(hdfsTargetDir, '_SUCCESS');

		// 디렉토리 경로가 HDFS에 존재하는지 확인
		if (await c3HdfsApi.exists(hdfsTargetDir)) {
			hdfsSuccessFileList.push(hdfsSuccessFile);
		}
	}

	// SUCCESS 파일을 하나씩 생성 하면 오래 걸리므로, 여러 경로를 한 번에 생성하도록 함
	if (!_.isEmpty(hdfsSuccessFileList)) {
		await c3HdfsCli.touch(hdfsSuccessFileList);
	}

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _createHdfsSuccessFile] publisherId 하위에 _SUCCESS 파일 추가 완료 ( ${dateList[0]}~${dateList[dateList.length - 1]} adProvider_id=${adProvider_id}/publisher_id=${publisher_id} )`);
};


/**
 * [PROCESS 5:: TASK 3-8] _copyHdfsDir : HDFS Intermediate 경로를 원본 경로로 복사하기
 *
 * @param {Object} { reportProcessingResult_id, adProvider_id, publisher_id, dateList }
 */
const _copyHdfsDir = async ({ reportProcessingResult_id, adProvider_id, publisher_id, dateList }) => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _copyHdfsDir] HDFS Intermediate 경로를 원본 경로로 복사하기 ( ${dateList[0]}~${dateList[dateList.length - 1]} adProvider_id=${adProvider_id}/publisher_id=${publisher_id} )`);

	// HDFS 디렉토리 삭제
	const monthPathList = await _deleteHdfsDir({ publisher_id, adProvider_id, dateList });

	// intermediate_nonrk_dir = /user/gfp-data/silvergrey/intermediate/nonrk
	// hdfsSrcDir = /user/gfp-data/silvergrey/intermediate/nonrk/processingResultId=xxx
	const hdfsSrcDir = path.join(SILVERGREY_HDFS_INTERMEDIATE_NONRK_DIR, `processingResultId=${reportProcessingResult_id}`);

	// HDFS Intermediate 경로를 원본 경로로 복사
	await Promise.all(monthPathList.map(async monthPath => {
		// hdfsSrcPath = /user/gfp-data/silvergrey/intermediate/nonrk/processingResultId=xxx/yyyy/mm/*
		// hdfsDestPath = /user/gfp-data/silvergrey/nonrk/yyyy/mm
		const hdfsSrcPath = path.join(hdfsSrcDir, monthPath, '*');
		const hdfsDestPath = path.join(SILVERGREY_HDFS_NONRK_DIR, monthPath);

		// HDFS 원본 디렉토리 생성
		if (!await c3HdfsApi.exists(hdfsDestPath)) {
			await c3HdfsApi.mkdir(hdfsDestPath);
		}

		logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _copyHdfsDir] HDFS intermediate 경로를 원본 경로로 복사 ( hdfsSrcPath= ${hdfsSrcPath}, hdfsDestPath= ${hdfsDestPath} )`);


		// 날짜 디렉토리가 있는지 체크 후, HDFS 원본 디렉토리로 복사
		// - /user/gfp-data/silvergrey/intermediate/nonrk/processingResultId=xxx/yyyy/mm
		if (await c3HdfsApi.exists(path.join(hdfsSrcDir, monthPath))) {
			await c3HdfsCli.cp(hdfsSrcPath, hdfsDestPath);
		}
	}));

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _copyHdfsDir] HDFS Intermediate 경로를 원본 경로로 복사 완료 ( ${dateList[0]}~${dateList[dateList.length - 1]} adProvider_id=${adProvider_id}/publisher_id=${publisher_id} )`);
};


/**
 * [PROCESS 5:: TASK 4] deleteLocal : AP 원본 파일 로컬 삭제
 *
 * @param {Object} { reportInfoExt: { apiResultPath } }
 */
module.exports.deleteLocal = async ({ reportInfoExt: { apiResultPath } }) => {
	// apiResultPath = local_download/reportapi/app_nexus/2023/12/5d8ac41f0fe0f652a82f608d_5d11dc1a34480e001d31fb26_20231201_20231201_14.csv
	// { root: '', dir: 'local_download/reportapi/app_nexus/2023/12',
	// 		base: '5d8ac41f0fe0f652a82f608d_5d11dc1a34480e001d31fb26_20231201_20231201_14.csv', ext: '.csv', name: '5d8ac41f0fe0f652a82f608d_5d11dc1a34480e001d31fb26_20231201_20231201_14' }
	const parsed = path.parse(apiResultPath);
	const deleteFilePath = path.join(parsed.dir, parsed.name);

	// 임시 파일 포함 하여 일괄 삭제 처리
	execSync(`rm ${deleteFilePath}*`);

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: deleteLocal] 명령어 실행 완료 ( rm ${deleteFilePath}* )`);
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: deleteLocal] AP 원본 파일 로컬 삭제 완료 ( apiResultPath= ${apiResultPath} )`);
};


/**
 * [PROCESS 5:: TASK 3] processAdProviderNonRkStat : 리포트 파일 가공 및 AdProviderNonRkStat DB 저장
 *  TASK 3-0. 파일 총 라인수 가져오기
 *  TASK 3-1. 리포트 파일 정보를 chunk_size 단위로 끊어서 BatchReportProcessingResults DB에 로깅
 *  TASK 3-2. publisher_id, adProvider_id를 기준으로 특정 기간에 해당하는 데이터를 AdProviderNonRkStats DB에서 일괄 삭제 처리
 *  TASK 3-3. 남은 CPU 개수만큼 ChildProcess 생성하여 병렬 처리하기
 *
 * @param {Object} adProviderMetaInfo
 * { 
		ymd, reportApiType, adProvider_id, publisher_id, 
		chunk_size, exchangeRate,
		period: { startDate, endDate }, 
		placeKeyTemplates, 
		reportInfoExt: { apiResultPath, separator, fields }, 
		placeExt,
		gfpFeeRate
 * }
 */
module.exports.processAdProviderNonRkStat = async ({ ymd, reportApiType, adProvider_id, publisher_id, chunk_size, exchangeRate, period: { startDate, endDate }, placeKeyTemplates, reportInfoExt: { apiResultPath, separator, fields }, placeExt, gfpFeeRate }) => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: processAdProviderNonRkStat] 리포트 파일 처리 시작`);
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: processAdProviderNonRkStat] api 결과 경로 ${apiResultPath}`);


	// [PROCESS 5:: TASK 3-0] 파일 총 라인수 가져오기
	const totalLineCount = await _getFileTotalLineCount(apiResultPath);

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: processAdProviderNonRkStat] totalLineCount :: ${totalLineCount}`);


	// [PROCESS 5:: TASK 3-1] 리포트 파일 정보를 chunk_size 단위로 끊어서 BatchReportProcessingResults DB에 로깅
	/* chunkInfos = [{ chunkNumber, startLine, endLine, state }] */
	const { reportProcessingResult_id, chunkInfos } = await batchReportResultService.upsertBatchReportProcessingResults({ totalLineCount, chunk_size, ymd, adProvider_id, publisher_id, reportApiType, apiResultPath, type: 'DB' });


	// [PROCESS 5:: TASK 3-2] publisher_id, adProvider_id를 기준으로 특정 기간에 해당하는 데이터를 AdProviderNonRkStats DB에서 일괄 삭제 처리
	await _deleteAdProviderNonRkStatDB({ publisher_id, adProvider_id, startDate, endDate });


	// [PROCESS 5:: TASK 3-3] 남은 CPU 개수만큼 ChildProcess 생성하여 병렬 처리하기
	if(!_.isEmpty(chunkInfos)) {
		const childProcessPath = '/src/nonRkApp.js';
		await _processParallelLimit({ reportProcessingResult_id, chunkInfos, adProvider_id, publisher_id, exchangeRate, placeKeyTemplates, placeExt, gfpFeeRate, apiResultPath, separator, fields }, childProcessPath);
	}


	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: processAdProviderNonRkStat] 리포트 파일 처리 완료`);
};


/**
 * [PROCESS 5:: TASK 3-2] _deleteAdProviderNonRkStatDB : publisher_id, adProvider_id를 기준으로 특정 기간에 해당하는 데이터를 AdProviderNonRkStats DB에서 일괄 삭제 처리
 * 
 * @param {Object} metaInfo { publisher_id, adProvider_id, startDate, endDate }
 */
const _deleteAdProviderNonRkStatDB = async ({ publisher_id, adProvider_id, startDate, endDate }) => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _deleteAdProviderNonRkStatDB] adProvider_id( ${adProvider_id} ), publisher_id( ${publisher_id} ) 를 기준으로 ${startDate.format('YYYYMMDD')}~${endDate.format('YYYYMMDD')} 기간의 데이터 일괄 삭제 처리 시작`);

	const bulk = AdProviderNonRkStat.collection.initializeUnorderedBulkOp();
	bulk.find({ adProvider_id, publisher_id, ymd: { $gte: startDate.format('YYYYMMDD'), $lte: endDate.format('YYYYMMDD') } }).delete();
	const results = await bulk.execute();

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _deleteAdProviderNonRkStatDB] ${results.result.nRemoved} 건 삭제 완료 ::: adProvider_id( ${adProvider_id} ), publisher_id( ${publisher_id} ) , ${startDate.format('YYYYMMDD')}~${endDate.format('YYYYMMDD')}`);

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: _deleteAdProviderNonRkStatDB] adProvider_id( ${adProvider_id} ), publisher_id( ${publisher_id} ) 를 기준으로 ${startDate.format('YYYYMMDD')}~${endDate.format('YYYYMMDD')} 기간의 데이터 일괄 삭제 처리 완료`);
};


/**
 * [PROCESS 5:: TASK 4] updateZirconTrace : ZirconTrace 이력 업데이트
 *
 * @param {Object} adProviderMetaInfo { adProvider_id, publisher_id, period: { startDate, endDate } }
 * @param {Boolean} success
 */
module.exports.updateZirconTrace = async ({ adProvider_id, publisher_id, period: { startDate, endDate } }, success) => {
	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: updateZirconTrace] ZirconTrace 이력 업데이트 시작 ( adProvider_id= ${adProvider_id}, publisher_id= ${publisher_id}, startDate= ${startDate.format('YYYYMMDD')}, endDate= ${endDate.format('YYYYMMDD')} )`);

	const today = moment();

	const dateList = _getDateList(startDate, endDate);

	const operations = [];
	if (success) {
		await Promise.all(dateList.map(async date => {
			operations.push({
				updateOne: {
					filter: { date, adProvider_id, publisher_id },
					update: {
						$set: { silvergreyCompletedAt: today },
						$setOnInsert: { createdAt: today, expiredAt: moment(date).add(12, 'months').add(1, 'days') },
					}, upsert: true,
				}
			});
		}));
	} else {
		// 연동 실패한 경우, endDate 만 silvergreyCompletedAt null 처리함
		// 기간별 AP(GOOGLE 등) 인 경우, 데이터가 존재함에도 null 로 셋팅 되는 이슈가 있었음
		operations.push({
			updateOne: {
				filter: { date: endDate.format('YYYYMMDD'), adProvider_id, publisher_id },
				update: {
					$set: { silvergreyCompletedAt: null },
					$setOnInsert: { createdAt: today, expiredAt: moment(endDate).add(12, 'months').add(1, 'days') },
				}, upsert: true,
			}
		});
	}

	if (!_.isEmpty(operations)) {
		const result = await ZirconTrace.bulkWrite(operations);

		// [ERROR] 응답 오류가 있는 경우, 에러 처리
		if (result.ok !== 1 || !_.isEmpty(result.writeErrors)) {
			throw new BusinessError({ message: `[non-rk-ad-provider.service :: updateZirconTrace] DB 저장 오류` }, { err: result, detail: JSON.stringify(result, null, 2) });
		}
	}

	logger.debug(NON_RK_LOGGER, `[non-rk-ad-provider.service :: updateZirconTrace] ZirconTrace 이력 업데이트 완료 ( adProvider_id= ${adProvider_id}, publisher_id= ${publisher_id}, startDate= ${startDate.format('YYYYMMDD')}, endDate= ${endDate.format('YYYYMMDD')} )`);
};
