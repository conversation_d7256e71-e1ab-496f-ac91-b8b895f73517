'use strict';

import _ from 'lodash';
import moment from 'moment';

import { BusinessError } from '../../common/error';

import * as logger from '../../utils/logger.util';

import * as reportApiService from '../reportapi/report-api.service';

const NON_RK_LOGGER = 'report_api_non_rk';

// 요청당 최대 데이터 수
const DATA_LIMIT = 10000;


/**
 * [PROCESS 3:: TASK 2] processReportApi : APPLOVIN_ZONE 리포트 API 연동 및 리포트 파일 다운로드
 *  SUBTASK 1. initCsvReport : csv 파일 초기화
 *  SUBTASK 2. 리포트 데이터 생성 요청 및 다운로드
 * 		2-1. _requestReport : 리포트 데이터 생성 요청하기
 * 		2-2. _getDataList : 리포트 데이터 가공하기
 * 		2-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
 * 
 * @RequestMapping(value='/batch/day/outside/applovin_zone')
 * 
 * @param {Object} adProviderMetaInfo 
 * { 
		reportInfos: { API_KEY },
		reportInfoExt: { apiResultPath, fields, columns, report_request_api },
		period: { startDate, endDate },
		fda
 * }
 */
module.exports.processReportApi = async ({ reportInfos: { API_KEY }, reportInfoExt: { apiResultPath, fields, columns, report_request_api }, period: { startDate, endDate }, fda }) => {
	logger.debug(NON_RK_LOGGER, '[applovin-zone.service :: processReportApi] 호출됨');


	/* SUBTASK 1. initCsvReport : csv 파일 초기화 */
	// 필드 순서 : ymd, zoneId, country, os, imp, clk, net_revenue
	await reportApiService.initCsvReport({ fda, fields });


	/*
		SUBTASK 2. 리포트 데이터 생성 요청 및 다운로드
	*/

	// SUBTASK 2-1. _requestReport : 리포트 데이터 생성 요청하기
	const rawDataList = await _requestReport({ report_request_api, columns, period: { startDate, endDate }, reportInfos: { API_KEY } });

	// SUBTASK 2-2. _getDataList : 리포트 데이터 가공하기
	const dataList = await _getDataList({ fields, rawDataList });

	// SUBTASK 2-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
	await reportApiService.writeCsvReport({ fda, apiResultPath, fields, dataList });
};


/**
 * SUBTASK 2-1. _requestReport : 리포트 데이터 생성 요청하기
 *
 * @param {Object} { report_request_api, columns, period: { startDate, endDate }, reportInfos: { API_KEY } }
 * @return {Array} rawDataList
 */
const _requestReport = async ({ report_request_api, columns, period: { startDate, endDate }, reportInfos: { API_KEY } }) => {
	logger.debug(NON_RK_LOGGER, `[applovin-zone.service :: _requestReport] 리포트 데이터 생성 요청하기`);

	// 리포트 데이터
	let rawDataList = new Array();

	// endDate 가 조회 기간에 포함이 됨
	const startDt = moment(startDate).format('YYYY-MM-DD');
	const endDt = moment(endDate).format('YYYY-MM-DD');

	// https://r.applovin.com/report?api_key=XXXX&columns=day,zone_id,country,platform,impressions,clicks,revenue&start=2022-11-29&end=2022-11-29&format=json&limit=10000&offset=0
	const requestApiUrl = `${report_request_api}?api_key=${API_KEY}&start=${startDt}&end=${endDt}&columns=${columns.join(',')}&format=json&limit=${DATA_LIMIT}`;

	// 페이징 정보
	let offset = 0;
	let loop = true;

	// loop 가 true인 경우, 계속 반복
	while(loop) {
		logger.debug(NON_RK_LOGGER, `[applovin-zone.service :: _requestReport] data offset = ( ${offset} ~ ${offset + DATA_LIMIT - 1} )`);

		const url = `${requestApiUrl}&offset=${offset}`;

		// error res
		// 	- 403 Authentication Failed
		// 	- 400 Invalid Publisher Column: xxxxx
		// res : { code: 200, count: 10, results: [ { day:'2022-11-29', zone_id:'968cc147c2f3f11d', country:'kr', platform:'android', impressions:'2', clicks:'1', revenue:'0.000804' }, ... ] }
		const res = await reportApiService.requestApi({ url });

		// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
		if (!_.isNil(res.err)) {
			throw new BusinessError({ message: `[applovin-zone.service :: _requestReport] API 요청 실패 ::: statusCode(${res.status}), url(${res.url}), err(${res.err})` }, { err: res });
		}

		// [ERROR] 응답 결과에 status 가 success 가 아닌 경우, 에러 처리
		if (!_.isEmpty(res.code) && !_.isEqual(res.code, 200)) {
			throw new BusinessError({ message: `[applovin-zone.service :: _requestReport] 리포트 데이터 생성 요청 실패 ::: ${JSON.stringify(res, null, 2)}` });
		}

		// res.count 가 DATA_LIMIT(10000)보다 작은 경우, 마지막 페이지이므로, loop 종료
		if(res.count < DATA_LIMIT) {
			loop = false;
		}

		// 데이터가 있으면, rawDataList 및 offset 셋팅
		if(res.count > 0) {
			// rawDataList 셋팅
			rawDataList = _.concat(rawDataList, res.results);

			// 다음 페이지 셋팅
			offset = offset + DATA_LIMIT;
		}
	}

	logger.debug(NON_RK_LOGGER, `[applovin-zone.service :: _requestReport] 리포트 데이터 생성 요청 완료 ( rawDataList.length = ${rawDataList.length} )`);

	return rawDataList;
};


/**
 * SUBTASK 2-2. _getDataList : 리포트 데이터 가공하기
 *
 * @param {Object} { fields: { country }, rawDataList }
 * @return {Array} dataList
 */
const _getDataList = async ({ fields: { country }, rawDataList }) => {
	logger.debug(NON_RK_LOGGER, `[applovin-zone.service :: _getDataList] 리포트 데이터 가공하기`);

	let dataList = new Array();

	rawDataList.map(rawData => {
		let data = Object.assign({}, rawData);

		// 국가 코드 대문자로 변환
		data[country] = _.upperCase(data[country]);

		dataList.push(data);
	});

	return dataList;
};
