'use strict';


import _ from 'lodash';
import moment from 'moment';

import { DataEnvironments } from '../../models/data/data-environments.schema';

import { BusinessError } from '../../common/error';

import * as logger from '../../utils/logger.util';

import * as reportApiService from '../reportapi/report-api.service';

const NON_RK_LOGGER = 'report_api_non_rk';

// 요청당 최대 데이터 수
const DATA_LIMIT = 5000;

// ECONNRESET 응답 에러 발생 시, 재요청할 횟수
const ECONNRESET_MAX_COUNT = 10;


/**
 * [PROCESS 3:: TASK 2-2] processReportApi : INMOBI 리포트 API 연동 및 리포트 파일 다운로드
 *  SUBTASK 1. initCsvReport : csv 파일 초기화
 *  SUBTASK 2. _getSession : 유효 세션 요청하기
 *  SUBTASK 3. 45일치 리포트 데이터 요청 및 다운로드
 * 		3-1. _requestReport : 리포트 데이터 생성 요청
 * 		3-2. _getDataList : 리포트 데이터 가공하기
 * 		3-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
 *
 * @RequestMapping(value='/batch/day/outside/inmobi')
 *
 * @param {Object} adProviderMetaInfo
 * { 
		reportInfos: { USERNAME, API_KEY, APP_IDS },
		reportInfoExt: { apiResultPath, groupBy, metrics, orderBy, orderType, fields, token_env_name, session_request_api, report_request_api },
		period: { startDate, endDate },
		fda, countryMappings
 * }
 * @return {String} reportFilePath
 */
module.exports.processReportApi = async ({ reportInfos: { USERNAME, API_KEY, APP_IDS }, reportInfoExt: { apiResultPath, groupBy, metrics, orderBy, orderType, fields, token_env_name, session_request_api, report_request_api }, period: { startDate, endDate }, fda, countryMappings }) => {
	logger.debug(NON_RK_LOGGER, `[inmobi.service :: processReportApi] 호출됨 ( 총 ${startDate.format('YYYY-MM-DD')} ~ ${endDate.format('YYYY-MM-DD')} )`);


	/* SUBTASK 1. initCsvReport : csv 파일 초기화 */
	// 필드 순서 : ymd, PLACEMENT_ID, country, os, imp, clk, net_revenue
	await reportApiService.initCsvReport({ fda, fields });


	/* SUBTASK 2. _getSession : 유효 세션 요청하기 */
	// sessionData: { sessionId, accountId }
	const sessionData = await _getSession({ reportInfos: { USERNAME, API_KEY }, token_env_name, session_request_api });


	/* SUBTASK 3. 45일치 리포트 데이터 요청 및 다운로드 */
	APP_IDS = APP_IDS.split(',');

	// APP_IDS 가 없는 경우, 리포트 요청 하지 않음
	if (_.isEmpty(APP_IDS)) {
		logger.debug(NON_RK_LOGGER, '[inmobi.service :: processReportApi] APP_IDS 가 없으므로, 리포트 요청하지 않고 종료함');
		return;
	}

	const PRD = 15; // PERIOD : 15일 단위
	let startDt = moment(startDate);
	let endDt = moment(startDate).add(PRD - 1, 'days');

	// 45일치 기간을 15일 단위로 요청
	while (startDt.isSameOrBefore(endDate)) {
		logger.debug(NON_RK_LOGGER, `[inmobi.service :: processReportApi] START ::: ( ${startDt.format('YYYY-MM-DD')} ~ ${endDt.format('YYYY-MM-DD')} )`);


		// SUBTASK 3-1. _requestReport : 리포트 데이터 생성 요청
		const rawDataList = await _requestReport({ sessionData, API_KEY, APP_IDS, groupBy, metrics, orderBy, orderType, report_request_api, startDate: startDt, endDate: endDt });


		// SUBTASK 3-2. _getDataList : 리포트 데이터 가공하기
		const dataList = await _getDataList({ fields, rawDataList, countryMappings });


		// SUBTASK 3-3. writeCsvReport : 리포트 데이터 csv 파일 쓰기
		await reportApiService.writeCsvReport({ fda, apiResultPath, fields, dataList });

		logger.debug(NON_RK_LOGGER, `[inmobi.service :: processReportApi] END ::: ( ${startDt.format('YYYY-MM-DD')} ~ ${endDt.format('YYYY-MM-DD')} )`);


		// 시작일, 종료일을 15일 후로 셋팅
		startDt.add(PRD, 'days');
		endDt.add(PRD, 'days');

		// 마지막 날짜 셋팅
		if (endDate.isSameOrBefore(endDt)) {
			endDt = moment(endDate);
		}
	}

	logger.debug(NON_RK_LOGGER, `[inmobi.service :: processReportApi] 리포트 파일 생성 완료 ( 총 ${startDate.format('YYYY-MM-DD')} ~ ${endDate.format('YYYY-MM-DD')} )`);
};


/**
 * SUBTASK 2. _getSession : 유효 세션 요청하기
 *
 * @param {Object} { reportInfos: { USERNAME, API_KEY }, token_env_name, session_request_api }
 * @return {Object} sessionData { sessionId, accountId }
 */
const _getSession = async ({ reportInfos: { USERNAME, API_KEY }, token_env_name, session_request_api }) => {
	logger.debug(NON_RK_LOGGER, `[inbmoi.service :: _getSession] 유효 세션 요청`);

	/* 1. Environments에 저장된 토큰 정보 가져오기 */
	// { sessionId, accountId }
	let sessionData = await _getTokenEnvInfo({ reportInfos: { USERNAME, API_KEY }, token_env_name });

	// DB에 토큰 정보가 있는 경우, 반환
	if (!_.isEmpty(sessionData)) {
		logger.debug(NON_RK_LOGGER, `[inbmoi.service :: _getSession] DB 인증 토큰 ( sessionData= ${JSON.stringify(sessionData, null, 2)} )`);

		return sessionData;
	}


	/* 2. 토큰 정보 발급하기 */

	// res : [{ sessionId, accountId, subAccounts }]
	// ECONNRESET error : { code : 'ECONNRESET' }
	let reqCount = 1;
	let res = await _requestApi({ url: session_request_api, headers: { userName: USERNAME, secretKey: API_KEY } });

	// ECONNRESET ERROR 인 경우, 재요청
	while (!_.isNil(res) && !_.isNil(res.code) && _.includes(res.code, 'ECONNRESET')) {
		logger.debug(NON_RK_LOGGER, `[inmobi.service :: _getSession] ECONNRESET으로 인한 재요청 ( reqCount : ${reqCount} )`);

		res = await _requestApi({ url: session_request_api, headers: { userName: USERNAME, secretKey: API_KEY } });
		reqCount++;

		// ECONNRESET error가 계속 발생하는 경우, 에러 처리
		// 최대 재요청 횟수는 10번
		if (ECONNRESET_MAX_COUNT < reqCount) {
			throw new BusinessError({ message: `[inmobi.service :: _getSession] ECONNRESET ERROR ::: ${JSON.stringify(res, null, 2)}` }, {
				err: res, detail: JSON.stringify(res, null, 2)
			});
		}
	}

	// [ERROR] 응답 결과가 없는 경우, 에러 처리
	if (_.isNil(res) || _.isEmpty(res)) {
		throw new BusinessError({ message: `[inbmoi.service :: _getSession] 유효 세션 정보 없음` });
	}

	sessionData = res.pop();

	// [ERROR] sessionData가 없는 경우, 에러 처리
	if (_.isNil(sessionData) || _.isEmpty(sessionData)) {
		throw new BusinessError({ message: `[inbmoi.service :: _getSession] 유효 세션 정보 없음` });
	}


	/* 3. 토큰 정보 DB에 저장하기 */
	await _upsertTokenEnvInfo({ reportInfos: { USERNAME, API_KEY }, token_env_name, sessionId: sessionData.sessionId, accountId: sessionData.accountId });


	logger.debug(NON_RK_LOGGER, `[inbmoi.service :: _getSession] 유효 세션 요청 완료 ( sessionData= ${JSON.stringify(sessionData, null, 2)} )`);

	return sessionData;
};


/**
 * SUBTASK 2-1. _getTokenEnvInfo : Environments report-api-inmobi-token에 저장된 토큰 정보 가져오기
 * 		- 토큰 환경 정보가 없는 경우, 새로 추가하고 null 반환
 * 		- 토큰 환경 정보가 있는 경우, 맞는 토큰 정보를 찾아서 반환
 *
 * @param {Object} { reportInfos: { USERNAME, API_KEY }, token_env_name }
 * @return {Object} tokenInfo { sessionId, accountId }
 */
const _getTokenEnvInfo = async ({ reportInfos: { USERNAME, API_KEY }, token_env_name }) => {
	logger.debug(NON_RK_LOGGER, `[inmobi.service :: _getTokenEnvInfo] Environments report-api-inmobi-token 정보 가져오기 ( USERNAME= ${USERNAME} )`);

	// [ERROR] token_env_name가 없으면, 에러
	if (_.isNil(token_env_name) || _.isEmpty(token_env_name)) {
		throw new BusinessError({ message: `[inmobi.service :: _getTokenEnvInfo] token_env_name 정보 없음` });
	}

	const now = moment();

	// 토큰 환경 정보 : { name: 'report-api-inmobi-token', value: [{ reportInfos: { USERNAME, API_KEY }, sessionId, accountId, expiredAt, createdAt }, ... ] }
	const tokenEnv = await DataEnvironments.findOne({ name: token_env_name }).exec();

	// 토큰 환경 정보 없는 경우, 새로 추가하고 null 반환
	if (_.isNil(tokenEnv)) {
		// 환경 정보 추가
		await DataEnvironments.findOneAndUpdate({ name: token_env_name }, {
			$set: {
				value: [], createdAt: now, modifiedAt: now
			}
		}, { new: true, runValidators: true, upsert: true }).exec();

		return null;
	}


	// 토큰 정보
	const tokenInfo = tokenEnv.value.filter(({ reportInfos, expiredAt }) => {
		// expiredAt이 현재 시간 이전인 경우, true
		if (now.isBefore(expiredAt) && _.isEqual(USERNAME, reportInfos.USERNAME) && _.isEqual(API_KEY, reportInfos.API_KEY)) {
			return true;
		}
	}).pop();

	// logger.debug(NON_RK_LOGGER, `[inmobi.service :: _getTokenEnvInfo] Environments report-api-inmobi-token 정보 ( tokenInfo= ${JSON.stringify(tokenInfo)} )`);

	return tokenInfo;
};


/**
 * SUBTASK 2-2. _upsertTokenEnvInfo : Environments report-api-inmobi-token(token_env_name)에 토큰 정보 업데이트하기
 * 		- 토큰 만료 24시간
 *
 * @param {Object} { reportInfos: { USERNAME, API_KEY }, token_env_name, sessionId, accountId }
 */
const _upsertTokenEnvInfo = async ({ reportInfos: { USERNAME, API_KEY }, token_env_name, sessionId, accountId }) => {
	logger.debug(NON_RK_LOGGER, `[inmobi.service :: _upsertTokenEnvInfo] Environments report-api-inmobi-token 정보 업데이트 ( USERNAME= ${USERNAME} )`);

	// [ERROR] token_env_name가 없으면, 에러
	if (_.isNil(token_env_name) || _.isEmpty(token_env_name)) {
		throw new BusinessError({ message: `[inmobi.service :: _getTokenEnvInfo] token_env_name 정보 없음` });
	}

	let result = null;

	const now = moment();

	// USERNAME, API_KEY 에 해당하는 토큰 정보 가져오기
	const isExist = await DataEnvironments.findOne({ name: token_env_name, 'value.reportInfos.USERNAME': USERNAME, 'value.reportInfos.API_KEY': API_KEY }).exec();

	// 토큰 정보가 있으면 수정, 없으면 추가
	if (!_.isNil(isExist)) {
		// 토큰 정보 수정
		result = await DataEnvironments.findOneAndUpdate({ name: token_env_name }, {
			$set: {
				'value.$[el].sessionId': sessionId, 'value.$[el].accountId': accountId, 'value.$[el].expiredAt': moment(now).add(-5, 'minutes').add(24, 'hours').toDate(), 'value.$[el].modifiedAt': now.toDate(), modifiedAt: now
			}
		}, {
			new: true, runValidators: true, arrayFilters: [
				{ 'el.reportInfos.USERNAME': USERNAME, 'el.reportInfos.API_KEY': API_KEY, }
			]
		}).exec();
	} else {
		// 토큰 정보 추가
		result = await DataEnvironments.findOneAndUpdate({ name: token_env_name }, {
			$push: {
				value: {
					reportInfos: { USERNAME, API_KEY }, sessionId, accountId, expiredAt: moment(now).add(-5, 'minutes').add(24, 'hours').toDate(), modifiedAt: now.toDate()
				}
			}, $set: { modifiedAt: now },
		}, { new: true, runValidators: true }).exec();
	}

	// [ERROR] 결과가 없는 경우, 에러 처리
	if (_.isNil(result) && _.isEmpty(result)) {
		throw new BusinessError({ message: `[inmobi.service :: _upsertTokenEnvInfo] Environments DB 저장 오류` }, { err: result, detail: JSON.stringify(result, null, 2) });
	}
};


/**
 * SUBTASK 3-1. _requestReport : 리포트 데이터 생성 요청
 *
 * @param {Object} reportQueryInfo 리포트 조회 조건 
	{
		sessionData: { sessionId, accountId }, 
		API_KEY,
		APP_IDS,
		groupBy: ['date', 'placement', 'country', 'platform', 'requestSlot'],
		metrics: ['adImpressions', 'clicks', 'earnings'], 
		orderBy: ['earnings'],
		orderType: 'desc', 
		report_request_api: 'https://api.inmobi.com/v3.0/reporting/publisher',
		startDate, endDate 
	}
 * @return {Array} rawDataList 리포트 데이터
 */
const _requestReport = async ({ sessionData: { sessionId, accountId }, API_KEY, APP_IDS, groupBy, metrics, orderBy, orderType, report_request_api, startDate, endDate }) => {
	logger.debug(NON_RK_LOGGER, `[inmobi.service :: _requestReport] 리포트 데이터 생성 요청 시작`);

	// 리포트 데이터
	let rawDataList = new Array();

	const since = startDate.format('YYYY-MM-DD');
	const until = endDate.format('YYYY-MM-DD');

	const headers = {
		sessionId, accountId, secretKey: API_KEY,

		'Accept': 'application/json', 'Content-Type': 'application/json'
	};

	let body = {
		reportRequest: {
			metrics, groupBy, orderBy, orderType, filterBy: [{ filterName: 'inmobiAppId', filterValue: APP_IDS, comparator: 'IN' }], timeFrame: `${since}:${until}`, offset: 0, length: DATA_LIMIT,
		},
	};

	// 페이징 정보
	let loop = true;

	// loop 가 true인 경우, 계속 반복
	while (loop) {
		logger.debug(NON_RK_LOGGER, `[inmobi.service :: _requestReport] data offset = ( ${body.reportRequest.offset} ~ ${body.reportRequest.offset + DATA_LIMIT - 1} )`);

		// 리포트 데이터 요청
		// res : [{ date, placementId, country, platform, requestSlotId, adImpressions, clicks, earnings }]
		// ECONNRESET ERROR : { code : 'ECONNRESET' }
		let reqCount = 1;
		let res = await _requestApi({ url: report_request_api, method: 'POST', headers, body });

		// ECONNRESET ERROR 인 경우, 재요청
		while (!_.isNil(res) && !_.isNil(res.code) && _.includes(res.code, 'ECONNRESET')) {
			logger.debug(NON_RK_LOGGER, `[inmobi.service :: _requestReport] ECONNRESET으로 인한 재요청 ( reqCount : ${reqCount} )`);

			res = await _requestApi({ url: report_request_api, method: 'POST', headers, body });
			reqCount++;

			// 최대 재요청 횟수는 10번
			// ECONNRESET ERROR 가 계속 발생하는 경우, 에러 처리
			if (ECONNRESET_MAX_COUNT < reqCount) {
				throw new BusinessError({ message: `[inmobi.service :: _requestReport] ECONNRESET ERROR ::: ${JSON.stringify(res, null, 2)}` }, { err: res, detail: JSON.stringify(res, null, 2) });
			}
		}

		// res 가 없는 경우, 가져올 데이터가 없으므로, loop 종료
		// res 사이즈가 DATA_LIMIT(5000)보다 작은 경우, 마지막 페이지이므로, loop 종료
		if (_.isNil(res) || _.isEmpty(res) || (!_.isNil(res) && res.length < DATA_LIMIT)) {
			loop = false;
		}

		// res 가 있으면, rawDataList 및 offset 셋팅
		if (!_.isNil(res) && res.length > 0) {
			// rawDataList 셋팅
			rawDataList = _.concat(rawDataList, res);

			// 다음 페이지 셋팅
			body.reportRequest.offset = body.reportRequest.offset + DATA_LIMIT;
		}
	}

	logger.debug(NON_RK_LOGGER, `[inmobi.service :: _requestReport] 리포트 데이터 생성 요청 완료 ( rawDataList.length = ${rawDataList.length} )`);

	return rawDataList;
};


/**
 * SUBTASK 3-2. _getDataList : 리포트 데이터 가공하기
 *
 * @param {Object} { fields: { ymd, country, imp, clk, net_revenue }, rawDataList, countryMappings }
 * @return {Array} dataList
 */
const _getDataList = async ({ fields: { ymd, country, imp, clk, net_revenue }, rawDataList, countryMappings }) => {
	logger.debug(NON_RK_LOGGER, `[inmobi.service :: _getDataList] 리포트 데이터 가공`);

	let dataList = new Array();

	rawDataList.map(rawData => {
		// imp, clk, net_revenue 가 모두 0인 건들은 걸러낸다.
		if (_.isEqual(rawData[imp], 0) && _.isEqual(rawData[clk], 0) && _.isEqual(rawData[net_revenue], 0)) {
			return;
		}

		let data = Object.assign({}, rawData);

		// GFP에서 지원하는 타겟 국가인 경우, 국가 코드로 변환
		data[country] = (!_.isNil(countryMappings[data[country]])) ? countryMappings[data[country]] : data[country];

		// 리포트 파일(csv) 생성 시, clk 은 optional 이라 0인 경우 공백 처리되므로, 스트링으로 변환해준다.
		data[clk] = data[clk] + '';

		dataList.push(data);
	});

	// 날짜별 정렬
	dataList = _.orderBy(dataList, ymd, 'asc');

	return dataList;
};


/**
 * _requestApi : request api 요청하기
 *
 * @param {Object} { url, method, headers, body }
 * @return {Object} result
 */
const _requestApi = async ({ url, method = 'GET', headers = {}, body = {} }) => {
	// logger.debug(NON_RK_LOGGER, `[inmobi.service :: _requestApi] ${method} 요청 URL ::: ${url}`);

	try {
		// timeout 10분 ( 600 초 )
		let res = await reportApiService.requestApi({ url, timeout: 600000, method, headers, body });

		// [ERROR] 응답 결과에 err가 있는 경우, 에러 처리
		if (!_.isNil(res.err)) {
			throw new BusinessError({ message: `[inmobi.service :: _requestApi] API 요청 실패 ::: statusCode(${res.status}), url(${url})` }, { err: res });
		}

		// [ERROR] error 가 true인 경우, 에러 처리 (ECONNRESET 여기서 잡힘)
		if (_.isEqual(res.error, true)) {
			throw new BusinessError({ message: `[inmobi.service :: _requestApi] ERROR ::: ${JSON.stringify(res, null, 2)}` }, { err: res });
		}

		const result = res.respList;

		return result;
	} catch (error) {
		// ECONNRESET ERROR 인 경우, 재요청을 위해 return 처리
		// 다른 ERROR 인 경우, 그대로 상위로 throw 처리
		if (!_.isNil(error) && !_.isNil(error.message) && _.includes(_.upperCase(error.message), 'ECONNRESET')) {
			logger.debug(NON_RK_LOGGER, `[inmobi.service :: _requestApi] ECONNRESET ERROR ::: ${error}`);

			return { code: 'ECONNRESET', error };
		} else {
			logger.debug(NON_RK_LOGGER, `[inmobi.service :: _requestApi] ERROR ::: ${JSON.stringify(error, null, 2)}\n`, error);

			throw error;
		}
	}
};
