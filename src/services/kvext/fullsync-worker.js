'use strict';

import os from 'os';
import fs from 'fs';
import _ from 'lodash';
import path from 'path';
import csv from 'csv-parser';
import fetch from 'node-fetch';
import mongoose from 'mongoose';
import moment from 'moment-timezone';
import { fork } from 'child_process';
import parallel_limit from 'run-parallel-limit';

import config from '../../config/config';
import * as logger from '../../utils/logger.util';
import * as HMAC from '../../utils/hmac.util';
import * as file from '../../utils/file.util';
import { sleep } from '../../utils/common.util';
import { BusinessError } from '../../common/error';

import { Publisher } from '../../models/publishers.schema';
import { ExtendedKeyValue } from '../../models/extended-key-values.schema';
import { ExtendedKeyValueJob } from '../../models/extended-key-value-jobs.schema';
import {
	ExtendedKeyValueFullSyncProcessingResult
} from '../../models/extended-key-value-fullsync-processing-results.schema';

const ObjectId = mongoose.Types.ObjectId;

const KVEXT_LOGGER = 'kvext';


/**
 * work : fullSync job 처리하기
 * 
 * @param {Object} job { _id, type, status, publisher_id, key, scheduledAt }
 */
module.exports.work = async (job) => {
	logger.debug(KVEXT_LOGGER, `[fullsync-worker :: work] 처리 시작 ( job_id:${job._id}, publisher_id:${job.publisher_id}, key:${job.key} )`);

	try {
		// 0. 확장키 fullSync 정보 가져오기
		const fullSyncInfo = await getExtendedKeyValueFullSyncInfo(job);


		// 1. 인증된 URL 가져오기
		// 		- 추후 인증이 추가되는 경우, 별도 모듈로 추출해서 만들 것
		const authenticatedUrl = await getAuthenticatedUrl(fullSyncInfo);


		// 2. fullSync 파일 다운로드 경로 가져오기
		const filePath = await getFilePath(job);


		// 3. fullSync 파일 다운로드 (API 요청)
		// 3-1. 파일 다운로드
		await requestFullSyncApi({ authenticatedUrl, filePath });

		// 3-2. 파일 라인별 유효한 컬럼 값인지 검증
		await validateColumn(filePath);


		/* 4. 대량 데이터 병럴 처리 */
		// 4-1. 파일 라인 수 체크
		const totalLineCount = await getFileTotalLineCount(filePath);

		logger.debug(KVEXT_LOGGER, `[fullsync-worker :: work] file totalLineCount :: ${totalLineCount}`);

		// 4-2. chunk_size(500000) 단위로 끊어서 ExtendedKeyValueFullSyncProcessingResults DB에 로깅
		const chunkInfos = await upsertExtendedKeyValueFullSyncProcessingResults({ job, filePath, totalLineCount, chunk_size: config.kvext_fullsync_chunk_size });

		// 4-3. publisher_id, key 를 기준으로 ExtendedKeyValues DB에서 일괄 삭제 처리
		await deleteExtendedKeyValues(job);

		// 4-4. CPU 개수만큼 ChildProcess 생성하여 병렬 처리하기
		if(!_.isEmpty(chunkInfos)) {
			await processParallelLimit({ chunkInfos, job, filePath });
		}


		/* 5. DB 총 건수 조회 */
		await sleep(10000);
		const totalCnt = await getTotalCnt(job);
		logger.debug(KVEXT_LOGGER, `[fullsync-worker :: work] DB totalCnt :: ${totalCnt}`);


		// job 상태 변경
		job.totalCnt = totalCnt;
		job.status = 'COMPLETE';
	} catch (err) {
		logger.error(`[fullsync-worker :: work] Error :: \n${err.stack}\n\n`, JSON.stringify(err, null, 2));

		// job 상태 변경
		job.error = err;
		job.status = 'FAILURE';
	} finally {
		// 6. job 처리 결과 DB 반영
		await updateExtendedKeyValueJobsStatus(job);
	}
};


/**
 * 0. getExtendedKeyValueFullSyncInfo : 확장키 fullSync 정보 가져오기
 * 
 * @param {Object} job { publisher_id, key }
 * @return {Object} fullSyncInfo { url, hmac, hmacKey }
 */
const getExtendedKeyValueFullSyncInfo = async ({ publisher_id, key }) => {
	logger.debug(KVEXT_LOGGER, '[fullsync-worker :: getExtendedKeyValueFullSyncInfo] 확장키 fullSync 정보 가져오기');

	let fullSyncInfo = await Publisher.aggregate()
		.match({ _id: publisher_id })
		.project({
			_id: 1,
			freeformKeyValues: {
				$filter: {
					input: '$freeformKeyValues',
					as: 'freeformKeyValue',
					cond: {
						$and: [
							{ $eq: [ '$$freeformKeyValue.extendable', 1 ] } ,
							{ $eq: [ '$$freeformKeyValue.fullSync.status', 'ON' ] } ,
							{ $eq: [ '$$freeformKeyValue.key', key ] } ,
						]
					}
				}
			}
		})
		.unwind('freeformKeyValues')
		.project({
			url: '$freeformKeyValues.fullSync.url',
			hmac: '$freeformKeyValues.fullSync.hmac',
			hmacKey: '$freeformKeyValues.fullSync.hmacKey',
		})
		.exec();

	fullSyncInfo = fullSyncInfo.pop() || null;

	// [ERROR] 확장키 정보가 없는 경우, 에러
	if (_.isNil(fullSyncInfo)) {
		throw new BusinessError({ message: `[fullsync-worker :: getExtendedKeyValueFullSyncInfo] 확장키 정보 없음 ( publisher_id= ${publisher_id}, key= ${key} )` });
	}

	return fullSyncInfo;
};


/**
 * 1. getAuthenticatedUrl : 인증된 URL 가져오기
 * 
 * @param {Object} fullSyncInfo { url, hmac, hmacKey }
 * @return {String} authenticatedUrl
 */
const getAuthenticatedUrl = async ({ url, hmac, hmacKey }) => {
	logger.debug(KVEXT_LOGGER, '[fullsync-worker :: getAuthenticatedUrl] 인증된 URL 가져오기');

	const isValidUrl = await _isValidUrl(url);

	// [ERROR] 유효한 URL이 아닌 경우, 에러
	if (isValidUrl === false) {
		throw new BusinessError({ message: `[fullsync-worker :: getAuthenticatedUrl] 유효한 URL 정보가 아님 ( url= ${url} )` });
	}

	// 인증된 URL
	let authenticatedUrl = url;

	// HMAC 인증인 경우
	if (_.isEqual(hmac, 1)) {
		// 인코딩된 URL
		const encodedUrl = _getEncodedUrl(url);

		// HMAC 인증된 URL
		authenticatedUrl = HMAC.getEncryptedUrlByKey({ url: encodedUrl, key: hmacKey });
	}

	logger.debug(KVEXT_LOGGER, `[fullsync-worker :: getAuthenticatedUrl] authenticatedUrl= ${authenticatedUrl}`);

	return authenticatedUrl;
};


/**
 * _isValidUrl : 유효한 URL 인지 체크
 * 
 * @param {String} url
 * @return {Boolean} isValidUrl
 */
const _isValidUrl = async url => {
	try {
		// 에러가 발생하면 캐치되면서 false를 반환한다.
		new URL(url);

		// 프로토콜이 http, https 가 아닌 경우, false
		const { protocol='' } = new URL(url);

		if (!_.includes(protocol, 'http')  && !_.includes(protocol, 'https')) {
			return false;
		}

		return true;
	} catch (err) {
		logger.debug(KVEXT_LOGGER, `[fullsync-worker :: isValidUrl] Error :: \n${err.stack}\n\n`, JSON.stringify(err, null, 2));

		return false;
	}
};


/**
 * _getEncodedUrl : 인코딩된 URL 가져오기
 * 
 * @param {String} url
 * @return {String} encodedUrl
 */
const _getEncodedUrl = url => {
	/* 
		ex> https://aaa:<EMAIL>:8080/search.naver?where=nexearch!@&sm=top_hty&fbm=1&ie=utf8&query=3dmp#hTest
		Url {
			protocol: 'https:', slashes: true, auth: 'aaa:bbb', 
			host: 'search.naver.com:8080', port: '8080', hostname: 'search.naver.com',
			hash: '#hTest', search: '?where=nexearch!@&sm=top_hty&fbm=1&ie=utf8&query=3dmp',
			query: 'where=nexearch!@&sm=top_hty&fbm=1&ie=utf8&query=3dmp',
			pathname: '/search.naver',
			path: '/search.naver?where=nexearch!@&sm=top_hty&fbm=1&ie=utf8&query=3dmp',
			href: 'https://aaa:<EMAIL>:8080/search.naver?where=nexearch!@&sm=top_hty&fbm=1&ie=utf8&query=3dmp#hTest' 
		}
	*/
	// URL 파싱
	const {
		protocol='', host='', pathname='', searchParams=null
	} = new URL(url);

	// searchParams 가 없는 경우, URL 그대로 반환
	let encodedUrl = url;

	if(!_.isEmpty(searchParams)) {
		// encodeURIComponent로 searchParams 인코딩 처리
		// blogId=blogdev10@&serviceId=gfp => blogId=blogdev10%40&serviceId=gfp
		const encodedQuery = searchParams.toString();

		// 인코딩된 URL
		encodedUrl = `${protocol}//${host}${pathname}?${encodedQuery}`;
	}

	logger.debug(KVEXT_LOGGER, `[fullsync-worker :: _getEncodedUrl] encodedUrl= ${encodedUrl}`);

	return encodedUrl;
};


/**
 * 2. getFilePath : fullSync 파일 다운로드 경로 가져오기
 * 
 * @param {Object} job { publisher_id, key, scheduledAt }
 * @return {String} filePath 'local_download/fullsync/2019/12/5dfae3f90ad82773a9358bba_spaceId_20191219_02.csv'
 */
const getFilePath = async ({ publisher_id, key, scheduledAt }) => {
	const dt = moment(scheduledAt);

	// commonPath = local_download/fullsync
	const commonPath = path.join(config.local_root, config.kvext_path);

	// monthlyPath = local_download/fullsync/2025/04
	const monthlyPath = file.getFilePathByMonthly(commonPath);

	// filePath = local_download/fullsync/2025/04/5dfae3f90ad82773a9358bba_spaceId_20250401_02.csv
	const filePath = `${monthlyPath}/${publisher_id}_${key}_${dt.format('YYYYMMDD')}_${dt.format('HH')}.csv`;

	// 파일이 존재하면
	if(fs.existsSync(filePath)) {
		// 임시 파일로 변경
		fs.renameSync(filePath, filePath + '.' + moment().format('YYYYMMDDHHmmss') + '.tmp');
	}

	return filePath;
};


/**
 * 3-1. requestFullSyncApi : fullSync API 요청 및 파일 다운로드
 * 
 * @param {Object} { authenticatedUrl, filePath }
 */
const requestFullSyncApi = async ({ authenticatedUrl, filePath }) => {
	logger.debug(KVEXT_LOGGER, '[fullsync-worker :: requestFullSyncApi] fullSync API 요청 및 파일 다운로드');


	return new Promise(async (resolve, reject) => {
		try {
			// 파일 다운로드 요청
			const res = await fetch(authenticatedUrl, {
				method:'GET',
				timeout: 900000, // timeout 15분 (1000 * 60 * 15)
			});

			// 응답이 200이 아닌 경우, 에러 처리
			if (!_.isEqual(res.status, 200)) {
				// [ERROR] 파일 다운로드 응답 에러 처리
				reject({ message: '[fullsync-worker :: requestFullSyncApi] 파일 다운로드 응답 에러', err: { status: res.status } });
				return;
			}

			// [이벤트] 요청 종료
			res.body.on('end', () => {
				logger.debug(KVEXT_LOGGER, `[fullsync-worker :: requestFullSyncApi] 파일 다운로드 요청 완료`);
			});

			// [이벤트] 요청 에러
			res.body.on('error', err => {
				reject({ message: `[fullsync-worker :: requestFullSyncApi] 파일 다운로드 요청 에러`, err });
			});

			// 파일 쓰기 스트림 생성
			const fileStream = fs.createWriteStream(filePath);

			// [이벤트] 파일 쓰기 종료
			fileStream.on('close', () => {
				logger.debug(KVEXT_LOGGER, `[fullsync-worker :: requestFullSyncApi] 파일 다운로드 완료 ::: ${filePath}`);

				resolve(filePath);
			});

			// [이벤트] 파일 쓰기 에러
			fileStream.on('error', err => {
				fileStream.end();

				// [ERROR] 파일 다운로드 에러 처리
				reject({ message: '[fullsync-worker :: requestFullSyncApi] 파일 다운로드 에러', err });
			});

			res.body.pipe(fileStream);
		} catch(err) {
			reject({ message: `[fullsync-worker :: requestFullSyncApi] Error ${JSON.stringify(err, null, 2)}`, err });
			return;
		}
	});
};


/**
 * 3-2. validateColumn : 파일 라인별 유효한 컬럼 값인지 검증
 *
 * @param {String} filePath 파일 경로
 */
const validateColumn = filePath => {
	logger.debug(KVEXT_LOGGER, '[fullsync-worker :: validateColumn] 호출됨');

	return new Promise((resolve, reject) => {
		let lineCount = 0;

		const fileStream = fs.createReadStream(filePath);
		const csvStream = csv({ separator: ',' });

		csvStream
			.on('data', row => {
				try {
					lineCount++;

					const headers = Object.keys(row);

					for (const header of headers) {
						// csv 값이 유효하지 않은 경우, undefined 로 처리됨
						// ex> Row { blogId: 'fo6b', bid: undefined, blogGrade: undefined, spaceId: undefined, influencerGrade: undefined, calp: undefined, adpost_status: undefined }
						if (row[header] === undefined) {
							logger.debug('row = ', row);
							throw new BusinessError({ message: `[fullsync-worker :: validateColumn] 파일에 유효하지 않은 컬럼 값이 있음 ( lineNumber = ${lineCount}, row = ${JSON.stringify(row)} )` });
						}
					}
				} catch (err) {
					reject({ message: `[fullsync-worker :: validateColumn] csvStream data 에러`, err });

					csvStream.destroy();

					closeStreams();
				}
			})
			.on('end', () => {
				logger.debug(KVEXT_LOGGER, '[fullsync-worker :: validateColumn] csvStream end');

				resolve();
			})
			.on('error', err => {
				reject({ message: `[fullsync-worker :: validateColumn] fileStream 에러`, err });

				closeStreams();
			});

		fileStream
			.on('end', () => {
				logger.debug(KVEXT_LOGGER, '[fullsync-worker :: validateColumn] fileStream end');
			})
			.on('error', err => {
				logger.debug(KVEXT_LOGGER, '[fullsync-worker :: validateColumn] fileStream error');

				closeStreams();
			});

		fileStream.pipe(csvStream);

		// 모든 stream 종료 처리
		const closeStreams = () => {
			if (csvStream) csvStream.end();
			if (fileStream) fileStream.close();
		};
	});
};


/**
 * 4-1. getFileTotalLineCount : 파일 총 라인수 가져오기
 * 
 * @param {String} filePath 파일 경로
 * @return {Number} totalLineCount
 */
const getFileTotalLineCount = filePath => {
	logger.debug(KVEXT_LOGGER, '[fullsync-worker :: getFileTotalLineCount] 호출됨');

	return new Promise((resolve, reject) => {
		const NEW_LINE = '\n'.charCodeAt(0);
	
		let index = 0;
		let last_chunk = '';
		let totalLineCount = -1; // 헤더 정보 제외
		const fileStream = fs.createReadStream(filePath);

		fileStream.on('data', chunk => {
			try {
				last_chunk = chunk[chunk.length - 1];

				for (index = 0; index < chunk.length; ++index) {
					if (chunk[index] === NEW_LINE) totalLineCount++;
				}
			} catch (err) {
				reject({ message: `[fullsync-worker :: getFileTotalLineCount] fileStream data 에러`, err });
			}
		})
		.on('end', () => {
			// 파일 끝에 개행문자가 없는 경우, 1건 추가
			if(last_chunk !== NEW_LINE) {
				totalLineCount++;
			}

			resolve(totalLineCount);
		})
		.on('error', err => {
			reject({ message: `[fullsync-worker :: getFileTotalLineCount] fileStream 에러`, err });

			if (fileStream) fileStream.close();
		});
	});
};


/**
 * 4-2. upsertExtendedKeyValueFullSyncProcessingResults : chunk_size(500000) 단위로 끊어서 ExtendedKeyValueFullSyncProcessingResults DB에 로깅
 * 
 * @param {Object} fullSyncProcessingInfo { job: { _id, publisher_id, key, scheduledAt }, filePath, totalLineCount, chunk_size }
 * @return {Array} chunkInfos [{ chunkNumber, startLine, endLine, state, successCount }]
 */
const upsertExtendedKeyValueFullSyncProcessingResults = async ({ job: { _id, publisher_id, key, scheduledAt }, filePath, totalLineCount, chunk_size }) => {
	logger.debug(KVEXT_LOGGER, `[fullsync-worker :: upsertExtendedKeyValueFullSyncProcessingResults] 파일을 ${chunk_size} 단위로 끊어서 DB에 로깅`);

	const today = moment();

	// 반환할 chunkInfo 리스트
	let chunkInfos = new Array();

	// 파일 쪼개기
	const chunkCount = _.floor(totalLineCount/chunk_size); // 총 청크 수
	const chunkMod = totalLineCount % chunk_size; // 마지막 청크 사이즈(나머지들)

	// chunk 정보 생성하기
	for(let i = 0; i <= chunkCount; i++) {
		if(i === chunkCount && chunkMod === 0) break;

		chunkInfos.push({
			chunkNumber: i + 1,
			startLine: (i * chunk_size) + 1,
			endLine: (i !== chunkCount) ? (i * chunk_size) + chunk_size : (i * chunk_size) + chunkMod,
			state: 'READY',
			successCount: 0,
		});
	}

	await ExtendedKeyValueFullSyncProcessingResult.findOneAndUpdate(
		{ job_id: _id, publisher_id, key, scheduledAt, filePath },
		{ 
			$set: { 
				totalLineCount, 
				totalSuccessCount: 0,
				chunkInfos,
				state: _.isEmpty(chunkInfos) ? 'NO_DATA' : 'READY',
				createdAt: today,
				modifiedAt: today
			}
		},
		{ new: true, runValidators: true, upsert: true }
	).exec();

	return chunkInfos;
};


/**
 * 4-3. deleteExtendedKeyValues : publisher_id, key 를 기준으로 ExtendedKeyValues DB에서 일괄 삭제 처리
 * 
 * @param {Object} job { publisher_id, key }
 */
const deleteExtendedKeyValues = async ({ publisher_id, key }) => {
	logger.debug(KVEXT_LOGGER, `[fullsync-worker :: deleteExtendedKeyValues] publisher_id=${publisher_id}, key=${key} 를 기준으로 데이터 일괄 삭제 처리 시작`);

	const bulk = ExtendedKeyValue.collection.initializeUnorderedBulkOp();
	bulk.find({ publisher_id, key }).delete();
	const results = await bulk.execute();

	logger.debug(KVEXT_LOGGER, `[fullsync-worker :: deleteExtendedKeyValues] ${results.result.nRemoved} 건 삭제 완료 ::: publisher_id=${publisher_id}, key=${key}`);

	logger.debug(KVEXT_LOGGER, `[fullsync-worker :: deleteExtendedKeyValues] publisher_id=${publisher_id}, key=${key} 를 기준으로 데이터 일괄 삭제 처리 완료`);
};


/**
 * 4-4. processParallelLimit : CPU 개수만큼 ChildProcess 생성하여 병렬 처리하기
 * 
 * @param {Object} metaInfo { chunkInfos, job, filePath }
 */
const processParallelLimit = ({ chunkInfos, job, filePath }) => {
	logger.debug(KVEXT_LOGGER, '[fullsync-worker :: processParallelLimit] ChildProcess 생성 및 병렬 처리');

	return new Promise((resolve, reject) => {
		// CPU 개수 구하기
		let cpuNum = os.cpus().length - 1; // 부모 프로세스 빼기
		if (chunkInfos.length < cpuNum) {
			cpuNum = chunkInfos.length;
		}

		logger.debug(KVEXT_LOGGER, `[fullsync-worker :: processParallelLimit] cpu 개수 : ${cpuNum}`);

		let tasks = chunkInfos.map(chunkInfo => {
			return (async (callback) => {
				// ChildProcess 생성하기
				const { err, dataCount } = await _callChildProcess({ chunkInfo, job, filePath });

				// 처리 완료 상태 ExtendedKeyValueFullSyncProcessingResults DB 업뎃
				await _updateExtendedKeyValueFullSyncProcessingResults({ job, filePath, chunkNumber: chunkInfo.chunkNumber, successCount: dataCount });

				// err가 있으면 FAILED 처리됨
				callback(err);
			});
		});

		parallel_limit(tasks, cpuNum, (err) => {
			if (err) {
				// [ERROR] 에러 처리
				reject({ message: '[fullsync-worker :: processParallelLimit] 에러', err });
			} else {
				resolve();
			}
		});
	});
};


/**
 * [Promise]
 * _callChildProcess : ChildProcess 생성하기
 * 
 * @param {Object} metaInfo { chunkInfo, job, filePath }
 */
const _callChildProcess = ({ chunkInfo, job, filePath }) => {
	return new Promise((resolve, reject) => {
		logger.debug(KVEXT_LOGGER, `[fullsync-worker :: _callChildProcess] chunkNumber #${chunkInfo.chunkNumber} START :: ${moment().format('YYYY-MM-DD HH:mm:ss')}`);

		// 자식 프로세스 생성
		const childProcess = fork(path.join(process.cwd(), '/src/kvextFullSyncApp.js'));

		// 자식 프로세스로부터 받은 처리 결과 메시지
		childProcess.on('message', (res) => {
			// 자식프로세스 종료
			childProcess.disconnect();

			// [ERROR] 에러 처리
			if(res.state === 'FAILED') {
				resolve({
					err: new BusinessError({ message: `[fullsync-worker :: _callChildProcess] chunkNumber #${chunkInfo.chunkNumber} 처리 FAILED 에러` }, { err: res, detail: JSON.stringify(res, null, 2)})
				});
			}

			if (res.state === 'COMPLETE') {
				logger.debug(KVEXT_LOGGER, `[fullsync-worker :: _callChildProcess] chunkNumber #${chunkInfo.chunkNumber} END :: ${moment().format('YYYY-MM-DD HH:mm:ss')}\n`);

				resolve({ dataCount: res.dataCount });
			}
		});

		// [ERROR] 에러 처리
		childProcess.on('error', err => {
			resolve({
				err: new BusinessError({ message: `[fullsync-worker :: _callChildProcess] chunkNumber #${chunkInfo.chunkNumber} 자식 프로세스 에러` }, { err: err, detail: JSON.stringify(err, null, 2)})
			});
		});

		// 프로세스가 준비될 때까지 기다렸다가 실행
		setTimeout(() => {
			childProcess.send({ chunkInfo, job, filePath });
		}, 2000);
	});
};


/**
 * _updateExtendedKeyValueFullSyncProcessingResults : 처리 완료 상태 ExtendedKeyValueFullSyncProcessingResults DB 업뎃
 * 
 * @param {Object} metaInfo { job, filePath, chunkNumber, successCount }
 */
const _updateExtendedKeyValueFullSyncProcessingResults = async ({ job: { _id, publisher_id, key, scheduledAt }, filePath, chunkNumber, successCount=0 }) => {
	logger.debug(KVEXT_LOGGER, `[fullsync-worker :: _updateExtendedKeyValueFullSyncProcessingResults] ChunkNumber #${chunkNumber} 처리 완료 상태 DB 업뎃`);

	const result = await ExtendedKeyValueFullSyncProcessingResult.findOneAndUpdate(
		{ job_id: _id, publisher_id, key, scheduledAt, filePath, 'chunkInfos.chunkNumber': chunkNumber },
		{
			$set: { 
				'chunkInfos.$.successCount' : successCount, 
				'chunkInfos.$.state' : 'COMPLETE', 
			},
			$inc: {
				'totalSuccessCount' : successCount,
			}
		},
		{ new: true, runValidators: true }
	).exec();

	// [ERROR] update 결과가 없는 경우, 에러 처리
	if(_.isNil(result) && _.isEmpty(result) && _.isNil(result.chunkInfos)) {
		throw new BusinessError({ message: `[fullsync-worker :: _updateExtendedKeyValueFullSyncProcessingResults] ChunkNumber #${chunkNumber} ExtendedKeyValueFullSyncProcessingResults DB 저장 오류` }, { err: result, detail: JSON.stringify(result, null, 2)});
	}

	const completeCnt = result.chunkInfos.filter(({ state }) => _.isEqual(state, 'COMPLETE')).length;

	// 모든 CHUNK 상태가 'COMPLETE'인 경우, 배치 전체 상태도 'COMPLETE' 으로 변경한다.
	if(result.chunkInfos.length === completeCnt) {
		await ExtendedKeyValueFullSyncProcessingResult.findOneAndUpdate(
			{ job_id: _id, publisher_id, key, scheduledAt, filePath },
			{ 
				$set: { 
					state: 'COMPLETE',
					modifiedAt: moment()
				}
			},
			{ new: true, runValidators: true }
		).exec();
	}
};


/**
 * 5. getTotalCnt : DB 총 건수 조회
 *
 * @param {Object} job { publisher_id, key }
 */
const getTotalCnt = async ({ publisher_id, key }) => {
	// https://mongoosejs.com/docs/5.x/docs/api/aggregate.html#aggregate_Aggregate-read
	let result = await ExtendedKeyValue.aggregate([
		{ $match: { publisher_id: ObjectId(publisher_id), key } },
		{ $count: 'nDocs' }
	]).allowDiskUse(true).read('primary').readConcern('linearizable').exec();

	result = result.pop();

	const totalCnt = result ? result.nDocs : 0;

	return totalCnt;
};


/**
 * 6. updateExtendedKeyValueJobsStatus : job 처리 결과 DB 반영
 * 
 * @param {Object} job { _id, status, totalCnt, error }
 */
const updateExtendedKeyValueJobsStatus = async ({ _id, status, totalCnt=0, error }) => {
	logger.debug(KVEXT_LOGGER, '[fullsync-worker :: updateExtendedKeyValueJobsStatus] Job 처리 결과 ExtendedKeyValueJob DB에 반영');

	let data = {
		status,
		totalCnt,
		modifiedAt: moment(),
		endedAt: moment()
	};

	if (!_.isNil(error)) {
		data.error = error;
	}

	const result = await ExtendedKeyValueJob.findOneAndUpdate(
		{ _id },
		{ $set: data, $inc: { 'retryCnt' : status === 'FAILURE' ? 1 : 0 } },
		{ new: true, runValidators: true }
	).exec();

	// [ERROR] update 결과가 없는 경우, 에러 처리
	if(_.isNil(result) && _.isEmpty(result)) {
		throw new BusinessError({ message: `[fullsync-worker :: updateExtendedKeyValueJobsStatus] ExtendedKeyValueJob DB 저장 오류` }, { err: result, detail: JSON.stringify(result, null, 2)});
	}
};
