'use strict';

import os from 'os';
import _ from 'lodash';
import path from 'path';
import mongoose from 'mongoose';
import moment from 'moment-timezone';
import child_process from 'child_process';

import config from '../../config/config';

import extendedLogger from '../../utils/logger-ext.util';

import { BusinessError } from '../../common/error';

import * as commonUtil from '../../utils/common.util';
import { Publisher } from "../../models/publishers.schema";
import { ExtendedKeyValue } from "../../models/extended-key-values.schema";
import { ExtendedKeyValueJob } from "../../models/extended-key-value-jobs.schema";
import { KvextManager } from "./kvext-manager";
import { Environments } from "../../models/environments.schema";
import * as mailer from "../../utils/mail.util";

let ObjectId = mongoose.Types.ObjectId;

const log = extendedLogger('kvext'); // 확장로거

const KVEXT_MANAGER_TABLE = new Map();
const INTERVAL = 5000; // job을 가져오는 주기
let deliverExecutor, triggerExecutor;
let shutdowning = false;


/**
 * Kvext 서비스 시작
 * @returns {Promise.<void>}
 */
module.exports.startup = async () => {
	log.debug('프로파일:', process.env.NODE_ENV, 'oshostname:', process.env.HOST_HOSTNAME);
	const env = await Environments.findOne({'name': 'kvext-hostname-to-run-batch'}, {'value': 1});
	if ((process.env.NODE_ENV !== 'local' && !_.isNil(env.value) && env.value === process.env.HOST_HOSTNAME) ||
		(process.env.NODE_ENV === 'local' && os.hostname === 'JuyounKim')) {
		// 미처리 분을 재처리 상태로 업데이트
		await _updateOngoingToRetry();

		// 확장키 별 매니저 생성 및 등록
		await _registerManagers();

		// 키값 변경사항 주기적 확인
		_launchTrigger();
		log.debug(`[KVEXT] 트리거 런칭 완료.`);

		// 주기적으로 job을 조회해서 매니저에게 전달
		_launchDelivery();
		log.debug(`[KVEXT] 배달부 런칭 완료.`);

		shutdowning = false;
	} else {
		log.info(`[KVEXT] Kvext Batch를 실행할 서버가 아니므로 스킵`);
	}
};


/**
 * Kvext 서비스 셧다운
 * @returns {Promise.<void>}
 */
module.exports.shutdown = async () => {
	const env = await Environments.findOne({'name': 'kvext-hostname-to-run-batch'}, {'value': 1});
	if ((process.env.NODE_ENV !== 'local' && !_.isNil(env.value) && env.value === process.env.HOST_HOSTNAME) ||
		(process.env.NODE_ENV === 'local' && os.hostname === 'JuyounKim')) { // 내가 키값확장 배치 실행자였다면
		shutdowning = true;

		log.info(`[KVEXT] 셧다운 시그널 받음`);

		clearTimeout(deliverExecutor);
		log.info(`[KVEXT] 배달부 종료`);

		clearTimeout(triggerExecutor);
		log.info(`[KVEXT] 주기적 트리거 종료`);

		await _waitKvext();
		log.info(`[KVEXT] 진행중인 작업 대기 종료`);

		_endManager();
		log.info(`[KVEXT] 모든 매니저 종료`);
	} else {
		log.info(`[KVEXT] 배치를 실행할 서버가 아니므로 shutdown 절차 스킵`);
	}
};


/**
 * 매니저 리스트 조회
 * @returns {Promise.<void>}
 */
module.exports.getManagerIds = () => {
	const managerIds = [];

	const managers = KVEXT_MANAGER_TABLE.values();
	for (const manager of managers) {
		managerIds.push(manager.getManagerId());
	}

	return managerIds;
};


/**
 * 진행중인 Job 목록 조회
 * @returns {Array}
 */
module.exports.getOngoingJobs = async () => {
	const jobs = await ExtendedKeyValueJob.find({'status': {'$in': ['ONGOING', 'STANDBY']}});
	return jobs;
};


/**
 * 재처리 대상으로 업데이트
 * @returns {Promise.<void>}
 * @private
 */
const _updateOngoingToRetry = async () => {
	await ExtendedKeyValueJob.updateMany(
		{'status': {'$in': ['ONGOING', 'STANDBY']}},
		{'$set': {'status': 'RETRY', 'modifiedAt': moment()}}
	);
};


/**
 * 진행중인 FullSync Job 목록 조회
 * @returns {Promise.<*>}
 */
const getOngoingFullSyncJobs = async () => {
	const jobs = await ExtendedKeyValueJob.find({'status': 'ONGOING', 'type': 'F'});
	return jobs;
};


/**
 * Publishers.freeformKeyValues.$.modifiedAt을 이용한 주기적인 트리거
 * @private
 */
const _launchTrigger = () => {
	log.info(`[KVEXT] 주기적인 트리거 런처 시작 ..`);

	triggerExecutor = setTimeout(async function myTimer() {
		await _trigger();
		clearTimeout(triggerExecutor);
		triggerExecutor = setTimeout(myTimer, INTERVAL);
	}, INTERVAL);

	log.info(`[KVEXT] 주기적인 트리거 런처 시작됨.`);
};


/**
 * Admin > Key 관리 > Key > 확장키로 선택 및 해제 시 호출됨.
 * @param publisherId
 * @param key
 * @returns {Promise.<void>}
 */
const _trigger = async () => {
	const env = await Environments.findOne({'name': 'kvext-last-modified-at'}).select('value');
	let lastModifiedAt = env.value;

	const pubs = await _getModifiedKeys(lastModifiedAt);

	if (_.isNil(pubs) || _.isEmpty(pubs)) {
		return
	}

	for (const pub of pubs) {
		log.debug(`[KVEXT] 트리거 :: pub: ${JSON.stringify(pub, null, 2)}`);

		if (_.isNil(pub) || _.isEmpty(pub)) {
			continue;
		}

		if (_.isNil(pub.freeformKeyValues) || _.isEmpty(pub.freeformKeyValues)) {
			continue;
		}

		const publisherId = pub._id;

		for (const keyValue of pub.freeformKeyValues) {
			/*
				// kvext
				{
					"_id" : ObjectId("5dba9e990fc112003272877e"),
					"key" : "spaceId",
					"name" : "인플루언서시스템ID",
					"fullSync" : {
						"url" : "http://www.naver.com",
						"basedAt" : ISODate("2019-12-10T00:00:00.000+09:00"),
						"interval" : "1",
						"time" : "02",
						"status" : "ON"
					},
					"extendable" : 1,
					"creatorName" : "박가려",
					"createdAt" : ISODate("2019-10-31T17:43:05.750+09:00"),
					"modifiedAt" : ISODate("2019-12-17T11:36:09.267+09:00")
				}
			 */
			const key = keyValue.key;

			const prefix = `[KVEXT] 트리거 :: pub:${pub._id} keyValue:${keyValue}`;
			lastModifiedAt = _.max([keyValue.modifiedAt, lastModifiedAt]);

			if (keyValue.extendable == 0) {
				_clean(publisherId, key); // 자원 해제
			} else {
				const isFullSyncOn = keyValue.fullSync.status == 'ON' ? true : false;
				const managerId = _formIndivisualManagerId(publisherId, key);

				// 없으니 생성
				if (!KVEXT_MANAGER_TABLE.has(managerId)) {
					log.info(`${prefix} new manager is created. managerId:${managerId}`);
					_createKvextManager(publisherId, key, isFullSyncOn);
				} else {
					const manager = KVEXT_MANAGER_TABLE.get(managerId);

					if (isFullSyncOn) {
						manager.resumeFullSync(); // full sync ON
					} else {
						manager.pauseFullSync(); // full sync OFF
					}
				}
			}
		}
	}

	await Environments.updateOne(
		{'name': 'kvext-last-modified-at'},
		{'$set': {'value': lastModifiedAt, 'modifiedAt': moment()}}
	);
};


/**
 * 배달부 시작
 * @private
 */
const _launchDelivery = () => {
	log.info(`[KVEXT] 배달부 시작 ..`);

	deliverExecutor = setTimeout(async function myTimer() {
		await _deliver();
		clearTimeout(deliverExecutor);
		deliverExecutor = setTimeout(myTimer, INTERVAL);
	}, INTERVAL);

	log.info(`[KVEXT] 배달부 작업 설정 완료.`);
};


/**
 * 주기적으로 job을 가져와서 매니저에게 배달
 * @returns {Promise}
 */
const _deliver = () => {
	return new Promise(async (resolve, reject) => {
		try {
			// job 가져오기
			const jobs = await _getJobs();

			if (!_.isNil(jobs) && !_.isEmpty(jobs)) {
				log.debug(`[KVEXT] 배달부 :: 가져온 jobs.length: ${jobs.length}`);

				// 각 job을 매니저에게 전달
				for (const job of jobs) {
					const managerId = _formIndivisualManagerId(job.publisher_id, job.key);
					const kvextManager = KVEXT_MANAGER_TABLE.get(managerId);

					// 셧다운 중일 때는 처리하지 않음.
					if (shutdowning) {
						break;
					}

					if (!_.isNil(kvextManager)) {
						const saturation = await _getSaturation(); // 매니저가 큐에 가질 수 있는 최대 크기
						if (kvextManager.getQueueSize() >= saturation) {
							log.info(`[KVEXT] 배달부 :: managerId:${managerId}의 큐가 포화(${kvextManager.getQueueSize()} 이상)되어 이번 job(${job._id}) 배달 스킵`);
						} else {
							await _markStatusAsStandby(job._id);
							kvextManager.deliver(job); // 매니저에 배달
						}
					} else {
						log.error(`[KVEXT] 배달부 :: job을 가져왔으나 해당하는 매니저(${managerId})가 없음.`);
						await _markStatusAsFailureForUnexpectedKey(job);
					}
				}
			}
		} catch (error) {
			log.error(`[KVEXT] 배달 중 에러 발생. ${error.stack}`);
		}

		resolve();
	});
};


/**
 * 상태 표시 - 진행중
 * @param jobId
 * @returns {Promise.<void>}
 * @private
 */
const _markStatusAsStandby = async (jobId) => {
	await ExtendedKeyValueJob.updateOne(
		{_id: jobId},
		{'$set': {'status': 'STANDBY', 'modifiedAt': moment()}}
	);
};


/**
 * 상태 표시 - 실패
 *
 * @param jobId
 * @returns {Promise.<void>}
 * @private
 */
const _markStatusAsFailureForUnexpectedKey = async (job) => {
	const update = {
		'status': 'FAILURE',
		'modifiedAt': moment(),
		'endedAt': moment(),
		'error': 'unexpected extension key: ' + job.key
	}

	const jobFromDB = await ExtendedKeyValueJob.findOne({_id: job._id}, {retryCnt: 1});

	if (_.isNil(jobFromDB.retryCnt)) { // 처음 실패한 경우
		update['retryCnt'] = 0;
		update['alarm'] = 0;
	} else {
		update['retryCnt'] = jobFromDB.retryCnt + 1;
	}

	await ExtendedKeyValueJob.updateOne(
		{_id: job._id},
		{'$set': update}
	);
};


/**
 * 진행중인 작업 대기
 * @returns {Promise.<void>}
 * @private
 */
const _waitKvext = async () => {
	const env = await Environments.findOne({'name': 'kvext-shutdown-delay-seconds-to-wait-for'}, 'value');
	const secondsToWait = parseInt(env.value);

	let ellapsedSeconds = 1;
	while (true) {
		try {
			if (ellapsedSeconds > secondsToWait) {
				log.info(`[KVEXT] 최대 대기시간(${secondsToWait}초) 초과로 FullSync 대기 취소.`);
				break;
			} else {
				const jobs = await getOngoingFullSyncJobs();
				const fullSyncJobIds = jobs.map(job => job._id);

				if (_.isNil(fullSyncJobIds) || _.isEmpty(fullSyncJobIds)) {
					break;
				} else {
					log.info(`[KVEXT] 진행중인 FullSync 작업이 있어 셧다운 대기 ... ${ellapsedSeconds} FullSyncJobIds:${JSON.stringify(fullSyncJobIds, null, 2)}`);
					await commonUtil.sleep(1000);
				}

				ellapsedSeconds++;
			}
		} catch (error) {
			log.error(`[KVEXT] 대기 중 에러 발생: ${error.stack}`);
		}
	}
};


/**
 * 확장키 별 매니저 생성 및 등록
 * @returns {Promise.<void>}
 * @private
 */
const _registerManagers = async () => {
	// 확장키 목록 조회
	const list = await _getCheckedKeys();

	if (_.isNil(list) || _.isEmpty(list)) {
		return;
	}

	// 확장키 별 매니저 생성 및 등록
	for (let i = 0; i < list.length; i++) {
		const pub = list[i];

		if (_.isNil(pub.freeformKeyValues) || _.isEmpty(pub.freeformKeyValues)) {
			continue;
		}

		for (let j = 0; j < pub.freeformKeyValues.length; j++) {
			const freeformKeyValue = pub.freeformKeyValues[j];
			const isFullSyncOn = (freeformKeyValue.fullSync.status === 'ON' ? true : false);
			_createKvextManager(pub._id, freeformKeyValue.key, isFullSyncOn)
		}
	}
};


/**
 * 모든 매니저 종료
 * @returns {Promise.<void>}
 * @private
 */
const _endManager = () => {
	const managers = KVEXT_MANAGER_TABLE.values();
	for (const manager of managers) {
		_removeKvextManager(manager.getPublisherId(), manager.getKey());
	}
};


/**
 * 선택된 키 목록 조회
 * @returns {Promise.<*>}
 * @private
 */
const _getCheckedKeys = async () => {
	const list = await Publisher.aggregate([
		{
			'$match': {
				'status': 'ON',
				'freeformKeyValues.extendable': 1
			}
		},
		{
			'$project': {
				'_id': '$_id',
				'freeformKeyValues': {
					'$filter': {
						'input': "$freeformKeyValues",
						'as': "key",
						'cond': {'$eq': ["$$key.extendable", 1]}
					}
				}
			}
		}
	]);

	return list;
};


/**
 * 확장키 정보 조회
 * @param publisherId
 * @param key
 * @returns {Promise.<*>}
 * @private
 */
const _getModifiedKeys = async (lastModifiedAt) => {
	const list = await Publisher.aggregate([
		{
			'$match': {
				'status': 'ON',
				'freeformKeyValues.modifiedAt': {'$gt': lastModifiedAt}
			}
		},
		{
			'$project': {
				'_id': '$_id',
				'freeformKeyValues': {
					'$filter': {
						'input': "$freeformKeyValues",
						'as': "kv",
						'cond': {'$gt': ["$$kv.modifiedAt", lastModifiedAt]}
					}
				}
			}
		}
	]);

	return list;
};


/**
 * 확장키 정보 조회
 * @param publisherId
 * @param key
 * @returns {Promise.<*>}
 * @private
 */
const _getKey = async (publisherId, key) => {
	const list = await Publisher.aggregate([
		{
			'$match': {
				'status': 'ON',
				'_id': ObjectId(publisherId),
				'freeformKeyValues.key': key
			}
		},
		{
			'$project': {
				'_id': '$_id',
				'freeformKeyValues': {
					'$filter': {
						'input': "$freeformKeyValues",
						'as': "kv",
						'cond': {'$eq': ["$$kv.key", key]}
					}
				}
			}
		}
	]);

	if (!_.isNil(list) && !_.isEmpty(list)) {
		return list[0];
	}

	return null;
};


/**
 * 확장키의 매니저 생성
 * @param publisherId
 * @param key
 * @private
 */
const _createKvextManager = (publisherId, key, isFullSyncOn) => {
	const manager = new KvextManager(publisherId, key, isFullSyncOn);
	KVEXT_MANAGER_TABLE.set(manager.managerId, manager);
	manager.runPeriodically();
};


/**
 * 해제된 확장키의 매니저 제거
 * @param publisherId
 * @param key
 */
const _removeKvextManager = (publisherId, key) => {
	const managerId = _formIndivisualManagerId(publisherId, key);

	if (KVEXT_MANAGER_TABLE.has(managerId)) {
		const manager = KVEXT_MANAGER_TABLE.get(managerId);

		// 매니저 종료
		manager.stop();

		// 매니저 테이블에서 삭제
		KVEXT_MANAGER_TABLE.delete(managerId);

		log.info(`[KVEXT] 매니저:${managerId}\t제거 완료. 남은 매니저:${KVEXT_MANAGER_TABLE.size}`); // ${JSON.stringify(KVEXT_MANAGER_TABLE, null, 2)}
	} else {
		log.debug(`[KVEXT] 매니저:${managerId}\t없음. 현재 매니저:${KVEXT_MANAGER_TABLE.size}`);
	}
};


/**
 * 매니저 ID 만들기
 * @type {function(*, *)}
 */
const _formIndivisualManagerId = (publisher_id, key) => {
	return publisher_id + '+' + key;
};


/**
 * 처리 목록 조회
 * @returns {Promise.<*>}
 * @private
 */
const _getJobs = async () => {
	let limit = 100; // default
	const env = await Environments.findOne({'name': 'kvext-job-limit'}, {'value': 1});
	if (!_.isNil(env.value)) {
		limit = parseInt(env.value);
	}

	// n개씩 잘라서 가져오기
	const jobs = await ExtendedKeyValueJob
		.find({
			'$or': [
				{'status': {'$in': ['READY', 'RETRY']}},

				// 키값 확장 전체 동기화 API 요청 시 에러 처리기능 개선 (https://jira.navercorp.com/browse/GFP-193)
				{'$and': [{'status': 'FAILURE'}, {'retryCnt': {'$lt': 3}}]},
			]
		})
		.sort({'scheduledAt': 1})
		.limit(limit);

	return jobs;
};


/**
 * 해제된 extensionKey의 ExtendedKeyValues 정리 및 매니저 제거
 * @returns {Promise.<void>}
 */
const _clean = (publisherId, key) => {
	return new Promise(async (resolve, reject) => {
		const prefix = `[KVEXT] 확장키 해제 :: publisherId:${publisherId} key:${key}`;

		try {
			// 키값 invalidate
			log.debug(`${prefix} 모든 key-value invalidate 시작 ...`);
			await ExtendedKeyValue.updateMany(
				{
					'publisher_id': ObjectId(publisherId),
					'key': key,
					'valid': 1
				},
				{
					'$set': {'valid': 0, 'modifiedAt': moment()}
				}
			);
			log.debug(`${prefix} 모든 key-value invalidate 완료.`);

			// 매니저 제거
			_removeKvextManager(publisherId, key);

			log.debug(`${prefix} 완료.`);

			resolve();
		} catch (error) {
			log.error(`${prefix} 데이터 정리 중 에러 발생. ${error.stack}`);

			reject(error);
		}
	});
};


const _getSaturation = async () => {
	const env = await Environments.findOne({name: 'kvext-job-saturation'}).select('value');
	if (!_.isNil(env) && !_.isNil(env.value)) {
		return parseInt(env.value);
	}
	return 1000;
};


/**
 * processFullSyncJob : fullSync job 일괄 추가
 *
 * @param {Object} option { publisher_id, key }
 */
module.exports.processFullSyncJob = async ({publisher_id, key}) => {
	log.debug('[kvext.service :: processFullSyncJob] 호출됨');

	// 1. 퍼블리셔-확장키별 fullSync 정보 가져오기
	const extendedKeyInfos = await _getExtendedKeyInfos({publisher_id, key});


	// 2. ExtendedKeyValueJobs 에 fullSync job 추가/수정하기
	const fullSyncJobs = await _upsertFullSyncJobs(extendedKeyInfos);


	log.debug('[kvext.service :: processFullSyncJob] 처리 완료');
};


/**
 * _getExtendedKeyInfos : 확장키별 fullSync 정보 가져오기
 *
 * @param {Object} option { publisher_id, key }
 * @return {Array} extendedKeyInfos [{ publisher_id, key, period, hour, dayOfWeek }]
 */
const _getExtendedKeyInfos = async ({publisher_id, key = ''}) => {
	let matchOption = {};
	if (!_.isNil(publisher_id)) {
		matchOption._id = ObjectId(publisher_id);
	}

	let filterCondition = [
		{$eq: ['$$freeformKeyValue.extendable', 1]},
		{$eq: ['$$freeformKeyValue.fullSync.status', 'ON']},
	];
	if (!_.isEmpty(key)) {
		filterCondition.push({$eq: ['$$freeformKeyValue.key', key]});
	}

	const extendedKeyInfos = await Publisher.aggregate()
		.match(matchOption)
		.project({
			_id: 1,
			freeformKeyValues: {
				$filter: {
					input: '$freeformKeyValues',
					as: 'freeformKeyValue',
					cond: {
						$and: filterCondition
					}
				}
			}
		})
		.unwind('freeformKeyValues')
		.project({
			_id: 0,
			publisher_id: '$_id',
			key: '$freeformKeyValues.key',
			period: '$freeformKeyValues.fullSync.period',
			hour: '$freeformKeyValues.fullSync.hour',
			dayOfWeek: '$freeformKeyValues.fullSync.dayOfWeek',
		})
		.exec();

	return extendedKeyInfos;
};


/**
 * _upsertFullSyncJobs : ExtendedKeyValueJobs에 fullSync job 추가/수정하기
 * 	수정인 경우, 오늘자 스케쥴에 대해서만 처리됨
 *
 * @param {Array} extendedKeyInfos [{ publisher_id, key, period, hour, dayOfWeek }]
 * @return {Array} fullSyncJobs
 */
const _upsertFullSyncJobs = async (extendedKeyInfos) => {
	log.debug('[kvext.service :: _upsertFullSyncJobs] 확장키 정보 ::: \n', extendedKeyInfos);

	const today = moment();
	let fullSyncJobs = new Array();

	extendedKeyInfos.map(({publisher_id, key, period, hour, dayOfWeek}) => {
		// 오늘 날짜가 배치 실행일에 해당되는지 체크 (DAY 는 매일 실행이므로 체크 안 함)
		if (_.isEqual(period, 'WEEK')) {
			// 오늘이 배치 주기 요일에 해당되지 않은 경우, 제외 처리
			const weekDay = moment().isoWeekday();
			if (!_.isEqual(weekDay, dayOfWeek)) {
				return;
			}
		}

		const scheduledAt = moment().startOf('day');
		scheduledAt.add(hour, 'hours');

		fullSyncJobs.push({
			updateOne: {
				filter: {type: 'F', publisher_id, key, scheduledAt},
				update: {
					$set: {
						status: 'READY',
						retryCnt: -1,
						alarm: 0,
						modifiedAt: today
					},

					// insert 시에만
					$setOnInsert: {
						createdAt: today
					},
				},
				upsert: true,
			}
		});
	});

	if (_.isEmpty(fullSyncJobs)) {
		log.debug('[kvext.service :: _upsertFullSyncJobs] fullSyncJobs is empty');

		return;
	}

	// fullSync Job 일괄 추가/수정
	let result = await ExtendedKeyValueJob.bulkWrite(fullSyncJobs);

	// [ERROR] 응답 오류가 있는 경우, 에러 처리
	if (result.ok !== 1 || !_.isEmpty(result.writeErrors)) {
		throw new BusinessError({message: `[kvext.service :: _upsertFullSyncJobs] DB 저장 오류`}, {
			err: result,
			detail: JSON.stringify(result, null, 2)
		});
	}

	log.debug('[kvext.service :: _upsertFullSyncJobs] fullSyncJobs ::: \n', JSON.stringify(fullSyncJobs, null, 2));

	return fullSyncJobs;
};


/**
 * deleteExtendedKeyValueJob : ExtendedKeyValue job 일괄 삭제
 * 	- 2주 전 Job 일괄 삭제 처리
 */
module.exports.deleteExtendedKeyValueJob = async () => {
	log.debug('[kvext.service :: deleteExtendedKeyValueJob] 호출됨');

	// 2주 전 날짜
	const twoWeeksAgo = moment().startOf('day').add(-13, 'days');

	// DB 일괄 삭제 청크 사이즈
	const DELETE_CHUNK_SIZE = config.delete_chunk_size || 50000;

	// 삭제 대상 ExtendedKeyValueJob 의 _id 리스트 가져오기
	let deleteIds = await ExtendedKeyValueJob.aggregate()
		.match({scheduledAt: {$lt: twoWeeksAgo.toDate()}})
		.project({_id: 1})
		.limit(DELETE_CHUNK_SIZE)
		.exec();

	// 삭제할 것이 없을 때까지, 50000건씩 끊어서 삭제 처리
	while (deleteIds.length > 0) {
		await ExtendedKeyValueJob.deleteMany({_id: {$in: deleteIds}});

		log.debug(`[kvext.service :: deleteExtendedKeyValueJob] ${deleteIds.length} 건 삭제 완료`);

		deleteIds = await ExtendedKeyValueJob.aggregate()
			.match({scheduledAt: {$lt: twoWeeksAgo.toDate()}})
			.project({_id: 1})
			.limit(DELETE_CHUNK_SIZE)
			.exec();
	}

	log.debug('[kvext.service :: deleteExtendedKeyValueJob] 처리 완료');
};


/**
 * deleteFullSyncLocal : fullSync LOCAL 파일 일괄 삭제
 *     - 매주 2주 전 기준으로 한달치 LOCAL 파일 삭제
 */
module.exports.deleteFullSyncLocal = async () => {
	log.debug('[kvext.service :: deleteFullSyncLocal] 호출됨');

	// 2주 전 기준으로 30일치 LOCAL 파일 삭제 (오늘이 12월 15일이면 10월 30일 ~ 11월 30일 일괄 삭제)
	// targetDate 이전 파일 일괄 삭제 (include)
	const targetEndDate = moment().subtract(15, 'days').startOf('day');
	const targetStartDate = moment(targetEndDate).subtract(1, 'months');

	log.debug(`[kvext.service :: deleteFullSyncLocal] ${targetStartDate.format('YYYY-MM-DD')} ~ ${targetEndDate.format('YYYY-MM-DD')} 일괄 삭제 시작`);


	/* 1. targetStartDate 에 해당 하는 년/월 디렉토리 일괄 삭제 */
	// targetStartDate 가 12월인 경우, 작년 디렉토리 삭제. 그 외에는 월 디렉토리 삭제
	// targetStartPath = local_download/kvext/2024
	// targetStartPath = local_download/kvext/2025/04
	let targetStartPath = path.join(config.local_root, config.kvext_path, targetStartDate.format('YYYY'));
	if (targetStartDate.month() + 1 < 12) {
		targetStartPath = path.join(targetStartPath, targetStartDate.format('MM'));
	}

	log.debug(`[kvext.service :: deleteFullSyncLocal] targetStartDate 에 해당 하는 년/월 디렉토리 일괄 삭제 시작 ( rm -rf ${targetStartPath} )`);

	child_process.execSync(`rm -rf ${targetStartPath}`);

	log.debug(`[kvext.service :: deleteFullSyncLocal] targetStartDate 에 해당 하는 년/월 디렉토리 일괄 삭제 완료 ( rm -rf ${targetStartPath} )`);


	/* 2. targetEndDate 에 해당 하는 월 1일 부터 targetEndDate 까지 일괄 삭제  */
	const dateList = await _getDateList(moment(targetEndDate).startOf('month'), targetEndDate);

	// targetPath = local_download/kvext/2025/04/*_{yyyymmdd,yyyymmdd,yyyymmdd,...}_*.csv
	const targetPath = path.join(config.local_root, config.kvext_path, targetEndDate.format('YYYY'), targetEndDate.format('MM'), dateList.length > 1 ? `*_{${dateList.join(',')}}_*.csv` : `*_${dateList[0]}_*.csv`);

	log.debug(`[kvext.service :: deleteFullSyncLocal] targetEndDate 에 해당 하는 월 1일 부터 targetEndDate 까지 일괄 삭제 시작 ( rm -f ${targetPath} )`);

	child_process.execSync(`rm -f ${targetPath}`);

	log.debug(`[kvext.service :: deleteFullSyncLocal] targetEndDate 에 해당 하는 월 1일 부터 targetEndDate 까지 일괄 삭제 완료 ( rm -f ${targetPath} )`);

	log.debug(`[kvext.service :: deleteFullSyncLocal] ${targetStartDate.format('YYYY-MM-DD')} ~ ${targetEndDate.format('YYYY-MM-DD')} 일괄 삭제 완료`);
};


/**
 * _getDateList : 시작일-종료일 간 날짜 리스트 가져오기
 *
 * @param {Date} startDate 20240101
 * @param {Date} endDate 20240115
 * @return {Array} dateList 날짜 리스트 ['20240101', '20240102', ..., '20240115' ]
 */
const _getDateList = async (startDate, endDate) => {
	// 날짜 리스트
	const dateList = [];

	// 날짜 리스트 생성(시작일 ~ 종료일)
	let tempDate = moment(startDate);
	while (tempDate.isSameOrBefore(endDate)) {
		dateList.push(tempDate.format('YYYYMMDD'));
		tempDate.add(1, 'days');
	}

	return dateList;
};


module.exports.alarmAbnormalFullSync = async () => {
	log.debug('[kvext.service :: alarmAbnormalFullSync] 호출됨');

	// 최대 재시도 횟수를 초과한 Full Sync 작업 조회(알람을 아직 보내지 않은 것만을 대상)
	// ExtendedKeyValueJobs._id = 66f18270544a69065bc1efdb key=blogId
	const MAX_RETRY_CNT = 3;
	const list = await ExtendedKeyValueJob.find({type: 'F', status: 'FAILURE', retryCnt: MAX_RETRY_CNT, alarm: 0});

	if (_.isEmpty(list)) return;

	// 이메일 알림
	await _send_full_sync_alarm_email(list)

	// 알림을 보냈음을 표시
	const _ids = list.map(doc => doc._id)
	await ExtendedKeyValueJob.updateMany({_id: {'$in': _ids}}, {'$set': {alarm: 1, modifiedAt: moment()}});

	log.debug('[kvext.service :: alarmAbnormalFullSync] 처리 완료');
};

const _send_full_sync_alarm_email = async (list) => {
	// 수신자
	// '<EMAIL>' 포함 기타 수신자
	const doc = await Environments.findOne({name: 'kvext-alarm-receivers'}).select('value');
	const to = doc.value.join(";");
	// log.debug(`............ _send_full_sync_alarm_email receivers=${JSON.stringify(to, null, 2)}`)

	// 제목
	const subject = '[키값확장] Full Sync Failure';

	// 내용
	let html = '[키값확장] Full Sync에 실패했습니다<br/><br/>';
	for (const doc of list) {
		// log.debug(`............doc=${JSON.stringify(doc, null, 2)}`)

		/*
		pub = {
			"name" : "퍼스널커뮤니티",
			"freeformKeyValues" : {
				"fullSync" : {
					"url" : "http://dev.api.blog.naver.com/closed/hmac/BlogAdvertisementGradeDump.nhn?serviceId=gfp"
				}
			}
		}
		 */
		const pubs = await Publisher.aggregate([
			{
				$unwind: "$freeformKeyValues"
			},
			{
				$match: {
					'_id': doc.publisher_id,
					'freeformKeyValues.key': doc.key
				}
			},
			{
				$project: {'name': 1, 'freeformKeyValues.fullSync.url': 1}
			}
		]);

		if (pubs.length > 0) {
			const pub = pubs[0]
			log.debug(`............pub=${JSON.stringify(pub, null, 2)}`)
			html += `job._id=${doc._id} publisher=${pub.name}(${pub._id}) key=${doc.key} scheduledAt=${moment(doc.scheduledAt).format('YYYY.MM.DDTHH:mm:ss')} fullSyncUrl=${pub.freeformKeyValues.fullSync.url}</br>`;
		}
	}

	// 이메일 전송
	await mailer.sendMail({to, subject, html});
}
