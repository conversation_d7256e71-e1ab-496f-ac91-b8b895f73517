'use strict';

import _ from 'lodash';
import mongoose from 'mongoose';
import moment from 'moment-timezone';

import extendedLogger from '../../utils/logger-ext.util';
import {ExtendedKeyValue} from '../../models/extended-key-values.schema';
import {ExtendedKeyValueJob} from '../../models/extended-key-value-jobs.schema';

const log = extendedLogger('kvext'); // 확장로거
let ObjectId = mongoose.Types.ObjectId;

module.exports.work = async (job) => {
	// job = { _id, type, status, httpMethod, publisher_id, key, value, items, createdAt, begunAt, endedAt }

	const prefix = `[KVEXT] 개별동기화 :: jobId:${job._id} pubId:${job.publisher_id} key:${job.key} value:${job.value}`;

	try {
		log.debug(`${prefix} 처리 중 ...`); //  ${JSON.stringify(job, null, 2)}

		if (job.httpMethod === 'POST') { // 추가
			await _add(job);
			await _markStatusAsComplete(job._id);
		} else if (job.httpMethod === 'PUT') { // 변경
			await _update(job);
			await _markStatusAsComplete(job._id);
		} else if (job.httpMethod === 'DELETE') { // 삭제
			await _delete(job);
			await _markStatusAsComplete(job._id);
		} else {
			log.error(`${prefix} unknown httpMethod: ${job.httpMethod}`);
			await _markStatusAsFailure(job._id, 'unknown http method:' + job.httpMethod);
		}

		log.debug(`${prefix} 처리 종료.`);
	} catch (error) {
		log.error(`${prefix} 처리 실패. ${error.stack}`);
		await _markStatusAsFailure(job._id, error);
	}
};

/**
 * 확장키값 조회
 * @param publisher_id
 * @param key
 * @param value
 * @returns {Promise.<*>}
 * @private
 */
const _get = async ({ publisher_id, key, value}) => {
	const kvext = await ExtendedKeyValue.findOne({ 'publisher_id': ObjectId(publisher_id), 'key': key, 'value': value });
	return kvext;
};


/**
 * 확장키값 추가
 * @type {function({publisher_id: *, key: *, value: *, items: *})}
 * @private
 */
const _add = async ({_id, publisher_id, key, value, items }) => {
	// 값이 이미 있는지 조회
	const asis = await _get({ publisher_id, key, value });

	// 이미 있으면 업데이트
	if (!_.isNil(asis)) {
		await _update({_id, publisher_id, key, value, items });
	} else { // 없으면 추가
		const kvext = {
			"publisher_id": ObjectId(publisher_id),
			"key": key,
			"value": value,
			"valid": 1,
			"items": items
		};

		await ExtendedKeyValue.create(kvext);

		log.debug(`[KVEXT] 추가 :: jobId:${_id} publisherId:${publisher_id} key:${key} value:${value}`);
	}
};


/**
 * 확장키값 변경
 * @type {function({publisher_id?: *, key?: *, value?: *, items: *})}
 * @private
 */
const _update = async ({_id, publisher_id, key, value, items}) => {
	// 값이 이미 있는지 조회
	const asis = await _get({publisher_id, key, value, items});

	if (_.isNil(asis)) {
		// 없으면 추가
		await _add({_id, publisher_id, key, value, items});
	} else {
		// 있으면 변경
		await ExtendedKeyValue.updateOne(
			{ 'publisher_id': ObjectId(publisher_id), 'key': key, 'value': value },
			{ '$set': { 'items': items, 'modifiedAt': moment(), 'valid': 1	} }
		);

		log.debug(`[KVEXT] 변경 :: jobId:${_id} publisherId:${publisher_id} key:${key} value:${value}`);
	}
};


/**
 * 확장키값 삭제
 * @type {function({publisher_id: *, key: *, value: *})}
 * @private
 */
const _delete = async ({_id, publisher_id, key, value}) => {
	await ExtendedKeyValue.updateOne(
		{ 'publisher_id': ObjectId(publisher_id), 'key': key, 'value': value },
		{ '$set': { 'valid': 0, 'modifiedAt': moment() } }
	);

	log.debug(`[KVEXT] 삭제 :: jobId:${_id} publisherId:${publisher_id} key:${key} value:${value}`);
};


/**
 * 상태 표시 - 완료
 * @param jobId
 * @returns {Promise.<void>}
 * @private
 */
const _markStatusAsComplete = async (jobId) => {
	await ExtendedKeyValueJob.updateOne(
		{ _id: jobId },
		{ '$set': { 'status': 'COMPLETE', 'modifiedAt': moment(), 'endedAt': moment() }
	});
};


/**
 * 상태 표시 - 실패
 * @param jobId
 * @param error
 * @returns {Promise.<void>}
 * @private
 */
const _markStatusAsFailure = async (jobId, error) => {
	await ExtendedKeyValueJob.updateOne(
		{ _id: jobId },
		{ '$set': { 'status': 'FAILURE', 'modifiedAt': moment(), 'endedAt': moment(), 'error': error }
	});
};
