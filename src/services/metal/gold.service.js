'use strict';

import path from 'path';
import slash from 'slash';
import config from "../../config/config";
import * as logger from "../../utils/logger.util";
import moment from "moment";
import {DataEnvironments} from "../../models/data/data-environments.schema";
import * as c3HdfsApi from "../../c3/hdfs-api"

/**
 * 골드 인덱스 최근 집계 일시 갱신
 * @returns {Promise<void>}
 */
module.exports.refreshRecentGoldIndexYmdh = async () => {
	const indexTypes = ['adunit', 'adprovider'];

	// 마지막 집계일시
	const env1 = await DataEnvironments.findOne({'name': 'gold-index-recent-accumulation-ymdh'});
	const lastGoldDate = moment(env1.value, 'YYYYMMDDHH');
	let recentGoldYmdh = lastGoldDate.format('YYYYMMDDHH'); // while loop에서 사용할 변수
	const lastGoldYmdh = recentGoldYmdh; // 로그 찍기 용 변수
	logger.debug(`골드 인덱스 최근 집계일시 업데이트.. lastGoldYmdh:${lastGoldYmdh}`);

	let current = lastGoldDate.add(1, 'hours'); // 마지막 집계일시 + 1 시간부터 조사
	const now = moment();

	while (current.isSameOrBefore(now)) {
		const currentYmdh = current.format('YYYYMMDDHH');
		const currentYmdhPath = current.format('YYYY/MM/DD/HH')

		// 광고유닛, 광고공급자 지표 존재 여부 검사
		let both = [0, 0];
		for (let i = 0; i < indexTypes.length; i++) {
			const logName = indexTypes[i];
			const filePath = slash(path.join(config.gold.hdfs.root_dir, logName, currentYmdhPath, '_GOLD_SUCCESS'));
			const isExist = await c3HdfsApi.exists(filePath);
			both[i] = (isExist ? 1 : 0);
		}

		// 둘 다 있으면 해당 시간으로 최근 집계 일시 갱신
		const isBothExist = both.reduce((prevValue, currValue) => prevValue & currValue);
		if (isBothExist) {
			if (process.env.NODE_ENV === 'local' || process.env.NODE_ENV === 'test') {
				// 테스트인 경우는 중간에 이가 빠지는 경우가 있어 순차 집계가 아니더라도
				// 마지막으로 쌓은 시간대를 최근 집계일시로 함
				recentGoldYmdh = currentYmdh;
			} else {
				// 중간에 이가 빠지지 않는 경우만 설정
				if (moment(recentGoldYmdh, 'YYYYMMDDHH').add(1, 'hours').isSame(moment(currentYmdh, 'YYYYMMDDHH'))) {
					recentGoldYmdh = currentYmdh;
				}
			}
			logger.debug(`골드 인덱스 최종 집계일시 ${lastGoldYmdh} 이후 ${currentYmdh} 존재함`);
		} else {
			logger.debug(`골드 인덱스 최종 집계일시 ${lastGoldYmdh} 이후 ${currentYmdh} 존재하지 않음`);
		}

		current = current.add(1, 'hours');
	}

	// 최근 적재 일시 반영
	await _updateRecentGoldYmdh(recentGoldYmdh)
};

const _updateRecentGoldYmdh = async (recentGoldYmdh) => {
	logger.debug('골드 인덱스 최종 집계일시', recentGoldYmdh);
	await DataEnvironments.updateOne(
		{'name': 'gold-index-recent-accumulation-ymdh'},
		{
			'$set':
				{
					'value': recentGoldYmdh.replace(/-/g, '').replace(/\//g, ''),
					'modifiedAt': moment()
				}
		}
	);
};
