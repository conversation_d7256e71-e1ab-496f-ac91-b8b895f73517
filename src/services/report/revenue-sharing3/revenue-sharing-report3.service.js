'use strict';

import * as _ from "lodash";
import moment from 'moment-timezone';
import archiver from 'archiver';
import { MappingHistory } from "../../../models/mapping-history.schema";
import extendedLogger from '../../../utils/logger-ext.util';
import { AdUnit } from "../../../models/ad-unit.schema";
import { Schedule } from "../../../models/data/schedules.schema";
import { RevenueSharingReport } from "../../../models/data/revenue-sharing-reports.schema";
import { AdProvider } from "../../../models/ad-providers.schema";
import { AdProviderInfo } from "../../../models/ad-provider-infos.schema";
import { BatchReportJobSchedule } from "../../../models/batch-report-job-schedule.schema";
import { DataEnvironments } from "../../../models/data/data-environments.schema";
import path from "path";
import * as file from "../../../utils/file.util";
import * as c3HdfsApi from "../../../c3/hdfs-api";
import { BusinessError } from "../../../common/error";
import * as c3HdfsCli from "../../../c3/hdfs-cli";
import fs from "fs";
import * as nubesCli from "../../../nubes/nubes-cli";
import mongoose from "mongoose";
import config from "../../../config/config";
import { Publisher } from "../../../models/publishers.schema";
import { SilverTrace } from "../../../models/data/silver-trace.schema";
import { ZirconTrace } from "../../../models/data/zircon-trace.schema";
import { RevenueSharingReportTrace } from "../../../models/data/revenue-sharing-report-trace.schema";
import { Environments } from "../../../models/environments.schema";
import * as logger from "../../../utils/logger.util";

const log = extendedLogger('rs3'); // 확장로거
const ObjectId = mongoose.Types.ObjectId;

const LOG_PREFIX = '[RVN-SHAR-3]';
const GATEWAY_ADDRESS = config.nubes.nam_api.gatewayAddress;
const BUCKET = config.nubes.nam_api.bucket;

// HDFS_DIR : /user/gfp-data/revenue_sharing_report
const HDFS_DIR = config.report.revenue_sharing3.hdfs.root_dir;

// LOCAL_DIR : local_download/report/revenue_sharing
const LOCAL_DIR = path.join(config.local_root, config.report.path, config.report.revenue_sharing3.path);

// NUBES_DIR : revenue_sharing_report
const NUBES_DIR = path.join(config.report.revenue_sharing3.nubes.root_dir);

const TIME_UNIT = {
	DAILY: "DAILY",
	MONTHLY: "MONTHLY"
};

const SILVERGREY_STATE = module.exports.AP_RPT_STATE = {
	NOT_SPECIFIED: "NOT_SPECIFIED", // AP 리포트 연동 정보(AdProviders.reportApi.period.end)가 기술되어 있지 않음
	NOT_REGISTERED_ALL: "NOT_REGISTERED_ALL", // 모든 AP 리포트 스케줄이 없음. 오늘자로 등록된 AP 스케줄이 하나도 없음
	NOT_REGISTERED: "NOT_REGISTERED", // AP 리포트 스케줄이 없음. 이 AP에 해당하는 스케줄이 없음.
	OFF: "OFF", // AdProviderInfos.reportApiStatus = 'OFF'
	READY: "READY", // BatchReportJobSchedule.state = 'READY'
	WAIT: "WAIT", // BatchReportJobSchedule.state = 'READY' (배치 처리 대상이지만 아직 READY인 경우가 있는데(스케쥴 순서 상), 두 번 요청으로 인해 이중으로 연동 처리되지 않도록 WAIT 상태를 둠)
	IN_PROGRESS: "IN_PROGRESS", // BatchReportJobSchedule.state = 'IN_PROGRESS'
	COMPLETE: "COMPLETE", // BatchReportJobSchedule.state = 'COMPLETE'
	FAILED: "FAILED", // BatchReportJobSchedule.state = 'FAILED'
};

const STATE = module.exports.STATE = {
	READY: "READY",
	IN_PROGRESS: "IN_PROGRESS",
	COMPLETE: "COMPLETE",
	FAILURE: "FAILURE"
}
const TASK_STATE = module.exports.TASK_STATE = {
	READY: "READY",

	WAIT: "WAIT",
	WAIT_SG: "WAIT_SG",
	WAIT_ZRGFP: "WAIT_ZRGFP",

	SATISFIED: "SATISFIED",

	AGG_IN_PROGRESS: "AGG_IN_PROGRESS",
	AGG_COMPLETE: "AGG_COMPLETE",
	AGG_FAILURE: "AGG_FAILURE",

	UPLOAD_IN_PROGRESS: "UPLOAD_IN_PROGRESS",
	UPLOAD_COMPLETE: "UPLOAD_COMPLETE",
	UPLOAD_FAILURE: "UPLOAD_FAILURE",

	REPROCESS: "REPROCESS"
}

const STORAGE_TYPE = { HDFS: 'HDFS', NUBES: 'NUBES' };

class SilvergreySchedule {
	constructor(date, state, expectedCompleteDate) {
		this.date = date;
		this.state = state;
		this.expectedCompleteDate = expectedCompleteDate;
	}
}

class SilvergreyDetail {
	constructor(adProvider_id, adProviderName, reportApiType, offset, reportApiStatus, date, state, expectedCompleteDate = null) {
		// 광고공급자
		this.adProvider_id = adProvider_id;
		this.adProviderName = adProviderName;

		// 어떤 AP의 리포트 연동인가. AdProviders.reportApi.type = "GOOGLE"
		this.reportApiType = reportApiType;

		// 며칠전 리포트까지 제공하는가. AdProviders.reportApi.period.end = -1
		this.offset = offset;

		// 광고공급자설정 메뉴에서 AP 리포트 연동을 활성화했는가. AdProviderInfos.reportApiStatus = "ON", "OFF"
		this.reportApiStatus = reportApiStatus;

		// 리포트의 날짜
		this.date = date;

		// AP 리포트 연동 상태(mongodb, hdfs의 종합상태) BatchReportJobSchedule.state RPT_STATE_XX
		this.state = state;

		// AP 리포트의 예상 완료일시 (모니터링에서 사용)
		this.expectedCompleteDate = expectedCompleteDate;
	}
}

/**
 * 1시간마다 호출되며 airflow에서 날짜가 지정되어 옴
 *
 * 일별
 * 		yyyyMMdd 1시간마다 D-1 날짜로 들어옴 (어제 자)
 * 		RevenueSharingSchedules.howManyDaysBefore = 2인 경우도 미리 생김
 * 		이 경우 OUSIDE AP 리포트가 도착하지 않았을 것이므로 READY or WAIT_APRPT 일 것임.
 * 월별
 * 		yyyyMM 1시간마다 M-1 날짜로 들어옴(지난 달)
 * 		수익쉐어 리포트 스케줄을 언제 등록하든, 등록 후 1시간 이내에 지난 달 날짜로 수익쉐어 리포트가 READY 상태로 등록됨.
 * 		이 후 updateReportState()에서 적절한 상태로 업데이트될 것이고, 그에 따라 실제 리포트가 생성됨.
 *
 * @param date
 * @returns {Promise<void>}
 */
module.exports.prepareReports = async (date) => {
	const frequency = date.length === 6 ? TIME_UNIT.MONTHLY : TIME_UNIT.DAILY;
	if (![6, 8].includes(date.length)) {
		throw new Error(`Invalid 'targetDate' format. 'targetDate' format should be yyyyMM or yyyyMMdd. current targetDate=${date}`);
	}

	const schedules = await Schedule.find({ status: 'ON', 'frequency': frequency });
	for (const sch of schedules) {
		if (!await _isAvailableDate(sch, date)) continue;

		const report = await RevenueSharingReport.findOne({ 'schedule_id': sch._id, 'date': date });
		report
			? log.debug(`${LOG_PREFIX} 이미 존재하므로 스킵. sch_id=${sch.id}(${sch.name}) date=${date}`)
			: await _addReport(sch, date);
	}

	log.debug(`${LOG_PREFIX} revenue-sharing-report.service.prepareReport() :: 리포트 준비 완료`);
};

/**
 * 여러 수익쉐어 리포트 상태 갱신(정규/수동 처리에서 사용)
 * @returns {Promise<void>}
 */
module.exports.updateReportsState = async (txId, reportIdList = []) => {
	// 재처리가 필요한 리포트 REPROCESS 상태로 변경
	await _setTaskStateIfNeedsReprocess();

	// 조건 설정
	const match = {};
	if (_.isEmpty(reportIdList)) {
		match['taskState'] = { '$in': [TASK_STATE.READY, TASK_STATE.WAIT, TASK_STATE.WAIT_ZRGFP, TASK_STATE.WAIT_SG, TASK_STATE.REPROCESS] };
	} else {
		match['_id'] = { '$in': reportIdList.map(id => ObjectId(id)) };  // reportIdList가 있다면 해당 리포트들만 갱신
	}

	// 상태를 업데이트할 리포트 가져오기
	const reports = await _getReportsWithMeta(match, { 'silvergreyDetails': 0 });
	// log.debug(`${LOG_PREFIX} txId=${txId} 상태를 업데이트할 리포트 = ${JSON.stringify(reports, null, 2)}`);

	// REPROCESS 상태를 포함하여 상태 갱신
	for (const report of reports) {
		await _updateReportState(txId, report);
	}
}

const _updateReportState = async (txId, report) => {
	// AP 리포트 상태 조회
	const sgDetails = await _getSilvergreyDetails(report.publisher_id, report.date, report.frequency);
	// log.debug(`${LOG_PREFIX} txId=${txId} _updateReportState() report_id=${report._id}(${report.date}) silvergreyDetails=${JSON.stringify(sgDetails, null, 2)}`);

	// ZRGFP 상태와 AP 리포트 상태를 종합한 상태 결정
	const taskState = await _decideRsRptTaskState(txId, report.date, sgDetails);
	const state = _decideStateByTaskState(taskState);

	// 종합 상태 업데이트
	await RevenueSharingReport.updateOne({ '_id': report._id }, {
		'$set': {
			'state': state,
			'taskState': taskState,
			'silvergreyDetails': sgDetails,
			'modifiedAt': new Date()
		}
	});
};

/**
 * 수익쉐어 리포트 트레이스 생성
 * 		- ZRGFP 재처리 시, 실버그레이 재처리 시 생성
 * @param previousSensingDateTimeStr
 * @returns {Promise<void>}
 */
module.exports.makeReportTraces = async (previousSensingDateTimeStr) => {
	const previousSensingAt = moment(previousSensingDateTimeStr, 'YYYYMMDDHHmmss').toDate();
	log.debug(`${LOG_PREFIX} 이전 주기 감지 시각 previousSensingDateTimeStr = ${previousSensingDateTimeStr} = ${previousSensingAt}`);

	// 후보를 걸러내고 (COMPLETE 된 D-1,2 대상 일별 리포트)
	const reports = await _getCandidateReportsToReprocess();

	if (reports.length < 1) {
		log.debug(`${LOG_PREFIX} 재처리할 대상 리포트 없음`);
		return;
	}

	for (const report of reports) {
		// 재처리된 SilverTrace가 있는지 조회 (해당 리포트 처리가 시작된 이후에 재처리된 ZRGFP)
		const silverTraces = await _getSilverTraces(report.date, report.startedAt, previousSensingAt);

		// 재처리된 ZirconTrace가 있는지 조회 (해당 리포트 처리가 시작된 이후에 재처리된 실버그레이)
		const zirconTraces = await _getZirconTraces(report.date, report.startedAt, previousSensingAt, report.publisher_id, report.silvergreyDetails);

		if ((silverTraces && silverTraces.length > 0) || (zirconTraces && zirconTraces.length > 0)) {
			await _addRevenueSharingReportTrace(report._id, report.date, silverTraces, zirconTraces);
		}
	}
};

/**
 * 재처리가 필요한 수익쉐어 리포트가 있다면 REPROCESS 상태로 변경
 * @returns {Promise<void>}
 * @private
 */
const _setTaskStateIfNeedsReprocess = async () => {
	// 아직 처리되지 않은 수익쉐어 리포트 트레이스 조회
	const pipeline = [
		{
			'$match': {
				'state': STATE.READY
			}
		},
		{
			'$group': {
				'_id': '$revenueSharingReport_id',
				'date': { '$first': '$date' },
				'revenueSharingReport_id': { '$first': '$revenueSharingReport_id' },
			}
		}
	];
	const tracesToReprocess = await RevenueSharingReportTrace.aggregate(pipeline);
	const report_ids = tracesToReprocess.map(trace => trace.revenueSharingReport_id);

	// REPROCESS 상태로 변경
	await RevenueSharingReport.updateMany(
		{ '_id': { '$in': report_ids } },
		{ '$set': { 'taskState': TASK_STATE.REPROCESS, 'modifiedAt': new Date() } }
	);
}

/**
 * 재처리할 일별 리포트 후보 목록 조회
 * 		- COMPLETE 된 D-1,2 대상 리포트
 *
 * 월별 리포트는 재처리 없음
 * 		- https://wiki.navercorp.com/spaces/GFP/pages/2901034244/%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8+%EA%B0%9C%EC%84%A0+-+%EC%A7%80%EB%A5%B4%EC%BD%98R+Phase3#id-%EC%88%98%EC%9D%B5%EC%89%90%EC%96%B4%EB%A6%AC%ED%8F%AC%ED%8A%B8%EA%B0%9C%EC%84%A0%EC%A7%80%EB%A5%B4%EC%BD%98R(Phase3)-%EC%9E%AC%EC%B2%98%EB%A6%AC%EC%A0%95%EC%B1%85
 *
 * @returns {Promise<boolean>}
 * @private
 */
const _getCandidateReportsToReprocess = async () => {
	// 일별 리포트 (COMPLETE 된 DAILY 리포트 - D-1,2 대상)
	const twoDaysAgo = moment().subtract(2, 'days').format('YYYYMMDD');
	const oneDaysAgo = moment().subtract(1, 'days').format('YYYYMMDD');
	const match = {
		'date': { '$in': [twoDaysAgo, oneDaysAgo] },
		'state': STATE.COMPLETE
	};
	// log.debug(`${LOG_PREFIX} match=${JSON.stringify(match, null, 2)}`);
	return await _getReportsWithMeta(match);
};


const _getReportsWithMeta = module.exports.getReportsWithMeta = async (match, projection = {}) => {
	const pipeline = [
		{ '$match': match },
		{
			'$lookup': {
				'from': 'Schedules',
				'foreignField': '_id',
				'localField': 'schedule_id',
				'as': 'sch'
			}
		},
		{ '$unwind': '$sch' },
		{
			'$lookup': {
				'from': 'SyncPublishers',
				'foreignField': '_id',
				'localField': 'sch.publisher_id',
				'as': 'pub'
			}
		},
		{ '$unwind': '$pub' },
		{
			'$replaceRoot': {
				'newRoot': {
					'$mergeObjects': [
						'$$ROOT',
						{ 'schedule_id': '$sch._id' },
						{ 'scheduleName': '$sch.name' },
						{ 'frequency': '$sch.frequency' },
						{ 'granularity': '$sch.granularity' },
						{ 'publisher_id': '$pub._id' },
						{ 'publisherName': '$pub.name' },
					]
				}
			}
		},
		{
			'$project': Object.assign({ 'sch': 0, 'pub': 0 }, projection)
		}
	];
	// log.debug(`${LOG_PREFIX} pipeline=${JSON.stringify(pipeline, null, 2)}`);

	const reports = await RevenueSharingReport.aggregate(pipeline);
	return reports;
};

/**
 * 이 리포트와 관계된 실버 트레이스 조회
 * @param {string} date 리포트의 일자(yyyyMMdd)
 * @param {Date} rptStartedAt 수익쉐어 리포트 생성 시작 시각
 * @param {Date} previousSensingAt 이전 주기 감지 시각
 * @returns {Promise<*|*[]>}
 * 			traces = [
 * 				{
 * 					'silverTrace_id': '649001111111111111',
 * 					'zirconRGfpCompletedAt': '2025-01-01T00:00:00.000Z'
 * 				},
 * 				...
 * 			]
 *
 * @private
 */
const _getSilverTraces = async (date, rptStartedAt, previousSensingAt) => {
	const match = {
		'date': date,
		'$and': [
			{ 'zirconRGfpCompletedAt': { '$gte': rptStartedAt } },  // 리포트 생성이 시작된 이후 재처리된 ZRGFP가 있고
			{ 'zirconRGfpCompletedAt': { '$gte': previousSensingAt } }  // 이전에 감지한 이후에 재처리된 것이고
		]
	};

	// 조회
	// log.debug(`${LOG_PREFIX} match=${JSON.stringify(match, null, 2)}`);
	const traces = await SilverTrace.find(match, { _id: 1, zirconRGfpCompletedAt: 1 });
	if (traces && traces.length > 0) {
		log.debug(`${LOG_PREFIX} 처리할 SilverTraces = ${JSON.stringify(traces, null, 2)}`);
		return traces.map(tr => ({
			'silverTrace_id': tr._id,
			'zirconRGfpCompletedAt': tr.zirconRGfpCompletedAt,
		}));
	} else {
		// log.debug(`${LOG_PREFIX} 처리할 SilverTrace 없음`);
		return [];
	}
};

/**
 * 이 리포트와 관계된 지르콘 트레이스 조회
 * @param date 리포트의 일자(yyyyMMdd)
 * @param rptStartedAt 수익쉐어 리포트 생성 시작 시각
 * @param previousSensingAt 이전 주기 감지 시각
 * @param publisher_id 수익쉐어 리포트의 publisher_id
 * @param silvergreyDetails 이 리포트와 관계된 adProvider_id 목록
 * @returns {Promise<*|*[]>}
 * 			shortenedTraces = [
 * 				{
 * 					'zirconTrace_id': '649001111111111111',
 * 					'adProvider_id': '649001111111111111',
 * 					'silvergreyCompletedAt': '2025-01-01T00:00:00.000Z'
 * 				},
 * 				...
 * 			]
 * @private
 */
const _getZirconTraces = async (date, rptStartedAt, previousSensingAt, publisher_id, silvergreyDetails) => {
	// AP 리포트 연동이 완료된 adProvider_id 목록
	// AP_RPT_STATE 중 NOT_SPECIFIED, NOT_REGISTERED, OFF 등은 무시하고 리포트가 생성되었을 것이므로 COMPLETE 상태의 AP만을 대상으로 함
	const completeApIds = silvergreyDetails
		.filter(sgDetail => sgDetail.state === STATE.COMPLETE)
		.map(sgDetail => sgDetail.adProvider_id);

	// 수익쉐어 리포트 처리를 시작한 이후 재처리된 실버그레이가 있는지 조회
	const match = {
		'date': date,
		'$and': [
			{ 'silvergreyCompletedAt': { '$gte': rptStartedAt } },  // 리포트 생성이 시작된 이후 재처리된 실버그레이가 있고
			{ 'silvergreyCompletedAt': { '$gte': previousSensingAt } } // 이전에 감지한 이후에 재처리된 것이고
		],
		'publisher_id': publisher_id,
		'adProvider_id': { '$in': completeApIds },
	};
	// log.debug(`${LOG_PREFIX} match=${JSON.stringify(match, null, 2)}`);
	const traces = await ZirconTrace.find(match);

	if (traces && traces.length > 0) {
		// 필요한 필드만으로 재구성한 목록
		const shortenedTraces = traces.map(zt => ({
			'zirconTrace_id': zt._id,
			'adProvider_id': zt.adProvider_id,
			'silvergreyCompletedAt': zt.silvergreyCompletedAt,
		}));
		log.debug(`${LOG_PREFIX} 처리할 ZirconTraces = ${JSON.stringify(shortenedTraces, null, 2)}`);
		return shortenedTraces;
	} else {
		// log.debug(`${LOG_PREFIX} 처리할 ZirconTrace 없음`);
		return [];
	}
};

/**
 * 수익쉐어 리포트 트레이스 추가
 * @param {string} revenueSharingReport_id - 수익쉐어 리포트 id
 * @param {string} date - yyyyMMdd 또는 yyyyMM
 * @param {object} silverTrace - 실버 트레이스
 * @param {array} zirconTraces - 지르콘 트레이스 목록
 * @returns {Promise<*|*[]>}
 */
const _addRevenueSharingReportTrace = async (revenueSharingReport_id, date, silverTraces, zirconTraces) => {
	const createdAt = moment();
	const expiredAt = moment().add(6, 'months');  // 6개월 후 만료
	const trace = {
		'revenueSharingReport_id': revenueSharingReport_id,
		'date': date,
		'state': STATE.READY,
		'silverTraces': silverTraces,
		'zirconTraces': zirconTraces,
		'succeededAt': null,
		'createdAt': createdAt,
		'modifiedAt': createdAt,
		'expiredAt': expiredAt,
	};
	await RevenueSharingReportTrace.create(trace);
	log.info(`${LOG_PREFIX} 추가한 리포트 트레이스 = ${JSON.stringify(trace, null, 2)}`);
};

const _isAvailableDate = async ({ _id, publisher_id, name, frequency }, date) => {
	const offset = _isIncludeOutSideAp(await _getApReportInterfaceInfos(publisher_id)) ? 2 : 1;
	const today = moment();
	const availableDateUntil = frequency === TIME_UNIT.DAILY
		? today.subtract(offset, 'days').format('YYYYMMDD')
		: today.subtract(1, 'months').format('YYYYMM');

	if (date > availableDateUntil) {
		// log.debug(`${LOG_PREFIX} sch=${_id}(${name})은 OUTSIDE AP가 연결되어 있어 offset=${offset}이므로 ${availableDateUntil} 까지만 준비시킬 수 있음. ${date} 준비 스킵`);
		return false;
	}

	return true;
};

/**
 * 리포트 등록
 *
 * @param sch 스케줄
 * @param date yyyyMM or yyyyMMdd
 * @returns {Promise<void>}
 * @private
 */
const _addReport = async ({ _id: schedule_id, frequency }, date) => {
	// const pub = await Publisher.findOne({ _id: sch.publisher_id }, { name: 1 })

	// 일별 리포트일 경우 startDate, endDate 모두 date(yyyyMMdd)와 동일
	// 월별 리포트일 경우 date(yyyyMM)에 해당하는 달의 1일이 startDate, 말일이 endDate
	let startDate, endDate;
	if (frequency == TIME_UNIT.DAILY) {
		startDate = endDate = date;
	} else {
		startDate = moment(date, 'YYYYMM').startOf('month').format('YYYYMMDD');
		endDate = moment(date, 'YYYYMM').endOf('month').format('YYYYMMDD');
	}

	// 리포트 등록
	const doc = {
		'schedule_id': schedule_id,
		'date': date,
		'startDate': startDate,
		'endDate': endDate,
		'state': STATE.READY,
		'taskState': TASK_STATE.READY,
	};
	await RevenueSharingReport.create(doc);
};

/**
 * AP 리포트 연동 상태 조회
 *
 * [ 2024.02.27 ]
 * 	- [DATA] 수익쉐어 리포트 빈 통으로 생성(https://oss.navercorp.com/da-ssp/bts/issues/2203)
 * 	- 원인: 0시 근방에 AP스케줄들이 등록되는데 수익쉐어 리포트 생성이 AP 스케줄 생성 전에 도는 바람에 모두 빈통으로 생김.
 * 	- 해결: 이에 이 키값그룹과 연결된 AP의 스케줄이 있는지를 확인하고 더불어 연동 정보가 기술되어 있는지를 확인함
 * 	- 결국 AP리포트 연동 상태 = ON && 리포트 연동 정보(reportApi.period.end)가 있는데 스케줄이 없으면 아직 안 생긴 것
 *
 * [ 2024.07.16 ]
 * 	- [DATA] 수익쉐어 리포트 - AP별 스케줄 생성 여부 보지 않고 오늘 자에 생성된 스케줄이 있는지를 보도록 변경(https://jira.navercorp.com/projects/GFP/issues/GFP-18?filter=allopenissues)
 * 	- 현상1: "2024.02.27" 수정으로 인해 매체에 AP가 새로 연결되면 해당 AP의 스케줄이 등록되지 않아 오늘 만들어야 할 수익쉐어 리포트를 못만들게 됨.
 * 	- 현상2: 과거 일자 재처리 시에도 새로 연결된 AP의 스케줄은 없기 때문에 못만듦
 * 	- 해결
 * 		- "2024.02.27" 문제를 해결하면서도 정상적으로 처리되어야 할 AP들을 포함시키기 위해
 * 		- 오늘 등록된 AP 스케줄이 하나라도 있으면 이 키값그룹과 관련된 AP 스케줄은 모두 있다고 보고 리포트 생성함
 * @param pub_id
 * @param date yyyyMM(월별 수익쉐어 리포트) or yyyyMMdd(일별 수익쉐어 리포트)
 * @param frequency
 * @returns {Promise<*[]>}
 * 		details = [
 *          // NCC 소상공인 20250101 ~ 20250131
 *         {
 *             "adProvider_id" : ObjectId("5b74d94bc36eef272090ca52"),
 *             "adProviderName" : "NCC 소상공인",
 *             "reportApiType" : "NCC",
 *             "offset" : NumberInt(-1),
 *             "reportApiStatus" : "ON",
 *             "date" : "20250101",
 *             "state" : "COMPLETE",
 *             "expectedCompleteDate" : ISODate("2025-01-01T06:10:00.000+0900")
 *         },
 *         ...
 *         {
 *             "adProvider_id" : ObjectId("5b74d94bc36eef272090ca52"),
 *             "adProviderName" : "NCC 소상공인",
 *             "reportApiType" : "NCC",
 *             "offset" : NumberInt(-1),
 *             "reportApiStatus" : "ON",
 *             "date" : "20250131",
 *             "state" : "COMPLETE",
 *             "expectedCompleteDate" : ISODate("2025-01-31T06:10:00.000+0900")
 *         },
 *
 *         // Moloco_DEAL 20250101 ~ 20250131
 *         {
 *             "adProvider_id" : ObjectId("66f0e409c53f9c82acf91051"),
 *             "adProviderName" : "Moloco_DEAL",
 *             "reportApiType" : "MOLOCO",
 *             "offset" : NumberInt(-2),
 *             "reportApiStatus" : "ON",
 *             "date" : "20250101",
 *             "state" : "COMPLETE",
 *             "expectedCompleteDate" : ISODate("2025-01-01T05:10:00.000+0900")
 *         },
 *         ...
 *         {
 *             "adProvider_id" : ObjectId("66f0e409c53f9c82acf91051"),
 *             "adProviderName" : "Moloco_DEAL",
 *             "reportApiType" : "MOLOCO",
 *             "offset" : NumberInt(-2),
 *             "reportApiStatus" : "ON",
 *             "date" : "20250131",
 *             "state" : "COMPLETE",
 *             "expectedCompleteDate" : ISODate("2025-01-31T05:10:00.000+0900")
 *         }
 *     ]
 * @private
 */
const _getSilvergreyDetails = async (pub_id, date, frequency) => {
	const logPrefix = `${LOG_PREFIX} _getSilvergreyDetails() pubId=${pub_id} date=${date} frequency=${frequency}`;
	const details = [];

	// 오늘 등록된 AP 스케줄이 하나라도 있는지
	let isApScheduleRegisterAtLeastOne = false;
	const todayRegisteredApSchedule = await BatchReportJobSchedule.findOne({ 'ymd': moment().format("YYYYMMDD") }, { 'ymd': 1 });
	if (todayRegisteredApSchedule) isApScheduleRegisterAtLeastOne = true;


	// 일별일 경우 수익쉐어 리포트의 일자와 동일
	// 월별일 경우 해당 월의 마지막 일자
	const apRptDate = frequency == TIME_UNIT.MONTHLY ? moment(date, 'YYYMM').endOf('month').format('YYYYMMDD') : date;

	/*
	광고공급자별 AP 스케줄 상태 설정

	일별 리포트
		해당 일자 스케줄의 상태 설정(1일치)

	월별 리포트
		해당 월의 모든 일자 스케줄의 상태 설정(n일치)
		아래 조건일 경우 해당 월의 마지막 일자만 설정(1일치)
			AP 리포트 연동 정보(AdProviders.reportApi.period.end)가 설정되어 있지 않거나 :: NOT_SPECIFIED
			AP 리포트 연동 정보(AdProviders.reportApi.period.end)가 설정되어 있는데 AP 리포트 스케줄이 없는 경우 :: NOT_REGISTERED
			AP 리포트 연동 상태(AdProviderInfos.reportApiStatus)가 'OFF'인경우 :: OFF
			오늘 등록된 AP 리포트 스케줄이 하나도 없거나 :: NOT_REGISTERED_ALL
	 */
	// 이 매체에 연결된 AP의 리포트 연동 정보 조회
	const apRptIfInfos = await _getApReportInterfaceInfos(pub_id);
	// log.debug(`${logPrefix} 점검할 AP 목록 = ${JSON.stringify(apRptIfInfos, null, 2)}`);

	for (const ap of apRptIfInfos) {
		const logPrefix2 = `${logPrefix} ap=${ap.name}(${ap._id})`
		const reportApiType = _.has(ap, 'reportApi.type') ? ap.reportApi.type : '-'

		if (_.has(ap, 'reportApi.period.end')) { // AP 리포트 연동 정보가 기술되어 있음
			if (isApScheduleRegisterAtLeastOne) { // 오늘 등록된 AP 스케줄이 하나라도 있는지
				// AP 리포트 연동 상태 조회
				const apInfo = await _getAdProviderInfo(pub_id, ap._id);
				const logPrefix3 = `${logPrefix2} infoId=${apInfo._id} reportApiStatus=${apInfo.reportApiStatus}`;

				// AP 리포트 연동 상태 = ON
				if (apInfo.reportApiStatus === 'ON') {
					// 이 매체에 연결된 AP의 리포트 연동 스케줄 조회
					// apRptSchedules.length = 일별일 경우 1개, 월별일 경우 한달치
					const sgSchedules = await _getSilvergreySchedules(date, frequency, pub_id, ap._id, ap.adProviderType, reportApiType);
					if (sgSchedules.length < 1) {
						// AP 리포트 연동 상태 = ON 이나 AP 리포트 스케줄이 없는 경우도 있음. 이런 경우는 무시
						// 새벽에 AP 스케줄이 일괄 생성된 후, AP 리포트 연동 정보를 설정한 경우임.
						// 이런 목록 조회는 코드 맨 아래 "MongoDB Query - (1)" 확인
						details.push(new SilvergreyDetail(ap._id, ap.name, reportApiType, 0, apInfo.reportApiStatus, apRptDate, SILVERGREY_STATE.NOT_REGISTERED, null));
						// log.debug(`${logPrefix3} AdProviders.reportApi.period.end가 설정되어 있으나 이 AP의 스케줄이 등록되어 있지 않음`);
					} else {
						// AP 스케줄이 있으므로 현재 상태 설정. READY, IN_PROGRESS, COMPLETE, FAILURE 중 하나.
						const apRptDetails = sgSchedules.map(sgSch => {
							return new SilvergreyDetail(ap._id, ap.name, reportApiType, ap.reportApi.period.end, apInfo.reportApiStatus, sgSch.date, sgSch.state, sgSch.expectedCompleteDate);
						})
						details.push(...apRptDetails);
					}
				} else {
					// AdProviderInfos.reportApiStatus = 'OFF'인 경우 AP리포트연동 배치가 '비활성'되어 스케줄이 안 생기므로 스킵
					details.push(new SilvergreyDetail(ap._id, ap.name, '-', 0, apInfo.reportApiStatus, apRptDate, SILVERGREY_STATE.OFF, null));
					// log.debug(`${logPrefix3} AP 리포트 연동 배치가 'OFF' 되어 AP 스케줄이 안 생기므로 스킵`);
				}
			} else {
				// 오늘 자로 등록된 AP 스케줄이 아예 하나도 없으므로 수익쉐어 리포트 생성 불가
				details.push(new SilvergreyDetail(ap._id, ap.name, reportApiType, ap.reportApi.period.end, '-', apRptDate, SILVERGREY_STATE.NOT_REGISTERED_ALL, null));
				// log.debug(`${logPrefix2} 오늘 자로 등록된 그 어떤 AP 스케줄도 없음`);
			}
		} else {
			// AP 리포트 연동 정보(AdProviders.reportApi.period.end)가 설정되어 있지 않음. 이런 경우는 무시
			// 이런 목록 조회는 코드 맨 아래 "MongoDB Query - (1)" 확인
			details.push(new SilvergreyDetail(ap._id, ap.name, reportApiType, 0, '-', apRptDate, SILVERGREY_STATE.NOT_SPECIFIED, null));
			// log.debug(`${logPrefix2} AP 리포트 연동 정보(AdProviders.reportApi.period.end)가 기술되어 있지 않으므로 스킵`);
		}
	}

	return details;
}

/**
 * ap.adProviderType === 'OUTSIDE'인게 하나라도 있으면 return true
 * @param aps
 * @returns {*}
 * @private
 */
const _isIncludeOutSideAp = (aps) => {
	return aps.some(ap => ap.adProviderType === 'OUTSIDE');
};

/**
 * 이 매체에 한 번이라도 연결된 적이 있는 플레이스의 AP 리포트 연동 정보 조회
 * @param pub_id
 * @returns {Promise<*>}
 * @private
 */
const _getApReportInterfaceInfos = async (pub_id) => {
	const adProviders = await MappingHistory.aggregate([
		// AdProviderPlaces
		{
			'$lookup': {
				from: 'AdProviderPlaces',
				foreignField: '_id',
				localField: 'adProviderPlace_id',
				as: 'place'
			}
		},

		// AdProviderInfos
		{
			'$lookup': {
				from: 'AdProviderInfos',
				foreignField: '_id',
				localField: 'place.adProviderInfo_id',
				as: 'info'
			}
		},

		// Publishers
		{
			'$lookup': {
				from: 'Publishers',
				foreignField: '_id',
				localField: 'info.publisher_id',
				as: 'pub'
			}
		},

		{
			'$match': {
				'info.publisher_id': pub_id
			}
		},

		// AdProviders
		{
			'$lookup': {
				from: 'AdProviders',
				foreignField: '_id',
				localField: 'info.adProvider_id',
				as: 'adp'
			}
		},

		{
			'$project': {
				'adProvider_id': '$adp._id',
			}
		},

		{ '$unwind': '$adProvider_id' },

		{
			'$group': {
				'_id': '$adProvider_id',
			}
		}
	]);

	const ap_ids = adProviders.map(ap => ap._id);

	// MappingHistory에서 관리되지 않는 NAVER_DIRECT AP가 있다면 포함시킴
	const naverDirectApId = await _getNaverDirectAdProviderId(pub_id);
	if (naverDirectApId) ap_ids.push(naverDirectApId);

	// AP리포트 연동 정보 조회
	const aps = await AdProvider.find({ _id: { '$in': ap_ids } }, {
		_id: 1,
		name: 1,
		adProviderType: 1, // IN_NAVER or OUTSIDE
		'reportApi.type': 1,
		'reportApi.period.end': 1
	});

	// ※ mongoose의 2 레벨 select는 Ojbect가 아니라 mongoose document이므로 _doc을 통해 Object를 가져와야 함.
	// const list = aps.map(ap => ap._doc);
	// log.debug(`${LOG_PREFIX} _getAdProviders() pubId=${pub_id} aps=${JSON.stringify(list, null, 2)}`);
	return aps.map(ap => ap._doc);
}

const _getNaverDirectAdProviderId = async (pub_id) => {
	const infos = await AdProviderInfo.aggregate([
		{
			'$match': {
				'publisher_id': ObjectId(pub_id),
			}
		},
		{
			'$lookup': {
				from: 'AdProviders',
				foreignField: '_id',
				localField: 'adProvider_id',
				as: 'ap'
			}
		},
		{
			'$unwind': {
				'path': '$ap',
				'preserveNullAndEmptyArrays': false
			}
		},
		{
			'$match': {
				'$or': [
					{ 'ap.adProviderCd': 'NAVER_DIRECT' },
				]
			}
		},
		{
			'$project': {
				'_id': 0,
				'adProvider_id': 1,
			}
		}
	]);

	if (infos && infos.length > 0) {
		return infos[0].adProvider_id;
	} else {
		return null;
	}
};

/**
 * AP리포트 연동 상태 조회
 * @param pub_id
 * @param ap_id
 * @returns {Promise<*>}
 * @private
 */
const _getAdProviderInfo = async (pub_id, ap_id) => {
	const filter = { 'publisher_id': pub_id, 'adProvider_id': ap_id }
	const apInfo = await AdProviderInfo.findOne(filter).select('reportApiStatus');
	return apInfo;
}

/**
 * 이 매체에 연결된 AP의 리포트 연동 스케줄 조회
 *
 * 일별 수익쉐어 리포트
 * 		- 해당 일자의 AP 리포트 스케줄 상태 조회
 *
 * 월별 수익쉐어 리포트
 * 		- 해당 월의 1일 ~ 말일까지의 AP 리포트 스케줄의 상태를 조회하여
 * 		- 하나라도 실패면 실패 날짜, 모두 완료면 완료 날짜, 누락된 것이 있다면 누락된 날짜와 상태로 설정
 *
 * @param date yyyyMM(월별 수익쉐어 리포트) or yyyyMMdd(일별 수익쉐어 리포트)
 * @param frequency DAILY, MONTHLY
 * @param pub_id
 * @param ap_id
 * @param apType IN_NAVER, OUTSIDE
 * @param apReportApiType GOOGLE, ADVIEW, FAN, INMOBI, ...
 * @returns {Promise<*>}
 * 		apRptSchedules = [
 * 			{
 * 				apRptDate: "20250101",
 * 				state: "COMPLETE",
 * 				expectedCompleteDate: "20250102T09:20:00"
 * 			},
 * 			{
 * 				apRptDate: "20250102",
 * 				state: "IN_PROGRESS",
 * 				expectedCompleteDate: "20250103T09:20:00"
 * 			},
 * 			...
 * 			{
 * 				apRptDate: "20250131",
 * 				state: "COMPLETE",
 * 				expectedCompleteDate: "20250201T09:20:00"
 * 			}
 * 		]
 * @private
 */

const _getSilvergreySchedules = async (date, frequency, pub_id, ap_id, apType, apReportApiType) => {
	// 해당 AP가 며칠 전 리포트를 제공하는지
	const apRptOffset = await AdProvider.findOne({ _id: ObjectId(ap_id) }).select('reportApi.period.end');

	const apRptSchedules = []
	const filter = {};

	// if (apReportApiType != 'NCC') return apRptSchedules;

	// 대상 날짜의 AP 리포트 스케줄 조회
	// OUTSIDE AP일 때는 pub_id + ap_id로 조회, IN_NAVER AP일 때는 reportApiType으로만 조회
	if (apType === 'OUTSIDE') {
		filter['publisher_id'] = pub_id;
		filter['adProvider_id'] = ap_id;
	} else {
		filter['reportApiType'] = apReportApiType;
	}

	if (frequency == TIME_UNIT.DAILY) {
		// 일별 수익쉐어 리포트는 해당 일자의 AP 리포트 스케줄 정보 조회
		filter['period.endDate'] = date;

		/*
		 apRptSchedule =
		 {
			"_id": "67755870e608711ea035c38d",
			"reportApiType": "NCC",
			"period": {
			  "endDate": "20250101"
			},
			"state": "COMPLETE"
		  },
		 */
		const apRptSchedule = await BatchReportJobSchedule.findOne(filter, {
			'reportApiType': 1,
			'period.endDate': 1,
			'state': 1
		});
		// log.debug(`${LOG_PREFIX} _getApSchedule() date=${date} filter=${JSON.stringify(filter)} apSchedule=${JSON.stringify(apRptSchedule)}`);

		if (apRptSchedule) {
			apRptSchedules.push(new SilvergreySchedule(
				apRptSchedule.period.endDate,
				apRptSchedule.state,
				await _getExpectedCompleteDateOfApReport(apRptSchedule.reportApiType, apRptSchedule.period.endDate, apRptOffset)
			));
		} else {
			apRptSchedules.push(new SilvergreySchedule(
				date,
				SILVERGREY_STATE.NOT_REGISTERED,
				moment()
			));
		}
	} else {
		// 월별 스케줄은 대상 월의 1일 ~ 말일까지의 스케줄 목록 조회
		const month = moment(date, 'YYYYMM');
		const firstDateOfMonth = month.clone().startOf('month');
		const lastDateOfMonth = month.clone().endOf('month');

		// 해당 월의 1일 ~ 말일까지의 AP 리포트 스케줄 조회
		filter['period.endDate'] = {
			'$gte': firstDateOfMonth.format('YYYYMMDD'),
			'$lte': lastDateOfMonth.format('YYYYMMDD')
		};
		const apRptSchList = await BatchReportJobSchedule.find(filter, {
			'reportApiType': 1,
			'period.endDate': 1,
			'state': 1
		}).sort({ 'period.endDate': 1 });
		// log.debug(`${LOG_PREFIX} _getApSchedules() ap_id=${ap_id} date=${date} filter=${JSON.stringify(filter)}
		// 	apRptSchList.length=${apRptSchList.length}
		// 	apRptSchList=${JSON.stringify(apRptSchList, null, 2)}`);

		// 해당 월의 1일 ~ 말일까지의 AP 리포트 스케줄 상태 설정
		const currentDate = firstDateOfMonth.clone();
		while (currentDate.isSameOrBefore(lastDateOfMonth)) {
			/*
				apRptSchedule = {
					"_id": "67755870e608711ea035c38d",
					"reportApiType": "NCC",
					"period": {	"endDate": "20250101" },
					"state": "COMPLETE"
				}
			*/
			const apRptSchedule = apRptSchList.find(sch => sch.period.endDate === currentDate.format('YYYYMMDD'));
			if (apRptSchedule) {
				apRptSchedules.push(new SilvergreySchedule(
					apRptSchedule.period.endDate,
					apRptSchedule.state,
					await _getExpectedCompleteDateOfApReport(apRptSchedule.reportApiType, apRptSchedule.period.endDate, apRptOffset)
				));
			} else {
				// 해당 일자의 스케줄이 없는 경우
				apRptSchedules.push(new SilvergreySchedule(
					currentDate.format('YYYYMMDD'),
					SILVERGREY_STATE.NOT_REGISTERED,
					await _getExpectedCompleteDateOfApReport(apReportApiType, currentDate.format('YYYYMMDD'), apRptOffset)
				));
			}
			currentDate.add(1, 'days');
		}
	}

	return apRptSchedules;
}

/**
 *
 * @param apRptDate AP 리포트 날짜 "20250101"
 * @param reportApiType 연동 타입 "NCC",
 * @param apRptOffset AP 리포트가 며칠 전 리포트를 제공하는지(AdProviders.reportApi.period.end). 음수임
 * @returns {Promise<*|moment.Moment|moment.Moment>}
 * @private
 */
const _getExpectedCompleteDateOfApReport = async (reportApiType, apRptDate, apRptOffset) => {
	// AP 리포트 연동 시간을 가져와서
	const env = await Environments.findOne({ 'name': 'report-api-schedule-time' }).select('value.' + reportApiType);
	if (!_.isNil(env) &&
		!_.isNil(env.value) &&
		!_.isNil(env.value[reportApiType])) {
		const timeStr = env.value[reportApiType].split(':');
		if (timeStr.length !== 2) {
			// Environments에 등록된 AP 스케줄이 포맷이 올바르지 않음. 통과로 간주하기 위해 현재시간으로 설정
			logger.debug(`Environments에 등록된 AP 스케줄이 포맷이 올바르지 않아 통과로 간주하기 위해 현재 시간으로 설정`);
			return moment();
		} else {
			// 연동 시간과 분을 가져옴
			const hour = parseInt(timeStr[0]);
			const minute = parseInt(timeStr[1]);

			// AP 리포트가 생성되기를 기다리기 위한 버퍼(분)
			let apReportWaitingMinutes = 10; // 디폴트 10 분
			const env = await DataEnvironments.findOne({ 'name': 'revenue-sharing-report-ap-report-waiting-minutes' }).select('value');
			if (!_.isNil(env) && !_.isNil(env.value)) {
				apReportWaitingMinutes = parseInt(env.value);
			}

			const criteria = moment(apRptDate, 'YYYYMMDD') // 구하고자 하는 리포트의 날짜를 설정하고
				// 그 리포트가 며칠 뒤에 생성되는지 설정
				// apRptOffset(=AdProviders.reportApi.period.end)가 음수이므로 -1을 곱해서 양수로 계산
				.add(-1 * apRptOffset, 'days')

				// AP 리포트 스케줄의 시,분 설정
				.hours(hour)
				.minutes(minute)
				.seconds(0)
				.milliseconds(0)

				// AP 리포트가 생성되기를 기다리기 위한 버퍼 적용
				.add(apReportWaitingMinutes, 'minutes');

			return criteria;
		}
	} else {
		logger.debug(`Environments에 등록된 AP 스케줄이 없어 통과로 간주하기 위해 현재 시간으로 설정`);
		return moment();
	}
};

/**
 * ZRGFP와 AP 리포트 상태를 종합한 상태 결정
 * @param date yyyyMMdd or yyyyMM
 * @param silvergreyDetails
 * @returns {Promise<string>}
 * @private
 */
const _decideRsRptTaskState = async (txId, date, silvergreyDetails) => {
	let rsRptTaskState = TASK_STATE.READY;

	const isCompleteZrgfp = await _isCompleteZrgfp(txId, date);
	const silvergreyState = await _getSilvergreyState(date, silvergreyDetails);

	if (isCompleteZrgfp && silvergreyState === SILVERGREY_STATE.COMPLETE) {
		rsRptTaskState = TASK_STATE.SATISFIED; // ZRGFP 완료, AP리포트 완료
	} else if (isCompleteZrgfp) {
		rsRptTaskState = TASK_STATE.WAIT_SG; // ZRGFP 완료, AP리포트 대기
	} else if (silvergreyState === SILVERGREY_STATE.COMPLETE) {
		rsRptTaskState = TASK_STATE.WAIT_ZRGFP; // ZRGFP 대기, AP리포트 완료
	} else {
		// AP_RPT_STATE.READY
		rsRptTaskState = TASK_STATE.WAIT; // ZRGFP 대기, AP리포트 대기
	}

	return rsRptTaskState;
}

const _decideStateByTaskState = (taskState) => {
	if ([TASK_STATE.READY, TASK_STATE.WAIT, TASK_STATE.WAIT_SG, TASK_STATE.WAIT_ZRGFP, TASK_STATE.SATISFIED].includes(taskState)) {
		return STATE.READY;
	}

	if ([TASK_STATE.AGG_IN_PROGRESS, TASK_STATE.UPLOAD_IN_PROGRESS].includes(taskState)) {
		return STATE.IN_PROGRESS;
	}

	// 재처리 대기(TASK_STATE_REPROCESS)는 COMPLETE로 간주
	if ([TASK_STATE.AGG_COMPLETE, TASK_STATE.UPLOAD_COMPLETE, TASK_STATE.REPROCESS].includes(taskState)) {
		return STATE.COMPLETE;
	}

	if ([TASK_STATE.AGG_FAILURE, TASK_STATE.UPLOAD_FAILURE].includes(taskState)) {
		return STATE.FAILURE;
	}
};

/**
 * AP 리포트 연동 상태 조회
 * @param silvergreyDetails
 * @returns {Promise<string>}
 * @private
 */
const _getSilvergreyState = async (date, silvergreyDetails) => {
	if (silvergreyDetails.length < 1) {
		// MappingHistory에 등록된 플레이스가 없어 AP 목록이 없어 연동정보가 없으므로 COMPLETE로 간주
		return SILVERGREY_STATE.COMPLETE;
	} else {
		// 월별 리포트는 해당 월 말일자 상태만 검사
		if (date.length == 6) {
			const lastDayOfMonth = moment(date, 'YYYYMM').endOf('month').format('YYYYMMDD');
			silvergreyDetails = silvergreyDetails.filter(detail => detail.date === lastDayOfMonth);
		}

		if (silvergreyDetails.every(detail =>
			// AP 리포트 연동 정보가 기술되지 않았거나 AP 리포트 스케줄이 없거나 연동상태가 OFF이거나 COMPLETE이면
			// AP 리포트의 종합 상태 = AP_RPT_STATE.COMPLETE
			[SILVERGREY_STATE.NOT_SPECIFIED, SILVERGREY_STATE.NOT_REGISTERED, SILVERGREY_STATE.OFF, SILVERGREY_STATE.COMPLETE].includes(detail.state))) {
			return SILVERGREY_STATE.COMPLETE;
		} else {
			return SILVERGREY_STATE.READY;
		}
	}
}

/**
 * 해당 일자 Zircon R GFP가 완료됐는지
 * @param date
 * @returns {Promise<*|boolean>}
 * @private
 */
const _isCompleteZrgfp = module.exports.isCompleteZrgfp = async (txId, date) => {
	const doc = await DataEnvironments.findOne({ 'name': 'zircon-r-gfp-recent-compaction-ymd' });
	const recentZrgfpCompactionYmd = doc.value;

	const result = date.length === 6
		? recentZrgfpCompactionYmd >= moment(date, 'YYYYMM').endOf('month').format('YYYYMMDD')
		: recentZrgfpCompactionYmd >= date;

	// log.debug(`${LOG_PREFIX} txId=${txId} _isCompleteZrgfp() date=${date} recentZrgfpCompactionYmd=${recentZrgfpCompactionYmd} result=${result}`);
	return result;
}

/**
 * 수익쉐어 리포트 업로드 (HDFS -> NUBES)
 * 	- taskState = AGG_COMPLETE 인 건들만 업로드 대상임
 */
module.exports.upload = async (txId, reportId) => {
	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: upload][txId=${txId}] reportId= ${reportId}`);

	let localDir = '';

	try {
		// 1. RevenueSharingReport 정보 조회
		// 		- 집계에 성공한 경우 AGG_COMPLETE
		// 		- 누베스 업로드에 실패하여 다시 시도하는 경우
		// 			- spark DAG에서 수동으로 호출한 경우) UPLOAD_FAILURE
		// 			- trigger_xx_revenue_sharing_report DAG에서 자도으로 호출된 경우 UPLOAD_IN_PROGRESS
		const report = await _getRevenueSharingReport(txId, reportId, [TASK_STATE.AGG_COMPLETE, TASK_STATE.UPLOAD_IN_PROGRESS, TASK_STATE.UPLOAD_FAILURE]);

		// [ERROR] 리포트 정보가 없는 경우, 에러
		if (_.isEmpty(report)) {
			throw new BusinessError({ message: `${LOG_PREFIX} [revenue-sharing-report3.service :: _getRevenueSharingReport][txId=${txId}] DB 에 taskState=AGG_COMPLETE 인 RevenueSharingReport 정보가 없음 ( reportId= ${reportId} )` });
		}

		// 2. RevenueSharingReport 업로드 처리 중으로 변경 (UPLOAD_IN_PROGRESS)
		await _updateRevenueSharingReport(txId, { reportId, taskState: TASK_STATE.UPLOAD_IN_PROGRESS });

		// 3. HDFS & LOCAL & NUBES 리포트 파일 경로 정보 셋팅
		// 		hdfs
		// 				hdfsSuccessFile = /user/gfp-data/revenue_sharing_report/2024/12/scheduleId=67332120f9f5f067ab274d6c/202412/_SUCCESS
		// 				hdfsReportFile  = /user/gfp-data/revenue_sharing_report/2024/12/scheduleId=67332120f9f5f067ab274d6c/RevenueSharingReport_20241203_67332120f9f5f067ab274d6c.csv
		// 		local
		// 				localReportDir  = local_download/report/revenue_sharing_report/2024/12/scheduleId=67332120f9f5f067ab274d6c
		// 				localReportFile = local_download/report/revenue_sharing_report/2024/12/scheduleId=67332120f9f5f067ab274d6c/RevenueSharingReport_20241203_67332120f9f5f067ab274d6c.csv
		// 		nubes
		// 				nubesReportFile = /revenue_sharing_report/2024/12/scheduleId=67332120f9f5f067ab274d6c/RevenueSharingReport_20241203_67332120f9f5f067ab274d6c.csv
		let {
			localReportDir, localReportFile,
			hdfsSuccessFile, hdfsReportFile,
			nubesReportFile,
		} = _getFilePathInfo(txId, report);

		localDir = localReportDir;

		// 4. HDFS 에 Success 파일이 있는지 체크 (리포트 생성 완료 여부 체크)
		await _isExistHdfsSuccess(hdfsSuccessFile);

		// 5. 파일 다운로드 ( HDFS -> LOCAL )
		await _download(txId, hdfsReportFile, localReportFile);

		// 6. 파일 크기 검증 ( LOCAL vs HDFS )
		await _validateFileSize(txId, {
			localReportFile,
			remoteReportFile: hdfsReportFile,
			storageType: STORAGE_TYPE.HDFS
		});

		// 7. 파일 압축 및 경로 정보 변경
		({ localReportFile, nubesReportFile } = await _makeZipFile(txId, localReportFile, nubesReportFile));

		// 8. 파일 업로드 ( LOCAL -> NUBES )
		await _upload(txId, localReportFile, nubesReportFile);

		// 9. 파일 크기 검증 ( LOCAL vs NUBES )
		const fileSize = await _validateFileSize(txId, {
			localReportFile,
			remoteReportFile: nubesReportFile,
			storageType: STORAGE_TYPE.NUBES
		});

		// 10. RevenueSharingReport 처리 성공 (UPLOAD_COMPLETE)
		await _updateRevenueSharingReport(txId, {
			reportId,
			taskState: TASK_STATE.UPLOAD_COMPLETE,
			filePath: nubesReportFile,
			fileSize
		});
	} catch (e) {
		log.error(`${LOG_PREFIX} [revenue-sharing-report3.service :: upload][txId=${txId}] Error :: ( reportId= ${reportId} ) \n ${e.stack}\n\n`, e);

		// 10. RevenueSharingReport 처리 실패 (UPLOAD_FAILURE)
		await _updateRevenueSharingReport(txId, { reportId, taskState: TASK_STATE.UPLOAD_FAILURE });

		throw e;
	} finally {
		// 11. LOCAL 디렉토리 & 파일 삭제
		_deleteLocalDir(txId, localDir);
	}
};


/**
 * 1. _getRevenueSharingReport : RevenueSharingReport 정보 조회
 * 		수익쉐어 리포트 정보 조회
 */
const _getRevenueSharingReport = async (txId, reportId, taskStateList) => {
	// log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _getRevenueSharingReport][txId=${txId}] RevenueSharingReport 정보 조회 ( reportId= ${reportId}, taskState= ${taskStateList} )`);
	const report = await RevenueSharingReport.findOne({ _id: ObjectId(reportId), taskState: { '$in': taskStateList } });
	// log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _getRevenueSharingReport][txId=${txId}] RevenueSharingReport 정보 조회 결과 ( report= ${JSON.stringify(report, null, 2)} )`);

	return report;
};


/**
 * _updateRevenueSharingReport : RevenueSharingReport 업데이트
 * 		- UPLOAD_IN_PROGRESS :: taskState
 * 		- UPLOAD_COMPLETE :: taskState, filePath, fileSize
 * 		- UPLOAD_FAILURE :: taskState
 */
const _updateRevenueSharingReport = async (txId, { reportId, taskState, filePath, fileSize }) => {
	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _updateRevenueSharingReport][txId=${txId}] RevenueSharingReport 변경 ( reportId= ${reportId}, taskState= ${taskState}, filePath= ${filePath}, fileSize= ${fileSize} )`);

	const set = { taskState };

	if (taskState === TASK_STATE.UPLOAD_COMPLETE) {
		set.filePath = filePath;
		set.fileSize = fileSize;
	}

	await RevenueSharingReport.findOneAndUpdate(
		{ _id: ObjectId(reportId) },
		{ $set: set },
		{ new: true, runValidators: true }
	);
};


/**
 * // HDFS_DIR : /user/gfp-data/revenue_sharing_report
 * const HDFS_DIR = config.report.revenue_sharing3.hdfs.root_dir;
 *
 * // LOCAL_DIR : local_download/report/revenuesharing
 * const LOCAL_DIR = path.join(config.local_root, config.report.path, config.report.revenue_sharing3.path);
 *
 * // NUBES_DIR : revenuesharing2
 * const NUBES_DIR = path.join(config.report.revenue_sharing3.nubes.root_dir);
 *
 *  3. _getFilePathInfo : HDFS & LOCAL & NUBES 리포트 파일 경로 정보
 */
const _getFilePathInfo = (txId, { _id, schedule_id, date }) => {
	const yyyy = date.substring(0, 4);
	const mm = date.substring(4, 6);

	const schIdPath = `scheduleId=${schedule_id}`

	// fileName = 20240315.csv
	const fileName = `RevenueSharingReport_${date}_${schedule_id}.csv`;

	/* hdfs 파일 정보 */
	// hdfsPath        = /user/gfp-data/revenue_sharing_report/2024/12/scheduleId=67332120f9f5f067ab274d6c
	const hdfsPath = path.join(HDFS_DIR, yyyy, mm, schIdPath)

	// hdfsSuccessFile = /user/gfp-data/revenue_sharing_report/2024/12/scheduleId=67332120f9f5f067ab274d6c/202412/_SUCCESS
	const hdfsSuccessFile = path.join(hdfsPath, date, '_SUCCESS');

	// hdfsReportFile  = /user/gfp-data/revenue_sharing_report/2024/12/scheduleId=67332120f9f5f067ab274d6c/RevenueSharingReport_20241203_67332120f9f5f067ab274d6c.csv
	const hdfsReportFile = path.join(hdfsPath, fileName);


	/* local 파일 정보 */
	// localReportDir  = local_download/report/revenue_sharing_report/2024/12/scheduleId=67332120f9f5f067ab274d6c
	const localReportDir = file.mkdirIfNotExist(path.join(LOCAL_DIR, yyyy, mm, schIdPath));

	// localReportFile = local_download/report/revenue_sharing_report/2024/12/scheduleId=67332120f9f5f067ab274d6c/RevenueSharingReport_20241203_67332120f9f5f067ab274d6c.csv
	const localReportFile = path.join(localReportDir, fileName);


	/* nubes 파일 정보 */
	// nubesReportFile = /revenue_sharing_report/2024/12/scheduleId=67332120f9f5f067ab274d6c/RevenueSharingReport_20241203_67332120f9f5f067ab274d6c.csv
	const nubesReportFile = path.join(NUBES_DIR, yyyy, mm, schIdPath, fileName);

	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _getFilePathInfo][txId=${txId}] HDFS & LOCAL & NUBES 리포트 파일 경로 정보
		- hdfsReportFile= ${hdfsReportFile}
		- localReportFile= ${localReportFile}
		- nubesReportFile= ${nubesReportFile}`);

	return { localReportDir, localReportFile, hdfsSuccessFile, hdfsReportFile, nubesReportFile };
};


/**
 * 4. _isExistHdfsSuccess : hdfs 에 success 파일이 존재하는지 확인
 *		- success 파일 경로 : /user/gfp-data/performance-report/{reqDate}/reportNo={reportNo}/_SUCCESS
 */
const _isExistHdfsSuccess = async hdfsSuccessFile => {
	// log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _isExistHdfsSuccess] hdfsSuccessFile= ${hdfsSuccessFile}`);

	const isExist = await c3HdfsApi.exists(hdfsSuccessFile);

	if (!isExist) {
		throw new BusinessError({ message: `${LOG_PREFIX} [revenue-sharing-report3.service :: _isExistHdfsSuccess] HDFS _SUCCESS 파일이 존재 하지 않음 ( hdfsSuccessFile= ${hdfsSuccessFile} )` });
	}
};


/**
 * 5. _download : 파일 다운로드 ( HDFS -> LOCAL )
 */
const _download = async (txId, hdfsReportFile, localReportFile) => {
	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _download][txId=${txId}] HDFS 파일 다운로드 시작 
	hdfsReportFile= ${hdfsReportFile}
	localReportFile= ${localReportFile}`);

	if (!await c3HdfsApi.exists(hdfsReportFile)) {
		throw new BusinessError({ message: `${LOG_PREFIX} [revenue-sharing-report3.service :: _download][txId=${txId}] HDFS 에 원본 파일이 존재 하지 않음 (hdfsReportFile= ${hdfsReportFile})` });
	}

	// hdfsReportFile = /user/gfp-data/performance-report/20230401/reportNo=xxxxxx/RevenueSharingReport_20240301_20240315.csv
	// localReportFile = local_download/report/performance/20230401/reportNo=xxxxxx/RevenueSharingReport_20240301_20240315.csv
	await c3HdfsCli.download(hdfsReportFile, localReportFile, true, false);

	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _download][txId=${txId}] HDFS 파일 다운로드 완료`);
};


/**
 * _validateFileSize : 파일 크기 검증
 */
const _validateFileSize = async (txId, { localReportFile, remoteReportFile, storageType }) => {
	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _validateFileSize][txId=${txId}] LOCAL vs ${storageType} 파일 크기 검증 
	localReportFile=${localReportFile}
	remoteReportFile=${remoteReportFile}`);

	const localFileSize = fs.statSync(localReportFile).size.toString();

	let remoteFileSize;

	if (_.isEqual(storageType, STORAGE_TYPE.HDFS)) {
		// HDFS 파일 사이즈
		const status = await c3HdfsApi.stat(remoteReportFile);
		remoteFileSize = status.length.toString();
	} else if (_.isEqual(storageType, STORAGE_TYPE.NUBES)) {
		// NUBES 파일 사이즈
		const status = await nubesCli.status(GATEWAY_ADDRESS, BUCKET, remoteReportFile);
		remoteFileSize = status['X-Object-Size'].toString();
	}

	// 파일 크기가 같지 않은 경우, Error
	if (localFileSize !== remoteFileSize) {
		throw new BusinessError({ message: `LOCAL vs ${storageType} 파일 크기 불일치 ( localFileSize= ${localFileSize}, remoteFileSize= ${remoteFileSize} )` });
	}

	return localFileSize;
};


/**
 * 7. _makeZipFile : 파일 압축
 * 		- 파일 사이즈가 100 MB 이상인 경우, zip 으로 압축
 * 		- 로컬 및 누베스 파일 경로를 zip 경로로 변경
 */
const _makeZipFile = async (txId, localReportFile, nubesReportFile) => {
	logger.debug(`[revenue-sharing-report3.service :: _makeZipFile][txId=${txId}] 파일 압축 ( localReportFile= ${localReportFile} )`);

	// - localReportFile = local_download/report/revenue_sharing_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv
	// - nubesReportFile = /revenue_sharing_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv

	const localFileSize = fs.statSync(localReportFile).size.toString();
	const minFileSize = await _getMinFileSizeForCompression();

	// 파일 사이즈가 100 MB 이하인 경우, 압축 하지 않음
	if (localFileSize < minFileSize) {
		logger.debug(`[revenue-sharing-report3.service :: _makeZipFile][txId=${txId}] 파일 압축 대상 아님 ( localReportFile= ${localReportFile}, localFileSize= ${localFileSize} )`);

		return { localReportFile, nubesReportFile };
	}

	const localFileZipSize = await new Promise((resolve, reject) => {
		// fileName = PerformanceReport_20240301_20240315.csv
		const fileName = path.parse(localReportFile).base;
		const writeStream = fs.createWriteStream(`${localReportFile}.zip`);
		const archive = archiver('zip', { zlib: { level: 6 } }); // 압축 레벨 설정 (0-9). 디폴트 6

		writeStream.on('finish', () => {
			logger.debug(`[revenue-sharing-report3.service :: _makeZipFile][txId=${txId}] writeStream finish`);

			logger.debug(`압축 파일 크기 : ${archive.pointer()} bytes`);

			resolve(archive.pointer());
		});

		archive.on('warning', err => {
			logger.debug(`[revenue-sharing-report3.service :: _makeZipFile][txId=${txId}] archive warning`);

			closeStreams();

			reject(err);
		});

		archive.on('error', err => {
			logger.debug(`[revenue-sharing-report3.service :: _makeZipFile][txId=${txId}] archive error`);

			closeStreams();

			reject(err);
		});

		archive.pipe(writeStream);

		archive.file(localReportFile, { name: fileName });

		archive.finalize();

		// 모든 stream 종료 처리
		const closeStreams = () => {
			if (writeStream) writeStream.close();
		};
	});

	// 로컬 및 누베스 파일 경로를 zip 경로로 변경
	// - localReportFile = local_download/report/performance_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv.zip
	// - nubesReportFile = /performance_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv.zip
	localReportFile = `${localReportFile}.zip`;
	nubesReportFile = `${nubesReportFile}.zip`;

	logger.debug(`[revenue-sharing-report3.service :: _makeZipFile][txId=${txId}] 파일 압축 결과 ( 전= ${(localFileSize / (1024 * 1024)).toFixed(2)} MB, 후= ${(localFileZipSize / (1024 * 1024)).toFixed(2)} MB )`);
	logger.debug(`[revenue-sharing-report3.service :: _makeZipFile][txId=${txId}] 파일 압축 및 경로 변경 완료 ( localReportFile= ${localReportFile}, nubesReportFile= ${nubesReportFile} )`);

	return { localReportFile, nubesReportFile };
};


const _getMinFileSizeForCompression = async () => {
	// 압축을 위한 최소 파일 크기 (MB 단위)
	const env = await DataEnvironments.findOne({ name: 'min-file-size-for-compression' });
	const minFileSize = env ? env.value : 100;

	// Byte 단위로 변환
	return minFileSize * 1024 * 1024;
}


/**
 * _upload : 파일 업로드 ( LOCAL -> NUBES )
 */
const _upload = async (txId, localReportFile, nubesReportFile) => {
	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _download][txId=${txId}] NUBES 파일 업로드 시작
	localReportFile= ${localReportFile}
	nubesReportFile= ${nubesReportFile}`);

	// parallel upload (overwrite = true)
	await nubesCli.pupload(GATEWAY_ADDRESS, BUCKET, localReportFile, nubesReportFile);

	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _upload][txId=${txId}] NUBES 파일 업로드 완료`);
};


/**
 * 9. _deleteLocalDir : LOCAL 디렉토리 & 파일 삭제
 */
const _deleteLocalDir = (txId, localDir) => {
	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _deleteLocalDir][txId=${txId}] localDir=${localDir} 삭제 시작`);

	if (fs.existsSync(localDir)) {
		// 디렉토리 & 파일 삭제
		file.deleteDirectoryRecursively(localDir, true);
	}

	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _deleteLocalDir][txId=${txId}] localDir=${localDir} 삭제 완료`);
};

module.exports.getReportById = async (report_id) => {
	return await this.getReportsWithMeta({ '_id': report_id })
}

module.exports.updateReport = async (report_id, update) => {
	await RevenueSharingReport.updateOne({ '_id': report_id }, update);

}

// /**
//  * 수익쉐어 리포트 HDFS & NUBES 삭제
//  *     - 1주 전 한달치 리포트 HDFS & NUBES 일괄 삭제
//  *     - ex> 12월 8일에 실행 시, 10월 30일 ~ 11월 30일 일괄 삭제 처리됨
//  *     - ex> 12월 15일에 실행 시, 11월 7일 ~ 12월 7일 일괄 삭제 처리됨
//  *     - ex> 12월 22일에 실행 시, 11월 14일 ~ 12월 14일 일괄 삭제 처리됨
//  */
// module.exports.delete = async (txId) => {
// 	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: delete][txId=${txId}]`);
//
// 	// 1주 전 기준으로 한달치 디렉토리 삭제 (오늘이 12월 15일이면 11월 7일 ~ 12월 7일 일괄 삭제)
// 	// targetDate 이전 리포트 파일 일괄 삭제 (include)
// 	const targetEndDate = moment().subtract(8, 'days').startOf('day');
// 	const targetStartDate = moment(targetEndDate).subtract(1, 'months');
//
// 	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: delete][txId=${txId}] ${targetStartDate.format('YYYY-MM-DD')} ~ ${targetEndDate.format('YYYY-MM-DD')} 일괄 삭제 시작`);
//
// 	const dateList = await _getDateList(targetStartDate, targetEndDate);
//
// 	// HDFS 일괄 삭제
// 	await _deleteHdfs(txId, dateList);
//
// 	// NUBES 일괄 삭제
// 	await _deleteNubes(txId, dateList);
//
// 	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: delete][txId=${txId}] ${targetStartDate.format('YYYY-MM-DD')} ~ ${targetEndDate.format('YYYY-MM-DD')} 일괄 삭제 완료`);
// };
//
//
// /**
//  * 날짜 리스트
//  */
// const _getDateList = async (startDate, endDate) => {
// 	const dateList = [];
//
// 	let tempDate = moment(startDate);
// 	while (tempDate.isSameOrBefore(endDate)) {
// 		dateList.push(tempDate.format('YYYYMMDD'));
//
// 		tempDate.add(1, 'days');
// 	}
//
// 	return dateList;
// }
//
// 
// /**
//  * HDFS 일괄 삭제
//  */
// const _deleteHdfs = async (txId, dateList) => {
// 	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _deleteHdfs][txId=${txId}] HDFS 일괄 삭제 시작`);
//
// 	await Promise.all(dateList.map(async date => {
// 		// deleteDirPath = /user/gfp-data/performance-report/YYYYMMDD
// 		const deleteDirPath = path.join(HDFS_DIR, date);
//
// 		// 디렉토리 경로가 HDFS 에 존재하는지 확인
// 		if (await c3HdfsApi.exists(deleteDirPath)) {
// 			// 디렉토리 일괄 삭제하기
// 			const err = await c3HdfsApi.delete(deleteDirPath, true);
//
// 			// 에러가 발생한 경우
// 			if (err) {
// 				throw new BusinessError({message: `${LOG_PREFIX} [revenue-sharing-report3.service :: _deleteHdfs] HDFS 일괄 삭제 실패`}, {
// 					err,
// 					detail: JSON.stringify(err, null, 2)
// 				});
// 			}
// 		}
// 	}));
//
// 	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _deleteHdfs][txId=${txId}] HDFS 일괄 삭제 완료`);
// };
//
//
// /**
//  * NUBES 일괄 삭제
//  */
// const _deleteNubes = async (txId, dateList) => {
// 	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _deleteNubes][txId=${txId}] NUBES 일괄 삭제 시작`);
//
// 	await Promise.all(dateList.map(async date => {
// 		// deleteDirPath = performance/YYYYMMDD
// 		const deleteDirPath = path.join(RS_DIR, date);
//
// 		// 디렉토리 경로가 NUBES 에 존재하는지 확인
// 		if (await _existNubes(deleteDirPath)) {
// 			// 디렉토리 일괄 삭제하기
// 			const err = await nubesCli.delete(GATEWAY_ADDRESS, BUCKET, deleteDirPath, true);
//
// 			// 에러가 발생한 경우
// 			if (err) {
// 				throw new BusinessError({message: `${LOG_PREFIX} [revenue-sharing-report3.service :: _deleteNubes] NUBES 일괄 삭제 실패`}, {
// 					err,
// 					detail: JSON.stringify(err, null, 2)
// 				});
// 			}
// 		}
// 	}));
//
// 	log.debug(`${LOG_PREFIX} [revenue-sharing-report3.service :: _deleteNubes][txId=${txId}] NUBES 일괄 삭제 완료`);
// };
//
//
// /**
//  * NUBES 존재 유무 확인
//  */
// const _existNubes = async path => {
// 	try {
// 		await nubesCli.status(GATEWAY_ADDRESS, BUCKET, path);
//
// 		return true;
// 	} catch (e) {
// 		return false;
// 	}
// };


/*
-- MongoDB Query - (1)
-- AP 리포트 연동 상태 AdProviderInfos.reportApiStatus = 'ON' 인데 AP의 reportApi가 없는 경우
db.AdProviderInfos.aggregate([
{
    $match: {
//        _id: ObjectId('5f3de6cca4ad0b0031bd4be0')
        reportApiStatus: 'ON'
    }
},
// AdProviders
{
	'$lookup': {
		from: 'AdProviders',
		foreignField: '_id',
		localField: 'adProvider_id',
		as: 'ap'
	}
},
{'$unwind': '$ap'},
// AdProviders
{
	'$lookup': {
		from: 'Publishers',
		foreignField: '_id',
		localField: 'publisher_id',
		as: 'pub'
	}
},
{'$unwind': '$pub'},
{
    $match: {
       'ap.reportApi.period': {$exists: false}
    }
},
{
    $project: {
        pub_id: '$pub._id',
        pub_name: '$pub.name',
        ap_id: '$ap._id',
        ap_name: '$ap.name',
        ap_reportApi: '$ap.reportApi'
    }
},
{
    $sort: { pub_name:1, ap_name: 1}
}
]);
 */
