'use strict';

import csv from 'csv';
import parse from 'csv-parse';
import fs from 'fs';
import _ from 'lodash';
import path from 'path';
import zlib from 'zlib';
import * as c3HdfsCli from '../../../../c3/hdfs-cli';
import cLogger from '../../../../common/logger';
import initDatabase from '../../../../common/mongooseForChildProcess';

import config from '../../../../config/config';
import { AdProvider } from '../../../../models/ad-providers.schema';
import { AdUnit } from '../../../../models/ad-unit.schema';
import { BiddingGroup } from '../../../../models/bidding-group.schema';
import { Country } from '../../../../models/country.schema';
import { GFDDeal } from '../../../../models/gfd-deals.schema';
import { NativeAdStyle } from '../../../../models/native-ad-style.schema';
import { Place } from '../../../../models/places.schema';
import { PublisherService } from "../../../../models/publisher-services.schema";

import * as fileUtil from '../../../../utils/file.util';
import extendedLogger from '../../../../utils/logger-ext.util';

const log = extendedLogger('md'); // 확장로거

const AdUnitCache = new Map();
const AdProviderCache = new Map();
const ServiceCache = new Map();
const BiddingGroupCache = new Map();
const DealCache = new Map();
const PlaceCache = new Map();
const NativeAdStyleCache = new Map();
const CountryCache = new Map();

let runId = '';

/**
 * child process 작업 수행
 *
 	bizCloudFileInfo:	{
		report
		idx
		localFilePath : "/home1/local_download/report/multidimensional/2023/07/{매체명}_{리포트명}_{YYYYMMDD_HHmmss}/xxx.csv.gz"
		remoteFilePath : "/data/log/gfp/gfp-multi-dimensional-report/{publisherId}/{reportId}/xxx.csv.gz"
	}
 */
process.on('message', async (bizCloudFileInfo) => {
	let database;

	const report = bizCloudFileInfo.report;
	const lastHistory_id = report.lastHistory._id;

	try {
		cLogger(config);

		database = await initDatabase(config).catch(error => log.error(`[MDR-C3] multi-dimensional-report-csv-child.service.process.on() :: ${error.stack}`));

		await _initCache(report);

		// c3로부터 다운로드
		const c3FilePath = await _downloadFromC3(bizCloudFileInfo.remoteFilePath, bizCloudFileInfo.localFilePath);

		runId = `[MDR-C3] childProc[${report.idx}]  TXID:${report.txId}  PUB:${report.publisherName}(${report.publisher_id})  RPT:${report.name}(${report._id}) HIST_ID:${lastHistory_id}\n\tc3FilePath: ${c3FilePath}`;

		// 다운로드 받은 c3 파일을 읽어 리포트(중간버전) 만들기
		const statsLength = await _processBizCloudFile(bizCloudFileInfo.report, c3FilePath);

		// csv 파일들을 합치기 전에, 처리가 끝난 다운로드 파일은 삭제
		fs.unlinkSync(c3FilePath);

		await database.db.close();

		process.send({op: 'complete', msg: `완료`, statsLength});
	} catch (error) {
		log.error(`[MDR-C3] multi-dimensional-report-csv-child.service.process.on() :: ${error.stack}`);

		await database.db.close();

		process.send({op: 'fail', msg: error.message});
	}
});

/**
 * C3로부터 파일 다운로드
 * @param remoteFilePath
 * @param localFilePath : "/home1/local_download/report/multidimensional/{yyyy}/{mm}/{매체명}_{리포트명}_{YYYYMMDD_HHmmss}/xxx.csv.gz"
 * @private
 */
const _downloadFromC3 = async (remoteFilePath, localFilePath) => {
	// localFilePath 중간에 "/c3" 경로 끼워 넣고 c3 경로 없으면 만들고
	const localFileInfo = path.parse(localFilePath);
	const c3Dir =  path.join(localFileInfo.dir, 'c3');
	fileUtil.mkdirIfNotExist(c3Dir);

	// c3 경로에 다운로드
	// c3FilePath = "/home1/local_download/report/multidimensional/2023/07/{매체명}_{리포트명}_{YYYYMMDD_HHmmss}/c3/xxx.csv.gz"
	const c3FilePath = path.join(c3Dir, localFileInfo.base);
	await c3HdfsCli.download(remoteFilePath, c3FilePath);

	return c3FilePath;
};

/**
 * C3로부터 다운받은 파일을 읽어 메타 데이터를 붙여 로컬에 저장한다.
 * @param report
 * @param localC3PathToRead
 * 		"/home1/local_download/report/multidimensional/{yyyy}/{mm}/{매체명}_{리포트명}_{YYYYMMDD_HHmmss}/c3/xxx.csv.gz"
 * @returns {Promise<unknown>}
 * @private
 */
const _processBizCloudFile = (report, localC3PathToRead) => {
	return new Promise((resolve, reject) => {
		try {
			let statsLength = 0;

			// ------------ 로컬 파일 읽기 스트림
			const readStream = fs.createReadStream(localC3PathToRead)
				.on('error', function onError(err) {
					log.error(`${runId} 로컬 파일 읽기 에러. localC3PathToRead:${localC3PathToRead} "error" 이벤트 ${err}`);
					reject(err);
				});

			// ------------ csv parser
			const parser = parse({
				escape: '\\', enclosed: '"', columns: true
			});
			parser.on('error', err => {
				log.error(`${runId} csv로 읽기 에러. localC3PathToRead:${localC3PathToRead} "error" 이벤트. ${err.stack}`);
				readStream.destroy();
				reject(err);
			});

			// ------------ 데이터 변환 스트림
			const transformer = csv.transform(function (data) {
				const row = _getRow(report, data);
				statsLength++;
				return row;
			});

			// ------------ 쓰기 스트림
			const info = path.parse(localC3PathToRead);
			const idx = localC3PathToRead.indexOf(`${path.sep}c3${path.sep}`); // '/c3/' 떼고
			const fileName = info.base.replace('.csv.gz', '.csv'); // '.gz'이라면 '.gz' 떼고
			const localPathToWrite = path.join(localC3PathToRead.substring(0, idx), fileName); // "/home1/local_download/report/multidimensional/2023/07/{매체명}_{리포트명}_{YYYYMMDD_HHmmss}/xxx.csv"
			log.debug(`${runId}\n\tlocalPathToWrite: ${localPathToWrite}`);

			const writeStream = fs.createWriteStream(localPathToWrite, {
				flags: 'a',
				autoClose: true,
				encoding: 'utf8',
				highWaterMark: 64 * 1024
			}); // 64 kb
			writeStream
				.on('close', () => {
					resolve(statsLength);
				})
				.on('error', err => {
					log.error(`${runId} 로컬파일쓰기 에러. localPathToWrite:${localPathToWrite} "error" 이벤트. ${err.stack}`);
					readStream.destroy();
					reject(err);
				});

			// ------------ 스트림 연결
			if (localC3PathToRead.endsWith('.gz')) {
				const gunzip = zlib.createGunzip(); // 압축풀기
				readStream.pipe(gunzip).pipe(parser).pipe(transformer).pipe(writeStream);
			} else {
				readStream.pipe(parser).pipe(transformer).pipe(writeStream);
			}
		} catch(err) {
			reject(err);
		}
	});
};

/**
 * Row 가공
 * @param report
 * @param stat
 * @returns {*}
 * @private
 */
const _getRow = (report, stat) => {
	let row = stat['datetime'];

	// 일반 디멘젼
	const dims = report.conditionForUi.generalDimensions.filter(dim => dim.name !== 'year' && dim.name !== 'month' && dim.name !== 'date' && dim.name !== 'hour');
	for (const dim of dims) {
		const value = stat[dim.name];

		if (dim.name === 'country') {
			row += ',' + (CountryCache.has(value) ? CountryCache.get(value) : '-'); //국가코드를 이름으로
		} else if (dim.name === 'adUnit') {
			if (report.cmsType === 'GFP') {
				row += ',' + _wrapWordWithDoubleQuotation(value);
			} else {
				// NAM 리포트일 경우 adUnitId 표현하지 않음
			}
		} else {
			row += ',' + _wrapWordWithDoubleQuotation(value);
		}

		// _id에 대한 name 컬럼 추가
		if (dim.name === 'adUnit') {
			row += ',' + (AdUnitCache.has(value) ? AdUnitCache.get(value) : '-');
		} else if (dim.name === 'adProvider') {
			row += ',' + (AdProviderCache.has(value) ? AdProviderCache.get(value) : '-');
		} else if (dim.name === 'service') {
			row += ',' + (ServiceCache.has(value) ? ServiceCache.get(value) : '-');
		} else if (dim.name === 'biddingGroup') {
			row += ',' + (BiddingGroupCache.has(value) ? BiddingGroupCache.get(value) : '-');
		} else if (dim.name === 'deal') {
			row += ',' + (DealCache.has(value) ? DealCache.get(value) : '-');
		} else if (dim.name === 'place') {
			row += ',' + (PlaceCache.has(value) ? PlaceCache.get(value) : '-');
		} else if (dim.name === 'nativeAdStyle') {
			row += ',' + (NativeAdStyleCache.has(value) ? NativeAdStyleCache.get(value) : '-');
		}
	}

	// 키 디멘젼
	report.conditionForUi.keyDimensions.forEach(dim => {
		let value = stat['kv_' + dim.name];
		row += ',' +  (_.isEmpty(value) ? '-' : _wrapWordWithDoubleQuotation(decodeURIComponent(value)));
	});

	// 메트릭
	report.conditionForUi.metrics.forEach(metric => {
		row += ',' + stat[metric.name];
	});

	row += '\n';

	return row;
};


/**
 * csv 파일에서 콤마를 포함한 단어를 한 셀에 넣기 위해 큰 따옴표를 묶음
 * @param word
 * @returns {string}
 */
const _wrapWordWithDoubleQuotation = (word) => {
	if (word == undefined) return word;
	else return '"' + word.replace(/"/g, '""') + '"';
};

/**
 * _id에 대한 name을 갖고 있는 캐시 구성
 * @param report
 * @returns {Promise<void>}
 * @private
 */
const _initCache = async (report) => {
	const dims = report.conditionForUi.generalDimensions.filter(dim => dim.name !== 'year' && dim.name !== 'month' && dim.name !== 'date' && dim.name !== 'hour');
	for (const dim of dims) {
		if (dim.name === 'adUnit') {
			await _initAdUnitCache();
		} else if (dim.name === 'adProvider') {
			await _initAdProviderCache();
		} else if (dim.name === 'service') {
			await _initServiceCache();
		} else if (dim.name === 'biddingGroup') {
			await _initBiddingGroupCache();
		} else if (dim.name === 'deal') {
			await _initDealCache();
		} else if (dim.name === 'place') {
			await _initPlaceCache();
		} else if (dim.name === 'nativeAdStyle') {
			await _initNativeAdStyleCache();
		} else if (dim.name === 'country') {
			await _initCountryCache();
		}
	}
};

const _initAdUnitCache = async () => {
	const docs = await AdUnit.find({}).select('adUnitId name adUnitCd cmsType');
	docs.forEach(doc => {
		let val = doc.cmsType == 'GFP' ? doc.name : doc.adUnitCd;
		AdUnitCache.set(doc.adUnitId, _wrapWordWithDoubleQuotation(val))
	});
};

const _initAdProviderCache = async () => {
	const docs = await AdProvider.find({}).select('_id name');
	docs.forEach(doc => AdProviderCache.set(doc._id.toString(), _wrapWordWithDoubleQuotation(doc.name)));
};

const _initServiceCache = async () => {
	const docs = await PublisherService.find({}).select('_id name');
	docs.forEach(doc => ServiceCache.set(doc._id.toString(), _wrapWordWithDoubleQuotation(doc.name)));
};

const _initBiddingGroupCache = async () => {
	const docs = await BiddingGroup.find({}).select('_id name');
	docs.forEach(doc => BiddingGroupCache.set(doc._id.toString(), _wrapWordWithDoubleQuotation(doc.name)));
};

const _initDealCache = async () => {
	const docs = await GFDDeal.find({}).select('dealId name');
	docs.forEach(doc => DealCache.set(doc.dealId.toString(), _wrapWordWithDoubleQuotation(doc.name)));
};

const _initPlaceCache = async () => {
	const docs = await Place.find({}).select('_id name');
	docs.forEach(doc => PlaceCache.set(doc._id.toString(), _wrapWordWithDoubleQuotation(doc.name)));
};

const _initNativeAdStyleCache = async () => {
	const docs = await NativeAdStyle.find({}).select('_id name');
	docs.forEach(doc => NativeAdStyleCache.set(doc._id.toString(), _wrapWordWithDoubleQuotation(doc.name)));
};

const _initCountryCache = async () => {
	const docs = await Country.find({}).select('code name');
	docs.forEach(doc => CountryCache.set(doc.code, doc.name));
};
