'use strict';

import * as logger from '../../../../../utils/logger.util';
import config from '../../../../../config/config';
import mongoose from '../../../../../common/mongooseForChildProcess';

import { SummaryAdProviderStatsDaily } from '../../../../../models/summary-ad-provider-stats-daily.schema';


process.on('message', async (docs) => {
	const database = await mongoose(config).catch(error => logger.error(error));

	await insertSummaryData(docs);

	await database.db.close();

	process.send({op: 'COMPLETE', msg: ''});
});


const insertSummaryData = async (docs) => {
	await SummaryAdProviderStatsDaily.insertMany(docs, [{ordered: false}]);
};
