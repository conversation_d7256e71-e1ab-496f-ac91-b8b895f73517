'use strict';

import * as logger from '../../../../../utils/logger.util';
import config from '../../../../../config/config';
import mongoose from '../../../../../common/mongooseForChildProcess';

import { SummaryAdProviderStatsDeal } from  '../../../../../models/summary-ad-provider-stats-deal.schema';


process.on('message', async (docs) => {
	const database = await mongoose(config).catch(error => logger.error(error));

	await insertSummaryData(docs);

	await database.db.close();

	process.send({op: 'COMPLETE', msg: ''});
});


const insertSummaryData = async (docs) => {
	await SummaryAdProviderStatsDeal.insertMany(docs, [{ordered: false}]);
};
