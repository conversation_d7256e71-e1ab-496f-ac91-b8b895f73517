'use strict';

import mongoose from 'mongoose';
import moment from 'moment';
import { Readable } from 'stream';

import * as _ from 'lodash';
import * as logger from '../../../utils/logger.util';

import { AdProviderNonRkStat } from '../../../models/ad-provider-non-rk-stats.schema';
import { AdProviderInfo } from '../../../models/ad-provider-infos.schema';

let ObjectId = mongoose.Types.ObjectId;


/**
 * NonRk 기반의 AdProvider Stats Data 조회 - Ad Provider별
 *
 */
module.exports.fetchDataAdProvider = (job) => {
	logger.debug('[ad-provider-stats-summary-non-rk.service] fetchDataAdProvider() 호출됨');

	return new Promise(async resolve => {
		const results = await AdProviderNonRkStat.aggregate()
			.match({
				ymd: { $gte: job.startDate, $lte: job.endDate },
				adProvider_id: ObjectId(job.adProviderId),
				publisher_id: ObjectId(job.publisherId)
			})
			.lookup({
				from: 'SummaryTargetCountries', //AP Summary는 관리하는 country만을 대상으로 grouping
				localField: 'country',
				foreignField: 'countryCd',
				as : 'countryMap'
			})
			.project({
				ymd: 1,
				publisher_id: 1,
				adProvider_id: 1,
				countries: '$countryMap.countryCd',
				imp: 1,
				clk: 1,
				gfpNetRevenueKRW: 1,
				gfpNetRevenueUSD: 1,
				netRevenueKRW: 1,
				netRevenueUSD: 1
			})
			.group( {
				_id: {
					ymd: '$ymd',
					publisher_id: '$publisher_id',
					adProvider_id: '$adProvider_id',
					country: { $arrayElemAt: [ '$countries', 0 ] }
				},
				imp: { $sum: '$imp' },
				clk: { $sum: '$clk' },
				netRevenueKRW: { $sum: '$gfpNetRevenueKRW' },
				netRevenueUSD: { $sum: '$gfpNetRevenueUSD' },
				revenueKRW: { $sum: '$netRevenueKRW' },
				revenueUSD: { $sum: '$netRevenueUSD' }
			})
			.project({
				ymd: '$_id.ymd',
				publisher_id: '$_id.publisher_id',
				adProvider_id: '$_id.adProvider_id',
				country: '$_id.country',
				imp: 1,
				clk: 1,
				netRevenueKRW: 1,
				netRevenueUSD: 1,
				revenueKRW: 1,
				revenueUSD: 1,
				_id: 0
			})
			.read('secondaryPreferred')
			.readConcern('local')
			.allowDiskUse(true)
			.exec();

//logger.debug('results--------', results);
//logger.debug('누락처리 전 count--------', results.length);

		_fillEmptyDates(job, results, true);

//logger.debug('누락처리 후 count--------', results.length);

		resolve(results);
	});
};


const _fillEmptyDates = (job, results) => {
	const {
		ymd,
		start,
		end,
		publisherId,
		adProviderId
	} = job;

	for (let i=start; i<=end; i++) {
		const dd = moment(ymd).add(i, 'days').format('YYYYMMDD');

		const found = results.find(v => {
			return v.ymd === dd && v.publisher_id.toString() === publisherId && v.adProvider_id.toString() === adProviderId.toString();
		});

		if (! found) {
			//logger.debug('누락일자 처리--------', dd, publisherId, adProviderId);

			let arr = {
				ymd: dd,
				publisher_id: ObjectId(publisherId),
				adProvider_id: adProviderId,
				imp: 0,
				clk: 0,
				netRevenueKRW: 0,
				netRevenueUSD: 0,
				revenueKRW: 0,
				revenueUSD: 0,
			};

			results.push(arr);
		}
	}
};


/**
 * NonRk 기반의 AdProvider Stats Data Stream 조회 - Place Key별
 *
 */
module.exports.fetchDataStreamPlaceKey = async (job) => {
	logger.debug('[ad-provider-stats-summary-non-rk.service] fetchDataStreamPlaceKey() 호출됨');

	const allowPlaceKeyDataPulling = await _getAllowPlaceKeyDataPulling(job.publisherId, job.adProviderId);
	if (!allowPlaceKeyDataPulling) {
		const emptyStream = new Readable({ objectMode: true });
		emptyStream._read = () => {};
		emptyStream.push(null);

		return emptyStream;
	}

	return await AdProviderNonRkStat.aggregate()
		.match({
			ymd: { $gte: job.startDate, $lte: job.endDate },
			adProvider_id: ObjectId(job.adProviderId),
			publisher_id: ObjectId(job.publisherId)
		})
		.lookup({
			from: 'SummaryTargetCountries',
			localField: 'country',
			foreignField: 'countryCd',
			as : 'countryMap'
		})
		.project({
			ymd: 1,
			publisher_id: 1,
			adProvider_id: 1,
			adProviderPlaceKey: 1,
			dealId: 1,
			os: 1,
			countries: '$countryMap.countryCd',
			size: 1,
			adUnitIds: 1,
			adSource: 1,
			imp: 1,
			clk: 1,
			gfpNetRevenueKRW: 1,
			gfpNetRevenueUSD: 1,
			netRevenueKRW: 1,
			netRevenueUSD: 1,
			adProviderPlace_ids: 1
		})
		.lookup({
			from: 'Publishers',
			localField: 'publisher_id',
			foreignField: '_id',
			as : 'publisher'
		})
		.group({
			_id: {
				ymd: '$ymd',
				publisher_id: '$publisher_id',
				adProvider_id: '$adProvider_id',
				adProviderPlaceKey: '$adProviderPlaceKey',
				dealId: '$dealId',
				os: '$os',
				country: {
					$cond: [
						{ $arrayElemAt: [ '$countries', 0 ] },
						{ $arrayElemAt: [ '$countries', 0 ] },
						'-'
					]
				},
				size: '$size',
				adUnitIds: '$adUnitIds',
				adSource: '$adSource'
			} ,
			imp: {  $sum: '$imp' },
			clk: {  $sum: '$clk' },
			netRevenueKRW: { $sum: '$gfpNetRevenueKRW' },
			netRevenueUSD: { $sum: '$gfpNetRevenueUSD' },
			revenueKRW: { $sum: '$netRevenueKRW' },
			revenueUSD: { $sum: '$netRevenueUSD' },
			adProviderPlace_ids: { $addToSet: '$adProviderPlace_ids' }
		})
		.unwind('$adProviderPlace_ids')
		.unwind('$adProviderPlace_ids')
		.group({
			_id: {
				ymd: '$_id.ymd',
				publisher_id: '$_id.publisher_id',
				adProvider_id: '$_id.adProvider_id',
				adProviderPlaceKey: '$_id.adProviderPlaceKey',
				dealId: '$_id.dealId',
				os: '$_id.os',
				country: '$_id.country',
				size: '$_id.size',
				adSource: '$_id.adSource',
				imp: '$imp',
				clk: '$clk',
				netRevenueKRW: '$netRevenueKRW',
				netRevenueUSD: '$netRevenueUSD',
				revenueKRW: '$revenueKRW',
				revenueUSD: '$revenueUSD',
				adUnitIds: '$_id.adUnitIds'
			},
			adProviderPlace_ids: { $addToSet: '$adProviderPlace_ids' }
		})
		.project({
			ymd: '$_id.ymd',
			publisher_id: '$_id.publisher_id',
			adProvider_id: '$_id.adProvider_id',
			adProviderPlaceKey: '$_id.adProviderPlaceKey',
			dealId: '$_id.dealId',
			adProviderPlace_ids: 1,
			os: '$_id.os',
			country: '$_id.country',
			size: '$_id.size',
			adSource: '$_id.adSource',
			imp: '$_id.imp',
			clk: '$_id.clk',
			netRevenueKRW: '$_id.netRevenueKRW',
			netRevenueUSD: '$_id.netRevenueUSD',
			revenueKRW: '$_id.revenueKRW',
			revenueUSD: '$_id.revenueUSD',
			adUnitIds: '$_id.adUnitIds',
			_id: 0
		})
		//.sort({ publisher_id: 1, ymd: 1, adProvider_id: 1 } )
		.read('secondaryPreferred')
		.readConcern('local')
		.allowDiskUse(true)
		.cursor()  // { batchSize: 2000000 }
		.exec()
		//.stream()
		;

};


const _getAllowPlaceKeyDataPulling = async (publisherId, adProviderId) => {
	let allowPlaceKeyDataPulling = false;

	await AdProviderInfo.findOne({
		publisher_id: ObjectId(publisherId),
		adProvider_id: ObjectId(adProviderId),
	})
	.select('allowPlaceKeyDataPulling')
	.exec()
	.then(result => {
		if (!_.isEmpty(result) && !_.isNil(result.allowPlaceKeyDataPulling)) {
			allowPlaceKeyDataPulling = result.allowPlaceKeyDataPulling;
		}
	});

	return allowPlaceKeyDataPulling ? true : false;
};
