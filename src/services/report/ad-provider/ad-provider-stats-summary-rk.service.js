'use strict';

import _ from 'lodash';
import  moment from 'moment';
import mongoose from 'mongoose';
import COMMON_CODE from '@ssp/ssp-common-code';

import * as logger from '../../../utils/logger.util';

import { AdProviderInfo } from '../../../models/ad-provider-infos.schema';
import { AdProviderRkStat } from "../../../models/ad-provider-rk-stats.schema";

const CMS_TYPE = COMMON_CODE.codeEncAvailable()['CmsType'];

let ObjectId = mongoose.Types.ObjectId;

/**
 * Rk 기반의 AdProvider Stats Data 조회 - Ad Provider별
 *
 */
module.exports.fetchDataAdProvider = (job) => {
	logger.debug('[ad-provider-stats-summary-rk.service] fetchDataAdProvider() 호출됨');

	return new Promise(async resolve => {
		// adProvider와 리포트 연동 중인 GFP 매체 id 조회 (NAM 매체 필터)
		const gfpPublisherIds = await _getGfpPublisherIds(job.adProviderId);
		const publisher_ids = _.map(gfpPublisherIds, 'publisher_id');

		const results = await AdProviderRkStat.aggregate()
			.match({
				ymd: { $gte: job.startDate, $lte: job.endDate },
				adProvider_id: ObjectId(job.adProviderId),
				publisher_id: { $in: publisher_ids }
			})
			.lookup({
				from: 'SummaryTargetCountries', //AP Summary는 관리하는 country만을 대상으로 grouping
				localField: 'country',
				foreignField: 'countryCd',
				as : 'countryMap'
			})
			.project({
				ymd: 1,
				publisher_id: 1,
				adProvider_id: 1,
				countries: '$countryMap.countryCd',
				imp: 1,
				clk: 1,
				gfpNetRevenueKRW: 1,
				gfpNetRevenueUSD: 1,
				netRevenueKRW: 1,
				netRevenueUSD: 1
			})
			.group({
				_id: {
					ymd: '$ymd',
					publisher_id: '$publisher_id',
					adProvider_id: '$adProvider_id',
					country: { $arrayElemAt: [ '$countries', 0 ] }
				},
				imp: { $sum: '$imp' },
				clk: { $sum: '$clk' },
				netRevenueKRW: { $sum: '$gfpNetRevenueKRW' },
				netRevenueUSD: { $sum: '$gfpNetRevenueUSD' },
				revenueKRW: { $sum: '$netRevenueKRW' },
				revenueUSD: { $sum: '$netRevenueUSD' }
			})
			.project({
				ymd: '$_id.ymd',
				publisher_id: '$_id.publisher_id',
				adProvider_id: '$_id.adProvider_id',
				country: '$_id.country',
				imp: 1,
				clk: 1,
				netRevenueKRW: 1,
				netRevenueUSD: 1,
				revenueKRW: 1,
				revenueUSD: 1,
				_id: 0
			})
			//.sort({ publisher_id: 1, ymd: 1, adProvider_id: 1 })
			.read("secondaryPreferred")
			.readConcern('local')
			.allowDiskUse(true)
			.exec();

		let publishers = _.chain(results).groupBy("publisher_id").map(function(v, i) {
			return {
				publisherId: i
			}
		}).value();

		//logger.debug('job--------', job);
		//logger.debug('results--------', results);

		//logger.debug('publishers=========', publishers);

		publishers.map(publisher => {
			_fillEmptyDates(job, results, publisher.publisherId);
		});

		resolve(results);
	});
};


const _fillEmptyDates = (job, results, publisherId) => {
	const {
		ymd,
		start,
		end,
		//publisherId,
		adProviderId
	} = job;

	for (let i=start; i<=end; i++) {
		const dd = moment(ymd).add(i, 'days').format('YYYYMMDD');

		const found = results.find(v => {
			return v.ymd === dd && v.publisher_id.toString() === publisherId && v.adProvider_id.toString() === adProviderId.toString();
		});

		if (! found) {
			//logger.debug('누락일자 처리--------', dd, publisherId, adProviderId);

			let arr = {
				ymd: dd,
				publisher_id: ObjectId(publisherId),
				adProvider_id: adProviderId,
				imp: 0,
				clk: 0,
				netRevenueKRW: 0,
				netRevenueUSD: 0,
				revenueKRW: 0,
				revenueUSD: 0
			};

			results.push(arr);
		}
	}
};


module.exports.fetchDataStreamPlaceKey = async (job) => {
	logger.debug('[ad-provider-stats-summary-rk.service] fetchDataStreamPlaceKey() 호출됨');

	// // Place Key를 연동하는 Publisher 대상 추출
	// const targetPublishers = await AdProviderRkStat.aggregate()
	// 	.match({
	// 		ymd: { $gte: job.startDate, $lte: job.endDate },
	// 		adProvider_id: ObjectId(job.adProviderId)
	// 	})
	// 	.group({
	// 		_id: {
	// 			publisher_id: '$publisher_id',
	// 			adProvider_id: '$adProvider_id',
	// 		},
	// 	})
	// 	.lookup({
	// 		from: 'AdProviderInfos',
	// 		let: {
	// 			"publisher_id": "$_id.publisher_id",
	// 			"adProvider_id": "$_id.adProvider_id"
	// 		},
	// 		pipeline: [{
	// 			$match: {
	// 				$expr: {
	// 					$and : [
	// 						{ $eq: ["$publisher_id", "$$publisher_id"] },
	// 						{ $eq: ["$adProvider_id", "$$adProvider_id"] }
	// 					]
	// 				}
	// 			}
	// 		}],
	// 		as : 'infos'
	// 	})
	// 	.match({
	// 		'infos.allowPlaceKeyDataPulling': 1   //PlaceKey 연동 대상 여부
	// 	})
	// 	.project({
	// 		publisher_id: '$_id.publisher_id',
	// 		adProvider_id: '$_id.adProvider_id',
	// 		_id: 0
	// 	});

	// const publisher_ids = _.map(targetPublishers, 'publisher_id');
	// //console.log('publisher_ids-------------------', publisher_ids);


	// adProvider와 리포트 연동 중인 GFP 매체 id 조회 (NAM 매체 필터)
	const gfpPublisherIds = await _getGfpPublisherIds(job.adProviderId);
	const publisher_ids = _.map(gfpPublisherIds.filter(({ allowPlaceKeyDataPulling }) => allowPlaceKeyDataPulling === 1), 'publisher_id');

	logger.debug(`[ad-provider-stats-summary-rk.service] allowPlaceKeyDataPulling=1 인 publisher_ids= ${publisher_ids}`);

	return await AdProviderRkStat.aggregate()
		.match({
			ymd: {$gte: job.startDate, $lte: job.endDate},
			adProvider_id: ObjectId(job.adProviderId),
			publisher_id: { $in : publisher_ids },
		})
		.lookup({
			from: 'SummaryTargetCountries',
			localField: 'country',
			foreignField: 'countryCd',
			as : 'countryMap'
		})
		.project({
			ymd: 1,
			publisher_id: 1,
			adProvider_id: 1,
			adProviderPlaceKey: 1,
			os: 1,
			countries: '$countryMap.countryCd',
			imp: 1,
			clk: 1,
			gfpNetRevenueKRW: 1,
			gfpNetRevenueUSD: 1,
			netRevenueKRW: 1,
			netRevenueUSD: 1,
			adProviderPlace_id: 1,
			adUnitId: 1,
		})
		.group({
			_id: {
				ymd: '$ymd',
				publisher_id: '$publisher_id',
				adProvider_id: '$adProvider_id',
				adProviderPlaceKey: '$adProviderPlaceKey',
				os: '$os',
				country: {
					$cond: [
						{ $arrayElemAt: [ '$countries', 0 ] },
						{ $arrayElemAt: [ '$countries', 0 ] },
						'-'
					]
				},
			},
			imp: {$sum: '$imp'},
			clk: {$sum: '$clk'},
			netRevenueKRW: { $sum: '$gfpNetRevenueKRW' },
			netRevenueUSD: { $sum: '$gfpNetRevenueUSD' },
			revenueKRW: { $sum: '$netRevenueKRW' },
			revenueUSD: { $sum: '$netRevenueUSD' },
			adProviderPlace_ids: {$addToSet: "$adProviderPlace_id"},
			adUnitIds: {$addToSet: "$adUnitId"}
		})
		.project({
			ymd: '$_id.ymd',
			publisher_id: '$_id.publisher_id',
			adProvider_id: '$_id.adProvider_id',
			adProviderPlaceKey: '$_id.adProviderPlaceKey',
			os: '$_id.os',
			country: '$_id.country',
			adProviderPlace_ids: 1,
			imp: 1,
			clk: 1,
			netRevenueKRW: 1,
			netRevenueUSD: 1,
			revenueKRW: 1,
			revenueUSD: 1,
			adUnitIds: 1,
			_id: 0
		})
		//.sort({ publisher_id: 1, ymd: 1, adProvider_id: 1 })
		.read("secondaryPreferred")
		.readConcern('local')
		.allowDiskUse(true)
		.cursor()  // { batchSize: 2000000 }
		.exec()
		//.stream()
		;
};


/**
 * _getGfpPublisherIds : placeKey 리포트 집계를 허용하는 ap, pub 정보 가져오기
 *
 * @param {String} adProviderId
 * @return {Array} gfpPublisherIds
 */
const _getGfpPublisherIds = async adProviderId => {
	logger.debug('[ad-provider-stats-summary-rk.service] _getGfpPublisherIds() 호출됨');

	const gfpPublisherIds = await AdProviderInfo.aggregate()
		.match({
			adProvider_id: ObjectId(adProviderId),
			reportStat: { $ne: 'NOTYET' },
			reportApiStatus: 'ON',
		})
		.lookup({
			from: 'Publishers',
			foreignField: '_id',
			localField: 'publisher_id',
			as: 'publisher'
		})
		.unwind('publisher')
		.match({
			'publisher.status': 'ON',
			'publisher.cmsType': CMS_TYPE.GFP.code,
		})
		.project({
			_id: 0,
			publisher_id: 1,
			allowPlaceKeyDataPulling: 1,
		})
		.exec();


	logger.debug(`[ad-provider-stats-summary-rk.service] adProviderId= ${adProviderId}, gfpPublisherIds= ${JSON.stringify(gfpPublisherIds, null, 2)}`);

	return gfpPublisherIds;
};


// const _orderArrayItems = (docs) => {  //summary collection을 grouping 할 때 배열값 순서가 달라 placekey별로 분리되는 이슈 해결을 위함. https://oss.navercorp.com/da-ssp/bts/issues/552
// 	return docs.map(doc => {
// 		doc.adProviderPlace_ids = _.orderBy(doc.adProviderPlace_ids);
// 		doc.adUnitIds = _.orderBy(doc.adUnitIds);
// 		return doc;
// 	});
// };


/**
 * Rk 기반의 AdProvider Stats Data 조회 - Place Key별
 *
 */
/*module.exports.fetchDataPlaceKey = (job, countries) => {
	logger.debug('[ad-provider-stats-summary-rk.service] fetchDataPlaceKey() 호출됨');

	return new Promise(async (resolve, reject) => {
		let results = [];

		// Place Key를 연동하는 Ad Provider, Publisher 대상 추출
		// (참고: 초기에는 adprovider, publisher로 심플하게 group by 하였으나, mongo특성상 최소단위로 쪼개서 여러번 쿼리가 성능 잇점이 있어 로직 변경)
		const targets = await AdProviderRkStat.aggregate()
			.match({
				ymd: { $gte: job.startDate, $lte: job.endDate },
				adProvider_id: ObjectId(job.adProviderId)
			})
			.group({
				_id: {
					publisher_id: '$publisher_id',
					adProvider_id: '$adProvider_id',
				},
			})
			.lookup({
				from: 'AdProviderInfos',
				let: {
					"publisher_id": "$_id.publisher_id",
					"adProvider_id": "$_id.adProvider_id"
				},
				pipeline: [{
					$match: {
						$expr: {
							$and : [
								{ $eq: ["$publisher_id", "$$publisher_id"] },
								{ $eq: ["$adProvider_id", "$$adProvider_id"] }
							]
						}
					}
				}],
				as : 'infos'
			})
			.match({
				'infos.allowPlaceKeyDataPulling': 1   //PlaceKey 연동 대상 여부
			})
			.project({
				publisher_id: '$_id.publisher_id',
				adProvider_id: '$_id.adProvider_id',
				_id: 0
			})
			.read("secondaryPreferred")
			.readConcern('local')
			.allowDiskUse(true)
			.exec();
//console.log('targets-------------', targets);

		// Data Fetch
		if (targets.length === 0) {
			resolve(results);

		} else {
			let promise = targets.map(async ({publisher_id, adProvider_id}) => {
				let docs = await AdProviderRkStat.aggregate()
					.match({
						ymd: {$gte: job.startDate, $lte: job.endDate},
						adProvider_id: adProvider_id,
						publisher_id: publisher_id
					})
					.lookup({
						from: 'SummaryTargetCountries',
						localField: 'country',
						foreignField: 'countryCd',
						as : 'countryMap'
					})
					.project({
						ymd: 1,
						publisher_id: 1,
						adProvider_id: 1,
						adProviderPlaceKey: 1,
						os: 1,
						countries: '$countryMap.countryCd',
						imp: 1,
						clk: 1,
						netRevenueKRW: 1,
						netRevenueUSD: 1,
						adProviderPlace_ids: 1,
						adUnitId: 1,
					})
					.group({
						_id: {
							ymd: '$ymd',
							publisher_id: '$publisher_id',
							adProvider_id: '$adProvider_id',
							adProviderPlaceKey: '$adProviderPlaceKey',
							os: '$os',
							country: {
								$cond: [
									{ $arrayElemAt: [ '$countries', 0 ] },
									{ $arrayElemAt: [ '$countries', 0 ] },
									'-'
								]
							},
						},
						imp: {$sum: '$imp'},
						clk: {$sum: '$clk'},
						netRevenueKRW: {$sum: '$netRevenueKRW'},
						netRevenueUSD: {$sum: '$netRevenueUSD'},
						adProviderPlace_ids: {$addToSet: "$adProviderPlace_id"},
						adUnitIds: {$addToSet: "$adUnitId"}
					})
					.project({
						ymd: '$_id.ymd',
						publisher_id: '$_id.publisher_id',
						adProvider_id: '$_id.adProvider_id',
						adProviderPlaceKey: '$_id.adProviderPlaceKey',
						os: '$_id.os',
						country: '$_id.country',
						adProviderPlace_ids: 1,
						imp: 1,
						clk: 1,
						netRevenueKRW: 1,
						//netRevenueUSD: { $divide: [{$trunc: {$multiply: ['$netRevenueUSD', 100]} }, 100 ] },
						netRevenueUSD: 1,
						adUnitIds: 1,
						_id: 0
					})
					//.sort({ publisher_id: 1, ymd: 1, adProvider_id: 1 })
					.read("secondaryPreferred")
					.readConcern('local')
					.allowDiskUse(true)
					.exec();

				docs = _orderArrayItems(docs);

				results.push(...docs);
			});
			await Promise.all(promise);

			resolve(results);
		}
	});
};*/

//@deprecated 건수가 많아 성능 이슈로 publisher/adprovider별로 loop처리로 변경
/*
module.exports.fetchDataPlaceKey_backup = (job) => {
	logger.debug('[ad-provider-stats-summary-rk.service] fetchDataPlaceKey() 호출됨');

	return new Promise(async (resolve, reject) => {
		const results = await AdProviderRkStat.aggregate()
			.match({
				ymd: { $gte: job.startDate, $lte: job.endDate },
				adProvider_id: ObjectId(job.adProviderId)
			})
			.group({
				_id: {
					ymd: '$ymd',
					publisher_id: '$publisher_id',
					adProvider_id: '$adProvider_id',
					adProviderPlaceKey: '$adProviderPlaceKey',
					os: '$os',
					country: '$country'
				} ,
				imp: { $sum: '$imp' },
				clk: { $sum: '$clk' },
				netRevenueKRW: { $sum: '$netRevenueKRW' } ,
				netRevenueUSD: { $sum: '$netRevenueUSD' } ,
				adProviderPlace_ids: { $addToSet: "$adProviderPlace_id" },
				adUnitIds: { $addToSet: "$adUnitId" }
			})
			.lookup({
				from: 'Publishers',
				localField: '_id.publisher_id',
				foreignField: '_id',
				as : 'publisher'
			})
			.match({
				'publisher.placeKeyViewable': 1
			})
			.project({
				ymd: '$_id.ymd',
				publisher_id: '$_id.publisher_id',
				adProvider_id: '$_id.adProvider_id',
				adProviderPlaceKey: '$_id.adProviderPlaceKey',
				os: '$_id.os',
				country: '$_id.country',
				adProviderPlace_ids: 1,
				imp: 1,
				clk: 1,
				netRevenueKRW: 1,
				//netRevenueUSD: { $divide: [{$trunc: {$multiply: ['$netRevenueUSD', 100]} }, 100 ] },
				netRevenueUSD: 1,
				adUnitIds: 1,
				_id: 0
			})
			//.sort({ publisher_id: 1, ymd: 1, adProvider_id: 1 })
			.read("secondaryPreferred")
			.readConcern('local')
			.allowDiskUse(true)
			.exec();

		resolve(results);
	});
};*/
