'use strict';

import fs from 'fs';
import _ from 'lodash';
import util from 'util';
import zlib from 'zlib';
import path from 'path';
import moment from 'moment';
import csv from 'csv-parser';
import child_process from 'child_process';

import config from '../../../config/config';
import { BusinessError } from '../../../common/error';

import * as file from '../../../utils/file.util';
import extendedLogger from '../../../utils/logger-ext.util';

import * as c3HdfsCli from '../../../c3/hdfs-cli';
import * as c3HdfsApi from '../../../c3/hdfs-api';

import { DataCreatorAdvisorReport } from '../../../models/data/data-creator-advisor-reports.schema';


// 확장 로거
const log = extendedLogger('ca');

// Creator Advisor Report 처리 상태
const STATE = {
	START: 'START',
	IN_PROGRESS: 'IN_PROGRESS',
	COMPLETE: 'COMPLETE',
	FAILURE: 'FAILURE',
}

// CA_HDFS_ROOT_DIR = /user/gfp-data/creator-advisor-report
const CA_HDFS_ROOT_DIR = config.report.creator_advisor.hdfs.root_dir;

// CSV ROW 포맷
// - adUnitId, adProviderName, adProviderPlaceKey, impressions, clicks, bid, naverId, gdid
const rowFormat = '%s,%s,%s,%s,%s,%s,%s,%s\n';

// Apollo 매체 - 'Apollo 수익쉐어' keyGroupId
const keyGroupId = (process.env.NODE_ENV === 'production') ? '5bff49990e15fb002fd7989e' : '5bfe4649762f05001995b8fe';

// reportPath = /report/creatoradvisor
// owfsBasePath : download/report/creatoradvisor/yyyy/mm/keyGroupId
// localBasePath : local_download/report/creatoradvisor/yyyy/mm/keyGroupId
const reportPath = `${config.report.path}${config.report.creator_advisor.path}`;
const owfsBasePath = path.join(config.owfs_root, reportPath, '{{YYYY}}/{{MM}}', keyGroupId);
const localBasePath = path.join(config.local_root, reportPath, '{{YYYY}}/{{MM}}', keyGroupId);


/**
 * Creator Advisor 리포트 처리 중인게 있는지 체크
 */
module.exports.checkInProgress = async (txId, date) => {
	log.debug(`[creator-advisor-report.service :: checkInProgress][txId=${txId}] date= ${date}`);

	return _getInProgressCreatorAdvisorReport(txId, date);
};


/**
 * _getInProgressCreatorAdvisorReport : CreatorAdvisorReport 조회
 */
const _getInProgressCreatorAdvisorReport = async (txId, date) => {
	log.debug(`[creator-advisor-report.service :: _getInProgressCreatorAdvisorReport][txId=${txId}] 처리 중인 CreatorAdvisorReport 조회 ( date= ${date} )`);

	return await DataCreatorAdvisorReport.findOne({
		date, state: { $nin: [STATE.COMPLETE, STATE.FAILURE] }
	}).exec();
};


/**
 * Creator Advisor 리포트 다운로드 (HDFS -> OWFS)
 */
module.exports.download = async (txId, date, isForce) => {
	log.debug(`[creator-advisor-report.service :: download][txId=${txId}] date= ${date}, isForce=${isForce}`);

	let reportId;

	try {
		// HDFS & LOCAL & OWFS 리포트 파일 경로 정보
		const {
			hdfsReportFile, hdfsSuccessFile,
			localC3Dir, localC3File, localReportFile,
			owfsReportFile, owfsSuccessFile,
		} = _getFilePathInfo(txId, date);

		// HDFS 에 Success 파일이 있는지 체크 (즉, 리포트가 생성 되어있는지)
		// 없는 경우, Error
		await _isExistHdfsSuccess(hdfsSuccessFile);

		// OWFS 에 success 파일이 이미 있는지 체크 (즉, 리포트가 이미 있는지)
		const isExistOwfsSuccess = _isExistOwfsSuccess(owfsSuccessFile);

		// OWFS 에 리포트가 아직 없거나 강제 생성인 경우, 리포트 파일 다운로드
		if (!isExistOwfsSuccess || isForce) {
			// C3 원본 파일 로컬 다운로드 ( C3 HDFS -> LOCAL )
			await _download(txId, hdfsReportFile, localC3Dir, localC3File);

			// CreatorAdvisorReport 생성 (START)
			reportId = await _createCreatorAdvisorReport(txId, date, owfsReportFile);

			// 기존 파일 백업
			_backUpExistingFile(txId, owfsReportFile);

			// C3 원본 파일 총 라인수 확인
			const localC3FileLineCount = await _getFileLineCount(localC3File);

			// CreatorAdvisorReport 처리 중으로 변경 (IN_PROGRESS)
			await _updateCreatorAdvisorReport(txId, reportId, STATE.IN_PROGRESS, localC3FileLineCount);

			// LOCAL 리포트 파일 생성 ( BOM 추가, 헤더 제거 )
			await _createLocalReport(txId, localC3FileLineCount, localC3File, localReportFile);

			// OWFS 리포트 파일 복사 ( LOCAL -> OWFS )
			await _copyToOwfs(txId, localReportFile, owfsReportFile)

			// OWFS Success File 생성
			_writeSuccessFile(txId, owfsSuccessFile);

			// C3 원본 디렉토리 로컬 삭제
			_deleteLocalC3Dir(txId, localC3Dir);

			// CreatorAdvisorReport 처리 성공 (COMPLETE)
			await _updateCreatorAdvisorReport(txId, reportId, STATE.COMPLETE);
		} else {
			log.debug(`[creator-advisor-report.service :: download][txId=${txId}] 이미 리포트가 존재함 ( date= ${date} )`);
		}
	} catch (e) {
		log.error(`[creator-advisor-report.service :: download][txId=${txId}] Error :: ( date= ${date} ) \n ${e.stack}\n\n`, e);

		// CreatorAdvisorReport 처리 실패 (FAILURE)
		await _updateCreatorAdvisorReport(txId, reportId, STATE.FAILURE);

		throw e;
	}
};


/**
 * _getFilePathInfo : HDFS & LOCAL & OWFS 리포트 파일 경로 정보
 * 		- OWFS 에 바로 다운로드하면 오래 걸리므로 로컬에 먼저 다운로드한다.
 */
const _getFilePathInfo = (txId, date) => {
	const dt = moment(date, 'YYYYMMDD');

	// fileName = 5bff49990e15fb002fd7989e_20230531
	const fileName = `${keyGroupId}_${dt.format('YYYYMMDD')}`;

	/* local 파일 정보 */
	// localReportDir = local_download/report/creatoradvisor/2023/05/5bff49990e15fb002fd7989e
	const localReportDir = file.mkdirIfNotExist(localBasePath.replace(/{{YYYY}}/g, dt.format('YYYY')).replace(/{{MM}}/g, dt.format('MM')));
	// localC3Dir = local_download/report/creatoradvisor/2023/05/5bff49990e15fb002fd7989e/c3
	const localC3Dir = file.mkdirIfNotExist(path.join(localReportDir, 'c3'));

	// localC3File = local_download/report/creatoradvisor/2023/05/5bff49990e15fb002fd7989e/c3/ca_20230531.csv
	// localReportFile = local_download/report/creatoradvisor/2023/05/5bff49990e15fb002fd7989e/5bff49990e15fb002fd7989e_20230531.csv
	const localC3File = path.join(localC3Dir, `ca_${dt.format('YYYYMMDD')}.csv`);
	const localReportFile = path.join(localReportDir, `${fileName}.csv`);

	/* owfs 파일 정보 */
	// owfsReportDir = download/report/creatoradvisor/2023/05/5bff49990e15fb002fd7989e
	const owfsReportDir = file.mkdirIfNotExist(owfsBasePath.replace(/{{YYYY}}/g, dt.format('YYYY')).replace(/{{MM}}/g, dt.format('MM')));

	// owfsSuccessFile = download/report/creatoradvisor/2023/05/5bff49990e15fb002fd7989e/5bff49990e15fb002fd7989e_20230531.success
	// owfsReportFile = download/report/creatoradvisor/2023/05/5bff49990e15fb002fd7989e/5bff49990e15fb002fd7989e_20230531.csv
	const owfsSuccessFile = path.join(owfsReportDir, `${fileName}.success`);
	const owfsReportFile = path.join(owfsReportDir, `${fileName}.csv`);

	// hdfsSuccessFile = /user/gfp-data/creator-advisor-report/2023/05/23/_SUCCESS
	// hdfsReportFile = /user/gfp-data/creator-advisor-report/2023/05/23/ca_20230523.csv.gz
	const hdfsSuccessFile = path.join(CA_HDFS_ROOT_DIR, dt.format('YYYY/MM/DD'), '_SUCCESS');
	const hdfsReportFile = path.join(CA_HDFS_ROOT_DIR, dt.format('YYYY/MM/DD'), `ca_${dt.format('YYYYMMDD')}.csv.gz`);

	log.debug(`[creator-advisor-report.service :: _getFilePathInfo][txId=${txId}] HDFS & LOCAL & OWFS 리포트 파일 경로 정보 ( date=${date} ) \n- hdfsReportFile= ${hdfsReportFile}\n- localReportFile= ${localReportFile}\n- owfsReportFile= ${owfsReportFile}`);

	return {
		hdfsReportFile, hdfsSuccessFile,
		localC3Dir, localC3File, localReportFile,
		owfsReportFile, owfsSuccessFile,
	};
};


/**
 * _isExistHdfsSuccess : hdfs 에 success 파일이 존재하는지 확인
 *		- success 파일 경로 : /user/gfp-data/creator-advisor-report/yyyy/mm/dd/_SUCCESS
 */
const _isExistHdfsSuccess = async (hdfsSuccessFile) => {
	log.debug(`[creator-advisor-report.service :: _isExistHdfsSuccess] hdfsSuccessFile= ${hdfsSuccessFile}`);

	const isExist = await c3HdfsApi.exists(hdfsSuccessFile);

	if (!isExist) {
		throw new BusinessError({ message: `[creator-advisor-report.service :: _isExistHdfsSuccess] HDFS _SUCCESS 파일이 존재 하지 않음 ( hdfsSuccessFile= ${hdfsSuccessFile} )` });
	}
};


/**
 * _isExistOwfsSuccess : owfs 에 success 파일이 존재하는지 확인
 * 		- success 파일 경로 : download/report/creatoradvisor/yyyy/mm/keyGroupId/keyGroupId_yyyymmdd.success
 */
const _isExistOwfsSuccess = owfsSuccessFile => {
	log.debug(`[creator-advisor-report.service :: _isExistOwfsSuccess] owfsSuccessFile= ${owfsSuccessFile}`);

	return fs.existsSync(owfsSuccessFile);
};


/**
 * _download : C3 원본 파일 로컬 다운로드 ( HDFS -> LOCAL )
 */
const _download = async (txId, hdfsReportFile, localC3Dir, localC3File) => {
	log.debug(`[creator-advisor-report.service :: _download][txId=${txId}] hdfsReportFile= ${hdfsReportFile}, localC3File= ${localC3File}`);

	if (!await c3HdfsApi.exists(hdfsReportFile)) {
		throw new BusinessError({ message: `[creator-advisor-report.service :: _download][txId=${txId}] HDFS 에 원본 파일이 존재 하지 않음 (hdfsReportFile= ${hdfsReportFile})` });
	}

	// hdfsReportFile = /user/gfp-data/creator-advisor-report/2023/05/23/ca_20230523.csv.gz
	// localC3Dir = local_download/report/creatoradvisor/2023/05/5bff49990e15fb002fd7989e/c3
	// localC3File = local_download/report/creatoradvisor/2023/05/5bff49990e15fb002fd7989e/c3/ca_20230531.csv

	await c3HdfsCli.download(hdfsReportFile, localC3Dir, true, false);

	// gz 압축 풀기
	await new Promise((resolve, reject) => {
		const gunZipStream = zlib.createGunzip();
		const readStream = fs.createReadStream(`${localC3File}.gz`);
		const writeStream = fs.createWriteStream(localC3File);

		readStream.on('end', () => {
			log.debug(`[creator-advisor-report.service :: _download][txId=${txId}] readStream end`);
		}).on('error', err => {
			log.debug(`[creator-advisor-report.service :: _download][txId=${txId}] readStream error`);

			readStream.close();

			reject(err);
		});

		writeStream.on('finish', () => {
			log.debug(`[creator-advisor-report.service :: _download][txId=${txId}] writeStream finish`);

			resolve();
		}).on('error', err => {
			log.debug(`[creator-advisor-report.service :: _download][txId=${txId}] writeStream error`);

			readStream.close();

			reject(err);
		});

		readStream.pipe(gunZipStream).pipe(writeStream);
	});
};


/**
 * _createCreatorAdvisorReport : CreatorAdvisorReport 생성
 * 		creator advisor 리포트 이력 생성
 */
const _createCreatorAdvisorReport = async (txId, date, reportFile) => {
	log.debug(`[creator-advisor-report.service :: _createCreatorAdvisorReport][txId=${txId}] CreatorAdvisorReport 생성 ( date= ${date}, filePath= ${reportFile} )`);

	const today = moment();

	const res = await DataCreatorAdvisorReport.findOneAndUpdate(
		{ date, filePath: reportFile },
		{
			$set: {
				state: 'START',
				begunAt: today,
				modifiedAt: today
			},

			// insert 시
			$setOnInsert: {
				createdAt: today
			},
		},
		{ new: true, runValidators: true, upsert: true }
	).exec();

	return res._id;
};


/**
 * _updateCreatorAdvisorReport : CreatorAdvisorReport 업데이트
 * 		- IN_PROGRESS :: state, dataCount, modifiedAt
 * 		- COMPLETE / FAILURE :: state, endedAt, modifiedAt
 */
const _updateCreatorAdvisorReport = async (txId, _id, state, dataCount) => {
	log.debug(`[creator-advisor-report.service :: _updateCreatorAdvisorReport][txId=${txId}] CreatorAdvisorReport 변경 ( _id= ${_id}, state= ${state}, dataCount= ${dataCount} )`);

	const today = moment();

	const set = { state, modifiedAt: today };

	if (state === STATE.COMPLETE || state === STATE.FAILURE) {
		set.endedAt = today;
	} else if (state === STATE.IN_PROGRESS) {
		set.dataCount = dataCount;
	}

	await DataCreatorAdvisorReport.findOneAndUpdate(
		{ _id },
		{ $set: set },
		{ new: true, runValidators: true }
	).exec();
};


/**
 * _backUpExistingFile : 기존 리포트 파일 백업
 * 		- 기존 경로 : download/report/creatoradvisor/yyyy/mm/keyGroupId/keyGroupId_yyyymmdd.csv
 * 		- 백업 경로 : download/report/creatoradvisor/yyyy/mm/keyGroupId/backup/keyGroupId_yyyymmdd.csv.yyyymmdd_hhmmss
 */
const _backUpExistingFile = (txId, owfsReportFile) => {
	// 기존 파일이 존재 하는 경우, 백업 디렉토리로 복사
	if (fs.existsSync(owfsReportFile)) {
		log.debug(`[creator-advisor-report.service :: _backUpExistingFile][txId=${txId}] 기존 리포트 파일 백업 시작 ( owfsReportFile=${owfsReportFile} )`);

		// owfsReportFile = download/report/creatoradvisor/yyyy/mm/keyGroupId/keyGroupId_yyyymmdd.csv
		// { root: '', dir: 'download/report/creatoradvisor/yyyy/mm/keyGroupId',
		// 		base: 'keyGroupId_yyyymmdd.csv', ext: '.csv', name: 'keyGroupId_yyyymmdd' }
		const existingFileInfo = path.parse(owfsReportFile);

		// backUpPath = download/report/creatoradvisor/yyyy/mm/keyGroupId/backup
		let backUpPath = file.mkdirIfNotExist(path.join(existingFileInfo.dir, 'backup'));

		// 백업 경로로 복사
		const fileStats = fs.statSync(owfsReportFile);
		// backUpPath = download/report/creatoradvisor/yyyy/mm/keyGroupId/backup/keyGroupId_yyyymmdd.csv.yyyymmdd_hhmmss
		backUpPath = path.join(backUpPath, existingFileInfo.base + '.' + moment(fileStats.mtime).format('YYYYMMDD_HHmmss'));
		fs.copyFileSync(owfsReportFile, backUpPath);

		log.debug(`[creator-advisor-report.service :: _backUpExistingFile][txId=${txId}] 기존 리포트 파일 백업 완료 ( owfsReportFile=${owfsReportFile}, backUpPath=${backUpPath} )`);
	}
};


/**
 * [Promise] _getFileLineCount : 파일 총 라인수 확인
 */
const _getFileLineCount = (filePath) => {
	log.debug(`[creator-advisor-report.service :: _getFileLineCount] filePath=${filePath}`);

	return new Promise((resolve, reject) => {
		const NEW_LINE = '\n'.charCodeAt(0);

		let index = 0;
		let last_chunk = '';
		let totalLineCount = -1; // 헤더 정보 제외
		const fileStream = fs.createReadStream(filePath);

		fileStream.on('data', chunk => {
			try {
				last_chunk = chunk[chunk.length - 1];

				for (index = 0; index < chunk.length; ++index) {
					if (chunk[index] === NEW_LINE) totalLineCount++;
				}
			} catch (err) {
				reject({ message: `[creator-advisor-report.service :: _getFileLineCount] fileStream data 에러`, err });
			}
		})
		.on('end', () => {
			// 파일 끝에 개행문자가 없는 경우, 1건 추가
			if(last_chunk !== NEW_LINE) {
				totalLineCount++;
			}

			resolve(totalLineCount);
		})
		.on('error', err => {
			reject({ message: `[creator-advisor-report.service :: _getFileLineCount] fileStream 에러`, err });

			if (fileStream) fileStream.close();
		});
	});
};


/**
 * _createLocalReport : LOCAL 리포트 파일 생성
 */
const _createLocalReport = async (txId, localC3FileLineCount, localC3File, localReportFile) => {
	log.debug(`[creator-advisor-report.service :: _createLocalReport][txId=${txId}] LOCAL 리포트 파일 생성 시작`);
	log.debug(`[creator-advisor-report.service :: _createLocalReport][txId=${txId}] localC3FileLineCount= ${localC3FileLineCount}, localC3File= ${localC3File}, localReportFile= ${localReportFile}`);

	// LOCAL 리포트 파일이 이미 있으면 삭제
	if (fs.existsSync(localReportFile)) {
		fs.unlinkSync(localReportFile);
	}

	try {
		// UTF-8 BOM (Byte Order Mark) 추가
		_appendByteOrderMark(localReportFile);

		// LOCAL 리포트 파일 생성
		const dataCount = await _create(txId, localC3File, localReportFile);

		// C3 원본 파일 건수와 LOCAL 리포트 파일 건수가 일치 하지 않는 경우, Error
		if (localC3FileLineCount !== dataCount) {
			throw new BusinessError({ message: `[creator-advisor-report.service :: _createLocalReport][txId=${txId}] C3 원본 파일 건수(${localC3FileLineCount}) 와 LOCAL 리포트 파일 건수(${dataCount})가 일치 하지 않음` });
		}
	} catch (e) {
		log.error(`[creator-advisor-report.service :: _createLocalReport][txId=${txId}] Error :: \n ${e.stack}\n\n`, e);

		throw e;
	}

	log.debug(`[creator-advisor-report.service :: _createLocalReport][txId=${txId}] LOCAL 리포트 파일 생성 종료`);
};


/**
 * _appendByteOrderMark : UTF-8 BOM (Byte Order Mark) 추가
 * 		- UTF-8 파일에 한글이 있으면 파일 열 때 깨지기 때문에, BOM 문자를 맨 앞에 추가함
 */
const _appendByteOrderMark = localReportFile => {
	const fda = fs.openSync(localReportFile, 'a');

	fs.appendFileSync(fda, '\ufeff', 'utf8');

	if (!_.isNil(fda)) fs.closeSync(fda);
};


/**
 * [Promise] _create
 */
const _create = (txId, localC3File, localReportFile) => {
	return new Promise((resolve, reject) => {
		let dataCount = 0;

		// LOCAL 리포트 파일 쓰기 스트림
		const writeStream = fs.createWriteStream(localReportFile, { flags : 'a', autoClose: false, encoding: 'utf8' })
			.on('close', () => {
				log.debug(`[creator-advisor-report.service :: _create][txId=${txId}] writeStream close 완료`);

				resolve(dataCount);
			})
			.on('error', err => {
				log.debug(`[creator-advisor-report.service :: _create][txId=${txId}] writeStream 에러 ( err = ${JSON.stringify(err, null, 2)})`);

				reject({ message: `[creator-advisor-report.service :: _create][txId=${txId}] writeStream 에러`, err });

				closeStreams();
			});

		// C3 원본 파일 읽기 스트림
		const readStream = fs.createReadStream(localC3File)
			.on('end', () => {
				log.debug(`[creator-advisor-report.service :: _create][txId=${txId}] readStream end 완료`);
			})
			.on('error', err => {
				log.debug(`[creator-advisor-report.service :: _create][txId=${txId}] readStream 에러 ( err = ${JSON.stringify(err, null, 2)})`);

				reject({ message: `[creator-advisor-report.service :: _create][txId=${txId}] readStream 에러`, err });

				closeStreams();
			});

		// csv 읽기
		const csvStream = csv();
		const onData = data => {
			try {
				dataCount++;

				// object -> string
				// row = "adUnitId","adProviderName","adProviderPlaceKey",impressions,clicks,bid,naverId,gdid
				const row = _makeRow(data);

				writeStream.write(row);
			} catch(err) {
				reject({ message: `[creator-advisor-report.service :: _create][txId=${txId}] csvStream data 에러`, err });

				closeStreams();
			}
		};

		csvStream.on('data', onData)
			.on('end', () => {
				try {
					log.debug(`[creator-advisor-report.service :: _create][txId=${txId}] csvStream end 완료`);

					csvStream.removeListener('data', onData);

					writeStream.close();
				} catch (err) {
					reject({ message: `[creator-advisor-report.service :: _create][txId=${txId}] csvStream end 에러`, err });

					closeStreams();
				}
			})
			.on('error', err => {
				reject({ message: `[creator-advisor-report.service :: _create][txId=${txId}] csvStream 에러`, err });

				closeStreams();
			});

		readStream.pipe(csvStream);

		// 모든 stream 종료 처리
		const closeStreams = () => {
			log.debug(`[creator-advisor-report.service :: closeStreams]`);

			if (csvStream) {
				csvStream.removeListener('data', onData);
				csvStream.end();
			}

			if (readStream) readStream.close();
			if (writeStream) writeStream.close();
		};
	});
};


/**
 * _makeRow : Object -> String
 * 	- row format 순서 :: adUnitId, adProviderName, adProviderPlaceKey, impressions, clicks, bid, naverId, gdid
 */
const _makeRow = data => {
	const row = util.format(rowFormat,
		_wrapWordWithDoubleQuotation(data.adUnitId), 		// 1. AdUnitId
		_wrapWordWithDoubleQuotation(data.adProviderName),	// 2. AdProvider Name

		// 3. AdProvider PlaceKey
		_.isEmpty(data.adProviderPlaceKey) ? '' : _wrapWordWithDoubleQuotation(data.adProviderPlaceKey),

		data.impressions, 									// 4. 노출
		data.clicks, 										// 5. 클릭

		_.isEmpty(data.bid) ? '-' : data.bid, 				// 6. bid (rsKeyValue)
		_.isEmpty(data.naverId) ? '-' : data.naverId, 		// 7. naverId (rsKeyValue)
		_.isEmpty(data.gdid) ? '-' : data.gdid 				// 8. gdid (param)
	);

	return row;
};


/**
 * _wrapWordWithDoubleQuotation : 쌍 따옴표 처리
 */
const _wrapWordWithDoubleQuotation = word => {
	return '"' + word.replace(/"/g, '""') + '"';
};


/**
 * _copyToOwfs : LOCAL 파일을 OWFS 로 복사
 */
const _copyToOwfs = async (txId, localReportFile, owfsReportFile) => {
	log.debug(`[creator-advisor-report.service :: _copyToOwfs][txId=${txId}] LOCAL 파일을 OWFS 로 복사 시작`);
	log.debug(`[creator-advisor-report.service :: _copyToOwfs][txId=${txId}] localReportFile= ${localReportFile}, owfsReportFile= ${owfsReportFile}`);

	try {
		// 파일 복사 ( 이미 존재 하는 경우, overwrite )
		fs.copyFileSync(localReportFile, owfsReportFile);

		// 3초 sleep
		await _sleep(3000);

		// 파일 크기 검증 ( LOCAL VS OWFS )
		_validateFileSize(localReportFile, owfsReportFile);
	} catch (err) {
		throw err;
	}

	log.debug(`[creator-advisor-report.service :: _copyToOwfs][txId=${txId}] LOCAL 파일을 OWFS 로 복사 종료`);
};


/**
 * 파일 크기 검증 ( LOCAL vs OWFS )
 */
const _validateFileSize = (localReportFile, owfsReportFile) => {
	// LOCAL 파일 사이즈
	const localFileSize = fs.statSync(localReportFile).size;

	// OWFS 파일 사이즈
	const owfsFileSize = fs.statSync(owfsReportFile).size;

	// 파일 크기가 같지 않은 경우, Error
	if (localFileSize !== owfsFileSize) {
		throw new BusinessError({ message: `LOCAL vs OWFS 파일 사이즈 불일치 ( localReportFile= ${localReportFile}, localFileSize= ${localFileSize}, owfsReportFile= ${owfsReportFile}, owfsFileSize= ${owfsFileSize} )` });
	}
};


/**
 * _writeSuccessFile : success 파일 쓰기
 */
const _writeSuccessFile = (txId, owfsSuccessFile) => {
	log.debug(`[creator-advisor-report.service :: _writeSuccessFile][txId=${txId}] success 파일 쓰기 ( owfsSuccessFile= ${owfsSuccessFile} )`);

	// 파일 생성
	const fda = fs.openSync(owfsSuccessFile, 'w');

	// 파일 닫기
	if (!_.isNil(fda)) fs.closeSync(fda);
};


/**
 * _deleteLocalC3Dir : LOCAL 일괄 삭제 처리
*/
const _deleteLocalC3Dir = (txId, localDirPath) => {
	log.debug(`[creator-advisor-report.service :: _deleteLocalC3Dir][txId=${txId}] localDirPath=${localDirPath} 하위 데이터 일괄 삭제 처리 시작`);

	if (fs.existsSync(localDirPath)) {
		// 디렉토리 & 파일 삭제
		file.deleteDirectoryRecursively(localDirPath, true);
	}

	log.debug(`[creator-advisor-report.service :: _deleteLocalC3Dir][txId=${txId}] localDirPath=${localDirPath} 하위 데이터 일괄 삭제 처리 완료`);
};


const _sleep = (ms) => {
	return new Promise(resolve => {
		setTimeout(resolve, ms)
	})
};


/**
 * Creator Advisor 리포트 LOCAL 삭제
 *     - 매주 2주 전 기준으로 한달치 리포트 LOCAL 삭제
 *     - ex> 12월 8일에 실행 시, 10월 23일 ~ 11월 23일 일괄 삭제 처리됨
 *     - ex> 12월 15일에 실행 시, 10월 30일 ~ 11월 30일 일괄 삭제 처리됨
 *     - ex> 12월 22일에 실행 시, 11월 7일 ~ 12월 7일 일괄 삭제 처리됨
 */
module.exports.deleteLocal = async (txId) => {
	log.debug(`[creator-advisor-report.service :: deleteLocal][txId=${txId}]`);

	// 2주 전 기준으로 30일치 리포트 LOCAL 삭제 (오늘이 12월 15일이면 10월 30일 ~ 11월 30일 일괄 삭제)
	// targetDate 이전 리포트 파일 일괄 삭제 (include)
	const targetEndDate = moment().subtract(15, 'days').startOf('day');
	const targetStartDate = moment(targetEndDate).subtract(1, 'months');

	log.debug(`[creator-advisor-report.service :: deleteLocal][txId=${txId}] ${targetStartDate.format('YYYY-MM-DD')} ~ ${targetEndDate.format('YYYY-MM-DD')} 일괄 삭제 시작`);


	/* 1. targetStartDate 에 해당 하는 년/월 디렉토리 일괄 삭제 */
	// targetStartDate 가 12월인 경우, 작년 디렉토리 삭제. 그 외에는 월 디렉토리 삭제
	// targetStartPath = local_download/report/creatoradvisor/2023
	// targetStartPath = local_download/report/creatoradvisor/2023/10
	let targetStartPath = path.join(config.local_root, reportPath, targetStartDate.format('YYYY'));
	if (targetStartDate.month() + 1 < 12) {
		targetStartPath = path.join(targetStartPath, targetStartDate.format('MM'));
	}

	log.debug(`[creator-advisor-report.service :: deleteLocal][txId=${txId}] targetStartDate 에 해당 하는 년/월 디렉토리 일괄 삭제 시작 ( rm -rf ${targetStartPath} )`);

	child_process.execSync(`rm -rf ${targetStartPath}`);

	log.debug(`[creator-advisor-report.service :: deleteLocal][txId=${txId}] targetStartDate 에 해당 하는 년/월 디렉토리 일괄 삭제 완료 ( rm -rf ${targetStartPath} )`);


	/* 2. targetEndDate 에 해당 하는 월 1일 부터 targetEndDate 까지 일괄 삭제  */
	const dateList = await _getDateList(moment(targetEndDate).startOf('month'), targetEndDate);

	// targetPath = local_download/report/creatoradvisor/2023/10/*/*_{yyyymmdd,yyyymmdd,yyyymmdd,...}.csv
	const targetPath = path.join(config.local_root, reportPath, targetEndDate.format('YYYY'), targetEndDate.format('MM'), '*', `*_{${dateList.join(',')}}.csv`);

	log.debug(`[creator-advisor-report.service :: deleteLocal][txId=${txId}] targetEndDate 에 해당 하는 월 1일 부터 targetEndDate 까지 일괄 삭제 시작 ( rm -f ${targetPath} )`);

	child_process.execSync(`rm -f ${targetPath}`);

	log.debug(`[creator-advisor-report.service :: deleteLocal][txId=${txId}] targetEndDate 에 해당 하는 월 1일 부터 targetEndDate 까지 일괄 삭제 완료 ( rm -f ${targetPath} )`);


	log.debug(`[creator-advisor-report.service :: deleteLocal][txId=${txId}] ${targetStartDate.format('YYYY-MM-DD')} ~ ${targetEndDate.format('YYYY-MM-DD')} 일괄 삭제 완료`);
};


/**
 * _getDateList : 시작일-종료일 간 날짜 리스트 가져오기
 *
 * @param {Date} startDate 20240101
 * @param {Date} endDate 20240115
 * @return {Array} dateList 날짜 리스트 ['20240101', '20240102', ..., '20240115' ]
 */
const _getDateList = async (startDate, endDate) => {
	// 날짜 리스트
	const dateList = [];

	// 날짜 리스트 생성(시작일 ~ 종료일)
	let tempDate = moment(startDate);
	while (tempDate.isSameOrBefore(endDate)) {
		dateList.push(tempDate.format('YYYYMMDD'));
		tempDate.add(1, 'days');
	}

	return dateList;
};


/**
 * Creator Advisor 리포트 HDFS 삭제
 *     - 매주 2주 전 기준으로 한달치 리포트 HDFS 일괄 삭제
 *     - ex> 12월 8일에 실행 시, 10월 23일 ~ 11월 23일 일괄 삭제 처리됨
 *     - ex> 12월 15일에 실행 시, 10월 30일 ~ 11월 30일 일괄 삭제 처리됨
 *     - ex> 12월 22일에 실행 시, 11월 7일 ~ 12월 7일 일괄 삭제 처리됨
 */
module.exports.deleteHdfs = async (txId) => {
	log.debug(`[creator-advisor-report.service :: deleteHdfs][txId=${txId}]`);

	// 2주 전 기준으로 30일치 디렉토리 HDFS 삭제 (오늘이 12월 13일이면 10월 28일 ~ 11월 28일 일괄 삭제)
	// targetDate 이전 리포트 파일 일괄 삭제 (include)
	const targetEndDate = moment().subtract(15, 'days').startOf('day');
	const targetStartDate = moment(targetEndDate).subtract(1, 'months');

	log.debug(`[creator-advisor-report.service :: deleteHdfs][txId=${txId}] ${targetStartDate.format('YYYY-MM-DD')} ~ ${targetEndDate.format('YYYY-MM-DD')} 일괄 삭제 시작`);

	const hdfsDeleteDirPathList = await _getHdfsDeleteDirPathList(targetStartDate, targetEndDate);

	await Promise.all(hdfsDeleteDirPathList.map(async hdfsDeleteDirPath => {
		// hdfsDeleteDirPath = /user/gfp-data/creator-advisor-report/YYYY/MM/DD

		// 디렉토리 경로가 HDFS 에 존재하는지 확인
		if (await c3HdfsApi.exists(hdfsDeleteDirPath)) {
			// 디렉토리 일괄 삭제하기
			const err = await c3HdfsApi.delete(hdfsDeleteDirPath, true);

			// 에러가 발생한 경우
			if (err) {
				throw new BusinessError({ message: `[creator-advisor-report.service :: deleteHdfs] ${targetStartDate.format('YYYY-MM-DD')} ~ ${targetEndDate.format('YYYY-MM-DD')} 일괄 삭제 실패` }, { err, detail: JSON.stringify(err, null, 2) });
			}
		}
	}));

	log.debug(`[creator-advisor-report.service :: deleteHdfs][txId=${txId}] ${targetStartDate.format('YYYY-MM-DD')} ~ ${targetEndDate.format('YYYY-MM-DD')} 일괄 삭제 완료`);
};


/**
 * HDFS 삭제 대상 디렉토리 경로 구하기
 */
const _getHdfsDeleteDirPathList = async (startDate, endDate) => {
	const hdfsDeleteDirPathList = new Array();

	let tempDate = moment(startDate);
	while (tempDate.isSameOrBefore(endDate)) {
		// dirPath = /user/gfp-data/creator-advisor-report/YYYY/MM/DD
		hdfsDeleteDirPathList.push(path.join(CA_HDFS_ROOT_DIR, tempDate.format('YYYY'), tempDate.format('MM'), tempDate.format('DD')));

		const lastDayOfMonth = moment(tempDate).endOf('month').startOf('day');
		if (tempDate.isSame(lastDayOfMonth)) {
			hdfsDeleteDirPathList.push(path.join(CA_HDFS_ROOT_DIR, tempDate.format('YYYY'), tempDate.format('MM')));
		}

		tempDate.add(1, 'days');
	}

	return hdfsDeleteDirPathList;
}
