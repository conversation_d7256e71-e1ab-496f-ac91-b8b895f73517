/********************************************************************************************************************
 * 수익쉐어 리포트 모니터링
 *
 * 1. 리포트가 정상적으로 생성됐는지 확인한다.
 * 2. 리포트 사이즈가 1MB 이하이면 관리자에게 알림을 보낸다.
 * 3. 이상감지 시 메일 전송
 * 	  관리자 이메일 주소는 Environments 컬렉션의 "name" : "revenue-sharing-report-monitor-receiver"의 value에
 * 	  콤마를 구분자로 열걸되어 있다.
 */


'use strict';

import _ from 'lodash';
import fs from 'fs';
import moment from 'moment-timezone';
import path from 'path';

import commCd from '@ssp/ssp-common-code';
import config from '../../../config/config';
import { SummaryRevenueSharingSchedule } from '../../../models/summary-revenue-sharing-schedules.schema';
import { SummaryRevenueSharingReport } from '../../../models/summary-revenue-sharing-reports.schema';
import { Publisher } from "../../../models/publishers.schema";
import { Environments } from "../../../models/environments.schema";
import * as NubesClient from '../../../nubes/clients'
import * as mailer from '../../../utils/mail.util';
import * as logger from '../../../utils/logger.util';
import * as csvService from "./revenue-sharing-report-csv.service";

const cd = commCd.codeEnc('KO');

const REPORT_ABNORMAL_TYPE_NOT_EXIST = 'NOT_EXIST';
const REPORT_ABNORMAL_TYPE_WAIT_REPORT = 'WAIT_REPORT';
const REPORT_ABNORMAL_TYPE_WAIT_AP_REPORT = 'WAIT_AP_REPORT';
const REPORT_ABNORMAL_TYPE_WAIT_GFP_REPORT = 'WAIT_GFP_REPORT';
const REPORT_ABNORMAL_TYPE_NOT_END = 'NOT_END';

/**
 * 매일 04:45분에 수익쉐어 리포트에 대한 모니터링 수행
 * @returns {Promise.<boolean>}
 */
module.exports.monitor = async () => {
	let isOk = true;

	// 리포트 생성여부 확인
	const results = [];
	results.push(await _checkReport());

	// 생성된 리포트 파일 사이즈 확인
	const env = await Environments.findOne({'name': 'revenue-sharing-report-monitor-file-size-check-yn'}).select('value');
	if (_.isNil(env) || _.isNil(env.value) || env.value.toLowerCase() === 'y') {
		results.push(await _checkFileSize());
	}

	for (const result of results) {
		isOk = isOk & result;
	}

	return isOk;
};

/**
 * 리포트가 정상적으로 생성됐는지 확인한다.
 * @returns {Promise.<void>}
 * @private
 */
const _checkReport = async () => {
	try {
		const abnormals = [];

		const schInfos = await csvService.getScheduleInfo('MONITOR-CHECK REPORT', null, null, null, true);
		for (const schInfo of schInfos) {
			const report = await csvService.getReportBySchedule(schInfo.schedule._id, schInfo.endYmd);
			if (_.isNil(report)) {
				// 생성되어야 하는 리포트가 없음
				abnormals.push({
					'schedule': schInfo.schedule,
					'ymd': schInfo.endYmd,
					'type': REPORT_ABNORMAL_TYPE_NOT_EXIST
				});
			} else {
				const progress = report.progress.toUpperCase();
				if ( progress !== cd.RevenueSharingReportProgress.COMPLETE.code && progress !== 'COMPLETED') { // 상태가 'COMPLETE'이 아님

					if (_.isNil(report.fileName)) {
						// 지금쯤 있어야 할 파일(fileName)이 없다는 건.. 아직 SummaryRevenueSharingReports.progress가 'START' 전이란 얘기
						// 차일드 프로세스에서 ( revenue-sharing-report-csv-child.service.writeIntoFile() )
						// 파일만 준비하고 ( _prepareFile() )
						// 아직 DB에 START 상태로 업데이트하기 전이란 얘기 ( _startReport() 호출하기 전 )

						const logPrefix = csvService.getLogPrefix(schInfo);

						// AP리포트 상태 조회
						const apReportsState = await csvService.getAdpReportsState(schInfo.schedule.publisher_id, schInfo.schedule.keyGroup_id, schInfo.endYmd, logPrefix);

						// 아직 AP 리포트가 만들어질 시간이 안됐으면 스킵 (https://oss.navercorp.com/da-ssp/bts/issues/816)
						const isPassed = await _isTimeOfReportApiTypePassed(schInfo.schedule.howManyDaysBefore, apReportsState.uncompleteAdps);
						if (isPassed) {
							// AP리포트 완료여부 조회
							const isApReportsComplete = (apReportsState.uncompleteAdps.length > 0) ?  false : true;

							// GFP 리포트 완료여부 조회
							let date;
							if (schInfo.schedule.interval === 'MONTHLY') {
								date = schInfo.endYmd.substr(0, 6);
							} else {
								date = schInfo.endYmd;
							}
							const isGfpReportsComplete = await csvService.isGfpReportsComplete(date, logPrefix);

							const abnormal = {
								'schedule': schInfo.schedule,
								'ymd': schInfo.endYmd,
								'report': report,
								'adpReportsState': apReportsState
							};

							if (!isApReportsComplete && !isGfpReportsComplete) {		// AP, GFP 둘 다 대기중
								abnormal['type'] = REPORT_ABNORMAL_TYPE_WAIT_REPORT;
								abnormals.push(abnormal);
							} else if (!isApReportsComplete && isGfpReportsComplete) {	// AP만 대기중
								abnormal['type'] = REPORT_ABNORMAL_TYPE_WAIT_AP_REPORT;
								abnormals.push(abnormal);
							} else if (isApReportsComplete && !isGfpReportsComplete) {	// GFP만 대기중
								abnormal['type'] = REPORT_ABNORMAL_TYPE_WAIT_GFP_REPORT;
								abnormals.push(abnormal);
							}
						} else {
							// 리포트 생성 시간이 아직 안된 AP가 있는 스케줄은 모니터링 스킵
							// const schedule = schInfo.schedule;
							// let msg = ' PUB:' + schedule.publisherName + ' (' + schedule.publisher_id + ')';
							// msg += ' SCH:' + schedule.name + ' (' + schedule._id + ')';
							// logger.debug(`[G-RVN-SHAR] revenue-sharing-report-monitor.service._checkReport() ::  리포트 생성 시간이 아직 안된 AP가 있음. ${msg}`);
						}
					} else { // 리포트가 안 끝났음. 진행중..
						abnormals.push({
							'schedule': schInfo.schedule,
							'ymd': schInfo.endYmd,
							'type': REPORT_ABNORMAL_TYPE_NOT_END,
							'report': report
						});
					}
				}
			}
		}

		if (_.isNil(abnormals) || _.isEmpty(abnormals)) {
			return true;
		} else {
			await _sendMailForCheckingReport(abnormals);
			return false;
		}
	} catch(error) {
		logger.error(`[G-RVN-SHAR] revenue-sharing-report-monitor.service._checkReport() :: ${error.stack}`);
	}
};


/**
 * 수익쉐어 리포트 모니터링 시 AP 통계 제공시간에 따라 알림 처리 (https://oss.navercorp.com/da-ssp/bts/issues/816)
 * 수익쉐어 리포트 - AP 상태 체크 기준 강화 (https://oss.navercorp.com/da-ssp/bts/issues/855)
 *
 * @param uncompleteAdps
 * @returns {Promise<boolean>}
 * @private
 */
const _isTimeOfReportApiTypePassed = async (howManyDaysBeforeOfPub, uncompleteAdps) => {
	let isPassed = true;

	for (const adpSchedule of uncompleteAdps) {
		const env = await Environments.findOne({'name': 'report-api-schedule-time'}).select('value.' + adpSchedule.reportApiType);

		if (!_.isNil(env) &&
			!_.isNil(env.value) &&
			!_.isNil(env.value[adpSchedule.reportApiType])) {
			const timeStr = env.value[adpSchedule.reportApiType].split(':');
			if (timeStr.length !== 2) {
				// Environments에 등록된 AP 스케줄이 포맷이 올바르지 않음. 통과로 간주.
				logger.debug(`Environments에 등록된 AP 스케줄이 포맷이 올바르지 않아 통과로 간주.`);
			} else {
				const hour = parseInt(timeStr[0]);
				const minute = parseInt(timeStr[1]);

				// AP 리포트가 생성되기를 기다리기 위한 버퍼
				let apReportWaitingMinutes = 10; // 디폴트 10 분
				const env = await Environments.findOne({'name': 'revenue-sharing-report-ap-report-waiting-minutes'}).select('value');
				if (!_.isNil(env) && !_.isNil(env.value)) {
					apReportWaitingMinutes = parseInt(env.value);
				}

				const criteria = moment()
					.hours(hour) // AP리포트 스케줄의 시분으로 설정하고
					.minutes(minute)
					.seconds(0)
					.milliseconds(0)
					.subtract(howManyDaysBeforeOfPub, 'days') // 수익쉐어 리포트 기준 며칠 전 날짜의 리포트인지를 구하고
					.add(-1 * adpSchedule.howManyDaysBefore, 'days') // AP리포트의 최근 리포트 날짜(AdProviders.rportApi.period.end)를 더해서
					.add(apReportWaitingMinutes, 'minutes'); // AP 리포트가 생성되기를 기다리기 위한 10분 버퍼 적용


				const now = moment();
				// console.log(`reportApiType:${adpSchedule.reportApiType} timestr:${timeStr}`);
				// console.log(`pub : ${criteria.format('YYYY.MM.DD HH:mm:ss')}`);
				// console.log(`now : ${now.format('YYYY.MM.DD HH:mm:ss')}`);

				// 지금이 기준 시각 이전이라면 아직 리포트가 나올 시간이 안된 것임.
				// 그러므로 AP 리포트가 패스되었는지는 false
				if (now.isSameOrBefore(criteria)) {
					isPassed = false;
					break;
				}
			}
		} else {
			logger.debug(`Environments에 등록된 AP 스케줄 시간이 등록되지 않아 통과로 간주.`);
		}
	}

	return isPassed;
};

/**
 * 비정상적인 리포트에 대해 메일 전송
 * @param abnormals
 * @returns {Promise.<void>}
 * @private
 */
const _sendMailForCheckingReport = async (abnormals) => {
	const subject = 'GFP 수익쉐어 리포트 생성이 비정상입니다.';
	
	let html = '';
	for (const abnormal of abnormals) {
		const schedule = abnormal.schedule;
		const report = abnormal.report;

		html += '[ 매체 ]<br/>';
		html += schedule.publisherName + ' (' + schedule.publisher_id + ')<br/><br/>';

		html += '[ 생성조건 ]<br/>';
		html += '	생서조건명: ' + schedule.name + ' (' + schedule._id + ')<br/>';
		html += '	키그룹: ' + schedule.keyGroup.name + ' (' + schedule.keyGroup._id + ')<br/>';
		html += '	생성주기: ' + cd.RevenueSharingReportPeriod[schedule.period].name + '<br/>';
		html += '	집계단위: ' + cd.RevenueSharingReportInterval[schedule.interval].name + '<br/>';
		html += '	파일생성기준: ' + (_.isNil(schedule.fileCreateCriteria) ? '-' : cd.RevenueSharingReportFileCreateCriteria[schedule.fileCreateCriteria].name) + '<br/><br/>';

		html += '[ 리포트 ]<br/>';
		if (abnormal.type == REPORT_ABNORMAL_TYPE_NOT_EXIST) {
			html += '	리포트가 존재하지 않음<br/>';
			html += '	날짜: ' + abnormal.ymd + '<br/>';
		} else if (abnormal.type == REPORT_ABNORMAL_TYPE_NOT_END) {
			html += '	리포트 생성이 아직도 진행중<br/>';
			html += '	_id: ' + report._id + '<br/>';
			html += '	파일: ' + report.filePath + '/' + report.fileName + '<br/>';
			html += '	날짜: ' + abnormal.ymd + '<br/>';
			html += '	상태: <span style="color:crimson">' + report.progress + '</span><br/>';
		} else if (abnormal.type == REPORT_ABNORMAL_TYPE_WAIT_REPORT) {
			html += '	AdProvider & GFP 리포트가 생성되기를 기다리는중<br/>';
			html += '	_id: ' + report._id + '<br/>';
			html += '	날짜: ' + abnormal.ymd + '<br/>';
			html += '	상태: <span style="color:crimson">' + report.progress + '</span><br/>';

			html += '	AP상태: <span style="color:crimson">' + csvService.getApReportsStatePhrase(abnormal.adpReportsState) + '</span><br/>';
			html += '	GFP상태: <span style="color:crimson">대기중</span><br/>';
		} else if (abnormal.type == REPORT_ABNORMAL_TYPE_WAIT_AP_REPORT) {
			html += '	AdProvider 리포트가 생성되기를 기다리는중<br/>';
			html += '	_id: ' + report._id + '<br/>';
			html += '	날짜: ' + abnormal.ymd + '<br/>';
			html += '	상태: <span style="color:crimson">' + report.progress + '</span><br/>';

			html += '	AP상태: <span style="color:crimson">' + csvService.getApReportsStatePhrase(abnormal.adpReportsState) + '</span><br/>';
		} else if (abnormal.type == REPORT_ABNORMAL_TYPE_WAIT_GFP_REPORT) {
			const report = abnormal.report;

			html += '	GFP 리포트가 생성되기를 기다리는중<br/>';
			html += '	_id: ' + report._id + '<br/>';
			html += '	날짜: ' + abnormal.ymd + '<br/>';
			html += '	상태: <span style="color:crimson">' + report.progress + '</span><br/>';
			html += '	GFP상태: <span style="color:crimson">대기중</span><br/>';
		}

		html += '<br/>--------------------------------------------------------------------------------<br/>';
	}

	logger.error(`[G-RVN-SHAR] ${subject} abnormals.length: ${abnormals.length}`);

	await _sendMail(subject, html);
};


/**
 * 생성 완료된 리포트의 파일 사이즈가 1024 bytes 이하인지 검사
 * @returns {Promise.<boolean>}
 * @private
 */
const _checkFileSize = async () => {
	try {
		const nubesClient = new NubesClient.Client(config.nubes.nam_api.gatewayAddress, config.nubes.nam_api.bucket);

		let reportListToCheck = [];

		// 오늘 생성된 리포트 가져오기
		const today = moment();
		today.startOf('day');
		const reportList = await SummaryRevenueSharingReport.find({'completedAt' : {$gte : today }});
		// const reportList = await SummaryRevenueSharingReport.find({_id: mongoose.Types.ObjectId('6283f0b5f1d88e03fb408c55')});
		if (_.isNil(reportList)) {
			return;
		}

		// 기준이 되는 파일 사이즈(bytes)
		let criteriaBytes = 512;
		const env = await Environments.findOne({'name': 'revenue-sharing-report-monitor-file-size-check-criteria-bytes'}).select('value');
		if (!_.isNil(env) && !_.isNil(env.value)) {
			criteriaBytes = parseInt(env.value);
		}

		const owfsDownloadRoot = config.owfs_root;
		for (let i = 0; i < reportList.length; i++) {
			const report = reportList[i];
			const schedule = await SummaryRevenueSharingSchedule.findOne({_id : report.summaryRevenueSharingSchedule_id});

			let fullPath = '';
			let stats = {};

			if (schedule.cmsType == 'GFP') {
				fullPath = path.join(owfsDownloadRoot, report.filePath, report.fileName); // 예) "/report/revenuesharing/2019/06/5c51739d70ef41001f900b0b"
				try {
					stats = fs.statSync(fullPath);
				} catch(e) {
					stats.size = 0;
					logger.error(`[G-RVN-SHAR] revenue-sharing-report-monitor.service._checkFileSize() :: ${e.stack}`);
				}
			} else { // 'NAM'
				fullPath = path.join(report.filePath, report.fileName); // 예) "/revenuesharing2/2019/06/5c51739d70ef41001f900b0b"
				/*
				 return {
					Name: path_1.posix.basename(path),
					IsEncodedName: false,
					IsDir: res.headers['x-object-type'] === 'directory',
					XETag: res.headers['x-etag'],
					ETag: res.headers['etag'],
					ModTime: mtime.isValid() ? mtime.local().format() : undefined,
					Size: Number.parseInt((_a = res.headers['x-object-size']) !== null && _a !== void 0 ? _a : '0'),
				};
				 */
				try {
					const nubesStats = await nubesClient.stat(fullPath)
					stats.size = nubesStats.Size;
				} catch(e) {
					stats.size = 0;
					logger.error(`[G-RVN-SHAR] revenue-sharing-report-monitor.service._checkFileSize() :: ${e.stack}`);
				}
			}
			logger.debug(`[G-RVN-SHAR] revenue-sharing-report-monitor.service._checkFileSize() :: report_id:${report._id} schName:${schedule.name} cmsType:${schedule.cmsType} fullPath:${fullPath} fileSize:${stats.size}`);

			if (stats.size <= criteriaBytes) { // 기준치 이하 bytes일 경우 알림
				const publisher = await Publisher.findOne({_id : schedule.publisher_id});

				const reportToCheck = {
					schedule: schedule,
					report: report,
					publisher: publisher,
					fileSize: stats.size + ' bytes'
				};

				reportListToCheck.push(reportToCheck);
			}
		}

		if (reportListToCheck.length > 0) {
			await _sendMailForCheckingFileSize(reportListToCheck);
			return false;
		} else {
			return true;
		}
	} catch(error) {
		logger.error(`[G-RVN-SHAR] revenue-sharing-report-monitor.service._checkFileSize() :: ${error.stack}`);
	}
};

/**
 * 기준치 이하 사이즈의 리포트에 대해 메일 전송
 * @param reportListToCheck
 * @returns {Promise.<void>}
 * @private
 */
const _sendMailForCheckingFileSize = async (reportListToCheck) => {
	const subject = `GFP 수익쉐어 리포트 - 기준 이하 사이즈 리포트 확인필요`;

	let html = '<table style="border:1px solid lightgrey">' +
		'<thead style="background: lightgrey"><td>매체</td><td>스케줄</td><td>리포트</td><td>파일사이즈</td></thead>' +
		'<tbody>';

	for (let i = 0; i < reportListToCheck.length; i++) {
		const item = reportListToCheck[i];
		html += `<tr>
					<td>${item.publisher.name}(${item.publisher._id})</td>
					<td>${item.schedule.name}(${item.schedule._id})</td>
					<td>${item.report._id} ${path.join(item.report.filePath, item.report.fileName)}</td>
					<td>${item.fileSize}</td>
				</tr>`;
	}
	html += '</tbody></table>';

	logger.error(`[G-RVN-SHAR] ${subject} reportListToCheck.length: ${reportListToCheck.length}`);

	await _sendMail(subject, html);
};

const _sendMail = async (subject, html) => {
	const env = await Environments.findOne({'name': 'revenue-sharing-report-monitor-receiver'}).select('value');

	if (_.isNil(env) || _.isNil(env.value) || _.isEmpty(env.value)) {
		return;
	}

	env.value.forEach(to => {
		mailer.sendMail({to, subject, html});
	});
};
