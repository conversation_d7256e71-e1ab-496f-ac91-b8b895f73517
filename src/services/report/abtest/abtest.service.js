'use strict';

import moment from 'moment';
import fs from "fs";
import path from 'path';
import zlib from "zlib";
import config from '../../../config/config';

import file from "../../../utils/file.util";
import * as logger from '../../../utils/logger.util';
import * as cuveUtil from "../../../utils/cuve.util";
import * as c3HdfsCli from "../../../c3/hdfs-cli";
import * as c3HdfsApi from "../../../c3/hdfs-api";

const LOGGER = 'abtest-report.service';

/**
 * downloadReportFromHdfs : HDFS 으로부터 report 파일 다운로드
 *
 * @param {Object} { ymd }
 */
module.exports.downloadReportFromHdfs = async ({ ymd }) => {
	logger.debug(`[${LOGGER} :: downloadReportFromHdfs] date=${ymd} 다운로드 시작`);

	const dt = moment(ymd);
	const dtPath = dt.format('YYYY/MM/DD');
	const reportPath = `${config.report.path}${config.report.abtest.path}`;
	const fileName = `/abtest_${ymd}.csv`;

	// 다운 받을 hdfs 경로
	// /user/gfp-data/abtest/yyyyy/MM/dd/abtest_yyyyMMdd.csv.gz
	const hdfsReportFile = path.join(config.report.abtest.hdfs.root_dir, dtPath, fileName) + '.gz';

	// 저장할 local 경로
	// /home1/irteam/deploy/local_download/report/abtest/yyyy/MM/dd/abtest_yyyyMMdd.csv
	const localFile = path.join(config.local_root, reportPath, dtPath, fileName);

	if(!await c3HdfsApi.exists(hdfsReportFile)) {
		logger.debug(`[${LOGGER} :: downloadReportFromHdfs] 존재하지 않는 report 경로= ${hdfsReportFile}`);
		return null;
	}

	// 로컬에 해당 path 의 디렉토리가 없으면 생성
	file.mkdirIfNotExist(path.dirname(localFile));

	// c3 hdfs 로부터 리포트 파일 다운로드
	await c3HdfsCli.download(hdfsReportFile, path.dirname(localFile), true, false);

	// gz 압축 풀기
	await new Promise((resolve, reject) => {
		const gunZipStream = zlib.createGunzip();
		const readStream = fs.createReadStream(`${localFile}.gz`);
		const writeStream = fs.createWriteStream(localFile);

		readStream.on('end', () => {
			logger.debug(`[${LOGGER} :: downloadReportFromHdfs] readStream end`);
		}).on('error', err => {
			logger.debug(`[${LOGGER} :: downloadReportFromHdfs] readStream error`);

			readStream.close();

			reject(err);
			return '';
		});

		writeStream.on('finish', () => {
			logger.debug(`[${LOGGER} :: downloadReportFromHdfs] writeStream finish`);

			resolve();
		}).on('error', err => {
			logger.debug(`[${LOGGER} :: downloadReportFromHdfs] writeStream error`);

			readStream.close();

			reject(err);
			return '';
		});

		readStream.pipe(gunZipStream).pipe(writeStream);
	});

	logger.debug(`[${LOGGER} :: downloadReportFromHdfs] HDFS 로부터 date=${ymd} abtest report 다운로드 완료`);

	// 압축파일 삭제
	file.deleteDirectoryRecursively(`${localFile}.gz`, true);

	return localFile;
};

/**
 * uploadReport : 로컬의 abtest report 파일을 Cysnc 를 사용하여 cuve 에 업로드
 *				  cuve 경로 : LRGM/yyyyMMdd/HHmm/abtest_{date}.csv
 * @param {Object} { ymd, localFilePath }
 */
module.exports.uploadReportWithCsync = async ({ ymd, localFilePath }) => {
	logger.debug(`[${LOGGER} :: uploadReport] date=${ymd} report 업로드 시작`);

	const LRGM = `${config.cuve_lrgm_root}/abt_report`;

	const uploadResult = await cuveUtil.putFileToCuveWithCsync(LRGM, localFilePath, ymd);

	logger.debug(`[${LOGGER} :: uploadReport] date=${ymd} report 업로드 ${uploadResult.isSuccess ? '완료' : '실패'}`);

	// 업로드 후 로컬 파일 삭제
	file.deleteDirectoryRecursively(localFilePath, true);

	return uploadResult.isSuccess;
};
