'use strict';

import fs from 'fs';
import _ from 'lodash';
import path from 'path';
import moment from 'moment';
import stream from 'stream';
import mongoose from 'mongoose';
import archiver from 'archiver';

import config from '../../../config/config';
import { BusinessError } from '../../../common/error';

import * as file from '../../../utils/file.util';
import extendedLogger from '../../../utils/logger-ext.util';

import * as c3HdfsCli from '../../../c3/hdfs-cli';
import * as c3HdfsApi from '../../../c3/hdfs-api';

import * as nubesCli from '../../../nubes/nubes-cli';

import { Publisher } from '../../../models/publishers.schema';
import { CustomReport } from '../../../models/data/custom-reports.schema';
import { DataEnvironments } from '../../../models/data/data-environments.schema';


// 확장 로거
const logger = extendedLogger('performance');

const ObjectId = mongoose.Types.ObjectId;

const GATEWAY_ADDRESS = config.nubes.nam_api.gatewayAddress;
const BUCKET = config.nubes.nam_api.bucket;

// HDFS_ROOT_DIR : /user/gfp-data/performance_report
const HDFS_ROOT_DIR = config.report.performance.hdfs.root_dir;
const API_DIR = config.report.performance.api_path;
const CMS_DIR = config.report.performance.cms_path;

// PERFORMANCE_DIR : /performance_report
const PERFORMANCE_DIR = config.report.performance.path;

// SUB_DIR : {{reportType:api,cms}}/{{reqDate:yyyyMMdd}}/reportId={{reportId}}
const SUB_DIR = path.join('{{reportType}}', '{{reqDate}}', 'reportId={{reportId}}');

// hdfsBasePath : /user/gfp-data/performance_report/{{reportType:api,cms}}/{{reqDate:yyyyMMdd}}/reportId={{reportId}}/
const hdfsBasePath = path.join(HDFS_ROOT_DIR, SUB_DIR);

// localBasePath : local_download/report/performance_report/{{reportType:api,cms}}/{{reqDate:yyyyMMdd}}/reportId={{reportId}}/
const localBasePath = path.join(config.local_root, config.report.path, PERFORMANCE_DIR, SUB_DIR);

// nubesBasePath : performance_report/{{reportType:api,cms}}/{{reqDate:yyyyMMdd}}/reportId={{reportId}}/
const nubesBasePath = path.join(PERFORMANCE_DIR, SUB_DIR);

// apiFileNameFormat : PerformanceReport_{{startDate}}_{{endDate}}.csv
// cmsFileNameFormat : CustomPerformanceReport_{{startDate}}_{{endDate}}_{{reportId}}.csv
const apiFileNameFormat = 'PerformanceReport_{{startDate}}_{{endDate}}.csv';
const cmsFileNameFormat = 'CustomPerformanceReport_{{startDate}}_{{endDate}}_{{reportId}}.csv';

// Performance Report State
const STATE = {
	IN_PROGRESS: 'IN_PROGRESS', FAILURE: 'FAILURE',
};

// Performance Report TaskState
const TASK_STATE = {
	AGG_COMPLETE: 'AGG_COMPLETE', UPLOAD_IN_PROGRESS: 'UPLOAD_IN_PROGRESS', UPLOAD_COMPLETE: 'UPLOAD_COMPLETE', UPLOAD_FAILURE: 'UPLOAD_FAILURE',
};

const STORAGE_TYPE = { HDFS: 'HDFS', NUBES: 'NUBES' };


/**
 * 매체 성과 리포트 업로드 처리중/성공/실패 상태에 해당 하는지 체크
 * 	- taskState = UPLOAD_IN_PROGRESS / UPLOAD_COMPLETE / UPLOAD_FAILURE
 */
module.exports.checkTaskStateUpload = async (txId, reportId) => {
	logger.debug(`[performance-report.service :: checkTaskStateUpload][txId=${txId}] reportId= ${reportId}`);

	return await _getCustomReportForPerf(txId, { reportId, state: STATE.IN_PROGRESS, taskState: `${TASK_STATE.UPLOAD_IN_PROGRESS}|${TASK_STATE.UPLOAD_COMPLETE}|${TASK_STATE.UPLOAD_FAILURE}` });
};


/**
 * 매체 성과 리포트 업로드 (HDFS -> NUBES)
 * 	- taskState = AGG_COMPLETE 인 건들만 업로드 대상임
 */
module.exports.upload = async (txId, reportId) => {
	logger.debug(`[performance-report.service :: upload][txId=${txId}] reportId= ${reportId}`);

	let localDir = '';

	try {
		// 1. CustomReport 정보 조회
		const report = await _getCustomReportForPerf(txId, { reportId, state: STATE.IN_PROGRESS, taskState: TASK_STATE.AGG_COMPLETE });

		// [ERROR] 리포트 정보가 없는 경우, 에러
		if (_.isEmpty(report)) {
			throw new BusinessError({ message: `[performance-report.service :: upload][txId=${txId}] DB 에 taskState=AGG_COMPLETE 인 CustomReport 정보가 없음 ( reportId= ${reportId} )` });
		}

		// 2. CustomReport 업로드 처리 중으로 변경 (UPLOAD_IN_PROGRESS)
		await _updateCustomReportForPerf(txId, { reportId, taskState: TASK_STATE.UPLOAD_IN_PROGRESS });

		// 3. HDFS & LOCAL & NUBES 리포트 파일 경로 정보 셋팅
		let {
			localReportDir, localReportFile,
			hdfsSuccessFile, hdfsReportFile, nubesReportFile,
		} = _getFilePathInfo(txId, report);

		localDir = localReportDir;

		// 4. HDFS 에 Success 파일이 있는지 체크 (리포트 생성 완료 여부 체크)
		await _isExistHdfsSuccess(hdfsSuccessFile);

		// 5. 파일 다운로드 ( HDFS -> LOCAL )
		await _download(txId, hdfsReportFile, localReportFile);

		// 6. 파일 크기 검증 ( LOCAL vs HDFS )
		await _validateFileSize(txId, { localReportFile, remoteReportFile: hdfsReportFile, storageType: STORAGE_TYPE.HDFS });

		// 7. 메타 정보 추가
		await _addMeta(txId, localReportFile, report);

		// 8. 파일 압축 및 경로 정보 변경
		({ localReportFile, nubesReportFile } = await _makeZipFile(txId, localReportFile, nubesReportFile));

		// 9. 파일 업로드 ( LOCAL -> NUBES )
		await _upload(txId, localReportFile, nubesReportFile);

		// 10. 파일 크기 검증 ( LOCAL vs NUBES )
		const fileSize = await _validateFileSize(txId, { localReportFile, remoteReportFile: nubesReportFile, storageType: STORAGE_TYPE.NUBES });

		// 11. CustomReport 처리 성공 (UPLOAD_COMPLETE)
		await _updateCustomReportForPerf(txId, { reportId, taskState: TASK_STATE.UPLOAD_COMPLETE, filePath: nubesReportFile, fileSize });
	} catch (e) {
		logger.error(`[performance-report.service :: upload][txId=${txId}] Error :: ( reportId= ${reportId} ) \n ${e.stack}\n\n`, e);

		// 12. CustomReport 처리 실패 (UPLOAD_FAILURE)
		await _updateCustomReportForPerf(txId, { reportId, taskState: TASK_STATE.UPLOAD_FAILURE });

		throw e;
	} finally {
		// 13. LOCAL 디렉토리 & 파일 삭제
		_deleteLocalDir(txId, localDir);
	}
};


/**
 * 1. _getCustomReportForPerf : CustomReport 정보 조회
 * 		맞춤 & 매체 성과 리포트 정보 조회
 */
const _getCustomReportForPerf = async (txId, { reportId, state = '', taskState = '' }) => {
	logger.debug(`[performance-report.service :: _getCustomReportForPerf][txId=${txId}] CustomReport 정보 조회 ( reportId= ${reportId}, state= ${state}, taskState= ${taskState} )`);

	const report = await CustomReport.findOne({
		_id: ObjectId(reportId), type: 'PERF', state: { $regex: `.*${state}.*` }, taskState: { $regex: `.*${taskState}.*` }
	});

	return report;
};


/**
 * 2. _updateCustomReportForPerf : CustomReport 업데이트
 * 		- UPLOAD_IN_PROGRESS :: taskState
 * 		- UPLOAD_COMPLETE :: taskState, filePath, fileSize
 * 		- UPLOAD_FAILURE :: taskState
 */
const _updateCustomReportForPerf = async (txId, { reportId, taskState, filePath, fileSize }) => {
	logger.debug(`[performance-report.service :: _updateCustomReportForPerf][txId=${txId}] CustomReport 변경 ( reportId= ${reportId}, taskState= ${taskState}, filePath= ${filePath}, fileSize= ${fileSize} )`);

	const set = { taskState };

	if (taskState === TASK_STATE.UPLOAD_COMPLETE) {
		set.filePath = filePath;
		set['ext.PERF.fileSize'] = parseInt(fileSize);
	}

	await CustomReport.findOneAndUpdate({ _id: ObjectId(reportId), type: 'PERF' }, { $set: set }, { new: true, runValidators: true }).exec();
};


/**
 * 3. _getFilePathInfo : HDFS & LOCAL & NUBES 리포트 파일 경로 정보
 */
const _getFilePathInfo = (txId, { _id, ext: { PERF: { reqDate } }, startDate, endDate, cmsUse }) => {
	const reqDt = moment(reqDate, 'YYYYMMDD').format('YYYY/MM/DD');
	const reportType = cmsUse === 1 ? CMS_DIR : API_DIR;
	const fileNameFormat = cmsUse === 1 ? cmsFileNameFormat : apiFileNameFormat;

	// 외부성과 API : PerformanceReport_20240301_20240315.csv
	// 맞춤성과 CMS : CustomPerformanceReport_20240301_20240315_xxxxxx.csv
	const fileName = fileNameFormat.replace(/{{startDate}}/g, startDate).replace(/{{endDate}}/g, endDate).replace(/{{reportId}}/g, _id);

	/* hdfs 파일 정보 */
	// hdfsReportDir = /user/gfp-data/performance_report/api/2023/04/01/reportId=xxxxxx
	const hdfsReportDir = hdfsBasePath.replace(/{{reportId}}/g, _id).replace(/{{reqDate}}/g, reqDt).replace(/{{reportType}}/g, reportType);

	// hdfsSuccessFile = /user/gfp-data/performance_report/api/2023/04/01/reportId=xxxxxx/_SUCCESS
	const hdfsSuccessFile = path.join(hdfsReportDir, '_SUCCESS');

	// hdfsReportFile = /user/gfp-data/performance_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv
	const hdfsReportFile = path.join(hdfsReportDir, fileName);


	/* local 파일 정보 */
	// localReportDir = local_download/report/performance/api/2023/04/01/reportId=xxxxxx/
	const localReportDir = file.mkdirIfNotExist(localBasePath.replace(/{{reportId}}/g, _id).replace(/{{reqDate}}/g, reqDt).replace(/{{reportType}}/g, reportType));

	// localReportFile = local_download/report/performance/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv
	const localReportFile = path.join(localReportDir, fileName);


	/* nubes 파일 정보 */
	// nubesReportDir = performance_report/api/2023/04/01/reportId=xxxxxx
	const nubesReportDir = nubesBasePath.replace(/{{reportId}}/g, _id).replace(/{{reqDate}}/g, reqDt).replace(/{{reportType}}/g, reportType);

	// nubesReportFile = performance_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv
	const nubesReportFile = path.join(nubesReportDir, fileName);

	logger.debug(`[performance-report.service :: _getFilePathInfo][txId=${txId}] HDFS & LOCAL & NUBES 리포트 파일 경로 정보 \n- hdfsReportFile= ${hdfsReportFile}\n- localReportFile= ${localReportFile}\n- nubesReportFile= ${nubesReportFile}`);

	return { localReportDir, localReportFile, hdfsSuccessFile, hdfsReportFile, nubesReportFile };
};


/**
 * 4. _isExistHdfsSuccess : hdfs 에 success 파일이 존재하는지 확인
 *		- success 파일 경로 : /user/gfp-data/performance_report/{reportType:api,cms}/{reqDate:yyyy/MM/dd}/reportId={reportId}/_SUCCESS
 */
const _isExistHdfsSuccess = async hdfsSuccessFile => {
	logger.debug(`[performance-report.service :: _isExistHdfsSuccess] hdfsSuccessFile= ${hdfsSuccessFile}`);

	const isExist = await c3HdfsApi.exists(hdfsSuccessFile);

	if (!isExist) {
		throw new BusinessError({ message: `[performance-report.service :: _isExistHdfsSuccess] HDFS _SUCCESS 파일이 존재 하지 않음 ( hdfsSuccessFile= ${hdfsSuccessFile} )` });
	}
};


/**
 * 5. _download : 파일 다운로드 ( HDFS -> LOCAL )
 */
const _download = async (txId, hdfsReportFile, localReportFile) => {
	logger.debug(`[performance-report.service :: _download][txId=${txId}] HDFS 파일 다운로드 시작 ( hdfsReportFile= ${hdfsReportFile}, localReportFile= ${localReportFile} )`);

	if (!await c3HdfsApi.exists(hdfsReportFile)) {
		throw new BusinessError({ message: `[performance-report.service :: _download][txId=${txId}] HDFS 에 원본 파일이 존재 하지 않음 (hdfsReportFile= ${hdfsReportFile})` });
	}

	// hdfsReportFile = /user/gfp-data/performance_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv
	// localReportFile = local_download/report/performance_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv
	await c3HdfsCli.download(hdfsReportFile, localReportFile, true, false);

	// // gz 압축 풀기
	// await new Promise((resolve, reject) => {
	// 	// const gunZipStream = zlib.createGunzip();
	// 	const readStream = fs.createReadStream(`${localC3File}.gz`);
	// 	const writeStream = fs.createWriteStream(localC3File);
	//
	// 	readStream.on('end', () => {
	// 		logger.debug(`[performance-report.service :: _download][txId=${txId}] readStream end`);
	// 	}).on('error', err => {
	// 		logger.debug(`[performance-report.service :: _download][txId=${txId}] readStream error`);
	//
	// 		readStream.close();
	//
	// 		reject(err);
	// 	});
	//
	// 	writeStream.on('finish', () => {
	// 		logger.debug(`[performance-report.service :: _download][txId=${txId}] writeStream finish`);
	//
	// 		resolve();
	// 	}).on('error', err => {
	// 		logger.debug(`[performance-report.service :: _download][txId=${txId}] writeStream error`);
	//
	// 		readStream.close();
	//
	// 		reject(err);
	// 	});
	//
	// 	readStream.pipe(gunZipStream).pipe(writeStream);
	// });

	logger.debug(`[performance-report.service :: _download][txId=${txId}] HDFS 파일 다운로드 완료`);
};


/**
 * 6. _validateFileSize : 파일 크기 검증
 */
const _validateFileSize = async (txId, { localReportFile, remoteReportFile, storageType }) => {
	logger.debug(`[performance-report.service :: _validateFileSize][txId=${txId}] LOCAL vs ${storageType} 파일 크기 검증 ( localReportFile=${localReportFile}, remoteReportFile=${remoteReportFile} )`);

	const localFileSize = fs.statSync(localReportFile).size.toString();

	let remoteFileSize;

	if (_.isEqual(storageType, STORAGE_TYPE.HDFS)) {
		// HDFS 파일 사이즈
		const status = await c3HdfsApi.stat(remoteReportFile);
		remoteFileSize = status.length.toString();
	} else if (_.isEqual(storageType, STORAGE_TYPE.NUBES)) {
		// NUBES 파일 사이즈
		const status = await nubesCli.status(GATEWAY_ADDRESS, BUCKET, remoteReportFile);
		remoteFileSize = status['X-Object-Size'].toString();
	}

	// 파일 크기가 같지 않은 경우, Error
	if (localFileSize !== remoteFileSize) {
		throw new BusinessError({ message: `LOCAL vs ${storageType} 파일 크기 불일치 ( localFileSize= ${localFileSize}, remoteFileSize= ${remoteFileSize} )` });
	}

	return localFileSize;
};


/**
 * 7. _addMeta : 메타 정보 추가
 * 		- CMS 리포트인 경우, 메타 정보 추가
 */
const _addMeta = async (txId, localReportFile, { _id, publisher_id, timezone, startDate, endDate, cmsUse }) => {
	logger.debug(`[performance-report.service :: _addMeta][txId=${txId}] 메타 정보 추가 ( cmsUse= ${cmsUse}, localReportFile= ${localReportFile} )`);

	// CMS 리포트가 아닌 경우, 메타 정보 추가 안 함
	if (cmsUse === 0) {
		return;
	}

	// 임시 파일 경로
	// local_download/report/performance_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv.temp
	const tempFile = `${localReportFile}.temp`;

	const publisher = await Publisher.findOne({ _id: publisher_id }).select('-_id name').exec();

	const metaInfo = [
		`Publisher,${!_.isEmpty(publisher) ? publisher.name : ''}`,
		'Report Type,Custom Performance Report',
		`Creation Time,${moment().format('YYYY-MM-DD HH:mm:ss Z')}`,
		`Timezone,${_.isEqual(timezone, '-') ? 'Ad Provider Timezone' : timezone}`,
		`Period,${moment(startDate).format('YYYY-MM-DD')} ~ ${moment(endDate).format('YYYY-MM-DD')}`,
		`Report ID,${_id}`,
	].join('\n') + '\n\n';

	// 메타 정보 추가
	await new Promise((resolve, reject) => {
		const readStream = fs.createReadStream(localReportFile);
		const writeStream = fs.createWriteStream(tempFile, { flags: 'a', autoClose: false, encoding: 'utf8' });

		let isFirstChunk = true;
		const metaAddStream = new stream.Transform({
			transform(chunk, encoding, callback) {
				let data = chunk.toString();

				// 첫 번째 청크 인지 확인
				if (isFirstChunk) {
					isFirstChunk = false;

					// BOM 이 있는 경우, 메타 정보 추가
					if (data.startsWith('\ufeff')) {
						data = '\ufeff' + metaInfo + data.slice(1);
					}
				}

				callback(null, data);
			}
		});

		readStream.on('end', () => {
			logger.debug(`[performance-report.service :: _addMeta][txId=${txId}] readStream end`);
		}).on('error', err => {
			logger.debug(`[performance-report.service :: _addMeta][txId=${txId}] readStream error`);

			closeStreams();

			reject(err);
		});

		writeStream.on('finish', () => {
			logger.debug(`[performance-report.service :: _addMeta][txId=${txId}] writeStream finish`);

			resolve();
		}).on('error', err => {
			logger.debug(`[performance-report.service :: _addMeta][txId=${txId}] writeStream error`);

			closeStreams();

			reject(err);
		});

		readStream.pipe(metaAddStream).pipe(writeStream);

		// 모든 stream 종료 처리
		const closeStreams = () => {
			if (writeStream) writeStream.close();
			if (readStream) readStream.close();
		};
	});

	// 임시 파일을 원본 파일로 대체
	fs.renameSync(tempFile, localReportFile);
};


/**
 * 8. _makeZipFile : 파일 압축
 * 		- 파일 사이즈가 100 MB 이상인 경우, zip 으로 압축
 * 		- 로컬 및 누베스 파일 경로를 zip 경로로 변경
 */
const _makeZipFile = async (txId, localReportFile, nubesReportFile) => {
	logger.debug(`[performance-report.service :: _makeZipFile][txId=${txId}] 파일 압축 ( localReportFile= ${localReportFile} )`);

	// - localReportFile = local_download/report/performance_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv
	// - nubesReportFile = /performance_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv

	const localFileSize = fs.statSync(localReportFile).size.toString();
	const minFileSize = await _getMinFileSizeForCompression();

	// 파일 사이즈가 100 MB 이하인 경우, 압축 하지 않음
	if (localFileSize < minFileSize) {
		logger.debug(`[performance-report.service :: _makeZipFile][txId=${txId}] 파일 압축 대상 아님 ( localReportFile= ${localReportFile}, localFileSize= ${localFileSize} )`);

		return { localReportFile, nubesReportFile };
	}

	const localFileZipSize = await new Promise((resolve, reject) => {
		// fileName = PerformanceReport_20240301_20240315.csv
		const fileName = path.parse(localReportFile).base;
		const writeStream = fs.createWriteStream(`${localReportFile}.zip`);
		const archive = archiver('zip', { zlib: { level: 6 } }); // 압축 레벨 설정 (0-9). 디폴트 6

		writeStream.on('finish', () => {
			logger.debug(`[performance-report.service :: _makeZipFile][txId=${txId}] writeStream finish`);

			logger.debug(`압축 파일 크기 : ${archive.pointer()} bytes`);

			resolve(archive.pointer());
		});

		archive.on('warning', err => {
			logger.debug(`[performance-report.service :: _makeZipFile][txId=${txId}] archive warning`);

			closeStreams();

			reject(err);
		});

		archive.on('error', err => {
			logger.debug(`[performance-report.service :: _makeZipFile][txId=${txId}] archive error`);

			closeStreams();

			reject(err);
		});

		archive.pipe(writeStream);

		archive.file(localReportFile, { name: fileName });

		archive.finalize();

		// 모든 stream 종료 처리
		const closeStreams = () => {
			if (writeStream) writeStream.close();
			if (readStream) readStream.close();
		};
	});

	// 로컬 및 누베스 파일 경로를 zip 경로로 변경
	// - localReportFile = local_download/report/performance_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv.zip
	// - nubesReportFile = /performance_report/api/2023/04/01/reportId=xxxxxx/PerformanceReport_20240301_20240315.csv.zip
	localReportFile = `${localReportFile}.zip`;
	nubesReportFile = `${nubesReportFile}.zip`;

	logger.debug(`[performance-report.service :: _makeZipFile][txId=${txId}] 파일 압축 결과 ( 전= ${(localFileSize / (1024 * 1024)).toFixed(2)} MB, 후= ${(localFileZipSize / (1024 * 1024)).toFixed(2)} MB )`);
	logger.debug(`[performance-report.service :: _makeZipFile][txId=${txId}] 파일 압축 및 경로 변경 완료 ( localReportFile= ${localReportFile}, nubesReportFile= ${nubesReportFile} )`);

	return { localReportFile, nubesReportFile };
};


const _getMinFileSizeForCompression = async () => {
	// 압축을 위한 최소 파일 크기 (MB 단위)
	const env = await DataEnvironments.findOne({ name: 'min-file-size-for-compression' });
	const minFileSize = env ? env.value : 100;

	// Byte 단위로 변환
	return minFileSize * 1024 * 1024;
}


/**
 * 9. _upload : 파일 업로드 ( LOCAL -> NUBES )
 */
const _upload = async (txId, localReportFile, nubesReportFile) => {
	logger.debug(`[performance-report.service :: _upload][txId=${txId}] NUBES 파일 업로드 시작 ( localReportFile= ${localReportFile}, nubesReportFile= ${nubesReportFile} )`);

	// parallel upload (overwrite = true)
	await nubesCli.pupload(GATEWAY_ADDRESS, BUCKET, localReportFile, nubesReportFile);

	logger.debug(`[performance-report.service :: _upload][txId=${txId}] NUBES 파일 업로드 완료`);
};


/**
 * 13. _deleteLocalDir : LOCAL 디렉토리 & 파일 삭제
 */
const _deleteLocalDir = (txId, localDir) => {
	logger.debug(`[performance-report.service :: _deleteLocalDir][txId=${txId}] localDir=${localDir} 삭제 시작`);

	if (fs.existsSync(localDir)) {
		// 디렉토리 & 파일 삭제
		file.deleteDirectoryRecursively(localDir, true);
	}

	logger.debug(`[performance-report.service :: _deleteLocalDir][txId=${txId}] localDir=${localDir} 삭제 완료`);
};


/**
 * 매체 성과 리포트 HDFS & NUBES 삭제
 *     - 1주 전 한달치 리포트 HDFS & NUBES 일괄 삭제
 *     - ex> 12월 8일에 실행 시, 10월 30일 ~ 11월 30일 일괄 삭제 처리됨
 *     - ex> 12월 15일에 실행 시, 11월 7일 ~ 12월 7일 일괄 삭제 처리됨
 *     - ex> 12월 22일에 실행 시, 11월 14일 ~ 12월 14일 일괄 삭제 처리됨
 */
module.exports.delete = async (txId) => {
	logger.debug(`[performance-report.service :: delete][txId=${txId}]`);

	// 1주 전 기준으로 한달치 디렉토리 삭제 (오늘이 12월 15일이면 11월 7일 ~ 12월 7일 일괄 삭제)
	// targetDate 이전 리포트 파일 일괄 삭제 (include)
	const targetEndDate = moment().subtract(8, 'days').startOf('day');
	const targetStartDate = moment(targetEndDate).subtract(1, 'months');

	logger.debug(`[performance-report.service :: delete][txId=${txId}] ${targetStartDate.format('YYYY-MM-DD')} ~ ${targetEndDate.format('YYYY-MM-DD')} 일괄 삭제 시작`);

	const dateList = await _getDateList(targetStartDate, targetEndDate);

	// HDFS 일괄 삭제
	await _deleteHdfs(txId, dateList);

	// NUBES 일괄 삭제
	await _deleteNubes(txId, dateList);

	logger.debug(`[performance-report.service :: delete][txId=${txId}] ${targetStartDate.format('YYYY-MM-DD')} ~ ${targetEndDate.format('YYYY-MM-DD')} 일괄 삭제 완료`);
};


/**
 * 날짜 리스트
 */
const _getDateList = async (startDate, endDate) => {
	const dateList = [];

	let tempDate = moment(startDate);
	while (tempDate.isSameOrBefore(endDate)) {
		dateList.push(tempDate.format('YYYY/MM/DD'));

		tempDate.add(1, 'days');
	}

	return dateList;
}


/**
 * HDFS 일괄 삭제
 */
const _deleteHdfs = async (txId, dateList) => {
	logger.debug(`[performance-report.service :: _deleteHdfs][txId=${txId}] HDFS 일괄 삭제 시작`);

	await Promise.all(dateList.map(async date => {
		// deleteDirPath = /user/gfp-data/performance_report/api/YYYY/MM/DD
		const deleteDirPath = path.join(HDFS_ROOT_DIR, API_DIR, date);

		// 디렉토리 경로가 HDFS 에 존재하는지 확인
		if (await c3HdfsApi.exists(deleteDirPath)) {
			// 디렉토리 일괄 삭제하기
			const err = await c3HdfsApi.delete(deleteDirPath, true);

			// 에러가 발생한 경우
			if (err) {
				throw new BusinessError({ message: `[performance-report.service :: _deleteHdfs] HDFS 일괄 삭제 실패` }, {
					err,
					detail: JSON.stringify(err, null, 2)
				});
			}
		}
	}));

	logger.debug(`[performance-report.service :: _deleteHdfs][txId=${txId}] HDFS 일괄 삭제 완료`);
};


/**
 * NUBES 일괄 삭제
 */
const _deleteNubes = async (txId, dateList) => {
	logger.debug(`[performance-report.service :: _deleteNubes][txId=${txId}] NUBES 일괄 삭제 시작`);

	await Promise.all(dateList.map(async date => {
		// deleteDirPath = performance_report/api/YYYY/MM/DD
		const deleteDirPath = path.join(PERFORMANCE_DIR, API_DIR, date);

		// 디렉토리 경로가 NUBES 에 존재하는지 확인
		if (await _existNubes(deleteDirPath)) {
			// 디렉토리 일괄 삭제하기
			const err = await nubesCli.delete(GATEWAY_ADDRESS, BUCKET, deleteDirPath, true);

			// 에러가 발생한 경우
			if (err) {
				throw new BusinessError({ message: `[performance-report.service :: _deleteNubes] NUBES 일괄 삭제 실패` }, {
					err,
					detail: JSON.stringify(err, null, 2)
				});
			}
		}
	}));

	logger.debug(`[performance-report.service :: _deleteNubes][txId=${txId}] NUBES 일괄 삭제 완료`);
};


/**
 * NUBES 존재 유무 확인
 */
const _existNubes = async path => {
	try {
		await nubesCli.status(GATEWAY_ADDRESS, BUCKET, path);

		return true;
	} catch (e) {
		return false;
	}
};
