'use strict';

import moment from 'moment';
import fs from "fs";
import util from "util";
import path from 'path';
import zlib from "zlib";
import config from '../../../config/config';

import file from "../../../utils/file.util";
import * as logger from '../../../utils/logger.util';
import * as cuveUtil from "../../../utils/cuve.util";
import * as c3HdfsCli from "../../../c3/hdfs-cli";
import * as c3HdfsApi from "../../../c3/hdfs-api";

const LOGGER = 'shortform-report.service';

const serviceLRGM = `korea_real/korea/inspector/video_valid_imp_service`;
const viewerInfoLRGM = `korea_real/korea/inspector/video_valid_imp_viewer_info`;
const shortformLRGM = `${config.cuve_lrgm_root}/shortform_revenue_share_report`;

const localServiceFile = 'service/%s_service.parquet';
const localViewerFile = 'viewer/%s_viewer.parquet';

/**
 * downloadReportWithCsync : cuve 의 shortform 소스 리포트 파일을 Csync 를 사용하여 로컬로 다운로드
 * 							 cuve 경로 : LRGM/yyyyMMdd/HHmm/{date}_service.parquet
 *										LRGM/yyyyMMdd/HHmm/{date}_viewer.parquet
 *
 * @param {Object} { ymd }
 */
module.exports.downloadReportWithCsync = async ({ ymd }) => {
	logger.debug(`[${LOGGER} :: downloadReportWithCsync] date=${ymd} 다운로드 시작`);

	const dt = moment(ymd);
	const localDtPath = dt.format('YYYY/MM/DD');
	const reportPath = `${config.report.path}${config.report.shortform.path}`;

	const localFilePath1 = path.join(config.local_root, reportPath, localDtPath, util.format(localServiceFile, ymd));
	const localFilePath2 = path.join(config.local_root, reportPath, localDtPath, util.format(localViewerFile, ymd));

	const downloadedResult1 = await cuveUtil.getFileToLocalWithCsync(serviceLRGM, localFilePath1, ymd);		// service 파일 다운로드
	const downloadedResult2 = await cuveUtil.getFileToLocalWithCsync(viewerInfoLRGM, localFilePath2, ymd);	// viewer 파일 다운로드

	// getFileToLocalWithCsync 가 성공인 경우 msg 에 다운 받은 파일의 경로를 반환
	const downloadedFilePath1 = downloadedResult1.msg;
	const downloadedFilePath2 = downloadedResult2.msg;

	if(downloadedResult1.isSuccess && localFilePath1 === downloadedFilePath1) {
		if(downloadedResult2.isSuccess && localFilePath2 === downloadedFilePath2) {
			logger.info(`[${LOGGER} :: downloadReportWithCsync] date=${ymd} 소스 리포트 파일 다운로드 완료}`);
			return [true, downloadedFilePath1, downloadedFilePath2];
		}
		else {
			logger.error(`[${LOGGER} :: downloadReportWithApi] 소스 리포트 파일(viewer) 다운로드에 실패했습니다. path=${downloadedFilePath2}`);
		}
	}
	else {
		logger.error(`[${LOGGER} :: downloadReportWithApi] 소스 리포트 파일(service) 다운로드에 실패했습니다. path=${downloadedFilePath1}`);
	}

	logger.info(`[${LOGGER} :: downloadReportWithCsync] date=${ymd} report 업로드 실패}`);
	return [false, downloadedFilePath1, downloadedFilePath2];	// 실패인 경우 FilePath 대신 에러 메시지
};

/**
 * downloadReportFromHdfs : HDFS 의 shortform 집계 결과 리포트 파일을 로컬로 다운로드
 *
 * @param {Object} { ymd }
 */
module.exports.downloadReportFromHdfs = async ({ ymd }) => {
	logger.debug(`[${LOGGER} :: downloadReportFromHdfs] date=${ymd} 다운로드 시작`);

	const dt = moment(ymd);
	const dtPath = dt.format('YYYY/MM/DD');
	const reportPath = `${config.report.path}${config.report.shortform.path}`;
	const fileName = `/${ymd}_shortform_revenue_sharing.csv`;

	// 다운 받을 hdfs 경로
	// /user/gfp-data/shortform/yyyyy/MM/dd/yyyyMMdd_shortform_revenue_sharing.csv.gz
	const hdfsReportFile = path.join(config.report.shortform.hdfs.root_dir, dtPath, fileName) + '.gz';

	// 저장할 local 경로
	// /home1/irteam/deploy/local_download/report/shortform/yyyyy/MM/dd/yyyyMMdd_shortform_revenue_sharing.csv
	const localFile = path.join(config.local_root, reportPath, dtPath, fileName);

	if (!await c3HdfsApi.exists(hdfsReportFile)) {
		logger.error(`[${LOGGER} :: downloadReportFromHdfs] 존재하지 않는 report 경로= ${hdfsReportFile}`);
		return '';
	}

	// 로컬에 해당 path 의 디렉토리가 없으면 생성
	await file.mkdirIfNotExist(path.dirname(localFile));

	// c3 hdfs 로부터 리포트 파일 다운로드
	await c3HdfsCli.download(hdfsReportFile, path.dirname(localFile), true, false);

	// gz 압축 풀기
	await new Promise((resolve, reject) => {
		const gunZipStream = zlib.createGunzip();
		const readStream = fs.createReadStream(`${localFile}.gz`);
		const writeStream = fs.createWriteStream(localFile);

		readStream.on('end', () => {
			logger.debug(`[${LOGGER} :: downloadReportFromHdfs] readStream end`);
		}).on('error', err => {
			logger.debug(`[${LOGGER} :: downloadReportFromHdfs] readStream error`);

			readStream.close();

			reject(err);
			return '';
		});

		writeStream.on('finish', () => {
			logger.debug(`[${LOGGER} :: downloadReportFromHdfs] writeStream finish`);

			resolve();
		}).on('error', err => {
			logger.debug(`[${LOGGER} :: downloadReportFromHdfs] writeStream error`);

			readStream.close();

			reject(err);
			return '';
		});

		readStream.pipe(gunZipStream).pipe(writeStream);
	});

	logger.debug(`[${LOGGER} :: downloadReportFromHdfs] HDFS 로부터 date=${ymd} shortform report 다운로드 완료`);

	// 압축 파일 삭제
	file.deleteDirectoryRecursively(`${localFile}.gz`, true);

	return localFile;
};

/**
 * downloadReportWithCsync : 로컬의 집계 리포트 파일을 Csync 를 사용하여 cuve로 업로드
 * 							 cuve 경로 : LRGM/yyyyMMdd/HHmm/{date}_shortform_revenue_sharing.csv
 *
 * @param {Object} { ymd, localFilePath }
 */
module.exports.uploadReportWithCsync = async ({ ymd, localFilePath }) => {
	logger.debug(`[${LOGGER} :: downloadReportWithCsync] date=${ymd} 업로드 시작`);

	const uploadResult = await cuveUtil.putFileToCuveWithCsync(shortformLRGM, localFilePath, ymd);

	logger.info(`[${LOGGER} :: uploadReportWithCsync] date=${ymd} Cuve 로 shortfrom report 업로드 ${uploadResult.isSuccess ? '완료' : '실패'}`);

	// 업로드 후 로컬 파일 삭제
	file.deleteDirectoryRecursively(localFilePath, true);

	return uploadResult.isSuccess;

};

/**
 * uploadReport : 로컬의 shortform 소스 파일을 HDFS 로 업로드
 *				  cuve 경로 : LRGM/yyyyMMdd/HHmm/abtest_{date}.parquet
 * @param {Object} { ymd, localFilePath1, localFilePath2 }
 */
module.exports.uploadReportToHdfs = async ({ ymd, localFilePath1, localFilePath2 }) => {
	logger.debug(`[${LOGGER} :: uploadReportToHdfs] date=${ymd} report 업로드 시작`);

	const dt = moment(ymd);
	const dtPath = dt.format('YYYY/MM/DD');

	// 업로드 할 hdfs 경로
	const remoteFilePath1 = path.join(config.report.shortform.hdfs.root_dir, dtPath, util.format(localServiceFile, ymd));
	const remoteFilePath2 = path.join(config.report.shortform.hdfs.root_dir, dtPath, util.format(localViewerFile, ymd));

	if(!fs.existsSync(localFilePath1)) {
		logger.error(`[${LOGGER} :: uploadReportToHdfs] 존재하지 않는 소스 report 경로입니다. path= ${localFilePath1}`);
		return false;
	}
	if(!fs.existsSync(localFilePath2)) {
		logger.error(`[${LOGGER} :: uploadReportToHdfs] 존재하지 않는 소스 report 경로입니다. path= ${localFilePath2}`);
		return false;
	}

	await c3HdfsApi.mkdir(path.dirname(remoteFilePath1));
	await c3HdfsCli.upload(localFilePath1, remoteFilePath1);

	await c3HdfsApi.mkdir(path.dirname(remoteFilePath2));
	await c3HdfsCli.upload(localFilePath2, remoteFilePath2);

	logger.debug(`[${LOGGER} :: uploadReportToHdfs] HDFS 로 date=${ymd} 소스 리포트 파일 업로드 완료`);

	// 업로드 후 로컬 파일 삭제
	file.deleteDirectoryRecursively(localFilePath1, true);
	file.deleteDirectoryRecursively(localFilePath2, true);

	return true;
};
