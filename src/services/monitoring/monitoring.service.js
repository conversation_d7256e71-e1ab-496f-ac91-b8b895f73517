'use strict';

import csv from 'csv';
import fs from 'fs';
import _ from 'lodash';
import moment from 'moment-timezone';
import path from 'path';
import * as c3HdfsApi from '../../c3/hdfs-api';
import * as c3HdfsCli from '../../c3/hdfs-cli';

import { BusinessError } from '../../common/error';

import config from '../../config/config';

import { AdProvider } from '../../models/ad-providers.schema';

import { BatchReportJobSchedule } from '../../models/batch-report-job-schedule.schema';
import { DataEnvironments } from '../../models/data/data-environments.schema';
import { MonitoringReportApiStatsCms } from '../../models/monitoring-report-api-stats.schema';
import { MonitoringReportApiStats } from '../../models/data/monitoring-report-api-stats.schema';

import * as file from '../../utils/file.util';
import * as logger from '../../utils/logger.util';

import * as mailer from '../../utils/mail.util';


/**
 * - AP 수익 연동 리포트 타입(reportApiType)이 "NCC"인 경우, 리포트의 원본이 되는 ssp.tsv에 대한 요약 지표 생성
 *
 * - 모 티켓   : [지표집계] IT응용통제(ITAC) 개선 (https://oss.navercorp.com/sa-platform/adpost-issues/issues/920)
 * - 부 티켓   : NCC IT응용통제(ITAC) 감사로 인한 개선 대응 - 수익쉐어 리포트 검증 데이터 제공 API (https://oss.navercorp.com/da-ssp/bts/issues/1181)
 * - 관련 티켓 : [DATA] 수익쉐어 개선으로 인한 리포트 연동 로직 변경 (https://jira.navercorp.com/browse/GFP-1031)
 *
 * - NCC로부터 가져온 ssp.tsv 파일을 읽어서 다음과 같은 지표를 생성하여 저장한다.
 * 		- rowCnt, sumOfImp, sumOfClk, sumOfKrwRevenue, sumOfKrwNetRevenue
 *
 * - NAM API 서버에서 NCC 리포트 연동에 대한 검증 지표로 제공함.
 *
 * - 이 지표 확인은 NCC에서 해야 하나, 여건 상 실질적인 업무는 애드포스트에서 하고 있음.
 * 		- https://jira.navercorp.com/browse/GFP-392?focusedId=8945563&page=com.atlassian.jira.plugin.system.issuetabpanels:comment-tabpanel#comment-8945563
 * 		- http://localhost:3002/v1/verification/ncc/stats?date=20250316
 *
 * 	- make_ncc_report_api_stats DAG으로부터 매 시 15분에 호출됨
 *
 * @param txId
 * @param date yyyyMMdd 형식
 * @returns {Promise<void>}
 */
module.exports.makeNccReportApiStats = async (txId, date) => {
	const schedule = await BatchReportJobSchedule.findOne({ reportApiType: 'NCC', 'period.endDate': date });

	if (!_.isNil(schedule) && schedule.state === 'COMPLETE') {
		// 이미 생성된 모니터링 통계가 있는지 확인
		const filter = { reportApiType: 'NCC', date };
		const monitoringDoc = await MonitoringReportApiStats.findOne(filter);
		logger.debug(`monitoring.service.makeNccReportApiStats() date:${date}
			ncc schedule.state           = ${schedule.state}
			ncc schedule.modifiedAt      = ${moment(schedule.modifiedAt).tz('Asia/Seoul').format('YYYY-MM-DD HH:mm:ss')}
			ncc monitoringDoc.modifiedAt = ${monitoringDoc ? moment(monitoringDoc.modifiedAt).tz('Asia/Seoul').format('YYYY-MM-DD HH:mm:ss') : 'N/A'}`);

		// 통계가 아예 없거나, 이미 생성되었는데 NCC 리포트 연동이 재실행된 경우에만 다시 생성
		const alreadyExists = !_.isNil(monitoringDoc);
		const run = alreadyExists ? (schedule.modifiedAt >= monitoringDoc.modifiedAt ? 1 : 0) : 1;

		if (run == 0) {
			logger.debug(`monitoring.service.makeNccReportApiStats() 이미 생성된 데이터가 있고, AP 연동 리포트가 재처리되지 않았으므로 스킵. date:${date}`);
			return;
		}

		const dt = moment(date);
		const fileName = `ncc_${date}.tsv`;

		// 이 날짜의 리포트가 실행되어야 할 스케줄 날짜 계산(경로명으로 사용)
		const doc = await AdProvider.findOne({ 'reportApi.type': 'NCC' }, { 'reportApi.period.end': 1 });
		if (!doc) {
			throw new Error('NCC AdProvider를 찾을 수 없습니다.');
		}
		const scheduledExecutionDatePath = dt.clone().add(Math.abs(doc._doc.reportApi.period.end), 'days').format('YYYY/MM/DD');

		// 저장소 경로
		// 	- silvergrey.hdfs.root_dir = /user/gfp-data/silvergrey
		// 	- hdfsFilePath             = /user/gfp-data/silvergrey/source/2025/03/28/ncc/ncc_20250327.tsv
		const hdfsFilePath = path.join(config.silvergrey.hdfs.root_dir, 'source', scheduledExecutionDatePath, 'ncc', fileName);

		// 로컬 경로
		// 	- localFilePath = local_download/monitoring/ncc_20250327.tsv
		const localDirPath = file.mkdirIfNotExist(path.join(config.local_root, 'monitoring'));
		const localFilePath = path.join(localDirPath, fileName);

		// NCC 원본 파일을 로컬로 다운로드
		await _downloadToLocal(hdfsFilePath, localFilePath);

		// 파일 존재 여부 확인
		if (!fs.existsSync(localFilePath)) {
			throw new Error(`파일 다운로드 실패: ${localFilePath}`);
		}

		// 파일 읽어 데이터 구성
		const stats = await new Promise((resolve, reject) => {
			let fileReadStream = null;
			let parser = null;

			// Promise 중복 resolve/reject 방지 플래그 (스트림 이벤트들이 동시 발생할 수 있어 Race Condition 방지용)
			let isResolved = false;

			const cleanup = () => {
				if (parser && _.isFunction(parser.end)) {
					parser.end();
				}
				if (fileReadStream && _.isFunction(fileReadStream.destroy)) {
					fileReadStream.destroy();
				}
			};

			const safeReject = (error) => {
				if (!isResolved) {
					isResolved = true;
					cleanup();
					reject(error);
				}
			};

			const safeResolve = (result) => {
				if (!isResolved) {
					isResolved = true;
					resolve(result);
				}
			};

			try {
				let rowCnt = 0, sumOfImp = 0, sumOfClk = 0, sumOfKrwRevenue = 0, sumOfKrwNetRevenue = 0;

				// ------------ 로컬 파일 읽기 스트림
				logger.debug(`${localFilePath} 원본 파일 읽기..`);
				fileReadStream = fs.createReadStream(localFilePath);
				fileReadStream
					.on('end', () => {
						// logger.debug(`${remoteFilePath} 리모트 파일 읽기 "end" event.`);
					})
					.on('error', function onError(err) {
						logger.error(`${localFilePath} 원본 파일 읽기 "error" event. ${localFilePath} ${err}`);
						safeReject(err);
					});

				// ------------ tsv로 읽기 스트림
				parser = csv.parse({ columns: true, delimiter: '\t' });
				parser
					.on('finish', () => {
						// logger.debug(`${remoteFilePath} 리모트 파일 파싱. "finish" 이벤트.`);
					})
					.on('end', () => {
						// logger.debug(`${remoteFilePath} 리모트 파일 파싱. "end" 이벤트.`);
						safeResolve({ date, rowCnt, sumOfImp, sumOfClk, sumOfKrwRevenue, sumOfKrwNetRevenue });
					})
					.on('error', err => {
						logger.error(`${localFilePath} 원본 파일 파싱. "error" 이벤트. ${err.stack}`);
						safeReject(err);
					})
					.on('data', data => {
						try {
							rowCnt++;
							sumOfImp += +data.imp_cnt;
							sumOfClk += +data.clk_cnt;
							sumOfKrwRevenue += +data.sales_amt;
							sumOfKrwNetRevenue += +data.net_sales_amt;
						} catch (dataError) {
							logger.error(`${localFilePath} 데이터 처리 중 오류: ${dataError.message}`);
							safeReject(dataError);
						}
					});

				fileReadStream.pipe(parser);
			} catch (error) {
				safeReject(error);
			}
		});

		// 내역 설정
		const update = {
			rowCnt: stats.rowCnt,
			sumOfImp: stats.sumOfImp,
			sumOfClk: stats.sumOfClk,
			sumOfKrwRevenue: parseFloat(stats.sumOfKrwRevenue.toFixed(6)),
			sumOfKrwNetRevenue: parseFloat(stats.sumOfKrwNetRevenue.toFixed(6)),
			expiredAt: moment(date).add(1, 'year'), // 데이터 파기 일시. ttl index (1년 후 만료)
			modifiedAt: moment()
		};
		update[alreadyExists ? 'modifiedAt' : 'createdAt'] = moment();

		// 업서트
		await MonitoringReportApiStats.updateOne({ reportApiType: 'NCC', date: stats.date }, update, { upsert: true });

		// !!!!!!!!!! 2025.09월에 수익쉐어 리포트가 정식 오픈되면 삭제해야 함.
		await MonitoringReportApiStatsCms.updateOne({ reportApiType: 'NCC', date: stats.date }, update, { upsert: true });

		// NCC 원본 파일 로컬 삭제
		_deleteLocalFile(localFilePath);
	} else {
		logger.debug(`monitoring.service.makeNccReportApiStats() ncc 리포트 연동 스케줄이 아직 안 만들어졌거나 성공이 아님. schedule:${JSON.stringify(schedule, null, 2)}`);
	}
};


/**
 * _downloadToLocal : AP 원본 파일 로컬 다운로드
 * 	- HDFS_C3 인 경우, cli 로 다운로드
 * 	- HDFS 인 경우, rest api 로 다운로드
 *
 * @param {String} hdfsFilePath
 * @param {String} localFilePath
 */
const _downloadToLocal = async (hdfsFilePath, localFilePath) => {
	logger.debug(`[monitoring.service :: _downloadToLocal] AP 원본 파일 다운로드 시작 ( api_result=${config.report_api.ncc.api_result} )`);

	await c3HdfsCli.download(hdfsFilePath, localFilePath);

	logger.debug(`[monitoring.service :: _downloadToLocal] AP 원본 파일 다운로드 종료`);
};


/**
 * _deleteLocalFile : 로컬 파일 삭제
 *
 * @param {String} localFilePath
 * 		- local_download/monitoring/ncc_20220106.tsv
 * 		- local_download/report/monitoring_zircon_b/monitoring_zircon_b_{{yyyymmdd}}.csv.gz
 */
const _deleteLocalFile = localFilePath => {
	logger.debug(`[monitoring.service :: _deleteLocalFile] localFilePath=${localFilePath} 삭제`);

	// 파일 삭제
	if (fs.existsSync(localFilePath)) {
		fs.unlinkSync(localFilePath);
	}
};


/**
 * 모니터링 지르콘B 알림 메일 발송
 */
module.exports.sendMailMonitoringZirconB = async date => {
	logger.debug(`[monitoring.service :: sendMailMonitoringZirconB] date= ${date}`);

	// 0. HDFS & LOCAL 모니터링 파일 경로 정보 셋팅
	const { localFile, hdfsFile } = _getFilePathInfo(date);

	// 1. 파일 다운로드 ( HDFS -> LOCAL )
	await _download(hdfsFile, localFile);

	// 2. 모니터링 지르콘B 알림 메일 발송 ( 제목, 내용, 첨부 파일 )
	await _sendMail(date, localFile);

	// 3. LOCAL 파일 삭제
	_deleteLocalFile(localFile);
};


/**
 * _getFilePathInfo : HDFS & LOCAL 모니터링 파일 경로 정보
 *
 * @param {String} date YYYYMMDD
 */
const _getFilePathInfo = date => {
	const dt = moment(date, 'YYYYMMDD');

	// fileName = monitoring_zircon_b_{{yyyymmdd}}.csv.gz
	const fileName = `monitoring_zircon_b_${date}.csv.gz`;


	/* hdfs 파일 정보 */
	// hdfsDir = /user/gfp-data/monitoring/zircon/b/{{YYYY}}/{{MM}}/{{DD}}
	const hdfsDir = path.join(config.report.monitoring_zircon_b.hdfs.root_dir, '{{YYYY}}', '{{MM}}', '{{DD}}')
		.replace(/{{YYYY}}/g, dt.format('YYYY')).replace(/{{MM}}/g, dt.format('MM')).replace(/{{DD}}/g, dt.format('DD'));

	// hdfsFile = /user/gfp-data/monitoring/zircon/b/{{YYYY}}/{{MM}}/{{DD}}/monitoring_zircon_b_{{yyyymmdd}}.csv.gz
	const hdfsFile = path.join(hdfsDir, fileName);


	/* local 파일 정보 */
	// localDir = local_download/report/monitoring_zircon_b/
	const localDir = file.mkdirIfNotExist(path.join(config.local_root, config.report.path, config.report.monitoring_zircon_b.path));

	// localFile = local_download/report/monitoring_zircon_b/monitoring_zircon_b_{{yyyymmdd}}.csv.gz
	const localFile = path.join(localDir, fileName);


	logger.debug(`[monitoring.service :: _getFilePathInfo] HDFS & LOCAL 모니터링 파일 경로 정보 \n- hdfsFile= ${hdfsFile}\n- localFile= ${localFile}\n`);

	return { localFile, hdfsFile };
};


/**
 * _download : 파일 로컬 다운로드
 *
 * @param {String} hdfsFilePath
 * @param {String} localFilePath
 */
const _download = async (hdfsFilePath, localFilePath) => {
	logger.debug(`[monitoring.service :: _download] HDFS 파일 다운로드 시작 ( hdfsFilePath= ${hdfsFilePath}, localFilePath= ${localFilePath} )`)

	if (!await c3HdfsApi.exists(hdfsFilePath)) {
		throw new BusinessError({ message: `[monitoring.service :: _download] HDFS 에 파일이 존재 하지 않음 (hdfsFilePath= ${hdfsFilePath})` });
	}

	// hdfsFilePath  = /user/gfp-data/monitoring/zircon/b/2024/05/01/monitoring_zircon_b_20240501.csv.gz
	// localFilePath = local_download/report/monitoring_zircon_b/monitoring_zircon_b_20240501.csv.gz
	await c3HdfsCli.download(hdfsFilePath, localFilePath, true, false);

	logger.debug('[monitoring.service :: _download] HDFS 파일 다운로드 완료');
};


/**
 * _sendMail : 모니터링 지르콘B 알림 메일 발송
 *
 * @param {String} date
 * @param {String} localFilePath
 */
const _sendMail = async (date, localFilePath) => {
	logger.debug(`[monitoring.service :: _sendMail] 모니터링 지르콘B 알림 메일 발송 ( date= ${date}, localFilePath= ${localFilePath} )`)

	// GFP DATA 전체 알림 수신자
	const env = await DataEnvironments.findOne({ name: 'gfp-data-alarm-receiver' });

	const to = env.value;
	const subject = `모니터링 지르콘B 알림 메일 - ${date}`;

	// file = local_download/report/monitoring_zircon_b/monitoring_zircon_b_yyyymmdd.csv.gz
	// { root: '', dir: 'local_download/report/monitoring_zircon_b',
	// 	 base: 'monitoring_zircon_b_yyyymmdd.csv.gz', ext: '.gz', name: 'monitoring_zircon_b_yyyymmdd.csv' }
	const fileInfo = path.parse(localFilePath);

	await mailer.sendMail({
		to, subject,
		attachments: [{
			filename: fileInfo.base,
			content: fs.createReadStream(localFilePath)
		}]
	});
};
