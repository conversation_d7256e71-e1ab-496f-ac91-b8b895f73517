'use strict';

import { createObjectCsvWriter } from "csv-writer";
import fs from 'fs';
import _ from 'lodash';
import path from 'path';
import { BusinessError } from '../../common/error';

import config from '../../config/config';

import { DataEnvironments } from '../../models/data/data-environments.schema';
import { MonitoringProfitRate } from "../../models/data/monitoring-profit-rate.schema";
import { MonitoringSalesRate } from "../../models/data/monitoring-sales-rate.schema";
import * as file from '../../utils/file.util';
import * as logger from '../../utils/logger.util';

import * as mailer from '../../utils/mail.util';

const SALES_RATE = 'sales_rate';
const PROFIT_RATE = 'profit_rate';

const DIMENSIONS = ['date', 'publisherName', 'publisherId', 'adProviderName', 'adProviderId'];
const METRICS = {
	[SALES_RATE]: {
		total: ['krwGross', 'krwSales', 'krwSalesRate', 'krwSalesRateDeviation', 'usdGross', 'usdSales', 'usdSalesRate', 'usdSalesRateDeviation'],
		monitor: ['krwSalesRateDeviation', 'usdSalesRateDeviation']
	},
	[PROFIT_RATE]: {
		total: ['krwSales', 'krwProfit', 'krwProfitRate', 'krwProfitRateDeviation', 'usdSales', 'usdProfit', 'usdProfitRate', 'usdProfitRateDeviation'],
		monitor: ['krwProfitRateDeviation', 'usdProfitRateDeviation']
	}
};


/**
 * 모니터링 수익률 모니터링 및 메일 발송
 */
module.exports.monitorRevenue = async (startDate, endDate) => {
	logger.debug(`[monitorRevenue.service :: monitorRevenue] startDate= ${startDate}, endDate= ${endDate}`);

	// 1. 수익률 모니터링 대상 데이터 조회
	const totalData = await _getMonitorStats(startDate, endDate);
	// logger.debug(`[monitorRevenue.service :: _getMonitorStats] ${JSON.stringify(totalData, null, 2)}`);
	// { sales_rate: [{ date: ~, publisherId: ~, adProviderId: ~, krwGross: ~, }, {}, .. ], profit_rate: [{}, {}, ..] }

	// 2. 광고공급자/매체 수익률 이상치 확인
	const abnormalData = await _checkAbnormalStats(totalData);
	// logger.debug(`[monitorRevenue.service :: _checkAbnormalStats] ${JSON.stringify(abnormalData, null, 2)}`);
	// { sales_rate: [{ date: ~, publisherId: ~, adProviderId: ~, krwSales: ~, }, {}, .. ], profit_rate: [{}, {}, ..] }

	// 3. 이상치가 있다면 파일 생성 및 하나의 메일에 첨부하여 발송
	if (!_.isEmpty(abnormalData)) {
		// 3-1. 모든 데이터를 대상으로 파일 생성
		// /home1/irteam/deploy/local_download/report/monitoring_revenue
		const fileDirPath = path.join(config.local_root, config.report.path, `${config.report.monitoring_revenue.path}_${startDate}_${endDate}`);
		await _generateFile(startDate, endDate, fileDirPath, totalData);

		// 3-2. 이상치 데이터를 대상으로 메일의 내용이 될 HTML 생성
		const html = await _generateMailHtml(startDate, endDate, abnormalData);

		// 3-3. 메일 발송
		await _sendMailForAbnormal(startDate, endDate, fileDirPath, html);

		// 3-4. 생성한 LOCAL 파일 삭제
		await file.deleteDirectoryRecursively(fileDirPath, true);
	}
}

/**
 * 수익률 모니터링 대상 데이터 조회
 *
 * @param startDate
 * @param endDate
 * @returns {Promise<{}>}
 * @private
 */
const _getMonitorStats = async (startDate, endDate) => {
	logger.debug(`[monitorRevenue.service :: _getMonitorStats] startDate= ${startDate} endDate= ${endDate}`);

	// 모니터링 대상 시간 필터
	const dateMatchSpec = { date: { $gte: startDate, $lte: endDate } };

	// 광고공급자, 매체 이름 추가하기 위한 lookup
	const pubNameLookupSpec = {
		from: 'SyncPublishers',
		foreignField: '_id',
		localField: 'publisher_id',
		as: 'publisher'
	};
	const apNameLookupSpec = {
		from: 'SyncAdProviders',
		foreignField: '_id',
		localField: 'adProvider_id',
		as: 'adProvider'
	};

	// 최종 파일 컬럼을 위한 project
	const dimensionProjectSpec = {
		_id: 0,
		date: 1,
		publisherId: { $toString: `$publisher_id` },
		publisherName: { $ifNull: [{ $arrayElemAt: ['$publisher.name', 0] }, "-"] },
		adProviderId: { $toString: `$adProvider_id` },
		adProviderName: { $ifNull: [{ $arrayElemAt: ['$adProvider.name', 0] }, "-"] }
	}

	/*{
		sales_rate: {krwGross: 1, krwSales: 1, krwSalesRate: 1, krwSalesRateDeviation: 1, ... },
		profit_rate: {krwSales: 1, krwProfit: 1, krwProfitRate: 1, krwProfitRateDeviation: 1, ... },
	}*/
	const metricProjectSpec = {
		[SALES_RATE]: METRICS[SALES_RATE].total.reduce((acc, met) => {
			acc[met] = 1;
			return acc;
		}, {}),
		[PROFIT_RATE]: METRICS[PROFIT_RATE].total.reduce((acc, met) => {
			acc[met] = 1;
			return acc;
		}, {})
	};


	const salesRateStats = await MonitoringSalesRate.aggregate()
		.allowDiskUse(true)
		.match(dateMatchSpec)
		.lookup(pubNameLookupSpec)
		.lookup(apNameLookupSpec)
		.project(Object.assign(dimensionProjectSpec, metricProjectSpec[SALES_RATE]))
		.sort({ date: 1, adProviderName: 1, publisherName: 1 })
		.exec();

	const profitRateStats = await MonitoringProfitRate.aggregate()
		.allowDiskUse(true)
		.match(dateMatchSpec)
		.lookup(pubNameLookupSpec)
		.lookup(apNameLookupSpec)
		.project(Object.assign(dimensionProjectSpec, metricProjectSpec[PROFIT_RATE]))
		.sort({ date: 1, publisherName: 1, adProviderName: 1 })
		.exec();

	const totalData = {};
	if (salesRateStats.length > 0) totalData[SALES_RATE] = salesRateStats;
	if (profitRateStats.length > 0) totalData[PROFIT_RATE] = profitRateStats;

	return totalData;
}

/**
 * DB 로부터 조회한 모니터링 대상 데이터에서 이상치 존재여부 확인 및 필터링
 * @param totalData
 * @returns {Promise<{}>}
 * @private
 */
const _checkAbnormalStats = async (totalData) => {
	logger.debug(`[monitorRevenue.service :: _checkAbnormalStats] 광고공급자/매체 수익률 이상치 확인 시작`);
	const abnormalData = {};

	for (const [type, data] of Object.entries(totalData)) {
		logger.debug(`[monitorRevenue.service :: _checkAbnormalStats] ${type} - ${METRICS[type].monitor}`);

		const abnormalRows = [];
		data.forEach(row => {
			// 전날대비 수익률의 변화가 -5% 이하 또는 5% 이상이면 이상치로 판단 (krw, usd 중 하나라도 해당되면 이상치)
			METRICS[type].monitor.some(metric => {
				if (row[metric] < -0.05 || row[metric] > 0.05) {
					abnormalRows.push(row);
					return true;
				}
			});
		});

		if (abnormalRows.length > 0) abnormalData[type] = abnormalRows;
	}

	return abnormalData;
}

/**
 * 해당 기간의 데이터가 모두 포함된 csv 파일 생성
 *
 * @param startDate
 * @param endDate
 * @param fileDirPath
 * @param totalData
 * @private
 */
const _generateFile = async (startDate, endDate, fileDirPath, totalData) => {
	logger.debug(`[monitorRevenue.service :: _generateFile] 모니터링 대상 전체 데이터 파일 생성`);

	await file.mkdirIfNotExist(fileDirPath);

	for (const [type, data] of Object.entries(totalData)) {
		// /home1/irteam/local_download/report/monitoring_revenue/Monitoring_sales_rate_{{startDate}}_{{endDate}}.csv
		const filePath = path.join(fileDirPath, `Monitoring_${type}_${startDate}_${endDate}.csv`);

		// csv 헤더 작성
		const header = DIMENSIONS.concat(METRICS[type].total);
		const csvWriter = createObjectCsvWriter({
			header: header.map(data => {
				return { id: data, title: data };
			}),
			path: filePath
		});

		// csv 파일 생성
		await csvWriter.writeRecords(data);

		// BOM 추가
		const fileContents = fs.readFileSync(filePath);
		fs.writeFileSync(filePath, "\ufeff" + fileContents);

		logger.debug(`[monitorRevenue.service :: _generateFile] ${filePath} 생성 완료`);
	}
}


/**
 *
 * @param startDate
 * @param endDate
 * @param abnormalData
 * @returns {string}
 * @private
 */
const _generateMailHtml = (startDate, endDate, abnormalData) => {
	logger.debug(`[monitorRevenue.service :: _generateMailHtml] 이상치 데이터로 HTML 생성`);
	let html = '';

	for (const [type, data] of Object.entries(abnormalData)) {
		logger.debug(`[monitorRevenue.service :: _generateMailHtml] ${type} 이상치 발견됨`);

		// 테이블 헤더
		const header = DIMENSIONS.concat(METRICS[type].total);

		// html 테이블
		html += `<h3> ${type === SALES_RATE ? '광고공급자 수익률' : '매체 수익률'}에서 ${data.length} 건의 이상치가 발견되었습니다. </h3><br>` +
			'<table style="border:1px solid lightgrey">' +
			`<thead style="background: lightgrey">${header.map(h => `<td>${h} </td>`).join('')}</thead>` +
			'<tbody>';

		for (const row of data) {
			html += '<tr>';
			for (const idx in header) {
				html += `<td> ${row[header[idx]]} </td>`;
			}
			html += '</tr>';
		}
		html += '</tbody></table>';

		html += '<br>'
	}

	return html;
}


/**
 * _sendMailForAbnormal : 수익률 모니터링 알림 메일 발송
 *
 * @param startDate
 * @param endDate
 * @param fileDirPath
 * @param html
 */
const _sendMailForAbnormal = async (startDate, endDate, fileDirPath, html) => {
	logger.debug(`[monitorRevenue.service :: _sendMailForAbnormal] 수익률 모니터링 알림 메일 발송`)

	const env = await DataEnvironments.findOne({ name: 'monitoring-revenue-alarm-receivers' });

	// <EMAIL>, <EMAIL>
	const to = Array.isArray(env.value) ? Array.from(env.value).join(';') : env.value
	const subject = `수익률 모니터링 알림 메일 ( ${startDate} ~ ${endDate} )`;
	const abnormalDataFiles = await file.readDirRecursively(fileDirPath, false);
	if (_.isEmpty(abnormalDataFiles)) {
		throw new BusinessError({ message: `[monitorRevenue.service :: _sendMailForAbnormal] 첨부할 파일이 존재하지 않음 (fileDirPath= ${fileDirPath})` });
	}
	const attachments = abnormalDataFiles.map(fileInfo => {
		return {
			filename: path.parse(fileInfo.path).base,
			content: fs.createReadStream(fileInfo.path)
		};
	})

	await mailer.sendMail({
		to, subject, html, attachments
	});
};
