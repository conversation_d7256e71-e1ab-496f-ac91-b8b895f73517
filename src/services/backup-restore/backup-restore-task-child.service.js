import child_process from 'child_process';
import fs from 'fs';
import _ from 'lodash';
import path from 'path';

import * as c3HdfsApi from '../../c3/hdfs-api';
import * as c3HdfsCli from '../../c3/hdfs-cli';
import { BusinessError } from '../../common/error';
import cLogger from '../../common/logger';

import config from '../../config/config';
import * as nubesCli from '../../nubes/nubes-cli';
import { sleep } from '../../utils/common.util';

import * as file from '../../utils/file.util';
import * as gzipUtil from '../../utils/gzip.util';
import extendedLogger from '../../utils/logger-ext.util';
import * as tarUtil from '../../utils/tar.util';

import {
	BACKUP_SUCCESS,
	BUCKET,
	deleteLocalFiles,
	GATEWAY_ADDRESS,
	JOB_BACK_UP,
	RESTORE_SUCCESS
} from "./backup-restore.service";

const log = extendedLogger('backup_restore');
let logPrefix = __filename.split('/').slice(-1)[0].slice(0, -3);

let otherJobFailed = false;

/**
 * child_process 작업 : 조건에 따라 Job 을 병렬 처리
 *  1. 수행하려는 작업의 종류: backup/restore
 *  2. 작업의 방식
 *   2-1. backup: dst file 의 아카이빙(및 압축) 여부에 따라 다름
 *   2-2. restore: src file 의 아카이빙(및 압축) 여부에 따라 다름
 *
 * @private
 * @param jobType
 * @param srcFileInfo List[[filePath1, fileSize1], [filePath2, fileSize2], ..]
 * @param localFile
 * @param dstFile
 * @param jobIdx
 */
process.on('message', async ({ execOption, jobType, srcFileInfo, localFile, dstFile, idx }) => {
	cLogger(config);
	logPrefix = __filename.split('/').slice(-1)[0].slice(0, -3) + `-childProc[${idx}]`;

	const srcFile = srcFileInfo[0];
	const srcFileSize = srcFileInfo[1];

	log.info(`[${logPrefix}] 프로세스 (pid:${process.pid}) 시작 ${srcFile} `);

	const tarExt = '.tar';
	const gzipExt = '.gz';
	const enable_archive = execOption.ENABLE_ARCHIVE;
	const enable_compressions = execOption.ENABLE_COMPRESSION;

	let errorMsg;

	if (jobType === JOB_BACK_UP) {
		// 백업 작업
		dstFile += (enable_archive ? tarExt + (enable_compressions ? gzipExt : '') : '');

		if (enable_archive) {
			// 아카이빙 및 압축을 하는 경우 로컬로 다운 + 파일 변환이 필요함
			const localCompressedFile = localFile + (enable_archive ? tarExt + (enable_compressions ? gzipExt : '') : '');
			try {
				// HDFS 파일을 압축 단위(디렉토리)별로 다운로드 ( HDFS -> LOCAL )
				await _downloadFromHdfs(srcFile, localFile);
				_checkProgress(jobType, `${localFile} 로컬로 다운로드`);

				// 다운로드 파일 압축 (HDFS 원본 파일)
				await _compressLocalDir(localFile, enable_archive, enable_compressions);
				_checkProgress(jobType, `${localCompressedFile} 아카이빙 및 압축`);

				// 로컬에 다운로드 받은 파일 삭제
				await deleteLocalFiles(localFile);

				// 압축 파일 Nubes 로 pupload ( LOCAL -> Nubes )
				await _uploadToNubes(localCompressedFile, dstFile);
				_checkProgress(jobType, `${dstFile} Nubes 백업`);

				// 로컬 압축 파일 삭제 (.gzip 압축은 원본을 남기지 않으므로 압축 여부에 따라 .tar.gz 이거나 .tar 중 하나의 파일)
				await deleteLocalFiles(localCompressedFile);
			} catch (err) {
				// 에러 발생 시, 로컬에 생성된 파일 삭제
				// 어느 단계에서 실패했을지 모르므로 .tar 및 최종 백업 확장자 파일 모두 삭제
				await deleteLocalFiles(localFile);
				await deleteLocalFiles(`${localFile}${tarExt}`);
				await deleteLocalFiles(localCompressedFile);

				errorMsg = err.message || err;
			}
		} else {
			// 아카이빙을 하지 않는 경우 로컬로 다운 받는 과정을 하지 않음
			try {
				await _uploadToNubes(srcFile, dstFile, srcFileSize, false);
				_checkProgress(jobType, `${dstFile} HDFS to Nubes 직접 백업`);
			} catch (err) {

				errorMsg = err.message || err;
			}
		}
	} else {
		// 복구 작업
		dstFile = dstFile.replace(gzipExt, '').replace(tarExt, '');

		const isGzip = path.extname(srcFile) === gzipExt;
		const isTar = path.extname(srcFile.replace(gzipExt, '')) === tarExt;
		if (isGzip || isTar) {
			// 아카이빙 및 압축이 되어 있는 파일은 로컬 다운 + 파일 변환이 필요함
			// 확장자(tar, parquet)가 달라도 각 파일의 복구 작업을 동시에 실행 (tar, tar.gz: 로컬로 다운, 그 외: HDFS 로 직접)
			// 이 때, 로컬로 다운 받는 작업은 subPath/dtPath 단위의 nubescli.dirDownload 로 가능하므로 병렬 실행할 필요가 없어 밖에서 실행
			const localDecompressedFile = localFile.replace(gzipExt, '').replace(tarExt, '');
			try {
				// Nubes 파일을 압축 단위(디렉토리)별로 다운로드 ( Nubes -> LOCAL )
				await _downloadFromNubes(srcFile, localFile, isTar, isGzip);
				_checkProgress(jobType, `${dstFile} 로컬로 다운로드`);

				// 다운로드 파일 아카이빙 및 압축 해제 (dir-download 로 다운받은 디렉토리 내의 로컬 파일들)
				await _decompressLocalDir(localFile);
				_checkProgress(jobType, `${localDecompressedFile} 아카이빙 및 압축 해제`);

				// 로컬에 다운로드 받은 파일 삭제 (.tar 해제는 원본을 남기고, .gz 해제는 그렇지 않으므로 isTar 라면 항상 .tar 파일)
				// .gz 압축만 된 파일인 경우가 있을까 싶지만.. 그렇다고 하더라도 해제하면 .gz 파일은 사라짐
				await deleteLocalFiles(isTar ? localFile.replace(gzipExt, '') : localDecompressedFile);

				// 아카이빙 및 압축 해제 파일 HDFS 로 업로드 ( LOCAL -> HDFS )
				await _uploadToHdfs(localDecompressedFile, dstFile);
				_checkProgress(jobType, `${dstFile} HDFS 로 복구`);

				// 로컬 압축 해제 파일 삭제
				await deleteLocalFiles(localDecompressedFile);
			} catch (err) {
				// 에러 발생 시, 로컬에 생성된 파일 삭제
				// 어느 단계에서 실패했을지 모르므로 .tar 및 .tar.gz 모두 삭제
				await deleteLocalFiles(localFile);
				await deleteLocalFiles(localFile.replace(gzipExt, ''));
				await deleteLocalFiles(localDecompressedFile);

				errorMsg = err.message || err;
			}
		} else {
			// 아카이빙이 되어 있지 않은 파일은 HDFS 로 직접 복구
			try {
				await _downloadFromNubes(srcFile, dstFile, isTar, isGzip);
				_checkProgress(jobType, `${dstFile} Nubes to HDFS 직접 복구`);
			} catch (err) {

				errorMsg = err.message || err;
			}
		}
	}

	if (errorMsg) {
		// throw 또는 reject 된 내용이 Error 객체면 err.message 가 존재하지만, 일반 문자열이면 message 필드가 없음(그 자체가 msg 인 경우)
		process.send({ op: 'FAILURE', msg: errorMsg });
	} else {
		process.send({ op: 'COMPLETE', msg: `완료` });
	}
});


/**
 * _uploadToHdfs : LOCAL 파일을 HDFS 로 업로드
 * 아카이빙 및 압축 된 백업 파일인 경우에만 LOCAL 에서 업로드 하기 위해 사용
 *
 * @param localFile
 * @param hdfsFile
 * @private
 */
const _uploadToHdfs = async (localFile, hdfsFile) => {
	log.debug(`[${logPrefix} :: _uploadToHdfs] HDFS 에 업로드 ( localFile= ${localFile} )`);

	try {
		await c3HdfsApi.mkdir(path.dirname(hdfsFile));

		// 파일 복사 ( 이미 존재 하는 경우, overwrite )
		if (await c3HdfsApi.exists(hdfsFile)) {
			// hdfs dfs -put <srcDir> <dstDir> 시, <dstDir> 가 존재하지 않는다면 생성되지만,
			// 이미 존재한다면 <dstDir> 까지 경로로 생각하여 <dstDir> 하위에 <dstDir> 이 재생성되므로 이를 방지
			await c3HdfsCli.upload(localFile, path.dirname(hdfsFile));
			log.debug(`[${logPrefix} :: _uploadToHdfs] HDFS 에 이미 존재하여 덮어씀 ( hdfsFile= ${hdfsFile} )`);
		} else {
			await c3HdfsCli.upload(localFile, hdfsFile);
		}

		// 파일 크기 검증 (HDFS vs LOCAL)
		await _validateFileSize(undefined, hdfsFile, localFile);
	} catch (err) {
		throw err;
	}
};


/**
 * _downloadFromHdfs : HDFS 파일을 LOCAL 로 다운로드
 * 아카이빙 및 압축을 설정한 경우에만 LOCAL 로 다운로드 받기 위해 사용
 *
 * @param hdfsFile
 * @param localFile
 * @private
 */
const _downloadFromHdfs = async (hdfsFile, localFile) => {
	log.debug(`[${logPrefix} :: _downloadFromHdfs] HDFS 로부터 다운로드 ( hdfsFile= ${hdfsFile} )`);

	try {
		file.mkdirIfNotExist(path.dirname(localFile));

		await c3HdfsCli.download(hdfsFile, localFile);

		// 파일 크기 검증 (HDFS vs LOCAL)
		await _validateFileSize(undefined, hdfsFile, localFile);
	} catch (err) {
		throw (err);
	}
};


/**
 * _uploadToNubes : Nubes 로 파일 업로드 (Nubes 에는 오브젝트 업로드 시 필요한 경로가 없으면 자동으로 경로를 생성)
 * 1. HDFS  -> Nubes
 * 2. LOCAL -> Nubes
 *
 * @param srcFile
 * @param nubesFile
 * @param fileSize
 * @param pupload
 * @private
 */
const _uploadToNubes = async (srcFile, nubesFile, fileSize, pupload = true) => {
	log.debug(`[${logPrefix} :: _uploadToNubes] Nubes 에 업로드 ( srcFile= ${srcFile} )`);

	try {
		// 파일 복사 ( 이미 존재 하는 경우, overwrite )
		if (pupload) {
			// 압축 파일인 경우 parallel upload 로 업로드
			await nubesCli.pupload(GATEWAY_ADDRESS, BUCKET, srcFile, nubesFile);
		} else {
			if (path.basename(nubesFile) === BACKUP_SUCCESS) {
				// _BACKUP_SUCCESS 파일은 로컬 -> nubes 로 upload (stream 방식 x)
				await nubesCli.upload(GATEWAY_ADDRESS, BUCKET, srcFile, nubesFile, { isStream: false, fileSize });
			} else {
				// HDFS 에서 Nubes 로 바로 업로드 (stream 방식)
				const stdout = await c3HdfsCli.cat(srcFile);
				if (fileSize !== Buffer.byteLength(stdout)) {
					throw new BusinessError({ message: `stdin 을 사용한 nubes upload 시 fileSize 와 대상 파일의 Buffer.byteLength 같아야 합니다. fileSize= ${fileSize}, Buffer.byteLength= ${Buffer.byteLength(stdout)}` })
				}
				await nubesCli.upload(GATEWAY_ADDRESS, BUCKET, '-', nubesFile, { isStream: true, fileSize, fileBuffer: stdout });
			}
		}

		// 파일 크기 검증
		if (pupload || _.includes([BACKUP_SUCCESS, RESTORE_SUCCESS], path.basename(srcFile))) {
			// Nubes vs LOCAL (위 두 파일은 로컬에 생성해서 업로드)
			await _validateFileSize(nubesFile, undefined, srcFile);
		} else {
			// Nubes vs HDFS
			await _validateFileSize(nubesFile, srcFile, undefined);
		}
	} catch (err) {
		throw err;
	}
};


/**
 * _downloadFromNubes : Nubes 파일을 다운로드
 * 1. Nubes -> HDFS
 * 2. Nubes -> LOCAL
 *
 * @param nubesFile
 * @param dstFile
 * @param isTar
 * @param isGzip
 * @private
 */
const _downloadFromNubes = async (nubesFile, dstFile, isTar = false, isGzip = false) => {
	log.debug(`[${logPrefix} :: _downloadFromNubes] Nubes 로부터 다운로드 (nubesFile= ${nubesFile} )`);

	try {
		// Nubes 파일 존재 여부 확인. 없으면 reject 하여 catch 됨
		await nubesCli.status(GATEWAY_ADDRESS, BUCKET, nubesFile);

		// 파일 복사 ( 이미 존재 하는 경우, overwrite )
		if (isTar || isGzip) {
			// 아카이빙 및 압축 된 파일이라면 LOCAL 로 다운로드
			file.mkdirIfNotExist(path.dirname(dstFile));
			// dirDownload 가 성능이 더 좋을 것으로 기대했으나 jobs 를 조절해 보아도 크게 달라지지 않음
			// 만약 사용할 것이라면, directory 의 size 는 sum(하위 파일들 size) 으로 조회되지 않으므로 하위의 파일들에 대해 각각의 파일 크기 검증 (Nubes vs LOCAL) 은 이 함수 밖에서 실행해야 함
			// await nubesCli.dirDownload(GATEWAY_ADDRESS, BUCKET, nubesFile, dstFile, DIR_DOWNLOAD_WRITE_TYPE.UPDATE, 300);

			await nubesCli.download(GATEWAY_ADDRESS, BUCKET, nubesFile, dstFile);

			await _validateFileSize(nubesFile, undefined, dstFile);
		} else {
			// 아카이빙 및 압축 되지 않은 파일이라면 HDFS 로 바로 업로드
			const stdout = await nubesCli.download(GATEWAY_ADDRESS, BUCKET, nubesFile, '-', true);
			await c3HdfsCli.upload('-', dstFile, stdout, true);

			// 파일 크기 검증 (Nubes vs HDFS)
			await _validateFileSize(nubesFile, dstFile, undefined);
		}
	} catch (err) {
		throw err;
	}
};


/**
 * 파일 크기 검증 (LOCAL 과의 비교는 백업/복구 하려는 데이터가 아카이빙 된 경우임)
 * 디렉토리의 size 는 하위의 모든 파일의 합으로 조회되지 않아 내부 파일을 직접 하나씩 비교해야 함.
 * 그에 따른 경우의 수 =>
 *
 * 1. Nubes -> LOCAL : 아카이빙 및 압축 하기 위해 디렉토리 단위로 다운로드. 디렉토리 하위의 아카이빙 된 파일들의 경로를 알고 있어 파일 단위 비교 가능
 * 2. LOCAL -> Nubes : 아카이빙 및 압축된 파일을 병렬처리. 파일 단위 비교 가능
 * 3. HDFS  -> LOCAL : * 아카이빙 및 압축 하기 위해 디렉토리 단위로 다운로드. 파일 단위 비교를 위해 readDir 해야함
 * 4. LOCAL -> HDFS  : * 아카이빙 및 압축 해제 후, 디렉토리 단위로 업로드. 파일 단위 비교를 위해 readDir 해야함
 * 5. Nubes -> HDFS  : stdin/stdout 을 사용해 직접 업로드. 파일 단위 비교 가능
 * 6. HDFS  -> Nubes : stdin/stdout 을 사용해 직접 업로드. 파일 단위 비교 가능
 *
 * @param nubesFile
 * @param hdfsFile
 * @param localFile
 * @private
 */
const _validateFileSize = async (nubesFile, hdfsFile, localFile) => {
	log.debug(`[${logPrefix} :: _validateFileSize] 파일 크기 검증을 통한 정상 업로드/다운로드 확인`);
	let nubesFileSize;
	let hdfsFileSize;
	let localFileSize;

	try {
		// validate 확정을 위한 read/write 완료 대기
		await sleep(3000);

		if (nubesFile) {
			nubesFileSize = parseInt((await nubesCli.status(GATEWAY_ADDRESS, BUCKET, nubesFile))['X-Object-Size']);
		}
		// hdfs 와 local 파일의 경우 디렉토리 하위의 파일을 각각 비교해야 하는 경우가 있음
		if (hdfsFile) {
			const hdfsFiles = (await c3HdfsApi.readDir(hdfsFile, true)).filter(f => f.type !== 'DIRECTORY');
			if (hdfsFiles.length === 1) {
				hdfsFileSize = parseInt(hdfsFiles[0]['length']);
			} else {
				hdfsFileSize = hdfsFiles.map((info) => {
					if (info['type'] === 'FILE') {
						return [info['path'], parseInt(info['length'])];
					} else {
						return null;
					}
				}).filter(e => e).sort((a, b) => a[0].localeCompare(b[0]));		// local readDir 와의 순서 보장을 위한 정렬
			}
		}
		if (localFile) {
			const localFiles = await file.readDirRecursively(localFile, false);
			if (localFiles.length === 1) {
				localFileSize = parseInt(localFiles[0].size);
			} else {
				localFileSize = localFiles
					.map(info => [info['path'], parseInt(info['size'])])
					.filter(e => e)
					.sort((a, b) => a[0].localeCompare(b[0]));	// hdfs readDir 와의 순서 보장을 위한 정렬
			}
		}
	} catch (err) {
		log.error(`[${logPrefix} :: _validateFileSize] 파일 크기 검증에 실패했습니다.\nnubesFile= ${nubesFile}\nhdfsFile= ${hdfsFile}\nlocalFile= ${localFile}\n${err}`);
		throw (err);
	}

	if (nubesFile && hdfsFile && (nubesFileSize !== hdfsFileSize)) {
		throw new BusinessError({ message: `Nubes vs HDFS 파일 사이즈 불일치 ( nubesFile= ${nubesFile}, nubesFileSize= ${nubesFileSize}, hdfsFile= ${hdfsFile}, hdfsFileSize= ${hdfsFileSize} )` });
	}
	if (nubesFile && localFile && (nubesFileSize !== localFileSize)) {
		throw new BusinessError({ message: `Nubes vs 로컬 파일 사이즈 불일치 ( nubesFile= ${nubesFile}, nubesFileSize= ${nubesFileSize}, localFile= ${localFile}, localFileSize= ${localFileSize} )` });
	}
	if (hdfsFile && localFile) {
		if (Array.isArray(hdfsFileSize) && Array.isArray(localFileSize)) {
			// 아카이빙 및 압축을 수행 또는 해제하려는 디렉토리 하위의 각 파일에 대해 파일 크기 검증 (HDFS vs LOCAL)
			await Promise.all(hdfsFileSize.map(async (hdfsPathInfo, idx) => {
				if (!localFileSize[idx]) {
					throw new BusinessError({ message: `로컬 파일 정보가 없습니다. hdfsFile= ${hdfsPathInfo[0]}` });
				}
				if (hdfsPathInfo[1] !== localFileSize[idx][1]) {
					throw new BusinessError({
						message: `HDFS vs 로컬 경로 내 파일 사이즈 불일치 ( hdfsFile= ${hdfsPathInfo[0]}, hdfsFileSize= ${hdfsPathInfo[1]}, localFile= ${localFileSize[idx][0]}, localFileSize= ${localFileSize[idx][1]} )`
					});
				}

				// 명시적 리턴이 없어도 자동으로 Promise.resolve()로 처리됨
			}));
		} else if (hdfsFileSize !== localFileSize) {
			throw new BusinessError({ message: `HDFS vs 로컬 파일 사이즈 불일치 ( hdfsFile= ${hdfsFile}, hdfsFileSize= ${hdfsFileSize}, localFile= ${localFile}, localFileSize= ${localFileSize} )` });
		}
	}
};


/**
 * _compressLocalDir : 다운받은 디렉토리롤 대상으로 아카이브/압축
 *  Environment 의 관련 파라미터 설정에 따라 .tar, .tar.gz 파일로 변환
 *
 * @param localDownloadDir
 * @param enable_archive
 * @param enable_compressions
 * @private
 */
const _compressLocalDir = async (localDownloadDir, enable_archive, enable_compressions) => {
	if (enable_archive && fs.existsSync(localDownloadDir)) {
		// tar 아카이브
		log.debug(`[${logPrefix} :: _compressLocalDir] .tar 아카이브 파일 생성 ( localFilePath= ${localDownloadDir} )`);
		await tarUtil.compress(localDownloadDir);
	}
	const tarArchivedFile = localDownloadDir + '.tar';

	if (enable_compressions && fs.existsSync(tarArchivedFile)) {
		// gzip 압축은 .tar 로 아카이브 된 파일만을 대상으로 함
		log.debug(`[${logPrefix} :: _compressLocalDir] .gz 압축 파일 생성 ( localFilePath= ${tarArchivedFile} )`);
		await gzipUtil.compress(tarArchivedFile);
	} else {
		log.debug(`[${logPrefix} :: _compressLocalDir] 압축 설정(ENABLE_COMPRESSION=${enable_compressions})에 따라 gzip 압축 스킵`);
	}
};


/**
 * _decompressLocalDir : 다운받은 파일의 타입에 따라 아카이브/압축 해제 (.tar, .tar.gz)
 *
 * @param localDownloadFile
 * @private
 */
const _decompressLocalDir = async (localDownloadFile) => {
	if (fs.existsSync(localDownloadFile) && path.extname(localDownloadFile) === '.gz') {
		log.debug(`[${logPrefix} :: _decompressLocalDir] .gz 압축 해제 ( localFilePath= ${localDownloadFile} )`);
		await gzipUtil.uncompress(localDownloadFile);
	}

	const unGzippedOrJustTarFile = localDownloadFile.replace('.gz', '');
	if (fs.existsSync(unGzippedOrJustTarFile) && path.extname(unGzippedOrJustTarFile) === '.tar') {
		log.debug(`[${logPrefix} :: _decompressLocalDir] .tar 아카이브 해제 ( localFilePath= ${unGzippedOrJustTarFile} )`);
		await tarUtil.uncompress(unGzippedOrJustTarFile);
	}
};


/**
 * 각 프로세스 들이 특정 작업을 완료하면 당시의 디스크 및 메모리 상태를 로깅
 *
 * @param jobType 수행하는 작업의 종류
 * @param progressMsg 각 작업별 로깅을 위한 문자열
 * @private
 */
const _checkProgress = (jobType, progressMsg) => {
	// 마운트 된 디스크 정보 (복구: home2, 백업: home3)
	const diskResult = child_process.execSync(`df -hv ${config.backup_restore[jobType].base_dir}`, { encoding: 'utf8' });
	let [mountPoint, total, used, avail, diskUsageRate] = diskResult.split('\n')[1].split(/\s+/);
	const diskInfo = { total, used, avail, diskUsageRate };

	// 전체 메모리 정보
	const memResult = child_process.execSync('free -g', { encoding: 'utf8' });
	let buffCache, free;
	[total, used, free, buffCache, avail] = memResult.split('\n')[1].split(/\s+/).filter(Boolean).slice(1);
	const memInfo = { total: `${total}G`, used: `${used}G`, free: `${free}G`, memUsageRate: `${Math.round(parseInt(used) / parseInt(total) * 100)}%` };

	// 해당 파일 작업 중인 프로세스 메모리 정보
	const processMemInfo = {
		rss: `${Math.round(process.memoryUsage().rss / (1024 ** 2))}M`,
		heapUsed: `${Math.round(process.memoryUsage().heapUsed / (1024 ** 2))}M`
	}

	log.info(`[${logPrefix} :: _checkProgress] ${progressMsg} 완료
	디스크 정보: ${JSON.stringify(diskInfo)}
	메모리 정보: 
	 - 전체: ${JSON.stringify(memInfo)}
	 - 프로세스: ${JSON.stringify(processMemInfo)}`);

	if (parseInt(diskUsageRate) > 90) {
		throw new BusinessError({ message: `[${logPrefix} :: _checkProgress] 디스크의 사용률이 90% 를 초과했습니다. ( ${diskUsageRate} )` });
	}

	if (parseInt(memInfo.memUsageRate) > 60) {
		throw new BusinessError({ message: `[${logPrefix} :: _checkProgress] 메모리의 사용률이 60% 를 초과했습니다. ( ${memInfo.memUsageRate} )` });
	}
}
