'use strict';

import fs from 'fs';
import _ from 'lodash';
import path from 'path';
import xlsx from 'xlsx';
import moment from 'moment';
import {PutObjectCommand, S3Client} from '@aws-sdk/client-s3';

import config from '../../config/config';

import * as file from '../../utils/file.util';
import * as logger from '../../utils/logger.util';

import {BusinessError} from '../../common/error';

import {getDiscrepancyReport} from "./discrepancy-report-api.service";

const LOGGER = 'discrepancy_report_api';

/*
	TTD 불일치 리포트 지표 정보
	- 디멘전
		- date
		- publisherId
	- 메트릭
		- sspEstimatedImpressions
		- sspEstimatedRevenue
*/

/**
 * [PROCESS 3:: TASK 2] processDiscrepancyReportApi : TTD 불일치 리포트 API 연동
 * 	SUBTASK 1. _getDiscrepancyReport : 리포트 집계
 * 	SUBTASK 2. _generateReportFile : 불일치 리포트 파일 생성하기
 * 	SUBTASK 3. _sendReport : AWS S3 리포트 업로드
 *
 * @RequestMapping(value='/batch/discrepancy/report/api/ttd')
 *
 * @param {Object} { _id, ymd, adProvider_ids, publisher_ids, startDate, endDate }
 * @param {Object} { finalDimensions, metrics, reportInfos: {file, mail} }
 * @return {String} filePath
 */
module.exports.processDiscrepancyReportApi = async ({
														_id,
														ymd,
														adProvider_ids,
														publisher_ids,
														startDate,
														endDate
													}, { finalDimensions, metrics, reportInfos: { file, api } }) => {
	logger.debug(LOGGER, `[ttd.service :: processDiscrepancyReportApi] 호출됨 ( schedule_id= ${_id} )`);

	// SUBTASK 0. 메트릭의 필드명만 추출
	const finalMetrics = metrics.map(met => met.name);

	// SUBTASK 1. _getDiscrepancyReport : DiscrepancyDaily 데이터로부터 리포트 집계
	// [ { date: "20220728", sspEstimatedImpressions: 31, sspEstimatedRevenue: 9.30, publisher_id: "xxxx" } ]
	const reportData = await getDiscrepancyReport({
		_id,
		adProvider_ids,
		publisher_ids,
		dimensions: finalDimensions,
		metrics: finalMetrics,
		startDate,
		endDate
	});

	// const reportData = [{ date: '20250210', sspEstimatedImpressions: 10, sspEstimatedRevenue: 100.1, publisher_id: 'test' }];

	// SUBTASK 2. _generateReportFile : 불일치 리포트 파일 생성하기
	const baseDir = path.join(config.owfs_root, file.path, moment(ymd).format('YYYY'), moment(ymd).format('MM'));
	const filePath = await _generateReportFile({ _id, ymd, baseDir, fileNameFormat: file.name, reportData });

	// 리얼 환경에서만 불일치 리포트 연동을 처리한다.
	if (process.env.NODE_ENV === 'production') {
		// SUBTASK 3. _sendReport : AWS S3 리포트 업로드
		await _sendReport({
			_id,
			filePath,
			apiInfo: {
				bucketName: api.bucketName,
				region: api.region,
				prefix: api.prefix,
				ACCESS_KEY: api.ACCESS_KEY,
				SECRET_KEY: api.SECRET_KEY
			},
		});
	}

	return filePath;
};


/**
 * SUBTASK 2. _generateReportFile : 불일치 리포트 파일 생성하기
 *
 * @param {Object} { _id, ymd, baseDir, fileNameFormat, discrepancyReport }
 * @return {String} filePath
 */
const _generateReportFile = async ({ _id, ymd, baseDir, fileNameFormat, reportData }) => {
	logger.debug(LOGGER, `[ttd.service :: _generateReportFile] 불일치 리포트 파일 생성하기 ( schedule_id= ${_id} )`);

	// discrepancy 디렉토리 생성
	await file.mkdirIfNotExist(baseDir);

	// file_name: '{{YYYY-MM-DD}}_daily-report_NAVER_{{hh-mm-ss}}.xlsx'
	// YYYY-MM-DD = 배치 일자 (ymd) / hh-mm-ss = 배치 처리 시각 (current time)
	const fileName = fileNameFormat.replace('{{YYYY-MM-DD}}', moment(ymd).format('YYYY-MM-DD')).replace('{{hh-mm-ss}}', moment().format('HH-mm-ss')).replace(/\s/g, '');

	// filePath = download/discrepancy_report/ttd/yyyy/mm/yyyy-mm-dd_daily-report_NAVER_hh-mm-ss.xlsx
	const filePath = path.join(baseDir, fileName);

	// workbook 생성
	const wBook = xlsx.utils.book_new();

	const dataList = [[ 'Date', 'Impressions', 'Media Cost', 'Currency Type', 'TimeZone', 'Bid Count', 'Media Type', 'Market Type', 'Publisher Id']];

	reportData.map(({ date, sspEstimatedImpressions, sspEstimatedRevenue, publisher_id }) => {
		dataList.push([moment(date).format('YYYY-MM-DD'), sspEstimatedImpressions, sspEstimatedRevenue, 'USD', 'UTC', '', '', '', publisher_id]);
	});

	// worksheet 생성
	const wSheet = xlsx.utils.aoa_to_sheet(dataList);

	/*
		엑셀 셀 서식 (https://github.com/SheetJS/sheetjs#cell-object)
		- v: The raw value of the cell.
		- t: The Excel Data Type of the cell. (b Boolean, n Number, e Error, s String, d Date, z Empty)
		- z: Number format string associated with the cell
		- s: The style/theme of the cell
	*/
	// 셀 서식 설정 (ex. wSheet['B2'] = { t: 'n', v: imp, z: '#,##0' })
	for (let i = 2; i < dataList.length + 1; i++) {
		wSheet[`A${i}`].t = 's';
		wSheet[`B${i}`].z = '#,##0';
		wSheet[`C${i}`].z = '#,##0'; // 소수점이 필요한 경우, '#,##0.####'
	}

	xlsx.utils.book_append_sheet(wBook, wSheet);
	xlsx.writeFile(wBook, filePath);

	return filePath;
};


/**
 * SUBTASK 3. _sendReport : AWS S3 리포트 업로드
 *
 * @param {Object} { _id, filePath, apiInfo: { bucketName, prefix, region, ACCESS_KEY, SECRET_KEY }}
 */
const _sendReport = async ({ _id, filePath, apiInfo: { bucketName, prefix, region, ACCESS_KEY, SECRET_KEY } }) => {
	logger.debug(LOGGER, `[ttd.service :: _sendReport] AWS S3 리포트 업로드 시작 ( schedule_id= ${_id} )`);

	// https://docs.aws.amazon.com/sdk-for-javascript/v3/developer-guide/s3-example-creating-buckets.html#s3-example-creating-buckets-upload-file
	const client = new S3Client({
		region,
		credentials: { accessKeyId: ACCESS_KEY, secretAccessKey: SECRET_KEY }
	});

	// filePath = download/reportapi/ttd/yyyy/mm/discrepancy/yyyy-mm-dd_daily-report_NAVER_hh-mm-ss.xlsx
	// Bucket: 'thetradedesk-supply-vendor-reporting', Key: 'naverglad/2021-06-17_daily-report_NAVER_17-40-56.xlsx',
	const params = {
		ACL: 'bucket-owner-full-control',
		Bucket: bucketName,
		Key: prefix + path.basename(filePath), // 확장자 포함
		Body: fs.createReadStream(filePath)
	};

	logger.debug(LOGGER, `[ttd.service :: _sendReport] Bucket=${params.Bucket}, Key=${params.Key} ( schedule_id= ${_id} )`);

	// 성공 = { $metadata: { httpStatusCode: 200, ... }, ETag, ... }
	// 실패 = { AccessDenied, Code: 'AccessDenied', name: 'AccessDenied', '$fault': 'client', '$metadata': { httpStatusCode: 403, ... }, ... }
	const res = await client.send(new PutObjectCommand(params));

	// [ERROR] httpStatusCode 가 200 이 아닌 경우, 에러 처리
	if (!_.isNil(res) && !_.isNil(res.$metadata) && !_.isEqual(res.$metadata.httpStatusCode, 200)) {
		throw new BusinessError({ message: `[ttd.service :: _sendReport] AWS S3 리포트 업로드 실패 ( schedule_id= ${_id} ) ${JSON.stringify(res, null, 2)}` });
	}

	logger.debug(LOGGER, `[ttd.service :: _sendReport] AWS S3 리포트 업로드 완료 ( schedule_id= ${_id} )`);
};
