import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { Model } from 'mongoose';

import { BusinessException } from '../exception/business-exception';
import { CommonErrorCode } from '../error/error-code';

import { CommonCode, CommonCodeDocument } from '../report/schema/common-code.schema';


@Injectable()
export class CommonCodeService implements OnModuleInit {
	private readonly logger = new Logger(CommonCodeService.name);

	private static instance: CommonCodeService; // 정적 싱글턴 인스턴스

	/* {
		channelType: ['IOS', 'ANDROID', 'WEB'],
		creativeType: ['BANNER', 'NATIVE', 'COMBINED', 'VIDEO'],
		productType: ['DISPLAY', 'INSTREAM', 'INTERSTITIAL', 'REWARD_VIDEO'],
		responseCreativeType: ['BANNER', 'NATIVE', 'VIDEO'],
		deviceOs: ['ANDROID', 'IOS', 'MACOS', 'WINDOWS', 'LINUX', 'OTHERS']
	} */
	private commonCodes: Record<string, readonly string[]> = {};

	constructor(
		@InjectModel(CommonCode.name)
		private commonCodeModel: Model<CommonCodeDocument>,
	) {
		this.logger.log("CommonCodeService constructor");

		// 싱글턴 인스턴스를 설정
		if (!CommonCodeService.instance) {
			CommonCodeService.instance = this;
		}
	}

	// NestJS 모듈 초기화 시 호출되는 메서드
	async onModuleInit() {
		this.logger.log("CommonCodeService onModuleInit");

		await this.loadCommonCodes();
	}

	// 정적 메서드로 싱글턴 인스턴스 반환
	static getInstance(): CommonCodeService {
		if (!CommonCodeService.instance) {
			throw new BusinessException([CommonErrorCode.UNKNOWN_ERROR]);
		}

		return CommonCodeService.instance;
	}

	// DB CommonCodes 로드
	private async loadCommonCodes() {
		// commonCodes = { deviceOs: ['ANDROID', 'IOS', 'MACOS', 'WINDOWS', 'LINUX', 'OTHERS' ], ... }
		const commonCodes = await this.commonCodeModel.aggregate([
			{ $group: { _id: '$category', code: {$push:'$code'} } },
			{ $project: { _id: 0, category: '$_id', code: 1 } }
		]);

		this.commonCodes = commonCodes.reduce((acc, record) => {
			acc[record.category] = [...record.code] as const;
			return acc;
		}, { } as Record<string, readonly string[]>);
	}

	getCommonCodes(): Record<string, readonly string[]> {
		return this.commonCodes;
	}

	getCommonCode(type: string): readonly string[] {
		return this.commonCodes[type];
	}
}
