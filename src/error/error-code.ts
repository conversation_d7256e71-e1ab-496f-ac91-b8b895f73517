export const CommonErrorCode = {
	/* BusinessException */
	UNKNOWN_ERROR: { code: 9000, message: '[9000] Unknown Error' },
} as const;

export const AuthorizationErrorCode = {
	/* UnauthorizedException */
	REQUIRED_CLIENT_ID: { code: 1000, message: '[1000] User information not found' }, 				// [1000] 사용자 정보가 없습니다
	INVALID_CLIENT_ID: { code: 1001, message: '[1001] User information is invalid' },				// [1001] 사용자 정보가 유효하지 않습니다
	NO_AUTH_PUBLISHER: { code: 1002, message: '[1002] No publisher access permission' }, 			// [1002] 매체 접근 권한이 없습니다
} as const;

export const CommonReportErrorCode = {
	/* BadRequestException */
	REQUIRED_PUBLISHER: { code: 2000, message: '[2000] Publisher information is invalid' },			// [2000] 매체 정보가 유효하지 않습니다
	INVALID_FORMAT_DATE: { code: 2001, message: '[2001] $property must be in yyyyMMdd format', field: { property: '' } },	// [2001] $property 는 yyyyMMdd 포맷이어야 합니다.
	START_DATE_BEFORE_END_DATE: { code: 2002, message: '[2002] startDate must be before endDate' }, // [2002] 시작일은 종료일보다 이전이어야 합니다
	END_DATE_BEFORE_TODAY: { code: 2003, message: `[2003] endDate must be before today's date` }, 	// [2003] 종료일은 오늘자보다 이전이어야 합니다
	QUERYABLE_DATE: { code: 2004, message: '[2004] Reports can only be queried for up to the last $value $unit', field: { value: '', unit: '' } }, // [2004] 리포트는 최근 $value $unit 까지만 조회가 가능합니다

	INVALID_DIMENSIONS: { code: 2005, message: '[2005] There are invalid values in dimensions' }, 		// [2005] dimensions 에 유효하지 않은 값이 있습니다
	INVALID_METRICS: { code: 2006, message: '[2006] There are invalid values in metrics' },				// [2006] metrics 에 유효하지 않은 값이 있습니다
	INVALID_FILTERS: { code: 2007, message: '[2007] There are invalid values in filters $property', field: { property: '' } },	// [2007] filters 에 유효하지 않은 값이 있습니다
	INVALID_VALUE: { code: 2008, message: '[2008] There are invalid value in $property', field: { property: '' } },				// [2008] $property 에 유효하지 않은 값이 있습니다

	REQUIRED_VALUE: { code: 2009, message: '[2009] $property should not be empty', field: { property: '' } },					// [2009] $property 는 필수값 입니다
	DUPLICATED_VALUE: { code: 2010, message: '[2010] The value of $property cannot be duplicated', field: { property: '' } },	// [2010] $property 의 값은 중복될 수 없습니다
	AT_LEAST_ONE_VALUE: { code: 2011, message: '[2011] $property must contain at least 1 elements', field: { property: '' } },	// [2011] $property 는 최소 1개 이상 값이 있어야 합니다

	REQUIRED_AT_LEAST_ONE_FIELDS: { code: 2020, message: '[2020] $target requires at least one of the following: $fields', field: { target: '', fields: [] } },	// [2020] $target 는 다음 항목들 중 하나가 필수 입니다. ($fields)
	REQUIRED_FIELDS: { code: 2021, message: '[2021] $target requires the following: $fields', field: { target: '', fields: [] } },	// [2021] $target 는 다음 항목(들)이 필수 입니다. ($fields)
	NOT_INCLUDE_FIELDS: { code: 2022, message: '[2022] $target must not include $fields', field: { target: '', fields: [] } },		// [2022] $target 는 다음 항목이 포함되면 안 됩니다. ($fields)
	USED_WITH_OTHER_METRICS: { code: 2023, message: '[2023] $property must be used with other metrics', field: { property: '' } },	// [2023] $target 는 다른 지표와 함께 사용해야 합니다.
	AP_TIMEZONE_REQUIRED_FIELDS: { code: 2024, message: '[2024] When selecting an AP timezone, $target requires the following: $fields', field: { target: '', fields: [] } },	// [2024] AP 타임존 선택 시, $target 는 다음 항목(들)이 필수 입니다. ($fields)

	CANNOT_EXCEED_PERIOD: { code: 2030, message: '[2030] Report period cannot exceed $value $unit', field: { value: '', unit: '', queryPeriod: {} } },	// [2030] 리포트 기간은 $value $unit 을 초과할 수 없습니다
	REPORT_NOT_EXIST: { code: 2031, message: '[2031] reportId does not exist' }, // [2031] reportId 가 존재하지 않습니다

	/* BusinessException */
	CANNOT_DOWNLOAD_REPORT: { code: 2032, message: '[2032] Unable to download the report file' }, // [2032] 리포트 파일을 다운로드할 수 없습니다
	CANNOT_REQUEST_REPORT: { code: 2033, message: '[2033] Unable to request report generation. Please contact the administrator' }, // [2033] 리포트 생성 요청을 할 수 없습니다. 관리자에게 문의 주세요
	REPORT_NOT_COMPLETE: { code: 2034, message: '[2034] The report has not yet been generated' }, // [2034] 리포트가 아직 생성 되지 않았습니다
	REPORT_FAILURE: { code: 2035, message: '[2035] Report creation has failed' },                 // [2035] 리포트 생성이 실패하였습니다
	NO_AUTH_REPORT: { code: 2036, message: '[2036] No report access permission' }, // [2036] 리포트 접근 권한이 없습니다
} as const;

export const PerformanceReportErrorCode = {
	/* BadRequestException */
	EXCEEDED_DAILY_REPORT_MAX_COUNT: { code: 3001, message: '[3001] The number of reports that can be generated in one day has been exceeded' }, // [3001] 하루 생성 가능한 리포트 개수를 초과하였습니다
	REPORT_NOT_COMPLETE: { code: 3002, message: '[3002] The report has not yet been generated' }, 					// [3002] 리포트가 아직 생성 되지 않았습니다
	EXPIRED_REPORT: { code: 3003, message: '[3003] The report has expired' }, 										// [3003] 리포트가 만료 되었습니다
	EXCEEDED_MAX_FILE_SIZE: { code: 3004, message: '[3004] The report file size exceeds the maximum allowed size' }, 	// [3004] 리포트 파일 크기가 최대 허용 기준을 초과하였습니다

	/* BusinessException */
	REPORT_FAILURE: { code: 3010, message: '[3010] Publisher performance report creation has failed' }, 			// [3010] 리포트 생성이 실패하였습니다
} as const;

export const CustomReportErrorCode = {
	/* BadRequestException */
	REQUIRED_TIME_UNIT: { code: 4001, message: '[4001] The timeUnit dimension is required' },				// [4001] TimeUnit Dimension 은 필수 항목 입니다
	ALLOWED_ONLY_ONE_TIME_UNIT: { code: 4002, message: '[4002] Only one timeUnit dimension is allowed' },	// [4002] TimeUnit Dimension 은 한개만 허용합니다
	ALLOWED_ONLY_ONE_PROPERTY: { code: 4003, message: '[4003] Only one property is allowed for $property', field: { property: '' } }, 	// [4003] $property 는 하나의 속성만 허용합니다
	CANNOT_UPDATE_REPORT: { code: 4004, message: '[4004] Unable to update a report unless it is in the READY state' }, 		// [4004] READY 상태가 아닌 리포트는 수정할 수 없습니다
	CANNOT_DELETE_REPORT: { code: 4005, message: '[4005] Unable to delete a report that is in progress' }, 					// [4005] IN_PROGRESS 상태인 리포트는 삭제할 수 없습니다
	EXCEEDED_QUERYABLE_REPORT_COUNT: { code: 4006, message: '[4006] The number of queryable reports has been exceeded' }, 	// [4006] 조회 가능한 리포트 개수를 초과하였습니다.
} as const;

export const ScheduleErrorCode = {
	/* BadRequestException */
	SCHEDULE_NOT_EXIST: { code: 5002, message: '[5002] scheduleId does not exist' }, // [5002] scheduleId 가 존재하지 않습니다

	/* UnauthorizedException */
	NO_AUTH_SCHEDULE: { code: 5003, message: '[5003] No schedule access permission' }, 	// [5003] 스케줄에 대한 접근 권한이 없습니다

	/* BusinessException */
	DUPLICATE_SCHEDULE_NAME: { code: 5004, message: '[5004] Duplicate schedule name' }, 	// [5004] 중복된 스케줄 이름입니다.
	EXCEEDED_MAXIMUM_NUMBER_OF_KEYS: { code: 5005, message: '[5005] The maximum number of keys is 15' }, 	// [5005] 등록할 수 있는 키는 최대 15개입니다
} as const;

export const RevenueSharingReportErrorCode = {
	/* BadRequestException */
	INVALID_DATE_FORMAT: { code: 6001, message: '[6001] date format must be yyyyMM("202501") or yyyyMMdd("20250101")' }, // [6001] 날짜 형식은 yyyyMM("202501") 이거나 yyyyMMdd("20250101") 형식이어야 합니다.

	/* BusinessException */
	CANNOT_DELETE_KEY: { code: 6002, message: '[6002] "The assigned key cannot be deleted once set' }, // [6002] 한 번 지정한 키는 삭제할 수 없습니다.
	// CANNOT_DELETE_SERVICEID: { code: 6003, message: '[6003] "The assigned serviceId cannot be deleted once set' }, // [6003] 한 번 지정한 서비스는 삭제할 수 없습니다.
	// CANNOT_DELETE_ADUNITID: { code: 6004, message: '[6004] "The assigned adUnitId cannot be deleted once set' }, // [6004] 한 번 지정한 광고유닛은 삭제할 수 없습니다.
	REQUIRED_ONE_OF_SVCIDS_OR_AUIDS: { code: 6005, message: '[6005] "Either serviceIds or adUnitIds is required' }, // [6005] serviceIds 또는 adUnitIds 중 하나는 필수입니다.
} as const;


