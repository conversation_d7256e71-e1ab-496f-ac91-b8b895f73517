import { ValidationArguments, ValidationError } from 'class-validator';

import _ from 'lodash';

import { isValidJSON } from '../util/util';

export type ErrorCode = {
	code: number;
	message: string;
	field?: object|any;
};

// errorCode = { code: 2009, message: '[2009] $property should not be empty' },
// result = "{\"code\":2009,\"message\":\"[2009] timezone should not be empty\",\"field\":{\"property\":\"timezone\"}}"
export function toStringReplacedErrorCode(errorCode: ErrorCode, includeField: boolean = true) {
	return (args: ValidationArguments) => {
		const property = (args.object as any).parentProperty || args.property;
		return JSON.stringify(getReplacedErrorCode(errorCode, { property }, includeField));
	}
}

// message 치환 문자열에 field 항목 적용
export function getReplacedErrorCode({ code, message }, field: Object, includeField: boolean = true) {
	for (const [key, value] of Object.entries(field)) {
		if (_.isArray(value)) {
			message = message.replace(`$${key}`, value.join(','));
		} else {
			message = message.replace(`$${key}`, value);
		}
	}

	return includeField ? { code, message, field } : { code, message };
}

// 사용자 ValidationPipe 에서 errorMessage 추출 시 사용
export function extractErrorMessages(errors: ValidationError[]): object[] {
	const errorMessages: object[] = [];

	// errors = [{ property: 'filters', children: [ { property: 'deviceOs', children: [ { property: 'in', children: [], constraints: { ArrayDistinct: "{\"code\":2010,\"message\":\"[2010] The value of deviceOs cannot be duplicated\",\"field\":{\"property\":\"deviceOs\"}}" } } ] } ] },
	// 			 { property: 'startDate', children: [], constraints: { isDateString: "{\"code\":2001,\"message\":\"[2001] startDate must be in yyyyMMdd format\",\"field\":{\"property\":\"startDate\"}}" } }]
	for (const error of errors) {
		const { constraints, children } = error;

		// constraints 에서 메시지 추출
		if (constraints) {
			// as-is : "{\"code\":2010,\"message\":\"[2010] The value of serviceId cannot be duplicated\",\"field\":{\"property\":\"serviceId\"}}"
			// to-be : { code: 2010, message: '[2010] The value of serviceId cannot be duplicated', field: { property: 'serviceId' } }
			const errorMessage = Object.values(constraints).map(message => isValidJSON(message) ? JSON.parse(message) : message);
			errorMessages.push(...errorMessage);
		}

		// children 이 있는 경우 메시지 추출
		if (children && children.length > 0) {
			errorMessages.push(...extractErrorMessages(children)); // 재귀 호출
		}
	}

	// 중복 제거 후 반환
	// [{ code: 2010, message: '[2010] The value of serviceId cannot be duplicated', field: { property: 'serviceId' } }, ....]
	return _.uniqWith(errorMessages, _.isEqual);
}
