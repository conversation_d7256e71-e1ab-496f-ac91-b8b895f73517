'use strict';

import 'babel-polyfill';

import fs from 'fs';
import koa from 'koa';
import path from 'path';
import json from 'koa-json';
import router from 'koa-router';
import helmet from 'koa-helmet';
import favicon from 'koa-favicon';
import bodyParser from 'koa-bodyparser';

import config from './config/config';
import logger from './common/logger';
import errorHandler from './common/error-handler';
import mongoose from './common/mongoose';
import * as ccd from './common/common-code';
import mongooseForData from './common/mongoose-for-data';
import routes from './common/routes';

import * as c3 from './c3/c3';
import * as kvextService from './services/kvext/kvext.service';

// logger settings
const wlogger = logger(config);

// L7 check stuff
const HEALTH_CHECK_FILE_PATH = module.exports.HEALTH_CHECK_FILE_PATH = path.resolve(process.env.HOME, 'health', 'down');
const HEALTH_CHECK_DOWN_FILE_PATH = module.exports.HEALTH_CHECK_DOWN_FILE_PATH = path.resolve(process.env.HOME, 'health', 'down_complete');

(async function() {
	const app = new koa();
	const Router = new router({strict:true});

	// C3 Hadoop Active Host settings
	await c3.setActiveHost();

	// database settings
	await mongoose(config);
	wlogger.info(`[server] ready db:cms`);
	await mongooseForData(config);
	wlogger.info(`[server] ready db:data`);

	// 공통코드 로딩
	await ccd.load();

	// error Handler settings
	errorHandler(app);

	// security
	app.use(helmet());
	app.use(json());
	app.use(bodyParser());
	app.use(favicon(path.resolve(process.cwd() + '/public/favicon.ico')));

	// Load the routing info
	routes(app, Router);

	// Create HTTP server
	const port = config.port || '3000';

	// add shutdown listener
	addShutdownListener();

	// koa server start
	let server = app.listen(port, () => {
		try {
			fs.unlinkSync(HEALTH_CHECK_FILE_PATH);
			fs.unlinkSync(HEALTH_CHECK_DOWN_FILE_PATH);
		} catch (e) {
		}

		wlogger.info('[server] SSP Batch Server 시작됨.');

		// KeyValue Extension Executor 초기화
		kvextService.startup();
	});
	server.timeout = ********; // 1000000 ms였음. 수익쉐어가 매달 2일에 한달치를 생성하기에 오래 걸리므로 8시간으로 늘림
})();



/**
 * 셧다운 시 해 줘야 할 일 등록
 */
const addShutdownListener = () => {
	/**
	 * Graceful Deploy Configuration
	 * pm2에서 종료시그널 (SIGINT)를 받으면 health check 응답으로 404 를 리턴하고,
	 * 일정시간 뒤에 프로세스를 종료한다
	 * SIGINT를 두번 받으면 종료한다 (ctrl + c 두번)
	 * 사내 헬스체크는 기본 5초씩 두번 실패하면 L4에서 빠진다.
	 */
	process.once("SIGINT", async () => {

		wlogger.info('....................................... SIGINT 받았어');

		if (config.gracefulWaitTime > 0) {

			// shutdown 전 처리해야 할 작업 정리
			await _shutdown();

			if (process.env.NODE_APP_INSTANCE === '0') {
				fs.closeSync(fs.openSync(HEALTH_CHECK_FILE_PATH, 'w')); // down중
				setTimeout(() => {
					wlogger.info(`waiting for health check off (${sec++ }s)`);
					fs.closeSync(fs.openSync(HEALTH_CHECK_DOWN_FILE_PATH, 'w'));
				}, config.gracefulWaitTime); // 15초 뒤 down완료.
			}

			// 1초마다 down완료 파일이 있는지 검사
			let sec = 1;
			setInterval(() => {
				// 있으면 프로세스 종료
				wlogger.info(`waiting for health check off (${sec++ }s)`);
				const exist = fs.existsSync(HEALTH_CHECK_DOWN_FILE_PATH);
				if (exist) {
					wlogger.info(`${process.env.NODE_APP_INSTANCE} PM2 Process End`);

					// setTimeout이 없으면 위 로그를 찍지 않는다.
					setTimeout(() => {
						process.exit(0);
					}, 0)
				}
			}, 1000);
		} else {
			// shutdown 전 처리해야 할 작업 정리
			await _shutdown();

			wlogger.info(`${process.env.NODE_APP_INSTANCE} PM2 Process End local`);
			process.exit(0);
		}
	});
};


/**
 * 프로세스 종료 전 할 일이 있다면 여기 등록하세요.
 * @returns {Promise.<void>}
 * @private
 */
const _shutdown = async () => {
	await _shutdownKvext();
};


/**
 * 확장키 관리 배치의 진행중인 job이 종료될 때까지 대기..
 * @returns {Promise.<void>}
 * @private
 */
const _shutdownKvext = async () => {
	await kvextService.shutdown();
};
