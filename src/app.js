try {
	require('babel-register');

	require('./server');
} catch (error) {
	let config = require('./config/config').default;
	let log4js = require('log4js');
	log4js.configure({
		appenders: { 
			all: { // common all log (out + error)
				type:'dateFile',
				filename: config.log_dir + '/all.log',
				pattern: '.yyyy-MM-dd',
			},
			error: { // common error log
				type:'dateFile',
				filename: config.log_dir + '/error.log',
				pattern: '.yyyy-MM-dd',
			},
			console: {
				type:'console',
				layout: {
					type:'pattern',
					pattern: `%[[%d{yyyy-MM-dd hh:mm:ss,SSS}] [%p] ::%] %m`
				}
			}
		},
		categories: {
			default: {
				appenders: (process.env.NODE_ENV !== 'production') ? ['console', 'all', 'error'] : ['all', 'error'],
				level: 'error'
			}
		},
		pm2: true,
		disableClustering: false
	});

	let wlogger = log4js.getLogger();

	wlogger.error("[app] Err handler", error.stack);
}
