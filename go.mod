module oss.navercorp.com/da-ssp/gfp-api

go 1.12

require github.com/gin-gonic/gin v1.4.0

require github.com/satori/go.uuid v1.2.0

require github.com/sirupsen/logrus v1.4.2

require github.com/lestrrat-go/file-rotatelogs v2.2.0+incompatible

require oss.navercorp.com/acl/acl-filter-go v1.0.1

require github.com/shunfei/godruid v0.0.0-20171207111340-296a59dd69bd

require (
	github.com/0xAX/notificator v0.0.0-20181105090803-d81462e38c21 // indirect
	github.com/araddon/dateparse v0.0.0-20190622164848-0fb0a474d195
	github.com/codegangsta/envy v0.0.0-20141216192214-4b78388c8ce4 // indirect
	github.com/codegangsta/gin v0.0.0-20171026143024-cafe2ce98974 // indirect
	github.com/deckarep/golang-set v1.7.1
	github.com/fastly/go-utils v0.0.0-20180712184237-d95a45783239 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/gobwas/glob v0.2.3
	github.com/golang/protobuf v1.3.2 // indirect
	github.com/jehiah/go-strftime v0.0.0-20171201141054-1d33003b3869 // indirect
	github.com/lestrrat-go/strftime v0.0.0-20190725011945-5c849dd2c51d // indirect
	github.com/magiconair/properties v1.8.1
	github.com/mattn/go-isatty v0.0.8 // indirect
	github.com/mattn/go-shellwords v1.0.6 // indirect
	github.com/samuel/go-zookeeper v0.0.0-20190810000440-0ceca61e4d75
	github.com/shopspring/decimal v1.3.1
	github.com/spf13/viper v1.4.0
	github.com/tebeka/strftime v0.1.3 // indirect
	github.com/ugorji/go v1.1.7 // indirect
	github.com/xdg/scram v0.0.0-20180814205039-7eeb5667e42c // indirect
	github.com/xdg/stringprep v1.0.0 // indirect
	go.mongodb.org/mongo-driver v1.7.4
	golang.org/x/net v0.0.0-20190724013045-ca1201d0de80 // indirect
	gopkg.in/go-playground/validator.v8 v8.18.2
	oss.navercorp.com/api-gateway/api-gateway-hmac.git v0.0.0-20190408050205-f2b54f236d4d
)

// replace oss.navercorp.com/acl/acl-filter-go => ../acl-filter-go
