n2c:
  application: nam-api
  instance: app-dev
  installation: '202203011530'

deployTag: tag-20220315-001

profile: dev
natIp: ''

# Whether to create instance level service or not
# if false, let N2C pipeline do the job
# if true, we are going to create instance service manually
instanceService: false

replicas: 2

pod:
  annotations: {}
  extras:
    terminationGracePeriodSeconds: 60

app:
  public_url: http://localhost:3002
  port: 3002
  image:
    name: reg.navercorp.com/gfp/nam-api
    tag: '0.1-dev'
  resources:
    limits:
      memory: 8Gi
      cpu: 1500m
  # env vars defined in templates/deployment.yaml
  # - NAM_API_PORT (derived from 'app.port')
  # - NAM_API_CONFIG_FILE (derived from 'profile')
  env: {}