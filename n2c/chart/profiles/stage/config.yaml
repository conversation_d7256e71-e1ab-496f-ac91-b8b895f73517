# deploy tag: {{ .Values.deployTag }}
logger:
  level: trace
  nelo:
    url: http://nelo2-col.navercorp.com/_store
    projectName: 3fcb80b09ef34872b56c9d91e6be5c8e
    projectVersion: "1.0-stage"
    maxInTransit: 8
    timeoutMs: 2000
  rotator:
    path: ./api-%DATE%.log
    frequency: daily
    maxLogFiles: 5
    maxLogSize: "80m"
    level: trace
  # requestIdHeader: X-Request-ID
db:
  debug: true
  api:
    # GFP DATA real mongodb (gfp-real@gfp-real, sharded)
    uri_encrypted: "2:DlUXhfbzo-AW0tsh:6GbAMd4_LMNQvMjyNStHhFmONoGmBqICf42FhBrZ6mt3svJShn-yBCffNN_s-ne2gAJuBFw79ZbIpt65P4DjFwUAyiMY4RcBec98eRJGTFG_dQbPef6iQgCCRMgxLeOhxOOXjl_lfq2jAVBj4OHwN1iN-wFsLsUYyYwKNLx-Sp7IXVGhH-OEIk4hzD4="
    connectTimeoutMS: 2000
    retryAttempts: 1
    retryDelay: 1500
  cms:
    # GFP CMS real mongodb (ssp@team-ssp, replica)
    uri_encrypted: "2:5Ux5ihKcWHi3TKAH:xNZJB49jlEnB_m7jzymLtvS14lF76wmxX6HAldiL490mIBW3PtnV-h4i0VqpctG2efeFRDCDm7QeTfquY7OoAFUuczdxr0xkMqiMshWp5Ji2vshWnRMAc-Vzfv8-Za7WdX-ZRGLgGOMMceMrZnZY7mKSqfrM31v6qgRa5Ljxgl09yML1AyidX6BWLuVGZk5ND98z8PqKoPOGYFJw51psNZFNdNqdf6vXwRHXbFSX07mUFyGGITzRw1icDxnqfoc="
    connectTimeoutMS: 2000
    retryAttempts: 1
    retryDelay: 1500
nubes:
  nam_api:
    bucket: nam_api_report
    # Nubes 평촌 instance 접속 정보
    gatewayAddress: a.nubes.sto.navercorp.com:8000
nclavis:
  # N2C NAT (dev)
  service_url: https://{{ .Values.natIp }}/kms/consumer
  resource_id: FaSXyi_TBlQH8_h8vkKOBQC2ZHI=
  insecureHttps: true
one_time_download:
  root_url: {{ .Values.app.public_url }}
  hash_key_encrypted: 2:71vxpm5R2yfDM_6I:WSHCNNM3GyO9lKIt4ZAzNx4t2Ft8TyBTrHpYesx480fhkoLfS6SlS3_SffjecD5WCYWIeP1eQUEPhQifI3IVlUDDGW_jIo9hlNfuLF7qepY=
  retention_sec:
    csv_report: 1800
