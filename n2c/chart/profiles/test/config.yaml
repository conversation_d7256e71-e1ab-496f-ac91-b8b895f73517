# deploy tag: {{ .Values.deployTag }}
logger:
  level: trace
  nelo:
    url: http://alpha-col.nelo2.navercorp.com/_store
    projectName: 5dea6d2f4eaf4067bfc6e811cbe9053f
    projectVersion: "1.0-test"
    maxInTransit: 8
    timeoutMs: 2000
  rotator:
    path: ./api-%DATE%.log
    frequency: daily
    maxLogFiles: 5
    maxLogSize: "30m"
    level: trace
  # requestIdHeader: X-Request-ID
db:
  debug: true
  api:
    # GFP test mongodb (gfptest@gfp-test, sharded, nam api)
    uri_encrypted: "2:u4T4qsjGgzuUHJ7h:5r1FYYSFRNZLbXsHA0F8kvouNdP0DxB42DLRcaRbssm3BRFSOzCeLLoU_CQsJkBifGLx3vM6UC1Cz1b2PaCHE89TGZGrVSkaXtQXyH4P7oIIQ36VNIHNVv1hvu-M_kkGxw6jGmnLIjHfStax98jAOCghxTvt7-8B8Adb_DG_8dIaDCm3hAih8aXCrbZMLMyl4cuwSGQmQG8JYNaj8V7jeJIoXJa1ikg="
    connectTimeoutMS: 2000
    retryAttempts: 1
    retryDelay: 1500
  cms:
    # GFP CMS test mongodb (test@ssp-test, replica)
    uri_encrypted: "2:ZSccOIJ4aHm0cvtF:oqsJLGFwrW2fHpO-AK0Yo6zYnHrH67AWjlaORo0DJLhW6WOEbh32u0Sqv2GoI9xhUbi1tQtFWmhD2zAKo1m8fSp1Li3tMRk5qhqHF8E2qKo7X-NiklVVeBM-9P4P7uk-NppqTva6eBHUqz5SRrgqAGDBE8Ma1au6Ix0dXg=="
    connectTimeoutMS: 2000
    retryAttempts: 1
    retryDelay: 1500
nubes:
  nam_api:
    bucket: nam_api_dev
    # Nubes 평촌 instance 접속 정보
    gatewayAddress: a-dev.nubes.sto.navercorp.com:8000
nclavis:
  # service_url: https://dev-apis.nclavis.navercorp.com/kms/consumer
  # resource_id: kzJnU9FFRSe1biD_d6U3RgSbThQ=
  # authn: neoid
  #
  # N2C NAT (dev)
  service_url: https://{{ .Values.natIp }}/kms/consumer
  resource_id: kzJnU9FFRSe1biD_d6U3RgSbThQ=
  insecureHttps: true
one_time_download:
  root_url: {{ .Values.app.public_url }}
  hash_key_encrypted: 2:QXoRITAVLr8ewdeK:_zEtb9XchapE9yzJJTKY3iPN2JJu7YoJcYtF_eBw0N-bey-8FdsL5VdGdobWmMDaVy-1Fd5R9mhHR71shbsqwW1zZPZE6GcDeXYWmeno5y0=
  retention_sec:
    # csv_report: 1800
    csv_report: 600
