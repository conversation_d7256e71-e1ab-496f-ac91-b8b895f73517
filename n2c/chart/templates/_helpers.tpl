{{/*
Create default object name
*/}}
{{- define "defaultName" -}}
{{- printf "%s-%s" .Values.n2c.instance .Values.n2c.installation | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{- define "defaultInstanceName" -}}
{{- printf "%s" .Values.n2c.instance | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create dndl labels
*/}}
{{- define "labels" -}}
app.kubernetes.io/name: {{ .Values.n2c.application }}
app.kubernetes.io/instance: {{ .Values.n2c.instance }}
ncc.navercorp.com/installation: "{{ .Values.n2c.installation }}"
{{- end -}}

{{- define "instanceLabels" -}}
app.kubernetes.io/name: {{ .Values.n2c.application }}
app.kubernetes.io/instance: {{ .Values.n2c.instance }}
{{- end -}}

{{/*
Create dndl selectors
*/}}
{{- define "selectors" -}}
app.kubernetes.io/name: {{ .Values.n2c.application }}
app.kubernetes.io/instance: {{ .Values.n2c.instance }}
ncc.navercorp.com/installation: "{{ .Values.n2c.installation }}"
{{- end -}}

{{- define "instanceSelectors" -}}
app.kubernetes.io/name: {{ .Values.n2c.application }}
app.kubernetes.io/instance: {{ .Values.n2c.instance }}
{{- end -}}
