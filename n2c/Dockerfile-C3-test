FROM reg.navercorp.com/gfp/base/maven:3.8.5 as builder

# ARG PROFILE={test,stage,real}
ARG PROFILE=test
RUN echo PROFILE: $PROFILE

WORKDIR /home1/irteam

# C3 설정 정보
COPY conf/c3/test/ ./conf

# sparkling-s 메이븐 빌드
COPY pom.xml ./
RUN --mount=type=cache,target=/home1/irteam/.m2,id=sparkling-maven-cache,uid=500,gid=500 \
	mvn -Dmaven.repo.local=/home1/irteam/.m2/repository dependency:go-offline

COPY src ./src
RUN --mount=type=cache,target=/home1/irteam/.m2,id=sparkling-maven-cache,uid=500,gid=500 \
	mvn -Dmaven.repo.local=/home1/irteam/.m2/repository package -P ${PROFILE}

# C3 Docker Image 가져오기
FROM reg.navercorp.com/c3/c3s-pan-env:centos7-20240905

USER root

# /home1/irteam/apps 경로 설정
ARG HOME=/home1/irteam
ARG APPS_DIR=$HOME/apps

RUN mkdir $APPS_DIR
WORKDIR $APPS_DIR

# spark 3.2.4 다운로드
ARG SPARK=spark-3.2.4-bin-without-hadoop
ADD https://archive.apache.org/dist/spark/spark-3.2.4/$SPARK.tgz .

# spark 3.2.4 설치
RUN tar -xzf $SPARK.tgz --no-same-owner -C $APPS_DIR \
	&& rm $SPARK.tgz

ARG SPARK_HOME=$APPS_DIR/$SPARK

# C3 conf 복사
COPY --from=builder /home1/irteam/conf $HOME/conf

# C3 spark-env.sh 설정 적용
RUN cp /etc/spark/conf/spark-env.sh $SPARK_HOME/conf/spark-env.sh

# C3 spark-defaults.conf 설정 적용
RUN cp $HOME/conf/spark-defaults.conf $SPARK_HOME/conf/spark-defaults.conf

# C3 Hadoop 설정 적용
RUN cp $HOME/conf/krb5.conf /etc/krb5.conf
RUN cp $HOME/conf/hadoop/* /etc/hadoop/conf/

# /home1/irteam/apps 경로 권한을 irteam으로 변경
RUN chown -R irteam:irteam $APPS_DIR

USER irteam

# airflow에서 kubectl apply 할 때 적용되는 환경 변수
ENV SPARK_HOME=$SPARK_HOME \
    PATH=$SPARK_HOME/bin:$PATH

# kubectl exec로 들어갈 때 스파크 환경을 맞춰주기 위해 .bashrc 설정
RUN echo "export SPARK_HOME=$SPARK_HOME" >> $HOME/.bashrc \
	&& echo "export PATH=$SPARK_HOME/bin:$PATH" >> $HOME/.bashrc \
	&& echo "alias ll='ls -al'" >> $HOME/.bashrc

# multi-stage build의 첫번째 이미지에서 sparkling-s.jar 만 가져오기
WORKDIR $HOME
COPY --from=builder /home1/irteam/jar/sparkling-s.jar ./sparkling-s.jar

CMD ["bash"]
