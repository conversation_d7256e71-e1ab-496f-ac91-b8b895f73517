apiVersion: batch/v1
kind: Job
metadata:
    name: sample-c3-job
spec:
    ttlSecondsAfterFinished: 86400
    template:
        spec:
            containers:
            -   name: spark-submit
                image: reg.navercorp.com/gfp/sparkling-stage:166d587-2308071246
                command:
                - spark-submit
                args:
                - "--class"
                - "com.navercorp.gfp.c3.Example1"
                - "--num-executors"
                - "1"
                - "--executor-cores"
                - "2"
                - "--executor-memory"
                - "1g"
                - "--deploy-mode"
                - "cluster"
                - "--master"
                - "yarn"
                - "--queue"
                - "biz_gep"
                - "--conf"
                - "spark.eventLog.dir=hdfs://pgcm/user/gfp-data/spark-history/"
                - "--conf"
                - "spark.eventLog.enabled=false"
                - "--conf"
                - "spark.executor.extraJavaOptions=-XX:+UseG1GC"
                - "--conf"
                - "spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922"
                - "--conf"
                - "spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro"
                - "--conf"
                - "spark.driver.extraJavaOptions=-Dhdp.version=*******-78 -XX:+UseG1GC"
                - "--conf"
                - "spark.hadoop.dfs.nameservices=pgcm,pg01,pg07"
                - "--conf"
                - "spark.kerberos.access.hadoopFileSystems=hdfs://pgcm,hdfs://pg01,hdfs://pg07"
                - "--conf"
                - "spark.kerberos.keytab=/home1/irteam/apps/c3/gfp-data.keytab"
                - "--conf"
                - "spark.kerberos.principal=<EMAIL>"
                - "--conf"
                - "spark.serializer=org.apache.spark.serializer.KryoSerializer"
                - "--conf"
                - "spark.sql.caseSensitive=true"
                - "--conf"
                - "spark.sql.parquet.mergeSchema=true"
                - "--conf"
                - "spark.sql.warehouse.dir=hdfs://pg07/user/gfp-data/apps/spark/warehouse"
                - "--conf"
                - "spark.yarn.am.extraJavaOptions=-Dhdp.version=*******-78"
                - "--conf"
                - "spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922"
                - "--conf"
                - "spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro"
                - "--conf"
                - "spark.yarn.archive=hdfs://pg07/user/gfp-data/apps/spark/3.2.1/spark3.2.1_jars.tar.gz"
                - "--conf"
                - "spark.yarn.submit.waitAppCompletion=false"
                - "/home1/irteam/sparkling-s.jar"
                - "64cb57f38ede8e39623f9f49"
                - "20230701"
                volumeMounts:
                -   name: c3s-keytab # 이게 가리키는 것은 spec.template.spec.volumes.name = 'c2s-keytab'을 의미함
                    mountPath: /home1/irteam/apps/c3/gfp-data.keytab
                    subPath: gfp-data.keytab # /home1/irteam/apps/c3 경로 아래의 다른 파일을 유지하고 gfp-data.keytab만 마운트
            volumes:
            -   name: c3s-keytab
                secret:
                    secretName: c3s-gfp-data-keytab
            restartPolicy: Never
    backoffLimit: 0
