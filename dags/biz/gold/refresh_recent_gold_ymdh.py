"""
### 최근 생성된 골드 인덱스 일시 갱신

#### 위키
- [https://wiki.navercorp.com/pages/viewpage.action?pageId=1397000986](https://wiki.navercorp.com/pages/viewpage.action?pageId=1397000986)

#### 개요
- 최근 골드 로그 적재 일시 갱신을 위해 배치 서버를 호출한다.

#### 주기
- 없음
- spark_goldsmith의 실행에 따름
"""
import logging
import os

import pendulum
import requests
import tenacity
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator

from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, BATCH_SERVER_ADDRESS

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [REFRESH-RECENT-GOLD-YMDH]'

# Batch 호출 주소
_REQ_URL = BATCH_SERVER_ADDRESS + '/batch/gold/recentymdh/refresh'


def _request(url: str):
    def _call_and_check():
        response = requests.get(url, timeout=60 * 5)
        response.raise_for_status()
        return response

    retry_obj = tenacity.Retrying(
        wait=tenacity.wait.wait_fixed(wait=3),
        stop=tenacity.stop.stop_after_attempt(3),
        retry=tenacity.retry_if_exception_type(requests.exceptions.HTTPError)
    )

    res = retry_obj(_call_and_check)
    logging.info(f'{_LOG_PREFIX} retry stat: attempts={retry_obj.statistics["attempt_number"]}')
    logging.info(f'{_LOG_PREFIX} status code: {res.status_code}')
    logging.info(f'{_LOG_PREFIX} response:\n{res.text}')

    jres = res.json()

    if jres.get('code') != '200':
        return False

    return True


def _refresh_recent_gold_ymdh():
    logging.info(f'{_LOG_PREFIX} url={_REQ_URL}')

    res = _request(_REQ_URL)
    return res


with DAG(
        DAG_ID,
        description='골드 인덱스 최근 적재 일시 갱신',
        default_args={
            'owner': 'ins.cho',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        start_date=pendulum.datetime(2022, 11, 1, tz=DEFAULT_TZ),
        schedule_interval=None,
        tags=['gold', 'batch', 'refresh', 'recent_ymdh', 'ins.cho'],
        catchup=False,
) as dag:
    dag.doc_md = __doc__

    # 골드 로그 최근 적재 일시 갱신
    refresh_recent_gold_ymdh = PythonOperator(
        task_id='refresh_recent_gold_ymdh',
        python_callable=_refresh_recent_gold_ymdh,
    )

    # 파이프라인
    refresh_recent_gold_ymdh
