"""
### 최근 골드 인덱스 적재 일시 갱신(스테이징 용)

#### 개요
- 스테이징 용 골드 인덱스 최근 적재일시 업데이트 DAG
- 리얼에서는 refresh_recent_gold_ymdh.py를 쓰는데, 배치 서버는 스테이지가 없기 때문에 DAG에서 배치서버와 동일한 로직 구현하여 실행

#### 주기
- 없음
- spark_goldsmith의 실행에 따름
"""
import logging
import os

import pendulum
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator

from core import c3
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, PROFILE_TEST, PROFILE, GOLD_HOME
from core.dao import environment_dao

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [REFRESH-RECENT-GOLD-YMDH]'


def _refresh_recent_gold_ymdh():
    index_types = ['adunit', 'adprovider']

    # 마지막 적재일시
    env1 = str(environment_dao.get_environment_value_by_name('gold-index-recent-accumulation-ymdh'))
    last_gold_date = pendulum.from_format(env1, 'YYYYMMDDHH', DEFAULT_TZ)
    recent_gold_ymdh = last_gold_date.format('YYYYMMDDHH')  # while loop에서 사용할 변수
    last_gold_ymdh = recent_gold_ymdh  # 로그 찍기 용 변수
    logging.info(f'\n골드 인덱스 최근 적재일시 업데이트.. last_gold_ymdh:{last_gold_ymdh}')

    current = last_gold_date.add(hours=1)  # 마지막 적재일시 + 1 시간부터 조사
    now = pendulum.now(tz=DEFAULT_TZ).set(minute=0, second=0, microsecond=0)

    while current < now:
        current_ymdh = current.format('YYYYMMDDHH')
        yyyy = current.format('YYYY')
        mm = current.format('MM')
        dd = current.format('DD')
        hh = current.format('HH')

        # 광고유닛/광고공급자 인덱스 존재 여부 검사
        both = [0, 0]
        for i, index_type in enumerate(index_types):
            index_type = index_types[i]
            file_path = f'{GOLD_HOME}/{index_type}/{yyyy}/{mm}/{dd}/{hh}/_GOLD_SUCCESS'
            is_exist = c3.exists(file_path)
            both[i] = 1 if is_exist else 0

        # 둘 다 있으면 해당 시간으로 최근 적재 일시 갱신
        is_both_exist = both[0] and both[1]
        if is_both_exist:
            if PROFILE == PROFILE_TEST:
                # 테스트인 경우는 중간에 이가 빠지는 경우가 있어 순차 적재가 아니더라도
                # 마지막으로 쌓은 시간대를 최근 적재일시로 함
                recent_gold_ymdh = current_ymdh
            else:
                # 중간에 이가 빠지지 않는 경우만 설정
                left = pendulum.from_format(recent_gold_ymdh, 'YYYYMMDDHH', DEFAULT_TZ).add(hours=1) \
                    .set(minute=0, second=0, microsecond=0)
                right = pendulum.from_format(current_ymdh, 'YYYYMMDDHH', DEFAULT_TZ) \
                    .set(minute=0, second=0, microsecond=0)

                if left == right:
                    recent_gold_ymdh = current_ymdh

            logging.info(f'골드 인덱스 최종 적재일시 검사중. {last_gold_ymdh} 이후 {current_ymdh} 존재함')
        else:
            logging.info(f'골드 인덱스 최종 적재일시 검사중. {last_gold_ymdh} 이후 {current_ymdh} 존재하지 않음')

        current = current.add(hours=1)

    environment_dao.set_environment('gold-index-recent-accumulation-ymdh', recent_gold_ymdh.replace('-', '').replace('/', ''))


with DAG(
        DAG_ID,
        description='골드 인덱스 최근 적재/컴팩션 일시 갱신',
        default_args={
            'owner': 'ins.cho',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
        schedule_interval=None,
        tags=['gold', 'batch', 'refresh', 'recent_ymdh', 'ins.cho'],
        catchup=False,
) as dag:
    dag.doc_md = __doc__

    # 골드 인덱스 최근 적재 일시 갱신
    refresh_recent_gold_ymdh = PythonOperator(
        task_id='refresh_recent_gold_ymdh',
        python_callable=_refresh_recent_gold_ymdh,
    )

    # 파이프라인
    refresh_recent_gold_ymdh
