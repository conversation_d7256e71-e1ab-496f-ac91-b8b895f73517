"""
### Shortform 리포트 스파크 집계

#### 위키
- [15-4. Shortform 리포트 생성](https://wiki.navercorp.com/pages/viewpage.action?pageId=1884298292)

#### 주기
- 정규   : trigger_shortform_report_regular 에 따름
- 수동   : trigger_shortform_report_manual 에 따름
- 재처리 : trigger_shortform_report_reprocess 에 따름

#### config
- target_ymd: YYYYMMDD
- by_new_source_file: 새로운 소스파일로 집계할 지 여부. trigger_shortform_report_manual 에서만 설정 가능.
						정규실행일 경우는 항상 다운로드 받아야하니 default= True
"""

import logging
import os
from functools import partial

import pendulum
from airflow.exceptions import AirflowException
from airflow.models import DAG
from airflow.operators.python import PythonOperator
from airflow.sensors.python import PythonSensor

from biz.shortform import shortform_report_dao
from biz.shortform.shortform_report_dao import get_shortform_target_publisher
from core import utils, c3
from core.base import DEFAULT_TZ, PROFILE, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD, XCOM_PUBLISHER_IDS, \
	PROFILE_LOCAL, PROFILE_TEST, PROFILE_STAGE, PROFILE_REAL, SPARK_SUBMIT_OPTIONS, \
	POOL_SPARK, SPARKLING_APP_JAR_PATH, SPARKLING_IMAGE, \
	SPARKLING_APP_HARD_LIMIT_1, BATCH_SERVER_ADDRESS, C3_PATH_PREFIX, PROFILE_DEV
from core.dao import job_dao
from core.spark_pool import POOL_SLOT_TRIVIAL_2
from core.spark_submit_op import invoke_job_with_args
from core.spark_task_group import create_task_group

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [SPK-SF-REPORT]'

# Spark
_SPARK_APP_CLASS = 'com.navercorp.gfp.biz.shortform.ShortformDailyAggregator'

# Profile
_PROFILE_SETTINGS = {
	PROFILE_TEST: {
		SPARK_SUBMIT_OPTIONS: """
            --num-executors 1
            --executor-cores 1
            --executor-memory 500m
			--conf spark.hadoop.mapred.output.compress=true
            --conf spark.hadoop.mapred.output.compression.codec=org.apache.hadoop.io.compress.GzipCodec
            """.split()
	},
	PROFILE_REAL: {
		SPARK_SUBMIT_OPTIONS: """
            --num-executors 4
            --executor-cores 2
            --executor-memory 1g
            """.split()
	}
}
_PROFILE_SETTINGS[PROFILE_LOCAL] = _PROFILE_SETTINGS[PROFILE_DEV] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_STAGE] = _PROFILE_SETTINGS[PROFILE_REAL]

# Batch URL
_DOWNLOAD_URL = BATCH_SERVER_ADDRESS + '/batch/shortform/download?ymd={ymd}'
_UPLOAD_URL = BATCH_SERVER_ADDRESS + '/batch/shortform/upload?ymd={ymd}'

# 디폴트 3시간
_LOG_CHECK_TIMEOUT = 10800


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	_init_settings(**context)
	_print_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
	:param context:
	:return:
	"""
	target_ymd = context['params'][XCOM_TARGET_YMD]
	if target_ymd:
		target_ymd = str(target_ymd).strip()
		pendulum.from_format(target_ymd, 'YYYYMMDD')

		context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)
	else:
		# target_ymd 가 없으면 에러
		logging.error(f'There is not "params.target_ymd"')
		raise AirflowException('There is not "params.target_ymd"')

	# Shortform report 대상 매체 id 목록 정보 추출
	publisher_ids = get_shortform_target_publisher()

	if publisher_ids:
		context['ti'].xcom_push(XCOM_PUBLISHER_IDS, publisher_ids)
	else:
		# publisher_ids 가 없으면 에러
		logging.error(f'There is not "publisher_ids"')
		raise AirflowException('There is not "publisher_ids"')


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK}
Airflow Pool Slot: {POOL_SLOT_TRIVIAL_2}

Profile: {PROFILE}
Spark Submit Options: {_PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS]}

Params:
    target_ymd= {context['ti'].xcom_pull(key=XCOM_TARGET_YMD)}
    by_new_source_file= {context['params']['by_new_source_file']}
    publisher_ids= {context['ti'].xcom_pull(key=XCOM_PUBLISHER_IDS)}
------------------------------------------------------------------------------------------
''')


def _is_exists_success_file(**context):
	"""
	shortform report 경로에 _SUCCESS 가 있는지 확인
		- /data/log/gfp/shortform/yyyy/mm/dd/_SUCCESS
	:param context:
	:return:
	"""

	ymd = pendulum.from_format(context['ti'].xcom_pull(key=XCOM_TARGET_YMD), 'YYYYMMDD')
	success_file_path = f'{C3_PATH_PREFIX}/shortform/{ymd.format("YYYY/MM/DD")}/_SUCCESS'
	logging.info(f'{_LOG_PREFIX} success_file_path= {success_file_path}')
	if not c3.exists(success_file_path):
		return False
	return True


def _request_cuve_download(**context):
	"""
	shortform 리포트 집계를 위해 필요한 기여도 파일을 네이버 메인측 cuve 로부터 다운로드 받기 위해 배치서버로 요청.
	실제 요청은 real/stage 환경에서만 보내고, 그 외의 환경에서는 요청없이 통과시켜 다음 task 를 진행하도록 함.
	기여도 소스 파일을 새로 갱신하여 집계하려면 trigger_shortform_report_manual 에서 'by_new_source_file'= True 설정

	_request_cuve_upload 와 달리 PythonSensor 로 동작하기 때문에 각 요청별 timeout 을 짧게 가져가 poke_interval 과 싱크를 맞춤
	"""
	by_new_source_file = context['params']['by_new_source_file']
	if PROFILE not in [PROFILE_REAL, PROFILE_STAGE]:
		logging.info(f'{_LOG_PREFIX} PROFILE={PROFILE}, cuve 다운로드 불가능하므로 배치서버 호출하지 않고 다음 task 를 위해 통과')
		# operator 가 PythonSensor 이므로 True 반환하여 다운로드 요청은 하지 않되, 일단 통과시키고 다음 task 실행
		return True
	elif not by_new_source_file:
		logging.info(f'{_LOG_PREFIX} by_new_source_file={by_new_source_file}, 기존 기여도 파일로 재집계. URL 호출하지 않음')
		return True
	else:
		target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
		url = _DOWNLOAD_URL.format(ymd=target_ymd)
		logging.info(f'{_LOG_PREFIX} request url={url}')

		try:
			# cuve 다운로드 요청. _request_cuve_upload 와 다르게 default timeout 사용
			res = utils.request(url)

			if res.get('code') == 200:
				return True
			elif res.get('code') == 500:
				logging.info(f'cuve 다운로드 요청 실패 ( res= {res} )')
				return False
			else:
				raise AirflowException(f'cuve 다운로드 요청 실패 ( res= {res} )')

		except Exception as ex:
			raise AirflowException(f'target_ymd= {target_ymd}, url= {url}\n ex= {ex}')


def _request_cuve_upload(**context):
	"""
	shortform 리포트 파일을 cuve 에 업로드 하기 위해 배치서버로 요청.
	실제 요청은 real/stage 환경에서만 보내며, 그 외의 환경에서는 요청없이 task 종료.

	_request_cuve_donwload 와 달리 PythonOperator 로 동작하기 때문에 업로드 성공할 때 까지 대기하지 않음
	"""
	if PROFILE not in [PROFILE_REAL, PROFILE_STAGE]:
		logging.info(f'{_LOG_PREFIX} PROFILE={PROFILE}, cuve 업로드 불가능하므로 배치서버 호출하지 않고 종료')
		# on_success_callback 으로 job 삭제
		return

	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	url = _UPLOAD_URL.format(ymd=target_ymd)
	logging.info(f'{_LOG_PREFIX} request url={url}')

	try:
		# cuve 업로드 요청 (타임아웃 30분)
		res = utils.request(url, timeout=60 * 30)

		if res.get('code') != 200:
			raise AirflowException(f'cuve 업로드 요청 실패 ( res= {res} )')

	except Exception as ex:
		raise AirflowException(f'target_ymd= {target_ymd}, url= {url}\n ex= {ex}')


def _clean_up_job(is_success: bool, context: dict):
	"""
	성공했다면 job 삭제
	실패했다면 retryCnt 증가시키고 running=0 설정

	:param context:
	:return:
	"""
	job_dao.clean_up(_DAG_ID, context['ti'].xcom_pull(key=XCOM_TARGET_YMD), is_success)


def _get_app_args(context: dict):
	"""
	spark-submit 시, 넘겨줄 파라미터 정보
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	target_publisher_ids = context['ti'].xcom_pull(key=XCOM_PUBLISHER_IDS)
	return [target_ymd, target_publisher_ids]


with DAG(
		_DAG_ID,
		description='Invoke spark app for shortform report',
		tags=['spark', 'shortform', 'daily', 'hdfs', 'aida', 'cuve', 'ins.cho'],
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			XCOM_TARGET_YMD: '',
			'by_new_source_file': True
		},
		start_date=pendulum.datetime(2023, 11, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	# 환경 설정
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 중복 실행 여부 확인
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=job_dao.check_dup_run,
		op_args=[
			_DAG_ID,
			XCOM_TARGET_YMD
		]
	)

	# 해당 시간대의 Jobs.running=1 로 추가
	upsert_job = PythonOperator(
		task_id='upsert_job',
		python_callable=job_dao.upsert_job,
		op_args=[
			_DAG_ID,
			XCOM_TARGET_YMD
		]
	)

	# 실버그레이 로그 적재 확인
	# 10분마다 재시도, 3시간 동안
	wait_for_silvergrey_log = PythonSensor(
		task_id='wait_for_log',
		python_callable=shortform_report_dao.is_ready_silvergrey_log,
		mode='reschedule',
		poke_interval=60 * 10,
		timeout=_LOG_CHECK_TIMEOUT,
		retries=2,
		on_failure_callback=partial(_clean_up_job, False)
	)

	# 기여도파일 다운로드 요청 및 확인
	# 20분 마다 재시도, 각 시도에 총 3번의 요청, 각 요청의 timeout=5분으로 이루어 짐
	request_contribution_report_download = PythonSensor(
		task_id='request_contribution_report_download',
		python_callable=_request_cuve_download,
		mode='reschedule',
		poke_interval=60 * 20,
		timeout=_LOG_CHECK_TIMEOUT,
		retries=2,
		on_failure_callback=partial(_clean_up_job, False)
	)

	# 스파크 집계
	task_group_id = 'spark_run_shortform_report'
	spark_run_shortform_report = create_task_group(
		task_group_id,
		tooltip=_SPARK_APP_CLASS,
		spark_pool=POOL_SPARK,
		pool_slot=POOL_SLOT_TRIVIAL_2,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': _SPARK_APP_CLASS,
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			'dag_id': _DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(_DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS,
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT_1,
		wait_showup_failure_cb=partial(_clean_up_job, False),
		wait_complete_failure_cb=partial(_clean_up_job, False),
		conclude_app_failure_cb=partial(_clean_up_job, False)
	)

	# _SUCCESS 파일 확인
	# 10분마다 재시도, 1시간 동안
	wait_for_report_success = PythonSensor(
		task_id='wait_for_report_success',
		python_callable=_is_exists_success_file,
		mode='reschedule',
		poke_interval=60 * 10,
		timeout=60 * 60 * 1,
		retries=2,
		on_failure_callback=partial(_clean_up_job, False)
	)

	# batch server에 cuve로 report file 업로드하도록 요청
	# 성공 시 Jobs 다큐먼트 삭제
	request_shortform_report_upload = PythonOperator(
		task_id='request_report_upload',
		python_callable=_request_cuve_upload,
		on_failure_callback=partial(_clean_up_job, False),
		on_success_callback=partial(_clean_up_job, True)
	)

	setup >> check_dup_run >> upsert_job \
	>> wait_for_silvergrey_log >> request_contribution_report_download >> spark_run_shortform_report \
	>> wait_for_report_success >> request_shortform_report_upload
