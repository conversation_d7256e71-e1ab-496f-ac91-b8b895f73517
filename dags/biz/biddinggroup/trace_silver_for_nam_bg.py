"""
### Silver 처리에 의한 NAM Bidding Group 재처리 job 생성

#### 위키
- [12-4. SilverTrace 감지](https://wiki.navercorp.com/spaces/GFP/pages/3608229287)

#### 개요
- SilverTrace 컬렉션에서 트레이스를 한 건씩 가져와서 Jobs 컬렉션에 spark_nam_biddinggroup job 추가
- job에 대한 실제 처리는 trigger_biddinggroup_reprocess 에서 함
- 동시에 하나의 DAG RUN만 허용

#### 주기
- 5분 마다

#### config
- 없음
"""
import logging
import os

import pendulum
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator, ShortCircuitOperator
from bson import ObjectId

from biz.biddinggroup import spark_nam_biddinggroup
from core import utils
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, XCOM_SILVER_TRACE_ID, XCOM_TARGET_YMD
from core.dao import job_dao, silver_trace_dao

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRACE-SILVER]'

_KIND = 'trace_silver'


def _get_silver_trace(**context):
	"""
	재처리가 필요한 SilverTrace 내역이 있다면 날짜 오름차순으로 한 건만 조회

	:param context: 
	:return: 
	"""
	match = {
		# '$lt' 이므로 D-1 이전의 실버
		'date': {'$lt': pendulum.now(DEFAULT_TZ).format('YYYYMMDD')},
		# NAM Bidding Group 재처리가 안 된 실버 (필드 자체가 없거나 null 값)
		'namBgCompletedAt': None,
	}
	logging.info(f'{_LOG_PREFIX} get_trace(match) = {utils.prettyStrOfMongoDoc(match)}')
	sort = [('date', 1)]
	s_trace = silver_trace_dao.get_trace(match, sort)
	if s_trace:
		ymd = s_trace.get('date')
		s_trace_id = str(s_trace.get('_id'))
		context['ti'].xcom_push(XCOM_TARGET_YMD, ymd)
		context['ti'].xcom_push(XCOM_SILVER_TRACE_ID, s_trace_id)

		logging.info(f'처리할 ymd={ymd} s_trace_id={s_trace_id}')
		return True
	else:
		logging.info(f'처리할 실버 없음')
		return False


def _insert_jobs(**context):
	"""
	실버 재처리에 따른 NAM Bidding Group job 추가
	추가된 job은 trigger_biddinggroup_regular 에 의해 순차적으로 재처리됨

	:param context:
	:return:
	"""
	ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	s_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)
	logging.info(f'처리할 {ymd} s_trace_id={s_trace_id}')

	# 재처리 할 spark_nam_biddinggroup job 생성
	filter = {
		'type': spark_nam_biddinggroup.DAG_ID,
		'datetime': ymd,
		# trace_silver 또는 reprocess
		'detail.kind': {'$in': [_KIND, 'reprocess']},
		'detail.silverTrace_id': ObjectId(s_trace_id),
		'manual': 0,
	}
	job = job_dao.get_job(filter)
	if job:
		if job['running'] == 1:
			logging.warning(f'이미 실행 중. job={job}')
			return

		job_dao.update_job(filter, {
			'$set': {
				# 같은 ymd 에 대한 재처리이더라도 처리된 실버의 시간이 다를 수 있으므로 retryCnt 를 초기화 (재처리이므로 0으로 설정)
				'retryCnt': 0,
				'modifiedAt': pendulum.now(DEFAULT_TZ)
			}
		})
		logging.warning(f'이미 있으므로 추가하지 않고 retryCnt=0으로 업데이트. job={job}')
	else:
		# job이 없다면 추가
		job = {
			'type': spark_nam_biddinggroup.DAG_ID,
			'datetime': ymd,
			'detail': {
				'kind': _KIND,  # silver_trace 에 의한 NAM Bidding Group 처리
				'silverTrace_id': ObjectId(s_trace_id),
			},
			'manual': 0,
			'running': 0,
			# 재처리이므로 0으로 설정
			'retryCnt': 0,
		}
		inserted_id = job_dao.insert_job(job)
		logging.info(f'{_LOG_PREFIX} {ymd} kind={_KIND} silver_trace_id={s_trace_id} job 추가. job_id={inserted_id}')

	logging.info(f'{_LOG_PREFIX} Jobs에 추가 완료')


with DAG(
		_DAG_ID,
		description='Silver Trace 에 의한 NAM Bidding Group 재처리 job 생성 DAG',
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2025, 5, 1, tz=DEFAULT_TZ),
		schedule_interval="*/5 * * * *",  # 매 5분마다
		tags=['nam', 'biddinggroup', 'bg', 'trace', 'silver', 'ins.cho'],
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# 재처리해야 할 실버가 있을 때만 다운스트림 태스크 진행
	# SilverTrace 에서 일자 오름차순으로 1개만 추출
	get_silver_trace = ShortCircuitOperator(
		task_id='get_silver_trace',
		python_callable=_get_silver_trace,
	)

	# NAM Bidding Group 재처리를 위한 job 추가
	insert_jobs = PythonOperator(
		task_id='insert_jobs',
		python_callable=_insert_jobs,
	)

	# 파이프라인
	get_silver_trace >> insert_jobs
