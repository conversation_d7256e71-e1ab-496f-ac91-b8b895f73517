"""
### GFP Bidding Group Spark 집계

#### 위키
- [12-5. GFP 비딩그룹 지표 생성](https://wiki.navercorp.com/spaces/GFP/pages/3608229501)

#### 개요
- gfp cms 에 제공할 비딩 그룹 지표 집계

#### 주기
- 정규   : spark_silversmith 에 따름
- 수동   : trigger_biddinggroup_manual 에 따름
- 재처리 : trigger_biddinggroup_reprocess 에 따름

#### config
- target_ymdh: YYYYMMDDHH
"""
import logging
import os
from datetime import timedelta
from functools import partial
from typing import List

import pendulum
from airflow.exceptions import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.sensors.python import PythonSensor
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType
from bson import ObjectId

from core import utils
from core.base import SPARKLING_IMAGE, SPARKLING_APP_JAR_PATH, POOL_SPARK, SPARKLING_APP_HARD_LIMIT_1, \
	PROFILE, PROFILE_TEST, PROFILE_REAL, ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, XCOM_TARGET_YMDH, PROFILE_STAGE, \
	PROFILE_DEV, SPARK_SUBMIT_OPTIONS, PROFILE_LOCAL
from core.dao.environment_dao import is_ready_silver_log, get_environment_value_by_name
from core.dao.job_dao import insert_job, get_jobs, get_job, update_job, clean_up, delete_job, set_to_run_flag
from core.spark_pool import POOL_SLOT_LIGHT_10
from core.spark_submit_op import invoke_job_with_args
from core.spark_task_group import create_task_group

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = ".......... [SPK-GFP-BG]"

# Silver Log
_SILVER_LOG_CHECK_TIMEOUT = int(get_environment_value_by_name('silver-log-check-timeout'))

# Spark
_SPARK_APP_CLASS = 'com.navercorp.gfp.biz.cms.CmsAdProviderPlaceBiddingGroupAggregator'

# Profile
_PROFILE_SETTINGS = {
	PROFILE_TEST: {
		SPARK_SUBMIT_OPTIONS: """
            --num-executors 1
            --executor-cores 1
            --executor-memory 512m
        """.split(),
	},
	PROFILE_REAL: {
		SPARK_SUBMIT_OPTIONS: """
            --num-executors 64
            --executor-cores 3
            --executor-memory 2g
            --conf spark.executor.memoryOverhead=1g
        """.split(),
	},
}

_PROFILE_SETTINGS[PROFILE_LOCAL] = _PROFILE_SETTINGS[PROFILE_DEV] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_STAGE] = _PROFILE_SETTINGS[PROFILE_REAL]


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정

	:param context:
	:return:
	"""
	_set_target_ymdh(**context)
	_print_settings(**context)


def _set_target_ymdh(**context):
	"""
	trigger 로부터 전달 받은 집계 대상 일시 설정

	:param context:
	:return:
	"""
	target_ymdh = context['params'].get(XCOM_TARGET_YMDH)

	if target_ymdh:
		target_ymdh = str(target_ymdh).strip()
		pendulum.from_format(target_ymdh, 'YYYYMMDDHH')
		logging.info(f'{_LOG_PREFIX} Using target_ymdh from "params". target_ymdh={target_ymdh}')

	else:
		raise AirflowException('params 이 입력되지 않음. (target_ymdh)')

	context['ti'].xcom_push(key=XCOM_TARGET_YMDH, value=target_ymdh)


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK}
Airflow Pool Slot: {POOL_SLOT_LIGHT_10}

Profile: {PROFILE}
Spark Submit Options: {_PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS]}
Params:
    target_ymdh={context['params'].get(XCOM_TARGET_YMDH)}
------------------------------------------------------------------------------------------
''')


def _check_dup_run(**context):
	"""
	중복실행을 방지하기 위한 방어 로직
	:param context:
	:return:
	"""
	target_ymdh = context['ti'].xcom_pull(key=XCOM_TARGET_YMDH)
	my_dag_run_id = context['dag_run'].run_id
	doc = get_job({'type': DAG_ID, 'datetime': target_ymdh, 'running': 1})

	if doc:
		running_dagRuns: List["DagRun"] = DagRun.find(dag_id=DAG_ID, state=DagRunState.RUNNING)
		for dagRun in running_dagRuns:
			your_run_id = dagRun.run_id
			your_target_ymdh = dagRun.conf[f'{XCOM_TARGET_YMDH}']
			if my_dag_run_id != your_run_id and target_ymdh == your_target_ymdh:
				raise AirflowException(f'이미 실행중인 DagRun이 있음. '
				                       f'dagRrunId={dagRun.run_id}({your_target_ymdh}) '
				                       f'target_ymdh={target_ymdh}')
	else:
		pass


def _upsert_job(**context):
	"""
	Params 로 입력된 시간대를 Jobs에 추가. 있으면 스킵.
	'trigger_biddinggroup_manual' 을 통해 실행된 경우만 Jobs.manual = 1

	gfp 비딩그룹지표는 해당 ymd 의 00~hh 까지의 실버로그로 집계됨.
	즉 ymd 당 가장 최근의 시간을 대상으로 하는 스케줄만을 실행해도 되므로
	Jobs 엔 ymd 당 최대 하나의 스케줄만 유지함 (실행 중인 Job 을 포함하면 두개)

	:param context:
	:return:
	"""
	is_insert = True
	target_ymdh = context['ti'].xcom_pull(key=XCOM_TARGET_YMDH)
	first_ymdh_in_target_ymd = target_ymdh[:-2] + '00'
	before_ymdh_jobs = get_jobs(
		{'type': DAG_ID, 'datetime': {'$gte': f'{first_ymdh_in_target_ymd}', '$lte': f'{target_ymdh}'}})

	if before_ymdh_jobs:
		latest_job = before_ymdh_jobs[-1]
		common_msg = f'{_LOG_PREFIX} {target_ymdh} 의 집계 결과가 포함할 job 존재.'
		for job in reversed(before_ymdh_jobs):
			if latest_job.get('datetime') == target_ymdh:
				# 이경우 대기중일 수 밖에. 실행중이라면 _check_dup_run 에서 걸렸을 것.
				is_insert = False
				set_to_run_flag(DAG_ID, target_ymdh, 1)
				case_msg = f' datetime={target_ymdh} 이므로 insert 하지 않고 실행. job={job}'
			# 이하의 조건은 target_ymdh 보다 이전의 시간대에만 해당
			elif job.get('running'):
				case_msg = f' 실행 중이므로 삭제하지 않음. job={job}'
			else:
				case_msg = f' 대기 중이므로 삭제(덮어씀). job={job}'
				delete_job({'_id': ObjectId(job.get('_id'))})
			logging.warning(common_msg + case_msg)

	if is_insert:
		new_job = {
			'type': DAG_ID,
			'datetime': target_ymdh,
			'retryCnt': -1,
			'running': 1,  # 대기 상태로 Jobs 에 추가
			'manual': 0 if context['dag_run'].run_type == DagRunType.SCHEDULED else 1,
		}
		insert_job(new_job)
		logging.info(f'{_LOG_PREFIX} {target_ymdh} Jobs 에 추가 {new_job}')


def _is_ready_silver_log(target_ymdh: str, **context):
	"""
	수동 실행 시, 입력한 target_ymd 에 대해 yyyyMMdd23 으로 집계하게 되는데,
	실행 당일의 실제 날짜와 같은 target_ymd 이라면 silver-log-recent-compaction-ymdh 로 실행하여 대기를 피함
	
	:param target_ymdh:
	:param context:
	:return:
	"""
	is_manual = context['dag_run'].run_type == DagRunType.MANUAL
	today = pendulum.now(DEFAULT_TZ).format('YYYYMMDD')
	target_ymdh_is_today = today == target_ymdh[:8]
	if is_manual and target_ymdh_is_today:
		recent_silver_log_datetime = get_environment_value_by_name('silver-log-recent-compaction-ymdh')
		logging.info(f'{_LOG_PREFIX} target_ymdh= {target_ymdh} 은 오늘이므로 23시까지 대기하지 않고 현재 적재된 실버로그({recent_silver_log_datetime}) 로 실행')

		# job 의 datetime 변경
		update_job({'type': DAG_ID, 'datetime': target_ymdh}, {'$set': {'datetime': str(recent_silver_log_datetime)}})

		context['ti'].xcom_push(key=XCOM_TARGET_YMDH, value=recent_silver_log_datetime)
		return True
	else:
		return is_ready_silver_log(target_ymdh)


def _clean_up_job(is_success: bool, context: dict):
	"""
	spark application 의 성공/실패 여부에 따라 해당 Job document 를 처리

	:param is_success:
	:param context:
	:return:
	"""
	clean_up(DAG_ID, context['ti'].xcom_pull(key=XCOM_TARGET_YMDH), is_success)


def _get_app_args(context: dict):
	"""
	spark-submit 시 넘겨줄 파라미터 정보
	:param context:
	:return:
	"""
	target_ymdh = context['ti'].xcom_pull(key=XCOM_TARGET_YMDH)
	logging.info(f'{_LOG_PREFIX} target_ymdh={target_ymdh}')
	return [target_ymdh]


with DAG(
		DAG_ID,
		description='GFP 비딩그룹 지표 집계',
		tags=['spark', 'cms', 'gfp', 'biddinggroup', 'bg', 'hourly', 'ins.cho'],
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2025, 5, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
		params={
			XCOM_TARGET_YMDH: '',
		},
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 중복실행 여부 확인
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=_check_dup_run,
	)

	# 해당 시간대의 Jobs.running=1 인 상태로 job 이 없으면 추가, 있으면 업데이트
	upsert_job = PythonOperator(
		task_id='upsert_job',
		python_callable=_upsert_job
	)

	# 실버로그 컴팩션 확인. 실패하더라도 Jobs.running=0 인 상태로 남음
	wait_for_silver_log = PythonSensor(
		task_id='wait_for_silver_log',
		python_callable=_is_ready_silver_log,
		op_args=[f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMDH}") }}}}'],
		poke_interval=60 * 15,  # 15분마다
		execution_timeout=timedelta(seconds=_SILVER_LOG_CHECK_TIMEOUT),
		on_failure_callback=partial(_clean_up_job, False)
	)

	task_group_id = 'gfp_biddinggroup_stat'
	run_spark_app = create_task_group(
		task_group_id,
		tooltip=_SPARK_APP_CLASS,
		spark_pool=POOL_SPARK,
		pool_slot=POOL_SLOT_LIGHT_10,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': f'{_SPARK_APP_CLASS}',
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMDH}") }}}}',
			'dag_id': DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS,
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMDH}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT_1,
		wait_showup_failure_cb=partial(_clean_up_job, False),
		wait_complete_failure_cb=partial(_clean_up_job, False),
		conclude_app_failure_cb=partial(_clean_up_job, False),
		conclude_app_success_cb=partial(_clean_up_job, True)
	)

	setup >> check_dup_run >> upsert_job >> wait_for_silver_log >> run_spark_app
