"""
### NAM Bidding Group Spark 집계

#### 위키
- [12-6. NAM 비딩그룹 지표 생성](https://wiki.navercorp.com/spaces/GFP/pages/1408857737)

#### 개요
- NAM cms 에 제공할 비딩 그룹 지표 집계
- 집계 단위는 일자별

#### 주기
- 정규   : trigger_nam_biddinggroup_regular 에 따름
- 수동   : trigger_biddinggroup_manual 에 따름
- 재처리 : trigger_biddinggroup_reprocess 에 따름

#### config
- target_ymd: YYYYMMDD
- kind: {'regular'|'trace_silver'(재처리)|'manual'(수동 실행)}
- silver_trace_id
	- kind='regular' 로 실패하여 재처리 또는 kind='trace_silver' 의 첫 재처리 실패 후, kind='reprocess' 로 변경된 경우에만 존재
"""
import logging
import os
from datetime import timedelta
from functools import partial
from typing import List

import pendulum
from airflow.exceptions import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.sensors.python import PythonSensor
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType
from bson import ObjectId

from core import utils
from core.base import (ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, POOL_SPARK, PROFILE, PROFILE_DEV, PROFILE_LOCAL, PROFILE_REAL,
	PROFILE_STAGE, PROFILE_TEST, SPARKLING_APP_HARD_LIMIT_1, SPARKLING_APP_JAR_PATH, SPARKLING_IMAGE,
	SPARK_SUBMIT_OPTIONS, XCOM_KIND, XCOM_SILVER_TRACE_ID, XCOM_TARGET_YMD)
from core.dao import job_dao, silver_trace_dao
from core.dao.environment_dao import get_environment_value_by_name, is_ready_silver_log
from core.dao.job_dao import clean_up_by_filter
from core.spark_pool import POOL_SLOT_LIGHT_10
from core.spark_submit_op import invoke_job_with_args
from core.spark_task_group import create_task_group

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = ".......... [SPK-NAM-BG]"

# Silver Log
_SILVER_LOG_CHECK_TIMEOUT = int(get_environment_value_by_name('silver-log-check-timeout'))

# Spark
_SPARK_APP_CLASS = 'com.navercorp.gfp.biz.biddinggroup.AdProviderPlaceBiddingGroupDailyAggregator'

# Profile
_PROFILE_SETTINGS = {
	PROFILE_REAL: {
		SPARK_SUBMIT_OPTIONS: """
			--num-executors 3
			--executor-cores 4
			--executor-memory 1g
		""".split(),
	},
}
_PROFILE_SETTINGS[PROFILE_LOCAL] = _PROFILE_SETTINGS[PROFILE_DEV] = _PROFILE_SETTINGS[PROFILE_TEST] \
	= _PROFILE_SETTINGS[PROFILE_STAGE] = _PROFILE_SETTINGS[PROFILE_REAL]


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정

	:param context:
	:return:
	"""
	_set_target_ymd(**context)
	_set_params(**context)
	_print_settings(**context)


def _set_target_ymd(**context):
	"""
	trigger 로부터 전달 받은 집계 대상 일자 설정
	spark 집계 단계에서 ymd 에 해당하는 경로를 모두 읽어 처리함

	:param context:
	:return:
	"""
	target_ymd = context['params'].get(XCOM_TARGET_YMD)

	if target_ymd:
		target_ymd = str(target_ymd).strip()
		pendulum.from_format(target_ymd, 'YYYYMMDD')
		logging.info(f'{_LOG_PREFIX} Using target_ymd from "params". target_ymd={target_ymd}')

	else:
		raise AirflowException('params 이 입력되지 않음. (target_ymd)')

	context['ti'].xcom_push(key=XCOM_TARGET_YMD, value=target_ymd)


def _set_params(**context):
	"""
	target_ymd 외의 다른 파라미터에 대한 설정
	:param context:
	:return:
	"""
	kind = context['params'].get(XCOM_KIND)
	silver_trace_id = context['params'].get(XCOM_SILVER_TRACE_ID)

	if kind not in ['regular', 'reprocess', 'manual', 'trace_silver']:
		raise AirflowException(f'kind 파라미터는 "regular", "reprocess", "manual", "trace_silver" 중 하나여야 합니다. kind= {kind}')
	elif kind == 'trace_silver' and not silver_trace_id:
		raise AirflowException(f'kind=trace_silver 인 경우 silver_trace_id 파라미터가 필요합니다. silver_trace_id= {silver_trace_id}')

	context['ti'].xcom_push(key=XCOM_KIND, value=kind)
	context['ti'].xcom_push(key=XCOM_SILVER_TRACE_ID, value=silver_trace_id)


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK}
Airflow Pool Slot: {POOL_SLOT_LIGHT_10}

Profile: {PROFILE}
Spark Submit Options: {_PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS]}
Params:
	target_ymd= {context['ti'].xcom_pull(key=XCOM_TARGET_YMD)}
	kind={context['ti'].xcom_pull(key=XCOM_KIND)}
	silver_trace_id={context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)}
------------------------------------------------------------------------------------------
''')


def _check_dup_run(**context):
	"""
	중복실행을 방지하기 위한 방어 로직
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	dag_run_id = context['dag_run'].run_id
	doc = job_dao.get_job({'type': DAG_ID, 'datetime': target_ymd, 'running': 1})

	if doc:
		running_dag_runs: List["DagRun"] = DagRun.find(dag_id=DAG_ID, state=DagRunState.RUNNING)
		for running_dag_run in running_dag_runs:
			ti = running_dag_run.get_task_instance('setup')
			running_target_ymd = ti.xcom_pull(key=XCOM_TARGET_YMD)
			if dag_run_id != running_dag_run.run_id and target_ymd == running_target_ymd:
				raise AirflowException(f'이미 실행 중인 DagRun 이 있음 ({running_dag_run.run_id} :: {running_target_ymd})')
	else:
		pass


def _upsert_job(**context):
	"""
	해당 ymd 의 Job 추가 또는 running=1 로 업데이트

	:param context:
	:return:
	"""
	target_ymd = context["ti"].xcom_pull(key=XCOM_TARGET_YMD)
	kind = context['ti'].xcom_pull(key=XCOM_KIND)
	s_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)

	filters = {
		'type': DAG_ID, 'datetime': target_ymd,
		'detail.kind': kind,
		# silverTrace_id 가 없다면 필터에서 제외
		**({'detail.silverTrace_id': ObjectId(s_trace_id)} if s_trace_id else {})
	}
	job = job_dao.get_job(filters)

	if job:
		update = {'$set': {
			'running': 1,
			'modifiedAt': pendulum.now(tz=DEFAULT_TZ),
			# 1. kind= 'regular' 로 최초 실행하여 재처리하는 경우 kind= 'reprocess' 로 변경
			# 2. kind= 'trace_silver' 로 재처리하다 실패하여 retryCnt 가 1 이상인 경우도 kind= 'reprocess' 로 변경
			'detail.kind': 'reprocess' if (kind == 'regular' and job['retryCnt'] >= 0)
										  or (kind == 'trace_silver' and job['retryCnt'] >= 1) else kind
		}}
		job_dao.update_job(filters, update)
	else:
		job = {
			'type': DAG_ID,
			'datetime': target_ymd,
			'detail': {
				'kind': kind,
				**({'silverTrace_id': ObjectId(s_trace_id)} if s_trace_id else {})
			},
			'retryCnt': -1,
			'running': 1,
			'manual': 0 if context['dag_run'].run_type == DagRunType.SCHEDULED else 1
		}
		job_dao.insert_job(job)
		logging.info(f'{_LOG_PREFIX} {target_ymd} Jobs 에 추가. {job}')


def _is_ready_silver_log(target_ymd: str, **context):
	"""
	수동 실행 시, 당일의 실제 날짜와 같은 target_ymd 이라면 silver-log-recent-compaction-ymdh 의 시간으로 실행하여 대기를 피함
	
	:param target_ymd:
	:param context:
	:return:
	"""
	is_manual = context['dag_run'].run_type == DagRunType.MANUAL
	today = pendulum.now(DEFAULT_TZ).format('YYYYMMDD')
	target_ymd_is_today = today == target_ymd
	if is_manual and target_ymd_is_today:
		recent_silver_log_ymdh = get_environment_value_by_name('silver-log-recent-compaction-ymdh')
		if recent_silver_log_ymdh[:8] == target_ymd:
			logging.info(f'{_LOG_PREFIX} target_ymd= {target_ymd} 은 오늘이므로 23시까지 대기하지 않고 현재 적재된 실버로그({recent_silver_log_ymdh}) 로 실행')
			return True
		else:
			return is_ready_silver_log(target_ymd)

	else:
		return is_ready_silver_log(target_ymd)


def _update_silver_trace_nam_bg_completed_at(s_trace_id: str):
	"""
	SilverTrace.namBgCompletedAt 업데이트
	:param context:
	:return:
	"""
	now = pendulum.now(DEFAULT_TZ)
	match = {'_id': ObjectId(s_trace_id)}
	update = [{
		'$set': {
			'namBgCompletedAt': now,
			'modifiedAt': now,
		}
	}]
	result = silver_trace_dao.update_trace_one(match, update)
	logging.info(f'{_LOG_PREFIX} _update_silver_trace_nam_bg_completed_at() result={result}')


def _clean_up_job(is_success: bool, context: dict):
	"""
	spark application 의 성공/실패 여부에 따라 해당 Job document 를 처리

	:param is_success:
	:param context:
	:return:
	"""
	s_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)

	filter = {
		'type': DAG_ID,
		'datetime': context['ti'].xcom_pull(key=XCOM_TARGET_YMD),
		'detail.kind': context['ti'].xcom_pull(key=XCOM_KIND),
		**({'detail.silverTrace_id': ObjectId(s_trace_id)} if s_trace_id else {})
	}
	clean_up_by_filter(filter, is_success)

	if is_success and s_trace_id and s_trace_id != '-':
		_update_silver_trace_nam_bg_completed_at(s_trace_id)


def _get_app_args(context: dict):
	"""
	spark-submit 시 넘겨줄 파라미터 정보
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	logging.info(f'{_LOG_PREFIX} target_ymd={target_ymd}')
	return [target_ymd]


with DAG(
		DAG_ID,
		description='NAM 비딩그룹 지표 집계',
		tags=['spark', 'nam', 'biddinggroup', 'bg', 'daily', 'ins.cho'],
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2025, 5, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
		params={
			XCOM_TARGET_YMD: '',
			XCOM_KIND: '',
			XCOM_SILVER_TRACE_ID: ''
		},
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 중복실행 여부 확인
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=_check_dup_run,
	)

	# 해당 시간대의 Jobs.running=1 인 상태로 job 이 없으면 추가, 있으면 업데이트
	upsert_job = PythonOperator(
		task_id='upsert_job',
		python_callable=_upsert_job
	)

	# 실버로그 컴팩션 확인. 실패하더라도 Jobs.running=0 인 상태로 남음
	wait_for_silver_log = PythonSensor(
		task_id='wait_for_silver_log',
		python_callable=_is_ready_silver_log,
		op_args=[f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}'],
		poke_interval=60 * 15,  # 15분마다
		execution_timeout=timedelta(seconds=_SILVER_LOG_CHECK_TIMEOUT),
		on_failure_callback=partial(_clean_up_job, False)
	)

	task_group_id = 'nam_biddinggroup_stat'
	run_spark_app = create_task_group(
		task_group_id,
		tooltip=_SPARK_APP_CLASS,
		spark_pool=POOL_SPARK,
		pool_slot=POOL_SLOT_LIGHT_10,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': f'{_SPARK_APP_CLASS}',
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			'dag_id': DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS,
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT_1,
		wait_showup_failure_cb=partial(_clean_up_job, False),
		wait_complete_failure_cb=partial(_clean_up_job, False),
		conclude_app_failure_cb=partial(_clean_up_job, False),
		conclude_app_success_cb=partial(_clean_up_job, True)
	)

	setup >> check_dup_run >> upsert_job >> wait_for_silver_log >> run_spark_app
