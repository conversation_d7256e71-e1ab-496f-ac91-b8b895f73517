"""
### GFP/NAM Bidding Group 수동 실행

#### 위키
- [12-3. GFP/NAM 수동 처리](https://wiki.navercorp.com/spaces/GFP/pages/1540887122)

#### 개요
- 수동 실행 (스케줄 되지 않음)
- config로 받은 시간대를 Jobs 컬렉션에 추가
- Jobs 에 추가 후 또는 존재하는 spark_XXX_biddinggroup job 실행
- 성공하면 Jobs 다큐먼트를 삭제하고, 실패하면 retryCnt를 증가시킴
- GFP 비딩그룹은 집계 시 yyyyMMddHH 를 args 로 받고, 같은 ymd 인 앞선 시간대를 모두 처리하므로 datetime= yyyyMMdd23 로 처리
  - 실행 당일의 실제 날짜를 입력한다면 silver-log-recent-compaction-ymdh 로 실행하여 대기를 피함

#### config
- target_job: 실행하고자 하는 비딩그룹 지표 타입
	- "GFP", "NAM" 중 하나
- start_date, end_date : 두 값 사이(edge 포함)의 모든 datetime 을 대상으로 하는 스케줄 실행
	- format : "YYYYMMDD"
- target_ymd_list : 입력한 값들에 속하는 datetime 을 대상으로 하는 스케줄 실행
	- comma로 구분된 ymd 를 공백없이 나열
	- format : "YYYYMMDD,YYYYMMDD,YYYYMMDD, .. "
- 세 값 모두 입력한 경우 : from/to 우선
- 모두 입력하지 않은 경우 : 에러
"""
import logging
import os
from time import sleep

import pendulum
from airflow.exceptions import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from airflow.utils.state import DagRunState

from biz.biddinggroup.spark_gfp_biddinggroup import DAG_ID as SPARK_DAG_ID_GFP_BG
from biz.biddinggroup.spark_nam_biddinggroup import DAG_ID as SPARK_DAG_ID_NAM_BG
from core.airflow_api import trigger_dagrun
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, XCOM_TARGET_YMD_LIST, XCOM_TARGET_YMD, XCOM_TARGET_YMDH, \
	XCOM_START_DATE, XCOM_END_DATE, XCOM_KIND
from core.dao import job_dao
from core.utils import get_between_ymd_list

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = ".......... [TRG-BG-MANUAL]"

_XCOM_LATER_YMDH = 'later_ymdh'
_XCOM_TARGET_JOB = 'target_job'

_MAX_RETRY_CNT = 12

_JOB_TYPE = {
	'GFP': SPARK_DAG_ID_GFP_BG,
	'NAM': SPARK_DAG_ID_NAM_BG,
}

_KIND = 'manual'


def _setup(**context):
	_set_job_list(**context)


def _set_job_list(**context):
	"""
	config를 이용해 실행할 job type 과 기간 설정
	:param context:
	:return:
	"""
	start_date = context['params'].get(XCOM_START_DATE)
	end_date = context['params'].get(XCOM_END_DATE)
	target_ymd_list = context['params'].get(XCOM_TARGET_YMD_LIST)
	target_job = context['params'].get(_XCOM_TARGET_JOB)

	dt_list = []

	if start_date or end_date:
		start_date = start_date.strip()
		pendulum.from_format(start_date, 'YYYYMMDD')
		if end_date:
			end_date = end_date.strip()
			pendulum.from_format(end_date, 'YYYYMMDD')
			if start_date > end_date:
				raise AirflowException(f'{XCOM_START_DATE}은 {XCOM_END_DATE} 보다 작거나 같아야 합니다.'
				                       f' {XCOM_START_DATE}={start_date} {XCOM_END_DATE}={end_date}')
			else:
				dt_list = get_between_ymd_list(start_date, end_date)
				logging.info(f'{_LOG_PREFIX} {XCOM_START_DATE} / {XCOM_END_DATE} 를 이용한 실행.'
				             f' {XCOM_START_DATE}={start_date} {XCOM_END_DATE}={end_date} ymd_list={dt_list}')
		else:
			raise AirflowException(f'{XCOM_END_DATE} 기술되지 않음. {XCOM_START_DATE}={start_date}')
	elif target_ymd_list:
		target_ymd_list = target_ymd_list.strip(', ')
		dt_list = target_ymd_list.split(',')
		logging.info(f'{_LOG_PREFIX} {XCOM_TARGET_YMD_LIST}를 이용한 실행. {dt_list}')
	else:
		raise AirflowException(f'config 내에 기간 정보가 기술되지 않음')

	if target_job:
		job_type = target_job.upper()
		context['ti'].xcom_push(_XCOM_TARGET_JOB, _JOB_TYPE[job_type])

		# GFP 비딩그룹은 yyyyMMddHH 를 args 로 받고, 같은 ymd 인 앞선 시간대를 모두 처리하므로 23 시로 처리
		# 같은 날짜, 이전 시간대의 job 에 대한 처리는 spark_gfp_biddinggroup DAG 에서 처리
		if job_type == 'GFP':
			dt_list = [f'{ymd}23' for ymd in dt_list]
	else:
		raise AirflowException(f'config 내에 job type 정보가 기술되지 않음')

	context['ti'].xcom_push(XCOM_TARGET_YMD_LIST, dt_list)


def _trigger_biddinggroup_job(**context):
	"""
	config 입력에 맞는 작업을 실행

	:param context:
	:return:
	"""
	skipped_list = []
	failed_list = []

	job_type = context['ti'].xcom_pull(key=_XCOM_TARGET_JOB)
	dt_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)
	dt_list_len = len(dt_list)

	for idx, dt in enumerate(dt_list):
		# GFP 는 dt= 'yyyyMMddHH' NAM 은 dt= 'yyyyMMdd'
		job = job_dao.get_job({'type': job_type, 'datetime': dt})
		if job:
			if job['manual'] == 0:
				skipped_list.append((dt, 'manual=0. regular/reprocess dag에 의해 처리예정이거나 처리중이므로 스킵'))
				logging.warning(
					f'{_LOG_PREFIX} manual=0. regular/reprocess dag에 의해 처리예정이거나 처리중이므로 스킵({idx + 1}/{dt_list_len}). {job_type}:{dt}')
				can_run = False
			else:
				if job['running'] == 1:
					skipped_list.append((dt, '이미 처리 중'))
					logging.warning(f'{_LOG_PREFIX} 이미 처리 중이므로 스킵({idx + 1}/{dt_list_len}). {job_type}:{dt}')
					can_run = False
				else:
					can_run = True
		else:
			can_run = True

		if can_run:
			logging.warning(f'{_LOG_PREFIX} 진행중({idx + 1}/{dt_list_len}) {dt}')

			config_dt_type = XCOM_TARGET_YMD if job_type == SPARK_DAG_ID_NAM_BG else XCOM_TARGET_YMDH

			# job_type 에 맞는 spark DAG 트리거
			dag_run_id = trigger_dagrun(job_type, pendulum.now(DEFAULT_TZ),
				{
					config_dt_type: dt,
					XCOM_KIND: _KIND
				}
			)
			logging.info(f'{_LOG_PREFIX} {job_type} 트리거. datetime={dt} target_dag_id={job_type} dag_run_id={dag_run_id}')

			# dag이 끝났는지 주기적으로 확인
			while True:
				sleep(60)
				dag_runs = DagRun.find(dag_id=job_type, run_id=dag_run_id)
				if dag_runs:
					dag_run: DagRun = dag_runs.pop()
					if dag_run.get_state() == DagRunState.SUCCESS:
						break
					elif dag_run.get_state() == DagRunState.FAILED:
						failed_list.append((dt, f'dag_run_id={dag_run_id}'))
						break
				else:
					failed_list.append((dt, f'dag_run_id={dag_run_id} 트리거 시켰으나, 존재하지 않음)'))
					logging.info(f'{_LOG_PREFIX} {job_type}({dag_run_id}) 트리거 시켰으나, 존재하지 않음')
					break

	logging.info(f'{_LOG_PREFIX} 완료')

	context['ti'].xcom_push('skipped_list', skipped_list)
	context['ti'].xcom_push('failed_list', failed_list)


def _conclude(**context):
	"""
	스킵 또는 실패된 리스트 알림

	:param context:
	:return:
	"""
	job_type = context['ti'].xcom_pull(key=_XCOM_TARGET_JOB)

	skipped_msg = '[skipped_list]<br/>'
	skipped_list = context['ti'].xcom_pull(key='skipped_list')
	for item in skipped_list:
		skipped_msg += f'&nbsp;&nbsp;&nbsp;&nbsp; datetime={item[0]} reason={item[1]}<br/>'

	failed_msg = '[failed_list]<br/>'
	failed_list = context['ti'].xcom_pull(key='failed_list')
	for item in failed_list:
		failed_msg += f'&nbsp;&nbsp;&nbsp;&nbsp; datetime={item[0]} dag_run_id={item[1]}<br/>'

	if skipped_list or failed_list:
		title = f'[{job_type} 비딩그룹 지표 생성 수동 실행에 스킵 또는 실패 건이 있음] 스킵 건수={len(skipped_list)} 실패 건수={len(failed_list)}'
		body = f'{skipped_msg}<br/>{failed_msg}<br/>'
		send_email(ALERT_EMAIL_ADDRESSES, title, body)


with DAG(
		_DAG_ID,
		description='GFP/NAM 비딩그룹 지표를 위한 수동 처리용 DAG',
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2025, 5, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		tags=['trigger', 'manual', 'gfp', 'nam', 'biddinggroup', 'bg', 'hourly', 'daily', 'ins.cho'],
		catchup=False,
		params={
			'USAGE': 'Refer to the DAG code for detailed parameter usage.',
			_XCOM_TARGET_JOB: '',
			XCOM_START_DATE: '',
			XCOM_END_DATE: '',
			XCOM_TARGET_YMD_LIST: '',
		},
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup
	)

	# spark_{gfp|nam}_biddinggroup 트리거
	trigger_biddinggroup = PythonOperator(
		task_id=f'trigger_biddinggroup_job',
		python_callable=_trigger_biddinggroup_job,
	)

	# 스킵 또는 실패된 리스트 알림
	conclude = PythonOperator(
		task_id='conclude',
		python_callable=_conclude,
	)

	setup >> trigger_biddinggroup >> conclude
