"""
### Settlement 재처리 트리거 DAG

#### 0. 위키
- [33. Settlement (정산 데이터)](https://wiki.navercorp.com/pages/viewpage.action?pageId=3490651100)

#### 1. 주기
- 매월 2일 12시~14시 10분 간격 ( */10 12-15 2 * * )

#### 2. 스케쥴 콜렉션
- Data DB : Jobs
- Jobs 를 사용하는 경우, 가장 과거 날짜가 끝나기 전까지는 다음 날짜를 처리하지 못한다.

#### 3. 트리거 대상 DAG
- spark_settlement

#### 4. max_active_runs=1
"""

import os
import logging
import pendulum

from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.utils.types import DagRunType
from airflow.operators.python import ShortCircuitOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

from core.dao import job_dao
from core.base import DEFAULT_TZ, XCOM_TARGET_YM, ALERT_EMAIL_ADDRESSES

from biz.settlement.spark_settlement import DAG_ID as SPARK_DAG_ID

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [TRIGGER-SETTLEMENT-REPROCESS]'


def _setup(**context):
	"""
	가장 오래된 실패 이력 1건만 가져와서 target_ymd 설정
	:param context:
	:return:
	"""
	doc = job_dao.get_job({'type': SPARK_DAG_ID, 'manual': 0})
	if doc:
		if doc['retryCnt'] >= job_dao.MAX_RETRY_CNT:
			# monitor_jobs 에서 알림 메일 발송하기 때문에, 별도 처리 하지 않음
			logging.warning(
				f'{_LOG_PREFIX} {doc["datetime"]}의 retryCnt={doc["retryCnt"]}. 최대처리횟수({job_dao.MAX_RETRY_CNT})에 도달해서 스킵.')
		elif doc['running'] == 1:
			logging.warning(
				f'{_LOG_PREFIX} {doc["datetime"]} 이미 처리 중이므로 스킵. doc={doc}')
		else:
			context['ti'].xcom_push(XCOM_TARGET_YM, doc['datetime'])
			return True
	else:
		logging.info(f'{_LOG_PREFIX} 처리할 job 없음')

	return False


with DAG(
		_DAG_ID,
		description='Trigger reprocess settlement DAG',
		tags=['trigger', 'reprocess', 'settlement', 'monthly', 'data_db', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2025, 5, 1, tz=DEFAULT_TZ),
		schedule_interval='*/10 12-15 2 * *',  # 매월 2일 12시~14시 10분 간격
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# 처리해야 할 job이 있을 때만 다운스트림 태스크 진행
	# Jobs 에서 시간대순으로 하나의 시간대만 추출
	setup = ShortCircuitOperator(
		task_id='setup',
		python_callable=_setup,
	)

	trigger_settlement = TriggerDagRunOperator(
		trigger_dag_id=SPARK_DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{SPARK_DAG_ID}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=False,
		poke_interval=60,
		reset_dag_run=True,
		conf={
			XCOM_TARGET_YM: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YM}") }}}}',
			'trigger_by': DagRunType.SCHEDULED,
		}
	)

	setup >> trigger_settlement
