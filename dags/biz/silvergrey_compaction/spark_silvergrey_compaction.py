"""
### 실버그레이 컴팩션 스파크 집계
- 구글 처럼 연동 기간이 긴 경우가 있어, 90일 이전 데이터를 대상으로 컴팩션을 수행한다.

#### 1. 위키
- [10. 실버그레이 컴팩션](https://wiki.navercorp.com/pages/viewpage.action?pageId=2105813338)

#### 2. 저장소
- hdfs : /user/gfp-data/silvergrey

#### 3. config
- target_ymd : 처리할 날짜 YYYYMMDD ( default: D-90 )
- trigger_by : scheduled or manual
"""

import logging
import os
from datetime import timedelta
from functools import partial
from typing import List

import pendulum
from airflow.exceptions import AirflowException
from airflow.models import DAG, DagRun
from airflow.operators.python import PythonOperator
from airflow.sensors.python import PythonSensor
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType

from biz.silvergrey_compaction.silvergrey_compaction_dao import check_silvergrey_schedules_complete
from core import utils
from core.base import DEFAULT_TZ, PROFILE, BATCH_SERVER_ADDRESS, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD, \
	PROFILE_LOCAL, PROFILE_DEV, PROFILE_TEST, PROFILE_STAGE, PROFILE_REAL, SPARK_SUBMIT_OPTIONS, \
	POOL_SPARK, SPARKLING_APP_JAR_PATH, SPARKLING_IMAGE, \
	SPARKLING_APP_HARD_LIMIT_1 as SPARKLING_APP_HARD_LIMIT
from core.dao import job_dao
from core.spark_pool import POOL_SLOT_TRIVIAL_5
from core.spark_submit_op import invoke_job_with_args
from core.spark_task_group import create_task_group

# DAG 기본 정보
DAG_ID = _DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [SPARK-SILVERGREY-COMPACTION]'

# Spark
_SPARK_APP_CLASS = 'com.navercorp.gfp.biz.silvergrey.SilvergreyCompactor'

# Profile
_PROFILE_SETTINGS = {
	PROFILE_TEST: {
		SPARK_SUBMIT_OPTIONS: """
			--num-executors 3
			--executor-cores 2
			--executor-memory 4g
			--conf spark.sql.shuffle.partitions=200
			--conf spark.sql.files.maxPartitionBytes=32mb
			""".split()
	},
	PROFILE_REAL: {
		SPARK_SUBMIT_OPTIONS: """
			--num-executors 3
			--executor-cores 2
			--executor-memory 4g
			--conf spark.sql.shuffle.partitions=200
			--conf spark.sql.files.maxPartitionBytes=32mb
			""".split()
	}
}
_PROFILE_SETTINGS[PROFILE_LOCAL] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_DEV] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_STAGE] = _PROFILE_SETTINGS[PROFILE_REAL]

# Batch 호출 주소
_REQ_URL = BATCH_SERVER_ADDRESS + '/batch/silvergrey/recentymd/refresh'


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	_init_settings(**context)
	_print_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
	:param context:
	:return:
	"""
	target_ymd = context['params'][XCOM_TARGET_YMD]

	if target_ymd:
		target_ymd = str(target_ymd).strip()
		pendulum.from_format(target_ymd, 'YYYYMMDD')

		context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)
	else:
		# target_ymd 가 없으면 에러
		logging.error(f'There is not "params.target_ymd"')
		raise AirflowException('There is not "params.target_ymd"')


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK}
Airflow Pool Slot: {POOL_SLOT_TRIVIAL_5}

Profile: {PROFILE}
Spark Submit Options: {_PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS]}

Params:
	target_ymd= {context['ti'].xcom_pull(key=XCOM_TARGET_YMD)}
------------------------------------------------------------------------------------------
''')


def _check_dup_run(**context):
	"""
	중복 실행을 방지하기 위한 방어 로직
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	dag_run_id = context['dag_run'].run_id
	doc = job_dao.get_job({'type': _DAG_ID, 'datetime': target_ymd, 'running': 1})

	if doc:
		running_dag_runs: List["DagRun"] = DagRun.find(dag_id=_DAG_ID, state=DagRunState.RUNNING)
		for running_dag_run in running_dag_runs:
			ti = running_dag_run.get_task_instance('setup')
			running_target_ymd = ti.xcom_pull(key=XCOM_TARGET_YMD)
			if dag_run_id != running_dag_run.run_id and target_ymd == running_target_ymd:
				raise AirflowException(f'이미 실행 중인 DagRun 이 있음 ({running_dag_run.run_id} :: {running_target_ymd})')
	else:
		pass


def _upsert_job(**context):
	"""
	해당 시간대의 Job 추가 또는 running=1 로 업데이트
	:param context:
	:return:
	"""
	target_ymd = context["ti"].xcom_pull(key=XCOM_TARGET_YMD)

	filters = {'type': _DAG_ID, 'datetime': target_ymd}
	doc = job_dao.get_job(filters)

	if doc:
		update = {'$set': {'running': 1, 'modifiedAt': pendulum.now(tz=DEFAULT_TZ)}}
		job_dao.update_job(filters, update)
	else:
		trigger_by = context['params'].get('trigger_by')
		doc = {
			'type': _DAG_ID,
			'datetime': target_ymd,
			'retryCnt': -1,
			'running': 1,
			'manual': 0 if trigger_by == DagRunType.SCHEDULED else 1
		}
		job_dao.insert_job(doc)
		logging.info(f'{_LOG_PREFIX} {target_ymd} Jobs 에 추가. {doc}')


def _wait_for_silvergrey_schedules_complete(target_date):
	"""
	대상 날짜에 해당하는 기간(period.endDate)의 리포트 연동 스케쥴이 모두 처리 완료 상태 인지 체크
	"""

	if PROFILE in [PROFILE_REAL, PROFILE_STAGE]:
		is_complete = check_silvergrey_schedules_complete(target_date)
	else:
		is_complete = True

	logging.info(f'{_LOG_PREFIX} {target_date}(period.endDate) 에 해당하는 실버그레이 연동 스케쥴이 모두 처리 완료 상태 인지 = {is_complete}')

	return is_complete


def _refresh_silvergrey_recent_compaction_ymd():
	"""
	실버그레이 최근 컴팩션 일자 갱신 요청
	"""
	logging.info(f'{_LOG_PREFIX} url={_REQ_URL}')

	if PROFILE in [PROFILE_REAL, PROFILE_TEST]:
		# 배치 서버 호출
		res = utils.request(_REQ_URL)

		if res.get('code') != 200:
			raise AirflowException(f'실버그레이 최근 컴팩션 일자 갱신 요청 실패 ( res= {res} )')
	else:
		logging.info(f'{_LOG_PREFIX} PROFILE={PROFILE}, URL 호출하지 않음')


def _clean_up_to_success(**context):
	"""
	성공했으므로 job 삭제
	:param context:
	:return:
	"""
	job_dao.clean_up(_DAG_ID, context["ti"].xcom_pull(key=XCOM_TARGET_YMD), True)


def _clean_up_to_failure(context: dict):
	"""
	실패했으므로 retryCnt 증가시키고 running=0 설정
	:param context:
	:return:
	"""
	job_dao.clean_up(_DAG_ID, context["ti"].xcom_pull(key=XCOM_TARGET_YMD), False)


def _get_app_args(context: dict):
	"""
	spark-submit 시, 넘겨줄 파라미터 정보
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	logging.info(f'{_LOG_PREFIX} target_ymd= {target_ymd}')
	return [target_ymd]


with DAG(
		_DAG_ID,
		description='Invoke spark app for silvergrey compaction',
		tags=['spark', 'silvergrey', 'compaction', 'silvergrey_compaction', 'daily', 'hdfs', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
			# 'email_on_retry': False,
			# 'retries': 3,
			# 'retry_delay': timedelta(minutes=30)
		},
		params={
			XCOM_TARGET_YMD: '',
			'trigger_by': '',
		},
		start_date=pendulum.datetime(2024, 2, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	# 환경 설정
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 중복 실행 여부 확인
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=_check_dup_run,
	)

	# 해당 시간대의 Jobs.running=1 로 추가
	upsert_job = PythonOperator(
		task_id='upsert_job',
		python_callable=_upsert_job
	)

	# 대상 날짜에 해당하는 기간(period.endDate)의 리포트 연동 스케쥴이 모두 처리 완료 상태 인지 체크
	wait_for_silvergrey_schedules_complete = PythonSensor(
		task_id='wait_for_silvergrey_schedules_complete',
		python_callable=_wait_for_silvergrey_schedules_complete,
		op_args=[
			f'{{{{ ti.xcom_pull(task_ids="{setup.task_id}", key="{XCOM_TARGET_YMD}") }}}}'
		],
		poke_interval=60 * 20,  # 20분마다 재시도
		execution_timeout=timedelta(hours=3),  # 3시간 동안
		on_failure_callback=partial(_clean_up_to_failure)
	)

	task_group_id = 'spark_run_silvergrey_compaction'
	spark_run_silvergrey_compaction = create_task_group(
		task_group_id,
		tooltip=_SPARK_APP_CLASS,
		spark_pool=POOL_SPARK,
		pool_slot=POOL_SLOT_TRIVIAL_5,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': _SPARK_APP_CLASS,
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			'dag_id': _DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(_DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS,
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT,
		wait_showup_failure_cb=partial(_clean_up_to_failure),
		wait_complete_failure_cb=partial(_clean_up_to_failure),
		conclude_app_failure_cb=partial(_clean_up_to_failure)
	)

	# 실버그레이 최근 컴팩션 일자 갱신
	refresh_silvergrey_recent_compaction_ymd = PythonOperator(
		task_id='refresh_silvergrey_recent_compaction_ymd',
		python_callable=_refresh_silvergrey_recent_compaction_ymd
	)

	# 성공 시 Jobs 다큐먼트 삭제
	clean_up_to_success = PythonOperator(
		task_id='clean_up_to_success',
		python_callable=_clean_up_to_success
	)

	setup >> check_dup_run >> upsert_job >> \
	wait_for_silvergrey_schedules_complete >> spark_run_silvergrey_compaction >> refresh_silvergrey_recent_compaction_ymd >> \
	clean_up_to_success
