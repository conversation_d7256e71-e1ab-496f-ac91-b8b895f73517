import logging
from typing import List
from typing import Union

import pendulum
import requests
import tenacity
from airflow.models import DagRun
from airflow.providers.http.hooks.http import HttpHook
from airflow.providers.mongo.hooks.mongo import MongoHook
from airflow.utils.types import DagRunType
from bson.objectid import ObjectId
from pendulum.tz.timezone import Timezone

from core.base import DEFAULT_TZ, CONN_MONGO_FOR_DATA, CONN_AIRFLOW_WEB, BATCH_SERVER_ADDRESS
from core.dao.environment_dao import is_ready_silver_log
from core.utils import id_arr_to_csv

# https://wiki.navercorp.com/pages/viewpage.action?pageId=974545341

SPARK_DAG_ID = 'spark_discrepancy_report'
COLLECTION = 'DiscrepancyReportSchedules'

ST_READY = 'READY'
ST_LAUNCH = 'LAUNCH'
ST_IN_PROGRESS = 'IN_PROGRESS'
ST_COMPLETE = 'COMPLETE'
ST_FAILURE = 'FAILURE'

XCOM_SCHEDULE_ID = 'schedule_id'
XCOM_REPORT_API_TYPE = 'report_api_type'
XCOM_SCH_LIST = 'schedule_list'

REQ_MAKE_SCH_URL = BATCH_SERVER_ADDRESS + '/batch/discrepancy/report/schedule?ymd={YYYYMMDD}&reportApiTypes={REPORT_API_TYPES}'
REQ_SYNC_REPORT_URL = BATCH_SERVER_ADDRESS + '/batch/discrepancy/report/api/{REPORT_API_TYPE}?ymd={YYYYMMDD}&schedule_id={SCHEDULE_ID}'


def _update_schedule(_id: ObjectId, set_values: dict):
	"""
	_id 에 해당하는 스케쥴 상태 정보 업데이트

	:param _id:
	:param set_values:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]
		res = coll.update_one(
			{'_id': _id},
			{'$set': set_values}
		)

	if res.modified_count == 1:
		logging.info(f'{COLLECTION}: _id={_id}: Updated')
	else:
		logging.info(f'{COLLECTION}: _id={_id}: Not updated')


def update_state_to_in_progress(context: dict):
	"""
	sparkState = IN_PROGRESS 로 설정
	jobStatus.yarnAppId, jobStatus.yarnTrackingUrl, jobStatus.submittedAt 설정
		- create_task_group 에서 호출됨
			- spark_submit_cb >> conclude_job.on_success_callback
	:param context:
	:return:
	"""
	ti = context['ti']
	schedule_id = ti.xcom_pull(key=XCOM_SCHEDULE_ID)

	if schedule_id:
		now = pendulum.now(DEFAULT_TZ)

		tracking_url = ti.xcom_pull(task_ids=ti.task_id, key='tracking_url')
		app_id = ti.xcom_pull(task_ids=ti.task_id, key='app_id')

		updates = {
			'sparkState': ST_IN_PROGRESS,
			'begunAt': now,
			'jobStatus.yarnAppId': app_id,
			'jobStatus.yarnTrackingUrl': tracking_url,
			'jobStatus.submittedAt': now,
			'modifiedAt': now,
		}
		_update_schedule(ObjectId(schedule_id), updates)


def update_state_to_complete(success: bool, context: dict):
	"""
	스케줄의 sparkState 를 COMPLETE / FAILED 로 설정
	endedAt 은 연동을 마치고 설정

	:param success:
	:param context:
	:return:
	"""

	schedule_id = context['ti'].xcom_pull(key=XCOM_SCHEDULE_ID)
	logging.info(f'schedule ID: {schedule_id}')

	if schedule_id:
		now = pendulum.now()
		updates = {
			'sparkState': ST_COMPLETE if success else ST_FAILURE,
			'modifiedAt': now,
		}

		_update_schedule(ObjectId(schedule_id), updates)


def fetch_schedule_by_id(_id: str):
	"""
	스케줄을 _id 를 통해 조회

	:param _id:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]
	return coll.find_one({'_id': ObjectId(_id)})


def fetch_schedules(
		ymd_list: List[str] = None,
		*,
		report_api_types: List[str] = None,
		spark_states: List[str] = None,
		publisher_ids: List[ObjectId] = None,
		adProvider_ids: List[ObjectId] = None):
	"""
	스케줄 컬렉션에서 조건에 일치하는 스케줄 목록 조회
	입력되지 않은 파라미터는 필터 조건에 포함되지 않음 (모든 값을 조회)

	:param ymd_list:
	:param spark_states:
	:param report_api_types:
	:param publisher_ids:
	:param adProvider_ids:
	:return:
	"""
	# https://peps.python.org/pep-0448/
	filter = {
		**({'ymd': {'$in': ymd_list}} if ymd_list else {}),
		**({'reportApiType': {'$in': report_api_types}} if report_api_types else {}),
		**({'sparkState': {'$in': spark_states}} if spark_states else {}),
		**({'publisher_ids': {'$in': publisher_ids}} if publisher_ids else {}),
		**({'adProvider_ids': {'$in': adProvider_ids}} if adProvider_ids else {})
	}
	logging.info(f'filter: {filter}')

	# 불필요한 시간정보 제외 (xcom 에 방해됨)
	projection = {
		'createdAt': False,
		'modifiedAt': False,
		'begunAt': False,
		'endedAt': False,
	}

	sort = [('ymd', 1)]

	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]
		schedules = list(coll.find(filter=filter, projection=projection, sort=sort))
		return schedules


def is_ready_to_aggregate(tz: Union[str, Timezone], sch: dict):
	"""
	스케줄의 집계 가능 여부 체크

	:param tz:
	:param sch:
	:return:
	"""
	if sch.get('sparkState') != ST_LAUNCH:
		return False

	# ap timezone 으로 적용된 endDate 를 한국시간대로 변환
	if sch.get('endDate'):
		end_ymdh = sch['endDate'] + '23'
		target_ymdh = pendulum.from_format(end_ymdh, 'YYYYMMDDHH', sch['timezone']).in_tz(tz).format('YYYYMMDDHH')

		return is_ready_silver_log(target_ymdh)

	logging.warning('"endDate" not found')
	return False


def is_ready_to_sync(schedule: dict):
	"""
	스케줄의 연동요청 가능 여부 체크

	:param schedule:
	:return:
	"""
	return schedule.get('sparkState') == ST_COMPLETE and schedule.get('apiState') in [ST_READY, ST_FAILURE, ST_COMPLETE]


def fetch_schedule_args(schedule_id: str):
	"""
	schedule_id 에 해당하는 ymd, end_date, reportApiType, adProvider_ids, publisher_ids 조회

	:param schedule_id:
	:return:
	"""

	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]
		schedule = coll.find_one(ObjectId(schedule_id))

		return dict(
			ymd=schedule['ymd'],
			end_date=schedule['endDate'],
			report_api_type=schedule['reportApiType'],
			adProvider_ids=id_arr_to_csv(schedule['adProvider_ids']),
			publisher_ids=id_arr_to_csv(schedule['publisher_ids'])
		)


def trigger_dag_with_api(sch: dict, dag_run_type: DagRunType = DagRunType.SCHEDULED) -> (str, str):
	"""
	rest api 를 사용하여, spark dag을 trigger 한다.

	:param sch: 스케쥴 정보
	:return:
	"""
	ap_ids = sch.get('adProvider_ids')
	pub_ids = sch.get('publisher_ids')
	conf = {
		'schedule_id': str(sch.get('_id')),
		'schedule_args': {
			'ymd': sch.get('ymd'),
			'end_date': sch.get('endDate'),
			'report_api_type': sch.get('reportApiType'),
			'adProvider_ids': ap_ids if type(ap_ids) == str else id_arr_to_csv(ap_ids),
			'publisher_ids': pub_ids if type(pub_ids) == str else id_arr_to_csv(pub_ids),
		},
	}
	now = pendulum.now(DEFAULT_TZ)
	dag_run_id = DagRun.generate_run_id(dag_run_type, now)
	logging.info(f'trigger DAG ready: dag_id={SPARK_DAG_ID}, '
				 f'dag_run_id={dag_run_id}'
				 f'{conf}')

	endpoint = f'api/v1/dags/{SPARK_DAG_ID}/dagRuns'
	payload = dict(
		dag_run_id=dag_run_id,
		logical_date=str(now),
		conf=conf,
	)

	api_conn = HttpHook('POST', CONN_AIRFLOW_WEB)
	retry_args = dict(
		wait=tenacity.wait.wait_fixed(30),
		stop=tenacity.stop.stop_after_attempt(4),
		retry=tenacity.retry_if_exception_type(requests.ConnectionError)
	)
	res: requests.Response = api_conn.run_with_advanced_retry(
		retry_args,
		endpoint,
		extra_options=dict(check_response=True),
		json=payload,
	)
	logging.info(f'triggered DAG: {endpoint}\n{res.text}')

	# 스파크 집계를 위해 처리 대기 상태(LAUNCH)로 변경
	_update_schedule(ObjectId(sch.get('_id')), {
		'sparkState': ST_LAUNCH,
		'jobStatus.dagId': SPARK_DAG_ID,
		'jobStatus.dagRunId': dag_run_id,
		'jobStatus.launchedAt': now,
	})
	logging.info(f'schedule({str(sch.get("_id"))}) state set to {ST_LAUNCH}')

	return SPARK_DAG_ID, dag_run_id
