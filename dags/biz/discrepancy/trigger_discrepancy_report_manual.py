"""
#### 위키
- [11. 불일치 리포트 집계 / 연동](https://wiki.navercorp.com/pages/viewpage.action?pageId=974545341)

#### 개요
- 수동 실행 (스케줄 되지 않음)
- 동시에 1개의 dag run만 존재할 수 있음. 중복 실행 불가.
- spark_discrepancy_report DAG을 트리거시킴

#### config
- 날짜 (required)
    - 1. target_ymd_from, target_ymd_to : 두 날짜 사이의 모든 스케줄 실행
    - 2. target_ymd_list : 처리할 스케쥴 날짜 리스트 (YYYYMMDD,YYYYMMDD,YYYYMMDD)
    - 모두 입력하지 않은 경우 : 에러
- report_api_type (default= 모든 타입)
	- 불일치 리포트 연동 중인 report_api_type 리스트 (TTD,RTBHOUSE,NATIVO,PANGLE 등)
	- common-code 기준으로 입력
- adProvider_ids (default= 정규와 같은 광고공급자 대상)
	- 집계 대상이 될 광고공급자의 id 리스트
- publisher_ids (default= 정규와 같은 매체 대상)
	- 집계 대상이 될 매체의 id 리스트
"""
import logging
import os
from datetime import timedelta
from time import sleep

import pendulum
from airflow.exceptions import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.sensors.python import PythonSensor
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType

from biz.discrepancy.discrepancy_report_schedule_dao import fetch_schedules, trigger_dag_with_api, REQ_MAKE_SCH_URL, \
	XCOM_REPORT_API_TYPE, ST_IN_PROGRESS, ST_LAUNCH
from biz.discrepancy.spark_discrepancy_report import _DAG_ID as SPARK_DAG_ID
from core import utils
from core.base import XCOM_TARGET_YMD_LIST, DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_PUBLISHER_IDS, XCOM_ADPROVIDER_IDS
from core.utils import csv_to_id_arr, get_between_ymd_list, id_arr_to_csv, csv_to_str_arr

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRG-DCP-MANUAL]'


def _setup(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화 및 로깅

	:param context:
	:return:
	"""

	_init_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
		- target_ymd_list (required)
		- report_api_type / publisher_ids / adProvider_ids (optional)

	:param context:
	:return:
	"""

	_set_target_ymd_list(**context)

	report_api_types = context['params'].get(XCOM_REPORT_API_TYPE).upper()
	adprovider_ids = context['params'].get(XCOM_ADPROVIDER_IDS)
	publisher_ids = context['params'].get(XCOM_PUBLISHER_IDS)

	# 공백 및 양끝 콤마 제거
	report_api_types = report_api_types.replace(' ', '').strip(',') if report_api_types else None
	adprovider_ids = adprovider_ids.replace(' ', '').strip(',') if adprovider_ids else None
	publisher_ids = publisher_ids.replace(' ', '').strip(',') if publisher_ids else None

	logging.info(f'{_LOG_PREFIX} report_api_types={report_api_types}, adProvider_ids={adprovider_ids}, publisher_ids={publisher_ids}')

	context['ti'].xcom_push(XCOM_REPORT_API_TYPE, report_api_types)
	context['ti'].xcom_push(XCOM_ADPROVIDER_IDS, adprovider_ids)
	context['ti'].xcom_push(XCOM_PUBLISHER_IDS, publisher_ids)


def _set_target_ymd_list(**context):
	"""
	config 로부터 받은 날짜 리스트를 추출하여 xcom push

	:param context:
	:return:
	"""

	ymd_from = context['params'].get('target_ymd_from')
	ymd_to = context['params'].get('target_ymd_to')
	ymd_list = context['params'].get('target_ymd_list')

	trigger_ymd_list = []

	if ymd_from and ymd_to:
		ymd_from = ymd_from.strip()
		ymd_to = ymd_to.strip()
		pendulum.from_format(ymd_from, 'YYYYMMDD')
		pendulum.from_format(ymd_to, 'YYYYMMDD')

		if ymd_from <= ymd_to:
			# trigger_ymd_list 에 추가
			trigger_ymd_list.extend(get_between_ymd_list(ymd_from, ymd_to))
			logging.info(f'{_LOG_PREFIX} ymd_from={ymd_from}, ymd_to={ymd_to}, trigger_ymd_list={trigger_ymd_list}')
		else:
			raise AirflowException(f'target_ymd_from 는 target_ymd_to 보다 작거나 같아야 합니다.'
								   f' target_ymd_from={ymd_from}, target_ymd_to={ymd_to}')
	elif ymd_list:
		ymd_list = ymd_list.replace(' ', '').strip(',')

		# trigger_ymd_list 에 추가
		trigger_ymd_list.extend(ymd_list.split(','))
		logging.info(f'{_LOG_PREFIX} trigger_ymd_list 를 이용한 실행. {trigger_ymd_list}')
	else:
		raise AirflowException(f'"config"가 기술되지 않음')

	context['ti'].xcom_push(XCOM_TARGET_YMD_LIST, trigger_ymd_list)


def _wait_for_today_discrepancy_report_schedule(**context):
	"""
	불일치 리포트 집계를 위해 target_ymd_list 에 해당하는 스케줄을 생성하도록 배치서버에 요청을 보냄.
	오늘의 대상 스케줄이 이미 있다면 요청을 보내지 않음

	:param context:
	:return:
	"""
	target_ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)
	report_api_types = context['ti'].xcom_pull(key=XCOM_REPORT_API_TYPE)

	for target_ymd in target_ymd_list:
		# REPORT_API_TYPES 빈 값으로 전달 시 모든 type 의 스케줄을 대상으로 함
		url = REQ_MAKE_SCH_URL.format(YYYYMMDD=target_ymd,
									  REPORT_API_TYPES=report_api_types if report_api_types else '')
		logging.info(f'{_LOG_PREFIX} 배치 서버로 불일치 리포트 스케줄 생성 요청 url={url}')

		try:
			# 스케줄 생성 요청 (타임아웃 30분)
			res = utils.request(url, timeout=60 * 30)

			if res.get('code') != 200:
				raise AirflowException(f'불일치 리포트 스케줄 생성 요청 실패 ( res= {res} )')

		except Exception as ex:
			raise AirflowException(f'target_ymd= {target_ymd}, url= {url}\n ex= {ex}')

	return True


def _trigger_discrepancy_report(**context):
	"""
	조건에 맞는 스케줄 조회 후
	sparkState= READY, COMPLETE, FAILURE 상태인 스케줄을 트리거
		- target_ymd_list 에 있는 날짜 대상
		- report_api_type / publisher_ids / adprovider_ids 에 해당하는 스케쥴 대상

	:param context:
	:return:
	"""
	# 스킵, 실패한 스케쥴 리스트
	skipped_list = []
	failed_list = []

	target_ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)
	report_api_types = context['ti'].xcom_pull(key=XCOM_REPORT_API_TYPE)
	adprovider_ids = context['ti'].xcom_pull(key=XCOM_ADPROVIDER_IDS)
	publisher_ids = context['ti'].xcom_pull(key=XCOM_PUBLISHER_IDS)

	schedule_list = fetch_schedules(
		ymd_list=target_ymd_list,
		report_api_types=csv_to_str_arr(report_api_types),
		adProvider_ids=csv_to_id_arr(adprovider_ids),  # csv -> List[oid]  (DB 검색을 위해)
		publisher_ids=csv_to_id_arr(publisher_ids),  # csv -> List[oid]
	)
	schedule_count = len(schedule_list)
	for idx, sch in enumerate(schedule_list):
		sch['_id'] = str(sch.get('_id'))
		sch['adProvider_ids'] = id_arr_to_csv(sch.get('adProvider_ids'))  # 위의 검색 결과는 List[oid] 이기 때문에 csv 로 변환
		sch['publisher_ids'] = id_arr_to_csv(sch.get('publisher_ids'))  # spark Dag 의 config 형태

		logging.info(
			f'{_LOG_PREFIX} [{idx + 1}/{schedule_count}] schedule_id={sch["_id"]}, ymd={sch["ymd"]}, reportApiType={sch["reportApiType"]}, '
			f'adProvider_ids={sch["adProvider_ids"]}, publisher_ids={sch["publisher_ids"]}')

		if sch.get('sparkState') in [ST_LAUNCH, ST_IN_PROGRESS]:
			logging.info(
				f'{_LOG_PREFIX} [{idx + 1}/{schedule_count}] 스킵됨. schedule_id={sch["_id"]}, ymd={sch["ymd"]}, reportApiType={sch["reportApiType"]}, '
				f'adProvider_ids={sch["adProvider_ids"]}, publisher_ids={sch["publisher_ids"]}')

			skipped_list.append(
				(sch["_id"], sch["ymd"], sch["reportApiType"], sch["adProvider_ids"], sch["publisher_ids"]))
			continue

		try:
			# 트리거
			dag_id, dag_run_id = trigger_dag_with_api(sch, DagRunType.MANUAL)
			logging.info(f'{_LOG_PREFIX} [{idx + 1}/{schedule_count}] 트리거됨 (dag_id={dag_id}, dag_run_id={dag_run_id}).'
						 f'schedule_id={sch["_id"]}, ymd={sch["ymd"]}, reportApiType={sch["reportApiType"]},'
						 f'adProvider_ids={sch["adProvider_ids"]}, publisher_ids={sch["publisher_ids"]}')

			# dag 이 끝났는지 주기적으로 확인
			result_state = 'UNKNOWN'
			while True:
				sleep(60)
				dag_runs = DagRun.find(dag_id=SPARK_DAG_ID, run_id=dag_run_id)
				if dag_runs:
					dag_run: DagRun = dag_runs.pop()
					dag_run_state = dag_run.get_state()
					if dag_run_state == DagRunState.SUCCESS:
						result_state = '성공'
						break
					elif dag_run_state == DagRunState.FAILED:
						result_state = '실패'
						failed_list.append(
							(sch["_id"], sch["ymd"], sch["reportApiType"], sch["adProvider_ids"], sch["publisher_ids"]))
						break
					else:
						pass
				else:
					logging.info(f'{_LOG_PREFIX} {SPARK_DAG_ID}({dag_run_id}) 트리거 시켰으나, 존재하지 않음')
					break
			logging.info(f'{_LOG_PREFIX} {dag_run_id} {result_state}')

		except Exception as ex:
			logging.exception(f'{_LOG_PREFIX} fail to trigger. ex={ex}')

	context['ti'].xcom_push('skipped_list', skipped_list)
	context['ti'].xcom_push('failed_list', failed_list)


def _conclude(**context):
	"""
	스킵 또는 실패된 리스트 알림
	:param context:
	:return:
	"""

	skipped_msg = ''
	skipped_list = context['ti'].xcom_pull(key='skipped_list')
	for item in skipped_list:
		skipped_msg += f'_id={item[0]}, ymd={item[1]}, reportApiType={item[2]}, adProvider_ids={item[3]}, publisher_ids={item[4]}\n'

	failed_msg = ''
	failed_list = context['ti'].xcom_pull(key='failed_list')
	for item in failed_list:
		failed_msg += f'_id={item[0]}, ymd={item[1]}, reportApiType={item[2]}, adProvider_ids={item[3]}, publisher_ids={item[4]}\n'

	if skipped_list or failed_list:
		raise AirflowException(f'불일치 리포정 수동 처리에 스킵 또는 실패 건이 있음.\n'
							   f'skipped_list=\n{skipped_msg}\n'
							   f'failed_list=\n{failed_msg}')


with DAG(
		_DAG_ID,
		description='불일치 리포트를 위한 수동 처리용 DAG.',
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2025, 1, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		tags=['trigger', 'manual', 'discrepancy', 'daily', 'ins.cho'],
		catchup=False,
		params={
			'target_ymd_from': '',
			'target_ymd_to': '',
			XCOM_TARGET_YMD_LIST: '',
			XCOM_REPORT_API_TYPE: '',
			XCOM_ADPROVIDER_IDS: '',
			XCOM_PUBLISHER_IDS: '',
		},
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
		execution_timeout=timedelta(minutes=5)
	)

	# SSP BATCH 로 불일치 스케줄 생성 요청
	# sparkState='READY', apiState='READY' 상태로 스케줄 생성됨
	wait_for_dcp_schedule_created = PythonSensor(
		task_id='wait_for_today_discrepancy_report_schedule',
		python_callable=_wait_for_today_discrepancy_report_schedule,
		poke_interval=60 * 5,
		execution_timeout=timedelta(minutes=5),
	)

	trigger_discrepancy_report = PythonOperator(
		task_id='trigger_discrepancy_report',
		python_callable=_trigger_discrepancy_report,
		retries=2,
		retry_delay=timedelta(seconds=20),
	)

	# 스킵 또는 실패된 리스트 알림
	conclude = PythonOperator(
		task_id='alert',
		python_callable=_conclude,
	)

	setup >> wait_for_dcp_schedule_created >> trigger_discrepancy_report >> conclude
