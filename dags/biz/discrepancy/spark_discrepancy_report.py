"""
#### 위키
- [11. 불일치 리포트 집계 / 연동](https://wiki.navercorp.com/pages/viewpage.action?pageId=974545341)

#### 개요
- 불일치 리포트 지표 집계 및 연동

#### 주기
- 정규/재처리	: trigger_discrepancy_report_regular 에 따름
- 수동   	: trigger_discrepancy_report_manual 에 따름

#### config
- schedule_id (DataDiscrepancy 스케줄의 id)
"""
import logging
import os
from datetime import timedelta
from functools import partial

import pendulum
from airflow import AirflowException
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.sensors.python import PythonSensor
from bson import ObjectId

from biz.discrepancy.discrepancy_report_schedule_dao import fetch_schedule_args, XCOM_SCHEDULE_ID, XCOM_REPORT_API_TYPE, \
	update_state_to_complete, is_ready_to_aggregate, is_ready_to_sync, fetch_schedule_by_id, \
	update_state_to_in_progress, REQ_SYNC_REPORT_URL
from core import utils
from core.base import DEFAULT_TZ, PROFILE, ALERT_EMAIL_ADDRESSES, \
	PROFILE_TEST, PROFILE_REAL, POOL_SPARK_AP, SPARKLING_APP_JAR_PATH, SPARKLING_IMAGE, \
	SPARKLING_APP_HARD_LIMIT_1, SPARK_SUBMIT_OPTIONS, PROFILE_STAGE, PROFILE_DEV, PROFILE_LOCAL, XCOM_TARGET_YMD, \
	XCOM_END_DATE, XCOM_ADPROVIDER_IDS, XCOM_PUBLISHER_IDS
from core.spark_pool import POOL_AP_SLOT_TRIVIAL_5
from core.spark_submit_op import invoke_job_with_args
from core.spark_task_group import create_task_group

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [SPK-DSP]'

# Silver Log
_SILVER_LOG_CHECK_TIMEOUT = 3600 * 3

# Spark
_SPARK_APP_CLASS = 'com.navercorp.gfp.biz.discrepancy.DiscrepancyDailyAggregator'

# Profile
_PROFILE_SETTINGS = {
	PROFILE_TEST: {
		SPARK_SUBMIT_OPTIONS: """
        --num-executors 1
        --executor-cores 3
        --executor-memory 512m
        """.split()
	},
	PROFILE_REAL: {
		SPARK_SUBMIT_OPTIONS: """
        --num-executors 1
        --executor-cores 3
        --executor-memory 512m
        """.split()
	}
}
_PROFILE_SETTINGS[PROFILE_LOCAL] = _PROFILE_SETTINGS[PROFILE_DEV] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_STAGE] = _PROFILE_SETTINGS[PROFILE_REAL]


def _setup(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화 및 로깅

	:param context:
	:return:
	"""
	_init_settings(**context)
	_print_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
		- schedule_id (required) / schedule_args (optional)
		- schedule_id 가 없는 경우, 에러 처리
		- schedule_args 가 없는 경우, DataAdProviderSummarySchedules 에서 schedule_id 에 해당하는 schedule_args 정보를 조회함
		- params.schedule_args.end_date = SummaryHistory.datatime

	:param fetch_schedule_args:
	:param context:
	:return:
	"""
	# if DagRunType.SCHEDULED in context['dag_run'].run_id:
	# 	context['dag_run'].run_type = DagRunType.SCHEDULED

	schedule_id = context['params'].get(XCOM_SCHEDULE_ID)

	if schedule_id and ObjectId.is_valid(schedule_id):
		if schedule_args := fetch_schedule_args(schedule_id):
			context['ti'].xcom_push(XCOM_SCHEDULE_ID, schedule_id)

		context['ti'].xcom_push(XCOM_TARGET_YMD, schedule_args['ymd'])
		context['ti'].xcom_push(XCOM_END_DATE, schedule_args['end_date'])
		context['ti'].xcom_push(XCOM_REPORT_API_TYPE, schedule_args['report_api_type'])
		context['ti'].xcom_push(XCOM_ADPROVIDER_IDS, schedule_args['adProvider_ids'])
		context['ti'].xcom_push(XCOM_PUBLISHER_IDS, schedule_args['publisher_ids'])
	else:
		# schedule_id 가 없으면 에러
		logging.error('잘못된 schedule_id 가 입력 되었습니다.')
		raise AirflowException('There is not "params.schedule_id"')


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK_AP}
Airflow Pool Slot: {POOL_AP_SLOT_TRIVIAL_5}

Profile: {PROFILE}
Spark Submit Options: {_PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS]}
Params:
    schedule_id={context['ti'].xcom_pull(key=XCOM_SCHEDULE_ID)}
    ymd={context['ti'].xcom_pull(key=XCOM_TARGET_YMD)}
    end_date={context['ti'].xcom_pull(key=XCOM_END_DATE)}
    report_api_type={context['ti'].xcom_pull(key=XCOM_REPORT_API_TYPE)}
    adProvider_ids={context['ti'].xcom_pull(key=XCOM_ADPROVIDER_IDS)}
    publisher_ids={context['ti'].xcom_pull(key=XCOM_PUBLISHER_IDS)}
------------------------------------------------------------------------------------------
''')


def _wait_for_ready_to_trigger(**context):
	"""
	sparkState 및 실버로그 컴팩션 일시를 확인하여 집계해도 되는지 확인

	:param context:
	:return:
	"""
	schedule_id = context['ti'].xcom_pull(key=XCOM_SCHEDULE_ID)
	schedule = fetch_schedule_by_id(schedule_id)
	return is_ready_to_aggregate(DEFAULT_TZ, schedule)


def _check_ready_to_sync(**context):
	"""
	불일치 리포트 스케줄이 연동 요청을 보내도 되는 상태인지 확인

	:param context:
	:return:
	"""
	schedule_id = context['ti'].xcom_pull(key=XCOM_SCHEDULE_ID)
	schedule = fetch_schedule_by_id(schedule_id)
	return is_ready_to_sync(schedule)


def _wait_for_sync_to_complete(**context):
	"""
	불일치 리포트 집계 완료 후, 배치서버로 연동 요청을 보냄
	apiState=COMPLETE/FAILURE 업데이트는 배치서버에서 함

	:param context:
	:return:
	"""
	schedule_id = context['ti'].xcom_pull(key=XCOM_SCHEDULE_ID)
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	report_api_type = context['ti'].xcom_pull(key=XCOM_REPORT_API_TYPE)

	# REPORT_API_TYPES 빈 값으로 전달 시 모든 type 의 스케줄을 대상으로 함
	url = REQ_SYNC_REPORT_URL.format(REPORT_API_TYPE=report_api_type, YYYYMMDD=target_ymd, SCHEDULE_ID=schedule_id)
	logging.info(f'{_LOG_PREFIX} 배치 서버로 불일치 리포트 연동 요청 url={url}')

	try:
		# 스케줄 생성 요청 (타임아웃 30분)
		res = utils.request(url, timeout=60 * 30)

		if res.get('code') != 200:
			raise AirflowException(f'불일치 리포트 스케줄 생성 요청 실패 ( res= {res} )')

	except Exception as ex:
		raise AirflowException(f'target_ymd= {target_ymd}, url= {url}\n ex= {ex}')

	return True


def _get_app_args(context: dict):
	"""
	spark-submit 시, 넘겨줄 파라미터 정보

	:param context:
	:return:
	"""
	return [
		context['ti'].xcom_pull(key=XCOM_END_DATE),
		context['ti'].xcom_pull(key=XCOM_REPORT_API_TYPE),
		context['ti'].xcom_pull(key=XCOM_ADPROVIDER_IDS),
		context['ti'].xcom_pull(key=XCOM_PUBLISHER_IDS)
	]


with DAG(
		_DAG_ID,
		description='스파크 앱 실행: 불일치 리포트',
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2025, 1, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		tags=['spark', 'discrepancy', 'daily', 'mongo', 'ins.cho'],
		catchup=False,
		params={
			'schedule_id': ''
		},
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='_setup',
		python_callable=_setup,
	)

	# 실버로그 컴팩션 확인.
	wait_for_silver_log = PythonSensor(
		task_id='wait_for_silver_log',
		python_callable=_wait_for_ready_to_trigger,
		poke_interval=60 * 15,  # 15분마다
		execution_timeout=timedelta(seconds=_SILVER_LOG_CHECK_TIMEOUT),
		on_failure_callback=partial(update_state_to_complete, False)
	)

	task_group_id = 'spark_discrepancy_report'
	run_spark_app = create_task_group(
		task_group_id,
		tooltip=_SPARK_APP_CLASS,
		spark_pool=POOL_SPARK_AP,
		pool_slot=POOL_AP_SLOT_TRIVIAL_5,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': _SPARK_APP_CLASS,
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_END_DATE}") }}}}',
			'dag_id': _DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(_DAG_ID, "{{ run_id }}")}',
			'detail': {
				'endDate': f'{{{{ ti.xcom_pull(key="{XCOM_END_DATE}") }}}}',
				'report_api_type': f'{{{{ ti.xcom_pull(key="{XCOM_REPORT_API_TYPE}") }}}}',
				'adProviderIds': f'{{{{ ti.xcom_pull(key="{XCOM_ADPROVIDER_IDS}") }}}}',
				'publisherIds': f'{{{{ ti.xcom_pull(key="{XCOM_PUBLISHER_IDS}") }}}}',
			}
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS,
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT_1,
		spark_submit_cb=partial(update_state_to_in_progress),
		wait_showup_failure_cb=partial(update_state_to_complete, False),
		wait_complete_failure_cb=partial(update_state_to_complete, False),
		conclude_app_success_cb=partial(update_state_to_complete, True),
		conclude_app_failure_cb=partial(update_state_to_complete, False),
	)

	# 해당 스케줄이 불일치 연동 요청이 가능한 상태인지 체크
	check_ready_to_sync = PythonSensor(
		task_id='check_ready_to_sync',
		python_callable=_check_ready_to_sync,
		poke_interval=60 * 1,
		execution_timeout=timedelta(minutes=10),
	)

	# SSP BATCH 로 불일치 스케줄 연동 요청
	wait_for_sync_to_complete = PythonSensor(
		task_id='wait_for_sync_to_complete',
		python_callable=_wait_for_sync_to_complete,
		poke_interval=60 * 5,
		execution_timeout=timedelta(minutes=30),
	)

	setup >> wait_for_silver_log >> run_spark_app >> check_ready_to_sync >> wait_for_sync_to_complete
