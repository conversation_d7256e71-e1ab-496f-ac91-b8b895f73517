"""
### 매체 성과 리포트 정규 트리거 DAG
- CustomReports 콜렉션에서 type=PERF, cmsUse=0, state=READY 인 건들을 가져와서 스파크 DAG 을 실행한다. (createdAt 순)

#### 0. 위키
- [17. 매체 성과 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=2210851496)

#### 1. 주기
- 매 10분

#### 2. 트리거 대상 DAG
- spark_performance_report

#### 3. config
- 없음

#### 4. 기타
- max_active_runs=1
- performance-report-thread-num=2
"""

import os
import pendulum

from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator, ShortCircuitOperator

from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES
from core.dao.environment_dao import get_environment_value_by_name

from biz.performance.performance_report_base import exist_report, trigger_performance_report

# 한 번에 처리할 수 있는 리포트 갯수 ( performance-report-thread-num = 2 )
THREAD_NUM = int(get_environment_value_by_name('performance-report-thread-num'))

_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")


with DAG(
		_DAG_ID,
		description='매체 성과 리포트 정규 DAG',
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		tags=['trigger', 'regular', 'pr', 'performance_report', 'data_api', 'bitna.cho'],
		start_date=pendulum.datetime(2024, 4, 1, tz=DEFAULT_TZ),
		schedule_interval='*/10 * * * *',
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# cms_use 가 0인 경우 매체 성과, 1인 경우 맞춤 성과

	# 처리할 리포트가 있는지
	exist_report = ShortCircuitOperator(
		task_id='exist_report',
		python_callable=exist_report,
		op_args=[0]
	)

	# 매체 성과 리포트 집계 트리거
	trigger_performance_report = PythonOperator(
		task_id='trigger_performance_report',
		python_callable=trigger_performance_report,
		op_args=[0, THREAD_NUM]
	)

	exist_report >> trigger_performance_report
