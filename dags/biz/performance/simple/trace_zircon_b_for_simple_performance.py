"""
### ZirconTrace 에 의한 Simple 매체 성과 리포트 재처리

#### 0. 위키
- [17. 매체 성과 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=2210851496)

#### 1. 주기
- 매시 5분
- simple-performance-trace-recent-reprocess-ymdh = yyyymmddhh
	- Simple 매체 성과 리포트 Trace 최근 재처리 시각 (exclude)

#### 2. ZirconTrace 콜렉션
- D-1(target_ymd - 1), D-0(target_ymd), D+1(target_ymd + 1) 을 처리 한다. ( 심플성과는 타임존별 집계를 하므로 )
	- 오늘 이후 날짜는 제외 처리

#### 3. Jobs 콜렉션
- ZirconB 재처리 이력이 있는 경우, Jobs 콜렉션에 추가

#### 4. max_active_runs=1
"""

import os
import logging
import pendulum
from bson import ObjectId
from datetime import datetime, timedelta
from collections import defaultdict

from airflow.models import DAG, DagRun
from airflow.utils.state import DagRunState
from airflow.utils.trigger_rule import TriggerRule
from airflow.sensors.python import PythonSensor
from airflow.operators.python import PythonOperator, BranchPythonOperator

from core import utils
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, CHECK_TIMEOUT, XCOM_TARGET_YMD, XCOM_ZIRCON_TRACES
from core.dao import zircon_trace_dao, job_dao
from core.dao.environment_dao import get_environment_value_by_name, set_environment

from biz.performance.simple.spark_simple_performance import DAG_ID as SPARK_DAG_ID
from biz.zircon.b.trigger_zircon_b_regular_1d_ago import DAG_ID as ZB_REGULAR_1D_AGO_DAG_ID
from biz.zircon.b.trigger_zircon_b_regular_2d_ago import DAG_ID as ZB_REGULAR_2D_AGO_DAG_ID

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRACE-ZIRCON-B-FOR-SIMPLE-PERFORMANCE]'

_XCOM_CURRENT_YMDH = 'current_ymdh'


def _get_zircon_traces(**context):
	"""
	ZirconTrace 이력 조회
		- 지르콘 정규에서 실패한 건들이 재처리된 경우, Simple 매체 성과 리포트도 재처리 대상이 됨
			- 지르콘 정규 D-2 에서 실패한 채로 D-3 이 되어버린 경우도 포함됨
		- 현재 재처리 시작 시각
			- 현재 시각이 2024-08-14T12:05:00.000 인 경우, 현재 재처리 시작 시각은 2024081412 이다. 초단위까지 관리하기 번거로움
			- 2024-08-14T12:00:00.000 ~ 2024-08-14T12:05:00.000 사이 건들은 다음 주기에서 처리 된다.
		- 지르콘B 최근 컴팩션 일자 (D-3) 이전인 경우 ( target_ymd < zb_recent_ymd )
			- zirconBSucceededAt 이 Simple 매체 성과 리포트 Trace 최근 재처리 시각 이후(include)이면서 현재 재처리 시작 시각 이전(exclude)인 경우 대상임
			- Simple 매체 성과 리포트 Trace 최근 재처리 시각 = simple-performance-trace-recent-reprocess-ymdh = 2024081411
				- ex > 최근 재처리 시각이 2024081411 이고, 현재 재처리 시작 시각이 2024081412 인 경우
						2024-08-14T11:00:00.000 (include) ~ 2024-08-14T12:00:00.000 (exclude) 건들이 Jobs 추가 대상 이다.
		- 지르콘B 최근 컴팩션 일자 (D-3) 이후인 경우, 즉 D-1, D-2, D-3 인 경우 ( target_ymd >= zb_recent_ymd )
			- zirconBSucceededAt 이 Simple 매체 성과 리포트 정규 집계 최근 시작 시각 이후(include)이면서 현재 재처리 시작 시각 이전(exclude)인 경우 대상임
			- Simple 매체 성과 리포트 최근 정규 집계 시작 시각 = simple-performance-recent-regular-datetime = {'20240813':'2024-08-14T11:30:00.000'}
				- ex> 20240813 일자의 최근 시작 시각이 2024-08-14T11:30:00.000 이고, 현재 재처리 시작 시각이 2024081412 인 경우
						2024-08-14T11:30:00.000 (include) ~ 2024-08-14T12:00:00.000 (exclude) 건들이 Jobs 추가 대상 이다.
		- 현재 정규 처리 중인 일자인 경우, 스킵함
		- 지르콘 하루치가 처리된 경우, 심플성과는 3일치(D-1, D, D+1)를 재처리함

	:param context:
	:return:
	"""
	# 현재 재처리 시작 시각
	current_ymdh = pendulum.now(DEFAULT_TZ).format('YYYYMMDDHH')
	current_dt = pendulum.from_format(current_ymdh, 'YYYYMMDDHH', tz=DEFAULT_TZ)

	# 현재 재처리 시작 시각 XCOM push
	context['ti'].xcom_push(_XCOM_CURRENT_YMDH, current_ymdh)

	logging.info(f'{_LOG_PREFIX} current_ymdh = {current_ymdh}')
	logging.info(f'{_LOG_PREFIX} current_dt = {current_dt}')

	# Simple 매체 성과 리포트 최근 정규 집계 시작 시각
	# { 'yyyymmdd': datetime }
	sp_recent_regular_dt = dict(get_environment_value_by_name('simple-performance-recent-regular-datetime'))

	# 지르콘B 최근 컴팩션 일자 (D-3)
	zb_recent_ymd = get_environment_value_by_name('zircon-b-recent-compaction-ymd')

	# Simple 매체 성과 리포트 Trace 최근 재처리 시각
	spt_recent_reprocess_ymdh = str(get_environment_value_by_name('simple-performance-trace-recent-reprocess-ymdh'))
	spt_recent_dt = pendulum.from_format(spt_recent_reprocess_ymdh, 'YYYYMMDDHH', tz=DEFAULT_TZ)

	logging.info(f'{_LOG_PREFIX} sp_recent_regular_dt = {sp_recent_regular_dt}')
	logging.info(f'{_LOG_PREFIX} zb_recent_ymd = {zb_recent_ymd}')
	logging.info(f'{_LOG_PREFIX} spt_recent_reprocess_ymdh = {spt_recent_reprocess_ymdh}')
	logging.info(f'{_LOG_PREFIX} spt_recent_dt = {spt_recent_dt}')

	# 지르콘B 정규에서 처리 중인 날짜 조회
	running_regular_ymd_list = _get_zircon_regular_ymd_list_in_progress()

	# 지르콘B 최근 컴팩션 일자 이후 날짜 리스트 추출 ( D-3, D-2, D-1 )
	regular_ymd_list = utils.get_between_ymd_list(zb_recent_ymd, current_dt.add(days=-1).format('YYYYMMDD'))

	logging.info(f'{_LOG_PREFIX} running_regular_ymd_list = {running_regular_ymd_list}')
	logging.info(f'{_LOG_PREFIX} regular_ymd_list = {regular_ymd_list}')

	or_cond = [{
		'date': {'$lt': zb_recent_ymd},  # D-3 이전 날짜들 대상
		'normalConfirm': 1,
		'zirconBSucceededAt': {'$gte': spt_recent_dt, '$lt': current_dt},
	}]

	# D-3, D-2, D-1
	for regular_ymd in regular_ymd_list:
		# 정규 처리 중인 날짜인 경우, 스킵
		if regular_ymd in running_regular_ymd_list:
			continue

		# D-1 이 아직 처리 전인 경우가 있음 (00시 ~ 07시30분)
		# D-2 가 다음 날로 넘어 가서 처리 되는 경우가 있음 (D-3 이 됨)
		if sp_recent_regular_dt.get(regular_ymd):
			regular_ymd_recent_dt = pendulum.instance(sp_recent_regular_dt.get(regular_ymd))

			logging.info(f'{_LOG_PREFIX} regular_ymd = {regular_ymd}, regular_ymd_recent_dt = {regular_ymd_recent_dt}')

			or_cond.append({
				'date': regular_ymd,  # D-3, D-2, D-1 날짜들 대상
				'normalConfirm': 1,
				'zirconBSucceededAt': {'$gte': regular_ymd_recent_dt if regular_ymd_recent_dt > spt_recent_dt else spt_recent_dt, '$lt': current_dt},
			})

	logging.info(f'{_LOG_PREFIX} or_cond = {or_cond}')

	# ZirconTrace 조회 ( date, publisher_id, adProvider_id )
	zircon_traces = zircon_trace_dao.get_traces({'$or': or_cond}, {'_id': False, 'date': True, 'publisher_id': True, 'adProvider_id': True})

	# XCOM push 를 위해, mongodb doc 의 ObjectId 를 string 으로 일괄 변환
	zircon_traces = utils.transform_mongo_data(zircon_traces)

	# 지르콘 하루치가 처리된 경우, 심플성과는 3일치(D-1, D, D+1)를 재처리 해야하므로, D-1, D+1 날짜 정보를 추가함
	zircon_traces = _extend_zircon_traces(zircon_traces)

	logging.info(f'{_LOG_PREFIX} 처리할 zircon_traces 개수 = {len(zircon_traces)}')
	logging.info(f'{_LOG_PREFIX} zircon_traces = {zircon_traces}')

	if zircon_traces:
		context['ti'].xcom_push(XCOM_ZIRCON_TRACES, zircon_traces)

		return 'is_job_complete'
	else:
		logging.info(f'{_LOG_PREFIX} 처리할 zircon b trace 없음')

		return 'update_performance_trace_recent_reprocess_ymdh'


def _get_zircon_regular_ymd_list_in_progress():
	"""
	지르콘B 정규에서 처리 중인 날짜 조회
		- 리얼 환경에서 지르콘이 1시간 넘게 처리되면서, 정규 처리임에도 불구하고 trace 에 걸리는 건들이 발생함
	:return: [D-1, D-2]
	"""
	zb_regular_1d_ago = DagRun.find(dag_id=ZB_REGULAR_1D_AGO_DAG_ID, state=DagRunState.RUNNING)
	zb_regular_2d_ago = DagRun.find(dag_id=ZB_REGULAR_2D_AGO_DAG_ID, state=DagRunState.RUNNING)

	return [dag_run.get_task_instance('setup').xcom_pull(key=XCOM_TARGET_YMD) for dag_run in zb_regular_1d_ago + zb_regular_2d_ago]


def _get_surrounding_dates(ymd):
	"""
	D-1, D, D+1 날짜 목록 조회
		- 지르콘은 하루치지만, 심플 성과는 3일치를 처리해야 함
		- 오늘 이후 날짜는 제외 처리함
	:param ymd:
	:return: [ymd-1, ymd, ymd+1]
	"""
	# 문자열을 날짜 객체로 변환
	date = pendulum.from_format(ymd, 'YYYYMMDD')

	# D-1, D, D+1 날짜 목록
	date_list = [date.subtract(days=1).format('YYYYMMDD'), date.format('YYYYMMDD'), date.add(days=1).format('YYYYMMDD')]

	# 오늘 이후 날짜 제외
	return [date for date in date_list if date < datetime.now().strftime("%Y%m%d")]


def _extend_zircon_traces(zircon_traces):
	"""
	D-1, D, D+1 처리를 위해 지르콘 Trace 목록에 확장 추가
	:param zircon_traces:
	:return:
	"""

	# zircon_traces = [{ date:'20250102', 'adProvider_id': '123', 'publisher_id': '456' }]
	# result = [
	# 		{ date:'20250101', 'adProvider_id': '123', 'publisher_id': '456' },
	# 		{ date:'20250102', 'adProvider_id': '123', 'publisher_id': '456' },
	# 		{ date:'20250103', 'adProvider_id': '123', 'publisher_id': '456' }
	# ]
	extend_zircon_traces = [
		{**trace, 'date': date}
		for trace in zircon_traces  # 첫번째 loop
		for date in _get_surrounding_dates(trace['date'])  # 두번째 loop
	]

	# 중복 제거
	return sorted([dict(t) for t in {tuple(trace.items()) for trace in extend_zircon_traces}], key=lambda x: x['date'])


def _is_job_complete(**context):
	"""
	재처리 대상 일자 중에 이미 처리 중인 건이 있다면, 완료될 때까지 대기
		- 같은 날짜가 꼬이지 않도록 완료 후 재처리함

	:param context:
	:return is_complete: Boolean
	"""
	zircon_traces = context['ti'].xcom_pull(key=XCOM_ZIRCON_TRACES)

	jobs = job_dao.get_jobs({
		'type': SPARK_DAG_ID,
		'datetime': {'$in': [zircon_trace['date'] for zircon_trace in zircon_traces]},
		'running': 1
	})

	if jobs:
		target_ymd_list = [job['datetime'] for job in jobs]

		logging.info(f'{_LOG_PREFIX} target_ymd_list= {target_ymd_list}')

		running_dag_runs = DagRun.find(dag_id=SPARK_DAG_ID, state=DagRunState.RUNNING)
		for running_dag_run in running_dag_runs:
			ti = running_dag_run.get_task_instance('setup')
			running_target_ymd = ti.xcom_pull(key=XCOM_TARGET_YMD)
			if running_target_ymd in target_ymd_list:
				logging.info(f'{_LOG_PREFIX} 처리 중인 일자 = {running_target_ymd}')
				return False

	return True


def _insert_simple_performance_jobs(**context):
	"""
	ZirconB 재처리에 의한 Simple 매체 성과 리포트 재처리 job 추가
	:param context:
	:return:
	"""
	# [{ date, publisher_id, adProvider_id }]
	zircon_traces = context['ti'].xcom_pull(key=XCOM_ZIRCON_TRACES)

	logging.info(f'{_LOG_PREFIX} zircon_traces = {zircon_traces}')

	# 그룹화를 위한 defaultdict 생성 (복합 키 사용)
	grouped = defaultdict(list)

	# (date, publisher_id)를 기준으로 adProvider_id를 그룹화
	for trace in zircon_traces:
		grouped[(trace['date'], ObjectId(trace['publisher_id']))].append(ObjectId(trace['adProvider_id']))

	# [{ date, publisher_id, adProvider_ids }]
	grouped_zircon_traces = [
		{'date': date, 'publisher_id': publisher_id, 'adProvider_ids': sorted(adprovider_ids)}
		for (date, publisher_id), adprovider_ids in grouped.items()
	]

	logging.info(f'{_LOG_PREFIX} 처리 대상 일자 = 총 {len(grouped_zircon_traces)} 건')
	logging.info(f'{_LOG_PREFIX} grouped_zircon_traces = {grouped_zircon_traces}')

	inserted_cnt = 0
	for grouped_zircon_trace in grouped_zircon_traces:
		target_ymd = grouped_zircon_trace['date']
		publisher_id = grouped_zircon_trace['publisher_id']
		adprovider_ids = grouped_zircon_trace['adProvider_ids']

		filters = {
			'type': SPARK_DAG_ID,
			'datetime': target_ymd,
			'detail.publisher_id': publisher_id,
			'detail.adProvider_ids': adprovider_ids,
			'detail.types': None,
			'manual': 0,
		}
		job = job_dao.get_job(filters)

		# job 이 이미 있는 경우, retryCnt, modifiedAt 리셋
		if job:
			job_dao.update_job(filters, {'$set': {'retryCnt': 0, 'modifiedAt': pendulum.now(DEFAULT_TZ)}})
		else:
			# job 이 없다면 추가
			job_dao.insert_job({
				'type': SPARK_DAG_ID,
				'datetime': target_ymd,
				'detail': {
					'publisher_id': publisher_id,
					'adProvider_ids': adprovider_ids,
					'types': None,
				},
				'manual': 0,
				'running': 0,
				'retryCnt': 0,  # 재처리는 0으로 설정. -1은 정규 또는 수동 처리 에서만 사용.
			})
			inserted_cnt = inserted_cnt + 1

	logging.info(f'{_LOG_PREFIX} Jobs 에 추가 완료 ( {inserted_cnt} 건 )')


def _update_performance_trace_recent_reprocess_ymdh(**context):
	"""
	Simple 매체 성과 리포트 Trace 최근 재처리 시각을 현재 재처리 시작 시각으로 갱신
	:param context:
	:return:
	"""
	# 현재 재처리 시작 시각
	current_ymdh = context['ti'].xcom_pull(key=_XCOM_CURRENT_YMDH)

	# Simple 매체 성과 리포트 Trace 최근 재처리 시각 갱신
	set_environment('simple-performance-trace-recent-reprocess-ymdh', current_ymdh)


with DAG(
		_DAG_ID,
		description='ZirconTrace 에 의한 Simple 매체 성과 리포트 재처리 Jobs 추가 DAG',
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2024, 4, 1, tz=DEFAULT_TZ),
		schedule_interval='5 * * * *',
		tags=['trace', 'zircon_b', 'pr', 'simple_performance', 'simple', 'bitna.cho'],
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# ZirconB 재처리 이력 조회
	get_zircon_traces = BranchPythonOperator(
		task_id='get_zircon_traces',
		python_callable=_get_zircon_traces,
	)

	# 이미 처리 중인 날짜가 있다면, 끝날 때까지 대기
	is_job_complete = PythonSensor(
		task_id='is_job_complete',
		python_callable=_is_job_complete,
		poke_interval=60 * 10,  # 10분 마다 재시도
		execution_timeout=timedelta(seconds=CHECK_TIMEOUT),  # 3시간 동안
	)

	# ZirconB 재처리에 의한 Simple 매체 성과 리포트 재처리 job 추가
	insert_simple_performance_jobs = PythonOperator(
		task_id='insert_simple_performance_jobs',
		python_callable=_insert_simple_performance_jobs,
	)

	# Simple 매체 성과 리포트 Trace 최근 재처리 시각을 현재 재처리 시작 시각으로 갱신
	update_performance_trace_recent_reprocess_ymdh = PythonOperator(
		task_id='update_performance_trace_recent_reprocess_ymdh',
		trigger_rule=TriggerRule.NONE_FAILED,
		python_callable=_update_performance_trace_recent_reprocess_ymdh,
	)

	# 파이프라인
	get_zircon_traces >> [is_job_complete, update_performance_trace_recent_reprocess_ymdh]
	is_job_complete >> insert_simple_performance_jobs >> update_performance_trace_recent_reprocess_ymdh
