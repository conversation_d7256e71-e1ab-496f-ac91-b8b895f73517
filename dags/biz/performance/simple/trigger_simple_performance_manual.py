"""
### Simple 매체 성과 리포트 수동 트리거 DAG

#### 0. 위키
- [17. 매체 성과 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=**********)

#### 1. 주기
- 없음

#### 2. 스케쥴 콜렉션 : Jobs
- manual=1 인 경우만 처리 대상임

#### 3. 트리거 대상 DAG
- spark_simple_performance

#### 4. config
- 날짜 (required)
	- target_ymd_from, target_ymd_to : 두 날짜 사이의 모든 스케줄 실행
	- target_ymd_list : 처리할 스케쥴 날짜 리스트 (YYYYMMDD,YYYYMMDD,YYYYMMDD)
	- 모두 입력하지 않은 경우 : 에러
- publisher_id (optional) : 매체 id
	- 없으면 전체 처리
- adprovider_id (optional) : 광고공급자 id
	- 없으면 전체 처리
- types (optional) : adprovider / adunit
	- 없으면 전체 처리
	- 수동 처리 외에는 사용 하지 않음
- parallel_count (optional) : 날짜 기준 동시 처리 개수
	- 없으면 순차 처리
"""

import os
import logging
import pendulum
from bson import ObjectId

from time import sleep
from datetime import timedelta

from airflow.models import DAG, DagRun
from airflow.utils.email import send_email
from airflow.utils.types import DagRunType
from airflow.utils.state import DagRunState
from airflow.exceptions import AirflowFailException
from airflow.operators.python import PythonOperator

from core.dao import job_dao
from core.airflow_api import trigger_dagrun
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, CHECK_TIMEOUT, \
	XCOM_TARGET_YMD, XCOM_TARGET_YMD_LIST, XCOM_SKIPPED_LIST, XCOM_FAILED_LIST, XCOM_PUBLISHER_ID, XCOM_ADPROVIDER_ID, XCOM_ADPROVIDER_IDS

from biz.performance.simple.spark_simple_performance import DAG_ID as SPARK_DAG_ID
from biz.performance.simple.simple_performance_base import XCOM_TYPES, XCOM_PARALLEL_COUNT, ADPROVIDER, ADUNIT
from core.utils import get_between_ymd_list

# DAG 기본정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRIGGER-SIMPLE-PERFORMANCE-MANUAL]'


def _setup(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화

	:param context:
	:return:
	"""
	_init_settings(**context)
	_print_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
		- target_ymd_list (required)
		- publisher_id / adprovider_id / types / parallel_count (optional)
	:param context:
	:return:
	"""
	_set_target_ymd_list(**context)

	publisher_id = context['params'].get(XCOM_PUBLISHER_ID)
	adprovider_id = context['params'].get(XCOM_ADPROVIDER_ID)
	types = context['params'].get(XCOM_TYPES)
	parallel_count = context['params'].get(XCOM_PARALLEL_COUNT)

	# ObjectId 타입 체크
	if publisher_id and not ObjectId.is_valid(publisher_id):
		raise AirflowFailException(f'"publisher_id" 가 ObjectId 타입이 아님')

	# ObjectId 타입 체크
	if adprovider_id and not ObjectId.is_valid(adprovider_id):
		raise AirflowFailException(f'"adprovider_id" 가 ObjectId 타입이 아님')

	publisher_id = str(publisher_id) if publisher_id else None
	adprovider_id = str(adprovider_id) if adprovider_id else None
	types = types if ADPROVIDER == types or ADUNIT == types else None
	parallel_count = int(parallel_count) if parallel_count else 1

	logging.info(f'{_LOG_PREFIX} publisher_id= {publisher_id}, adprovider_id= {adprovider_id}, types= {types}, parallel_count= {parallel_count}')

	context['ti'].xcom_push(XCOM_PUBLISHER_ID, publisher_id)
	context['ti'].xcom_push(XCOM_ADPROVIDER_ID, adprovider_id)
	context['ti'].xcom_push(XCOM_TYPES, types)
	context['ti'].xcom_push(XCOM_PARALLEL_COUNT, parallel_count)


def _set_target_ymd_list(**context):
	"""
	config 로부터 받은 날짜 리스트를 추출하여 xcom push
	:param context:
	:return:
	"""

	ymd_from = context['params'].get('target_ymd_from')
	ymd_to = context['params'].get('target_ymd_to')
	ymd_list = context['params'].get(XCOM_TARGET_YMD_LIST)

	target_ymd_list = []

	if ymd_from and ymd_to:
		ymd_from = ymd_from.strip()
		ymd_to = ymd_to.strip()
		pendulum.from_format(ymd_from, 'YYYYMMDD')
		pendulum.from_format(ymd_to, 'YYYYMMDD')

		if ymd_from <= ymd_to:
			target_ymd_list.extend(get_between_ymd_list(ymd_from, ymd_to))
			logging.info(f'{_LOG_PREFIX} ymd_from={ymd_from}, ymd_to={ymd_to}, target_ymd_list={target_ymd_list}')
		else:
			raise AirflowFailException(f'target_ymd_from 는 target_ymd_to 보다 작거나 같아야 합니다.'
								   f' target_ymd_from={ymd_from}, target_ymd_to={ymd_to}')
	elif ymd_list:
		ymd_list = ymd_list.replace(' ', '').strip(',')

		# target_ymd_list 에 추가
		target_ymd_list.extend(ymd_list.split(','))

		for i, ymd in enumerate(target_ymd_list):
			pendulum.from_format(ymd, 'YYYYMMDD')

		logging.info(f'{_LOG_PREFIX} target_ymd_list 를 이용한 실행. {target_ymd_list}')
	else:
		raise AirflowFailException(f'"config"에 날짜 정보가 없음 (target_ymd_from&target_ymd_to 또는 target_ymd_list)')

	context['ti'].xcom_push(XCOM_TARGET_YMD_LIST, target_ymd_list)


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Params:
	trigger_ymd_list= {context["ti"].xcom_pull(key=XCOM_TARGET_YMD_LIST)}
	publisher_id= {context["ti"].xcom_pull(key=XCOM_ADPROVIDER_ID)}
	adprovider_id= {context["ti"].xcom_pull(key=XCOM_PUBLISHER_ID)}
	types= {context["ti"].xcom_pull(key=XCOM_TYPES)}
------------------------------------------------------------------------------------------
''')


def _filter_skipped_list(**context):
	"""
	target_ymd_list 에서 스킵된 날짜 제외 처리
	:param context:
	:return:
	"""
	target_ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)
	publisher_id = context['ti'].xcom_pull(key=XCOM_PUBLISHER_ID)
	adprovider_id = context['ti'].xcom_pull(key=XCOM_ADPROVIDER_ID)
	types = context['ti'].xcom_pull(key=XCOM_TYPES)

	logging.info(f'{_LOG_PREFIX} target_ymd_list= {target_ymd_list}, publisher_id= {publisher_id}, adprovider_id= {adprovider_id}, types= {types}')

	# 스킵된 날짜 리스트
	skipped_list = []

	# job 이 이미 있는 경우, 스킵 처리함

	# 전체 리포트 유형 또는 같은 리포트 유형이 처리 중인게 있는가
	types_cond = {'detail.types': None} if types is None else {'$or': [{'detail.types': None}, {'detail.types': types}]}

	if publisher_id is None:
		# param 에 매체 정보가 없는 경우
		# 	전체 리포트 유형 또는 같은 리포트 유형이 처리 중인게 있는가
		or_cond = {**types_cond}
	else:
		# param 에 매체 정보가 있는 경우
		# 	전체 매체의 전체 리포트 유형 또는 같은 리포트 유형이 처리 중인게 있는가
		# 	해당 매체의 전체 리포트 유형 또는 같은 리포트 유형이 처리 중인게 있는가
		or_cond = {'$or': [
			{'detail.publisher_id': None, **types_cond},
			{'detail.publisher_id': ObjectId(publisher_id), **types_cond},
		]}

	jobs = job_dao.get_jobs({
		'type': SPARK_DAG_ID,
		'datetime': {'$in': target_ymd_list},
		**or_cond
	})

	jobs = [job for job in jobs if job['manual'] == 0 or job['running'] == 1]

	if jobs:
		logging.warning(f'{_LOG_PREFIX} 정규 처리 또는 이미 처리 중인 날짜가 있으므로 skip 처리함. \n jobs= {jobs}')

		skipped_list = [job['datetime'] for job in jobs]

		# target_ymd_list 에서 스킵된 날짜 제외 처리
		target_ymd_list = sorted(list(set(target_ymd_list) - set(skipped_list)))

	logging.info(f'{_LOG_PREFIX} target_ymd_list= {target_ymd_list}, skipped_list= {skipped_list}')

	context['ti'].xcom_push(XCOM_SKIPPED_LIST, skipped_list)
	context['ti'].xcom_push(XCOM_TARGET_YMD_LIST, target_ymd_list)


def _trigger_simple_performance(**context):
	"""
	target_ymd_list 에 있는 날짜에 대해 스파크 집계 처리함

	:param context:
	:return:
	"""
	target_ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)
	publisher_id = context['ti'].xcom_pull(key=XCOM_PUBLISHER_ID)
	adprovider_id = context['ti'].xcom_pull(key=XCOM_ADPROVIDER_ID)
	types = context['ti'].xcom_pull(key=XCOM_TYPES)
	parallel_count = context['ti'].xcom_pull(key=XCOM_PARALLEL_COUNT)

	logging.info(f'{_LOG_PREFIX} target_ymd_list= {target_ymd_list}, publisher_id= {publisher_id}, adprovider_id= {adprovider_id}, types= {types}, parallel_count= {parallel_count}')

	# 처리할 총 건수
	total_count = len(target_ymd_list)

	# 집계 완료 건수 (성공/실패 구분 안 함)
	complete_count = 0

	# 처리 중인 내역
	# [{ ymd, dag_run_id }]
	running_list = []

	# 실패한 스케쥴 리스트
	failed_list = []

	while True:
		# 모든 리포트 집계 처리 완료 되어 종료 처리함
		if total_count == complete_count:
			logging.info(f'{_LOG_PREFIX} 집계 처리 완료 (총 {complete_count} 건)')
			break

		running_count = len(running_list)

		# 현재 처리 중인 건수가 parallel_count 이상인 경우, 완료될 때까지 대기함
		if running_count >= parallel_count or total_count - complete_count == running_count:
			logging.info(f'{_LOG_PREFIX} 끝날 때까지 대기함 ( parallel_count= {parallel_count}, running_count= {running_count}, running_list= {running_list} )')

			# 처리 중인 게 완료될 때까지 대기
			failed_list += _wait_for_complete(running_list)

			complete_count += len(running_list)
			running_list.clear()

			continue

		logging.info(f'{_LOG_PREFIX} total_count= {total_count}, complete_count= {complete_count}, running_count= {running_count}')

		if target_ymd_list:
			# 날짜 순으로 정렬 되어 있다.
			target_ymd = target_ymd_list.pop(0)

			# Simple 매체 성과 리포트 집계 DAG 트리거
			dag_run_id = trigger_dagrun(SPARK_DAG_ID, pendulum.now(DEFAULT_TZ), {
				XCOM_TARGET_YMD: target_ymd,
				XCOM_PUBLISHER_ID: publisher_id,
				XCOM_ADPROVIDER_IDS: adprovider_id,
				XCOM_TYPES: types,
				'trigger_by': 'manual'
			}, DagRunType.MANUAL)

			logging.info(f'{_LOG_PREFIX} Simple 매체 성과 리포트 집계 DAG 트리거 ( spark_dag_id= {SPARK_DAG_ID}, dag_run_id={dag_run_id}, ' 
				f'target_ymd= {target_ymd}, publisher_id= {publisher_id}, adprovider_id= {adprovider_id}, types= {types} )')

			running_list.append({'ymd': target_ymd, 'dag_run_id': dag_run_id})

	context['ti'].xcom_push(XCOM_FAILED_LIST, failed_list)

	logging.info(f'{_LOG_PREFIX} 완료')


def _wait_for_complete(running_list: list):
	"""
	처리 중인 게 완료될 때까지 대기
	:return:
	"""
	# 실패한 스케쥴 리스트
	failed_list = []

	for running in running_list:
		ymd = running['ymd']
		dag_run_id = running['dag_run_id']

		# dag run 이 끝났는지 30초 간격으로 확인
		while True:
			sleep(30)
			dag_runs = DagRun.find(dag_id=SPARK_DAG_ID, run_id=dag_run_id)
			if dag_runs:
				dag_run: DagRun = dag_runs.pop()

				logging.info(f'{_LOG_PREFIX} {SPARK_DAG_ID}({dag_run_id}) state = {dag_run.get_state()}')
				if dag_run.get_state() == DagRunState.SUCCESS:
					break
				elif dag_run.get_state() == DagRunState.FAILED:
					failed_list.append((ymd, f'dag_run_id={dag_run_id}'))
					break
			else:
				failed_list.append((ymd, f'dag_run_id={dag_run_id} 트리거 시켰으나, 존재하지 않음'))
				logging.info(f'{_LOG_PREFIX} {SPARK_DAG_ID}({dag_run_id}) 트리거 시켰으나, 존재하지 않음')
				break

	return failed_list


def _alert(**context):
	"""
	스킵 또는 실패된 리스트 알림
	:param context:
	:return:
	"""
	skipped_list = context['ti'].xcom_pull(key=XCOM_SKIPPED_LIST)
	skipped_msg = f'skipped {len(skipped_list)} 건 <br/>'

	for item in skipped_list:
		skipped_msg += f'- ymd={item}<br/>'

	failed_list = context['ti'].xcom_pull(key=XCOM_FAILED_LIST)
	failed_msg = f'failed {len(failed_list)} 건 <br/>'
	for item in failed_list:
		failed_msg += f'- ymd={item[0]}, reason={item[1]}<br/>'

	logging.info(f'{_LOG_PREFIX} skipped_list= {skipped_list}, failed_list= {failed_list}')

	if skipped_list or failed_list:
		title = f'[Simple 매체 성과 리포트 수동 처리] skipped {len(skipped_list)} 건, failed {len(failed_list)} 건'
		body = f'{skipped_msg}<br/>{failed_msg}<br/>'
		send_email(ALERT_EMAIL_ADDRESSES, title, body)


with DAG(
		_DAG_ID,
		description='Simple 매체 성과 리포트 수동처리 DAG',
		tags=['trigger', 'manual', 'pr', 'simple_performance', 'simple', 'daily', 'data_db', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			XCOM_TARGET_YMD_LIST: '',
			XCOM_PUBLISHER_ID: '',
			XCOM_ADPROVIDER_ID: '',
			XCOM_TYPES: 'adprovider or adunit',
			XCOM_PARALLEL_COUNT: '',
		},
		start_date=pendulum.datetime(2024, 8, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup
	)

	# target_ymd_list 에서 스킵된 날짜 제외 처리
	filter_skipped_list = PythonOperator(
		task_id='filter_skipped_list',
		python_callable=_filter_skipped_list
	)

	# target_ymd_list 에 있는 날짜에 대해 스파크 집계 처리
	trigger_simple_performance = PythonOperator(
		task_id=f'trigger_simple_performance',
		python_callable=_trigger_simple_performance,
		execution_timeout=timedelta(seconds=CHECK_TIMEOUT),  # 타임아웃 3시간
	)

	alert = PythonOperator(
		task_id='alert',
		python_callable=_alert,
	)

	setup >> filter_skipped_list >> trigger_simple_performance >> alert
