import logging
import pendulum

from core.base import DEFAULT_TZ
from core.dao.environment_dao import get_environment_value_by_name, update_environment
from core.utils import get_between_ymd_list

# XCOM 정보 - Simple 성과
XCOM_ONE_DAY_AGO = 'one_day_ago'
XCOM_TYPES = 'types'
XCOM_ADPROVIDER_STATE = 'adprovider_state'
XCOM_ADUNIT_STATE = 'adunit_state'
XCOM_PARALLEL_COUNT = 'parallel_count'

# Spark App 정보 - Simple 성과
ADPROVIDER = 'adprovider'
ADUNIT = 'adunit'

_LOG_PREFIX = '.......... [SIMPLE-PERFORMANCE-BASE]'


def update_recent_regular_datetime(target_ymd: str):
	"""
	Simple 매체 성과 리포트 최근 정규 집계 시작 시각 갱신 (simple-performance-recent-regular-datetime)
		- trace_zircon_b_for_simple_performance 에서 D-3, D-2, D-1 처리 시, 시작 시각 이후 건들을 대상으로 처리하도록 하기 위함
	"""
	# 지르콘B 최근 컴팩션 일자 (D-3)
	zb_recent_ymd = get_environment_value_by_name('zircon-b-recent-compaction-ymd')

	logging.info(f'{_LOG_PREFIX} zb_recent_ymd= {zb_recent_ymd}, target_ymd= {target_ymd}')

	# 지르콘 최근 컴팩션 일자 이후를 대상으로 정규 집계 시작 시각을 남긴다.
	if zb_recent_ymd < target_ymd:
		# 지르콘 최근 컴팩션 일자 -4 부터 ~ 지르콘 최근 컴팩션 일자 -1 까지의 yyyymmdd 리스트 추출 (3일치)
		# 	- 지르콘 최근 컴팩션 일자가 20240811 인 경우, str_dates = ['20240808', '20240809', '20240810']
		zb_recent_4days_ago_dt = pendulum.from_format(zb_recent_ymd, 'YYYYMMDD', tz=DEFAULT_TZ).add(days=-4)
		zb_recent_1days_ago_dt = pendulum.from_format(zb_recent_ymd, 'YYYYMMDD', tz=DEFAULT_TZ).add(days=-1)
		delete_ymd_list = get_between_ymd_list(zb_recent_4days_ago_dt.format('YYYYMMDD'), zb_recent_1days_ago_dt.format('YYYYMMDD'))

		update = {
			# 지르콘 최근 컴팩션 일자 이전 날짜들에 대한 시작 시각 이력 제거 처리
			'$unset': {f'value.{delete_ymd}': '' for delete_ymd in delete_ymd_list},

			# target_ymd 의 시작 시각을 현재 시각으로 설정
			'$set': {f'value.{target_ymd}': pendulum.now(DEFAULT_TZ)},
		}

		update_environment('simple-performance-recent-regular-datetime', update)
