"""
### Simple 매체 성과 리포트 스파크 집계

#### 1. 위키
- [17. 매체 성과 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=**********)

#### 2. 저장소
- Data DB
	- PerformanceAdProviderDaily
	- PerformanceAdUnitDaily

#### 3. config
- target_ymd : YYYYMMDD
- publisher_id : 매체 ID
	- 전체 처리 : ''
- adprovider_ids : 광고공급자 ID 리스트
	- 전체 처리 : ''
- types : 리포트 유형
	- 전체 처리 : ''
	- 특정 집계만 처리 : 'adprovider' or 'adunit' (수동 처리용)

#### 4. 특이사항
- publisher_id 가 존재하는 경우, 적은 리소스를 사용하도록 분기 처리한다.
"""

import os
import logging
import pendulum
from typing import List
from bson import ObjectId
from functools import partial

from airflow.models import DAG, DagRun
from airflow.utils.types import DagRunType
from airflow.utils.state import DagRunState
from airflow.utils.trigger_rule import TriggerRule
from airflow.exceptions import AirflowFailException, AirflowException
from airflow.operators.python import PythonOperator, BranchPythonOperator

from core import utils
from core.base import DEFAULT_TZ, PROFILE, ALERT_EMAIL_ADDRESSES, \
	XCOM_TARGET_YMD, XCOM_PUBLISHER_ID, XCOM_ADPROVIDER_IDS, \
	PROFILE_LOCAL, PROFILE_DEV, PROFILE_TEST, PROFILE_STAGE, PROFILE_REAL, SPARK_SUBMIT_OPTIONS, \
	POOL_SPARK, SPARKLING_APP_JAR_PATH, SPARKLING_IMAGE, \
	SPARKLING_APP_HARD_LIMIT_4 as SPARKLING_APP_HARD_LIMIT, \
	ST_READY, ST_COMPLETE, ST_FAILURE
from core.dao import job_dao
from core.spark_pool import POOL_SLOT_LIGHT_10
from core.spark_submit_op import invoke_job_with_args
from core.spark_task_group import create_task_group

from biz.performance.simple.simple_performance_base import \
	XCOM_TYPES, XCOM_ADPROVIDER_STATE, XCOM_ADUNIT_STATE, ADPROVIDER, ADUNIT, \
	update_recent_regular_datetime

# DAG 기본 정보
DAG_ID = _DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [SPARK-SIMPLE-PERFORMANCE]'

# Spark
_SPARK_APP_CLASS = {
	ADPROVIDER: 'com.navercorp.gfp.biz.performance.simple.PerformanceAdProviderDailyAggregator',
	ADUNIT: 'com.navercorp.gfp.biz.performance.simple.PerformanceAdUnitDailyAggregator'
}


def _get_profile_real(type=None, use_min=False):
	if use_min and type == ADUNIT:
		return f"""
			--num-executors 10
			--executor-cores 4
			--executor-memory 2g
			--conf spark.sql.shuffle.partitions=500
			--conf spark.executor.memoryOverhead=500m
			--conf spark.sql.files.maxPartitionBytes=64mb
			""".split()
	elif use_min and type == ADPROVIDER:
		return f"""
			--num-executors 5
			--executor-cores 4
			--executor-memory 2g
			--conf spark.sql.shuffle.partitions=500
			--conf spark.executor.memoryOverhead=500m
			--conf spark.sql.files.maxPartitionBytes=64mb
			""".split()
	else:
		return f"""
			--num-executors 20
			--executor-cores 3
			--executor-memory 2g
			--conf spark.sql.shuffle.partitions=500
			--conf spark.executor.memoryOverhead=500m
			--conf spark.sql.files.maxPartitionBytes=64mb
			""".split()


# Profile
_PROFILE_SETTINGS = {
	PROFILE_TEST: {
		ADUNIT: {
			SPARK_SUBMIT_OPTIONS: """
				--num-executors 10
				--executor-cores 3
				--executor-memory 2g
				--conf spark.executor.memoryOverhead=500m
				--conf spark.sql.shuffle.partitions=500
				--conf spark.sql.files.maxPartitionBytes=64mb
				""".split()
		},
		ADPROVIDER: {
			SPARK_SUBMIT_OPTIONS: """
				--num-executors 10
				--executor-cores 3
				--executor-memory 2g
				--conf spark.executor.memoryOverhead=500m
				--conf spark.sql.shuffle.partitions=500
				--conf spark.sql.files.maxPartitionBytes=64mb
				""".split()
		},
	},
	PROFILE_REAL: {
		ADUNIT: {SPARK_SUBMIT_OPTIONS: _get_profile_real()},
		ADPROVIDER: {SPARK_SUBMIT_OPTIONS: _get_profile_real()}
	},
}
_PROFILE_SETTINGS[PROFILE_LOCAL] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_DEV] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_STAGE] = _PROFILE_SETTINGS[PROFILE_REAL]


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	_init_settings(**context)
	_print_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
	:param context:
	:return:
	"""
	target_ymd = context['params'][XCOM_TARGET_YMD]
	publisher_id = context['params'][XCOM_PUBLISHER_ID]
	adprovider_ids = context['params'][XCOM_ADPROVIDER_IDS]
	types = context['params'][XCOM_TYPES]

	if target_ymd:
		target_ymd = target_ymd.strip()
		pendulum.from_format(target_ymd, 'YYYYMMDD')

		publisher_id = publisher_id if publisher_id else None
		adprovider_ids = adprovider_ids if adprovider_ids else None
		types = types if ADPROVIDER == types or ADUNIT == types else None

		context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)
		context['ti'].xcom_push(XCOM_PUBLISHER_ID, publisher_id)
		context['ti'].xcom_push(XCOM_ADPROVIDER_IDS, adprovider_ids)
		context['ti'].xcom_push(XCOM_TYPES, types)

		# 전체 처리인 경우
		if not publisher_id and not types:
			# Simple 매체 성과 리포트 최근 정규 집계 시작 시각 갱신 (simple-performance-recent-regular-datetime)
			update_recent_regular_datetime(target_ymd)

		if publisher_id and not ObjectId.is_valid(publisher_id):
			raise AirflowFailException(f'"publisher_id" 가 ObjectId 타입이 아님')

		if adprovider_ids and not all(ObjectId.is_valid(oid) for oid in utils.csv_to_str_arr(adprovider_ids)):
			raise AirflowFailException(f'"adprovider_ids" 가 ObjectId 타입이 아님')

		# 리얼 환경 :: publisher_id 가 존재 하는 경우, Min Resource 를 사용하도록 Spark Submit Option 변경
		if PROFILE == PROFILE_REAL and publisher_id:
			logging.info(f"publisher_id = {publisher_id}, use min resource")
			_PROFILE_SETTINGS[PROFILE_REAL] = {
				ADUNIT: {SPARK_SUBMIT_OPTIONS: _get_profile_real(type=ADUNIT, use_min=True)},
				ADPROVIDER: {SPARK_SUBMIT_OPTIONS: _get_profile_real(type=ADPROVIDER, use_min=True)}
			}
	else:
		# target_ymd 가 없으면 에러
		logging.error(f'There is not "params.target_ymd"')
		raise AirflowFailException('There is not "params.target_ymd"')


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK}
Airflow Pool Slot: {POOL_SLOT_LIGHT_10}

Profile: {PROFILE}
Spark Submit Options: {_PROFILE_SETTINGS[PROFILE]}

Params:
	target_ymd= {context['ti'].xcom_pull(key=XCOM_TARGET_YMD)}
	publisher_id= {context['ti'].xcom_pull(key=XCOM_PUBLISHER_ID)}
	adprovider_ids= {context['ti'].xcom_pull(key=XCOM_ADPROVIDER_IDS)}
	types= {context['ti'].xcom_pull(key=XCOM_TYPES)}
------------------------------------------------------------------------------------------
''')


def _check_dup_run(**context):
	"""
	중복 실행을 방지하기 위한 방어 로직
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	publisher_id = context['ti'].xcom_pull(key=XCOM_PUBLISHER_ID)
	types = context['ti'].xcom_pull(key=XCOM_TYPES)

	# 전체 리포트 유형 또는 같은 리포트 유형이 처리 중인게 있는가
	types_cond = {'detail.types': None} if types is None else {'$or': [{'detail.types': None}, {'detail.types': types}]}

	if publisher_id is None:
		# param 에 매체 정보가 없는 경우
		# 	전체 리포트 유형 또는 같은 리포트 유형이 처리 중인게 있는가
		or_cond = {**types_cond}
	else:
		# param 에 매체 정보가 있는 경우
		# 	전체 매체의 전체 리포트 유형 또는 같은 리포트 유형이 처리 중인게 있는가
		# 	해당 매체의 전체 리포트 유형 또는 같은 리포트 유형이 처리 중인게 있는가
		or_cond = {'$or': [
			{'detail.publisher_id': None, **types_cond},
			{'detail.publisher_id': ObjectId(publisher_id), **types_cond},
		]}

	filters = {
		'type': _DAG_ID,
		'datetime': target_ymd,
		'running': 1,
		**or_cond
	}

	job = job_dao.get_job(filters)

	if job:
		dag_run_id = context['dag_run'].run_id
		running_dag_runs: List["DagRun"] = DagRun.find(dag_id=_DAG_ID, state=DagRunState.RUNNING)
		for running_dag_run in running_dag_runs:
			ti = running_dag_run.get_task_instance('setup')
			running_target_ymd = ti.xcom_pull(key=XCOM_TARGET_YMD)
			if dag_run_id != running_dag_run.run_id and target_ymd == running_target_ymd:
				raise AirflowException(f'이미 실행 중인 DagRun 이 있음 ({running_dag_run.run_id} :: {running_target_ymd})')
	else:
		pass


def _upsert_job(**context):
	"""
	해당 시간대의 Job 추가 또는 running=1 로 업데이트
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	publisher_id = context['ti'].xcom_pull(key=XCOM_PUBLISHER_ID)
	adprovider_ids = context['ti'].xcom_pull(key=XCOM_ADPROVIDER_IDS)
	types = context['ti'].xcom_pull(key=XCOM_TYPES)

	filters = {
		'type': _DAG_ID,
		'datetime': target_ymd,
		'detail.publisher_id': ObjectId(publisher_id) if publisher_id else None,
		'detail.adProvider_ids': sorted(utils.csv_to_id_arr(adprovider_ids)) if adprovider_ids else None,
		'detail.types': types if types else None,
	}

	logging.info(f'{_LOG_PREFIX} filters= {filters}')

	job = job_dao.get_job(filters)

	state = {
		ADPROVIDER: ST_READY if (types is None or ADPROVIDER in types) else None,
		ADUNIT: ST_READY if (types is None or ADUNIT in types) else None
	}

	if job:
		state = job.get('detail', {}).get('state', state)
		update = {'$set': {
			'detail.state': state,
			'running': 1,
			'modifiedAt': pendulum.now(tz=DEFAULT_TZ)
		}}
		job_dao.update_job(filters, update)
	else:
		trigger_by = context['params'].get('trigger_by')
		dag_run = context['dag_run']
		job = {
			'type': _DAG_ID,
			'datetime': target_ymd,
			'detail': {
				'publisher_id': ObjectId(publisher_id) if publisher_id else None,
				'adProvider_ids': sorted(utils.csv_to_id_arr(adprovider_ids)) if adprovider_ids else None,
				'types': types if types else None,
				'state': state
			},
			'retryCnt': -1,
			'running': 1,
			'manual': 1 if trigger_by == DagRunType.MANUAL else 0
		}
		job_dao.insert_job(job)
		logging.info(f'{_LOG_PREFIX} {target_ymd} Jobs 에 추가. {job}')

	logging.info(f'{_LOG_PREFIX} state= {state}')

	# 리포트 집계 상태 조회 및 XCOM PUSH
	# 해당 정보를 기반으로 리포트 집계 실행 여부를 판단한다.
	context['ti'].xcom_push(XCOM_ADPROVIDER_STATE, state[ADPROVIDER])
	context['ti'].xcom_push(XCOM_ADUNIT_STATE, state[ADUNIT])


def _branch_spark_run(**context):
	"""
	state 각 항목이 READY 또는 FAILURE 인 경우, 스파크 집계 분기 처리
		- 상태 : None, READY, COMPLETE, FAILURE
		- ADPROVIDER : spark_run_performance_adprovider
		- ADUNIT : spark_run_performance_adunit
	:param context:
	:return:
	"""
	adprovider_state = context['ti'].xcom_pull(key=XCOM_ADPROVIDER_STATE)
	adunit_state = context['ti'].xcom_pull(key=XCOM_ADUNIT_STATE)

	logging.info(f'{_LOG_PREFIX} adprovider_state= {adprovider_state}, adunit_state= {adunit_state}')

	branch_list = []
	if adprovider_state in (ST_READY, ST_FAILURE):
		branch_list.append(f'spark_run_performance_{ADPROVIDER}.prepare_summary_history')

	if adunit_state in (ST_READY, ST_FAILURE):
		branch_list.append(f'spark_run_performance_{ADUNIT}.prepare_summary_history')

	if not branch_list:
		raise AirflowFailException(f'처리할 리포트 집계가 없습니다. (adprovider_state= {adprovider_state}, adunit_state= {adunit_state})')

	return branch_list


def _get_app_args(context: dict):
	"""
	spark-submit 시, 넘겨줄 파라미터 정보
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	publisher_id = context['ti'].xcom_pull(key=XCOM_PUBLISHER_ID)
	adprovider_ids = context['ti'].xcom_pull(key=XCOM_ADPROVIDER_IDS)

	publisher_id = publisher_id if publisher_id else ''
	adprovider_ids = adprovider_ids if adprovider_ids else ''

	if adprovider_ids and publisher_id == '':
		publisher_id = '*'

	logging.info(f'{_LOG_PREFIX} target_ymd= {target_ymd}, publisher_id= {publisher_id}, adprovider_ids= {adprovider_ids}')

	return [target_ymd, publisher_id, adprovider_ids]


def _update_state(type: str, is_success: bool, context: dict):
	"""
	리포트 집계 처리 상태 업데이트
	:param type:
	:param is_success:
	:return:
	"""
	logging.info(f'{_LOG_PREFIX} type= {type}, is_success= {is_success}')

	if type == ADPROVIDER:
		context['ti'].xcom_push(XCOM_ADPROVIDER_STATE, ST_COMPLETE if is_success else ST_FAILURE)
	else:
		context['ti'].xcom_push(XCOM_ADUNIT_STATE, ST_COMPLETE if is_success else ST_FAILURE)


def _create_task_group(
		*,
		task_group_id: str,
		type: str,
):
	"""
	task group 생성을 위한 공통 로직
	:param task_group_id:
	:param type:
	:return:
	"""
	return create_task_group(
		task_group_id,
		tooltip=_SPARK_APP_CLASS[type],
		spark_pool=POOL_SPARK,
		pool_slot=POOL_SLOT_LIGHT_10,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': f'{_SPARK_APP_CLASS[type]}',
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			'dag_id': _DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(_DAG_ID, "{{ run_id }}")}',
			'detail': {
				'publisherId': f'{{{{ ti.xcom_pull(key="{XCOM_PUBLISHER_ID}") }}}}',
				'adProviderIds': sorted(utils.csv_to_str_arr(f'{{{{ ti.xcom_pull(key="{XCOM_ADPROVIDER_IDS}") }}}}')),
			}
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS[type],
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[PROFILE][type][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT,
		wait_showup_failure_cb=partial(_update_state, type, False),
		wait_complete_failure_cb=partial(_update_state, type, False),
		conclude_app_failure_cb=partial(_update_state, type, False),
		conclude_app_success_cb=partial(_update_state, type, True),
	)


def _check_failure(**context):
	"""
	리포트 집계 실패한 케이스가 하나라도 있는 경우, Job Failure 처리
	"""
	adprovider_state = context['ti'].xcom_pull(key=XCOM_ADPROVIDER_STATE)
	adunit_state = context['ti'].xcom_pull(key=XCOM_ADUNIT_STATE)

	logging.info(f'{_LOG_PREFIX} adprovider_state= {adprovider_state}, adunit_state= {adunit_state}')

	# 실패 케이스 조회
	if (adprovider_state not in [ST_COMPLETE, None] or adunit_state not in [ST_COMPLETE, None]
			or (adprovider_state is None and adunit_state is None)):
		_clean_up_job(False, **context)

		raise AirflowFailException('집계 실패 케이스가 있으므로 Fail 처리함')


def _clean_up_job(is_success: bool, **context):
	"""
	성공한 경우, job 삭제
	실패한 경우, retryCnt 증가시키고 running=0 설정
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	publisher_id = context['ti'].xcom_pull(key=XCOM_PUBLISHER_ID)
	adprovider_ids = context['ti'].xcom_pull(key=XCOM_ADPROVIDER_IDS)
	types = context['ti'].xcom_pull(key=XCOM_TYPES)
	adprovider_state = context['ti'].xcom_pull(key=XCOM_ADPROVIDER_STATE)
	adunit_state = context['ti'].xcom_pull(key=XCOM_ADUNIT_STATE)

	filters = {
		'type': _DAG_ID,
		'datetime': target_ymd,
		'detail.publisher_id': ObjectId(publisher_id) if publisher_id else None,
		'detail.adProvider_ids': sorted(utils.csv_to_id_arr(adprovider_ids)) if adprovider_ids else None,
		'detail.types': types,
	}

	# detail.state 업데이트
	trigger_by = context['params'].get('trigger_by')
	update = {'$set': {
		'detail.state': {
			ADPROVIDER: adprovider_state if (types is None or ADPROVIDER in types) else None,
			ADUNIT: adunit_state if (types is None or ADUNIT in types) else None
		},
		'manual': 1 if trigger_by == DagRunType.MANUAL else 0
	}}

	logging.info(f'{_LOG_PREFIX} update= {update}')

	job_dao.update_job(filters, update)

	job_dao.clean_up_by_filter(filters, is_success)


with DAG(
		_DAG_ID,
		description='Simple 매체 성과 리포트 스파크 집계 DAG',
		tags=['spark', 'pr', 'simple_performance', 'simple', 'daily', 'data_db', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
			'retries': 3
		},
		params={
			XCOM_TARGET_YMD: '',
			XCOM_PUBLISHER_ID: '',
			XCOM_ADPROVIDER_IDS: '',
			XCOM_TYPES: '',
			'trigger_by': '',
		},
		start_date=pendulum.datetime(2024, 8, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 중복 실행 여부 확인
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=_check_dup_run,
	)

	# 해당 시간대의 Jobs.running=1 로 추가
	upsert_job = PythonOperator(
		task_id='upsert_job',
		python_callable=_upsert_job,
	)

	branch_spark_run = BranchPythonOperator(
		task_id='branch_spark_run',
		python_callable=_branch_spark_run,
	)

	spark_run_performance_adprovider = _create_task_group(
		task_group_id=f'spark_run_performance_adprovider',
		type=ADPROVIDER,
	)

	spark_run_performance_adunit = _create_task_group(
		task_group_id=f'spark_run_performance_adunit',
		type=ADUNIT,
	)

	# 리포트 집계 실패한 케이스가 하나라도 있는 경우, Job Failure 처리
	# 병렬로 처리 되는 spark_run_xxx 이 성공이든 실패든 모두 끝날 때까지 기다리도록 TriggerRule.ALL_DONE 로 설정함
	check_failure = PythonOperator(
		task_id='check_failure',
		python_callable=_check_failure,
		trigger_rule=TriggerRule.ALL_DONE,
		retries=0
	)

	# 성공 시 Jobs 다큐먼트 삭제
	clean_up_to_success = PythonOperator(
		task_id='clean_up_to_success',
		python_callable=partial(_clean_up_job, True),
		trigger_rule=TriggerRule.NONE_FAILED,
	)

	setup >> check_dup_run >> upsert_job >> branch_spark_run >> [spark_run_performance_adprovider, spark_run_performance_adunit]
	spark_run_performance_adprovider >> check_failure
	spark_run_performance_adunit >> check_failure
	check_failure >> clean_up_to_success
