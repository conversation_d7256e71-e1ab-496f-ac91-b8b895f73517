"""
### Simple 매체 성과 리포트 재처리 트리거 DAG

#### 0. 위키
- [17. 매체 성과 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=2210851496)

#### 1. 주기
- 매 10분 간격

#### 2. 스케쥴 콜렉션 : Jobs
- 정규 DAG 에서 실패한 경우
- trace_zircon_b_for_simple_performance DAG 에서 추가한 경우 ( ZirconTrace 에 지르콘B 재처리 이력이 남은 경우 )

#### 3. 트리거 대상 DAG
- spark_simple_performance

#### 4. max_active_runs=1
"""

import os
import logging
import pendulum
from time import sleep
from bson import ObjectId

from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.utils.types import DagRunType
from airflow.utils.state import DagRunState
from airflow.operators.python import ShortCircuitOperator, PythonOperator

from core import utils
from core.dao import job_dao
from core.airflow_api import trigger_dagrun
from core.dao.environment_dao import get_environment_value_by_name

from core.base import DEFAULT_TZ, XCOM_TARGET_YMD, ALERT_EMAIL_ADDRESSES, XCOM_PUBLISHER_ID, XCOM_ADPROVIDER_IDS

from biz.performance.simple.simple_performance_base import XCOM_TYPES

from biz.performance.simple.spark_simple_performance import DAG_ID as SPARK_DAG_ID

# 한 번에 처리할 수 있는 리포트 갯수 ( simple-performance-report-thread-num = 10 )
THREAD_NUM = int(get_environment_value_by_name('simple-performance-report-thread-num')) or 10

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [TRIGGER-SIMPLE-PERFORMANCE-REPORT-REPROCESS]'


def _exist_jobs():
	"""
	재처리할 job이 있는지
	:return:
	"""

	jobs = job_dao.get_jobs({
		'type': SPARK_DAG_ID,
		'manual': 0,
		'running': 0,
		'retryCnt': {'$gt': -1, '$lt': job_dao.MAX_RETRY_CNT}
	})

	if jobs:
		logging.info(f'{_LOG_PREFIX} 재처리할 심플 성과 Jobs 개수 = {len(jobs)}')
		return True
	else:
		logging.info(f'{_LOG_PREFIX} 처리할 job 없음')
		return False


def _trigger_simple_performance():
	"""
	심플 성과 리포트 집계 트리거
		- 처리할 job 들 한번에 가져와서, 날짜 기준 Job > detail 기준 Job 순으로 처리한다.
			- 날짜 기준 Job 과 detail 기준 Job 이 겹치는 경우, detail 기준 Job 을 일괄 삭제 처리 한다.
		- 현재 처리 중인 스파크 집계 DagRun 갯수가 THREAD_NUM 미만인 경우에만 스파크 DAG 을 트리거 한다.
		- thread_num 이상인 경우, 5분 간격으로 체크하며 트리거 가능한 상태가 될 때까지 대기 한다.
		- 처리할 리포트가 없거나 모두 처리한 경우, 종료
		- simple-performance-report-thread-num=10
	"""

	# 재처리할 Jobs 목록 조회
	jobs = _get_jobs()
	total_count = len(jobs)

	logging.info(f'{_LOG_PREFIX} total_count= {total_count}, THREAD_NUM= {THREAD_NUM}')

	# 트리거 요청 완료 건수
	trigger_complete_count = 0
	dag_run_ids = []

	while True:
		if total_count == trigger_complete_count:
			# 심플 성과 DagRun 이 모두 완료 처리 되었는지 확인
			if _check_spark_run_complete(dag_run_ids):
				logging.info(f'{_LOG_PREFIX} 심플 성과 재처리 완료 (총 {total_count} 건)')
				break
			else:
				logging.info(f'{_LOG_PREFIX} 심플 성과 재처리 진행중. dag_run_ids= {dag_run_ids}')
				sleep(30)
				continue

		# 현재 스파크 집계 재처리 중인 건수
		spark_run_count = _get_spark_run_count()

		# 현재 처리 중인 건수가 THREAD_NUM 이상인 경우, 5분 대기
		if spark_run_count >= THREAD_NUM:
			logging.info(f'{_LOG_PREFIX} 5분간 대기함 ( spark_run_count= {spark_run_count}, THREAD_NUM= {THREAD_NUM} )')
			sleep(60 * 5)
			continue

		logging.info(
			f'{_LOG_PREFIX} total_count= {total_count}, trigger_complete_count= {trigger_complete_count}, spark_run_count= {spark_run_count}')

		job = jobs.pop(0)
		detail = job.get('detail', {})
		publisher_id = str(detail.get('publisher_id') or '')
		adprovider_ids = utils.id_arr_to_csv(detail.get('adProvider_ids', ''))
		types = str(detail.get('types') or '')

		# 심플 성과 리포트 집계 DAG 트리거
		dag_run_id = trigger_dagrun(SPARK_DAG_ID, pendulum.now(DEFAULT_TZ), {
			XCOM_TARGET_YMD: job['datetime'],
			XCOM_PUBLISHER_ID: publisher_id,
			XCOM_ADPROVIDER_IDS: adprovider_ids,
			XCOM_TYPES: types,
			'trigger_by': 'reprocess'
		}, DagRunType.SCHEDULED)

		logging.info(
			f'{_LOG_PREFIX} 심플 성과 리포트 집계 DAG 트리거 ( date= {job["datetime"]}, publisher_id= {publisher_id}, adProvider_ids= {adprovider_ids}, types= {types}, '
			f'spark_dag_id= {SPARK_DAG_ID}, dag_run_id={dag_run_id} )')

		trigger_complete_count += 1
		dag_run_ids.append(dag_run_id)


def _get_jobs():
	"""
	재처리할 Jobs 목록 조회
		- 날짜 기준 Job > detail 기준 Job 순으로 처리
	"""
	jobs = job_dao.get_jobs({
		'type': SPARK_DAG_ID,
		'manual': 0,
		'running': 0,
		'retryCnt': {'$gt': -1, '$lt': job_dao.MAX_RETRY_CNT}
	})

	# 특정 날짜를 기준으로 전체 처리하는 케이스가 있는 경우 ( ex> 정규 실패로 인한 재처리 케이스 )
	# 동일한 날짜의 detail 처리 job 들은 일괄 삭제 처리 한다.

	# 날짜 기준 Job
	datetime_jobs = [job for job in jobs if _is_datetime_job(job)]
	datetime_list = {job['datetime'] for job in datetime_jobs}

	# detail 기준 Job
	detail_jobs = [job for job in jobs if not _is_datetime_job(job)]

	# datetime_jobs 에서 같이 처리될 예정으로 삭제되어야할 Job
	deleted_jobs = [job for job in detail_jobs if job['datetime'] in datetime_list]

	# datetime_jobs 과 같이 재처리되어야할 Job
	target_jobs = [job for job in detail_jobs if job['datetime'] not in datetime_list]

	# deleted_jobs 는 일괄 삭제 처리함 ( 해당 일자 전체 처리 하는 job 이 이미 존재하므로 )
	job_dao.delete_jobs({'_id': {'$in': [ObjectId(job['_id']) for job in deleted_jobs]}})

	logging.info(f'{_LOG_PREFIX} datetime_jobs= {len(datetime_jobs)}건 >> \n{datetime_jobs}')
	logging.info(f'{_LOG_PREFIX} target_jobs= {len(target_jobs)}건 >> \n{target_jobs}')
	logging.info(f'{_LOG_PREFIX} deleted_jobs= {len(deleted_jobs)}건 >> \n{deleted_jobs}')

	# 날짜 기준 Job 을 우선 처리함
	return datetime_jobs + target_jobs


def _is_datetime_job(job):
	"""
	날짜 기준 Job 인지 체크
	:param job
	:return
	"""
	detail = job.get('detail', {})
	return all(detail.get(key) is None for key in ['publisher_id', 'adProvider_ids', 'types'])


def _get_spark_run_count():
	"""
	RUNNING / QUEUED 상태인 심플 성과 재처리 DagRuns 갯수
	:return:
	"""
	dagRuns = DagRun.find(dag_id=SPARK_DAG_ID, state=DagRunState.RUNNING) + DagRun.find(dag_id=SPARK_DAG_ID, state=DagRunState.QUEUED)

	# running + queued 갯수
	spark_count = sum(1 for dagRun in dagRuns if dagRun.conf.get('trigger_by') == 'reprocess')

	logging.info(f'{_LOG_PREFIX} spark_count= {spark_count}')

	return spark_count


def _check_spark_run_complete(dag_run_ids):
	"""
	심플 성과 재처리 DagRuns 이 모두 처리 되었는지 체크 (성공/실패 여부 관계 없이)
	:param dag_run_ids
	:return:
	"""
	dagRuns = DagRun.find(dag_id=SPARK_DAG_ID, state=DagRunState.RUNNING) + DagRun.find(dag_id=SPARK_DAG_ID, state=DagRunState.QUEUED)

	# running + queued run_id 추출
	current_run_ids = [dagRun.run_id for dagRun in dagRuns if dagRun.conf.get('trigger_by') == 'reprocess']

	return all(item not in current_run_ids for item in dag_run_ids)


with DAG(
		_DAG_ID,
		description='Simple 매체 성과 리포트 재처리 DAG',
		tags=['trigger', 'reprocess', 'pr', 'simple_performance', 'simple', 'daily', 'data_db', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2025, 3, 1, tz=DEFAULT_TZ),
		schedule_interval='*/10 * * * *',  # 매 10분 간격
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# 재처리해야 할 job이 있을 때만 다운스트림 태스크 진행
	exist_jobs = ShortCircuitOperator(
		task_id='exist_jobs',
		python_callable=_exist_jobs,
	)

	# 심플 성과 스파크 병렬처리
	trigger_simple_performance = PythonOperator(
		task_id='trigger_simple_performance',
		python_callable=_trigger_simple_performance,
	)

	exist_jobs >> trigger_simple_performance
