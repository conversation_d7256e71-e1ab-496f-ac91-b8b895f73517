"""
### Creator Advisor 리포트 정규 트리거 DAG

#### 0. 위키
- [31. Creator Advisor 리포트 생성](https://wiki.navercorp.com/pages/viewpage.action?pageId=1475746291)

#### 1. 주기
- 리얼 환경 : 매일 05시(~08시)
	- 그 외 환경 : 매일 12시
- silver-log-check-timeout = 10800 (3시간)

#### 2. 트리거 대상 DAG
- spark_creator_advisor_report

#### 3. config
- target_ymd : 처리할 날짜 YYYYMMDD ( default: 어제 )

#### 4. max_active_runs=1
"""

import os
import logging
import pendulum

from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.utils.types import DagRunType
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

from core.base import DEFAULT_TZ, PROFILE, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD, \
	PROFILE_REAL

from biz.creator_advisor.spark_creator_advisor_report import DAG_ID as SPARK_DAG_ID

_DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [TRIGGER-CREATOR-ADVISOR-REPORT-REGULAR]'

_SCHEDULE = '0 5 * * *' if PROFILE == PROFILE_REAL else '0 12 * * *'


def _setup(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
	- target_ymd

	:param context:
	"""
	target_ymd = context['params'][XCOM_TARGET_YMD]
	if target_ymd:
		pendulum.from_format(target_ymd, 'YYYYMMDD')
		logging.info(f'{_LOG_PREFIX} Using target_ymd from "params". target_ymd={target_ymd}')
	else:
		target_ymd = pendulum.instance(context['logical_date']).in_tz(DEFAULT_TZ).format('YYYYMMDD')
		logging.info(f'{_LOG_PREFIX} Using target_ymd from tz-shifted "logical_date". target_ymd={target_ymd}')

	context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)


with DAG(
		_DAG_ID,
		description='Trigger regular creator advisor report DAG',
		tags=['trigger', 'regular', 'ca', 'creator_advisor', 'daily', 'hdfs', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			'target_ymd': '',
		},
		start_date=pendulum.datetime(2023, 6, 1, tz=DEFAULT_TZ),
		schedule_interval=_SCHEDULE,  # 매일 05시
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	trigger_creator_advisor_report = TriggerDagRunOperator(
		trigger_dag_id=SPARK_DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{SPARK_DAG_ID}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=False,
		poke_interval=60,
		reset_dag_run=True,
		conf={
			XCOM_TARGET_YMD: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			'trigger_by': DagRunType.SCHEDULED,
		}
	)

	setup >> trigger_creator_advisor_report
