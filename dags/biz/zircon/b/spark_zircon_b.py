"""
### Zircon B 생성

#### 위키
- [18-15. [SPARK] Zircon B 적재](https://wiki.navercorp.com/pages/viewpage.action?pageId=2388087623)

#### 개요
- Zircon B 생성
- 매체별 스파크 앱 실행

####주기
- trigger_zircon_b_regular/reprocess/manaul에 따름

#### config
- target_ymd: '',  # YYYYMMDD
- kind: '',  # "regular", "reprocess", "manual", "trace_silver", "trace_silvergrey"
- pub_id: '',  # 특정 PUB ID 또는 모든 PUB
- ap_id: '',  # 특정 AP ID 또는 모든 AP
- silver_trace_id: '',  # silver 재처리 시에만 명시
- zircon_b_trace_id: '',  # silvergrey 재처리 시에만 명시
- is_full_field: '',  # 중간 집계 필드를 포함할지 (디폴트 미포함)
"""

import logging
import os
from datetime import timedelta
from functools import partial

import pendulum
from airflow import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.sensors.python import PythonSensor
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType
from bson import ObjectId

from core import utils
from core.base import PROFILE_TEST, PROFILE_REAL, PROFILE, ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, XCOM_TARGET_YMD, \
	POOL_SPARK, \
	SPARK_SUBMIT_OPTIONS, \
	XCOM_SILVER_TRACE_ID, XCOM_ZIRCON_TRACE_ID, XCOM_PUB_ID, XCOM_AP_ID, XCOM_KIND, \
	XCOM_IS_FULL_FIELD, ZIRCON_B_STATE_START, ZIRCON_B_STATE_SUCCESS, ZIRCON_B_STATE_FAILURE, POOL_SPARK_ZIRCON, \
	SPARKLING_IMAGE, SPARKLING_APP_JAR_PATH, SPARKLING_APP_HARD_LIMIT_1
from core.dao import job_dao, zircon_trace_dao
from core.dao.environment_dao import get_environment_value_by_name, is_ready_zircon_b_gfp
from core.dao.job_dao import clean_up_by_filter
from core.spark_pool import POOL_SLOT_TRIVIAL_5, POOL_SLOT_TRIVIAL_3
from core.spark_submit_op import invoke_job_with_args
from core.spark_task_group import create_task_group

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [SPK-ZB]'

# Zircon B GFP
_ZIRCON_B_GFP_CHECK_TIMEOUT = int(get_environment_value_by_name('zircon-b-gfp-check-timeout'))  # 디폴트 3시간

# Spark App 정보
_ACCU = 'ACCUMULATION'
_COMP = 'COMPACTION'

# Spark
_SPARK_APP_CLASS = {
	_ACCU: 'com.navercorp.gfp.biz.zircon.b.ZirconBAggregator',
	_COMP: 'com.navercorp.gfp.biz.zircon.b.ZirconBCompactor'
}

# Profile
_PROFILE_SETTINGS = {
	_ACCU: {
		PROFILE_TEST: {
			SPARK_SUBMIT_OPTIONS: '''
			--executor-memory 1g
			--num-executors 6
			--executor-cores 4
			--driver-memory 1g
			--conf spark.sql.shuffle.partitions=240
		'''.split(),
		},
		PROFILE_REAL: {
			SPARK_SUBMIT_OPTIONS: '''
			--executor-memory 6g
			--num-executors 12
			--executor-cores 2
			--conf spark.sql.shuffle.partitions=720
			--conf spark.sql.adaptive.advisoryPartitionSizeInBytes=16m
			--conf spark.kryoserializer.buffer.max=128m
		'''.split(),
		}
	},
	_COMP: {
		PROFILE_TEST: {
			SPARK_SUBMIT_OPTIONS: '''
			--executor-memory 1g
			--num-executors 3
			--executor-cores 2
			--conf spark.sql.shuffle.partitions=100
			--conf spark.scheduler.mode=FAIR
			--conf spark.scheduler.allocation.file=hdfs://bizcloud/user/gfp-data/fairscheduler.xml
			--conf spark.scheduler.pool=zircon_b
		'''.split(),
		},
		PROFILE_REAL: {
			SPARK_SUBMIT_OPTIONS: '''
			--executor-memory 1g
			--num-executors 3
			--executor-cores 2
			--conf spark.sql.shuffle.partitions=100
			--conf spark.scheduler.mode=FAIR
			--conf spark.scheduler.allocation.file=hdfs://pg07/user/gfp-data/fairscheduler.xml
			--conf spark.scheduler.pool=zircon_b
		'''.split(),
		}
	}
}


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	# 날짜 설정
	_set_target_ymd(**context)

	# 필수 파라미터
	kind = context['params'].get(XCOM_KIND)
	pub_id = context['params'].get(XCOM_PUB_ID)
	ap_id = context['params'].get(XCOM_AP_ID)

	# 선택 파라미터
	s_trace_id = context['params'].get(XCOM_SILVER_TRACE_ID) \
		if context['params'].get(XCOM_SILVER_TRACE_ID) else '-'
	zb_trace_id = context['params'].get(XCOM_ZIRCON_TRACE_ID) \
		if context['params'].get(XCOM_ZIRCON_TRACE_ID) else '-'

	# 선택 파라미터
	# Zircon B 생성 시 중간 계산 필드를 포함할지의 여부. 0:미포함, 1:포함
	if context['params'].get(XCOM_IS_FULL_FIELD):
		is_full_field = context['params'].get(XCOM_IS_FULL_FIELD)
	else:
		is_full_field = str(get_environment_value_by_name('zircon-b-is-full-field'))

	# 파라미터 유효성 검사
	validate_param(kind, pub_id, ap_id, s_trace_id, zb_trace_id, is_full_field)

	# XCOM에 푸시
	context['ti'].xcom_push(XCOM_KIND, kind)
	context['ti'].xcom_push(XCOM_PUB_ID, pub_id)
	context['ti'].xcom_push(XCOM_AP_ID, ap_id)
	context['ti'].xcom_push(XCOM_SILVER_TRACE_ID, s_trace_id)
	context['ti'].xcom_push(XCOM_ZIRCON_TRACE_ID, zb_trace_id)
	context['ti'].xcom_push(XCOM_IS_FULL_FIELD, is_full_field)

	_print_settings(**context)


def _set_target_ymd(**context):
	"""
	사용자 입력 파라미터 또는 스케줄에 의한 파라미터로 결정된다.
	:param context:
	:return:
	"""
	target_ymd = context['params'].get(XCOM_TARGET_YMD)

	if target_ymd:
		target_ymd = str(target_ymd).strip()
		pendulum.from_format(target_ymd, 'YYYYMMDD')
		logging.info(f'{_LOG_PREFIX} Using target_ymd from "params"={target_ymd}')
	else:
		logging.info(f'{_LOG_PREFIX} params does not exist. target_ymd is empty')
		raise AirflowException(f'target_ymd is not defined. target_ymd={target_ymd}')

	context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)


def validate_param(kind: str, pub_id: str, ap_id: str, s_trace_id, zb_trace_id, is_full_field):
	if kind != 'regular' and kind != 'trace_silver' and kind != 'trace_silvergrey' and kind != 'manual':
		raise AirflowException(f'"kind" should be "regular" or "trace_silver" or "trace_silvergrey" or "manual"')

	if pub_id != '*' and len(pub_id) != 24:
		raise AirflowException(f'"pub_id" is not a 24-digit')

	if ap_id != '*' and len(ap_id) != 24:
		raise AirflowException(f'"ap_id" is not a 24-digit')

	if pub_id != '*' and not _is_hexadecimal(pub_id):
		raise AirflowException(f'"pub_id" is not a hexadecimal')

	if ap_id != '*' and not _is_hexadecimal(ap_id):
		raise AirflowException(f'"ap_id" is not a hexadecimal')

	if kind == 'trace_silver':
		if s_trace_id == '-':
			raise AirflowException(f'"trace_silver" kind needs "s_trace_id"')
		else:
			if not _is_hexadecimal(s_trace_id):
				raise AirflowException(f'"s_trace_id" is not a hexadecimal')

			if len(s_trace_id) != 24:
				raise AirflowException(f'"s_trace_id" is not a 24-digit')

	if kind == 'trace_silvergrey':
		if zb_trace_id == '-':
			raise AirflowException(f'"trace_silvergrey" kind needs "zb_trace_id"')
		else:
			if not _is_hexadecimal(zb_trace_id):
				raise AirflowException(f'"zb_trace_id" is not a hexadecimal')

			if len(zb_trace_id) != 24:
				raise AirflowException(f'"zb_trace_id" is not a 24-digit')

	if kind != 'manual' and pub_id == '*' and ap_id == '*':
		raise AirflowException(f'Neither "pub_id"" nor "ap_id" can be "*".')

	if is_full_field and is_full_field != '0' and is_full_field != '1':
		raise AirflowException(f'"is_full_field" should be "0" or "1"')


def _is_hexadecimal(value: str) -> bool:
	# 모든 문자가 16진수로 구성되어 있는지 확인
	if all(c.isdigit() or c.lower() in 'abcdef' for c in value):
		return True
	else:
		return False


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK}
Airflow Pool Slot: {POOL_SLOT_TRIVIAL_5}

Profile: {PROFILE}
Spark Submit Options :
	_ACCU:{_PROFILE_SETTINGS[_ACCU][PROFILE][SPARK_SUBMIT_OPTIONS]}
	_COMP:{_PROFILE_SETTINGS[_COMP][PROFILE][SPARK_SUBMIT_OPTIONS]}
Params:
    ymd={context['ti'].xcom_pull(key=XCOM_TARGET_YMD)}
    pub_id={context['ti'].xcom_pull(key=XCOM_PUB_ID)}
    ap_id={context['ti'].xcom_pull(key=XCOM_AP_ID)}
    kind={context['ti'].xcom_pull(key=XCOM_KIND)}
    s_trace_id={context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)}
    zb_trace_id={context['ti'].xcom_pull(key=XCOM_ZIRCON_TRACE_ID)}
    is_full_field={context['ti'].xcom_pull(key=XCOM_IS_FULL_FIELD)}
    ZIRCON_B_GFP_CHECK_TIMEOUT={_ZIRCON_B_GFP_CHECK_TIMEOUT}
------------------------------------------------------------------------------------------
''')


def _is_ready_zircon_b_gfp(ymd: str):
	"""
	ymd에 해당하는 zircon b gfp가 준비되었는지 확인한다.
	:param ymd: 포맷은 'YYYYMMDD'
	:return:
	"""
	is_ready = is_ready_zircon_b_gfp(ymd)
	logging.info(f'{_LOG_PREFIX} is {ymd} ready? {is_ready}')
	return is_ready


def _check_dup_run(**context):
	"""
	중복실행을 방지하기 위한 방어 로직
	:param context:
	:return:
	"""
	ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	ap_id = context['ti'].xcom_pull(key=XCOM_AP_ID)
	kind = context['ti'].xcom_pull(key=XCOM_KIND)
	s_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)
	zb_trace_id = context['ti'].xcom_pull(key=XCOM_ZIRCON_TRACE_ID)

	filter = {
		'type': DAG_ID,
		'datetime': ymd,
		'detail.kind': kind,
		'detail.publisher_id': ObjectId(pub_id) if pub_id != '*' else None,
		'detail.adProvider_id': ObjectId(ap_id) if ap_id != '*' else None,
		'detail.silverTrace_id': ObjectId(s_trace_id) if s_trace_id != '-' else None,
		'detail.zirconTrace_id': ObjectId(zb_trace_id) if zb_trace_id != '-' else None,
		'running': 1
	}
	job = job_dao.get_job(filter)

	if job:  # 이미 진행중인 작업이 있다면
		my_dag_run_id = context['dag_run'].run_id
		running_dag_runs: list[DagRun] = DagRun.find(dag_id=DAG_ID, state=DagRunState.RUNNING)
		for dag_run in running_dag_runs:
			ti = dag_run.get_task_instance('setup')
			your_ymd = ti.xcom_pull(key=XCOM_TARGET_YMD)
			your_pub_id = ti.xcom_pull(key=XCOM_PUB_ID)
			your_ap_id = ti.xcom_pull(key=XCOM_AP_ID)
			your_kind = ti.xcom_pull(key=XCOM_KIND)
			your_s_trace_id = ti.xcom_pull(key=XCOM_SILVER_TRACE_ID)
			your_zb_trace_id = ti.xcom_pull(key=XCOM_ZIRCON_TRACE_ID)
			your_run_id = dag_run.run_id

			if my_dag_run_id != your_run_id and ymd == your_ymd and pub_id == your_pub_id and ap_id == your_ap_id:
				raise AirflowException(f'이미 실행중인 DagRun이 있음.'
									   f'\n\tYour dag_run_id={your_run_id} ymd={your_ymd} kind={your_kind}'
									   f' pub_id={your_pub_id} ap_id={your_ap_id} s_trace_id={your_s_trace_id} zb_trace_id={your_zb_trace_id}'
									   f'\n\tMy   dag_run_id={my_dag_run_id} ymd={ymd} kind={kind}'
									   f' pub_id={pub_id} ap_id={ap_id} s_trace_id={s_trace_id} zb_trace_id={zb_trace_id}')
	else:
		pass


def _upsert_job(**context):
	"""
	해당 시간대의 Job 추가 또는 running=1로 업데이트
	:param context:
	:return:
	"""
	ymd = context["ti"].xcom_pull(key=XCOM_TARGET_YMD)
	pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	ap_id = context['ti'].xcom_pull(key=XCOM_AP_ID)
	kind = context['ti'].xcom_pull(key=XCOM_KIND)
	s_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)
	zb_trace_id = context['ti'].xcom_pull(key=XCOM_ZIRCON_TRACE_ID)
	is_full_field = context['ti'].xcom_pull(key=XCOM_IS_FULL_FIELD)

	filter = {
		'type': DAG_ID,
		'datetime': ymd,
		'detail.kind': kind,
		'detail.publisher_id': ObjectId(pub_id) if pub_id != '*' else None,
		'detail.adProvider_id': ObjectId(ap_id) if ap_id != '*' else None,
		'detail.silverTrace_id': ObjectId(s_trace_id) if s_trace_id != '-' else None,
		'detail.zirconTrace_id': ObjectId(zb_trace_id) if zb_trace_id != '-' else None,
		# is_full_field는 조회조건에 포함시키지 않음
	}
	job = job_dao.get_job(filter)

	if job:
		update = {'$set': {'running': 1, 'modifiedAt': pendulum.now(tz=DEFAULT_TZ)}}
		job_dao.update_job(filter, update)
	else:
		dag_run = context["dag_run"]
		job = {
			'type': DAG_ID,
			'datetime': ymd,
			'detail': {
				'kind': kind,
				'publisher_id': ObjectId(pub_id) if pub_id != '*' else None,
				'adProvider_id': ObjectId(ap_id) if ap_id != '*' else None,
				'silverTrace_id': ObjectId(s_trace_id) if s_trace_id != '-' else None,
				'zirconTrace_id': ObjectId(zb_trace_id) if zb_trace_id != '-' else None,
				'isFullField': is_full_field,  # 저장할 때는 사용. 부가 정보로만 쓰임.
			},
			'retryCnt': -1,
			'running': 1,
			'manual': 0 if dag_run.run_type == DagRunType.SCHEDULED else 1
		}
		job_dao.insert_job(job)
		logging.info(f'{_LOG_PREFIX} {ymd} Jobs에 추가. dag_run_type={dag_run.run_type} {job}')


def _set_zircon_trace_to_start(**context):
	"""
	ZirconTrace.startedAt 업데이트
	스파크 작업 시작 전 ZirconTrace.startedAt를 업데이트하여 스파크 작업이 시작됨을 표시
	Simple 매체성과리포트 DAG에서 정규처리와 재처리 대상을 가려내기 위해 사용됨.
	:param context:
	:return:
	"""
	ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	ap_id = context['ti'].xcom_pull(key=XCOM_AP_ID)
	zb_trace_id = context['ti'].xcom_pull(key=XCOM_ZIRCON_TRACE_ID)

	if zb_trace_id and zb_trace_id != '-':  # silvregrey 재처리인 경우
		_update_zircon_trace_by_zb_trace_id(zb_trace_id, ZIRCON_B_STATE_START)
	else:  # regular, manual인 경우
		_update_zircon_trace_by(ymd, pub_id, ap_id, ZIRCON_B_STATE_START)


def _update_zircon_trace_by_zb_trace_id(zb_trace_id: str, state: str):
	"""
	ZirconTrace의 상태 업데이트
	:param context:
	:return:
	"""
	match = {'_id': ObjectId(zb_trace_id)}
	update = _get_update_statement(state)
	result = zircon_trace_dao.update_trace_one(match, update)
	logging.info(f'{_LOG_PREFIX} _update_zircon_trace_by_zb_trace_id() result={result}')


def _update_zircon_trace_by(ymd: str, pub_id: str, ap_id: str, state: str):
	"""
	ZirconTrace의 상태 업데이트
	:param context:
	:return:
	"""
	match = {'date': ymd}

	if pub_id != '*':
		match['publisher_id'] = ObjectId(pub_id)
	if ap_id != '*':
		match['adProvider_id'] = ObjectId(ap_id)

	update = _get_update_statement(state)
	result = zircon_trace_dao.update_trace_many(match, update)
	logging.info(
		f'{_LOG_PREFIX} _update_zircon_trace_by() state={state} match={match} update={update} result.modified_count={result.modified_count}')


def _get_update_statement(state: str):
	"""
	ZirconTrace의 상태를 업데이트하기 위한 문장 구성
	:param state:
	:return:
	"""
	now = pendulum.now(DEFAULT_TZ)

	zircon_b_at = {}
	if state == ZIRCON_B_STATE_START:
		zircon_b_at['zirconBStartedAt'] = now
	elif state == ZIRCON_B_STATE_SUCCESS:
		zircon_b_at['zirconBSucceededAt'] = now
		zircon_b_at['zirconBSilvergreyAt'] = '$silvergreyCompletedAt'
	elif state == ZIRCON_B_STATE_FAILURE:
		zircon_b_at['zirconBFailedAt'] = now

	update = [{
		'$set': {
			'zirconBState': state,
			**zircon_b_at,
			'normalConfirm': 1,
			'modifiedAt': now
		}
	}]

	return update


def _get_app_args(context: dict):
	"""
	스파크 애플리케이션 실행에 필요한 아규먼트 가져오기
	:param context:
	:return:
	"""
	ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	kind = context['ti'].xcom_pull(key=XCOM_KIND)
	pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	ap_id = context['ti'].xcom_pull(key=XCOM_AP_ID)
	s_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)
	zb_trace_id = context['ti'].xcom_pull(key=XCOM_ZIRCON_TRACE_ID)
	is_full_field = context['ti'].xcom_pull(key=XCOM_IS_FULL_FIELD)

	logging.info(f'{_LOG_PREFIX} _get_app_args() ymd={ymd} kind={kind} pub_id={pub_id} ap_id={ap_id} '
				 f's_trace_id={s_trace_id} zb_trace_id={zb_trace_id} is_full_field={is_full_field}')

	return [kind, ymd, pub_id, ap_id, s_trace_id, zb_trace_id, is_full_field]


def _clean_up_to_success(**context):
	"""
	- 성공했으므로 job 삭제
	- ZirconTrace 업데이트

	- SilverTrace 업데이트를 여기서 하지 않는 이유
		* SilverTrace 업데이트는 ZBGFP, ZB가 모두 끝나야 할 수 있는데 본 DAG은 pub_id, ap_id 단위로 수행하므로
		  전체 매체가 모두 처리되었는지 알 수 없다.
		* 따라서 전체 처리 여부를 확인할 수 있는 trace_silver DAG에서 수행하는 것임.
		* trace_silver DAG의 update_zircon_b_completed_at_of_silver_trace task에서 수행
	:param context:
	:return:
	"""
	ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	kind = context['ti'].xcom_pull(key=XCOM_KIND)
	pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	ap_id = context['ti'].xcom_pull(key=XCOM_AP_ID)
	s_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)
	zb_trace_id = context['ti'].xcom_pull(key=XCOM_ZIRCON_TRACE_ID)
	filter = {
		'type': DAG_ID,
		'datetime': ymd,
		'detail.kind': kind,
		'detail.publisher_id': ObjectId(pub_id) if pub_id != '*' else None,
		'detail.adProvider_id': ObjectId(ap_id) if ap_id != '*' else None,
		'detail.silverTrace_id': ObjectId(s_trace_id) if s_trace_id != '-' else None,
		'detail.zirconTrace_id': ObjectId(zb_trace_id) if zb_trace_id != '-' else None,
	}
	clean_up_by_filter(filter, True)

	# ZirconTrace SUCCESS로 업데이트
	if zb_trace_id and zb_trace_id != '-':  # silvregrey 재처리인 경우
		_update_zircon_trace_by_zb_trace_id(zb_trace_id, ZIRCON_B_STATE_SUCCESS)
	else:  # regular, manual인 경우
		_update_zircon_trace_by(ymd, pub_id, ap_id, ZIRCON_B_STATE_SUCCESS)


def _clean_up_to_failure(context: dict):
	"""
	실패했으므로 retryCnt증가시키고 running=0 설정
	:param context:
	:return:
	"""
	ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	ap_id = context['ti'].xcom_pull(key=XCOM_AP_ID)
	kind = context['ti'].xcom_pull(key=XCOM_KIND)
	s_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)
	zb_trace_id = context['ti'].xcom_pull(key=XCOM_ZIRCON_TRACE_ID)
	filter = {
		'type': DAG_ID,
		'datetime': ymd,
		'detail.kind': kind,
		'detail.publisher_id': ObjectId(pub_id) if pub_id != '*' else None,
		'detail.adProvider_id': ObjectId(ap_id) if ap_id != '*' else None,
		'detail.silverTrace_id': ObjectId(s_trace_id) if s_trace_id != '-' else None,
		'detail.zirconTrace_id': ObjectId(zb_trace_id) if zb_trace_id != '-' else None,
	}
	clean_up_by_filter(filter, False)

	# ZirconTrace FAILURE로 업데이트
	if zb_trace_id and zb_trace_id != '-':  # silvregrey 재처리인 경우
		_update_zircon_trace_by_zb_trace_id(zb_trace_id, ZIRCON_B_STATE_FAILURE)
	else:  # regular, manual인 경우
		_update_zircon_trace_by(ymd, pub_id, ap_id, ZIRCON_B_STATE_FAILURE)


with DAG(
		DAG_ID,
		description='Zircon B 생성 스파크 앱 실행',
		tags=['spark', 'zircon', 'zirconb', 'zb', 'juyoun.kim'],
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2024, 4, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
		params={
			XCOM_TARGET_YMD: '',  # YYYYMMDD
			XCOM_KIND: '',  # 정규 생성, 실버 재처리에 의한 재처리, 실버그레이 재처리에 의한 재처리
			XCOM_PUB_ID: '',  # 특정 PUB ID 또는 모든 PUB
			XCOM_AP_ID: '',  # 특정 AP ID 또는 모든 AP
			XCOM_SILVER_TRACE_ID: '',  # silver 재처리 시에만 명시
			XCOM_ZIRCON_TRACE_ID: '',  # silvergrey 재처리 시에만 명시
			XCOM_IS_FULL_FIELD: '',  # 중간 집계 필드를 포함할지 (디폴트 미포함)
		}
) as dag:
	dag.doc_md = __doc__

	# 환경 설정
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 중복실행 여부 확인
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=_check_dup_run,
	)

	# 해당 시간대의 Jobs.running=1로 추가
	upsert_job = PythonOperator(
		task_id='upsert_job',
		python_callable=_upsert_job
	)

	# Zircon B GFP가 준비됐는지 확인
	wait_for_zircon_b_gfp = PythonSensor(
		task_id='wait_for_zircon_b_gfp',
		python_callable=_is_ready_zircon_b_gfp,
		op_args=[
			f'{{{{ ti.xcom_pull(task_ids="{setup.task_id}", key="target_ymd") }}}}'
		],
		poke_interval=60 * 15,  # 15분마다 재시도
		execution_timeout=timedelta(seconds=_ZIRCON_B_GFP_CHECK_TIMEOUT),
		on_failure_callback=partial(_clean_up_to_failure)
	)

	# ZirconTrace를 START 상태로 업데이트
	set_zircon_trace_to_start = PythonOperator(
		task_id='set_zircon_trace_to_start',
		python_callable=_set_zircon_trace_to_start
	)

	# Zircon B 생성
	task_group_id_accumulation = 'accumulate_zircon_b'
	accumulate_zircon_b = create_task_group(
		task_group_id_accumulation,
		tooltip=_SPARK_APP_CLASS[_ACCU],
		spark_pool=POOL_SPARK_ZIRCON,
		pool_slot=POOL_SLOT_TRIVIAL_5,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': f'{_SPARK_APP_CLASS[_ACCU]}',
			'dt': '{{ ti.xcom_pull(key="target_ymd") }}',
			'dag_id': DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id_accumulation,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS[_ACCU],
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[_ACCU][PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': '{{ ti.xcom_pull(key="target_ymd") }}-pub-{{ ti.xcom_pull(key="pub_id") }}-ap-{{ ti.xcom_pull(key="ap_id") }}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT_1,
		wait_showup_failure_cb=partial(_clean_up_to_failure),
		wait_complete_failure_cb=partial(_clean_up_to_failure),
		conclude_app_failure_cb=partial(_clean_up_to_failure)  # 실패 시 Jobs.retryCnt 증가시키고 Jobs.running=0
	)

	# Zircon B 켬팩션
	task_group_id_compaction = 'compact_zircon_b'
	compact_zircon_b = create_task_group(
		task_group_id_compaction,
		tooltip=_SPARK_APP_CLASS[_COMP],
		spark_pool=POOL_SPARK_ZIRCON,
		pool_slot=POOL_SLOT_TRIVIAL_3,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': f'{_SPARK_APP_CLASS[_COMP]}',
			'dt': '{{ ti.xcom_pull(key="target_ymd") }}',
			'dag_id': DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id_compaction,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS[_COMP],
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[_COMP][PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': '{{ ti.xcom_pull(key="target_ymd") }}-pub-{{ ti.xcom_pull(key="pub_id") }}-ap-{{ ti.xcom_pull(key="ap_id") }}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT_1,
		wait_showup_failure_cb=partial(_clean_up_to_failure),
		wait_complete_failure_cb=partial(_clean_up_to_failure),
		conclude_app_failure_cb=partial(_clean_up_to_failure)  # 실패 시 Jobs.retryCnt 증가시키고 Jobs.running=0
	)

	# 성공 시 Jobs 다큐먼트 삭제
	clean_up_to_success = PythonOperator(
		task_id='clean_up_to_success',
		python_callable=_clean_up_to_success
	)

	# 파이프라인
	setup >> check_dup_run >> upsert_job >> wait_for_zircon_b_gfp >> \
	set_zircon_trace_to_start >> \
	accumulate_zircon_b >> compact_zircon_b >> \
	clean_up_to_success
