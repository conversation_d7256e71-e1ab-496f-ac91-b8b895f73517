"""
### 의미 없는 ZirconTrace 확정

#### 위키
- [18-24. [Confirm] 의미 없는 ZirconTrace 확정](https://wiki.navercorp.com/pages/viewpage.action?pageId=2392148299)

#### 개요
- ZirconTrace에 처리되지 않고 남아 있는 의미 없는 실버그레이가 존재한다.
- AP 리포트는 존재하는데 실버가 없는 경우 이런 상황이 발생한다.
	- 테스트환경
		- source=AP인 OUTSIDE AP는 리포트 연동 정보를 리얼 데이터로 부어 넣고 있음
	- 리얼 환경
		- 베타 SSP & 리얼 AP로 연동한 경우
- 의미 없는 데이터가 계속 남아 있으면 지르콘이 재처리하려고 계속 시도를 하게된다.
	- 따라서 이런 데이터는 강제로 지르콘 B를 성공으로 설정하여 재처리 목록에 포함되지 않도록 한다.

#### 주기
- 매 시 30분
- trace_silvergrey_for_zb가 45분에 수행되므로 그 전에 확정

#### config
- 없음
"""

import logging
import os

import pendulum
from airflow.models.dag import DAG
from airflow.operators.python import ShortCircuitOperator, PythonOperator
from bson import ObjectId

from core import c3
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_ZIRCON_TRACES, ZIRCON_HOME
from core.dao import zircon_trace_dao, publisher_dao, adprovider_dao

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [CONFIRM-MEANINGLESS-SILVERGREY]'


def _get_meaningless_silvergreys(**context):
	"""
	ZirconTrace에서 의미 없는 실버그레이 조회
	정규 처리가 모두 끝난 D-3을 포함한 그 이 전 데이터 중 지르콘이 처리하지 않은 데이터.
	실버에 없는 매체로 트레이스 조회
	:param context:
	:return:
	"""
	# D-3일 이력 중
	three_days_ago = pendulum.now().subtract(days=3).format("YYYYMMDD")
	match = {
		'date': three_days_ago,  # 정규처리해서 처리하지 않은 D-3 일자 중
		'silvergreyCompletedAt': {'$ne': None},  # silvregrey가 처리되었고
		'zirconBTouchedAt': {'$eq': None},  # zircon이 아직 처리 대상으로 삼지 않은
		'publisher_id': {'$nin': _get_zbgfp_publisher_ids(three_days_ago)}  # ZBGFP에 없는 매체
	}
	sort = [('date', 1), ('publisher_id', 1), ('adProvider_id', 1)]
	print(f'match={match}')

	# 조회
	refined_traces = []
	traces = zircon_trace_dao.get_traces(match, sort=sort)
	for trace in traces:
		print(f'trace={trace}')
		refined_traces.append({
			'date': trace['date'],
			'pub_id': str(trace['publisher_id']),
			'ap_id': str(trace['adProvider_id']),
			'id': str(trace['_id']),
		})

	if refined_traces:
		context['ti'].xcom_push(XCOM_ZIRCON_TRACES, refined_traces)
		logging.info(f'{_LOG_PREFIX} 처리할 trace 개수 = {len(refined_traces)}')
		return True
	else:
		logging.info(f'{_LOG_PREFIX} 처리할 trace 없음')

	return False


def _get_zbgfp_publisher_ids(ymd: str):
	"""
	해당 날짜로 ZBGFP에 쌓인 매체ID 목록
	DB에서 바로 조회할 수 있게 ObjectId 타입으로 리턴
	:param ymd:
	:return:
	"""
	pub_obj_ids = []
	ymd_path = f'{ymd[:4]}/{ymd[4:6]}/{ymd[6:8]}'  # 20220401 -> 2022/04/01
	hours = [str(hour).zfill(2) for hour in range(24)]
	for hour in hours:
		check_dir = f'{ZIRCON_HOME}/b/gfp/warehouse/{ymd_path}/{hour}'
		# logging.info(f'{_LOG_PREFIX} check_dir={check_dir}')

		pub_dirs = c3.read_dir(check_dir)
		for pub_dir in pub_dirs:
			# logging.info(f'{_LOG_PREFIX} pub_dir={pub_dir}')
			path_suffix = pub_dir.get('pathSuffix')
			if path_suffix.startswith('_publisherId='):
				pub_obj_ids.append(ObjectId(path_suffix.replace('_publisherId=', '')))

	unique_pub_obj_ids = list(set(pub_obj_ids))  # 중복제거
	logging.info(f'{_LOG_PREFIX} ZBGFP에 존재하는 매체 ID 개수 = {len(unique_pub_obj_ids)}')
	for obj_id in unique_pub_obj_ids:
		logging.info(f'{_LOG_PREFIX} \tpub_id={obj_id}')

	return pub_obj_ids


def _confirm_meaningless_silvergreys(**context):
	"""
	의미 없는 ZirconTrace의 silvergrey 확정
	:param context:
	:return:
	"""

	# pub_id에 대한 이름을 갖고 있는 dict 설정
	# db연산을 조금이라도 덜 하기 위해 한 번만 조회해서 dict로 갖고 있자
	pub_map = {}
	pubs = publisher_dao.get_publishers({})  # 중복 제거한 pub_ids
	for pub in pubs:
		pub_map[str(pub.get('_id'))] = pub.get('name')

	# ap_id에 대한 이름을 갖고 있는 dict 설정
	# db연산을 조금이라도 덜 하기 위해 한 번만 조회해서 dict로 갖고 있자
	ap_map = {}
	aps = adprovider_dao.get_adproviders({})  # 중복 제거한 ap_ids
	for ap in aps:
		ap_map[str(ap.get('_id'))] = ap.get('name')


	refined_traces = context['ti'].xcom_pull(key=XCOM_ZIRCON_TRACES)

	msg = ''
	trace_obj_ids = []

	for refined_trace in refined_traces:
		pub_id = refined_trace["pub_id"]
		ap_id = refined_trace["ap_id"]
		msg = f'{msg}\n\tzirconTrace_id={refined_trace["id"]} date={refined_trace["date"]} ' \
			  f'pub_id={pub_id}({pub_map.get(pub_id)}) ap_id={ap_id}({ap_map.get(ap_id)})'
		trace_obj_ids.append(ObjectId(refined_trace['id']))

	now = pendulum.now(DEFAULT_TZ)
	match = {'_id': {'$in': trace_obj_ids}}
	update = [{
		'$set': {
			'zirconBTouchedAt': now,
			'zirconBStartedAt': now,
			'zirconBSucceededAt': now,
			'zirconBSilvergreyAt': '$silvergreyCompletedAt',  # 배열로 해야 업데이트됨
			'zirconBState': 'SUCCESS',
			'normalConfirm': 0,
			'modifiedAt': now,
		}
	}]
	zircon_trace_dao.update_trace_many(match, update)
	logging.info(f'{_LOG_PREFIX} 완료. 확정 건수 = {len(trace_obj_ids)}\n{msg}\n')


with DAG(
		_DAG_ID,
		description='ZirconTrace에서 의미 없는 실버그레이 확정',
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2024, 4, 1, tz=DEFAULT_TZ),
		schedule_interval="30 * * * *",  # 매 시 30분. trace_silvergrey_for_zb가 45분에 수행되므로 그 전에 정리
		tags=['zircon', 'zirconb', 'zb', 'trace', 'silvergrey', 'juyoun.kim', 'confirm'],
		catchup=False,
		max_active_runs=1,

) as dag:
	dag.doc_md = __doc__

	# ZirconTrace에서 의미 없는 실버그레이 조회
	get_meaningless_silvergreys = ShortCircuitOperator(
		task_id='get_meaningless_silvergreys',
		python_callable=_get_meaningless_silvergreys,
	)

	# 의미 없는 ZirconTrace 확정
	confirm_meaningless_silvergreys = PythonOperator(
		task_id='confirm_meaningless_silvergreys',
		python_callable=_confirm_meaningless_silvergreys,
	)

	# 파이프라인
	get_meaningless_silvergreys >> confirm_meaningless_silvergreys
