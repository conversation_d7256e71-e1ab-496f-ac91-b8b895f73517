"""
### Zircon B GFP 생성

#### 위키
- [18-5. [SPARK] Zircon B GFP 적재](https://wiki.navercorp.com/pages/viewpage.action?pageId=2388082632)

#### 개요
- Zircon B GFP 생성
- 일자별 생성(디폴트)
- 매체 단위 생성도 가능

####주기
- trigger_zircon_b_regular/reprocess/manaul에 따름

#### config
- target_ymd: '',  # YYYYMMDD
- kind: '',  # "regular", "reprocess", "manual", "trace_silver"
- pub_id: '',  # 특정 PUB ID 또는 모든 PUB
- silver_trace_id: '',  # silver 재처리 시에만 명시
"""

import logging
import os
from datetime import timedelta
from functools import partial

import pendulum
from airflow import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.sensors.python import PythonSensor
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType
from bson import ObjectId

from biz.zircon.b import refresh_recent_zircon_b_gfp_ymd
from core import utils
from core.base import PROFILE_TEST, PROFILE_REAL, PROFILE, ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, XCOM_TARGET_YMD, \
	POOL_SPARK, \
	SPARKLING_APP_JAR_PATH, SPARKLING_APP_HARD_LIMIT_4, SPARKLING_IMAGE, SPARK_SUBMIT_OPTIONS, \
	POOL_SPARK_ZIRCON, XCOM_PUB_ID, XCOM_SILVER_TRACE_ID, XCOM_KIND
from core.dao import job_dao, silver_trace_dao
from core.dao.environment_dao import get_environment_value_by_name, is_ready_silver_log
from core.dao.job_dao import clean_up_by_filter
from core.spark_pool import POOL_SLOT_NORMAL_33, POOL_SLOT_NORMAL_20
from core.spark_submit_op import invoke_job_with_args
from core.spark_task_group import create_task_group

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [SPK-ZBGFP]'

# Silver
_SILVER_LOG_CHECK_TIMEOUT = int(get_environment_value_by_name('silver-log-check-timeout'))  # 디폴트 3시간

# Spark App 정보
_ACCU = 'ACCUMULATION'
_COMP = 'COMPACTION'

# Spark
_SPARK_APP_CLASS = {
	_ACCU: 'com.navercorp.gfp.biz.zircon.b.ZirconBGfpAggregator',
	_COMP: 'com.navercorp.gfp.biz.zircon.b.ZirconBGfpCompactor'
}

# Profile
_PROFILE_SETTINGS = {
	_ACCU: {
		PROFILE_TEST: {
			SPARK_SUBMIT_OPTIONS: '''
			--executor-memory 4g
			--num-executors 3
			--executor-cores 2
			--driver-memory 3g
			--conf spark.sql.files.maxPartitionBytes=64mb
			--conf spark.sql.shuffle.partitions=120
			--conf spark.scheduler.mode=FAIR
			--conf spark.scheduler.allocation.file=hdfs://bizcloud/user/gfp-data/fairscheduler.xml
			--conf spark.scheduler.pool=zircon_b_gfp
		'''.split(),
		},
		PROFILE_REAL: {
			SPARK_SUBMIT_OPTIONS: '''
			--executor-memory 4g
			--num-executors 100
			--executor-cores 4
			--driver-memory 3g
			--conf spark.executor.memoryOverhead=2g
			--conf spark.driver.memoryOverhead=2g
			--conf spark.sql.files.maxPartitionBytes=64mb
			--conf spark.sql.shuffle.partitions=10000
			--conf spark.scheduler.mode=FAIR
			--conf spark.scheduler.allocation.file=hdfs://pg07/user/gfp-data/fairscheduler.xml
			--conf spark.scheduler.pool=zircon_b_gfp
		'''.split(),
		}
	},
	_COMP: {
		PROFILE_TEST: {
			SPARK_SUBMIT_OPTIONS: '''
			--executor-memory 1g
			--num-executors 10
			--executor-cores 2
			--driver-memory 2g
			--conf spark.sql.shuffle.partitions=400
			--conf spark.scheduler.mode=FAIR
			--conf spark.scheduler.allocation.file=hdfs://bizcloud/user/gfp-data/fairscheduler.xml
			--conf spark.scheduler.pool=zircon_b_gfp
		'''.split(),
		},
		PROFILE_REAL: {
			SPARK_SUBMIT_OPTIONS: '''
			--executor-memory 2g
			--num-executors 20
			--executor-cores 4
			--driver-memory 2g
			--conf spark.sql.shuffle.partitions=8000
			--conf spark.scheduler.mode=FAIR
			--conf spark.scheduler.allocation.file=hdfs://pg07/user/gfp-data/fairscheduler.xml
			--conf spark.scheduler.pool=zircon_b_gfp
		'''.split(),
		}
	}
}


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	# 날짜 설정
	_set_target_ymd(**context)

	# 파라미터 가져오기
	pub_id = context['params'].get(XCOM_PUB_ID)
	kind = context['params'].get(XCOM_KIND)
	s_trace_id = context['params'].get(XCOM_SILVER_TRACE_ID) if context['params'].get(XCOM_SILVER_TRACE_ID) else '-'

	validate_param(kind, pub_id, s_trace_id)

	# XCOM에 PUSH
	context['ti'].xcom_push(XCOM_KIND, kind)
	context['ti'].xcom_push(XCOM_PUB_ID, pub_id)
	context['ti'].xcom_push(XCOM_SILVER_TRACE_ID, s_trace_id)

	_print_settings(**context)


def _set_target_ymd(**context):
	"""
	사용자 입력 파라미터 또는 스케줄에 의한 파라미터로 결정된다.
	:param context:
	:return:
	"""
	target_ymd = context['params'].get(XCOM_TARGET_YMD)

	if target_ymd:
		target_ymd = str(target_ymd).strip()
		pendulum.from_format(target_ymd, 'YYYYMMDD')
		logging.info(f'{_LOG_PREFIX} Using target_ymd from "params"={target_ymd}')
	else:
		logging.info(f'{_LOG_PREFIX} params does not exist. target_ymd is empty')
		raise AirflowException(f'target_ymd is not defined. target_ymd={target_ymd}')

	context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)


def validate_param(kind: str, pub_id: str, s_trace_id):
	if kind != 'regular' and kind != 'trace_silver' and kind != 'manual':
		raise AirflowException(f'"kind" should be "regular" or "trace_silver"')

	if pub_id != '*' and len(pub_id) != 24:
		raise AirflowException(f'"pub_id" is not a 24-digit')

	if s_trace_id != '-' and len(s_trace_id) != 24:
		raise AirflowException(f'"s_trace_id" is not a 24-digit')

	if pub_id != '*' and not _is_hexadecimal(pub_id):
		raise AirflowException(f'"pub_id" is not a hexadecimal')

	if s_trace_id != '-' and not _is_hexadecimal(s_trace_id):
		raise AirflowException(f'"s_trace_id" is not a hexadecimal')


def _is_hexadecimal(value: str) -> bool:
	# 모든 문자가 16진수로 구성되어 있는지 확인
	if all(c.isdigit() or c.lower() in 'abcdef' for c in value):
		return True
	else:
		return False


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK}
Airflow Pool Slot: {POOL_SLOT_NORMAL_33}

Profile: {PROFILE}
Spark Submit Options :
	_ACCU:{_PROFILE_SETTINGS[_ACCU][PROFILE][SPARK_SUBMIT_OPTIONS]}
	_COMP:{_PROFILE_SETTINGS[_COMP][PROFILE][SPARK_SUBMIT_OPTIONS]}

Params:
	target_ymd={context['ti'].xcom_pull(key=XCOM_TARGET_YMD)}
	kind={context['ti'].xcom_pull(key=XCOM_KIND)}
	pub_id={context['ti'].xcom_pull(key=XCOM_PUB_ID)}
	SILVER_LOG_CHECK_TIMEOUT={_SILVER_LOG_CHECK_TIMEOUT}
------------------------------------------------------------------------------------------
''')


def _is_ready_silver(target_ymd: str):
	"""
	전 날 23시 실버 로그가 준비되었는지 확인한다.
	:param target_ymd: 포맷은 'YYYYMMDD'
	:return:
	"""
	prev_ymdh = pendulum.from_format(target_ymd, 'YYYYMMDD', DEFAULT_TZ).format("YYYYMMDD23")
	is_ready = is_ready_silver_log(prev_ymdh)
	logging.info(f'{_LOG_PREFIX} is {prev_ymdh} ready? {is_ready}')
	return is_ready


def _check_dup_run(**context):
	"""
	중복실행을 방지하기 위한 방어 로직
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	kind = context['ti'].xcom_pull(key=XCOM_KIND)
	pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	silver_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)

	if pub_id == '*':
		# 매체 상관없이 동일 날짜에 대해 진행중인 작업이 있는지
		doc = job_dao.get_job({'type': DAG_ID, 'datetime': target_ymd, 'running': 1})
	else:
		# 특정 매체, 동일 날짜에 대해 진행중인 작업이 있는지
		doc = job_dao.get_job(
			{'type': DAG_ID, 'datetime': target_ymd, 'detail.publisher_id': ObjectId(pub_id), 'running': 1})

	my_dag_run_id = context['dag_run'].run_id
	if doc:
		running_dag_runs: list[DagRun] = DagRun.find(dag_id=DAG_ID, state=DagRunState.RUNNING)
		for dag_run in running_dag_runs:
			ti = dag_run.get_task_instance('setup')

			your_run_id = dag_run.run_id
			your_target_ymd = ti.xcom_pull(key=XCOM_TARGET_YMD)
			your_kind = ti.xcom_pull(key=XCOM_KIND)
			your_pub_id = ti.xcom_pull(key=XCOM_PUB_ID)
			your_silver_trace_id = ti.xcom_pull(key=XCOM_SILVER_TRACE_ID)

			if my_dag_run_id != your_run_id and target_ymd == your_target_ymd:
				raise AirflowException(f'이미 실행중인 DagRun이 있음. target_ymd={your_target_ymd}'
									   f' Your dag_run_id={your_run_id} kind={your_kind} pub_id={your_pub_id} silver_trace_id={your_silver_trace_id}'
									   f' My dag_run_id={my_dag_run_id} kind={kind} pub_id={pub_id} silver_trace_id={silver_trace_id}')
	else:
		pass


def _upsert_job(**context):
	"""
	해당 시간대의 Job 추가 또는 running=1로 업데이트
	:param context:
	:return:
	"""
	target_ymd = context["ti"].xcom_pull(key=XCOM_TARGET_YMD)
	kind = context["ti"].xcom_pull(key=XCOM_KIND)
	pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	silver_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)

	filter = {
		'type': DAG_ID,
		'datetime': target_ymd,
		'detail.kind': kind,
		'detail.publisher_id': ObjectId(pub_id) if pub_id != '*' else None,
		'detail.silverTrace_id': ObjectId(silver_trace_id) if silver_trace_id != '-' else None,
	}
	job = job_dao.get_job(filter)

	if job:
		update = {'$set': {'running': 1, 'modifiedAt': pendulum.now(tz=DEFAULT_TZ)}}
		job_dao.update_job(filter, update)
	else:
		dag_run = context["dag_run"]
		job = {
			'type': DAG_ID,
			'datetime': target_ymd,
			'detail': {
				'kind': kind,
				'publisher_id': ObjectId(pub_id) if pub_id != '*' else None,
				'silverTrace_id': ObjectId(silver_trace_id) if silver_trace_id != '-' else None,
			},
			'retryCnt': -1,
			'running': 1,
			'manual': 0 if dag_run.run_type == DagRunType.SCHEDULED else 1
		}
		job_dao.insert_job(job)
		logging.info(f'{_LOG_PREFIX} {target_ymd} Jobs에 추가. dag_run_type={dag_run.run_type} {job}')


def _get_app_args(context: dict):
	"""
	스파크 애플리케이션 실행에 필요한 아규먼트 가져오기
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	kind = context['ti'].xcom_pull(key=XCOM_KIND)
	pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	s_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)
	logging.info(f'{_LOG_PREFIX} target_ymd={target_ymd} kind={kind} pub_id={pub_id} s_trace_id={s_trace_id}')
	return [kind, target_ymd, pub_id, s_trace_id]


def _clean_up_to_success(**context):
	"""
	성공했으므로 job 삭제
	:param context:
	:return:
	"""
	pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	s_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)

	filter = {
		'type': DAG_ID,
		'datetime': context["ti"].xcom_pull(key=XCOM_TARGET_YMD),
		'detail.kind': context["ti"].xcom_pull(key=XCOM_KIND),
		'detail.publisher_id': ObjectId(pub_id) if pub_id != '*' else None,
		'detail.silverTrace_id': ObjectId(s_trace_id) if s_trace_id != '-' else None,
	}
	clean_up_by_filter(filter, True)


# if s_trace_id and s_trace_id != '-':
# 	_update_silver_trace_zircon_b_gfp_completed_at(s_trace_id)


def _clean_up_to_failure(context: dict):
	"""
	실패했으므로 retryCnt증가시키고 running=0 설정
	:param context:
	:return:
	"""
	pub_id = context['ti'].xcom_pull(key=XCOM_PUB_ID)
	s_trace_id = context['ti'].xcom_pull(key=XCOM_SILVER_TRACE_ID)

	filter = {
		'type': DAG_ID,
		'datetime': context["ti"].xcom_pull(key=XCOM_TARGET_YMD),
		'detail.kind': context["ti"].xcom_pull(key=XCOM_KIND),
		'detail.publisher_id': ObjectId(pub_id) if pub_id != '*' else None,
		'detail.silverTrace_id': ObjectId(s_trace_id) if s_trace_id != '-' else None,
	}
	clean_up_by_filter(filter, False)


def _update_silver_trace_zircon_b_gfp_completed_at(s_trace_id: str):
	"""
	SilverTrace.zirconBGfpCompletedAt 업데이트
	:param context:
	:return:
	"""
	match = {'_id': ObjectId(s_trace_id)}
	now = pendulum.now(DEFAULT_TZ)
	update = [{
		'$set': {
			'zirconBGfpCompletedAt': now,
			'modifiedAt': now,
		}
	}]
	result = silver_trace_dao.update_trace_one(match, update)
	logging.info(f'{_LOG_PREFIX} _update_silver_trace_zircon_b_gfp_completed_at() result={result}')


with DAG(
		DAG_ID,
		description='Zircon B GFP 생성 스파크 앱 실행',
		tags=['spark', 'zircon', 'zirconb', 'zirconbgfp', 'zb', 'zbgfp', 'juyoun.kim'],
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2024, 3, 31, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
		params={
			XCOM_TARGET_YMD: '',
			XCOM_KIND: '',
			XCOM_PUB_ID: '',
			XCOM_SILVER_TRACE_ID: '',
		}
) as dag:
	dag.doc_md = __doc__

	# 환경 설정
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 중복실행 여부 확인
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=_check_dup_run,
	)

	# 해당 시간대의 Jobs.running=1로 추가
	upsert_job = PythonOperator(
		task_id='upsert_job',
		python_callable=_upsert_job
	)

	# 실버 로그가 준비됐는지 확인
	wait_for_silver_log = PythonSensor(
		task_id='wait_for_silver_log',
		python_callable=_is_ready_silver,
		op_args=[
			f'{{{{ ti.xcom_pull(task_ids="{setup.task_id}", key="target_ymd") }}}}'
		],
		poke_interval=60 * 15,  # 15분마다 재시도
		execution_timeout=timedelta(seconds=_SILVER_LOG_CHECK_TIMEOUT),
		on_failure_callback=partial(_clean_up_to_failure)
	)

	# Zircon B GFP 생성
	task_group_id_accumulation = 'accumulate_zircon_b_gfp'
	accumulate_zircon_b_gfp = create_task_group(
		task_group_id_accumulation,
		tooltip=_SPARK_APP_CLASS[_ACCU],
		spark_pool=POOL_SPARK_ZIRCON,
		pool_slot=POOL_SLOT_NORMAL_33,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': f'{_SPARK_APP_CLASS[_ACCU]}',
			'dt': '{{ ti.xcom_pull(key="target_ymd") }}',
			'dag_id': DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id_accumulation,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS[_ACCU],
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[_ACCU][PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': '{{ ti.xcom_pull(key="target_ymd") }}-pub-{{ ti.xcom_pull(key="pub_id") }}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT_4,
		wait_showup_failure_cb=partial(_clean_up_to_failure),
		wait_complete_failure_cb=partial(_clean_up_to_failure),
		conclude_app_failure_cb=partial(_clean_up_to_failure)  # 실패 시 Jobs.retryCnt 증가시키고 Jobs.running=0
	)

	# Zircon B GFP 컴팩션
	task_group_id_compaction = 'compact_zircon_b_gfp'
	compact_zircon_b_gfp = create_task_group(
		task_group_id_compaction,
		tooltip=_SPARK_APP_CLASS[_COMP],
		spark_pool=POOL_SPARK_ZIRCON,
		pool_slot=POOL_SLOT_NORMAL_20,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': f'{_SPARK_APP_CLASS[_COMP]}',
			'dt': '{{ ti.xcom_pull(key="target_ymd") }}',
			'dag_id': DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id_compaction,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS[_COMP],
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[_COMP][PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': '{{ ti.xcom_pull(key="target_ymd") }}-pub-{{ ti.xcom_pull(key="pub_id") }}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT_4,
		wait_showup_failure_cb=partial(_clean_up_to_failure),
		wait_complete_failure_cb=partial(_clean_up_to_failure),
		conclude_app_failure_cb=partial(_clean_up_to_failure)  # 실패 시 Jobs.retryCnt 증가시키고 Jobs.running=0
	)

	# 최근 Zircon B GFP 컴팩션 일자 갱신
	trigger_refresh_recent_zircon_b_gfp_ymd = TriggerDagRunOperator(
		trigger_dag_id=refresh_recent_zircon_b_gfp_ymd.DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id='trigger_refresh_recent_zircon_b_gfp_ymd',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=True,
		poke_interval=5,  # wait_for_completion=True 일때만 유효한 옵션
		reset_dag_run=True,
		on_failure_callback=partial(_clean_up_to_failure)  # 적재 실패 시 Jobs.retryCnt 증가시키고 Jobs.running=0
	)

	# 성공 시 Jobs 다큐먼트 삭제
	clean_up_to_success = PythonOperator(
		task_id='clean_up_to_success',
		python_callable=_clean_up_to_success
	)

	# 파이프라인
	setup >> check_dup_run >> upsert_job >> wait_for_silver_log >> \
	accumulate_zircon_b_gfp >> compact_zircon_b_gfp >> \
	trigger_refresh_recent_zircon_b_gfp_ymd >> clean_up_to_success
