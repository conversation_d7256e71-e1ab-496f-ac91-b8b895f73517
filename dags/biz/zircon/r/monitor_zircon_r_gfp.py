"""
### Zircon R GFP 지연 모니터링

#### 위키
- [18-26. [DELETE] 보관 기간이 지난 Zircon R 삭제](https://wiki.navercorp.com/pages/viewpage.action?pageId=2768029636)

#### 개요
- Zircon R GFP 지연 모니터링

#### 주기
- 매시 30분에 배치 서버 호출

"""

import logging
import os

import pendulum
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator

from core import utils
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, BATCH_SERVER_ADDRESS

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [MONITOR-ZRGFP]'

# Batch 호출 주소
_REQ_URL = BATCH_SERVER_ADDRESS + '/batch/zircon/r/gfp/monitor'


def _monitor_zircon_r_gfp():
	logging.info(f'{_LOG_PREFIX} url={_REQ_URL}')
	res = utils.request(_REQ_URL)
	return res


with DAG(
		DAG_ID,
		description='Zircon R GFP 모니터링',
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2024, 9, 1, tz=DEFAULT_TZ),
		schedule_interval='30 * * * *',  # 매 시 30분에 배치 서버 호출
		tags=['zircon', 'zirconr', 'zr', 'zrgfp', 'batch', 'monitor', 'juyoun.kim'],
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	# Zircon R GFP 모니터링
	monitor_zircon_r_gfp = PythonOperator(
		task_id='monitor_zircon_r_gfp',
		python_callable=_monitor_zircon_r_gfp,
	)

	# 파이프라인
	monitor_zircon_r_gfp
