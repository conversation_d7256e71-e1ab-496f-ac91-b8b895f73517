"""
### Zircon R GFP 생성 정규 처리
- D-2 생성

#### 위키
- [18-1. [Trigger] Zircon R GFP 생성 정규 - 1일/2일 전](https://wiki.navercorp.com/pages/viewpage.action?pageId=2380320462)

#### 주기
- 매일 04:30분
- 동시에 하나의 DAG RUN만 허용

#### config
- 없음
- 수동 실행을 위해서는 trigger_zircon_r_gfp_manual을 사용헤 주세요.
"""

import logging
import os

import pendulum
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.types import DagRunType

from biz.zircon.r.spark_zircon_r_gfp import DAG_ID as SPARK_DAG_ID, XCOM_PUB_ID
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD, XCOM_KIND, XCOM_SILVER_TRACE_ID

# DAG 기본정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRG-ZRGFP-REGULAR-2D-AGO]'


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	_set_target_ymd(**context)  # 생성해야 할 일자 설정
	context['ti'].xcom_push(XCOM_PUB_ID, '*')  # 모든 매체


def _set_target_ymd(**context):
	"""
	스케줄에 의한 파라미터로 결정된다.
	:param context:
	:return:
	"""
	target_ymd = pendulum.instance(context['logical_date']).in_tz(DEFAULT_TZ).subtract(days=1).format('YYYYMMDD')
	logging.info(f'{_LOG_PREFIX} Using target_ymd from logical_date={target_ymd}')
	context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)


with DAG(
		DAG_ID,
		description='ZIRCON R GFP 생성을 위한 정규 DAG. spark_zircon_r_gfp 트리거시킴',
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		tags=['trigger', 'zircon', 'zirconrgfp', 'zr', 'zrgfp', 'regular', 'juyoun.kim', '2d.ago'],
		start_date=pendulum.datetime(2025, 1, 1, tz=DEFAULT_TZ),
		# 매일 02시 30분
		# 이틀 전 Zircon R GFP는 이미 전 날 확정됐기에 02:30분에 실행
		schedule_interval='30 2 * * *',
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# 처리해야 할 일자를 xcom에 push
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# spark_zirocn_r_gfp 트리거
	trigger_zircon_r_gfp = TriggerDagRunOperator(
		trigger_dag_id=SPARK_DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{SPARK_DAG_ID}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=True,
		poke_interval=60,
		reset_dag_run=True,
		conf={
			XCOM_KIND: 'regular',
			XCOM_TARGET_YMD: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			XCOM_PUB_ID: f'{{{{ ti.xcom_pull(key="{XCOM_PUB_ID}") }}}}',
			XCOM_SILVER_TRACE_ID: '-',
		}
	)

	# 파이프라인
	setup >> trigger_zircon_r_gfp
