"""
### Silver 재처리에 의한 Zircon R GFP 재처리

#### 위키
- [18-22. [Trace] SilverTrace 감지](https://wiki.navercorp.com/pages/viewpage.action?pageId=2392147892)

#### 개요
- SilverTrace 컬렉션에서 트레이스 한 건씩 재처리해야 할 트레이스름 모두 가져와서 spark_zircon_r_gfp DAG을 트리거시킴
- 동시에 하나의 DAG RUN만 허용

#### 주기
- 매 11,23시 50분
  * make_silver_trace가 하루 2번 실행되므로 동일하게 하루 2번 검사하되, 시간만 20분 후인 50분으로 설정

#### config
- 없음
"""
import logging
import os
from time import sleep

import pendulum
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.email import EmailOperator
from airflow.operators.python import ShortCircuitOperator, PythonOperator
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType

from biz.zircon.r import spark_zircon_r_gfp
from core.airflow_api import trigger_dagrun
from core.base import DEFAULT_TZ, XCOM_TARGET_YMD, ALERT_EMAIL_ADDRESSES, XCOM_PUB_ID, \
	XCOM_SILVER_TRACE_ID, XCOM_KIND, PROFILE, XCOM_EMAIL_SUBJECT, XCOM_EMAIL_CONTENT, ZIRCON_ALERT_EMAIL_ADDRESS
from core.dao import silver_trace_dao
from core.dao.environment_dao import get_environment_value_by_name
from core.utils import prettyStr, prettyStrOfMongoDoc

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRACE-SILVER]'

_TRACE_SILVER_KIND = 'trace_silver'

# 디폴트 1시간
_ZIRCON_R_GFP_REGULAR_EXE_TIMEOUT = int(get_environment_value_by_name('zircon-r-gfp-regular-execution-timeout'))

_XCOM_RESULTS = 'results'

# 재처리가 필요한 실버 트레이스가 있는지 확인하기 위한 조건
_MATCH = {
	'$or': [{'zirconRGfpCompletedAt': {'$exists': False}}, {'zirconRGfpCompletedAt': None}],
	'date': {'$lte': pendulum.now(DEFAULT_TZ).subtract(days=1).format('YYYYMMDD')},  # D-1을 포함한 이전 날짜
}


def _is_exist_silver_trace(**context):
	"""
	재처리가 필요한 실버 트레이스가 있는지 확인

	:param context:
	:return: 
	"""
	cnt = silver_trace_dao.get_trace_count(_MATCH)
	if cnt > 0:
		logging.info(f'처리할 트레이스. 개수={cnt} filter={prettyStr(_MATCH)}')
		return True
	else:
		return False


def _process(**context):
	results = []

	# 재처리가 필요한 실버 트레이스 조회
	sort = [('date', 1)]  # 날짜 오름차순
	silver_traces = silver_trace_dao.get_traces(_MATCH, sort)

	# 날짜 별로 돌면서
	for silver_trace in silver_traces:
		logging.info(f'{_LOG_PREFIX} silver_trace={prettyStrOfMongoDoc(silver_trace)}')

		current_ymd = silver_trace.get('date')
		current_s_trace_obj_id = silver_trace.get('_id')

		# 해당 날짜의 ZRGFP를 생성하고
		dag_run_id = _trigger_spark_zircon_r_gfp(current_ymd, current_s_trace_obj_id)

		# ZRGFP가 완료될 때까지 대기했다가
		dag_run_state = None
		while True:
			dag_run_state = _get_dag_run_state(dag_run_id)

			if dag_run_state == DagRunState.SUCCESS or dag_run_state == DagRunState.FAILED:
				break
			else:
				sleep(60)

		# ZRGFP가 성공적으로 끝나면 SilverTrace.zirconRGfpCompletedAt 업데이트
		if dag_run_state == DagRunState.SUCCESS:
			_update_silver_trace_zircon_r_gfp_completed_at(current_s_trace_obj_id)
		# ZRGFP가 실패하면 다음 주기에 추출되어 처리할 수 있도록 pass
		else:
			dag_run_state = DagRunState.FAILED
			logging.error(f'실패. dag_run_id={dag_run_id} dag_run_state={dag_run_state} '
						  f'ymd={current_ymd} s_trace_id={current_s_trace_obj_id}')

		# 결과 목록에 추가
		results.append({
			'ymd': current_ymd,
			's_trace_id_str': str(current_s_trace_obj_id),
			'state': dag_run_state,
		})

	context['ti'].xcom_push(key=_XCOM_RESULTS, value=results)


def _trigger_spark_zircon_r_gfp(ymd, s_trace_id):
	conf = {
		XCOM_KIND: _TRACE_SILVER_KIND,
		XCOM_TARGET_YMD: ymd,  # YYYYMMDD
		XCOM_PUB_ID: "*",
		XCOM_SILVER_TRACE_ID: str(s_trace_id),
	}

	# 스파크 트리거
	spark_dag_run_id = trigger_dagrun(spark_zircon_r_gfp.DAG_ID,
									  pendulum.now(DEFAULT_TZ),
									  conf,
									  dag_run_type=DagRunType.SCHEDULED)
	return spark_dag_run_id


def _get_dag_run_state(dag_run_id):
	dag_runs = DagRun.find(dag_id=spark_zircon_r_gfp.DAG_ID, run_id=dag_run_id)
	if dag_runs:
		dag_run: DagRun = dag_runs.pop()
		return dag_run.get_state()


def _update_silver_trace_zircon_r_gfp_completed_at(s_trace_obj_id):
	"""
	SilverTrace.zirconRGfpCompletedAt 업데이트
	:param context:
	:return:
	"""
	match = {'_id': s_trace_obj_id}
	now = pendulum.now(DEFAULT_TZ)
	update = {'$set': {
		'zirconRGfpCompletedAt': now,
		'modifiedAt': now,
	}}
	result = silver_trace_dao.update_trace_one(match, update)
	logging.info(
		f'{_LOG_PREFIX} _update_silver_trace_zircon_r_gfp_completed_at() modified_count={result.modified_count}')


def _set_email_contents(**context):
	subject = f'[{PROFILE.upper()}] Zircon R GFP 재처리 내역 by Silver'

	content = ''
	results = context['ti'].xcom_pull(key=_XCOM_RESULTS)
	for (idx, result) in enumerate(results):
		content += f'[{str(idx + 1).zfill(3)}] SilverTrace._id={result["s_trace_id_str"]} &nbsp; date=<span style="color:goldenrod">{result["ymd"]}</span> &nbsp; result={result["state"]}'

	context['ti'].xcom_push(XCOM_EMAIL_SUBJECT, subject)
	context['ti'].xcom_push(XCOM_EMAIL_CONTENT, content)

	logging.info(f'{_LOG_PREFIX} _set_email_contents() \nsubject={subject}\ncontent={content}')


with DAG(
		_DAG_ID,
		description='Silver 재처리에 의한 Zircon R GFP 자동 재처리 DAG',
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2025, 1, 1, tz=DEFAULT_TZ),

		# make_silver_trace가 하루 2번 실행되므로 동일하게 하루 2번 검사하되, 시간만 20분 후인 50분으로 설정
		schedule_interval="50 11,23 * * *",
		# schedule_interval=None,

		tags=['zircon', 'zirconr', 'zirconrgfp', 'zr', 'zrgfp', 'trace', 'silver', 'juyoun.kim'],
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# 재처리해야 할 실버가 있을 때만 다운스트림 태스크 진행
	# SilverTrace에서 일자 오름차순으로 여러 개 추출
	is_exist_silver_trace = ShortCircuitOperator(
		task_id='is_exist_silver_trace',
		python_callable=_is_exist_silver_trace,
	)

	process = PythonOperator(
		task_id='process',
		python_callable=_process
	)

	# 이메일 내용 작성
	set_email_contents = PythonOperator(
		task_id='set_email_contents',
		python_callable=_set_email_contents,
	)

	# 처리내역 이메일로 전송
	send_email = EmailOperator(
		task_id='send_email',
		to=ZIRCON_ALERT_EMAIL_ADDRESS,
		subject='{{ ti.xcom_pull(key="subject") }}',
		html_content='{{ ti.xcom_pull(key="content") }}',
	)

	# 파이프라인
	is_exist_silver_trace >> process >> set_email_contents >> send_email
