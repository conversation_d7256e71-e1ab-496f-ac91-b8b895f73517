"""
[ 개요 ]
    - Jobs 이상 감지
        - 최대 재시도 횟수에 도달한 job이 있는지 검사하여 알림
        - 주어진 시간을 초과하여 실행되고 있는 job이 있다면 알림

[ 주기 ]
    - 매 시 35분

[ config ]
    - 없음
"""
import logging
import os

import pendulum
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email

from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ
from core.dao import job_dao
from core.dao.environment_dao import get_environment_value_by_name
from core.dao.job_dao import MAX_RETRY_CNT, get_jobs

# 기본 정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [MONITOR-JOBS]'


def _monitor_jobs(**context):
	_monitor_max_retry_cnt_over_jobs(**context)
	_monitor_too_long_jobs(**context)


def _monitor_max_retry_cnt_over_jobs(**context):
	"""
	최대 재시도 횟수에 다다른 job이 있다면 알림
	:param context:
	:return:
	"""
	retryCnt = MAX_RETRY_CNT - 1
	jobs = get_jobs({'retryCnt': {'$gt': retryCnt}})
	if jobs:
		_alarm_of_max_retry_cnt_over_jobs(jobs)


def _alarm_of_max_retry_cnt_over_jobs(jobs: list):
	title = '[Jobs 확인 필요] 최대 재시도 횟수에 다다름'

	body = f'''
    확인이 필요한 건 수: {len(jobs)}<br/>
    최대 재시도 횟수: {job_dao.MAX_RETRY_CNT}<br/><br/>
    ''' + _get_html_for_jobs(jobs)

	logging.info(f'{_LOG_PREFIX} title={title}')
	logging.info(f'{_LOG_PREFIX} body={body}')

	send_email(ALERT_EMAIL_ADDRESSES, title, body)


def _monitor_too_long_jobs(**context):
	"""
	주어진 시간을 초과하여 실행되고 있는 job이 있다면 알림
	:param context:
	:return:
	"""
	timeoutInHours = int(get_environment_value_by_name('job-timeout-in-hours'))
	criteria = pendulum.now(tz=DEFAULT_TZ).subtract(hours=timeoutInHours)
	jobs = get_jobs({'running': 1, 'modifiedAt': {'$lt': criteria}})
	if jobs:
		_alarm_of_too_long_running_jobs(jobs, timeoutInHours)


def _alarm_of_too_long_running_jobs(jobs: list, timeoutInHours: int):
	title = '[Jobs 확인 필요] 너무 오래 실행되고 있는 Job이 있습니다.'

	body = f'''
    확인이 필요한 건 수: {len(jobs)}<br/>
    Job Timeout(단위: 시간): {timeoutInHours}<br/><br/>
    ''' + _get_html_for_jobs(jobs)

	logging.info(f'{_LOG_PREFIX} title={title}')
	logging.info(f'{_LOG_PREFIX} body={body}')

	send_email(ALERT_EMAIL_ADDRESSES, title, body)


def _get_html_for_jobs(jobs) -> str:
	html = '''
    <table style='width: 100%; border: 1px solid #444444; border-collapse: collapse;'>
        <tr>
            <th style='text-align: center; border: 1px solid #444444;'>type</th>
            <th style='text-align: center; border: 1px solid #444444;'>datetime</th>
            <th style='text-align: center; border: 1px solid #444444;'>running</th>
            <th style='text-align: center; border: 1px solid #444444;'>retryCnt</th>
            <th style='text-align: center; border: 1px solid #444444;'>manual</th>
            <th style='text-align: center; border: 1px solid #444444;'>detail</th>
            <th style='text-align: center; border: 1px solid #444444;'>createdAt</th>
            <th style='text-align: center; border: 1px solid #444444;'>modifiedAt</th>
        </tr>'''

	for job in jobs:
		html += f"<tr>" \
				f"     <td style='text-align: center; border: 1px solid #444444;'>{job.get('type')}</td>" \
				f"     <td style='text-align: center; border: 1px solid #444444;'>{job.get('datetime')}</td>" \
				f"     <td style='text-align: center; border: 1px solid #444444;'>{job.get('running')}</td>" \
				f"     <td style='text-align: center; border: 1px solid #444444;'>{job.get('retryCnt')}</td>" \
				f"     <td style='text-align: center; border: 1px solid #444444;'>{job.get('manual')}</td>" \
				f"     <td style='text-align: center; border: 1px solid #444444;'>{job.get('detail')}</td>" \
				f"     <td style='text-align: center; border: 1px solid #444444;'>{job.get('createdAt')}</td>" \
				f"     <td style='text-align: center; border: 1px solid #444444;'>{job.get('modifiedAt')}</td>" \
				f"</tr>"

	html += '</table>'

	return html


with DAG(
		DAG_ID,
		description='Jobs 감시',
		tags=['monitor', 'job', 'juyoun.kim'],
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2022, 3, 1, tz=DEFAULT_TZ),
		schedule_interval='35 * * * *',
		catchup=False,
) as dag:
	monitor_jobs = PythonOperator(
		task_id='monitor_jobs',
		python_callable=_monitor_jobs,
	)

	monitor_jobs
