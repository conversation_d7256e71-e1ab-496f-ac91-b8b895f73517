"""
silvergrey base 설정 정보
"""

import logging
from datetime import datetime
from typing import Callable, List

import pendulum
from airflow.exceptions import AirflowException, AirflowFailException
from bson import ObjectId

from core.base import DEFAULT_TZ
from core.dao import zircon_trace_dao

# 2일이 지나면, 스케쥴 실패 처리됨
DEADLINE_HOURS = 53

# xcom
XCOM_COMPLETE_RATIO = 'complete_ratio'
XCOM_WAITING_RATIO = 'waiting_ratio'
XCOM_DEADLINE = 'deadline'

XCOM_YMD = 'ymd'  # 스케쥴 ymd
XCOM_SCHEDULE_ID = 'schedule_id'
XCOM_END_DATE = 'end_date'
XCOM_REPORT_API_TYPE = 'report_api_type'
XCOM_ADPROVIDER_ID = 'adprovider_id'
XCOM_PUBLISHER_ID = 'publisher_id'
XCOM_ADPROVIDER_IDS = 'adprovider_ids'
XCOM_PUBLISHER_IDS = 'publisher_ids'

_dag_mapping = {
	'GFP': {
		'dag_id': 'spark_silvergrey_nonrk_gfp',
	},
}


def map_source_type_to_dag(source_type: str):
	"""
	source=GFP 인 경우, NonRK GFP 스파크 집계

	:param source_type: only GFP
	:return:
	"""
	if _dag_mapping.get(source_type):
		return _dag_mapping[source_type]['dag_id']


def check_deadline(deadline: datetime):
	if pendulum.now() > deadline:
		raise AirflowFailException(f'deadline passed: {deadline}')


def _validate_schedule_args(schedule_args: dict):
	if schedule_args['ymd'] and len(schedule_args['ymd']) == 8 \
			and schedule_args['end_date'] and len(schedule_args['end_date']) == 8 \
			and schedule_args['report_api_type'] \
			and schedule_args['adprovider_id'] and schedule_args['publisher_id']:

		pendulum.from_format(schedule_args['ymd'], 'YYYYMMDD')
		pendulum.from_format(schedule_args['end_date'], 'YYYYMMDD')
		ObjectId(schedule_args['adprovider_id'])
		ObjectId(schedule_args['publisher_id'])

		return True
	else:
		return False


def init_settings(fetch_schedule_args: Callable, **context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
		- schedule_id (required) / schedule_args (optional)
		- schedule_id 가 없는 경우, 에러 처리
		- schedule_args 가 없는 경우, BatchReportJobSchedules 에서 schedule_id 에 해당하는 schedule_args 정보를 조회함
		- params.schedule_args.end_date = SummaryHistory.datatime

	:param fetch_schedule_args:
	:param context:
	:return:
	"""

	schedule_id = context['params'].get('schedule_id')
	schedule_args = context['params'].get('schedule_args')

	if schedule_id:
		context['ti'].xcom_push(XCOM_SCHEDULE_ID, schedule_id)

		if not (_validate_schedule_args(schedule_args)):
			# schedule_id 로 schedule_args 정보 조회
			schedule_args = fetch_schedule_args(schedule_id)

		context['ti'].xcom_push(XCOM_YMD, schedule_args['ymd'])
		context['ti'].xcom_push(XCOM_END_DATE, schedule_args['end_date'])
		context['ti'].xcom_push(XCOM_REPORT_API_TYPE, schedule_args['report_api_type'])
		context['ti'].xcom_push(XCOM_ADPROVIDER_ID, schedule_args['adprovider_id'])
		context['ti'].xcom_push(XCOM_PUBLISHER_ID, schedule_args['publisher_id'])
	else:
		# schedule_id 가 없으면 에러
		logging.error(f'There is not "params.schedule_id"')
		raise AirflowException('There is not "params.schedule_id"')


def print_settings(*, pool_spark: str, pool_slot: str, profile: str, spark_submit_options: List[str], **context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {pool_spark}
Airflow Pool Slot: {pool_slot}

Profile: {profile.upper()}
Spark Submit Options: {spark_submit_options}
Params:
	ymd={context["ti"].xcom_pull(key=XCOM_YMD)}
	schedule_id={context["ti"].xcom_pull(key=XCOM_SCHEDULE_ID)}
	end_date={context["ti"].xcom_pull(key=XCOM_END_DATE)}
	report_api_type={context["ti"].xcom_pull(key=XCOM_REPORT_API_TYPE)}
	adprovider_id={context["ti"].xcom_pull(key=XCOM_ADPROVIDER_ID)}
	publisher_id={context["ti"].xcom_pull(key=XCOM_PUBLISHER_ID)}
------------------------------------------------------------------------------------------
''')


def update_zircon_trace(date: str, adprovider_id: str, publisher_id: str, success: bool):
	"""
	ZirconTrace 성공/실패 이력 추가

	:param date: 데이터 날자 (YYYYMMDD)
	:param adprovider_id:
	:param publisher_id:
	:param success:
	:return:
	"""

	today = pendulum.now(DEFAULT_TZ) if success else None

	match = {'date': date, 'adProvider_id': ObjectId(adprovider_id), 'publisher_id': ObjectId(publisher_id)}
	update = {
		'$set': {'silvergreyCompletedAt': today},
		'$setOnInsert': {'createdAt': today, 'expiredAt': pendulum.from_format(date, 'YYYYMMDD', DEFAULT_TZ).add(months=12).add(days=1)},
	}

	result = zircon_trace_dao.update_trace_one(match, update, True)

	logging.info(f'update_zircon_trace :: date={date}, adprovider_id={adprovider_id}, publisher_id={publisher_id} :: result={result}')
