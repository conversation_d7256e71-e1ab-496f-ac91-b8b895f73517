"""
### silvergrey schedules 정규 트리거 DAG

#### 0. 위키
- [06. 실버그레이 로그 (NonRk - GFP)](https://wiki.navercorp.com/pages/viewpage.action?pageId=835654925)

#### 1. 주기
- 매일 05시

#### 2. 스케쥴 콜렉션
- Cms DB : BatchReportJobSchedules

#### 3. 트리거 대상 DAG
- spark_silvergrey_nonrk_gfp >> spark_silvergrey_nonrk_gfp_cms

#### 4. config
- target_ymd : 처리할 스케쥴 날짜 YYYYMMDD ( default: 오늘 )
"""

import os
import logging
import pendulum

from time import sleep
from datetime import timedelta

from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.exceptions import AirflowSkipException

from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD
from biz.silvergrey.silvergrey_base import \
	XCOM_COMPLETE_RATIO, XCOM_WAITING_RATIO, XCOM_DEADLINE, DEADLINE_HOURS, \
	map_source_type_to_dag, check_deadline
from biz.silvergrey.silvergrey_schedule_dao import COLLECTION, \
	fetch_schedules, fetch_schedules_count, is_ready_to_trigger, trigger_dag_with_api, \
	ST_READY

_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRIGGER-SILVERGREY-SCHEDULE-REGULAR]'


def _setup(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화 및 로깅

	:param context:
	:return:
	"""

	_init_settings(**context)
	# _print_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
		- target_ymd / complete_ratio / waiting_ratio / deadline

	:param context:
	:return:
	"""

	target_ymd = context['params'][XCOM_TARGET_YMD]
	if target_ymd:
		pendulum.from_format(target_ymd, 'YYYYMMDD')
		logging.info(f'{_LOG_PREFIX} Using target_ymd from "params". target_ymd={target_ymd}')
	else:
		target_ymd = pendulum.instance(context['logical_date']).add(days=1).in_tz(DEFAULT_TZ).format('YYYYMMDD')
		logging.info(f'{_LOG_PREFIX} Using target_ymd from tz-shifted "logical_date". target_ymd={target_ymd}')

	total_count, complete_count, waiting_count = fetch_schedules_count(target_ymd)

	logging.info(f'{_LOG_PREFIX} {COLLECTION}: target_ymd={target_ymd}, total_count={total_count}')

	if total_count == 0:
		raise AirflowSkipException(f'target_ymd={target_ymd} 에 해당하는 스케쥴이 없음')

	complete_ratio = complete_count / total_count if total_count > 0 else 1
	waiting_ratio = waiting_count / total_count if total_count > 0 else 0

	context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)
	context['ti'].xcom_push(XCOM_COMPLETE_RATIO, complete_ratio)
	context['ti'].xcom_push(XCOM_WAITING_RATIO, waiting_ratio)
	context['ti'].xcom_push(XCOM_DEADLINE, pendulum.now().add(hours=DEADLINE_HOURS).isoformat())


# def _print_settings(**context):
#     logging.info(f'''
# ---------------------------------------- Settings ----------------------------------------
# target_ymd={context["ti"].xcom_pull(key=XCOM_TARGET_YMD)}
# ------------------------------------------------------------------------------------------
# ''')


def _trigger(
		ymd: str,
		target_trigger_ratio: float,
		waiting_ratio_str: str,
		loop_interval_str: str,
		deadline_str: str,
		**context
):
	"""
	각 단계별로 처리 대기 중인 스케쥴들 확인 및 트리거
		- 각 단계에서 목표로 하는 target_trigger_ratio 에 도달할 때까지 trigger 한다.

	:param ymd: 처리할 스케쥴 날짜
	:param target_trigger_ratio: i번째 trigger 단계에서 처리해야할 스케쥴 비율
	:param waiting_ratio_str: 현재 처리 대기 중인 스케쥴 비율
	:param loop_interval_str:
	:param deadline_str:
	:param context:
	:return:
	"""

	waiting_ratio = float(waiting_ratio_str)
	loop_interval = float(loop_interval_str)
	deadline = pendulum.parse(deadline_str)

	logging.info(f'{_LOG_PREFIX} target_ymd={ymd}, target_trigger_ratio={target_trigger_ratio}, waiting_ratio={waiting_ratio}')

	while target_trigger_ratio > (1 - waiting_ratio):
		# 30초 sleep
		sleep(loop_interval)

		total_count, _, waiting_count = fetch_schedules_count(ymd)
		schedules = fetch_schedules(ymd, state=ST_READY)

		for sch in schedules:
			# 목표로 하는 target_trigger_ratio 에 도달한 경우, 다음 단계로 넘어감
			if not (target_trigger_ratio > (1 - waiting_ratio)):
				break

			# 스케쥴 state=READY && 실버 로그 적재 완료인 경우에만 trigger 한다.
			if not is_ready_to_trigger(DEFAULT_TZ, sch):
				continue

			try:
				# 스케쥴이 동적으로 생성 되기 때문에, rest api 로 spark DAG 을 trigger 한다.
				# TriggerDagRunOperator 사용 불가
				trigger_dag_with_api(
					sch,
					dag_map_fn=map_source_type_to_dag,
					trigger_by='regular',
				)
				waiting_count -= 1
				waiting_ratio = waiting_count / total_count if total_count > 0 else 0

				# 한번에 많은 요청이 몰리지 않도록 15초 정도 sleep 처리
				sleep(15)
			except Exception as ex:
				logging.exception(f'{_LOG_PREFIX} fail to trigger. ex={ex}')

		check_deadline(deadline)

	context['ti'].xcom_push(XCOM_WAITING_RATIO, waiting_ratio)


def _check_complete(
		ymd: str,
		target_complete_ratio: float,
		complete_ratio_str: str,
		loop_interval_str: str,
		deadline_str: str,
		**context
):
	"""
	각 단계별로 처리 완료된 스케쥴들 체크
		- 각 단계에서는 목표로 하는 target_complete_ratio 에 도달할 때까지 loop_interval 을 주기로 체크한다.
		- trigger 로직과는 직접적인 연관 관계가 없다.

	:param ymd: 처리할 스케쥴 날짜
	:param target_complete_ratio: i번째 complete 단계에서 완료 처리해야할 스케쥴 비율
	:param complete_ratio_str: 현재 완료된 스케쥴 비율
	:param loop_interval_str:
	:param deadline_str:
	:param context:
	:return:
	"""

	complete_ratio = float(complete_ratio_str)
	loop_interval = float(loop_interval_str)
	deadline = pendulum.parse(deadline_str)

	logging.info(f'{_LOG_PREFIX} target_ymd={ymd}, target_complete_ratio={target_complete_ratio}, complete_ratio={complete_ratio}')

	while target_complete_ratio > complete_ratio:
		# 30초 sleep
		sleep(loop_interval)

		total_count, complete_count, _ = fetch_schedules_count(ymd)
		complete_ratio = complete_count / total_count if total_count > 0 else 1

		check_deadline(deadline)

	context['ti'].xcom_push(XCOM_COMPLETE_RATIO, complete_ratio)


with DAG(
		_DAG_ID,
		description='Trigger regular Silvergrey NonRk GFP DAGs',
		tags=['trigger', 'regular', 'silvergrey_nonrk_gfp', 'daily', 'hdfs', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			'target_ymd': '',
			'trigger_poke_interval': 30,
			'complete_poke_interval': 30,
		},
		start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
		schedule_interval='0 5 * * *',
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# trigger step
	upstream = setup
	for i in range(1, 10 + 1):
		op = PythonOperator(
			task_id=f'trigger_{i * 10}',
			python_callable=_trigger,
			op_args=[
				f'{{{{ ti.xcom_pull(task_ids="{setup.task_id}", key="{XCOM_TARGET_YMD}") }}}}',
				i * 0.1,  # target_trigger_ratio
				f'{{{{ ti.xcom_pull(task_ids="{upstream.task_id}", key="{XCOM_WAITING_RATIO}") }}}}',
				'{{ params["trigger_poke_interval"] }}',
				f'{{{{ ti.xcom_pull(task_ids="{setup.task_id}", key="{XCOM_DEADLINE}") }}}}',
			],
			retries=3,
			retry_delay=timedelta(seconds=20),
		)
		upstream >> op
		upstream = op

	# complete step
	upstream = setup
	for i in range(1, 10 + 1):
		op = PythonOperator(
			task_id=f'complete_{i * 10}',
			python_callable=_check_complete,
			op_args=[
				f'{{{{ ti.xcom_pull(task_ids="{setup.task_id}", key="{XCOM_TARGET_YMD}") }}}}',
				i * 0.1,  # target_complete_ratio
				f'{{{{ ti.xcom_pull(task_ids="{upstream.task_id}", key="{XCOM_COMPLETE_RATIO}") }}}}',
				'{{ params["complete_poke_interval"] }}',
				f'{{{{ ti.xcom_pull(task_ids="{setup.task_id}", key="{XCOM_DEADLINE}") }}}}',
			],
			retries=3,
			retry_delay=timedelta(seconds=20),
		)
		upstream >> op
		upstream = op
