"""
### silvergrey schedules 수동 트리거 DAG

#### 0. 위키
- [06. 실버그레이 로그 (NonRk - GFP)](https://wiki.navercorp.com/pages/viewpage.action?pageId=835654925)

#### 1. 주기
- 없음

#### 2. 스케쥴 콜렉션
- Cms DB : BatchReportJobSchedules

#### 3. 트리거 대상 DAG
- spark_silvergrey_nonrk_gfp >> spark_silvergrey_nonrk_gfp_cms

#### 4. config
- 날짜 (required)
	- target_ymd_from, target_ymd_to : 두 날짜 사이의 모든 스케줄 실행
	- target_ymd_list : 처리할 스케쥴 날짜 리스트 (YYYYMMDD,YYYYMMDD,YYYYMMDD)
	- 모두 입력하지 않은 경우 : 에러
- report_api_type (required)
- publisher_ids (optional) : 매체 id 리스트 (oid,oid,oid,..)
- adprovider_ids (optional) : 광고공급자 id 리스트 (oid,oid,oid,..)
"""

import os
import logging
import pendulum

from time import sleep
from datetime import timedelta

from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.utils.email import send_email
from airflow.utils.state import DagRunState
from airflow.exceptions import AirflowException
from airflow.operators.python import PythonOperator

from core.utils import csv_to_id_arr, get_between_ymd_list
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD_LIST
from biz.silvergrey.silvergrey_base import \
	XCOM_REPORT_API_TYPE, XCOM_PUBLISHER_IDS, XCOM_ADPROVIDER_IDS, \
	map_source_type_to_dag
from biz.silvergrey.silvergrey_schedule_dao import COLLECTION, \
	fetch_schedules, is_ready_to_trigger, trigger_dag_with_api, \
	ST_READY

_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRIGGER-SILVERGREY-SCHEDULE-MANUAL]'


def _setup(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화 및 로깅

	:param context:
	:return:
	"""

	_init_settings(**context)
	_print_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
		- target_ymd_list / report_api_type (required)
		- publisher_ids / adprovider_ids (optional)

	:param context:
	:return:
	"""

	_set_target_ymd_list(**context)

	report_api_type = context['params'].get('report_api_type')
	publisher_ids = context['params'].get('publisher_ids')
	adprovider_ids = context['params'].get('adprovider_ids')

	if report_api_type:
		# 공백 및 양끝 콤마 제거
		publisher_ids = publisher_ids.replace(' ', '').strip(',') if publisher_ids else None
		adprovider_ids = adprovider_ids.replace(' ', '').strip(',') if adprovider_ids else None

		logging.info(f'{_LOG_PREFIX} {COLLECTION}: report_api_type={report_api_type}, publisher_ids={publisher_ids}, adprovider_ids={adprovider_ids}')

		context['ti'].xcom_push(XCOM_REPORT_API_TYPE, report_api_type)
		context['ti'].xcom_push(XCOM_PUBLISHER_IDS, publisher_ids)
		context['ti'].xcom_push(XCOM_ADPROVIDER_IDS, adprovider_ids)
	else:
		raise AirflowException(f'report_api_type required')


def _set_target_ymd_list(**context):
	"""
	config 로부터 받은 날짜 리스트를 추출하여 xcom push
	:param context:
	:return:
	"""

	ymd_from = context['params'].get('target_ymd_from')
	ymd_to = context['params'].get('target_ymd_to')
	ymd_list = context['params'].get('target_ymd_list')

	trigger_ymd_list = []

	if ymd_from and ymd_to:
		ymd_from = ymd_from.strip()
		ymd_to = ymd_to.strip()
		pendulum.from_format(ymd_from, 'YYYYMMDD')
		pendulum.from_format(ymd_to, 'YYYYMMDD')

		if ymd_from <= ymd_to:
			# trigger_ymd_list 에 추가
			trigger_ymd_list.extend(get_between_ymd_list(ymd_from, ymd_to))
			logging.info(f'{_LOG_PREFIX} ymd_from={ymd_from}, ymd_to={ymd_to}, trigger_ymd_list={trigger_ymd_list}')
		else:
			raise AirflowException(f'target_ymd_from 는 target_ymd_to 보다 작거나 같아야 합니다.'
								   f' target_ymd_from={ymd_from}, target_ymd_to={ymd_to}')
	elif ymd_list:
		ymd_list = ymd_list.replace(' ', '').strip(',')

		# trigger_ymd_list 에 추가
		trigger_ymd_list.extend(ymd_list.split(','))
		logging.info(f'{_LOG_PREFIX} trigger_ymd_list 를 이용한 실행. {trigger_ymd_list}')
	else:
		raise AirflowException(f'"config"가 기술되지 않음')

	context['ti'].xcom_push(XCOM_TARGET_YMD_LIST, trigger_ymd_list)


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Params:
	trigger_ymd_list={context["ti"].xcom_pull(key=XCOM_TARGET_YMD_LIST)}
	report_api_type={context["ti"].xcom_pull(key=XCOM_REPORT_API_TYPE)}
	adprovider_ids={context["ti"].xcom_pull(key=XCOM_ADPROVIDER_IDS)}
	publisher_ids={context["ti"].xcom_pull(key=XCOM_PUBLISHER_IDS)}
------------------------------------------------------------------------------------------
''')


def _trigger_silvergrey(**context):
	"""
	READY 상태인 스케쥴들 확인 및 트리거
		- target_ymd_list 에 있는 날짜 대상
		- report_api_type / publisher_ids / adprovider_ids 에 해당하는 스케쥴 대상

	:param context:
	:return:
	"""

	# 스킵, 실패한 스케쥴 리스트
	skipped_list = []
	failed_list = []

	target_ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)
	report_api_type = context['ti'].xcom_pull(key=XCOM_REPORT_API_TYPE)
	publisher_ids = context['ti'].xcom_pull(key=XCOM_PUBLISHER_IDS)
	adprovider_ids = context['ti'].xcom_pull(key=XCOM_ADPROVIDER_IDS)

	logging.info(f'{_LOG_PREFIX} target_ymd_list={target_ymd_list}, report_api_type={report_api_type}, '
				 f'publisher_ids={publisher_ids}, adprovider_ids={adprovider_ids}')

	schedules = fetch_schedules(
		ymd_list=target_ymd_list,
		state=ST_READY,
		report_api_type=report_api_type,
		publisher_ids=csv_to_id_arr(publisher_ids),
		adprovider_ids=csv_to_id_arr(adprovider_ids),
	)

	schedule_count = len(schedules)
	for idx, sch in enumerate(schedules):
		logging.info(f'{_LOG_PREFIX} [{idx + 1}/{schedule_count}] schedule_id={sch["_id"]}, ymd={sch["ymd"]}, reportApiType={sch["reportApiType"]}, '
					 f'adProvider_id={sch["adProvider_id"]}, publisher_id={sch["publisher_id"]}')

		# 스케쥴 state=READY && 실버 로그 적재 완료인 경우에만 trigger 한다.
		if not is_ready_to_trigger(DEFAULT_TZ, sch):
			logging.info(f'{_LOG_PREFIX} [{idx + 1}/{schedule_count}] 스킵됨. '
						 f'schedule_id={sch["_id"]}, ymd={sch["ymd"]}, reportApiType={sch["reportApiType"]}, '
						 f'adProvider_id={sch["adProvider_id"]}, publisher_id={sch["publisher_id"]}')

			skipped_list.append((str(sch["_id"]), sch["ymd"], sch["reportApiType"], str(sch["adProvider_id"]), str(sch["publisher_id"])))
			continue

		try:
			# 트리거
			dag_id, dag_run_id = trigger_dag_with_api(
				sch,
				dag_map_fn=map_source_type_to_dag,
				trigger_by='manual',
			)
			logging.info(f'{_LOG_PREFIX} [{idx + 1}/{schedule_count}] 트리거됨 (dag_id={dag_id}, dag_run_id={dag_run_id}).'
						 f'schedule_id={sch["_id"]}, ymd={sch["ymd"]}, reportApiType={sch["reportApiType"]}, '
						 f'adProvider_id={sch["adProvider_id"]}, publisher_id={sch["publisher_id"]}')

			# dag run 이 끝났는지 30초 간격으로 확인
			while True:
				sleep(30)
				dag_runs = DagRun.find(dag_id=dag_id, run_id=dag_run_id)
				if dag_runs:
					dag_run = dag_runs.pop()

					logging.info(f'{_LOG_PREFIX} {dag_id}({dag_run_id}) state = {dag_run.get_state()}')
					if dag_run.get_state() == DagRunState.SUCCESS:
						break
					elif dag_run.get_state() == DagRunState.FAILED:
						failed_list.append((str(sch["_id"]), sch["ymd"], sch["reportApiType"], str(sch["adProvider_id"]), str(sch["publisher_id"])))
						break
				else:
					failed_list.append((str(sch["_id"]), sch["ymd"], sch["reportApiType"], str(sch["adProvider_id"]), str(sch["publisher_id"])))
					logging.error(f'{_LOG_PREFIX} {dag_id}({dag_run_id}) 존재하지 않음')
					break

		except Exception as ex:
			logging.exception(f'{_LOG_PREFIX} fail to trigger. ex={ex}')

	context['ti'].xcom_push('skipped_list', skipped_list)
	context['ti'].xcom_push('failed_list', failed_list)


def _alert(**context):
	"""
	스킵 또는 실패된 리스트 알림
	:param context:
	:return:
	"""

	skipped_list = context['ti'].xcom_pull(key='skipped_list')
	skipped_msg = f'skipped {len(skipped_list)} 건 <br/>'
	for item in skipped_list:
		skipped_msg += f'- _id={item[0]}, ymd={item[1]}, reportApiType={item[2]}, adProvider_id={item[3]}, publisher_id={item[4]}<br/>'

	failed_list = context['ti'].xcom_pull(key='failed_list')
	failed_msg = f'failed {len(failed_list)} 건 <br/>'
	for item in failed_list:
		failed_msg += f'- _id={item[0]}, ymd={item[1]}, reportApiType={item[2]}, adProvider_id={item[3]}, publisher_id={item[4]}<br/>'

	if skipped_list or failed_list:
		title = f'[silvergrey 수동 처리] skipped {len(skipped_list)} 건, failed {len(failed_list)} 건'
		body = f'{skipped_msg}<br/>{failed_msg}<br/>'
		send_email(ALERT_EMAIL_ADDRESSES, title, body)


with DAG(
		_DAG_ID,
		description='Trigger manual Silvergrey NonRk GFP DAGs',
		tags=['trigger', 'manual', 'silvergrey_nonrk_gfp', 'hdfs', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			'target_ymd_from': '',
			'target_ymd_to': '',
			'target_ymd_list': '',
			'report_api_type': '',
			'publisher_ids': '',
			'adprovider_ids': '',
		},
		start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	trigger_silvergrey = PythonOperator(
		task_id='trigger_silvergrey',
		python_callable=_trigger_silvergrey,
		retries=3,
		retry_delay=timedelta(seconds=20),
	)

	# 스킵 또는 실패된 리스트 알림
	alert = PythonOperator(
		task_id='alert',
		python_callable=_alert,
	)

	setup >> trigger_silvergrey >> alert
