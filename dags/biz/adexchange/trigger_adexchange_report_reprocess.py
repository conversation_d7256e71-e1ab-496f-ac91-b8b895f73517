"""
### Ad Exchange 리포트 재처리 트리거 DAG

#### 위키
- [20-2. Ad Exchange 리포트 생성 재처리](https://wiki.navercorp.com/pages/viewpage.action?pageId=2514700693)

#### 주기 및 스케줄
- 매일 06-15시 1시간 간격
- Data DB : Jobs
  - 가장 과거 날짜가 끝나기 전까지는 다음 날짜를 처리하지 못한다.
- 본 DAG 에서 집계와 imply 적재를 task 단위로 분리하여 트리거 하므로 max_active_runs 를 따로 설정하지 않는다. (Default= 16)
  - max_active_runs=1 이라면 trigger_spark 의 실행 종료를 기다리느라 다른 DAGRun 에서 실행되어야 할 trigger_imply 이 지연될 수 있다.
  - max_active_runs 를 늘리는 대신 각 task 의 동시실행은 막아야 하므로 각 task 에 max_active_tis_per_dag=1 로 설정한다.
  - 다만 본 DAG 이 1시간 마다 실행되고, setup 단계에서 가장 오래된 1건만 조회함(그것이 실행중이라면 스킵)을 고려하면 위 설정이 적용될 가능성은 적어보인다.

#### 트리거 대상 DAG
- spark_adexchange_report

#### config
- 없음
"""

import logging
import os

import pendulum
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import BranchPythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.types import DagRunType

from biz.adexchange.imply_adexchange_report import _DAG_ID as IMPLY_DAG_ID
from biz.adexchange.spark_adexchange_report import _DAG_ID as SPARK_DAG_ID
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD
from core.dao import job_dao

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [TRG-ADX-REPORT-REPROCESS]'

_XCOM_SPARK_YMD = 'spark_ymd'
_XCOM_IMPLY_YMD = 'imply_ymd'


def _setup(**context):
	"""
	가장 오래된 실패 이력 1건만 가져와서 target_ymd 설정
	대상이 되는 Job 은 spark, imply 두 종류이며 다른 종류의 Job 끼리는 동시에 실행 가능

	단, 같은 ymd 의 spark, imply 이 Jobs 컬렉션에 동시에 존재하는 경우 순차처리를 보장해야 하는데
	이러한 상황은 하나의 job 이 이미 컬렉션에 있을 때 다른 하나를 수동 실행할 경우만 생긴다.

	reprocess 는 수동 실행한 job 은 재처리 대상으로 삼지 않으므로 순차처리에 대한 예외처리는 없어도 된다.
	또한 통상적인 정규/수동 실행의 경우 spark -> imply 가 순차처리되고, 컬렉션 내에서 같은 ymd 로 존재하지 않는다.

	:param context:
	:return:
	"""
	spark_job = job_dao.get_job({'type': SPARK_DAG_ID, 'manual': 0})
	imply_job = job_dao.get_job({'type': IMPLY_DAG_ID, 'manual': 0})

	# job 이 없는 경우(None) 는 필터
	total_jobs = list(filter(None, [spark_job, imply_job]))

	if len(total_jobs):
		target_job_types = []
		for job in total_jobs:
			job_info = job['type'] + ':' + job['datetime']
			if job['retryCnt'] >= job_dao.MAX_RETRY_CNT:
				# monitor_jobs 에서 알림 메일 발송하기 때문에, 별도 처리 하지 않음
				logging.warning(
					f'{_LOG_PREFIX} {job_info}의 retryCnt={job["retryCnt"]}. 최대처리횟수({job_dao.MAX_RETRY_CNT})에 도달해서 스킵.')
			elif job['running'] == 1:
				logging.warning(
					f'{_LOG_PREFIX} {job_info} 이미 처리 중이므로 스킵. job={job}')
			elif 'spark' in job['type']:
				target_job_types.append(SPARK_DAG_ID)
				context['ti'].xcom_push(_XCOM_SPARK_YMD, job['datetime'])
			else:
				target_job_types.append(IMPLY_DAG_ID)
				context['ti'].xcom_push(_XCOM_IMPLY_YMD, job['datetime'])

		return [f'trigger_{job_type}' for job_type in target_job_types]

	else:
		logging.info(f'{_LOG_PREFIX} 처리할 job 없음')

	return []


with DAG(
		_DAG_ID,
		description='Trigger reprocess ad exchange report DAG',
		tags=['trigger', 'reprocess', 'adexchange', 'adx', 'daily', 'hdfs', 'imply', 'druid', 'ins.cho'],
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2024, 7, 11, tz=DEFAULT_TZ),
		schedule_interval='0 06-15 * * *',  # 매일 06-15시 1시간 간격
		catchup=False
) as dag:
	dag.doc_md = __doc__

	# 처리해야 할 job이 있는 작업의 task 만 브랜치로 진행
	# Jobs 에서 spark/imply 작업 별 가장 오래된 실패 이력의 job 만 추출
	setup = BranchPythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	trigger_spark_aggregator = TriggerDagRunOperator(
		trigger_dag_id=SPARK_DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{SPARK_DAG_ID}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=True,
		poke_interval=60,
		reset_dag_run=True,
		max_active_tis_per_dag=1,
		conf={
			XCOM_TARGET_YMD: f'{{{{ ti.xcom_pull(key="{_XCOM_SPARK_YMD}") }}}}',
		}
	)

	trigger_imply_ingestion = TriggerDagRunOperator(
		trigger_dag_id=IMPLY_DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{IMPLY_DAG_ID}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=True,
		poke_interval=60,
		reset_dag_run=True,
		max_active_tis_per_dag=1,
		conf={
			XCOM_TARGET_YMD: f'{{{{ ti.xcom_pull(key="{_XCOM_IMPLY_YMD}") }}}}',
		}
	)

	setup >> [trigger_spark_aggregator, trigger_imply_ingestion]
