"""
#### 위키
- [20-5. Ad Exchange 리포트 imply ingestion](https://wiki.navercorp.com/pages/viewpage.action?pageId=2544561853)

#### 개요
- 익스체인지 리포트를 imply 를 통해 제공하기 위해 ingestion 을 수행
- imply ingestion 만 수동처리가 필요할 경우, trigger_manual 에서 imply_only=true 로 실행

#### 주기
- spark_adexchange_report 의 실행에 따름

#### config
- target_ymd: YYYYMMDD
"""
import logging
import os
from datetime import timedelta
from functools import partial

import pendulum
from airflow import AirflowException
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator, ShortCircuitOperator
from airflow.sensors.python import PythonSensor

from core import utils, c3
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD, DRUID_OVERLORD, C3_PATH_PREFIX, C3_USER, \
	C3_WEBHDFS_URL, C3_PASSWORD, PROFILE_REAL, PROFILE_STAGE, PROFILE
from core.dao import job_dao

_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [ADX-REPORT-IMPLY]'

_XCOM_DRUID_LEADER_URL = 'druid_leader_url'
_XCOM_INGESTION_SPEC = 'ingestion_spec'
_XCOM_IMPLY_TASK_ID = 'imply_task_id'

_GET_LEADER_ENDPOINT = '/druid/indexer/v1/leader'
_SUBMIT_TASK_ENDPOINT = '/druid/indexer/v1/task'
_GET_TASK_STATUS_ENDPOINT = '/druid/indexer/v1/task/{taskId}/status'

_REPORT_TYPE = 'adexchange'
_HDFS_HTTP_URL = C3_WEBHDFS_URL + C3_PATH_PREFIX + f'/{_REPORT_TYPE}' + '/{dt_path}?op=OPEN'


def _setup(**context):
	"""
	dag 실행에 필요한 파라미터 세팅

	:param context:
	:return:
	"""
	_set_target_ymd(**context)


def _set_target_ymd(**context):
	"""
	ingest 하기 위해 입력한 대상 날짜 세팅

	:param context:
	:return:
	"""
	target_ymd = context['params'].get(XCOM_TARGET_YMD)
	if target_ymd:
		target_ymd = str(target_ymd).strip()
		pendulum.from_format(target_ymd, 'YYYYMMDD')
		logging.info(f'{_LOG_PREFIX} Using target_ymd from "params"={target_ymd}')
	else:
		logging.info(f'{_LOG_PREFIX} params does not exist. target_ymd is empty')
		raise AirflowException(f'target_ymd is not defined. target_ymd={target_ymd}')

	context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)


def _set_druid_leader_url(**context):
	"""
	드루이드의 leader Overlord url 을 조회하여 세팅 (달라질 수 있기 때문에 매번 실행)

	:param context:
	:return:
	"""
	for druid_overlord in DRUID_OVERLORD:
		res = utils.request(url=druid_overlord + _GET_LEADER_ENDPOINT)
		if res.startswith('http://'):
			context['ti'].xcom_push(key=_XCOM_DRUID_LEADER_URL, value=res)
			return True
	return False


def _is_exists_success_file(**context):
	"""
	ad exchange report 경로에 _SUCCESS 가 있는지 확인
		- /data/log/gfp/adexchange/yyyy/mm/dd/_SUCCESS
	:param context:
	:return:
	"""

	ymd = pendulum.from_format(context['ti'].xcom_pull(key=XCOM_TARGET_YMD), 'YYYYMMDD')
	success_file_path = f'{C3_PATH_PREFIX}/adexchange/{ymd.format("YYYY/MM/DD")}/_SUCCESS'
	logging.info(f'{_LOG_PREFIX} success_file_path= {success_file_path}')
	if not c3.exists(success_file_path):
		return False
	return True


def _prepare_ingestion_spec(**context):
	"""
	druid api 로 전달할 ingestion spec 을 작성

	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	year, month, date = target_ymd[0:4], target_ymd[4:6], target_ymd[6:8]

	start = pendulum.date(int(year), int(month), int(date))
	end = start.add(days=1)

	dt_file_path = f'{year}/{month}/{date}/{target_ymd}_{_REPORT_TYPE}.csv'
	input_source_uri = _HDFS_HTTP_URL.format(dt_path=dt_file_path)
	logging.info(f'{_LOG_PREFIX} 익스체인지 리포트 경로: {input_source_uri}')

	ingestion_spec = {
		# 필드 의미 참고:
		# https://druid.apache.org/docs/latest/ingestion/ingestion-spec
		"type": "index_parallel",
		"spec": {
			"ioConfig": {
				"type": "index_parallel",
				"inputSource": {
					# "https://knox-pan.c3s.navercorp.com/gateway/pg07-auth-basic/webhdfs/v1/user/gfp-data/monthly/2023/11/202311_adunit_aur.csv?op=OPEN"
					"type": "http",
					"uris": [input_source_uri],
					"httpAuthenticationUsername": C3_USER,
					"httpAuthenticationPassword": C3_PASSWORD,
				},
				"inputFormat": {
					"type": "csv",
					"findColumnsFromHeader": False,
					"skipHeaderRows": 1,
					"columns": [
						## input_source_uri 의 csv file 헤더와 순서를 맞춰야 함
						# dimensions
						'datetime', 'publisher', 'service', 'adunit', 'adprovider',
						'place', 'place_channel_type', 'place_product_type', 'place_creative_type', 'bidding_group',
						'app_bundle', 'site_domain', 'response_creative_type', 'device_os', 'country',
						# metrics
						'adprovider_requests', 'adprovider_responses', 'gfp_filled_requests', 'gfp_impressions',
						'gfp_viewable_impressions', 'gfp_estimated_impressions', 'gfp_clicks', 'gfp_ctr',
						'gfp_estimated_usd_revenue', 'gfp_estimated_krw_revenue', 'gfp_estimated_usd_net_revenue',
						'gfp_estimated_krw_net_revenue', 'gfp_estimated_usd_cpm', 'gfp_estimated_krw_cpm',
						'gfp_estimated_usd_cpc', 'gfp_estimated_krw_cpc', 'gfp_estimated_usd_net_cpm',
						'gfp_estimated_krw_net_cpm', 'gfp_estimated_usd_net_cpc', 'gfp_estimated_krw_net_cpc'
					]
				},
				"appendToExisting": False,
				"dropExisting": True,  # true 이려면 appendToExisting 을 false 로 해야함.
			},
			"tuningConfig": {
				"type": "index_parallel",
				"partitionsSpec": {
					"type": "dynamic"
				},
				"logParseExceptions": True
			},
			"dataSchema": {
				"dataSource": f"gfp_{_REPORT_TYPE}",
				"timestampSpec": {
					"column": "datetime",
					"format": "yyyyMMddHH"
					# https://joda-time.sourceforge.net/apidocs/org/joda/time/format/DateTimeFormat.html
				},
				"granularitySpec": {
					"segmentGranularity": "day",
					"queryGranularity": "hour",
					"intervals": [f"{start.year}-{start.month}-{start.day}/{end.year}-{end.month}-{end.day}"],
					"rollup": False,
				},
				"dimensionsSpec": {
					"useSchemaDiscovery": True,
					"includeAllDimensions": True,
					"dimensionExclusions": []
				}
			}
		}
	}

	logging.info(f'{_LOG_PREFIX} ingestion spec:\n{utils.prettyStr(ingestion_spec)}')

	context['ti'].xcom_push(_XCOM_INGESTION_SPEC, ingestion_spec)


def _submit_ingestion_spec_to_druid(**context):
	"""
	ingestion 실행

	:param context:
	:return:
	"""
	if PROFILE not in [PROFILE_REAL, PROFILE_STAGE]:
		logging.info(f'imply ingestion 은 리얼/스테이지 환경에서만 실행됩니다. ( PROFILE= {PROFILE} )')
		# downstream task 스킵 및 job 삭제
		_clean_up_job(True, context)
		return False

	submit_task_url = context['ti'].xcom_pull(key=_XCOM_DRUID_LEADER_URL) + _SUBMIT_TASK_ENDPOINT
	ingestion_spec = context['ti'].xcom_pull(key=_XCOM_INGESTION_SPEC)
	res = utils.request(
		url=submit_task_url,
		method='POST',
		headers={'Content-Type': 'application/json'},
		json=ingestion_spec
	)

	ingested_task_id = res.get('task')
	context['ti'].xcom_push(_XCOM_IMPLY_TASK_ID, ingested_task_id)
	return True


def _wait_for_ingestion_complete(**context):
	"""
	ingestion 된 task 상태를 조회하여 정상적으로 실행되었는지 확인

	:return:
	"""
	ingested_task_id = context['ti'].xcom_pull(key=_XCOM_IMPLY_TASK_ID)
	druid_url = context['ti'].xcom_pull(key=_XCOM_DRUID_LEADER_URL)
	get_status_url = druid_url + _GET_TASK_STATUS_ENDPOINT.format(taskId=ingested_task_id)

	res = utils.request(url=get_status_url)
	task_status = res.get('status').get('status')

	if task_status == 'SUCCESS':
		logging.info(f'ingestion 완료')
		return True
	elif task_status == 'FAILED':
		raise AirflowException(f"ingestion 실패\n{res.get('status').get('errorMsg')}")
	else:
		logging.info(f'ingestion 진행중.. ( task_status= {task_status} )')
		return False


def _clean_up_job(is_success: bool, context: dict):
	"""
	실패했으므로 retryCnt 증가시키고 running=0 설정
	:param context:
	:return:
	"""

	job_dao.clean_up(_DAG_ID, context['ti'].xcom_pull(key=XCOM_TARGET_YMD), is_success)


with DAG(
		_DAG_ID,
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		schedule_interval=None,
		start_date=pendulum.datetime(2024, 7, 11, tz=DEFAULT_TZ),
		tags=['imply', 'ingest', 'druid', 'adexchange', 'adx', 'imply', 'druid', 'ins.cho'],
		params={
			XCOM_TARGET_YMD: '',
		},
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup
	)

	# 중복 실행 여부 확인
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=job_dao.check_dup_run,
		op_args=[
			_DAG_ID,
			XCOM_TARGET_YMD
		]
	)

	# 해당 시간대의 Jobs.running=1 로 추가
	upsert_job = PythonOperator(
		task_id='upsert_job',
		python_callable=job_dao.upsert_job,
		op_args=[
			_DAG_ID,
			XCOM_TARGET_YMD
		]
	)

	# _SUCCESS 파일 확인
	# 10분마다 재시도, 1시간 동안
	wait_for_report_success = PythonSensor(
		task_id='wait_for_report_success',
		python_callable=_is_exists_success_file,
		mode='reschedule',
		poke_interval=60 * 10,
		timeout=60 * 60 * 1,
		retries=2,
		on_failure_callback=partial(_clean_up_job, False),
	)

	# api 를 통해 druid leader url 을 세팅
	# 1분마다 재시도, 10분 동안
	wait_for_setting_leader = PythonSensor(
		task_id='wait_for_setting_leader',
		python_callable=_set_druid_leader_url,
		mode='reschedule',
		poke_interval=60 * 1,
		timeout=60 * 10,
		retries=2,
		on_failure_callback=partial(_clean_up_job, False),
	)

	# ingestion spec 작성
	prepare_ingestion_spec = PythonOperator(
		task_id='prepare_ingestion_spec',
		python_callable=_prepare_ingestion_spec,
		on_failure_callback=partial(_clean_up_job, False),
	)

	# profile 에 따라 ingestion 요청 또는 종료
	submit_ingestion_spec = ShortCircuitOperator(
		task_id=f'submit_ingestion',
		python_callable=_submit_ingestion_spec_to_druid,
		execution_timeout=timedelta(minutes=30),
		on_failure_callback=partial(_clean_up_job, False),
	)

	# ingestion 완료 대기
	# 5분마다 재시도, 30분 동안
	# (실패 시 총) 3번의 요청, 각 timeout=5분 이므로 최대 약 15분
	wait_for_ingestion_complete = PythonSensor(
		task_id='wait_for_ingestion_complete',
		python_callable=_wait_for_ingestion_complete,
		mode='reschedule',
		poke_interval=60 * 5,
		timeout=60 * 30,
		retries=2,
		on_failure_callback=partial(_clean_up_job, False),
		on_success_callback=partial(_clean_up_job, True),
	)

	setup >> check_dup_run >> upsert_job >> wait_for_report_success >> wait_for_setting_leader \
	>> prepare_ingestion_spec >> submit_ingestion_spec >> wait_for_ingestion_complete
