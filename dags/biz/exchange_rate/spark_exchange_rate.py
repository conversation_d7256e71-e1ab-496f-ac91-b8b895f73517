"""
#### 위키
- [08. 평균 환율 집계](https://wiki.navercorp.com/pages/viewpage.action?pageId=835655020)

#### 개요
- GFP NonRk AP 리포트 연동 시 필요한 환율 정보를 neon 을 통해 받고, 평균 환율 계산

#### 주기
- 매일 17시
- 실패시, 30분 간격으로 3번 재시도 (총 4번 실행)

#### config
- target_ymd: YYYYMMDD
"""
import decimal
import logging
import os
from datetime import timedelta
from urllib.parse import urlparse

import pendulum
import requests
import tenacity
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.providers.mongo.hooks.mongo import MongoHook
from bson.decimal128 import create_decimal128_context, Decimal128

from core import utils
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, CONN_MONGO_FOR_DATA, XCOM_TARGET_YMD, POOL_SPARK, \
    XCOM_TARGET_YM, SPARKLING_APP_JAR_PATH, SPARKLING_IMAGE, SPARKLING_YARN_ARCHIVE
from core.dao.environment_dao import get_environment_value_by_name
from core.spark_pool import POOL_AP_SLOT_TRIVIAL_2
from core.spark_submit_op import invoke_job_with_args
from core.spark_task_group import create_task_group

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = ".......... [SYNC-EXCHG-RATE]"

_XCOM_TARGET_URL = 'target_url'

_TARGET_CURRENCY = ['KRW', 'JPY', 'USD']

_REQ_URL = '{endpoint}/nfi/InterfaceInfoBO/getExchangeRateList'
_REQ_PARAMS = {
    'EMPLOYEE_NO': 'AD_REQUEST_USER',
    'exchangeType': 'COMPANY',
    'exchangeDateStr': "'20220301'",
    'currencyCdFrom': 'KRW',
    'currencyCdTo': 'USD',
}

_RATES_COLLECTION = 'ExchangeRates'
_RATES_AVG_COLLECTION = 'ExchangeRateAverages'

# Spark
_SPARK_APP_CLASS = 'com.navercorp.gfp.biz.exchangerate.ExchangeRateAggregator'
_SPARK_SUBMIT_OPTIONS = """
    --master yarn
    --deploy-mode cluster
    --queue root.users.gfp
    --num-executors 1
    --executor-cores 1
    --executor-memory 500m
    --conf spark.driver.extraJavaOptions=-XX:+UseG1GC
    --conf spark.executor.extraJavaOptions=-XX:+UseG1GC
    --conf spark.eventLog.enabled=true
    --conf spark.sql.parquet.mergeSchema=true
    --conf spark.sql.caseSensitive=true
    --conf spark.serializer=org.apache.spark.serializer.KryoSerializer
    --conf spark.yarn.archive={yarn_archive}
    --conf spark.yarn.submit.waitAppCompletion=false
    """.format(yarn_archive=SPARKLING_YARN_ARCHIVE).split()


def _setup(**context):
    """
    DAG 실행에 필요한 환경 설정

    :param context:
    :return:
    """
    ymd = context['params'].get('target_ymd')
    if ymd:
        pendulum.from_format(ymd, 'YYYYMMDD')
    else:
        ymd = pendulum.today(DEFAULT_TZ).format('YYYYMMDD')

    (url, verify) = _get_url()
    context['ti'].xcom_push(key=XCOM_TARGET_YM, value=ymd[:-2])
    context['ti'].xcom_push(key=XCOM_TARGET_YMD, value=ymd)
    context['ti'].xcom_push(key=_XCOM_TARGET_URL, value=(url, verify))


def _get_url():
    """
    환율 정보를 요청할 url 을 구한다.

    nat_ip 가 있다면 해당 ip 와 endpoint 의 포트를,
    없다면 endpoint 로 반환

    :return:
    """
    exchange_config = get_environment_value_by_name('neon-config').get('exchg_rate')
    url_endpoint = exchange_config.get('endpoint')
    nat_ip = exchange_config.get('nat_ip')

    if not nat_ip:
        return url_endpoint, True

    # urlparse 은 url 형태의 string 을 아래와 같은 형태로 분리하여 반환함
    # <scheme>://<netloc>/<path>;<params>?<query>#<fragment>
    # 이 외에도 username, password, hostname, port 등이 있지만 존재하지 않는다면 None
    parsed_endpoint = urlparse(url_endpoint)
    port = parsed_endpoint.port

    # 포트가 있다면 nat_ip 뒤에 명시함
    netloc = f'{nat_ip}:{port}' if port else nat_ip

    # 위의 netloc 부분을 nat_ip 정보로 대체하여 반환
    return parsed_endpoint._replace(netloc=netloc).geturl(), False


def _request_to_url(url: str, params: dict, verify: bool):
    """
    주어진 Url 에 대해 요청을 보냄

    :param url:
    :return:
    """

    def _call_and_check():
        response = requests.get(url, params=params, verify=verify, timeout=10)
        response.raise_for_status()
        return response

    retry_obj = tenacity.Retrying(
        wait=tenacity.wait.wait_fixed(wait=3),
        stop=tenacity.stop.stop_after_attempt(3),
        retry=tenacity.retry_if_exception_type(requests.exceptions.HTTPError)
    )

    res = retry_obj(_call_and_check)
    logging.info(f'{_LOG_PREFIX} retry stat: attempts={retry_obj.statistics["attempt_number"]}')
    logging.info(f'{_LOG_PREFIX} status code: {res.status_code}')
    logging.info(f'{_LOG_PREFIX} response:\n{res.text}')

    jres = res.json()
    if jres.get('SUCCESS') != 'Y' or jres.get('RESULT') is None or len(jres['RESULT']) < 1:
        return None

    record = jres['RESULT'][0]
    with decimal.localcontext(create_decimal128_context()) as ctx:
        rate = Decimal128(
            ctx.create_decimal(record['exchangeRate']['json.expansion@value'])
        )

    return {
        'currencyCdFrom': record['currencyCdFrom'],
        'currencyCdTo': record['currencyCdTo'],
        'exchangeRate': rate,
    }


def _req_and_save_exchange_rate(**context):
    """
    응답받은 환율정보를 컬렉션에 적재

    :param context:
    :return:
    """
    target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
    url_info = context['ti'].xcom_pull(key=_XCOM_TARGET_URL)
    endpoint, verify = url_info[0], url_info[1]
    logging.info(f'{_LOG_PREFIX} ymd={target_ymd}, endpoint={endpoint}, currencies={_TARGET_CURRENCY}')

    url = _REQ_URL.format(endpoint=endpoint)
    params = _REQ_PARAMS.copy()
    params['exchangeDateStr'] = f"'{target_ymd}'"

    exchange_rates = []
    for from_c in _TARGET_CURRENCY:
        for to_c in _TARGET_CURRENCY:
            if from_c == to_c:
                continue
            params['currencyCdFrom'] = from_c
            params['currencyCdTo'] = to_c

            # from - to 환율 정보를 추가해 요청
            res = _request_to_url(url, params, verify)
            if res:
                logging.info(f'{_LOG_PREFIX} {from_c} -> {to_c}: {res["exchangeRate"]}')
                res['date'] = target_ymd
                # {
                #   'date': 'YYYYMMDD',
                #   'currencyCdFrom': 'KRW',
                #   'currencyCdTo': 'USD'
                #   'exchangeRate': Decimal128('1100.05040334')
                # }
                exchange_rates.append(res)
            else:
                logging.info(f'{_LOG_PREFIX} {from_c} -> {to_c}: None')
                logging.info(f'{_LOG_PREFIX} aggregate avg exchange rate with yesterday_ymd')
                yesterday_ymd = pendulum.yesterday(tz=DEFAULT_TZ).format('YYYYMMDD')
                context['ti'].xcom_push(key=XCOM_TARGET_YMD, value=yesterday_ymd)
                return

    created_at = pendulum.now(DEFAULT_TZ)
    modified_cnt = 0
    upsert_cnt = 0
    with MongoHook(CONN_MONGO_FOR_DATA) as hook:
        coll = hook.get_conn().get_database()[_RATES_COLLECTION]
        for rate_spec in exchange_rates:
            update_res = coll.update_one(
                {k: v for (k, v) in rate_spec.items() if k != 'exchangeRate'},
                {
                    '$set': {
                        'createdAt': created_at,
                        'exchangeRate': rate_spec['exchangeRate'],
                    }
                },
                upsert=True,
            )
            modified_cnt += update_res.modified_count
            if update_res.upserted_id:
                upsert_cnt += 1

    logging.info(f'{_LOG_PREFIX} Done: modified={modified_cnt}, upserted={upsert_cnt}')


def _get_app_args(context: dict):
    """
    spark-submit 시, 넘겨줄 파라미터 정보

    :param context:
    :return:
    """
    return [context["ti"].xcom_pull(key=XCOM_TARGET_YMD)]


with DAG(
        _DAG_ID,
        description='GFP NonRk AP 리포트 연동에 사용되는 환율 정보를 neon 을 통해 받고, 평균 환율 계산',
        default_args={
            'owner': 'ins.cho',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
            'retries': 3,
            'retry_delay': timedelta(minutes=30),
        },
        start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
        schedule_interval='0 17 * * *',
        tags=['exchange-rate', 'ap-report', 'neon', 'ins.cho'],
        catchup=False,
        params={
            'target_ymd': '',
        },
) as dag:
    dag.doc_md = __doc__

    setup = PythonOperator(
        task_id='setup',
        python_callable=_setup,
    )

    req_and_save_exchange_rate = PythonOperator(
        task_id='reqeust_and_save_exchange_rate',
        python_callable=_req_and_save_exchange_rate,
    )

    task_group_id = 'sync_exchange_rate'
    run_spark_app = create_task_group(
        task_group_id=task_group_id,
        tooltip=_SPARK_APP_CLASS,
        spark_pool=POOL_SPARK,
        pool_slot=POOL_AP_SLOT_TRIVIAL_2,
        tz=DEFAULT_TZ,
        summary_history_kwargs={
            'aggregator_name': f'{_SPARK_APP_CLASS}',
            'dt': '{{ ti.xcom_pull(key="target_ym") }}',
            'dag_id': _DAG_ID,
            'dag_run_id': '{{ run_id }}',
            'dag_run_url': f'{utils.get_dag_run_url(_DAG_ID, "{{ run_id }}")}'
        },
        invoke_job_callable=invoke_job_with_args,
        invoke_job_kwargs={
            'task_group_id': task_group_id,
            'image': SPARKLING_IMAGE,
            'app_jar': SPARKLING_APP_JAR_PATH,
            'app_class': _SPARK_APP_CLASS,
            'app_args_fn': _get_app_args,
            'spark_submit_options': _SPARK_SUBMIT_OPTIONS,
            'tz': DEFAULT_TZ,
            'alt_datetime': '{{ ti.xcom_pull(key="target_ym") }}',
        },
    )

    setup >> req_and_save_exchange_rate >> run_spark_app
