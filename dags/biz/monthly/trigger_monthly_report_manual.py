"""
### 월별 보고서 생성 수동 처리

#### 위키
- [05-3. 월별 보고서 생성 수동 처리](https://wiki.navercorp.com/pages/viewpage.action?pageId=1402969276)

#### 개요
- 수동 실행 (스케줄 되지 않음)
- config로 받은 시간대를 Jobs 컬렉션에 추가
- Jobs에서 시간대순으로 하나씩 꺼내어 spark_monthly_report DAG 트리거시킴
- 성공하면 Jobs 다큐먼트를 삭제하고, 실패하면 retryCnt를 증가시킴

#### config
- from_target_ym, to_target_ym : 두 값 사이(edge 포함)의 모든 datetime 을 대상으로 하는 스케줄 실행
    - format : "YYYYMM"
- target_ym_list : 입력한 값들에 속하는 datetime 을 대상으로 하는 스케줄 실행
    - comma로 구분된 ym 를 공백없이 나열 (정렬 필요 없음)
    - format : "YYYYMM,YYYYMM,YYYYMM, .. "
- 세 값 모두 입력한 경우 : from/to 우선
- 모두 입력하지 않은 경우 : 에러
"""
import logging
import os
from datetime import timedelta
from time import sleep

import pendulum
from airflow import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType

from biz.monthly.spark_monthly_report import _DAG_ID as SPARK_DAG_ID
from core.airflow_api import trigger_dagrun
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ
from core.dao import job_dao
from core.utils import get_between_ym_list

# DAG 기본정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRG-MONTHLY-RPT-MANUAL]'

_XCOM_TARGET_YM_LIST = 'target_ym_list'


def _setup(**context):
    _set_ym_list(**context)


def _set_ym_list(**context):
    """
    config를 이용해 재실행할 대상월(ym) 설정
    :param context:
    :return:
    """
    from_ym = context['params'].get('from_target_ym')
    to_ym = context['params'].get('to_target_ym')
    target_ym_list: str = context['params'].get('target_ym_list')

    ym_list = []

    if from_ym or to_ym:
        from_ym = from_ym.strip()
        pendulum.from_format(from_ym, 'YYYYMM')
        if to_ym:
            to_ym = to_ym.strip()
            pendulum.from_format(to_ym, 'YYYYMM')
            if from_ym > to_ym:
                raise AirflowException(f'from_target_ym은 to_target_ym보다 작거나 같아야 합니다.'
                                       f' from_ym={from_ym} to_ym={to_ym}')
            else:
                ym_list = get_between_ym_list(from_ym, to_ym)
                logging.info(f'{_LOG_PREFIX} from_target_ym / to_target_ym를 이용한 실행'
                             f' from_ym={from_ym} to_ym={to_ym} ym_list={ym_list}')
        else:
            raise AirflowException(f'to_target_ym이 기술되지 않음. from_ym={from_ym}')
    elif target_ym_list:
        target_ym_list = target_ym_list.strip(', ')
        ym_list = target_ym_list.split(',')
        logging.info(f'{_LOG_PREFIX} target_ym_list를 이용한 실행. {ym_list}')
    else:
        raise AirflowException(f'"config"가 기술되지 않음')

    # xcom에 push
    context['ti'].xcom_push(_XCOM_TARGET_YM_LIST, ym_list)


def _trigger_monthly_report(**context):
    """
        config로부터 받은 시간대에 대해 순차적으로 spark_goldsmith 트리거시킴
        스킵되거나, 실패된 리스트는 따로 정리
        :param context:
        :return:
        """
    skipped_list = []
    failed_list = []

    # 대상 월 목록을 돌면서
    ym_list = context['ti'].xcom_pull(key=_XCOM_TARGET_YM_LIST)
    ym_list_len = len(ym_list)
    for idx, ym in enumerate(ym_list):
        # 이미 job이 있는지
        job = job_dao.get_job({'type': SPARK_DAG_ID, 'datetime': ym})
        if job:
            if job['manual'] == 0:
                # 자동
                skipped_list.append((ym, 'manual=0. regular/reprocess dag에 의해 처리예정이거나 처리중이므로 스킵'))
                logging.warning(
                    f'{_LOG_PREFIX} manual=0. regular/reprocess dag에 의해 처리예정이거나 처리중이므로 스킵({idx + 1}/{ym_list_len}). {ym}')
                can_run = False
            else:
                # 수동
                # 이미 실행중인지 확인
                if job['running'] == 1:
                    skipped_list.append((ym, '이미 처리 중'))
                    logging.warning(f'{_LOG_PREFIX} 이미 처리 중이므로 스킵({idx + 1}/{ym_list_len}). {ym}')
                    can_run = False
                else:
                    can_run = True
        else:
            can_run = True

        if can_run:
            logging.warning(f'{_LOG_PREFIX} 진행중({idx + 1}/{ym_list_len}) {ym}')

            # 해당 시간대의 월간 보고서 생성 요청
            dag_run_id = trigger_dagrun(SPARK_DAG_ID, pendulum.now(DEFAULT_TZ), {'target_ym': ym}, DagRunType.MANUAL)
            logging.info(f'{_LOG_PREFIX} {SPARK_DAG_ID} 트리거시킴'
                         f' ym={ym} spark_dag_id={SPARK_DAG_ID} dag_run_id={dag_run_id}')

            # dag이 끝났는지 주기적으로 확인
            while True:
                sleep(60)
                dag_runs = DagRun.find(dag_id=SPARK_DAG_ID, run_id=dag_run_id)
                if dag_runs:
                    dag_run: DagRun = dag_runs.pop()
                    if dag_run.get_state() == DagRunState.SUCCESS:
                        break
                    elif dag_run.get_state() == DagRunState.FAILED:
                        failed_list.append((ym, f'dag_run_id={dag_run_id}'))
                        break
                else:
                    failed_list.append((ym, f'dag_run_id={dag_run_id} 트리거 시켰으나, 존재하지 않음)'))
                    logging.info(f'{_LOG_PREFIX} {SPARK_DAG_ID}({dag_run_id}) 트리거 시켰으나, 존재하지 않음')
                    break

    logging.info(f'{_LOG_PREFIX} 완료')

    context['ti'].xcom_push('skipped_list', skipped_list)
    context['ti'].xcom_push('failed_list', failed_list)


def _conclude(**context):
    """
    스킵 또는 실패된 리스트 알림
    수동 처리에서 실패한 건들은 trigger_xx_reprocess에서 맡게 되므로 본 DAG에서는 알림만.
    :param context:
    :return:
    """
    skipped_msg = '[skipped_list]<br/>'
    skipped_list = context['ti'].xcom_pull(key='skipped_list')
    for item in skipped_list:
        skipped_msg += f'&nbsp;&nbsp;&nbsp;&nbsp; ym:{item[0]} reason:{item[1]}<br/>'

    failed_msg = '[failed_list]<br/>'
    failed_list = context['ti'].xcom_pull(key='failed_list')
    for item in failed_list:
        failed_msg += f'&nbsp;&nbsp;&nbsp;&nbsp; ym:{item[0]} reason:{item[1]}<br/>'

    if skipped_list or failed_list:
        title = f'[월간 보고서 생성에 스킵 또는 실패 건이 있음] 스킵 건수={len(skipped_list)} 실패 건수={len(failed_list)}'
        body = f'{skipped_msg}<br/>{failed_msg}<br/>'
        send_email(ALERT_EMAIL_ADDRESSES, title, body)


with DAG(
        _DAG_ID,
        description='월간 보고서 생성 수동 처리 용',
        default_args={
            'owner': 'ins.cho',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
        schedule_interval=None,
        tags=['trigger', 'monthly_report', 'monthly', 'manual', 'ins.cho'],
        catchup=False,
        params={
            'USAGE': 'Refer to the DAG code for detailed parameter usage.',
            'from_target_ym': '',
            'to_target_ym': '',
            'target_ym_list': '',
        },
) as dag:
    dag.doc_md = __doc__

    # 필요한 정보 세팅
    setup = PythonOperator(
        task_id='setup',
        python_callable=_setup
    )

    # spark_monthly_report 트리거. n개 월에 대해 순차 처리
    trigger_monthly_report = PythonOperator(
        task_id=f'trigger_monthly_report',
        python_callable=_trigger_monthly_report,
        retries=2,
        retry_delay=timedelta(seconds=60)
    )

    # 스킵 또는 실패된 리스트 알림
    conclude = PythonOperator(
        task_id='conclude',
        python_callable=_conclude,
    )

    # 파이프라인
    setup >> trigger_monthly_report >> conclude
