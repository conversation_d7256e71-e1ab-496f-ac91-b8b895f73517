"""
### 월별 보고서 생성 재처리

#### 위키
- [05-2. 월별 보고서 생성 재처리](https://wiki.navercorp.com/pages/viewpage.action?pageId=1402967786)

#### 개요
- Jobs에 처리되지 않고 남아 있는 월에 대해 자동으로 재처리.
- 한 번 실행 시 하나의 시간대만 처리.
- 동시에 1개의 dag run만 존재할 수 있음. 중복 실행 불가.
- spark_monthly_report DAG을 트리거시킴

#### 주기
- 매 시 5분

#### config
- 없음
- 수동 실행을 위해서는 trigger_monthly_report_manual을 사용헤 주세요.
"""

import logging
import os

import pendulum
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import ShortCircuitOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.types import DagRunType

from biz.monthly.spark_monthly_report import _DAG_ID as SPARK_DAG_ID
from core.base import DEFAULT_TZ, XCOM_TARGET_YM, ALERT_EMAIL_ADDRESSES
from core.dao import job_dao

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRG-MONTHLY-RPT-REPROCESS]'


def _get_job(**context):
    """
    가장 오래된 실패 이력 1건만 가져와서 target_ym 설정
    :param context:
    :return:
    """
    doc = job_dao.get_job({'type': SPARK_DAG_ID, 'manual': 0})
    if doc:
        if doc['retryCnt'] >= job_dao.MAX_RETRY_CNT:
            logging.warning(
                f'{_LOG_PREFIX} {doc["datetime"]}의 retryCnt={doc["retryCnt"]}. 최대처리횟수({job_dao.MAX_RETRY_CNT})에 도달해서 스킵.')
        elif doc['running'] == 1:
            logging.warning(
                f'{_LOG_PREFIX} {doc["datetime"]} 이미 처리 중이므로 스킵. _id={doc["_id"]}')
        else:
            context['ti'].xcom_push(XCOM_TARGET_YM, doc['datetime'])
            return True
    else:
        logging.info(f'{_LOG_PREFIX} 처리할 job 없음')

    return False


with DAG(
        _DAG_ID,
        description='월간 보고서 작성을 위한 자동 재처리 DAG. spark_monthly_report 트리거시킴. 한 번에 한 개의 월에 대해서만 처리.',
        default_args={
            'owner': 'ins.cho',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
        schedule_interval="5 * * * *",
        tags=['trigger', 'monthly_report', 'monthly', 'reprocess', 'ins.cho'],
        catchup=False,
        max_active_runs=1,
) as dag:
    dag.doc_md = __doc__

    # 처리해야 할 job이 있을 때만 다운스트림 태스크 진행
    # Jobs에서 시간대순으로 하나의 시간대만 추출
    get_job = ShortCircuitOperator(
        task_id='setup',
        python_callable=_get_job,
    )

    # spark_monthly_report 트리거
    trigger_monthly_report = TriggerDagRunOperator(
        trigger_dag_id='spark_monthly_report',
        trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
        task_id='trigger_spark_monthly_report',
        execution_date=pendulum.now(DEFAULT_TZ),
        wait_for_completion=True,
        poke_interval=60,  # wait_for_completion=True 일때만 유효한 옵션
        reset_dag_run=True,
        conf={
            XCOM_TARGET_YM: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YM}") }}}}'
        }
    )

    # 파이프라인
    get_job >> trigger_monthly_report
