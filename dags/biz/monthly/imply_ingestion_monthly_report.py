"""
#### 위키
- [05-5. 월별 보고서 ingestion](https://wiki.navercorp.com/pages/viewpage.action?pageId=**********)

#### 개요
- gfp 월별 리포트를 imply 를 통해 제공하기 위해 ingestion 을 수행
- 수동처리도 이 dag 으로 처리

#### 주기
- spark_monthly_report 의 실행에 따름

#### config
- target_ym: YYYYMM,
- target_report_type: ingestion 하고자 하는 report type. comma 로 구분하여 기재 (default: adprovider,adunit_aur,adunit_dlv)
- drop_existing: 해당 YYYYMM 에 대해서 imply 의 기존 data 를 덮어쓸 지 여부. True 일 경우 기존과 rollup 됨. (default: true)
"""
import logging
import os
import subprocess
from datetime import timedelta

import pendulum
from airflow import AirflowException
from airflow.exceptions import AirflowFailException, AirflowSkipException
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator

from core import utils
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YM, DRUID_OVERLORD, \
    C3_PATH_PREFIX, C3_USER, C3_PASSWORD, C3_WEBHDFS_URL

DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... MONTHLY_REPORT_IMPLY'

_XCOM_REPORT_TYPE = 'target_report_type'
_XCOM_DRUID_URL = 'druid_url'
_XCOM_INGESTION_SPEC = 'ingestion_spec'
PARAM_DROP_EXISTING = 'drop_existing'

_GET_LEADER_ENDPOINT = '/druid/indexer/v1/leader'
_SUBMIT_TASK_ENDPOINT = '/druid/indexer/v1/task'

_HDFS_HTTP_URL = C3_WEBHDFS_URL + C3_PATH_PREFIX + '/monthly/{report_path}?op=OPEN'
_AVAILABLE_REPORT = ['adprovider', 'adunit_aur', 'adunit_dlv']


def _setup(**context):
    """
    dag 실행에 필요한 파라미터 세팅

    :param context:
    :return:
    """
    _set_target_ymdh(**context)
    _set_target_report_type(**context)
    _set_druid_url(**context)


def _set_target_ymdh(**context):
    """
    ingest 하기 위해 입력한 대상 '월별리포트의 년도/월' 을 세팅

    :param context:
    :return:
    """
    target_ym = context['params'].get(XCOM_TARGET_YM)
    if target_ym:
        target_ym = str(target_ym).strip()
        pendulum.from_format(target_ym, 'YYYYMM')
        logging.info(f'{_LOG_PREFIX} Using target_ym from "params"={target_ym}')
    else:
        logging.info(f'{_LOG_PREFIX} params does not exist. target_ym is empty')
        raise AirflowException(f'target_ym is not defined. target_ym={target_ym}')

    context['ti'].xcom_push(XCOM_TARGET_YM, target_ym)


def _set_target_report_type(**context):
    """
    ingest 하기 위해 입력한 대상 '월별리포트의 기준 타입' 을 세팅

    :param context:
    :return:
    """
    target_report = []
    target_report_type = context['params'].get(_XCOM_REPORT_TYPE)
    if target_report_type:
        target_report_types = list(map(lambda s: s.strip(), target_report_type.split(',')))
        for rtype in target_report_types:
            if rtype in _AVAILABLE_REPORT:
                target_report.append(rtype)
            elif rtype.split('_')[0] == 'adunit':
                raise AirflowFailException(f'{_LOG_PREFIX} adunit 기준 월별리포트는 adunit_aur, adunit_dlv 중에 입력하세요. rtype={rtype}')
            else:
                raise AirflowFailException(f'{_LOG_PREFIX} 대상 리포트가 잘못 입력되었습니다. rtype={rtype}')

    else:
        raise AirflowFailException(f'{_LOG_PREFIX} 대상 리포트가 입력되지 않았습니다.')

    context['ti'].xcom_push(key=_XCOM_REPORT_TYPE, value=target_report)


def _set_druid_url(**context):
    """
    드루이드의 leader Overlord url 을 조회하여 세팅 (달라질 수 있기 때문에 매번 실행)

    :param context:
    :return:
    """
    druid_url_list = []
    try:
        for druid_overlord in DRUID_OVERLORD:
            proc = subprocess.run(
                [f'curl -get {druid_overlord}{_GET_LEADER_ENDPOINT}'],
                shell=True,
                capture_output=True,
                text=True,
                timeout=900,
            )
            logging.info(f'STDOUT:\n{proc.stdout}')
            logging.info(f'STDERR:\n{proc.stderr}')
            proc.check_returncode()
            druid_url_list.append(proc.stdout + _SUBMIT_TASK_ENDPOINT)
    except Exception:
        raise AirflowFailException(f'command 실패')

    context['ti'].xcom_push(key=_XCOM_DRUID_URL, value=druid_url_list)


def _prepare_ingestion_spec(**context):
    """
    druid api 로 전달할 ingestion spec 을 작성

    :param context:
    :return:
    """
    spec_map = dict()

    target_report_type = context['ti'].xcom_pull(key=_XCOM_REPORT_TYPE)
    target_ym = context['ti'].xcom_pull(key=XCOM_TARGET_YM)
    year, month = target_ym[0:4], target_ym[4:6]
    next_ym = pendulum.date(int(year), int(month), 1).add(months=1)
    next_year, next_month = next_ym.year, next_ym.month

    drop_existing = context['params'].get(PARAM_DROP_EXISTING)

    for report_type in target_report_type:
        target_report_path = f'{year}/{month}/{target_ym}_{report_type}.csv'
        input_source_uri = _HDFS_HTTP_URL.format(report_path=target_report_path)
        logging.info(f'{_LOG_PREFIX} 월별리포트 경로: {input_source_uri}')

        ingestion_spec = {
            "type": "index_parallel",
            "spec": {
                "ioConfig": {
                    "type": "index_parallel",
                    "inputSource": {
                        # "https://knox-pan.c3s.navercorp.com/gateway/pg07-auth-basic/webhdfs/v1/user/gfp-data/monthly/2023/11/202311_adunit_aur.csv?op=OPEN"
                        "type": "http",
                        "uris": [input_source_uri],
                        "httpAuthenticationUsername": C3_USER,
                        "httpAuthenticationPassword": C3_PASSWORD,
                    },
                    "inputFormat": {
                        "type": "csv",
                        "findColumnsFromHeader": True
                    },
                    "appendToExisting": False,  # 같은 timestamp 를 가질 경우 aggregate 수행 (true 면 수행하지 않고 단순히 append)
                    "dropExisting": drop_existing,  # true 이려면 appendToExisting 을 false 로 해야함.
                },
                "tuningConfig": {
                    "type": "index_parallel",
                    "partitionsSpec": {
                        "type": "dynamic"
                    },
                    "logParseExceptions": True
                },
                "dataSchema": {
                    "dataSource": f"gfp_monthly_{report_type}",
                    "timestampSpec": {
                        "column": "!!!_no_such_column_!!!",
                        "missingValue": f"{year}-{month}-01T00:00:00Z"
                    },
                    "granularitySpec": {
                        "segmentGranularity": "month",
                        "queryGranularity": "month",
                        "intervals": [f"{year}-{month}-01/{next_year}-{next_month}-01"],
                        "rollup": True,
                    },
                    "dimensionsSpec": {
                        "useSchemaDiscovery": True,
                        "includeAllDimensions": True,
                        "dimensionExclusions": []
                    }
                }
            }
        }

        spec_map[report_type] = ingestion_spec

    context['ti'].xcom_push(key=_XCOM_INGESTION_SPEC, value=spec_map)


def submit_injestioin_spec_to_druid(rtype: str, **context):
    """
    3가지 타입의 월별리포트 중 입력된 타입만을 ingestion 실행

    :param target_report_type:
    :param context:
    :return:
    """
    target_report_type = context['ti'].xcom_pull(key=_XCOM_REPORT_TYPE)
    druid_url_list = context['ti'].xcom_pull(key=_XCOM_DRUID_URL)
    if rtype in target_report_type:
        ingestion_spec = context['ti'].xcom_pull(key=_XCOM_INGESTION_SPEC).get(rtype)
        for druid_url in druid_url_list:
            utils.request(
                url=druid_url,
                method='POST',
                headers={'Content-Type': 'application/json'},
                json=ingestion_spec
            )
    else:
        raise AirflowSkipException(f'{_LOG_PREFIX} 입력된 리포트 타입이 아니므로 스킵')


with DAG(
    DAG_ID,
    default_args={
        'owner': 'ins.cho',
        'email': ALERT_EMAIL_ADDRESSES,
        'email_on_failure': True,
    },
    schedule_interval=None,
    start_date=pendulum.datetime(2023, 7, 1, tz=DEFAULT_TZ),
    tags=['imply', 'ingest', 'druid', 'monthly', 'monthly_report', 'ins.cho'],
    params={
        XCOM_TARGET_YM: '',
        _XCOM_REPORT_TYPE: 'adprovider,adunit_aur,adunit_dlv',
        PARAM_DROP_EXISTING: True,
    },
    catchup=False,
    max_active_runs=1,
) as dag:
    dag.doc_md = __doc__

    setup = PythonOperator(
        task_id='setup',
        python_callable=_setup
    )

    prepare_ingestion_spec = PythonOperator(
        task_id='prepare_ingestion_spec',
        python_callable=_prepare_ingestion_spec
    )

    setup >> prepare_ingestion_spec
    prev_task = prepare_ingestion_spec
    for rtype in _AVAILABLE_REPORT:
        submit_ingestion_spec = PythonOperator(
            task_id=f'ingestion_for_{rtype}',
            python_callable=submit_injestioin_spec_to_druid,
            op_args=[rtype],
            execution_timeout=timedelta(minutes=30),
            trigger_rule='none_failed'
        )

        prev_task >> submit_ingestion_spec
        prev_task = submit_ingestion_spec
