"""
### 수익쉐어 리포트 생성 - 정규

#### 위키
- [https://wiki.navercorp.com/pages/viewpage.action?pageId=1461924703](https://wiki.navercorp.com/pages/viewpage.action?pageId=1461924703)

#### 개요
- SATISFIED, AGG_FAILURE, UPLOAD_FAILURE, REPROCESS 리포트 대상으로 스파크 DAG 트리거
- 한 번 떴을 때 처리해야 할 리포트 모두 처리 (Environments.name = revenue-sharing-report-max-reports-daily)
- 여유 슬롯 개수만큼 병렬처리 (Environments.name = revenue-sharing-report-max-parallel-runs-daily)

#### 주기
- 10분
- 동시에 한 번의 DAG RUN만 허용

#### config
- 없음
- 수동 실행을 위해서는 trigger_daily_revenue_sharing_report_manual을 사용헤 주세요.
"""
import logging
import os
from time import sleep

import pendulum
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator, ShortCircuitOperator

from biz.revenue_sharing3 import revenue_sharing_report_base
from biz.revenue_sharing3 import spark_revenue_sharing_report
from core.airflow_api import trigger_dagrun
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_KIND, XCOM_REPORT_ID, \
	T_ST_UPLOAD_FAILURE, T_ST_REPROCESS, XCOM_RS_REPORT_TRACE_IDS, XCOM_DETAIL, XCOM_IS_FULL_FIELD, XCOM_IS_DEBUG_MODE
from core.base import TIME_UNIT_DAILY, T_ST_AGG_FAILURE, T_ST_SATISFIED

# DAG 기본정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")

_LOG_PREFIX = f'.......... [TRG-{TIME_UNIT_DAILY}-RS-RPT-REGULAR]'

_SPARK_DAG_ID = spark_revenue_sharing_report.DAG_ID

# 처리해야 할 리포트 조회 필터
# 		- 재처리해야 할 리포트도 포함
_FILTER = {
	'taskState': {'$in': [T_ST_SATISFIED, T_ST_AGG_FAILURE, T_ST_UPLOAD_FAILURE, T_ST_REPROCESS]},
	'$expr': {'$eq': [{'$strLenCP': '$date'}, 8]}  # 일별 리포트만 추출
}


def _is_exist_report_to_process(**context):
	"""
	생성할 리포트가 있는지 확인

	:param context:
	:return:
	"""

	count = revenue_sharing_report_base.get_reports_count_to_process(_FILTER)
	if count < 1:
		logging.info(f'{_LOG_PREFIX} 처리할 리포트 없음')
		return False
	else:
		return True


def _trigger_spark_in_parallel(**context):
	"""
	스파크 DAG 트리거
	- 여유 슬롯 개수만큼 병렬처리
	- 처리할 리포트가 없을 때까지 반복
	- 트리거 DAG의 완료를 기다리지 않으므로 실패한 것이 다음 여유 슬롯에 다시 선정될 수 있음.
	  그러나 무한 반복되지는 않음(리포트 생성이 진행중이라면 다음 여유 슬롯에 걸리지 않음)
	:param context:
	:return:
	"""
	triggered_cnt = 0

	# DAG이 한 번 떴을 때 처리할 수 있는 최대 리포트 건 수
	max_report_cnt = revenue_sharing_report_base.get_max_reports_per_run(TIME_UNIT_DAILY)

	while True:
		if triggered_cnt > max_report_cnt:
			# DAG RUN이 한 번 떴을 때 너무 많은 리포트를 처리하지 않도록 제한
			logging.info(f'{_LOG_PREFIX} DAG RUN이 한 번 떴을 때 처리할 수 있는 최대 리포트 개수({max_report_cnt}) 도달. 종료')
			break

		# 최대 동시 실행 개수 조회
		max_parallel_runs = revenue_sharing_report_base.get_spark_dag_max_parallel_runs('daily')

		# 스파크를 실행시킬 수 있는 여유 슬롯 설정
		available_slots = max_parallel_runs - revenue_sharing_report_base.get_spark_run_count(TIME_UNIT_DAILY)

		if available_slots > 0:
			logging.info(f'{_LOG_PREFIX} max_active_runs={max_parallel_runs} available_slots={available_slots}')

			# 여유 슬롯 개수만큼 실행할 일별 리포트를 일자/생성일 순서로 가져옴
			# 재처리가 필요한 리포트를 포함하는 경우 트레이스 ID 목록이 설정되어 옴
			reports = revenue_sharing_report_base.get_reports_to_process(_FILTER, available_slots)

			if len(reports) < 1:
				logging.info(f'{_LOG_PREFIX} 더 이상 처리할 리포트 없음')
				break

			for idx, rpt in enumerate(reports):
				# 1. 리포트 상태를 AGG/UPLOAD_IN_PROGRESS로 변경
				#    리포트를 선정했을 때 이 리포트들에 대한 상태를 업데이트해야 다음 주기에서 중복 추출되지 않음
				revenue_sharing_report_base.set_to_in_progress_of_report(rpt['_id'], rpt['taskState'])

				# 2. 재처리할 트레이스가 있는 경우 트레이스 상태를 IN_PROGRESS로 변경
				if 'revenueSharingReportTrace_ids' in rpt and len(rpt['revenueSharingReportTrace_ids']) > 0:
					revenue_sharing_report_base.set_to_in_progress_of_trace(rpt['_id'])

				# 3. conf 설정
				"""
				예)	conf = {
					'kind': 'regular',
					'report_id': '68472af04378d800177726bd',
					'revenue_sharing_report_trace_ids': '68479e54f594ed553ef14736,684a3203546c50023dce52a3',
					'detail': {
						'time_unit': 'daily',
						'date':'20250609',
						'schedule': '스케줄1(633e7befd4c6100036235d12)',
						'publisher': '퍼블리셔1(67fe7e12fc15f1adcab17c2b)'
					} 
				}
				"""
				trace_ids_with_comma = ','.join(rpt['revenueSharingReportTrace_ids']) if rpt[
					'revenueSharingReportTrace_ids'] else ''
				conf = {
					XCOM_KIND: 'regular',  # 정규 생성
					XCOM_REPORT_ID: str(rpt['_id']),  # 리포트 ID
					XCOM_RS_REPORT_TRACE_IDS: trace_ids_with_comma,  # 리포트 ID에 해당하는 리포트 트레이스가 있을 때만 명시
					XCOM_DETAIL: {  # 상세정보
						'time_unit': TIME_UNIT_DAILY,  # revenue_sharing_report_base.get_spark_run_count()에서 사용함
						'date': rpt['date'],
						'schedule': f'{rpt["scheduleName"]}({str(rpt["schedule_id"])})',
						'publisher': f'{rpt["publisherName"]}({str(rpt["publisher_id"])})'
					},
					XCOM_IS_FULL_FIELD: '0',
					XCOM_IS_DEBUG_MODE: '0',
				}

				# 4. 스파크 DAG 트리거
				dag_run_id = trigger_dagrun(_SPARK_DAG_ID, pendulum.now(DEFAULT_TZ), conf)
				triggered_cnt += 1
				logging.info(f'{_LOG_PREFIX} {triggered_cnt} 트리거 완료. conf={conf} dag_run_id={dag_run_id}')
		else:
			logging.info(f'{_LOG_PREFIX} 여유 슬롯 없음')
			sleep(60)  # 1분 대기 후 다시 확인

	logging.info(f'{_LOG_PREFIX} 모두 완료. triggered_count={triggered_cnt}')


with DAG(
		DAG_ID,
		description='일별 수익쉐어 리포트 집계를 위한 스파크 실행 정규 DAG. spark_revenue_sharing_report 트리거시킴',
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		tags=['trigger', 'rs', 'daily', 'regular', 'juyoun.kim'],
		start_date=pendulum.datetime(2025, 1, 1, tz=DEFAULT_TZ),
		schedule_interval="*/10 * * * *",  # 10분 마다
		# schedule_interval=None,
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# 처리할 리포트가 있다면 설정
	is_exist_report_to_process = ShortCircuitOperator(
		task_id='is_exist_report_to_process',
		python_callable=_is_exist_report_to_process,
	)

	# 스파크 트리거 병렬 처리
	trigger_spark_in_parallel = PythonOperator(
		task_id='trigger_spark_in_parallel',
		python_callable=_trigger_spark_in_parallel,
	)

	# 파이프라인
	is_exist_report_to_process >> trigger_spark_in_parallel
