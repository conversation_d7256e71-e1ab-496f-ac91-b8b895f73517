"""
### 수익쉐어 리포트 모니터링
- SG 지연
- ZRGFP 지연
- 너무 오래 실행되고 있는 리포트

#### 위키
- [18-23. [Trace] ZirconTrace 감지](https://wiki.navercorp.com/pages/viewpage.action?pageId=2392148190)

#### 개요

- 동시에 하나의 DAG RUN만 허용

#### 주기
- 매 시 15분

#### config
- 없음
"""

import logging
import os

import pendulum
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator

from core import utils
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, BATCH_SERVER_ADDRESS

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [MAKE-RS-TRACE]'

# Batch 호출 주소
_REQ_URL = BATCH_SERVER_ADDRESS + '/batch/revenuesharing/monitor'


def _monitor_revenue_sharing_report(**context):
	# 배치 서버 호출
	logging.info(f'{_LOG_PREFIX} url={_REQ_URL}')
	res = utils.request(_REQ_URL)
	return res


with DAG(
		_DAG_ID,
		description='수익쉐어 리포트 모니터링 DAG',
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2025, 6, 1, tz=DEFAULT_TZ),
		schedule_interval="15 * * * *",  # 매 시 15분
		# schedule_interval=None,
		tags=['rs', 'monitor', 'juyoun.kim'],
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	"""
	"""

	# 수익쉐어 리포트 모니터링
	monitor_revenue_sharing_report = PythonOperator(
		task_id='monitor_revenue_sharing_report',
		python_callable=_monitor_revenue_sharing_report,
		dag=dag,
	)

	# 파이프라인
	monitor_revenue_sharing_report
