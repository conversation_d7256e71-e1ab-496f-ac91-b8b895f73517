import os

import pendulum
from airflow.models import DagRun
from airflow.utils.state import DagRunState
from bson import ObjectId

from biz.revenue_sharing3 import spark_revenue_sharing_report
from core.base import DEFAULT_TZ, T_ST_REPROCESS, ST_IN_PROGRESS, ST_READY, \
	T_ST_AGG_COMPLETE
from core.base import T_ST_AGG_IN_PROGRESS, \
	T_ST_AGG_FAILURE, T_ST_UPLOAD_IN_PROGRESS, T_ST_SATISFIED
from core.dao import revenue_sharing_report_dao, revenue_sharing_report_trace_dao
from core.dao.environment_dao import get_environment_value_by_name

# DAG 기본정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")

_SPARK_DAG_ID = spark_revenue_sharing_report.DAG_ID


def get_max_reports_per_run(time_unit: str) -> int:
	"""
	정규 DAG(trigger_{daily|monthly}_revenue_sharing_report_regular)이 한 번 떴을 때 처리할 수 있는 최대 리포트 수

	:param time_unit: 'daily' 또는 'monthly'
	:return: 최대 리포트 수
	"""
	key = f'revenue-sharing-report-max-reports-{time_unit.lower()}'
	return int(str(get_environment_value_by_name(key)))


def get_spark_dag_max_parallel_runs(time_unit: str) -> int:
	"""
	일별/월별 리포트, 정규/수동/자동을 포괄하여 동시에 실행될 수 있는 최대 spark dag run의 수

	:param time_unit: 'daily' 또는 'monthly'
	:return: 최대 활성 실행 수
	"""
	key = f'revenue-sharing-report-max-parallel-runs-{time_unit.lower()}'
	return int(str(get_environment_value_by_name(key)))


def get_reports_count_to_process(filter: dict):
	"""
	처리할 리포트 개수 조회
		- RevenueSharingReports.taskState 가 T_ST_SATISFIED, T_ST_AGG_FAILURE, T_ST_UPLOAD_FAILURE, T_ST_REPROCESS 인 것 중에서

	:param filter: 필터 조건
	:return:
	"""
	return revenue_sharing_report_dao.get_reports_count(filter)


def get_reports_to_process(filter: dict, available_slots: int):
	"""
	슬롯 개수만큼 처리할 리포트 가져오기
		- RevenueSharingReports.taskState 가 T_ST_SATISFIED, T_ST_AGG_FAILURE, T_ST_UPLOAD_FAILURE, T_ST_REPROCESS 대상
		- 재처리 대상 리포트를 포함하는 경우 트레이스 ID 목록까지 설정하여 리턴

	:param filter: 필터 조건
	:param available_slots: 현재 사용 가능한 슬롯 개수
	:return:
	"""
	sort = [('date', 1), ('createdAt', 1)]
	reports = revenue_sharing_report_dao.get_reports_with_meta(filter, sort, available_slots)

	for rpt in reports:
		# 재처리 필요 상태인 경우 리포트에 트레이스 ID 리스트 설정
		if rpt['taskState'] == T_ST_REPROCESS:
			traces = revenue_sharing_report_trace_dao.get_traces({
				'revenueSharingReport_id': rpt['_id'],
				'state': ST_READY
			})
			rpt['revenueSharingReportTrace_ids'] = [trace['_id'] for trace in traces]
		else:
			rpt['revenueSharingReportTrace_ids'] = []

	return reports


def get_spark_run_count(time_unit):
	"""
	RUNNING / QUEUED 상태인 DagRuns 갯수

	:return:
	"""
	dag_runs = []
	running_dag_runs = DagRun.find(dag_id=_SPARK_DAG_ID, state=DagRunState.RUNNING)
	queued_dag_runs = DagRun.find(dag_id=_SPARK_DAG_ID, state=DagRunState.QUEUED)

	for dr in (running_dag_runs + queued_dag_runs):
		# dag_run의 conf 설정 가져오기
		# queued 된 dag run의 xcom은 가져올 수 없으므로 conf로 판단
		dag_run_conf = dr.conf if dr.conf else {}
		if dag_run_conf['detail']['time_unit'] == time_unit:
			dag_runs.append(dr)

	return len(dag_runs)


def set_to_in_progress_of_report(report_id: ObjectId, asis_task_state):
	"""
	수익쉐어 리포트 상태를 IN_PROGRESS로 변경
	:param report_id: 리포트 ID
	:param asis_task_state T_ST_SATISFIED, T_ST_AGG_FAILURE, T_ST_UPLOAD_FAILURE, T_ST_REPROCESS 중 하나
	:return:
	"""
	tobe_task_state = (T_ST_AGG_IN_PROGRESS if asis_task_state in
											   [T_ST_SATISFIED, T_ST_AGG_COMPLETE, T_ST_AGG_FAILURE, T_ST_REPROCESS]
					   else T_ST_UPLOAD_IN_PROGRESS)
	revenue_sharing_report_dao.update_report({'_id': report_id}, {
		'state': ST_IN_PROGRESS,
		'taskState': tobe_task_state,
		'startedAt': pendulum.now().in_tz(DEFAULT_TZ),
		'modifiedAt': pendulum.now().in_tz(DEFAULT_TZ),
	})


def set_to_in_progress_of_trace(report_id: ObjectId):
	"""
	수익쉐어 리포트 트레이스 상태를 IN_PROGRESS로 변경
	:param report_id: 리포트 ID
	:return:
	"""
	revenue_sharing_report_trace_dao.update_trace({'revenueSharingReport_id': report_id, 'state': ST_READY}, {
		'state': ST_IN_PROGRESS,
		'modifiedAt': pendulum.now().in_tz(DEFAULT_TZ),
	})
