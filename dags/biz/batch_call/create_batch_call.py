"""
### 단순 배치 서버 호출을 위해 Dynamic Dag 생성
- 공통 : 배치서버 호출/재시도/실패알림
- vip=True  이면 배치서버의 vip 로 호출
  vip=False 이면 배치서버 3,4번에 각각 호출
"""

import logging
from urllib.parse import urlencode

import requests
from airflow.decorators import dag, task
from airflow.exceptions import AirflowException
from pendulum import datetime, duration

from core import utils
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, \
	BATCH_SERVER_ADDRESS, BATCH_SERVER_IP_ADDRESS, \
	PROFILE, PROFILE_TEST, PROFILE_REAL


def get_batch_server_ip_address(number):
	try:
		return BATCH_SERVER_IP_ADDRESS[number]
	except IndexError:
		return ''


_dag_infos = [
	# Creator Advisor 배치서버 로컬 삭제
	{
		'dag_id': 'delete_creator_advisor_local_batch',
		'description': 'delete creator advisor local directory (매주 화요일 오전 10시)',
		'tags': ['batch_call', 'creator_advisor', 'delete', 'local', 'monthly', 'bitna.cho'],
		'doc_md': 'delete creator advisor local directory (매주 화요일 오전 10시)',
		'schedule_interval': '0 10 * * 2',
		'start_date': datetime(2023, 1, 1, tz=DEFAULT_TZ),
		'default_args': {
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
			'email_on_retry': False,
			'retries': 5,
			'retry_delay': duration(minutes=5),
		},
		'req_url': '/batch/report/creatoradvisor/delete/local',
		'log_prefix': '.......... [DELETE-CREATOR-ADVISOR-LOCAL]',
		'vip': False  # 3,4번 서버 모두 실행
	},

	# Creator Advisor HDFS 삭제
	{
		'dag_id': 'delete_creator_advisor_hdfs',
		'description': 'delete creator advisor hdfs directory (매주 월요일 오전 10시)',
		'tags': ['batch_call', 'creator_advisor', 'delete', 'hdfs', 'weekly', 'bitna.cho'],
		'doc_md': 'delete creator advisor hdfs directory (매주 월요일 오전 10시)',
		'schedule_interval': '0 10 * * 1',
		'start_date': datetime(2023, 1, 1, tz=DEFAULT_TZ),
		'default_args': {
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
			'email_on_retry': False,
			'retries': 5,
			'retry_delay': duration(minutes=5),
		},
		'req_url': '/batch/report/creatoradvisor/delete/hdfs',
		'log_prefix': '.......... [DELETE-CREATOR-ADVISOR-HDFS]',
		'vip': True
	},

	# Silvergrey 배치서버 HDFS 삭제
	{
		'dag_id': 'delete_silvergrey_hdfs',
		'description': 'delete silvergrey hdfs directory (매주 화요일 오전 10시)',
		'tags': ['batch_call', 'silvergrey', 'delete', 'hdfs', 'weekly', 'bitna.cho'],
		'doc_md': 'delete silvergrey hdfs directory (매주 화요일 오전 10시)',
		'schedule_interval': '0 10 * * 2',
		'start_date': datetime(2023, 1, 1, tz=DEFAULT_TZ),
		'default_args': {
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
			'email_on_retry': False,
			'retries': 5,
			'retry_delay': duration(minutes=5),
		},
		'req_url': '/batch/day/reportapi/delete',
		'log_prefix': '.......... [DELETE-SILVERGREY-HDFS]',
		'vip': True
	},

	# Performance Report HDFS/NUBES 삭제
	{
		'dag_id': 'delete_performance_report',
		'description': 'delete performance report hdfs & nubes (매주 월요일 오전 10시)',
		'tags': ['batch_call', 'performance_report', 'delete', 'hdfs', 'nubes', 'weekly', 'bitna.cho'],
		'doc_md': 'delete performance report hdfs & nubes (매주 월요일 오전 10시)',
		'schedule_interval': '0 10 * * 1',
		'start_date': datetime(2024, 1, 1, tz=DEFAULT_TZ),
		'default_args': {
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
			'email_on_retry': False,
			'retries': 5,
			'retry_delay': duration(minutes=5),
		},
		'req_url': '/batch/performance/delete',
		'log_prefix': '.......... [DELETE-PERFORMANCE-REPORT]',
		'vip': True
	},

	# C3 Hadoop Active Host 설정
	{
		'dag_id': 'set_active_host_c3',
		'description': 'set c3 active host (매시 15분)',
		'tags': ['batch_call', 'c3', 'active_host', 'hourly', 'bitna.cho'],
		'doc_md': 'set c3 active host (매시 15분)',
		'schedule_interval': '15 * * * *',
		'start_date': datetime(2024, 1, 1, tz=DEFAULT_TZ),
		'default_args': {
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
			'email_on_retry': False,
			'retries': 5,
			'retry_delay': duration(minutes=5),
		},
		'req_url': '/batch/c3/set_active_host',
		'log_prefix': '.......... [SET-ACTIVE-HOST-C3]',
		'vip': False,
	},
]


def create_dag(dag_id, schedule_interval, default_args, start_date, tags, description, doc_md, req_url, log_prefix,
			   **kwargs):
	@dag(
		dag_id=dag_id,
		description=description,
		doc_md=doc_md,
		tags=tags,
		start_date=start_date,
		schedule_interval=schedule_interval,
		default_args=default_args,
		params=kwargs.get('params'),  # dag_infos 에 기술한 값 일뿐, 외부 실행으로 params 값을 넣어준다면 context['params'] 을 사용,
		catchup=False,
	)
	def dynamic_dag():
		@task(task_id='request_batch_server')
		def _request_batch_server(**context):
			try:
				if PROFILE in [PROFILE_REAL, PROFILE_TEST]:
					# 배치 서버 요청 (타임아웃 30분)
					if kwargs.get('params'):
						res = utils.request(f'{req_url}?{urlencode(context["params"])}', timeout=60 * 30)
					else:
						res = utils.request(req_url, timeout=60 * 30)

					if res.get('code') == 200:
						logging.info(f'{log_prefix} 배치 서버 요청 완료 ( res= {res} )')
					else:
						raise AirflowException(f'배치 서버 요청 실패 ( res= {res} )')
				else:
					logging.info(f'{log_prefix} PROFILE={PROFILE}, URL={req_url} 호출하지 않음')
			except requests.exceptions.ReadTimeout as ex:
				logging.exception(f'{log_prefix} 배치 서버 요청 timeout ( req_url= {req_url} ) \n ex= {ex}')
			except Exception as ex:
				raise AirflowException(f'배치 서버 요청 실패 ( req_url= {req_url} ) \n ex= {ex}')

		_request_batch_server()

	dynamic_dag = dynamic_dag()

	return dynamic_dag


for _, dag_info in enumerate(_dag_infos):
	###########
	# Dag 생성에 필요한 파라미터들 (dag_id 포함)
	schedule_interval = dag_info['schedule_interval']
	default_args = dag_info['default_args']
	doc_md = dag_info['doc_md']
	start_date = dag_info['start_date']
	tags = dag_info['tags']
	description = dag_info['description']
	log_prefix = dag_info['log_prefix']
	kwargs = {
		'params': dag_info.get('params'),
	}
	###########

	if dag_info['vip']:
		dag_id = dag_info['dag_id']
		req_url = BATCH_SERVER_ADDRESS + dag_info['req_url']

		globals()[dag_id] = create_dag(
			dag_id, schedule_interval, default_args, start_date, tags, description, doc_md,
			req_url, log_prefix,
			**kwargs
		)

	else:
		for idx, ip_addr in enumerate(BATCH_SERVER_IP_ADDRESS):
			dag_id = dag_info['dag_id'] + f'{idx + 3 if len(BATCH_SERVER_IP_ADDRESS) > 1 else ""}'
			req_url = BATCH_SERVER_IP_ADDRESS[idx] + dag_info['req_url']

			globals()[dag_id] = create_dag(
				dag_id, schedule_interval, default_args, start_date, tags, description, doc_md,
				req_url, log_prefix,
				**kwargs
			)
