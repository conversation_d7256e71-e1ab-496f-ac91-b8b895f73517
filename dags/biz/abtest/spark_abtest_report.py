"""
### ABTest 리포트 스파크 집계

#### 위키
- [14-4. ABTest 리포트 생성](https://wiki.navercorp.com/pages/viewpage.action?pageId=1768354984)

#### 주기
- 정규   : trigger_abtest_report_regular 에 따름
- 수동   : trigger_abtest_report_manual 에 따름
- 재처리 : trigger_abtest_report_reprocess 에 따름

#### config
- target_ymd: YYYYMMDD

"""

import logging
import os
from functools import partial

import pendulum
from airflow.exceptions import AirflowException
from airflow.models import DAG
from airflow.operators.python import PythonOperator
from airflow.sensors.python import PythonSensor

from biz.abtest import abtest_report_dao
from biz.abtest.abtest_report_dao import get_abt_target_publisher
from core import utils, c3
from core.base import DEFAULT_TZ, PROFILE, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD, XCOM_PUBLISHER_IDS, \
	PROFILE_DEV, PROFILE_TEST, PROFILE_STAGE, PROFILE_REAL, BATCH_SERVER_ADDRESS, SPARK_SUBMIT_OPTIONS, \
	POOL_SPARK, SPARKLING_APP_JAR_PATH, SPARKLING_IMAGE, SPARKLING_APP_HARD_LIMIT_1 as SPARKLING_APP_HARD_LIMIT, \
	C3_PATH_PREFIX, PROFILE_LOCAL
from core.dao import job_dao
from core.spark_pool import POOL_SLOT_TRIVIAL_2
from core.spark_submit_op import invoke_job_with_args
from core.spark_task_group import create_task_group

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [SPK-ABT-REPORT]'

# Spark
_SPARK_APP_CLASS = 'com.navercorp.gfp.biz.abtest.ABTestDailyAggregator'

# Profile
_PROFILE_SETTINGS = {
	PROFILE_TEST: {
		SPARK_SUBMIT_OPTIONS: """
            --num-executors 1
            --executor-cores 5
            --executor-memory 512m
			--conf spark.hadoop.mapred.output.compress=true
            --conf spark.hadoop.mapred.output.compression.codec=org.apache.hadoop.io.compress.GzipCodec
            """.split()
	},
	PROFILE_REAL: {
		SPARK_SUBMIT_OPTIONS: """
            --num-executors 1
            --executor-cores 4
            --executor-memory 1g
            """.split()
	}
}
_PROFILE_SETTINGS[PROFILE_LOCAL] = _PROFILE_SETTINGS[PROFILE_DEV] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_STAGE] = _PROFILE_SETTINGS[PROFILE_REAL]

# Batch URL
_REQ_URL = BATCH_SERVER_ADDRESS + '/batch/abtest/upload?ymd={ymd}'

# 디폴트 3시간
_LOG_CHECK_TIMEOUT = 10800


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	_init_settings(**context)
	_print_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
	:param context:
	:return:
	"""
	target_ymd = context['params'][XCOM_TARGET_YMD]
	if target_ymd:
		target_ymd = str(target_ymd).strip()
		pendulum.from_format(target_ymd, 'YYYYMMDD')

		context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)
	else:
		# target_ymd 가 없으면 에러
		logging.error(f'There is not "params.target_ymd"')
		raise AirflowException('There is not "params.target_ymd"')

	# ABtest report 대상 매체 id 목록 정보 추출
	publisher_ids = get_abt_target_publisher()

	if publisher_ids:
		context['ti'].xcom_push(XCOM_PUBLISHER_IDS, publisher_ids)
	else:
		# publisher_ids 가 없으면 에러
		logging.error(f'There is not "publisher_ids"')
		raise AirflowException('There is not "publisher_ids"')


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK}
Airflow Pool Slot: {POOL_SLOT_TRIVIAL_2}

Profile: {PROFILE}
Spark Submit Options: {_PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS]}

Params:
    target_ymd= {context['ti'].xcom_pull(key=XCOM_TARGET_YMD)}
    publisher_ids= {context['ti'].xcom_pull(key=XCOM_PUBLISHER_IDS)}
------------------------------------------------------------------------------------------
''')


def _is_exists_success_file(**context):
	"""
	abtest report 경로에 _SUCCESS 가 있는지 확인
		- /data/log/gfp/abtest/yyyy/mm/dd/_SUCCESS
	:param context:
	:return:
	"""

	ymd = pendulum.from_format(context['ti'].xcom_pull(key=XCOM_TARGET_YMD), 'YYYYMMDD')
	success_file_path = f'{C3_PATH_PREFIX}/abtest/{ymd.format("YYYY/MM/DD")}/_SUCCESS'
	logging.info(f'{_LOG_PREFIX} success_file_path= {success_file_path}')
	if not c3.exists(success_file_path):
		return False
	return True


def _request_report_upload(**context):
	"""
	abtest 리포트를 Cuve 로 upload 요청 (Batch 호출)
	hdfs -> batch local -> cuve
	"""
	if PROFILE not in [PROFILE_REAL, PROFILE_STAGE]:
		logging.info(f'{_LOG_PREFIX} PROFILE={PROFILE}, cuve 업로드 불가능하므로 배치서버 호출하지 않고 종료')
		# on_success_callback 으로 job 삭제
		return

	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	url = _REQ_URL.format(ymd=target_ymd)
	logging.info(f'{_LOG_PREFIX} request url={url}')

	try:
		# cuve 업로드 요청 (타임아웃 30분)
		res = utils.request(url, timeout=60 * 30)

		if res.get('code') != 200:
			raise AirflowException(f'cuve 업로드 요청 실패 ( res= {res} )')

	except Exception as ex:
		raise AirflowException(f'target_ymd= {target_ymd}, url= {url}\n ex= {ex}')


def _clean_up_job(is_success: bool, context: dict):
	"""
	성공했다면 job 삭제
	실패했다면 retryCnt 증가시키고 running=0 설정

	:param context:
	:return:
	"""
	job_dao.clean_up(_DAG_ID, context['ti'].xcom_pull(key=XCOM_TARGET_YMD), is_success)


def _get_app_args(context: dict):
	"""
	spark-submit 시, 넘겨줄 파라미터 정보
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	target_publisher_ids = context['ti'].xcom_pull(key=XCOM_PUBLISHER_IDS)
	return [target_ymd, target_publisher_ids]


with DAG(
		_DAG_ID,
		description='Invoke spark app for abtest report',
		tags=['spark', 'abtest', 'daily', 'hdfs', 'aida', 'cuve', 'ins.cho'],
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			XCOM_TARGET_YMD: '',
		},
		start_date=pendulum.datetime(2023, 9, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	# 환경 설정
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 중복 실행 여부 확인
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=job_dao.check_dup_run,
		op_args=[
			_DAG_ID,
			XCOM_TARGET_YMD
		]
	)

	# 해당 시간대의 Jobs.running=1 로 추가
	upsert_job = PythonOperator(
		task_id='upsert_job',
		python_callable=job_dao.upsert_job,
		op_args=[
			_DAG_ID,
			XCOM_TARGET_YMD
		]
	)

	# 실버그레이 로그 적재 확인
	# 10분마다 재시도, 3시간 동안
	wait_for_silvergrey_log = PythonSensor(
		task_id='wait_for_log',
		python_callable=abtest_report_dao._is_ready_silvergrey_log,
		mode='reschedule',
		poke_interval=60 * 10,
		timeout=_LOG_CHECK_TIMEOUT,
		retries=2,
		on_failure_callback=partial(_clean_up_job, False)
	)

	# 스파크 집계
	task_group_id = 'spark_run_abtest_report'
	spark_run_abtest_report = create_task_group(
		task_group_id,
		tooltip=_SPARK_APP_CLASS,
		spark_pool=POOL_SPARK,
		pool_slot=POOL_SLOT_TRIVIAL_2,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': _SPARK_APP_CLASS,
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			'dag_id': _DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(_DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS,
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT,
		wait_showup_failure_cb=partial(_clean_up_job, False),
		wait_complete_failure_cb=partial(_clean_up_job, False),
		conclude_app_failure_cb=partial(_clean_up_job, False)
	)

	# _SUCCESS 파일 확인
	# 10분마다 재시도, 1시간 동안
	wait_for_report_success = PythonSensor(
		task_id='wait_for_report_success',
		python_callable=_is_exists_success_file,
		mode='reschedule',
		poke_interval=60 * 10,
		timeout=60 * 60 * 1,
		retries=2,
		on_failure_callback=partial(_clean_up_job, False)
	)

	# batch server에 cuve로 report file 업로드하도록 요청
	# 성공 시 Jobs 다큐먼트 삭제
	request_report_upload = PythonOperator(
		task_id='request_report_upload',
		python_callable=_request_report_upload,
		on_failure_callback=partial(_clean_up_job, False),
		on_success_callback=partial(_clean_up_job, True)
	)

	setup >> check_dup_run >> upsert_job \
	>> wait_for_silvergrey_log >> spark_run_abtest_report \
	>> wait_for_report_success >> request_report_upload
