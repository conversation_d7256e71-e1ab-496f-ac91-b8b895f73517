"""
### salas rate (광고 공급자 수익률) 모니터링 데이터 스파크 집계

#### 1. 위키
- [25-4. salas rate 모니터링 데이터 집계](https://wiki.navercorp.com/spaces/GFP/pages/3263714693)

#### 2. 저장소
- DATA DB : MonitoringSalesRate

#### 3. config
- target_ymd : 집계 실행 날짜 YYYYMMDD
- start_date : 집계에 포함할 처음 날짜 YYYYMMDD
- end_date : 집계에 포함할 마지막 날짜 YYYYMMDD

#### 4. 타임아웃
- 로그 대기에 대한 타임아웃은 없음
- spark 집계에 대한 타임아웃 4시간

#### 5. 주기
- 정규   : trigger_monitoring_revenue_regular 에 따름
- 수동   : trigger_monitoring_revenue__manual 에 따름
- 재처리 : trigger_monitoring_revenue__reprocess 에 따름
"""

import logging
import os
from functools import partial
from typing import List

import pendulum
from airflow.exceptions import AirflowException
from airflow.models import DAG, DagRun
from airflow.operators.python import PythonOperator, ShortCircuitOperator
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType

from core import utils
from core.base import DEFAULT_TZ, PROFILE, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD, \
	PROFILE_LOCAL, PROFILE_DEV, PROFILE_TEST, PROFILE_STAGE, PROFILE_REAL, SPARK_SUBMIT_OPTIONS, \
	POOL_SPARK, SPARKLING_APP_JAR_PATH, SPARKLING_IMAGE, \
	SPARKLING_APP_HARD_LIMIT_4 as SPARKLING_APP_HARD_LIMIT, XCOM_END_DATE, XCOM_START_DATE
from core.dao import job_dao
from core.spark_pool import POOL_SLOT_TRIVIAL_5
from core.spark_submit_op import invoke_job_with_args
from core.spark_task_group import create_task_group

# DAG 기본 정보
DAG_ID = _DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [SPARK_MONITORING_SALES_RATE]'

# Spark
_SPARK_APP_CLASS = 'com.navercorp.gfp.biz.monitoring.SalesRateMonitor'

# Profile
_PROFILE_SETTINGS = {
	PROFILE_TEST: {
		SPARK_SUBMIT_OPTIONS: """
			--num-executors 2
			--executor-cores 2
			--executor-memory 500m
			--conf spark.sql.shuffle.partitions=5
			--conf spark.executor.memoryOverhead=500m
			""".split()
	},
	PROFILE_REAL: {
		SPARK_SUBMIT_OPTIONS: """
			--num-executors 4
			--executor-cores 2
			--executor-memory 500m
			--conf spark.sql.shuffle.partitions=5
			--conf spark.executor.memoryOverhead=500m
			""".split()
	}
}
_PROFILE_SETTINGS[PROFILE_LOCAL] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_DEV] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_STAGE] = _PROFILE_SETTINGS[PROFILE_REAL]


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	_init_settings(**context)
	_print_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
	:param context:
	:return:
	"""
	target_ymd = context['params'][XCOM_TARGET_YMD]
	start_date = context['params'][XCOM_START_DATE]
	end_date = context['params'][XCOM_END_DATE]

	if target_ymd and start_date and end_date:
		context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)
		context['ti'].xcom_push(XCOM_START_DATE, start_date)
		context['ti'].xcom_push(XCOM_END_DATE, end_date)
	else:
		# target_ymd, start_date, end_date 가 없으면 에러 발생
		logging.error(f'There is not "{XCOM_TARGET_YMD} or "{XCOM_START_DATE}" or "{XCOM_END_DATE}" in "params"')
		raise AirflowException('There is not "target_ymd", "start_date" or "end_date" in "params"')


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK}
Airflow Pool Slot: {POOL_SLOT_TRIVIAL_5}

Profile: {PROFILE}
Spark Submit Options: {_PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS]}

Params:
    target_ymd= {context['params'][XCOM_TARGET_YMD]}
    start_date= {context['params'][XCOM_START_DATE]}
    end_date= {context['params'][XCOM_END_DATE]}
------------------------------------------------------------------------------------------
''')


def _check_dup_run(**context):
	"""
	중복 실행을 방지하기 위한 방어 로직
	sparkling-s 에서 startDate, endDate 단위로 실행되므로 중복검사도 이를 통해 처리

	이미 startDate ~ endDate 를 포함하는 Job 이 존재하면 중복으로 판단
	즉, datetime 이 같더라도 startDate, endDate 가 다르다면 (완전히 포함되는 관계가 없다면) 중복으로 판단하지 않음

	:param context:
	:return:
	"""
	dag_run_id = context['dag_run'].run_id
	start_date = context['params'][XCOM_START_DATE]
	end_date = context['params'][XCOM_END_DATE]

	doc = job_dao.get_job({
		'type': _DAG_ID, 'running': 1,
		'detail.startDate': {'$lte': start_date},
		'detail.endDate': {'$gte': end_date}
	})

	if doc:
		running_dag_runs: List["DagRun"] = DagRun.find(dag_id=_DAG_ID, state=DagRunState.RUNNING)
		for running_dag_run in running_dag_runs:
			ti = running_dag_run.get_task_instance('setup')
			running_target_start_date = ti.xcom_pull(key=XCOM_START_DATE)
			running_target_end_date = ti.xcom_pull(key=XCOM_END_DATE)

			if dag_run_id != running_dag_run.run_id and start_date <= running_target_start_date and end_date >= running_target_end_date:
				logging.info(
					f'이미 실행 중인 DagRun 이 있음 ({running_dag_run.run_id} :: {running_target_start_date} ~ {running_target_end_date})')
				return False
		return True
	else:
		return True


def _upsert_job(**context):
	"""
	해당 시간대의 Job 추가 또는 running=1 로 업데이트
	중복검사에서처럼 datetime 대신 startDate, endDate 로 Job 을 구분

	:param context:
	:return:
	"""
	target_ymd = context['params'][XCOM_TARGET_YMD]
	start_date = context['params'][XCOM_START_DATE]
	end_date = context['params'][XCOM_END_DATE]

	filters = {'type': _DAG_ID, 'datetime': target_ymd, 'detail.startDate': start_date, 'detail.endDate': end_date}
	doc = job_dao.get_job(filters)

	if doc:
		update = {'$set': {'running': 1, 'modifiedAt': pendulum.now(tz=DEFAULT_TZ)}}
		job_dao.update_job(filters, update)
	else:
		doc = {
			'type': _DAG_ID,
			'datetime': target_ymd,
			'retryCnt': -1,
			'running': 1,
			'manual': 0 if context['dag_run'].run_type == DagRunType.SCHEDULED else 1,
			'detail': {
				'startDate': start_date,
				'endDate': end_date
			}
		}
		job_dao.insert_job(doc)
		logging.info(f'{_LOG_PREFIX} {target_ymd} Jobs 에 추가. {doc}')


def _clean_up_job(is_success: bool, context: dict):
	"""
	job 의 성공실패 여부에 따라 Jobs 컬렉션의 해당 다큐먼트 상태 변경

	성공인 경우, 반복을 피하기 위해 현재 수행한 startDate ~ endDate 에 포함되는 같은 type 의 대기 중인 job 을 함께 삭제

	:param context:
	:return:
	"""
	target_job_filter = {
		'type': _DAG_ID,
		'datetime': context['ti'].xcom_pull(key=XCOM_TARGET_YMD),
		'detail.startDate': context['params'][XCOM_START_DATE],
		'detail.endDate': context['params'][XCOM_END_DATE],
	}
	job_dao.clean_up_by_filter(target_job_filter, is_success)

	if is_success:
		dup_period_job_filter = {
			'type': _DAG_ID,
			'running': 0,
			'manual': 0,
			'detail.startDate': {'$gte': context['params'][XCOM_START_DATE]},
			'detail.endDate': {'$lte': context['params'][XCOM_END_DATE]},
		}
		job_dao.clean_up_by_filter(dup_period_job_filter, is_success)


def _get_app_args(context: dict):
	"""
	spark-submit 시, 넘겨줄 파라미터 정보
	:param context:
	:return:
	"""
	start_date = context['params'][XCOM_START_DATE]
	end_date = context['params'][XCOM_END_DATE]
	return [start_date, end_date]


with DAG(
		_DAG_ID,
		description='Invoke spark app for monitoring sales rate',
		tags=['spark', 'monitoring', 'sales_rate', 'monitoring_revenue', 'daily', 'mongo', 'ins.cho'],
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
			# 'email_on_retry': False,
			# 'retries': 3,
			# 'retry_delay': timedelta(minutes=30)
		},
		params={
			XCOM_TARGET_YMD: '',
			XCOM_START_DATE: '',
			XCOM_END_DATE: ''
		},
		start_date=pendulum.datetime(2025, 3, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	# 환경 설정
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 중복 실행 여부 확인
	check_dup_run = ShortCircuitOperator(
		task_id='check_dup_run',
		python_callable=_check_dup_run,
		on_failure_callback=partial(_clean_up_job, False),
	)

	# 해당 시간대의 Jobs.running=1 로 추가
	upsert_job = PythonOperator(
		task_id='upsert_job',
		python_callable=_upsert_job
	)

	task_group_id = 'spark_run_monitoring_sales_rate'
	spark_run_monitoring_sales_rate = create_task_group(
		task_group_id,
		tooltip=_SPARK_APP_CLASS,
		spark_pool=POOL_SPARK,
		pool_slot=POOL_SLOT_TRIVIAL_5,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': _SPARK_APP_CLASS,
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			'dag_id': _DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(_DAG_ID, "{{ run_id }}")}',
			'detail': {
				'startDate': f'{{{{ ti.xcom_pull(key="{XCOM_START_DATE}") }}}}',
				'endDate': f'{{{{ ti.xcom_pull(key="{XCOM_END_DATE}") }}}}'
			}
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS,
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT,
		wait_showup_failure_cb=partial(_clean_up_job, False),
		wait_complete_failure_cb=partial(_clean_up_job, False),
		conclude_app_failure_cb=partial(_clean_up_job, False),
		conclude_app_success_cb=partial(_clean_up_job, True)
	)

	setup >> check_dup_run >> upsert_job >> spark_run_monitoring_sales_rate
