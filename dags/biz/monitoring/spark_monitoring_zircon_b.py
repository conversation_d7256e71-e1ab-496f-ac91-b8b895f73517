"""
### 모니터링 지르콘B 스파크 집계

#### 1. 위키
- [19. 모니터링 지르콘B](https://wiki.navercorp.com/pages/viewpage.action?pageId=2380323492)

#### 2. 저장소
- hdfs : /user/gfp-data/monitoring/zircon/b/yyyy/mm/dd/monitoring_zircon_b_{yyyymmdd}.csv.gz

#### 3. config
- target_ymd : 처리할 날짜 YYYYMMDD ( default: D-3 )

#### 4. 타임아웃
- wait_for_zircon_b : 3시간 (10분 간격으로 retry)
"""

import os
import logging
import pendulum

from typing import List
from functools import partial
from datetime import timedelta

from airflow.utils.types import DagRunType
from airflow.utils.state import DagRunState
from airflow.utils.trigger_rule import TriggerRule
from airflow.models import DAG, DagRun
from airflow.exceptions import AirflowException, AirflowFailException
from airflow.sensors.python import PythonSensor
from airflow.operators.python import PythonOperator, BranchPythonOperator

from core import utils, c3
from core.base import DEFAULT_TZ, PROFILE, BATCH_SERVER_ADDRESS, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD, \
	PROFILE_LOCAL, PROFILE_DEV, PROFILE_TEST, PROFILE_STAGE, PROFILE_REAL, SPARK_SUBMIT_OPTIONS, \
	POOL_SPARK, SPARKLING_APP_JAR_PATH, SPARKLING_IMAGE, \
	SPARKLING_APP_HARD_LIMIT_1 as SPARKLING_APP_HARD_LIMIT

from core.spark_pool import POOL_SLOT_TRIVIAL_5
from core.spark_task_group import create_task_group
from core.spark_submit_op import invoke_job_with_args

from core.dao import job_dao
from core.dao.environment_dao import is_ready_zircon_b, get_environment_value_by_name, get_environment, set_environment

# DAG 기본 정보
DAG_ID = _DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [SPARK-MONITORING-ZIRCON-B]'

# Spark
_SPARK_APP_CLASS = 'com.navercorp.gfp.biz.monitoring.MonitoringZirconBAggregator'

# Profile
_PROFILE_SETTINGS = {
	PROFILE_TEST: {
		SPARK_SUBMIT_OPTIONS: """
			--num-executors 4
			--executor-cores 2
			--executor-memory 2g
			--conf spark.sql.shuffle.partitions=400
			--conf spark.sql.files.maxPartitionBytes=64mb
			--conf spark.hadoop.mapred.output.compress=true
			--conf spark.hadoop.mapred.output.compression.codec=org.apache.hadoop.io.compress.GzipCodec
			""".split()
	},
	PROFILE_REAL: {
		SPARK_SUBMIT_OPTIONS: """
			--num-executors 4
			--executor-cores 2
			--executor-memory 2g
			--conf spark.sql.shuffle.partitions=400
			--conf spark.sql.files.maxPartitionBytes=64mb
			""".split()
	}
}
_PROFILE_SETTINGS[PROFILE_LOCAL] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_DEV] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_STAGE] = _PROFILE_SETTINGS[PROFILE_REAL]

# 디폴트 3시간
_ZIRCON_B_CHECK_TIMEOUT = int(get_environment_value_by_name('zircon-b-check-timeout'))

# Batch URL
_REQ_URL = BATCH_SERVER_ADDRESS + '/batch/monitoring/zircon/b/send_mail?date={DATE}'


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	_init_settings(**context)
	_print_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
	:param context:
	:return:
	"""
	target_ymd = context['params'][XCOM_TARGET_YMD]

	if target_ymd:
		target_ymd = str(target_ymd).strip()
		pendulum.from_format(target_ymd, 'YYYYMMDD')

		context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)
	else:
		# target_ymd 가 없으면 에러
		logging.error(f'There is not "params.target_ymd"')
		raise AirflowException('There is not "params.target_ymd"')


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK}
Airflow Pool Slot: {POOL_SLOT_TRIVIAL_5}

Profile: {PROFILE}
Spark Submit Options: {_PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS]}

Params:
    target_ymd= {context['ti'].xcom_pull(key=XCOM_TARGET_YMD)}
------------------------------------------------------------------------------------------
''')


def _check_dup_run(**context):
	"""
	중복 실행을 방지하기 위한 방어 로직
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	dag_run_id = context['dag_run'].run_id
	doc = job_dao.get_job({'type': _DAG_ID, 'datetime': target_ymd, 'running': 1})

	if doc:
		running_dag_runs: List["DagRun"] = DagRun.find(dag_id=_DAG_ID, state=DagRunState.RUNNING)
		for running_dag_run in running_dag_runs:
			ti = running_dag_run.get_task_instance('setup')
			running_target_ymd = ti.xcom_pull(key=XCOM_TARGET_YMD)
			if dag_run_id != running_dag_run.run_id and target_ymd == running_target_ymd:
				raise AirflowException(f'이미 실행 중인 DagRun 이 있음 ({running_dag_run.run_id} :: {running_target_ymd})')
	else:
		pass


def _upsert_job(**context):
	"""
	해당 시간대의 Job 추가 또는 running=1 로 업데이트
	:param context:
	:return:
	"""
	target_ymd = context["ti"].xcom_pull(key=XCOM_TARGET_YMD)

	filters = {'type': _DAG_ID, 'datetime': target_ymd}
	doc = job_dao.get_job(filters)

	if doc:
		update = {'$set': {'running': 1, 'modifiedAt': pendulum.now(tz=DEFAULT_TZ)}}
		job_dao.update_job(filters, update)
	else:
		trigger_by = context['params'].get('trigger_by')
		doc = {
			'type': _DAG_ID,
			'datetime': target_ymd,
			'retryCnt': -1,
			'running': 1,
			'manual': 1 if trigger_by == DagRunType.MANUAL else 0
		}
		job_dao.insert_job(doc)
		logging.info(f'{_LOG_PREFIX} {target_ymd} Jobs 에 추가. {doc}')


def _wait_for_zircon_b(target_ymd):
	"""
	D-3 지르콘B 적재가 완료 되었는지 체크
	:param target_ymd: YYYYMMDD
	:return is_ready: Boolean
	"""
	is_ready = is_ready_zircon_b(target_ymd)

	logging.info(f'{_LOG_PREFIX} {target_ymd} 지르콘B 존재 유무 = {is_ready}')

	return is_ready


def _branch_request(target_ymd):
	"""
	모니터링 지르콘B 경로에
		_SUCCESS & csv.gz 파일이 있는 경우, 모니터링 알림 메일 발송 요청
		_SUCCESS 파일만 있는 경우, 성공 처리
		둘다 없는 경우, 실패 처리

		- /user/gfp-data/monitoring/zircon/b/yyyy/mm/dd/_SUCCESS
		- /user/gfp-data/monitoring/zircon/b/yyyy/mm/dd/monitoring_zircon_b_yyyymmdd.csv.gz
	"""
	ymd = pendulum.from_format(target_ymd, 'YYYYMMDD')

	base_path = f'/user/gfp-data/monitoring/zircon/b/{ymd.format("YYYY/MM/DD")}'
	success_file_path = f'{base_path}/_SUCCESS'
	csv_file_path = f'{base_path}/monitoring_zircon_b_{target_ymd}.csv.gz'

	logging.info(f'{_LOG_PREFIX} success_file_path= {success_file_path}, csv_file_path= {csv_file_path}')

	if c3.exists(success_file_path):
		if c3.exists(csv_file_path):
			return 'request_send_mail_monitoring_zircon_b'
		else:
			return 'update_monitoring_zircon_b_recent_ymd'
	else:
		raise AirflowFailException(f'{_LOG_PREFIX} _SUCCESS not exists')


def _request_send_mail_monitoring_zircon_b(**context):
	"""
	모니터링 지르콘B 알림 메일 발송 요청 (Batch 호출)
	"""
	target_ymd = context["ti"].xcom_pull(key=XCOM_TARGET_YMD)

	url = _REQ_URL.format(DATE=target_ymd)

	logging.info(f'{_LOG_PREFIX} url={url}')

	try:
		if PROFILE in [PROFILE_REAL, PROFILE_TEST]:
			# 모니터링 지르콘B 알림 메일 발송 요청
			res = utils.request(url)

			if res.get('code') != 200:
				raise AirflowException(f'모니터링 지르콘B 알림 메일 발송 요청 실패 ( res= {res} )')
		else:
			logging.info(f'{_LOG_PREFIX} PROFILE={PROFILE}, URL 호출하지 않음')
	except Exception as ex:
		raise AirflowException(
			f'fail to request send mail monitoring zircon b ( target_ymd= {target_ymd}, url= {url} ) \n ex= {ex}')


def _update_monitoring_zircon_b_recent_ymd(**context):
	"""
	모니터링 지르콘B 최근 적재 일자 갱신 (D-3)
		- recent_ymd <= target_ymd <= three_days_ago 인 경우만 갱신

	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)

	# 모니터링 지르콘B 최근 적재 일자
	recent_ymd = str(get_environment('monitoring-zircon-b-recent-ymd').get('value'))

	# D-3 일자
	three_days_ago = pendulum.now(DEFAULT_TZ).subtract(days=3).format('YYYYMMDD')

	# target_ymd 가 최근 적재 일자 이후이면서 D-3 이전인 경우
	if recent_ymd <= target_ymd <= three_days_ago:
		# 모니터링 지르콘B 최근 적재 일자 갱신 (modifiedAt 갱신 포함)
		set_environment('monitoring-zircon-b-recent-ymd', target_ymd)


def _clean_up_to_success(**context):
	"""
	성공했으므로 job 삭제
	:param context:
	:return:
	"""
	job_dao.clean_up(_DAG_ID, context["ti"].xcom_pull(key=XCOM_TARGET_YMD), True)


def _clean_up_to_failure(context: dict):
	"""
	실패했으므로 retryCnt 증가시키고 running=0 설정
	:param context:
	:return:
	"""
	job_dao.clean_up(_DAG_ID, context["ti"].xcom_pull(key=XCOM_TARGET_YMD), False)


def _get_app_args(context: dict):
	"""
	spark-submit 시, 넘겨줄 파라미터 정보
	:param context:
	:return:
	"""
	target_ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
	logging.info(f'{_LOG_PREFIX} target_ymd= {target_ymd}')
	return [target_ymd]


with DAG(
		_DAG_ID,
		description='Invoke spark app for monitoring zircon b',
		tags=['spark', 'monitoring', 'zircon_b', 'monitoring_zircon_b', 'daily', 'hdfs', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
			# 'email_on_retry': False,
			# 'retries': 3,
			# 'retry_delay': timedelta(minutes=30)
		},
		params={
			XCOM_TARGET_YMD: '',
			'trigger_by': '',
		},
		start_date=pendulum.datetime(2024, 5, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	# 환경 설정
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 중복 실행 여부 확인
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=_check_dup_run,
	)

	# 해당 시간대의 Jobs.running=1 로 추가
	upsert_job = PythonOperator(
		task_id='upsert_job',
		python_callable=_upsert_job
	)

	# D-3 지르콘B 적재가 완료 되었는지 체크
	wait_for_zircon_b = PythonSensor(
		task_id='wait_for_zircon_b',
		python_callable=_wait_for_zircon_b,
		op_args=[f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}'],
		poke_interval=60 * 10,  # 10분마다 재시도
		execution_timeout=timedelta(seconds=_ZIRCON_B_CHECK_TIMEOUT),  # 3시간 동안
		on_failure_callback=partial(_clean_up_to_failure)
	)

	task_group_id = 'spark_run_monitoring_zircon_b'
	spark_run_monitoring_zircon_b = create_task_group(
		task_group_id,
		tooltip=_SPARK_APP_CLASS,
		spark_pool=POOL_SPARK,
		pool_slot=POOL_SLOT_TRIVIAL_5,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': _SPARK_APP_CLASS,
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
			'dag_id': _DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(_DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS,
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT,
		wait_showup_failure_cb=partial(_clean_up_to_failure),
		wait_complete_failure_cb=partial(_clean_up_to_failure),
		conclude_app_failure_cb=partial(_clean_up_to_failure)
	)

	# _SUCCESS & csv.gz 파일 유무에 따라, 알림 메일 발송 요청을 할 것인지, 완료 처리할 것인지
	branch_request = BranchPythonOperator(
		task_id='branch_request',
		python_callable=_branch_request,
		op_args=[f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}'],
		on_failure_callback=partial(_clean_up_to_failure)
	)

	# 배치 서버 호출
	request_send_mail_monitoring_zircon_b = PythonOperator(
		task_id='request_send_mail_monitoring_zircon_b',
		python_callable=_request_send_mail_monitoring_zircon_b,
		on_failure_callback=partial(_clean_up_to_failure),
	)

	# 모니터링 지르콘B 최근 적재 일자 갱신 (D-3)
	update_monitoring_zircon_b_recent_ymd = PythonOperator(
		task_id='update_monitoring_zircon_b_recent_ymd',
		python_callable=_update_monitoring_zircon_b_recent_ymd,
		trigger_rule=TriggerRule.NONE_FAILED,
		on_failure_callback=partial(_clean_up_to_failure),
	)

	# 성공 시 Jobs 다큐먼트 삭제
	clean_up_to_success = PythonOperator(
		task_id='clean_up_to_success',
		python_callable=_clean_up_to_success
	)

	setup >> check_dup_run >> upsert_job >> wait_for_zircon_b >> spark_run_monitoring_zircon_b >> branch_request
	branch_request >> [request_send_mail_monitoring_zircon_b, update_monitoring_zircon_b_recent_ymd]
	request_send_mail_monitoring_zircon_b >> update_monitoring_zircon_b_recent_ymd >> clean_up_to_success
