"""
### NCC ITAC 감사 대응 용 리포트 API 연동 통계 생성

#### 1. 위키
- [...](...)

#### 2. config
- target_ymd : 모니터링 요청을 보낸 날짜 YYYYMMDD

#### 3. 타임아웃
- 배치서버 요청에 대해 30분

#### 4. 주기
- 매 시 15분
"""

import logging
import os
from functools import partial
from typing import List
from urllib.parse import urlencode

import pendulum
from airflow import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator, ShortCircuitOperator
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType

from core import utils
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, PROFILE, XCOM_END_DATE, XCOM_START_DATE, XCOM_TARGET_YMD, \
	BATCH_SERVER_ADDRESS, PROFILE_REAL
from core.dao import job_dao

# DAG 기본 정보
DAG_ID = _DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [MAKE_NCC_STATS]'

# Batch URL
_REQ_URL = BATCH_SERVER_ADDRESS + '/batch/monitoring/reportapistats/ncc'


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	logical_date = context["logical_date"].in_tz(DEFAULT_TZ)
	ymd = logical_date.format("YYYYMMDD") if logical_date.hour == 23 \
		else logical_date.subtract(days=1).format("YYYYMMDD")
	logging.info(f'{_LOG_PREFIX} 처리해야 할 날짜={ymd}')

	context['ti'].xcom_push(XCOM_TARGET_YMD, ymd)

	_print_settings(**context)


def _make_ncc_report_api_stats():
	logging.info(f'{_LOG_PREFIX} url={_REQ_URL}')
	res = utils.request(_REQ_URL, timeout=60 * 30)  # 30분 타임아웃
	return res


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Profile: {PROFILE}
Request URL: {_REQ_URL}
Params:
    ymd= {context['ti'].xcom_pull(key=XCOM_TARGET_YMD)}
------------------------------------------------------------------------------------------
''')


with DAG(
		_DAG_ID,
		description='NCC ITAC 감사 대응 용 리포트 API 연동 통계 생성',
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			XCOM_TARGET_YMD: '',
		},
		start_date=pendulum.datetime(2025, 6, 1, tz=DEFAULT_TZ),
		schedule_interval='15 * * * *',  # 매 시 15분
		tags=['make', 'batch', 'monitoring', 'ncc', 'report_api', 'juyoun.kim'],
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	# 환경 설정
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 통계 생성
	make_ncc_report_api_stats = PythonOperator(
		task_id='make_ncc_report_api_stats',
		python_callable=_make_ncc_report_api_stats,
	)

	# 파이프라인
	setup >> make_ncc_report_api_stats
