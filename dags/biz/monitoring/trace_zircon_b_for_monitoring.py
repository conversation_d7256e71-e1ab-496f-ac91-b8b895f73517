"""
### ZirconTrace 에 의한 모니터링 지르콘B 재처리

#### 0. 위키
- [19. 모니터링 지르콘B](https://wiki.navercorp.com/pages/viewpage.action?pageId=2380323492)

#### 1. 주기
- 리얼 환경 : 매일 08시, 12시, 16시, 20시
	- 테스트 환경 OFF 처리
- monitoring-zircon-b-trace-recent-reprocess-ymdh = yyyymmddhh
	- 모니터링 지르콘B Trace 최근 재처리 시각

#### 2. ZirconTrace 콜렉션
- 모니터링 지르콘B 완료 일자 이전(include)을 재처리 대상으로 본다.

#### 3. Jobs 콜렉션
- ZirconB 재처리 이력이 있는 경우, Jobs 콜렉션에 추가

#### 4. max_active_runs=1
"""

import logging
import os

import pendulum
from datetime import timedelta

from airflow.models import DAG, DagRun
from airflow.utils.state import DagRunState
from airflow.utils.trigger_rule import TriggerRule
from airflow.sensors.python import PythonSensor
from airflow.operators.python import PythonOperator, BranchPythonOperator

from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD, XCOM_TARGET_YMD_LIST
from core.dao import zircon_trace_dao, job_dao
from core.dao.environment_dao import get_environment_value_by_name, get_environment, set_environment

from biz.monitoring.spark_monitoring_zircon_b import DAG_ID as SPARK_DAG_ID

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRACE-ZIRCON-B-FOR-MONITORING]'

_XCOM_CURRENT_YMDH = 'current_ymdh'

# 디폴트 3시간
_CHECK_TIMEOUT = 10800

# 0 8,12,16,20 * * *
_SCHEDULE = str(get_environment_value_by_name('monitoring-zircon-b-trace-schedule'))


def _get_zircon_trace(**context):
	"""
	ZirconTrace 재처리 이력 조회
		- 모니터링 지르콘B 의 최근 적재 일자 이전 일자 대상 ( 즉, 정규 DAG 처리 전인 경우 제외 )
			- D-3 정규 DAG 처리 전인 경우, D-4 이전 일자 대상 ( 즉, 모니터링 지르콘B 의 최근 적재 일자 = D-4 )
			- D-3 정규 DAG 처리 후인 경우, D-3 이전 일자 대상
		- 최근 적재 일자 이전인 경우 ( target_ymd < recent_ymd )
			- zirconBSucceededAt 이 모니터링 지르콘B Trace 최근 재처리 시각 이후(include)이면서 현재 시각 이전(exclude)인 경우 대상임
				- 모니터링 지르콘B Trace 최근 재처리 시각 = monitoring-zircon-b-trace-recent-reprocess-ymdh = 2024052211
				- ex > 최근 재처리 시각이 2024052211 이고, 현재 재처리 시작 시각이 2024052301 인 경우
						2024-05-22T11:00:00.000 (include) ~ 2024-05-23T01:00:00.000 (exclude) 건들이 Jobs 추가 대상 이다.
		- 최근 적재 일자인 경우 ( target_ymd == recent_ymd )
			- zirconBSucceededAt 이 최근 적재 일자 수정일(modifiedAt) 이후이면서 현재 시각 이전(exclude)인 경우 대상임

	:param context:
	:return:
	"""
	# 현재 재처리 시작 시각
	current_ymdh = pendulum.now(DEFAULT_TZ).format('YYYYMMDDHH')
	current_dt = pendulum.from_format(current_ymdh, 'YYYYMMDDHH', tz=DEFAULT_TZ)

	# 현재 재처리 시작 시각 XCOM push
	context['ti'].xcom_push(_XCOM_CURRENT_YMDH, current_ymdh)

	logging.info(f'{_LOG_PREFIX} current_ymdh = {current_ymdh}')
	logging.info(f'{_LOG_PREFIX} current_dt = {current_dt}')


	# 모니터링 지르콘B 최근 적재 일자
	mzb_recent_ymd_doc = get_environment('monitoring-zircon-b-recent-ymd')
	mzb_recent_ymd = str(mzb_recent_ymd_doc.get('value'))
	# 모니터링 지르콘B 최근 적재 일자가 만들어진 시각 (최근 적재 일자에 해당 하는 이력 중에서, 최근 적재 일자가 만들어진 시각 이전에 들어온 이력들은 재처리에서 제외하기 위함)
	mzb_recent_ymd_modified_at = pendulum.instance(mzb_recent_ymd_doc.get('modifiedAt')).in_tz(DEFAULT_TZ)

	# 모니터링 지르콘B Trace 최근 재처리 시각
	trace_recent_ymdh = str(get_environment_value_by_name('monitoring-zircon-b-trace-recent-reprocess-ymdh'))
	trace_recent_dt = pendulum.from_format(trace_recent_ymdh, 'YYYYMMDDHH', tz=DEFAULT_TZ)

	logging.info(f'{_LOG_PREFIX} mzb_recent_ymd = {mzb_recent_ymd}')
	logging.info(f'{_LOG_PREFIX} mzb_recent_ymd_modified_at = {mzb_recent_ymd_modified_at}')
	logging.info(f'{_LOG_PREFIX} trace_recent_ymdh = {trace_recent_ymdh}')
	logging.info(f'{_LOG_PREFIX} trace_recent_dt = {trace_recent_dt}')

	match = {
		'$or': [
			{
				'date': {'$lt': mzb_recent_ymd},
				'normalConfirm': 1,
				'zirconBSucceededAt': {'$gte': trace_recent_dt, '$lt': current_dt},
			},
			{
				# 최근 적재 일자의 경우, 최근 적재 일자의 모니터링 집계가 만들어진 시각보다 이전에 지르콘 재처리된 건들은 모니터링 집계 재처리에서 제외함
				'date': mzb_recent_ymd,
				'normalConfirm': 1,
				'zirconBSucceededAt': {'$gte': mzb_recent_ymd_modified_at if mzb_recent_ymd_modified_at > trace_recent_dt else trace_recent_dt, '$lt': current_dt},
			},
		],
	}

	# ZirconTrace 조회
	zircon_traces = zircon_trace_dao.get_traces(match, projection={'date': True})

	# ZirconTrace 는 매체별/AP별로 남기고 있어서, 동일한 date 가 여러개 나올 수 있으므로, set 으로 중복 제거함
	target_ymd_list = sorted(list(set([zircon_trace['date'] for zircon_trace in zircon_traces])))

	logging.info(f'{_LOG_PREFIX} 처리할 zircon trace 개수 = {len(target_ymd_list)}')

	if target_ymd_list:
		context['ti'].xcom_push(XCOM_TARGET_YMD_LIST, target_ymd_list)

		return 'is_job_complete'
	else:
		logging.info(f'{_LOG_PREFIX} 처리할 zircon trace 없음')

		return 'update_monitoring_zircon_b_trace_reprocess_ymdh'


def _is_job_complete(**context):
	"""
	재처리 대상 일자 중에 이미 처리 중인 건이 있다면, 완료될 때까지 대기
		- 같은 날짜가 꼬이지 않도록 완료 후 재처리함

	:param context:
	:return is_complete: Boolean
	"""
	target_ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)

	logging.info(f'{_LOG_PREFIX} 처리 대상 일자 = {target_ymd_list}')

	for _, ymd in enumerate(target_ymd_list):
		logging.info(f'{_LOG_PREFIX} target_ymd= {ymd}')

		# 이미 처리 중인 job 이 있는지
		job = job_dao.get_job({'type': SPARK_DAG_ID, 'datetime': ymd, 'running': 1})
		if job:
			running_dag_runs = DagRun.find(dag_id=SPARK_DAG_ID, state=DagRunState.RUNNING)
			for running_dag_run in running_dag_runs:
				ti = running_dag_run.get_task_instance('setup')
				running_target_ymd = ti.xcom_pull(key=XCOM_TARGET_YMD)
				if ymd == running_target_ymd:
					logging.info(f'{_LOG_PREFIX} 처리 중인 일자 = {ymd}')
					return False

	return True


def _insert_monitoring_zircon_b_jobs(**context):
	"""
	ZirconB 재처리에 의한 모니터링 지르콘B 재처리 job 추가
	:param context:
	:return:
	"""
	target_ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)

	logging.info(f'{_LOG_PREFIX} 처리 대상 일자 = 총 {len(target_ymd_list)} 건')

	inserted_cnt = 0
	for ymd in target_ymd_list:
		filter = {'type': SPARK_DAG_ID, 'datetime': ymd, 'manual': 0}
		job = job_dao.get_job(filter)

		# job 이 이미 있는 경우, retryCnt, modifiedAt 리셋
		if job:
			job_dao.update_job(filter, {'$set': {'retryCnt': 0, 'modifiedAt': pendulum.now(DEFAULT_TZ)}})
		else:
			# job 이 없다면 추가
			job_dao.insert_job({
				'type': SPARK_DAG_ID,
				'datetime': ymd,
				'manual': 0,
				'running': 0,
				'retryCnt': 0,  # 재처리는 0으로 설정. -1은 정규 또는 수동 처리 에서만 사용.
			})
			inserted_cnt = inserted_cnt + 1

	logging.info(f'{_LOG_PREFIX} Jobs 에 추가 완료 ( {inserted_cnt} 건 )')


def _update_monitoring_zircon_b_trace_reprocess_ymdh(**context):
	"""
	모니터링 지르콘B Trace 최근 재처리 시각을 현재 재처리 시작 시각으로 갱신
	:param context:
	:return:
	"""
	# 현재 재처리 시작 시각
	current_ymdh = context['ti'].xcom_pull(key=_XCOM_CURRENT_YMDH)

	# 모니터링 지르콘B Trace 최근 재처리 시각 갱신
	set_environment('monitoring-zircon-b-trace-recent-reprocess-ymdh', current_ymdh)


with DAG(
		_DAG_ID,
		description='ZirconTrace 에 의한 모니터링 지르콘B 재처리 Jobs 추가 DAG',
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2024, 4, 1, tz=DEFAULT_TZ),
		schedule_interval=_SCHEDULE,  # 0 8,12,16,20 * * *
		tags=['trace', 'zircon_b', 'monitoring', 'monitoring_zircon_b', 'bitna.cho'],
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# ZirconB 재처리 이력 조회
	get_zircon_trace = BranchPythonOperator(
		task_id='get_zircon_trace',
		python_callable=_get_zircon_trace,
	)

	# 이미 처리 중인 날짜가 있다면, 끝날 때까지 대기
	is_job_complete = PythonSensor(
		task_id='is_job_complete',
		python_callable=_is_job_complete,
		poke_interval=60 * 10,  # 10분 마다 재시도
		execution_timeout=timedelta(seconds=_CHECK_TIMEOUT),  # 3시간 동안
	)

	# ZirconB 재처리에 의한 모니터링 지르콘B 재처리 job 추가
	insert_monitoring_zircon_b_jobs = PythonOperator(
		task_id='insert_monitoring_zircon_b_jobs',
		python_callable=_insert_monitoring_zircon_b_jobs,
	)

	# 모니터링 지르콘B Trace 최근 재처리 시각을 현재 재처리 시작 시각으로 갱신
	update_monitoring_zircon_b_trace_reprocess_ymdh = PythonOperator(
		task_id='update_monitoring_zircon_b_trace_reprocess_ymdh',
		trigger_rule=TriggerRule.NONE_FAILED,
		python_callable=_update_monitoring_zircon_b_trace_reprocess_ymdh,
	)

	# 파이프라인
	get_zircon_trace >> [is_job_complete, update_monitoring_zircon_b_trace_reprocess_ymdh]
	is_job_complete >> insert_monitoring_zircon_b_jobs >> update_monitoring_zircon_b_trace_reprocess_ymdh
