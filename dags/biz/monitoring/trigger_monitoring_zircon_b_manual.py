"""
### 모니터링 지르콘B 수동 트리거 DAG

#### 0. 위키
- [19. 모니터링 지르콘B](https://wiki.navercorp.com/pages/viewpage.action?pageId=2380323492)

#### 1. 주기
- 없음

#### 2. 스케쥴 콜렉션 : Jobs
- manual=1 인 경우만 처리 대상임

#### 3. 트리거 대상 DAG
- spark_monitoring_zircon_b

#### 4. config
- target_ymd_list : 처리할 스케쥴 날짜 리스트 (YYYYMMDD,YYYYMMDD,YYYYMMDD)
"""

import os
import logging
import pendulum

from time import sleep
from datetime import timedelta

from airflow.models import DAG, DagRun
from airflow.utils.email import send_email
from airflow.utils.types import DagRunType
from airflow.utils.state import DagRunState
from airflow.exceptions import AirflowException
from airflow.operators.python import PythonOperator

from core.dao import job_dao
from core.airflow_api import trigger_dagrun
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, XCOM_TARGET_YMD_LIST

from biz.monitoring.spark_monitoring_zircon_b import DAG_ID as SPARK_DAG_ID

# DAG 기본정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRIGGER-MONITORING-ZIRCON-B-MANUAL]'


def _setup(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화

	:param context:
	:return:
	"""
	target_ymd_list_str = context['params'].get(XCOM_TARGET_YMD_LIST)

	target_ymd_list = []

	if target_ymd_list_str:
		target_ymd_list_str = target_ymd_list_str.replace(' ', '').strip(',')

		# target_ymd_list 에 추가
		target_ymd_list.extend(target_ymd_list_str.split(','))

		for i, ymd in enumerate(target_ymd_list):
			pendulum.from_format(ymd, 'YYYYMMDD')

		logging.info(f'{_LOG_PREFIX} target_ymd_list_str = {target_ymd_list_str}')
	else:
		raise AirflowException(f'"target_ymd_list"가 기술 되지 않음')

	context['ti'].xcom_push(XCOM_TARGET_YMD_LIST, target_ymd_list)


def _trigger_monitoring_zircon_b(**context):
	"""
	target_ymd_list 에 있는 날짜에 대해 스파크 집계 처리함

	:param context:
	:return:
	"""

	# 스킵, 실패한 스케쥴 리스트
	skipped_list = []
	failed_list = []

	target_ymd_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMD_LIST)

	logging.info(f'{_LOG_PREFIX} target_ymd_list={target_ymd_list}')

	count = len(target_ymd_list)
	for idx, ymd in enumerate(target_ymd_list):
		logging.info(f'{_LOG_PREFIX} [{idx + 1}/{count}] ymd={ymd}')

		# 이미 job이 있는지
		job = job_dao.get_job({'type': SPARK_DAG_ID, 'datetime': ymd})
		if job:
			if job['manual'] == 0:
				# 자동인 경우, 처리 대상 아님
				skipped_list.append((ymd, '스케쥴 DAG 에 의해 처리 예정'))
				logging.warning(f'{_LOG_PREFIX} [{idx + 1}/{count}] ymd={ymd}. 스케쥴 DAG 에 의해 처리 예정')

				continue
			else:
				# 수동인 경우, 이미 실행 중인지 확인
				if job['running'] == 1:
					skipped_list.append((ymd, '이미 처리 중'))
					logging.warning(f'{_LOG_PREFIX} [{idx + 1}/{count}] ymd={ymd}, running=1. 이미 처리 중이므로 스킵')

					continue

		logging.info(f'{_LOG_PREFIX} [{idx + 1}/{count}] ymd={ymd}. 진행중')

		# 해당 날짜의 monitoring zircon b 생성 요청
		dag_run_id = trigger_dagrun(SPARK_DAG_ID, pendulum.now(DEFAULT_TZ), {'target_ymd': ymd, 'trigger_by': DagRunType.MANUAL}, DagRunType.MANUAL)
		logging.info(f'{_LOG_PREFIX} 스파크 집계 트리거 ( ymd={ymd}, target_dag_id={SPARK_DAG_ID}, dag_run_id={dag_run_id} )')

		# dag run 이 끝났는지 60초 간격으로 확인
		while True:
			sleep(60)
			dag_runs = DagRun.find(dag_id=SPARK_DAG_ID, run_id=dag_run_id)
			if dag_runs:
				dag_run: DagRun = dag_runs.pop()

				logging.info(f'{_LOG_PREFIX} {SPARK_DAG_ID}({dag_run_id}) state = {dag_run.get_state()}')
				if dag_run.get_state() == DagRunState.SUCCESS:
					break
				elif dag_run.get_state() == DagRunState.FAILED:
					failed_list.append((ymd, f'dag_run_id={dag_run_id}'))
					break
			else:
				failed_list.append((ymd, f'dag_run_id={dag_run_id} 트리거 시켰으나, 존재하지 않음'))
				logging.info(f'{_LOG_PREFIX} {SPARK_DAG_ID}({dag_run_id}) 트리거 시켰으나, 존재하지 않음')
				break

	logging.info(f'{_LOG_PREFIX} 완료')

	context['ti'].xcom_push('skipped_list', skipped_list)
	context['ti'].xcom_push('failed_list', failed_list)


def _alert(**context):
	"""
	스킵 또는 실패된 리스트 알림
	:param context:
	:return:
	"""
	skipped_list = context['ti'].xcom_pull(key='skipped_list')
	skipped_msg = f'skipped {len(skipped_list)} 건 <br/>'

	for item in skipped_list:
		skipped_msg += f'- ymd={item[0]}, reason={item[1]}<br/>'

	failed_list = context['ti'].xcom_pull(key='failed_list')
	failed_msg = f'failed {len(failed_list)} 건 <br/>'
	for item in failed_list:
		failed_msg += f'- ymd={item[0]}, reason={item[1]}<br/>'

	if skipped_list or failed_list:
		title = f'[monitoring zircon b 수동 처리] skipped {len(skipped_list)} 건, failed {len(failed_list)} 건'
		body = f'{skipped_msg}<br/>{failed_msg}<br/>'
		send_email(ALERT_EMAIL_ADDRESSES, title, body)


with DAG(
		_DAG_ID,
		description='Trigger manual monitoring zircon b DAG',
		tags=['trigger', 'manual', 'monitoring', 'zircon_b', 'monitoring_zircon_b', 'daily', 'hdfs', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			'target_ymd_list': '',
		},
		start_date=pendulum.datetime(2024, 5, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup
	)

	trigger_monitoring_zircon_b = PythonOperator(
		task_id=f'trigger_monitoring_zircon_b',
		python_callable=_trigger_monitoring_zircon_b,
		retries=3,
		retry_delay=timedelta(seconds=60)
	)

	alert = PythonOperator(
		task_id='alert',
		python_callable=_alert,
	)

	setup >> trigger_monitoring_zircon_b >> alert
