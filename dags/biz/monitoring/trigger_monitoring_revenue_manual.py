"""
### 수익률 모니터링 수동 트리거 DAG
### 정규/재처리 trigger 와 다르게 각 type 별 job 을 순차실행

#### 0. 위키
- [25-3. 수익률 모니터링 수동 처리](https://wiki.navercorp.com/spaces/GFP/pages/3263714692)

#### 1. 주기
- 없음

#### 2. 스케쥴 콜렉션 : Jobs
- manual=1 인 경우만 처리 대상임

#### 3. 트리거 대상 DAG
- spark_monitoring_sales_rate
- spark_monitoring_profit_rate
- request_monitoring_revenue

#### 4. config
- target_jobs : 실행할 job type 이 속한 list (default: [sales, profit, request])
- start_date : 집계에 포함할 처음 날짜 YYYYMMDD
- end_date : 집계에 포함할 마지막 날짜 YYYYMMDD
"""

import logging
import os
from datetime import timedelta
from time import sleep

import pendulum
from airflow.exceptions import AirflowException
from airflow.models import DAG, DagRun
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType

from biz.monitoring.request_monitoring_revenue import DAG_ID as REQUEST_DAG_ID
from biz.monitoring.spark_monitoring_profit_rate import DAG_ID as SPARK_DAG_ID_PROFIT
from biz.monitoring.spark_monitoring_sales_rate import DAG_ID as SPARK_DAG_ID_SALES
from core.airflow_api import trigger_dagrun
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, XCOM_START_DATE, XCOM_END_DATE
from core.dao import job_dao

# DAG 기본정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRIGGER-MONITORING-REVENUE-MANUAL]'

_XCOM_TARGET_JOBS = 'target_jobs'

JOB_TYPE_DAG_MAPPING = {
	'sales': SPARK_DAG_ID_SALES,
	'profit': SPARK_DAG_ID_PROFIT,
	'request': REQUEST_DAG_ID
}


def _setup(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화

	:param context:
	:return:
	"""
	job_types = context['params'][_XCOM_TARGET_JOBS]
	start_date = context['params'][XCOM_START_DATE]
	end_date = context['params'][XCOM_END_DATE]

	if job_types and start_date and end_date:
		# 배치서버 요청을 해야한다면 가장 마지막에 해야하므로 순서 변경
		job_types.sort(key=lambda job_type: JOB_TYPE_DAG_MAPPING[job_type] == REQUEST_DAG_ID)
		context['ti'].xcom_push(_XCOM_TARGET_JOBS, [JOB_TYPE_DAG_MAPPING[job_type] for job_type in job_types])
		context['ti'].xcom_push(XCOM_START_DATE, start_date)
		context['ti'].xcom_push(XCOM_END_DATE, end_date)
	else:
		# job_types, start_date, end_date 가 없으면 에러 발생
		logging.error(f'There is not "{_XCOM_TARGET_JOBS}" or "{XCOM_START_DATE}" or "{XCOM_END_DATE}" in "params"')
		raise AirflowException('There is not "target_jobs" or "start_date" or "end_date" in "params"')


def _trigger_monitoring_revenue(**context):
	"""
	start_date, end_date 를 파라미터로 집계, 모니터링 DAG 를 트리거

	:param context:
	:return:
	"""

	# 스킵, 실패한 스케쥴 리스트
	skipped_list = []
	failed_list = []

	target_job_types = context['ti'].xcom_pull(key=_XCOM_TARGET_JOBS)
	start_date = context['ti'].xcom_pull(key=XCOM_START_DATE)
	end_date = context['ti'].xcom_pull(key=XCOM_END_DATE)

	logging.info(f'{_LOG_PREFIX} target_job_types= {target_job_types} start_date= {start_date} end_date= {end_date}')

	count = len(target_job_types)
	for idx, job_type in enumerate(target_job_types):
		logging.info(f'{_LOG_PREFIX} [{idx + 1}/{count}] job_type= {job_type}')

		# 수동 실행할 기간을 포함하는 job이 이미 있는지 확인
		job = job_dao.get_job({
			'type': job_type,
			'detail.startDate': {'$lte': start_date},
			'detail.endDate': {'$gte': end_date}
		})
		if job:
			if job['manual'] == 0:
				# 자동인 경우, 처리 대상 아님
				skipped_list.append((job_type, f'{start_date}~{end_date}', '스케쥴 DAG 에 의해 처리 예정'))
				logging.warning(f'{_LOG_PREFIX} [{idx + 1}/{count}] type= {job_type}. 스케쥴 DAG 에 의해 처리 예정')

				continue
			else:
				# 수동인 경우, 이미 실행 중인지 확인
				if job['running'] == 1:
					skipped_list.append((job_type, f'{start_date}~{end_date}', '이미 처리 중'))
					logging.warning(f'{_LOG_PREFIX} [{idx + 1}/{count}] type= {job_type}, running=1. 이미 처리 중이므로 스킵')

					continue

		logging.info(f'{_LOG_PREFIX} [{idx + 1}/{count}] type= {job_type}. 진행중')

		# 해당 날짜의 monitoring revenue 생성 요청
		dag_run_id = trigger_dagrun(job_type, pendulum.now(DEFAULT_TZ), {
			# 본 DAG 의 실질적인 집계/모니터링에 쓰이는 날짜 정보는 start_date, end_date 이다.
			# datetime (= target_ymd) 는 Jobs 의 'type' 필드와 함께 index 중 하나인데,
			# 수동처리로, start_date, end_date 은 다르나, 같은 type 의 job 을 여러개 실행하기 위해서는 datetime 이 달라야 한다.
			# 이를 위해 datetime 을 초 단위까지 늘려 생성하여 중복을 최대한 피한다. (datetime='20250308T050617')
			'target_ymd': pendulum.instance(context['logical_date']).in_tz(DEFAULT_TZ).format('YYYYMMDDTHHmmss'),
			'start_date': start_date,
			'end_date': end_date
		}, DagRunType.MANUAL)
		logging.info(
			f'{_LOG_PREFIX} 스파크 집계 트리거 ( target_dag_id={job_type}, dag_run_id={dag_run_id} )')

		# dag run 이 끝났는지 60초 간격으로 확인
		while True:
			sleep(60)
			dag_runs = DagRun.find(dag_id=job_type, run_id=dag_run_id)
			if dag_runs:
				dag_run: DagRun = dag_runs.pop()

				logging.info(f'{_LOG_PREFIX} {job_type}({dag_run_id}) state= {dag_run.get_state()}')
				if dag_run.get_state() == DagRunState.SUCCESS:
					break
				elif dag_run.get_state() == DagRunState.FAILED:
					failed_list.append((job_type, f'{start_date}~{end_date}', f'dag_run_id={dag_run_id}'))
					break
			else:
				failed_list.append((job_type, f'{start_date}~{end_date}', f'dag_run_id={dag_run_id} 트리거 시켰으나, 존재하지 않음'))
				logging.info(f'{_LOG_PREFIX} {job_type}({dag_run_id}) 트리거 시켰으나, 존재하지 않음')
				break

	logging.info(f'{_LOG_PREFIX} 완료')

	context['ti'].xcom_push('skipped_list', skipped_list)
	context['ti'].xcom_push('failed_list', failed_list)


def _alert(**context):
	"""
	스킵 또는 실패된 리스트 알림
	:param context:
	:return:
	"""
	skipped_list = context['ti'].xcom_pull(key='skipped_list')
	skipped_msg = f'skipped {len(skipped_list)} 건 <br/>'

	for item in skipped_list:
		skipped_msg += f'- job_type= {item[0]}, period= {item[1]}, reason={item[2]}<br/>'

	failed_list = context['ti'].xcom_pull(key='failed_list')
	failed_msg = f'failed {len(failed_list)} 건 <br/>'
	for item in failed_list:
		failed_msg += f'- job_type= {item[0]}, period= {item[1]}, reason={item[2]}<br/>'

	if skipped_list or failed_list:
		title = f'[monitoring revenue 수동 처리] skipped {len(skipped_list)} 건, failed {len(failed_list)} 건'
		body = f'{skipped_msg}<br/>{failed_msg}<br/>'
		send_email(ALERT_EMAIL_ADDRESSES, title, body)

		if len(failed_list):
			raise AirflowException(f'{_LOG_PREFIX} 수익률 모니터링 수동 실행 중에 실패한 작업이 존재합니다.')


with DAG(
		_DAG_ID,
		description='Trigger manual monitoring revenue DAG',
		tags=['trigger', 'manual', 'monitoring', 'sales', 'profit', 'monitoring_revenue', 'mongo', 'ins.cho'],
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			_XCOM_TARGET_JOBS: ['sales', 'profit', 'request'],
			XCOM_START_DATE: '',
			XCOM_END_DATE: ''
		},
		start_date=pendulum.datetime(2025, 3, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup
	)

	trigger_monitoring_revenue = PythonOperator(
		task_id=f'trigger_monitoring_revenue',
		python_callable=_trigger_monitoring_revenue,
		retries=1,
		retry_delay=timedelta(seconds=60)
	)

	alert = PythonOperator(
		task_id='alert',
		python_callable=_alert,
	)

	setup >> trigger_monitoring_revenue >> alert
