"""
### 비즈 성과 분석 리포트 정규 트리거 DAG

#### 위키
- [16-1. 비즈 성과 분석 리포트 생성 정규 처리](https://wiki.navercorp.com/pages/viewpage.action?pageId=2196977299)

#### 주기 및 스케줄
- 매일 06시
- Data DB : Jobs
  - 가장 과거 날짜가 끝나기 전까지는 다음 날짜를 처리하지 못한다.
- max_active_runs=1

#### 트리거 대상 DAG
- spark_biz_analysis_report

#### config
- 수동 입력 없음 (아래를 대상으로 실행)
  - target_ymd: 어제 날짜
"""

import logging
import os

import pendulum
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.types import DagRunType

from biz.biz_analysis.spark_biz_analysis_report import _DAG_ID as SPARK_DAG_ID
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD

_DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [TRG-BIZ-ANLYS-REPORT-REGULAR]'


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정

	:param context:
	"""
	_set_target_ymd(**context)


def _set_target_ymd(**context):
	"""
	regular dag 이므로 스케줄에 의한 파라미터로 결정

	:param context:
	:return:
	"""
	target_ymd = pendulum.instance(context['logical_date']).in_tz(DEFAULT_TZ).format('YYYYMMDD')
	logging.info(
		f'{_LOG_PREFIX} Using target_ymd from tz-shifted "logical_date". target_ymd={target_ymd}, logical_date={context["logical_date"]}')
	context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)


with DAG(
		_DAG_ID,
		description='Trigger regular biz analysis report DAG',
		tags=['trigger', 'regular', 'biz', 'analysis', 'daily', 'hdfs', 'aida', 'cuve', 'ins.cho'],
		default_args={
			'owner': 'ins.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2023, 9, 1, tz=DEFAULT_TZ),
		schedule_interval='0 6 * * *',  # 매일 06시
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	trigger_biz_analysis_report = TriggerDagRunOperator(
		trigger_dag_id=SPARK_DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{SPARK_DAG_ID}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=True,
		poke_interval=60,
		reset_dag_run=True,
		conf={
			XCOM_TARGET_YMD: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMD}") }}}}',
		}
	)

	setup >> trigger_biz_analysis_report
