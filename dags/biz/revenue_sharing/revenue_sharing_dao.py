"""
PyMongo 3.12.3 documentation » API Documentation
    https://pymongo.readthedocs.io/en/3.12.3/api/pymongo/index.html
"""

import pymongo
from airflow.providers.mongo.hooks.mongo import MongoHook

from core.base import CONN_MONGO_FOR_CMS

_COLL_NAME = 'SummaryRevenueSharingGfpStatsSchedules'


def get_target_dates(filter: dict) -> list:
    """
    CMS DB > SummaryRevenueSharingGfpStatsSchedules 컬렉션에서 filter에 해당하는 목록 조회
    :param filter:
    :return:
    """
    with MongoHook(CONN_MONGO_FOR_CMS) as hook:
        coll = hook.get_conn().get_database()[_COLL_NAME]
        docs = coll.find(filter, {'date': 1}).sort([
            ('periodUnit', pymongo.ASCENDING),  # 일별(DAILY) 스케줄을 먼저 처리해야 함
            ('date', pymongo.ASCENDING)
        ])
        doc_list = list(docs)

        # date 필드만 뽑아서 리스트로 구성
        dt_list = []
        for doc in doc_list:
            dt_list.append(doc['date'])

        return dt_list
