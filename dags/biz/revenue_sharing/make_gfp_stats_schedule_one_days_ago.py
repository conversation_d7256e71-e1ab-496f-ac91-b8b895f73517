"""
### 수익쉐어 리포트 용 GFP 통계 생성을 위한 스케줄 생성 - 1일 전 통계

#### 위키
- [https://wiki.navercorp.com/pages/viewpage.action?pageId=1465670404](https://wiki.navercorp.com/pages/viewpage.action?pageId=1465670404)

#### 개요
- 수익쉐어 리포트 용 GFP 통계 생성을 위한 스케줄을 생성한다.
    - 1일 전 통계 대상
    - 2일 전 통계 대상보다 늦게 생성해야 한다. 날짜순으로 순차 처리해야 하므로.

#### 주기
- 매일 03:15분

#### config
- ymd: 통계를 생성하고 싶은 날짜

"""

import logging
import os

import pendulum
import requests
import tenacity
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator

from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, BATCH_SERVER_ADDRESS, XCOM_TARGET_YMD

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [MAKE-GFP-STATS-SCHEDULE]'

# Batch 호출 주소
_REQ_URL = BATCH_SERVER_ADDRESS + '/batch/report/revenuesharing/makeGfpStatsSchedule'


def _setup(**context):
    """
    DAG 실행에 필요한 환경 설정 및 확인

    :param context:
    :return:
    """
    _set_target_ymd(**context)  # 작성해야 할 달 설정


def _set_target_ymd(**context):
    """
    스케줄에 의한 파라미터 또는 사용자 입력 파라미터에 의해 결정된다.
    :param context:
    :return:
    """
    user_requested_ymd = context['params'].get('ymd')
    if user_requested_ymd:
        pendulum.from_format(user_requested_ymd, 'YYYYMMDD')
        logging.info(f'{_LOG_PREFIX} Using target_ymd from config={user_requested_ymd}')
        context['ti'].xcom_push(XCOM_TARGET_YMD, user_requested_ymd)
    else:
        target_ymd = pendulum.instance(context['logical_date']).in_tz(DEFAULT_TZ).format('YYYYMMDD')  # 1일전
        logging.info(f'{_LOG_PREFIX} Using target_ymd from logical_date={target_ymd}')
        context['ti'].xcom_push(XCOM_TARGET_YMD, target_ymd)


def _request(url: str):
    """
    ssp-batch 서버로 수수료율 갱신 요청
    :param url:
    :return:
    """

    def _call_and_check():
        response = requests.get(url, timeout=60 * 20)
        response.raise_for_status()
        return response

    retry_obj = tenacity.Retrying(
        wait=tenacity.wait.wait_fixed(wait=3),
        stop=tenacity.stop.stop_after_attempt(3),
        retry=tenacity.retry_if_exception_type(requests.exceptions.HTTPError)
    )

    res = retry_obj(_call_and_check)
    logging.info(f'{_LOG_PREFIX} retry stat: attempts={retry_obj.statistics["attempt_number"]}')
    logging.info(f'{_LOG_PREFIX} status code: {res.status_code}')
    logging.info(f'{_LOG_PREFIX} response:\n{res.text}')

    jres = res.json()

    if jres.get('code') != '200':
        return False

    return True


def _make(**context):
    # 사용자로부터 받은 날짜가 있다면 설정
    ymd = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
    if ymd:
        uri = f'{_REQ_URL}?date={ymd}'

    # 배치 서버 호출
    logging.info(f'{_LOG_PREFIX} url={uri}')
    res = _request(uri)
    logging.info(f'{_LOG_PREFIX} res={res}')
    return res


with DAG(
        DAG_ID,
        description='수익쉐어 리포트 용 GFP 통계 생성을 위한 스케줄 설정(1일 전 통계)',
        default_args={
            'owner': 'juyoun.kim',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        start_date=pendulum.datetime(2023, 5, 1, tz=DEFAULT_TZ),
        schedule_interval="15 3 * * *",  # 매일 03:15
        tags=['batch', 'revenue_sharing', 'revenuesharing', 'revenue', 'juyoun.kim'],
        catchup=False,
        params={
            'USAGE': 'Refer to the DAG code for detailed parameter usage.',
            'ymd': ''
        },
) as dag:
    dag.doc_md = __doc__

    # 필요한 정보 세팅
    setup = PythonOperator(
        task_id='setup',
        python_callable=_setup,
    )

    make = PythonOperator(
        task_id='make',
        python_callable=_make,
    )

    # 파이프라인
    setup >> make
