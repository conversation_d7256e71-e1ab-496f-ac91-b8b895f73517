"""
### 수익쉐어 리포트 용 GFP 통계 생성을 위한 스케줄 생성 - 1달 전 통계

#### 위키
- [https://wiki.navercorp.com/pages/viewpage.action?pageId=1465670404](https://wiki.navercorp.com/pages/viewpage.action?pageId=1465670404)

#### 개요
- 수익쉐어 리포트 용 GFP 통계 생성을 위한 스케줄을 생성한다.
    - make_gfp_stats_schedule_one_month_ago 생성 이후에 본 스케줄을 생성한다.

#### 주기
- 매월 2일 03:20분

#### config
- ym: 통계를 생성하고 싶은 달

"""

import logging
import os

import pendulum
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator

from core import utils
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, BATCH_SERVER_ADDRESS, XCOM_TARGET_YMD

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [MAKE-GFP-STATS-SCHEDULE]'

# Batch 호출 주소
_REQ_URL = BATCH_SERVER_ADDRESS + '/batch/report/revenuesharing/makeGfpStatsSchedule'


def _setup(**context):
    """
    DAG 실행에 필요한 환경 설정 및 확인

    :param context:
    :return:
    """
    _set_target_ym(**context)  # 작성해야 할 달 설정


def _set_target_ym(**context):
    """
    스케줄에 의한 파라미터 또는 사용자 입력 파라미터에 의해 결정된다.
    :param context:
    :return:
    """
    user_requested_ym = context['params'].get('ym')
    if user_requested_ym:
        pendulum.from_format(user_requested_ym, 'YYYYMM')
        logging.info(f'{_LOG_PREFIX} Using target_ym from config={user_requested_ym}')
        context['ti'].xcom_push(XCOM_TARGET_YMD, user_requested_ym)
    else:
        target_ym = pendulum.instance(context['logical_date']).in_tz(DEFAULT_TZ).format('YYYYMM')  # 1달 전
        logging.info(f'{_LOG_PREFIX} Using target_ym from logical_date={target_ym}')
        context['ti'].xcom_push(XCOM_TARGET_YMD, target_ym)


def _make(**context):
    # 사용자로부터 받은 월이 있다면 설정
    ym = context['ti'].xcom_pull(key=XCOM_TARGET_YMD)
    if ym:
        uri = f'{_REQ_URL}?date={ym}'

    # 배치 서버 호출
    logging.info(f'{_LOG_PREFIX} url={uri}')
    res = utils.request(uri, timeout=60 * 20)
    logging.info(f'{_LOG_PREFIX} res={res}')
    return res


with DAG(
        DAG_ID,
        description='수익쉐어 리포트 용 GFP 통계 생성을 위한 스케줄 설정(1달 전 통계)',
        default_args={
            'owner': 'juyoun.kim',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        start_date=pendulum.datetime(2023, 5, 1, tz=DEFAULT_TZ),
        schedule_interval="20 3 2 * *",  # 매월 2일 03:20
        tags=['batch', 'revenue_sharing', 'revenuesharing', 'revenue', 'juyoun.kim'],
        catchup=False,
        params={
            'USAGE': 'Refer to the DAG code for detailed parameter usage.',
            'ym': ''
        },
) as dag:
    dag.doc_md = __doc__

    # 필요한 정보 세팅
    setup = PythonOperator(
        task_id='setup',
        python_callable=_setup,
    )

    make = PythonOperator(
        task_id='make',
        python_callable=_make,
    )

    # 파이프라인
    setup >> make
