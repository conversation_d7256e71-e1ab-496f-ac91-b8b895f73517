"""
### 최근 실버 로그 적재 일시 및 컴팩션 일시 갱신

#### 위키
- [로그적재/DAG 정의/연동/재처리 디자인 패턴](https://wiki.navercorp.com/pages/viewpage.action?pageId=1242737611)
- [https://wiki.navercorp.com/pages/viewpage.action?pageId=1397001879](https://wiki.navercorp.com/pages/viewpage.action?pageId=1397001879)

#### 개요
- 최근 실버 로그 적재 일시 및 컴팩션 일시 갱신을 위해 배치 서버를 호출한다.
- 배치 서버 내에서 적재 및 컴팩션 일시를 모두 갱신한다.

#### 주기
- 없음
- spark_silversmith의 실행에 따름
"""

import logging
import os

import pendulum
import requests
import tenacity
from airflow.models.dag import DAG
from airflow.models.variable import Variable
from airflow.operators.python import PythonOperator

from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, BATCH_SERVER_ADDRESS

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [REFRESH-RECENT-SILVER-YMDH]'

# Batch 호출 주소
_REQ_URL = BATCH_SERVER_ADDRESS + '/batch/silver/recentymdh/refresh'


def _request(url: str):
    def _call_and_check():
        response = requests.get(url, timeout=60 * 5)
        response.raise_for_status()
        return response

    retry_obj = tenacity.Retrying(
        wait=tenacity.wait.wait_fixed(wait=3),
        stop=tenacity.stop.stop_after_attempt(3),
        retry=tenacity.retry_if_exception_type(requests.exceptions.HTTPError)
    )

    res = retry_obj(_call_and_check)
    logging.info(f'{_LOG_PREFIX} retry stat: attempts={retry_obj.statistics["attempt_number"]}')
    logging.info(f'{_LOG_PREFIX} status code: {res.status_code}')
    logging.info(f'{_LOG_PREFIX} response:\n{res.text}')

    jres = res.json()

    if jres.get('code') != '200':
        return False

    return True


def _refresh_recent_silver_ymdh():
    logging.info(f'{_LOG_PREFIX} url={_REQ_URL}')
    res = _request(_REQ_URL)
    return res


with DAG(
        DAG_ID,
        description='실버 로그 최근 적재/컴팩션 일시 갱신',
        default_args={
            'owner': 'juyoun.kim',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
        schedule_interval=None,
        tags=['silver', 'batch', 'refresh', 'recent_ymdh', 'juyoun.kim'],
        catchup=False,
) as dag:
    dag.doc_md = __doc__

    # 실버 로그 최근 적재 일시 갱신
    refresh_recent_silver_ymdh = PythonOperator(
        task_id='refresh_recent_silver_ymdh',
        python_callable=_refresh_recent_silver_ymdh,
    )

    # 파이프라인
    refresh_recent_silver_ymdh
