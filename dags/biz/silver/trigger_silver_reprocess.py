"""
### 실버 자동 재처리

#### 위키
- [05-2. 실버 생성 재처리](https://wiki.navercorp.com/pages/viewpage.action?pageId=1402967786)

#### 개요
- Jobs에 처리되지 않고 남아 있는 일자에 대해 자동으로 재처리.
- 재처리와 정규처리를 분리해 서로 독립적으로 실행하여 정규 처리에 영향을 주지 않도록 함
- 정규처리 대상인지, 재처리 대상인지 구분
  - 정규처리 대상: retryCnt = -1
  - 재처리 대상: retryCnt >= 0 and retryCnt < job_dao.MAX_RETRY_CNT
- 한 번에 병렬도만큼 동시 처리
  - 병렬도는 Data DB > Environments.name='silver-log-parallel-level'에 정의되어 있음
  - 정규는 순차처리하지만 재처리는 병렬처리
  - 한꺼번에 많은 시간대를 재처리해야 할 경우 반영 속도를 높이기 위함
- 동시에 1개의 dag run만 존재할 수 있음. 중복 실행 불가.
  - 한 번 떴을 때 처리해야 할 작업이 있다면 모두 처리하고 끝냄
- spark_silversmith DAG을 트리거시킴

#### 주기
- 5분 마다

#### config
- 없음
- 수동 실행을 위해서는 trigger_silversmith_manual을 사용헤 주세요.
"""

import logging
import os
from time import sleep

import pendulum
from airflow import AirflowException
from airflow.models.dag import DAG
from airflow.operators.dummy import DummyOperator
from airflow.operators.python import ShortCircuitOperator
from airflow.utils.types import DagRunType

from biz.silver import spark_silversmith
from core.airflow_api import trigger_dagrun
from core.base import DEFAULT_TZ, XCOM_TARGET_YMDH, ALERT_EMAIL_ADDRESSES, XCOM_LOG_TYPE
from core.dao import job_dao, environment_dao

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRG-SILVERSMITH-REPROCESS]'


def _is_exist_jobs_to_reprocess(**context) -> bool:
	"""
	재처리할 job이 있는지
	:param context:
	:return:
	"""
	filter = {
		'type': spark_silversmith.DAG_ID,
		'manual': 0,
		'retryCnt': {'$gt': -1, '$lt': job_dao.MAX_RETRY_CNT},
		'running': 0,
	}
	jobs = job_dao.get_jobs(filter)
	if jobs:
		logging.info(f'{_LOG_PREFIX} 재처리할 Jobs 개수 = {len(jobs)}')
		return True
	else:
		logging.info(f'{_LOG_PREFIX} 처리할 job 없음')
		return False


def _run_parallel_spark_silversmith(**context):
	"""
	spark_silversmith 병렬 트리거
	:param context:
	:return:
	"""
	# 병렬도 설정
	value = environment_dao.get_environment_value_by_name('silver-log-parallel-level')
	_MAX_PARALLEL_LEVEL = int(value) if value else 3  # 디폴트 3
	logging.info(f'{_LOG_PREFIX} _MAX_PARALLEL_LEVEL={_MAX_PARALLEL_LEVEL}')

	start_time = pendulum.now(DEFAULT_TZ)
	triggered_cnt = 0
	while True:
		remained_list = _get_remained_list()

		# 재처리할 것이 없다면 루프 종료
		if len(remained_list) == 0:
			end_time = pendulum.now(DEFAULT_TZ)
			elapsed_time = end_time.diff(start_time)
			minutes = elapsed_time.in_minutes()
			seconds = elapsed_time.in_seconds() % 60
			logging.info(f'{_LOG_PREFIX}\t 재처리 종료. 소요시간 {minutes}분 {seconds}초\n')

			break
		# 한 번 깨어났을 때 재처리할 것이 있다면 모두 처리할 때까지 루프. 루프 주기 = sleep(60)
		else:
			# 진행중인 작업 목록 조회
			ing_list = _get_ing_list()
			ing_cnt = len(ing_list) if ing_list else 0

			# 동시 처리를 위한 여분 설정
			spare_cnt = _MAX_PARALLEL_LEVEL - ing_cnt
			if spare_cnt == 0:  # 여유분 없이 꽉 찼음
				pass
			elif spare_cnt > 0:  # 여유분이 있을 경우
				# 여유분 만큼 job 목록 자겨오기
				standby_list = _get_standby_list(spare_cnt)

				# 여유분만큼 스파크 트리거 병렬처리
				if standby_list:
					triggered_job_ids = []
					for job in standby_list:
						job_id = job.get('_id')

						ymdh = job.get('datetime')

						log_type = job.get('detail').get('logType')
						if not log_type:
							log_type = '*'

						conf = {
							XCOM_TARGET_YMDH: ymdh,  # YYYYMMDDHH
							XCOM_LOG_TYPE: log_type,
						}

						# 스파크 트리거
						spark_dag_run_id = trigger_dagrun(spark_silversmith.DAG_ID,
														  pendulum.now(DEFAULT_TZ),
														  conf,
														  dag_run_type=DagRunType.SCHEDULED)
						triggered_job_ids.append(job_id)
						triggered_cnt = triggered_cnt + 1

						logging.info(
							f'{_LOG_PREFIX} {spark_silversmith.DAG_ID} 트리거시킴. ({triggered_cnt}) {ymdh} job_id={job_id} conf={conf} spark_dag_run_id={spark_dag_run_id}')

					# job이 running=1로 바뀔 때까지 대기
					# 그래야 _get_remained_list()를 했을 때 다시 추출되지 않음
					# spark_silversmith._upsert_job()에서 running=1로 바꿈
					_wait_for_job_running(triggered_job_ids)

			else:  # 여유분이 음수란 얘기는 진행 중인 작업의 개수가 _MAX_PARALLEL_LEVEL보다 많다는 뜻
				msg = f'-----------  _MAX_PARALLEL_LEVEL({_MAX_PARALLEL_LEVEL}) OVER  ing_cnt={ing_cnt} ------------\n'
				for ing in ing_list:
					msg = msg + f'\t{ing}\n'
				logging.warning(f'{_LOG_PREFIX}{msg}')

				raise AirflowException(
					f'진행중인 작업이 _MAX_PARALLEL_LEVEL({_MAX_PARALLEL_LEVEL})보다 많습니다. ing_cnt={ing_cnt}')

			sleep(15)


def _get_remained_list() -> list:
	"""
	남은 작업 수 조회
	:return:
	"""
	filter = {
		'type': spark_silversmith.DAG_ID,
		'manual': 0,
		'retryCnt': {'$gt': -1, '$lt': job_dao.MAX_RETRY_CNT},
	}
	remained_list = job_dao.get_jobs(filter)
	return remained_list


def _get_ing_list() -> list:
	"""
	진행중인 작업 수 조회
	:return:
	"""
	filter = {
		'type': spark_silversmith.DAG_ID,
		'manual': 0,
		'retryCnt': {'$gt': -1, '$lt': job_dao.MAX_RETRY_CNT},
		'running': 1,
	}
	ing_list = job_dao.get_jobs(filter)
	return ing_list


def _get_standby_list(spare_cnt: int) -> list:
	"""
	대기 작업 조회. spare_cnt 만큼만
	:return:
	"""
	filter = {
		'type': spark_silversmith.DAG_ID,
		'manual': 0,
		'retryCnt': {'$gt': -1, '$lt': job_dao.MAX_RETRY_CNT},
		'running': 0,
	}
	sort = [('_id', 1)]
	standby_list = job_dao.get_jobs(filter, sort, spare_cnt)
	return standby_list


def _wait_for_job_running(triggered_job_ids: list):
	"""
	트리거 시킨 spark_dag_run과 관련된 Job.running = 0인 것이 하나라도 있다면 대기
	retryCnt < job_dao.MAX_RETRY_CNT 인 것만 조회. 최대 재시도 횟수를 초과한 것은 제외.
	혹은 너무 빨리 끝나서 job이 모두 없어졌다면 종료
	:param job_id:
	:return:
	"""
	sleep(10)
	while True:
		jobs = job_dao.get_jobs({
			'_id': {'$in': triggered_job_ids},
			'retryCnt': {'$gt': -1, '$lt': job_dao.MAX_RETRY_CNT}
		})
		if jobs:
			unrunning_cnt = 0
			for job in jobs:
				if job.get('running') == 0:
					unrunning_cnt = unrunning_cnt + 1

			logging.info(f'{_LOG_PREFIX} len(triggered_job_ids)={len(triggered_job_ids)} unrunning_cnt={unrunning_cnt}')
			if unrunning_cnt < 1:
				break
			else:
				sleep(10)
		else:
			break


with DAG(
		_DAG_ID,
		description='실버 자동 재처리 DAG. spark_silversmith 트리거시킴',
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2024, 4, 1, tz=DEFAULT_TZ),
		schedule_interval="*/5 * * * *",  # 5분마다
		tags=['trigger', 'silver', 'silversmith', 'reprocess', 'juyoun.kim'],
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# 처리해야 할 job이 있을 때만 다운스트림 태스크 진행
	is_exist_jobs_to_reprocess = ShortCircuitOperator(
		task_id='is_exist_jobs_to_reprocess',
		python_callable=_is_exist_jobs_to_reprocess,
	)

	# 스파크 병렬처리
	run_parallel_spark_silversmith = ShortCircuitOperator(
		task_id='run_parallel_spark_silversmith',
		python_callable=_run_parallel_spark_silversmith,
	)

	done = DummyOperator(
		task_id='done',
	)

	# 파이프라인
	is_exist_jobs_to_reprocess >> run_parallel_spark_silversmith >> done
