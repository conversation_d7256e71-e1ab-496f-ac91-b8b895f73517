"""
### 실버 적재/컴팩션 지연 감시

#### 위키
- [https://wiki.navercorp.com/pages/viewpage.action?pageId=1397001727](https://wiki.navercorp.com/pages/viewpage.action?pageId=1397001727)

#### 개요
-  Environments에 있는 값으로 체크
    * 실버의 최근 적재일시: Environments.name='silver-log-recent-accumulation-ymdh'
    * 실버의 최근 컴팩션일시: Environments.name='silver-log-recent-compaction-ymdh'

#### 주기
- 매 시 50분

#### config
- 없음
"""
import logging
import os

import pendulum
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from airflow.utils.trigger_rule import TriggerRule

from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, SILVER_HOME, HADOOP_FILE_BROWSER_VIEW, PROFILE
from core.dao.environment_dao import get_environment_value_by_name

# 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [MONITOR-SILVER]'


def _monitor_silver_accu(**context):
    """
    실버 로그 적재에 지연이 있는지 감시
        - 현재 시간이 18시일 때 참을 수 있는 최대 지연 시간이 3시간이라면
        - 15시까지는 지연을 허용하므로 14시 대가 없으면 지연
    Environments 값으로 체크
        - 실버의 최근 적재일시: Environments.name='silver-log-recent-accumulation-ymdh'
        - 참을 수 있는 최대 지연 시간: Environments.name='silver-log-max-tolerable-delay-hours'
    :return:
    """
    # 참을 수 있는 최대 지연 시간
    max_tolerable_hours = int(get_environment_value_by_name('silver-log-max-tolerable-delay-hours'))
    expected_silver_dt = pendulum.now(tz=DEFAULT_TZ).subtract(hours=max_tolerable_hours) \
        .set(minute=0, second=0, microsecond=0)

    # 마지막 적재일시
    last_silver_ymdh = get_environment_value_by_name('silver-log-recent-accumulation-ymdh')
    last_silver_dt = pendulum.from_format(last_silver_ymdh, 'YYYYMMDDHH', DEFAULT_TZ)

    logging.info(f'{_LOG_PREFIX} max_tolerable_hours = {max_tolerable_hours}')
    logging.info(f'{_LOG_PREFIX}      last_silver_dt = {last_silver_dt}')
    logging.info(f'{_LOG_PREFIX}  expected_silver_dt = {expected_silver_dt}')

    # 최근 적재일시가 기대되는 적재일시보다 작으면(참을 수 있는 최대 지연 시간을 넘으면)
    if last_silver_dt < expected_silver_dt:
        title = f'[{PROFILE} - 실버 로그 적재 지연 알림]'

        # 현재 시간으로부터 지연된 시간치
        diff = (pendulum.now(tz=DEFAULT_TZ) - last_silver_dt).in_hours()
        logging.info(f'{_LOG_PREFIX}                diff = {diff}')

        body = f'실버 로그가 지연되고 있습니다.<br/><br/>' \
               f'마지막 적재 일시: {last_silver_ymdh}<br/>' \
               f'허용 가능한 최대 지연 시간: {max_tolerable_hours}<br/>' \
               f'지연 시간: <span style="color: red; font-weight: bold;">{diff}</span> 시간<br/><br/>'

        ymd_h_path = last_silver_dt.format('YYYYMMDD-HH')
        body += f'<a href="{HADOOP_FILE_BROWSER_VIEW}{SILVER_HOME}/er-ssp-client/{ymd_h_path}">er-ssp-client</a><br/>'
        body += f'<a href="{HADOOP_FILE_BROWSER_VIEW}{SILVER_HOME}/er-ssp-server/{ymd_h_path}">er-ssp-server</a>'

        logging.info(f'{_LOG_PREFIX} title = {title}')
        logging.info(f'{_LOG_PREFIX}  body = {body}')
        send_email(get_environment_value_by_name('silver-log-delay-alarm-receivers'), title, body)


def _monitor_silver_compaction(**context):
    """
    실버 로그 컴팩션에 지연이 있는지 감시
        - 현재 시간이 18시일 때 참을 수 있는 최대 지연 시간이 3시간이라면
        - 15시까지는 지연을 허용하므로 14시 대가 없으면 지연
    Environments 값으로 체크
        - 실버의 최근 컴팩션일시: Environments.name='silver-log-recent-compaction-ymdh'
        - 참을 수 있는 최대 지연 시간: Environments.name='silver-log-compaction-max-tolerable-delay-hours'
    :return:
    """
    # 참을 수 있는 최대 지연 시간
    max_tolerable_hours = int(get_environment_value_by_name('silver-log-compaction-max-tolerable-delay-hours'))
    expected_silver_compaction_dt = pendulum.now(tz=DEFAULT_TZ).subtract(hours=max_tolerable_hours) \
        .set(minute=0, second=0, microsecond=0)

    # 마지막 컴팩션일시
    last_silver_compaction_ymdh = get_environment_value_by_name('silver-log-recent-compaction-ymdh')
    last_silver_compaction_dt = pendulum.from_format(last_silver_compaction_ymdh, 'YYYYMMDDHH', DEFAULT_TZ)

    logging.info(f'{_LOG_PREFIX}           max_tolerable_hours = {max_tolerable_hours}')
    logging.info(f'{_LOG_PREFIX}     last_silver_compaction_dt = {last_silver_compaction_dt}')
    logging.info(f'{_LOG_PREFIX} expected_silver_compaction_dt = {expected_silver_compaction_dt}')

    # 최근 컴팩션일시가 기대되는 컴팩션일시보다 작으면(참을 수 있는 최대 지연 시간을 넘으면)
    if last_silver_compaction_dt < expected_silver_compaction_dt:
        title = f'[{PROFILE} - 실버 로그 컴팩션 지연 알림]'

        # 현재 시간으로부터 지연된 시간치
        diff = (pendulum.now(tz=DEFAULT_TZ) - last_silver_compaction_dt).in_hours()
        logging.info(f'{_LOG_PREFIX}                diff = {diff}')

        body = f'실버 로그가 지연되고 있습니다.<br/><br/>' \
               f'마지막 컴팩션 일시: {last_silver_compaction_ymdh}<br/>' \
               f'허용 가능한 최대 지연 시간: {max_tolerable_hours}<br/>' \
               f'지연 시간: <span style="color: red; font-weight: bold;">{diff}</span> 시간<br/><br/>'

        ymd_h_path = last_silver_compaction_dt.format('YYYYMMDD-HH')
        body += f'<a href="{HADOOP_FILE_BROWSER_VIEW}{SILVER_HOME}/er-ssp-client/{ymd_h_path}">er-ssp-client</a><br/>'
        body += f'<a href="{HADOOP_FILE_BROWSER_VIEW}{SILVER_HOME}/er-ssp-server/{ymd_h_path}">er-ssp-server</a>'

        logging.info(f'{_LOG_PREFIX} title = {title}')
        logging.info(f'{_LOG_PREFIX}  body = {body}')
        send_email(get_environment_value_by_name('silver-log-compaction-delay-alarm-receivers'), title, body)


with DAG(
        _DAG_ID,
        description='실버 로그 적재/컴팩션 지연 감시',
        tags=['monitor', 'silver', 'juyoun.kim'],
        default_args={
            'owner': 'juyoun.kim',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
        schedule_interval='50 * * * *',
        catchup=False,
) as dag:
    dag.doc_md = __doc__

    # 실버 적재 지연 감시
    monitor_silver_accu = PythonOperator(
        task_id='monitor_silver_accu',
        python_callable=_monitor_silver_accu
    )

    # 실버 컴팩션 지연 감시
    monitor_silver_compaction = PythonOperator(
        task_id='monitor_silver_compaction',
        python_callable=_monitor_silver_compaction,
        trigger_rule=TriggerRule.ALWAYS
    )

    # 파이프라인
    monitor_silver_accu >> monitor_silver_compaction
