"""
### 최근 실버 로그 적재 및 컴팩션 일시 갱신(스테이징 용)

#### 개요
- 스테이징 용 실버 로그 최근 적재일시 업데이트 DAG
- 리얼에서는 refresh_recent_silver_ymdh.py를 쓰는데, 배치 서버는 스테이지가 없기 때문에 DAG에서 배치서버와 동일한 로직 구현하여 실행

#### 주기
- 없음
- spark_silversmith의 실행에 따름
"""

import logging
import os

import pendulum
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator

from core import c3
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, PROFILE_TEST, PROFILE, SILVER_HOME, ER_SSP_SERVER, \
    ER_SSP_CLIENT
from core.dao import environment_dao

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [REFRESH-RECENT-SILVER-YMDH]'


def _refresh_recent_silver_ymdh():
    _refresh_recent_silver_accu_ymdh()
    _refresh_recent_silver_compaction_ymdh()


def _refresh_recent_silver_accu_ymdh():
    log_names = [ER_SSP_SERVER, ER_SSP_CLIENT]

    # 마지막 적재일시
    env1 = str(environment_dao.get_environment_value_by_name('silver-log-recent-accumulation-ymdh'))
    last_silver_date = pendulum.from_format(env1, 'YYYYMMDDHH', DEFAULT_TZ)
    recent_silver_ymdh = last_silver_date.format('YYYYMMDD-HH')  # while loop에서 사용할 변수
    last_silver_ymdh = recent_silver_ymdh  # 로그 찍기 용 변수
    logging.info(f'\n실버 로그 최근 적재일시 업데이트.. last_silver_ymdh:{last_silver_ymdh}')

    current = last_silver_date.add(hours=1)  # 마지막 적재일시 + 1 시간부터 조사
    now = pendulum.now(tz=DEFAULT_TZ).set(minute=0, second=0, microsecond=0)

    while current < now:
        current_ymdh = current.format('YYYYMMDD-HH')

        # 서버, 클라이언트 로그 존재 여부 검사
        both = [0, 0]
        for i, log_name in enumerate(log_names):
            log_name = log_names[i]
            file_path = f'{SILVER_HOME}/{log_name}/{current_ymdh}/_SUCCESS'

            is_exist = c3.exists(file_path)

            both[i] = 1 if is_exist else 0

        # 둘 다 있으면 해당 시간으로 최근 적재 일시 갱신
        is_both_exist = both[0] and both[1]
        if is_both_exist:
            if PROFILE == PROFILE_TEST:
                # 테스트인 경우는 중간에 이가 빠지는 경우가 있어 순차 적재가 아니더라도
                # 마지막으로 쌓은 시간대를 최근 적재일시로 함
                recent_silver_ymdh = current_ymdh
            else:
                # 중간에 이가 빠지지 않는 경우만 설정
                left = pendulum.from_format(recent_silver_ymdh, 'YYYYMMDD-HH', DEFAULT_TZ).add(hours=1) \
                    .set(minute=0, second=0, microsecond=0)
                right = pendulum.from_format(current_ymdh, 'YYYYMMDD-HH', DEFAULT_TZ) \
                    .set(minute=0, second=0, microsecond=0)

                if left == right:
                    recent_silver_ymdh = current_ymdh

            logging.info(f'실버 로그 최종 적재일시 검사중. {last_silver_ymdh} 이후 {current_ymdh} 존재함')
        else:
            logging.info(f'실버 로그 최종 적재일시 검사중. {last_silver_ymdh} 이후 {current_ymdh} 존재하지 않음')

        current = current.add(hours=1)

    environment_dao.set_environment('silver-log-recent-accumulation-ymdh', recent_silver_ymdh.replace('-', ''))


def _refresh_recent_silver_compaction_ymdh():
    log_names = [ER_SSP_SERVER, ER_SSP_CLIENT]

    # 마지막 컴팩션일시
    env1 = str(environment_dao.get_environment_value_by_name('silver-log-recent-compaction-ymdh'))
    last_silver_date = pendulum.from_format(env1, 'YYYYMMDDHH', DEFAULT_TZ)
    recent_silver_ymdh = last_silver_date.format('YYYYMMDD-HH')  # while loop에서 사용할 변수
    last_silver_ymdh = recent_silver_ymdh  # 로그 찍기 용 변수
    logging.info(f'\n실버 로그 최근 컴팩션일시 업데이트.. last_silver_ymdh:{last_silver_ymdh}')

    current = last_silver_date.add(hours=1)  # 마지막 컴팩션일시 + 1 시간부터 조사
    now = pendulum.now(tz=DEFAULT_TZ).set(minute=0, second=0, microsecond=0)

    while current < now:
        current_ymdh = current.format('YYYYMMDD-HH')

        # 서버, 클라이언트 로그 존재 여부 검사
        both = [0, 0]
        for i, log_name in enumerate(log_names):
            log_name = log_names[i]
            file_path = f'{SILVER_HOME}/{log_name}/{current_ymdh}/_COMPACTION_SUCCESS'

            is_exist = c3.exists(file_path)

            both[i] = 1 if is_exist else 0

        # 둘 다 있으면 해당 시간으로 최근 컴팩션 일시 갱신
        is_both_exist = both[0] and both[1]
        if is_both_exist:
            if PROFILE == PROFILE_TEST:
                # 테스트인 경우는 중간에 이가 빠지는 경우가 있어 순차 컴팩션가 아니더라도
                # 마지막으로 쌓은 시간대를 최근 컴팩션일시로 함
                recent_silver_ymdh = current_ymdh
            else:
                # 중간에 이가 빠지지 않는 경우만 설정
                left = pendulum.from_format(recent_silver_ymdh, 'YYYYMMDD-HH', DEFAULT_TZ).add(hours=1) \
                    .set(minute=0, second=0, microsecond=0)
                right = pendulum.from_format(current_ymdh, 'YYYYMMDD-HH', DEFAULT_TZ) \
                    .set(minute=0, second=0, microsecond=0)

                if left == right:
                    recent_silver_ymdh = current_ymdh

            logging.info(f'실버 로그 최종 컴팩션일시 검사중. {last_silver_ymdh} 이후 {current_ymdh} 존재함')
        else:
            logging.info(f'실버 로그 최종 컴팩션일시 검사중. {last_silver_ymdh} 이후 {current_ymdh} 존재하지 않음')

        current = current.add(hours=1)

    environment_dao.set_environment('silver-log-recent-compaction-ymdh', recent_silver_ymdh.replace('-', ''))


with DAG(
        DAG_ID,
        description='실버 로그 최근 적재/컴팩션 일시 갱신',
        default_args={
            'owner': 'juyoun.kim',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
        schedule_interval=None,
        tags=['silver', 'batch', 'refresh', 'recent_ymdh', 'juyoun.kim'],
        catchup=False,
) as dag:
    dag.doc_md = __doc__

    # 실버 로그 최근 적재 일시 갱신
    refresh_recent_silver_ymdh = PythonOperator(
        task_id='refresh_recent_silver_ymdh',
        python_callable=_refresh_recent_silver_ymdh,
    )

    # 파이프라인
    refresh_recent_silver_ymdh
