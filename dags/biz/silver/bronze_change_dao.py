"""
PyMongo 3.12.3 documentation » API Documentation
    https://pymongo.readthedocs.io/en/3.12.3/api/pymongo/index.html
"""
import logging
from datetime import datetime

import pymongo
from airflow.providers.mongo.hooks.mongo import MongoHook
from bson import ObjectId
from pymongo.results import InsertOneResult, DeleteResult

from core.base import DEFAULT_TZ, CONN_MONGO_FOR_DATA

_COLL_BRONZE_CHANGES = 'BronzeChanges'
_JOB_TYPE = 'spark_silversmith'


def get_change_by_datetime(dt: str) -> dict:
    """
    BronzeChanges.datetime에 해당하는 다큐먼트 1 건 가져오기
    :param name:
    :return: 
    """
    with <PERSON><PERSON><PERSON><PERSON>(CONN_MONGO_FOR_DATA) as hook:
        coll = hook.get_conn().get_database()[_COLL_BRONZE_CHANGES]
        return coll.find_one({'datetime': dt})


def get_changes_for_jobs() -> list:
    """
    Jobs에 넣을 브론즈 정보 가져오기
        Jobs에 한 번도 들어간 적이 없는 경우 :: librarianModifiedAt != null && silverJobCreatedAt = null
        브론즈가 재처리됐을 경우 :: $librarianModifiedAt > $silverJobCreatedAt
    :return:
    """
    with MongoHook(CONN_MONGO_FOR_DATA) as hook:
        coll = hook.get_conn().get_database()[_COLL_BRONZE_CHANGES]

        docs = coll.find({
            '$or': [
                # Jobs에 처음 들어갈 상황이거나
                {'$and': [{'librarianModifiedAt': {'$exists': True}},
                          {'silverJobCreatedAt': {'$exists': False}}]},
                # 브론즈가 재처리됐거나
                {'$expr': {'$gt': ['$librarianModifiedAt', '$silverJobCreatedAt']}}
            ]
        }).sort('datetime', pymongo.ASCENDING)
        doc_list = list(docs)

        return doc_list


def get_changes(filter: dict) -> list:
    """
    브론즈 변경 정보 조회. 여러 건 조회. datetime 오름차순 정렬.
    :return:
    """
    with MongoHook(CONN_MONGO_FOR_DATA) as hook:
        coll = hook.get_conn().get_database()[_COLL_BRONZE_CHANGES]

        docs = coll.find(filter).sort('datetime', pymongo.ASCENDING)
        doc_list = list(docs)

        return doc_list


def update_librarian_modified_at(dt: str, modified_at: datetime):
    """
    BronzeChanges.librarianModifiedAt의 일시 업데이트. 1건 업데이트
    :param name:
    :return:
    """
    with MongoHook(CONN_MONGO_FOR_DATA) as hook:
        coll = hook.get_conn().get_database()[_COLL_BRONZE_CHANGES]
        coll.update_one({'datetime': dt}, {'$set': {'librarianModifiedAt': modified_at}})
        logging.info(f'{_COLL_BRONZE_CHANGES}["{dt}"] set to librarianModifiedAt={modified_at}')


def update_silver_job_created_at(dt: str, created_at: datetime):
    """
    BronzeChanges.librarianModifiedAt의 일시 업데이트. 1건 업데이트
    :param name:
    :return:
    """
    with MongoHook(CONN_MONGO_FOR_DATA) as hook:
        coll = hook.get_conn().get_database()[_COLL_BRONZE_CHANGES]
        coll.update_one({'datetime': dt}, {'$set': {'silverJobCreatedAt': created_at}})
        logging.info(f'{_COLL_BRONZE_CHANGES}["{dt}"] set to silverJobCreatedAt={created_at}')


def insert_change(dt: str, modified_at: datetime) -> ObjectId:
    """
    BronzeChanges에 다큐먼트 추가
    :param dt: 
    :param modified_at: 
    :return: 
    """
    with MongoHook(CONN_MONGO_FOR_DATA) as hook:
        coll = hook.get_conn().get_database()[_COLL_BRONZE_CHANGES]

        doc = {
            'datetime': dt,
            'createdAt': datetime.now(tz=DEFAULT_TZ)
        }
        if modified_at:
            doc['librarianModifiedAt'] = modified_at

        result: InsertOneResult = coll.insert_one(doc)
        logging.info(f'{_COLL_BRONZE_CHANGES}["datetime={dt} modified_at={modified_at}"] inserted')

        return result.inserted_id


def delete_expired_changes(filter: dict):
    """
    filter에 해당하는 bronze change 여러 건 삭제
    :param filter:
    :return:
    """
    with MongoHook(CONN_MONGO_FOR_DATA) as hook:
        coll = hook.get_conn().get_database()[_COLL_BRONZE_CHANGES]

        result: DeleteResult = coll.delete_many(filter)
        if result.deleted_count > 0:
            logging.info(f'{_COLL_BRONZE_CHANGES} {filter} doc is deleted. result={result}')
        else:
            logging.warning(f'{_COLL_BRONZE_CHANGES}: {filter} doc is not delete. result={result}')
