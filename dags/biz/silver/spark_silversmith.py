"""
### 실버 로그 적재

#### 위키
- [로그적재/DAG 정의/연동/재처리 디자인 패턴](https://wiki.navercorp.com/pages/viewpage.action?pageId=1242737611)
- [https://wiki.navercorp.com/pages/viewpage.action?pageId=970694044](https://wiki.navercorp.com/pages/viewpage.action?pageId=970694044)

#### 개요
- 실버 로그 적재 > 컴팩션 > 최근 적재 일시 갱신 > GFP비딩그룹지표, GoldIndex 생성
- GFP비딩그룹지표, GoldIndex 생성 부터는 wait_for_complete=False이므로 끝날 때까지 대기하지 않음.

#### 주기
- 정규 : trigger_silversmith_regular
- 수동 : trigger_silversmith_manual
- 재처리 : DAG 없음
    * 실버 적재를 위한 Job은 make_silver_job DAG에 의해 생성되어 Jobs 컬렉션에서 관리된다.
    * 정규 DAG은 10분마다 Jobs에 있는 spark_silversmith Job을 찾아 spark_silversmith DAG을 실행시키는 형태이다.
    * spark_silversmith DAG이 실패하는 경우 Jobs 컬렉션에 해당 Job이 running = 0 형태로 남아있기 때문에 정규 DAG에 의해 다시 추출된다.
    * 정규 DAG이 재처리의 역할까지 포괄하고 있기에 별도의 재처리 DAG을 두지 않는다.

#### config
- target_ymdh: YYYYMMDDHH
"""

import logging
import os
from functools import partial
from pprint import pp
from typing import List

import pendulum
from airflow import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType

from biz.biddinggroup import spark_gfp_biddinggroup
from biz.gold import spark_goldsmith
from biz.silver import refresh_recent_silver_ymdh
from core import utils
from core.base import PROFILE_TEST, PROFILE_REAL, PROFILE, ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, POOL_SPARK, \
	SPARKLING_APP_JAR_PATH, XCOM_TARGET_YMDH, SPARKLING_IMAGE, \
	SPARKLING_APP_HARD_LIMIT_1, PROFILE_STAGE, PROFILE_DEV, C3_NAMESPACE, XCOM_LOG_TYPE
from core.dao import job_dao
from core.dao.job_dao import clean_up, set_to_run_flag
from core.spark_pool import POOL_SLOT_NORMAL_33
from core.spark_submit_op import invoke_job_with_args
from core.spark_task_group import create_task_group

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [SPK-SILVERSMITH]'

# Spark
_SPARK_APP_CLASS_ACCU = 'com.navercorp.gfp.biz.silver.Silversmith'
_SPARK_APP_CLASS_COMPACTION = 'com.navercorp.gfp.biz.silver.SilverCompactor'

# Profile
_PF_KEY_SPARK_SUBMIT_OPTIONS = 'spark_submit_options'
_AGG_TYPE_ACCU = 'ACCU'
_AGG_TYPE_COMPACTION = 'COMPACTION'
_PROFILE_SETTINGS = {
	_AGG_TYPE_ACCU: {
		PROFILE_DEV: {
			_PF_KEY_SPARK_SUBMIT_OPTIONS: """
                --num-executors 4
                --executor-cores 9
                --executor-memory 2g
                --conf spark.sql.shuffle.partitions=800
            """.split(),
		},
		PROFILE_TEST: {
			_PF_KEY_SPARK_SUBMIT_OPTIONS: """
                --num-executors 4
                --executor-cores 9
                --executor-memory 2g
                --conf spark.sql.shuffle.partitions=800
            """.split(),
		},
		PROFILE_STAGE: {
			_PF_KEY_SPARK_SUBMIT_OPTIONS: """
                --num-executors 60
                --executor-cores 2
                --executor-memory 6g
                --conf spark.sql.shuffle.partitions=2400
                --conf spark.executor.memoryOverhead=1g
            """.split(),
		},
		PROFILE_REAL: {
			_PF_KEY_SPARK_SUBMIT_OPTIONS: """
                --num-executors 60
                --executor-cores 2
                --executor-memory 6g
                --conf spark.sql.shuffle.partitions=2400
                --conf spark.executor.memoryOverhead=1g
            """.split(),
		},
	},
	_AGG_TYPE_COMPACTION: {
		PROFILE_DEV: {
			_PF_KEY_SPARK_SUBMIT_OPTIONS: f"""
            --num-executors 9
            --executor-cores 2
            --executor-memory 1g
            --driver-memory 1g
            --conf spark.driver.memoryOverhead=2g
            --conf spark.executor.memoryOverhead=2g
            --conf spark.sql.files.maxPartitionBytes=64mb
            --conf spark.sql.shuffle.partitions=800
            --conf spark.scheduler.mode=FAIR
            --conf spark.scheduler.allocation.file=hdfs://{C3_NAMESPACE}/user/gfp-data/fairscheduler.xml
            --conf spark.scheduler.pool=silver_compactor
        """.split(),
		},
		PROFILE_TEST: {
			_PF_KEY_SPARK_SUBMIT_OPTIONS: f"""
            --num-executors 9
            --executor-cores 2
            --executor-memory 1g
            --driver-memory 1g
            --conf spark.driver.memoryOverhead=2g
            --conf spark.executor.memoryOverhead=2g
            --conf spark.sql.files.maxPartitionBytes=64mb
            --conf spark.sql.shuffle.partitions=800
            --conf spark.scheduler.mode=FAIR
            --conf spark.scheduler.allocation.file=hdfs://{C3_NAMESPACE}/user/gfp-data/fairscheduler.xml
            --conf spark.scheduler.pool=silver_compactor
        """.split(),
		},
		PROFILE_STAGE: {
			_PF_KEY_SPARK_SUBMIT_OPTIONS: f"""
            --num-executors 40
            --executor-cores 2
            --executor-memory 6g
            --driver-memory 2g
            --conf spark.driver.memoryOverhead=2g
            --conf spark.executor.memoryOverhead=2g
            --conf spark.sql.files.maxPartitionBytes=64mb
            --conf spark.sql.shuffle.partitions=800
            --conf spark.scheduler.mode=FAIR
            --conf spark.scheduler.allocation.file=hdfs://{C3_NAMESPACE}/user/gfp-data/fairscheduler.xml
            --conf spark.scheduler.pool=silver_compactor
        """.split(),
		},
		PROFILE_REAL: {
			_PF_KEY_SPARK_SUBMIT_OPTIONS: f"""
            --num-executors 40
            --executor-cores 2
            --executor-memory 6g
            --driver-memory 2g
            --conf spark.driver.memoryOverhead=2g
            --conf spark.executor.memoryOverhead=2g
            --conf spark.sql.files.maxPartitionBytes=64mb
            --conf spark.sql.shuffle.partitions=800
            --conf spark.scheduler.mode=FAIR
            --conf spark.scheduler.allocation.file=hdfs://{C3_NAMESPACE}/user/gfp-data/fairscheduler.xml
            --conf spark.scheduler.pool=silver_compactor
        """.split(),
		},
	}
}
_SPARK_SUBMIT_OPTIONS_FOR_ACCU = _PROFILE_SETTINGS[_AGG_TYPE_ACCU][PROFILE][_PF_KEY_SPARK_SUBMIT_OPTIONS]
_SPARK_SUBMIT_OPTIONS_FOR_COMPACTION = _PROFILE_SETTINGS[_AGG_TYPE_COMPACTION][PROFILE][_PF_KEY_SPARK_SUBMIT_OPTIONS]


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	# 일시 설정
	_set_target_ymdh(**context)

	# log type 설정
	log_type = context['params'].get(XCOM_LOG_TYPE)
	context['ti'].xcom_push(XCOM_LOG_TYPE, log_type)

	# 파라미터 유효성 검사
	ymdh = context['ti'].xcom_pull(key=XCOM_TARGET_YMDH)
	validate_param(ymdh, log_type)

	_print_settings(**context)


def _set_target_ymdh(**context):
	"""
	적재 대상 일시 설정
	:param context:
	:return:
	"""
	pp(context)

	target_ymdh = context['params'].get(XCOM_TARGET_YMDH)

	# 파라미터로 들어온 시간으로 설정
	if target_ymdh:
		target_ymdh = str(target_ymdh).strip()
		pendulum.from_format(target_ymdh, 'YYYYMMDDHH')
		logging.info(f'{_LOG_PREFIX} Using from "params". target_ymdh={target_ymdh}')

		# xcom에 push
		context['ti'].xcom_push(XCOM_TARGET_YMDH, target_ymdh)
	# 없으면 에러
	else:
		logging.error(f'{_LOG_PREFIX} There is not "params": target_ymdh')
		raise AirflowException('There is not "params": target_ymdh')


def validate_param(ymdh: str, log_type: str):
	try:
		pendulum.from_format(ymdh, 'YYYYMMDDHH')
	except Exception as ex:
		raise AirflowException(f'"ymdh" format is invalid')

	if log_type != '*' and log_type != 's' and log_type != 'c':
		raise AirflowException(f'"log_type" should be "*" or "s" or "c"')


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK}
Airflow Pool Slot: {POOL_SLOT_NORMAL_33}

Profile: {PROFILE}
Spark Submit Options (ACCU): {_SPARK_SUBMIT_OPTIONS_FOR_ACCU}
Spark Submit Options (COMPACTION): {_SPARK_SUBMIT_OPTIONS_FOR_COMPACTION}
Params:
    target_ymdh={context["ti"].xcom_pull(key=XCOM_TARGET_YMDH)}
    log_type={context["ti"].xcom_pull(key=XCOM_LOG_TYPE)}
------------------------------------------------------------------------------------------
''')


def _check_dup_run(**context):
	"""
	중복실행을 방지하기 위한 방어 로직
	:param context:
	:return:
	"""
	target_ymdh = context['ti'].xcom_pull(key=XCOM_TARGET_YMDH)
	my_dag_run_id = context['dag_run'].run_id
	doc = job_dao.get_job({'type': DAG_ID, 'datetime': target_ymdh, 'running': 1})

	if doc:
		runningDagRuns: List["DagRun"] = DagRun.find(dag_id=DAG_ID, state=DagRunState.RUNNING)
		for dagRun in runningDagRuns:
			ti = dagRun.get_task_instance('setup')
			your_target_ymdh = ti.xcom_pull(key=XCOM_TARGET_YMDH)
			your_run_id = dagRun.run_id
			if (my_dag_run_id != your_run_id and target_ymdh == your_target_ymdh):
				raise AirflowException(f'이미 실행중인 DagRun이 있음. '
				                       f'dagRrunId={dagRun.run_id}({your_target_ymdh}) '
				                       f'target_ymdh={target_ymdh}')
	else:
		pass


def _set_to_run_flag(running: int, **context):
	set_to_run_flag(DAG_ID, context["ti"].xcom_pull(key=XCOM_TARGET_YMDH), running)


def _get_app_args(context: dict):
	target_ymdh = context['ti'].xcom_pull(key=XCOM_TARGET_YMDH)
	log_type = context['ti'].xcom_pull(key=XCOM_LOG_TYPE)
	return [target_ymdh, log_type]


def _clean_up_to_success(**context):
	"""
	성공했으므로 job 삭제
	:param context:
	:return:
	"""
	# op_kwargs
	clean_up(DAG_ID, context["ti"].xcom_pull(key=XCOM_TARGET_YMDH), True)


def _clean_up_to_failure(context: dict):
	"""
	실패했으므로 retryCnt증가시키고 running=0 설정
	:param context:
	:return:
	"""
	# op_args
	clean_up(DAG_ID, context["ti"].xcom_pull(key=XCOM_TARGET_YMDH), False)


with DAG(
		DAG_ID,
		description='실버 적재/컴팩션 스파크 앱 실행',
		tags=['spark', 'silver', 'silversmith', 'compactor', 'compaction', 'hourly', 'juyoun.kim'],
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
		params={
			XCOM_TARGET_YMDH: '',
			XCOM_LOG_TYPE: '',  # "s" or "c" or "*"
		}
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 중복실행 여부 확인
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=_check_dup_run,
	)

	# 해당 시간대의 Jobs.running=1로 설정
	set_to_running = PythonOperator(
		task_id='set_to_running',
		python_callable=_set_to_run_flag,
		op_args=[1]
	)

	# 실버 로그 적재
	task_group_id = 'accumulate_silver'
	accumulate_silver = create_task_group(
		task_group_id,
		tooltip='Silversmith: ' + _SPARK_APP_CLASS_ACCU,
		spark_pool=POOL_SPARK,
		pool_slot=POOL_SLOT_NORMAL_33,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': f'{_SPARK_APP_CLASS_ACCU}',
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMDH}") }}}}',
			'dag_id': DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS_ACCU,
			'app_args_fn': _get_app_args,
			'spark_submit_options': _SPARK_SUBMIT_OPTIONS_FOR_ACCU,
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMDH}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT_1,
		wait_showup_failure_cb=partial(_clean_up_to_failure),
		wait_complete_failure_cb=partial(_clean_up_to_failure),
		conclude_app_failure_cb=partial(_clean_up_to_failure)  # 적재 실패 시 Jobs.retryCnt 증가시키고 Jobs.running=0
	)

	# 실버 컴팩터 실행
	task_group_id = 'compact_silver'
	compact_silver = create_task_group(
		task_group_id,
		tooltip='Silver Compaction: ' + _SPARK_APP_CLASS_COMPACTION,
		spark_pool=POOL_SPARK,
		pool_slot=POOL_SLOT_NORMAL_33,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'task_group_id': task_group_id,
			'aggregator_name': f'{_SPARK_APP_CLASS_COMPACTION}',
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMDH}") }}}}',
			'dag_id': DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS_COMPACTION,
			'app_args_fn': _get_app_args,
			'spark_submit_options': _SPARK_SUBMIT_OPTIONS_FOR_COMPACTION,
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YMDH}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT_1,
		wait_showup_failure_cb=partial(_clean_up_to_failure),
		wait_complete_failure_cb=partial(_clean_up_to_failure),
		conclude_app_failure_cb=partial(_clean_up_to_failure)  # 적재 실패 시 Jobs.retryCnt 증가시키고 Jobs.running=0
	)

	# 최근 실버 로그 적재 일시 갱신
	trigger_refresh_recent_silver_ymdh = TriggerDagRunOperator(
		trigger_dag_id=refresh_recent_silver_ymdh.DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id='trigger_refresh_recent_silver_ymdh',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=True,
		poke_interval=5,  # wait_for_completion=True 일때만 유효한 옵션
		reset_dag_run=True,
		on_failure_callback=partial(_clean_up_to_failure)  # 적재 실패 시 Jobs.retryCnt 증가시키고 Jobs.running=0
	)

	# 성공 시 Jobs 다큐먼트 삭제
	clean_up_to_success = PythonOperator(
		task_id='clean_up_to_success',
		python_callable=_clean_up_to_success
	)

	# gfp 비딩그룹 생성
	trigger_gfp_biddinggroup = TriggerDagRunOperator(
		trigger_dag_id=spark_gfp_biddinggroup.DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id='trigger_gfp_biddinggroup',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=False,
		# poke_interval=3,  # wait_for_completion=True 일때만 유효한 옵션
		reset_dag_run=True,
		conf={'target_ymdh': '{{ ti.xcom_pull(key="target_ymdh") }}'},
	)

	# 골드 인덱스 생성
	trigger_goldsmith = TriggerDagRunOperator(
		trigger_dag_id=spark_goldsmith.DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id='trigger_goldsmith',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=False,
		# poke_interval=3,  # wait_for_completion=True 일때만 유효한 옵션
		reset_dag_run=True,
		conf={'target_ymdh': '{{ ti.xcom_pull(key="target_ymdh") }}'},
	)

	# 파이프라인 real
	setup >> check_dup_run >> set_to_running >> \
	accumulate_silver >> compact_silver >> trigger_refresh_recent_silver_ymdh >> \
	clean_up_to_success >> trigger_gfp_biddinggroup >> trigger_goldsmith
