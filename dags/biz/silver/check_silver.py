"""
### 실버 로그 현황 검사

#### 위키
- [https://wiki.navercorp.com/pages/viewpage.action?pageId=1397001986](https://wiki.navercorp.com/pages/viewpage.action?pageId=1397001986)

#### 개요
- 검사항목
    - 실버 적재가 제대로 이루어졌는지 확인
    - 컴팩션이 제대로 이루어졌는지 확인
    - 퍼블리셔 개수와 컴팩션된 개수가 일치하는지 확인
    - 퍼블리셔 개수가 평균 이하인지 확인
- 위 검사항목에 하나라면 걸린다면 로그에 출력

#### 주기
- 없음

#### config
- from_ymdh: 검사하고 싶은 시작 일시(YYYMMDDHH)
- to_ymdh: 검사하고 싶은 종료 일시(YYYMMDDHH)
"""
import logging
import os
from datetime import datetime, timedelta

import humanize
from airflow import DAG, AirflowException
from airflow.operators.python import PythonOperator
from sqlalchemy_utils.types.enriched_datetime.pendulum_datetime import pendulum

from core import c3
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, ER_SSP_CLIENT, ER_SSP_SERVER, SILVER_HOME
from core.dao.environment_dao import get_environment_value_by_name

DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [CHK-SILVER]'

# https://pypi.org/project/hdfs/
# HDFS CLI : https://hdfscli.readthedocs.io/en/latest/api.html#hdfs.client.Client.list
# hdfs_client = InsecureClient('http://atcdh009-sa.nfra.io:14000')  # test
# hdfs_client = InsecureClient('http://webfs.bizcloud.navercorp.com:14000')  # real

_LOG_TYPES = [ER_SSP_CLIENT, ER_SSP_SERVER]


def _check_silver(**context):
    _set_ymdh(**context)
    _set_avg_pub_cnt(context)

    invalid_infos = _get_invalid_infos(**context)
    _print_invalid_infos(invalid_infos)
    _print_unique_ymdh(invalid_infos)


def _set_ymdh(**context):
    """
    체크 대상 일시 설정
    :param context:
    :return:
    """
    # pp(context)

    from_ymdh = context['params'].get('from_ymdh')
    to_ymdh = context['params'].get('to_ymdh')

    if not from_ymdh:
        raise AirflowException('from_ymdh를 입력해 주세요.')
    else:
        pendulum.from_format(from_ymdh, 'YYYYMMDDHH', DEFAULT_TZ)

    # to_ymdh가 없을 경우 실버 로그 최근 적재일시로 설정
    if not to_ymdh:
        to_ymdh = get_environment_value_by_name('silver-log-recent-accumulation-ymdh')
    else:
        pendulum.from_format(to_ymdh, 'YYYYMMDDHH', DEFAULT_TZ)

    logging.info(f'{_LOG_PREFIX} from_ymdh={from_ymdh} to_ymdh={to_ymdh}')
    context['ti'].xcom_push('from_ymdh', from_ymdh)
    context['ti'].xcom_push('to_ymdh', to_ymdh)


def _set_avg_pub_cnt(context: dict):
    """
    클라/서버 실버 경로의 평균 퍼블리셔 개수 설정
    :param context: 
    :return: 
    """
    avg_pub_cnt = {
        ER_SSP_CLIENT: {
            'sum': 0,
            'cnt': 0
        },
        ER_SSP_SERVER: {
            'sum': 0,
            'cnt': 0
        }
    }

    # 최근 1주일의 퍼블리셔 개수 평균
    end_dt = pendulum.now(DEFAULT_TZ).subtract(hours=10)  # (현재시간 - 10시간) 전
    begin_dt = end_dt.subtract(hours=24 * 7)  # end_dt로부터 7일 전
    while begin_dt < end_dt:
        for log_type in _LOG_TYPES:
            ymd_h = begin_dt.format('YYYYMMDD-HH')
            ymdh_path = f'{SILVER_HOME}/{log_type}/{ymd_h}'

            # 디렉토리가 존재할 때만
            st = _get_status(ymdh_path)
            if st:
                pub_cnt = 0

                # 디렉토리 하위 정보
                infos_under_ymdh = c3.read_dir(ymdh_path)
                for info in infos_under_ymdh:
                    if info['pathSuffix'].find('publisherId=') > -1:
                        pub_cnt += 1

                if pub_cnt > 0:
                    avg_pub_cnt[log_type]['sum'] += pub_cnt
                    avg_pub_cnt[log_type]['cnt'] += 1

        begin_dt = begin_dt.add(hours=1)

    avg_pub_cnt_client = round(avg_pub_cnt[ER_SSP_CLIENT]['sum'] / avg_pub_cnt[ER_SSP_CLIENT]['cnt'])
    avg_pub_cnt_server = round(avg_pub_cnt[ER_SSP_SERVER]['sum'] / avg_pub_cnt[ER_SSP_SERVER]['cnt'])

    logging.info(f'{_LOG_PREFIX} avg_pub_cnt_client={avg_pub_cnt_client}')
    logging.info(f'{_LOG_PREFIX} avg_pub_cnt_server={avg_pub_cnt_server}')

    context['ti'].xcom_push(f'avg_pub_cnt_{ER_SSP_CLIENT}', avg_pub_cnt_client)
    context['ti'].xcom_push(f'avg_pub_cnt_{ER_SSP_SERVER}', avg_pub_cnt_server)


def _get_invalid_infos(**context):
    """
    유효하지 않은 시간대 수집
    :return: 
    """
    from_ymdh = context['ti'].xcom_pull(key='from_ymdh')
    to_ymdh = context['ti'].xcom_pull(key='to_ymdh')

    begin_dt = datetime.strptime(from_ymdh, '%Y%m%d%H')
    end_dt = datetime.strptime(to_ymdh, '%Y%m%d%H')

    invalid_infos = []

    while begin_dt <= end_dt:
        ymd_h = begin_dt.strftime('%Y%m%d-%H')
        logging.info(f'{_LOG_PREFIX} 검사중... {ymd_h}')

        for log_type in _LOG_TYPES:
            ymdh_path = f'{SILVER_HOME}/{log_type}/{ymd_h}'

            st = _get_status(ymdh_path)
            if st:
                # 디렉토리 용량 (https://hadoop.apache.org/docs/r1.0.4/webhdfs.html#ContentSummary)
                # ct = hdfs_client.content(ymdh_path)
                ct = c3.get_content_summary(ymdh_path)
                dir_size = humanize.naturalsize(ct["length"], binary=True)

                # 디렉토리 하위 정보
                infos_under_ymdh = c3.read_dir(ymdh_path)

                # 이 시간대 정보 수집
                success_cnt = sum(['_SUCCESS' in x['pathSuffix'] for x in infos_under_ymdh])
                is_done_accu = True if success_cnt > 0 else False  # 적재가 성공했는지

                comp_success_cnt = sum(['_COMPACTION_SUCCESS' in x['pathSuffix'] for x in infos_under_ymdh])
                is_done_compaction = True if comp_success_cnt > 0 else False  # 컴팩션이 성공했는지

                pub_cnt = sum(['publisherId=' in x['pathSuffix'] for x in infos_under_ymdh])  # 퍼블리셔 개수

                # 이 시간대 밑에 퍼블리셔 정보 수집
                comp_suc_cnt = 0
                invalid_compaction_list = []
                for info in infos_under_ymdh:
                    if info['pathSuffix'].find('publisherId=') > -1:
                        pub_path = f'{ymdh_path}/{info["pathSuffix"]}/'
                        files = c3.read_dir(pub_path)  # 매체 밑에 parquet 파일 개수
                        success_cnt = sum(['_SUCCESS' in f['pathSuffix'] for f in files])
                        is_exist_compaction_succ_file = True if success_cnt > 0 else False  # 매체 밑에 _SUCCESS 파일 존재여부

                        if is_exist_compaction_succ_file == 1:
                            comp_suc_cnt += 1
                        else:
                            invalid_compaction_list.append({'pub_path': pub_path, 'msg': '컴팩션 _SUCCESS 없음'})

                invalid_info = {}

                # 검사항목 1. 실버 적재가 제대로 이루어졌는지 확인
                if is_done_accu != 1:
                    invalid_info['accu'] = f'_SUCCESS 없음'

                # 검사항목 2. 컴팩션이 제대로 이루어졌는지 확인
                if is_done_compaction != 1:
                    invalid_info['compact'] = f'_COMPACTION_SUCCESS 없음'

                # 검사항목 3. 퍼블리셔 개수와 컴팩션된 개수가 일치하는지 확인
                if comp_suc_cnt != pub_cnt:
                    invalid_info['compact_diff'] = f'퍼블리셔개수={pub_cnt} 컴팩션된개수={comp_suc_cnt}'
                    invalid_info['invalid_compaction_list'] = invalid_compaction_list

                # 검사항목 4. 퍼블리셔 개수가 평균 이하임.
                avg_pub_cnt = context['ti'].xcom_pull(key=f'avg_pub_cnt_{log_type}')
                if pub_cnt < avg_pub_cnt:
                    diff = avg_pub_cnt - pub_cnt
                    more_or_less = '-' if diff > 0 else '+'
                    invalid_info['abnormal_pub_cnt'] = f'퍼블리셔개수={pub_cnt} 평균개수={avg_pub_cnt} 차이={more_or_less}{diff}'

                # 검사항목에 하나라도 해당된다면 목록에 추가
                if len(invalid_info) > 0:
                    invalid_info['ymdh'] = ymd_h
                    invalid_info['ymdh_path'] = ymdh_path
                    invalid_info['dir_size'] = dir_size
                    invalid_infos.append(invalid_info)
            else:
                invalid_infos.append({'ymdh': ymd_h, 'ymdh_path': ymdh_path, 'accu': '디렉토리 존재하지 않음'})

        begin_dt = begin_dt + timedelta(hours=1)

    return invalid_infos


def _get_status(path: str) -> dict:
    """
    HDFS 해당 경로의 상태 조회
    :param path: 
    :return: 
    """

    try:
        st = c3.get_file_status(path)
        return st
    except Exception as ex:
        if str(ex).find('File does not exist') > -1 or str(ex).find('FileNotFound') > -1:
            return None


def _print_invalid_infos(invalid_infos):
    """
    유효하지 않은 시간대 출력(client, server 각각)
    :param invalid_infos:
    :return:
    """

    msg = ''
    if len(invalid_infos) < 1:
        msg += f'\t없음'

    for info in invalid_infos:
        msg += f"\n\t{info['ymdh']} {info['ymdh_path']} ({info['dir_size']})"

        if 'accu' in info:
            msg += f'\n\t\t적재: {info["accu"]}'

        if 'compact' in info:
            msg += f'\n\t\t컴팩션: {info["compact"]}'

        if 'compact_diff' in info:
            msg += f'\n\t\t퍼블리셔 개수와 컴팩션된 개수가 다름: {info["compact_diff"]}'
            if 'invalid_compaction_list' in info:
                for iv_compact in info['invalid_compaction_list']:
                    msg += f'\n\t\t\t{iv_compact}'

        if 'abnormal_pub_cnt' in info:
            msg += f'\n\t\t컴팩션된 퍼블리셔 개수가 평상시 개수에 못 미침: {info["abnormal_pub_cnt"]}'

    logging.info(f'{_LOG_PREFIX}\n[ 유효하지 않은 시간대 ]{msg}')


def _print_unique_ymdh(invalid_infos):
    """
    유효하지 않은 일시 유일하게 출력
    :param invalid_infos: 
    :return: 
    """
    if len(invalid_infos) < 1:
        return

    ymdh_list = []
    for info in invalid_infos:
        ymdh_list.append(info['ymdh'])
    unique_ymdh_list = list(set(ymdh_list))
    unique_ymdh_list.sort()

    msg = ''
    for idx, ymdh in enumerate(unique_ymdh_list):
        if idx > 0:
            msg += f' \t, "{ymdh}"\n'
        else:
            msg += f' \t  "{ymdh}"\n'

    logging.info(f'{_LOG_PREFIX}\n[ 유효하지 않은 일시(uniquely) size={len(unique_ymdh_list)} ]\n{msg}')


with DAG(
        DAG_ID,
        description='실버 적재/컴팩션 상황 체크',
        tags=['silver', 'check', 'juyoun.kim'],
        default_args={
            'owner': 'juyoun.kim',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
        schedule_interval=None,
        catchup=False,
        params={
            'USAGE': "Format is YYYYMMDDHH. If there is no 'to_ymdh', it will be applied as silver log's recent accumulation ymdh.",
            'from_ymdh': '',
            'to_ymdh': '',
        },
) as dag:
    dag.doc_md = __doc__

    check_silver = PythonOperator(
        task_id='check_silver',
        python_callable=_check_silver,
    )

    check_silver
