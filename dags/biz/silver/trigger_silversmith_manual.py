"""
### 실버 수동 처리

#### 위키
- [05-3. 실버 생성 수동 처리](https://wiki.navercorp.com/pages/viewpage.action?pageId=1402969276)

#### 개요
- 수동 실행 (스케줄 되지 않음)
- config로 받은 시간대를 Jobs 컬렉션에 추가
- 재처리와 정규처리를 분리해 서로 독립적으로 실행하여 정규 처리에 영향을 주지 않도록 함
- 정규처리 대상인지, 재처리 대상인지 구분
  - 정규처리 대상: retryCnt = -1
  - 재처리 대상: retryCnt >= 0 and retryCnt < job_dao.MAX_RETRY_CNT
- 한 번에 병렬도만큼 동시 처리
  - 병렬도는 Data DB > Environments.name='silver-log-parallel-level'에 정의되어 있음
  - 정규는 순차처리하지만 재처리는 병렬처리
  - 한꺼번에 많은 시간대를 재처리해야 할 경우 반영 속도를 높이기 위함
- 한 번 떴을 때 사용자가 요청한 날짜를 모두 처리하고 끝냄
- 성공하면 Jobs 다큐먼트를 삭제하고, 실패하면 retryCnt를 증가시킴
  - 실패한 job은 자동으로 재처리하지 않음.
  - 원인 파악을 위해 실패 건 들을 이메일로 전송


#### config
- from_target_ymdh, to_target_ymdh : 두 값 사이(edge 포함)의 모든 datetime 을 대상으로 하는 스케줄 실행
	- format : "YYYYMMDD"
- target_ymdh_list : 입력한 값들에 속하는 datetime 을 대상으로 하는 스케줄 실행
	- comma로 구분된 ym 를 공백없이 나열 (정렬 필요 없음)
	- format : "YYYYMMDD,YYYYMMDD,YYYYMMDD, .. "
- from_target_ymdh, to_target_ymdh, target_ymdh_list
	- 세 값 모두 입력한 경우 from/to 우선
	- 모두 입력하지 않은 경우 에러
- log_type
	- "S"(er-ssp-server) 또는 "C"(er-ssp-server) 또는 "*"(둘 다)
"""
import logging
import os
from time import sleep

import pendulum
from airflow import AirflowException
from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email
from airflow.utils.state import DagRunState
from airflow.utils.types import DagRunType

from biz.silver import spark_silversmith
from core.airflow_api import trigger_dagrun
from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, XCOM_TARGET_YMDH, XCOM_TARGET_YMDH_LIST, XCOM_LOG_TYPE
from core.dao import job_dao, environment_dao
from core.utils import get_between_ymdh_list, get_between_ymdh_list

# DAG 기본정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [TRG-SILVERSMITH-MANUAL]'


def _setup(**context):
	# 날짜 설정
	_set_ymdh_list(**context)

	# logType 설정
	log_type = context['params'].get(XCOM_LOG_TYPE).lower()
	context['ti'].xcom_push(XCOM_LOG_TYPE, log_type)

	ymdh_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMDH_LIST)
	for ymdh in ymdh_list:
		spark_silversmith.validate_param(ymdh, log_type)


def _set_ymdh_list(**context):
	"""
	config를 이용해 재실행할 대상일시(ymdh) 설정
	:param context:
	:return:
	"""
	from_ymdh = context['params'].get('from_target_ymdh')
	to_ymdh = context['params'].get('to_target_ymdh')
	target_ymdh_list: str = context['params'].get('target_ymdh_list')

	ymdh_list = []

	if from_ymdh or to_ymdh:
		from_ymdh = from_ymdh.strip()
		pendulum.from_format(from_ymdh, 'YYYYMMDDHH')
		if to_ymdh:
			to_ymdh = to_ymdh.strip()
			pendulum.from_format(to_ymdh, 'YYYYMMDDHH')
			if from_ymdh > to_ymdh:
				raise AirflowException(f'from_target_ymdh은 to_target_ymdh보다 작거나 같아야 합니다.'
									   f' from_ymd={from_ymdh} to_ymd={to_ymdh}')
			else:
				ymdh_list = get_between_ymdh_list(from_ymdh, to_ymdh)
				logging.info(f'{_LOG_PREFIX} from_target_ymdh / to_target_ymdh를 이용한 실행'
							 f' from_ymdh={from_ymdh} to_ymdh={to_ymdh} ymdh_list={ymdh_list}')
		else:
			raise AirflowException(f'to_target_ymdh이 기술되지 않음. from_ymdh={from_ymdh}')
	elif target_ymdh_list:
		target_ymdh_list = target_ymdh_list.strip(', ')
		ymdh_list = target_ymdh_list.split(',')
		logging.info(f'{_LOG_PREFIX} target_ymdh_list를 이용한 실행. {ymdh_list}')
	else:
		raise AirflowException(f'"config"가 기술되지 않음')

	# xcom에 push
	context['ti'].xcom_push(XCOM_TARGET_YMDH_LIST, ymdh_list)


def _check_dup_run(**context):
	"""
	사용자가 요청한 일시가 이미 다른 DAG에서 돌고 있는지
	:param context:
	:return:
	"""
	ymdh_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMDH_LIST)

	# My DAG
	my_ymd_set = set(ymdh_list)
	my_dag_run_id = context['dag_run'].run_id

	# Others DAG
	dag_runs: list[DagRun] = DagRun.find(dag_id=_DAG_ID, state=DagRunState.RUNNING)
	for dag_run in dag_runs:
		if my_dag_run_id != dag_run.run_id:
			ti = dag_run.get_task_instance('setup')
			running_target_ymdh_list = ti.xcom_pull(key=XCOM_TARGET_YMDH_LIST)
			intersection = list(my_ymd_set & set(running_target_ymdh_list))
			if intersection:
				raise AirflowException(
					f'{", ".join(intersection)} 가 이미 다른 DAG RUN에 의해 실행중입니다. '
					f'other_dag_run_id={dag_run.run_id} other_ymdh_list={running_target_ymdh_list}')


def _insert_silversmith_jobs(**context):
	"""
	job 추가
	:param context:
	:return:
	"""
	ymdh_list = context['ti'].xcom_pull(key=XCOM_TARGET_YMDH_LIST)
	log_type = context['ti'].xcom_pull(key=XCOM_LOG_TYPE)
	my_dag_run_id = context['dag_run'].run_id

	for ymdh in ymdh_list:
		logging.info(f'{_LOG_PREFIX} 처리해야 할 일시={ymdh} / {", ".join(ymdh_list)} log_type={log_type}')

		# 날짜별로 spark_silversmith job 생성
		filter = {
			'type': spark_silversmith.DAG_ID,
			'datetime': ymdh,
			'manual': 1,
		}
		job = job_dao.get_job(filter)
		if job:
			# job이 이미 있으면 retryCnt=-1
			update = {
				'$set': {
					'retryCnt': -1,
					'detail.dagRunId': my_dag_run_id,
					'modifiedAt': pendulum.now(DEFAULT_TZ)
				}
			}
			job_dao.update_job(filter, update)
			logging.warning(f'이미 있으므로 추가하지 않고 retryCnt=-1로 업데이트. job={job}')
		else:
			# job이 없다면 추가
			job = {
				'type': spark_silversmith.DAG_ID,
				'datetime': ymdh,
				'detail': {
					'logType': log_type,
					'dagRunId': my_dag_run_id
				},
				'retryCnt': -1,
				'manual': 1,
				'running': 0,
			}
			job_dao.insert_job(job)

	logging.info(f'{_LOG_PREFIX} Jobs에 추가 완료. log_type={log_type} {len(ymdh_list)}건')


def _run_parallel_spark_silversmith(**context):
	"""
	일시별 spark_silversmith 트리거
	:param context:
	:return:
	"""
	# 병렬도 설정
	value = environment_dao.get_environment_value_by_name('silver-log-parallel-level')
	_MAX_PARALLEL_LEVEL = int(value) if value else 3  # 디폴트 3
	logging.info(f'{_LOG_PREFIX} _MAX_PARALLEL_LEVEL={_MAX_PARALLEL_LEVEL}')

	my_dag_run_id = context['dag_run'].run_id
	logging.info(f'{_LOG_PREFIX} my_dag_run_id={my_dag_run_id}')

	start_time = pendulum.now(DEFAULT_TZ)
	triggered_cnt = 0
	while True:
		remained_list = _get_remained_list(my_dag_run_id)

		if len(remained_list) == 0:
			end_time = pendulum.now(DEFAULT_TZ)
			elapsed_time = end_time.diff(start_time)
			minutes = elapsed_time.in_minutes()
			seconds = elapsed_time.in_seconds() % 60
			logging.info(f'{_LOG_PREFIX}\t수동처리 완료. 소요시간 {minutes}분 {seconds}초\n')
			break
		else:
			# 진행중인 작업 목록 조회
			ing_list = _get_ing_list(my_dag_run_id)
			ing_cnt = len(ing_list) if ing_list else 0

			# 동시 처리를 위한 여분 설정
			spare_cnt = _MAX_PARALLEL_LEVEL - ing_cnt
			if spare_cnt == 0:  # 여유분 없이 꽉 찼음
				pass
			elif spare_cnt > 0:  # 여유분이 있을 경우
				logging.info(f'{_LOG_PREFIX} 여유분={spare_cnt}')
				# 여유분 만큼 job 목록 가겨오기
				standby_list = _get_standby_list(my_dag_run_id, spare_cnt)

				# 여유분만큼 스파크 트리거 병렬처리
				if standby_list:
					triggered_job_ids = []
					for job in standby_list:
						job_id = job.get('_id')

						ymdh = job.get('datetime')
						log_type = job.get('detail').get('logType')

						conf = {
							XCOM_TARGET_YMDH: ymdh,  # YYYYMMDDHH
							XCOM_LOG_TYPE: log_type,
						}

						# 스파크 트리거
						spark_dag_run_id = trigger_dagrun(spark_silversmith.DAG_ID,
														  pendulum.now(DEFAULT_TZ),
														  conf,
														  dag_run_type=DagRunType.SCHEDULED)

						triggered_job_ids.append(job_id)
						triggered_cnt = triggered_cnt + 1

						logging.info(
							f'{_LOG_PREFIX} {spark_silversmith.DAG_ID} 트리거시킴. spark_dag_run_id={spark_dag_run_id} ({triggered_cnt}) '
							f'{ymdh} log_type={log_type}')

					# job이 running=1로 바뀔 때까지 대기
					# 그래야 다음 루프에서 _get_remained_list()를 했을 때 다시 추출되지 않음
					# spark_silversmith._upsert_job()에서 running=1로 바꿈
					_wait_for_job_running(triggered_job_ids)

			else:  # 여유분이 음수란 얘기는 진행 중인 작업의 개수가 _MAX_PARALLEL_LEVEL보다 많다는 뜻
				msg = f'-----------  _MAX_PARALLEL_LEVEL({_MAX_PARALLEL_LEVEL}) OVER  ing_cnt={ing_cnt} ------------\n'
				for ing in ing_list:
					msg = msg + f'\t{ing}\n'
				logging.warning(f'{_LOG_PREFIX}{msg}')

				raise AirflowException(
					f'진행중인 작업이 _MAX_PARALLEL_LEVEL({_MAX_PARALLEL_LEVEL})보다 많습니다. ing_cnt={ing_cnt}')

			sleep(15)


def _get_remained_list(my_dag_run_id: str) -> list:
	"""
	남은 작업 수 조회
	:return:
	"""
	filter = {
		'type': spark_silversmith.DAG_ID,
		'manual': 1,
		'retryCnt': -1,
		'detail.dagRunId': my_dag_run_id
	}
	remained_list = job_dao.get_jobs(filter)
	return remained_list


def _get_standby_list(my_dag_run_id: str, spare_cnt: int) -> list:
	"""
	대기 작업 조회. spare_cnt 만큼만
	:return:
	"""
	filter = {
		'type': spark_silversmith.DAG_ID,
		'manual': 1,
		'retryCnt': -1,
		'detail.dagRunId': my_dag_run_id,
		'running': 0,
	}
	sort = [('_id', 1)]
	remained_list = job_dao.get_jobs(filter, sort, spare_cnt)
	return remained_list


def _get_ing_list(my_dag_run_id: str) -> list:
	"""
	진행중인 작업 수 조회
	:return:
	"""
	filter = {
		'type': spark_silversmith.DAG_ID,
		'manual': 1,
		'retryCnt': -1,
		'detail.dagRunId': my_dag_run_id,
		'running': 1,
	}
	ing_list = job_dao.get_jobs(filter)
	return ing_list


def _get_failed_list(my_dag_run_id: str) -> list:
	"""
	실패한 작업 수 조회
	:return:
	"""
	filter = {
		'type': spark_silversmith.DAG_ID,
		'manual': 1,
		'retryCnt': {'$gte': 0},
		'detail.dagRunId': my_dag_run_id,
	}
	failed_list = job_dao.get_jobs(filter)
	return failed_list


def _wait_for_job_running(triggered_job_ids: list):
	"""
	트리거 시킨 spark_dag_run과 관련된 Job.running = 0인 것이 하나라도 있다면 대기
	retryCnt:-1인 것만 조회. 실패한 것은 제외.
	혹은 너무 빨리 끝나서 job이 모두 없어졌다면 종료
	:param job_id:
	:return:
	"""
	sleep(10)
	while True:
		jobs = job_dao.get_jobs({'_id': {'$in': triggered_job_ids}, 'retryCnt': -1})
		if jobs:
			unrunning_cnt = 0
			for job in jobs:
				if job.get('running') == 0:
					unrunning_cnt = unrunning_cnt + 1

			logging.info(f'{_LOG_PREFIX} len(triggered_job_ids)={len(triggered_job_ids)} unrunning_cnt={unrunning_cnt}')
			if unrunning_cnt < 1:
				break
			else:
				sleep(10)
		else:
			break


def _conclude(**context):
	"""
	실패 건이 있을 경우 이메일 전송
	:return:
	"""
	my_dag_run_id = context['dag_run'].run_id
	failed_list = _get_failed_list(my_dag_run_id)
	if len(failed_list):
		log_type = context['ti'].xcom_pull(key=XCOM_LOG_TYPE)
		failed_msg = f'dagRunId: {my_dag_run_id} log_type: {log_type}</br></br>'
		for item in failed_list:
			failed_msg += f'&nbsp;&nbsp;&nbsp;&nbsp;ymd: {item.get("datetime")}<br/>'

		title = f'[실버 적재 수동 실행에 실패 건이 있음] 건수={len(failed_list)}'
		body = f'{failed_msg}<br/>'
		send_email(ALERT_EMAIL_ADDRESSES, title, body)

		logging.info(f'{_LOG_PREFIX}실버 적재 수동 처리에 실패한 작업이 있음. {len(failed_list)} 건.\n{body}\n')


with DAG(
		_DAG_ID,
		description='실버 생성 수동 처리',
		default_args={
			'owner': 'juyoun.kim',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2024, 4, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		tags=['trigger', 'silver', 'silversmith', 'manual', 'juyoun.kim'],
		catchup=False,
		params={
			'from_target_ymdh': '',
			'to_target_ymdh': '',
			'target_ymdh_list': '',
			'log_type': '*',  # "s" or "c" or "*"
		},
) as dag:
	dag.doc_md = __doc__

	# 필요한 정보 세팅
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup
	)

	# 중복 실행중인 날짜가 있는지 체크
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=_check_dup_run,
	)

	# 날짜별 job 추가
	insert_silversmith_jobs = PythonOperator(
		task_id='insert_silversmith_jobs',
		python_callable=_insert_silversmith_jobs,
	)

	# 병렬처리
	run_parallel_spark_silversmith = PythonOperator(
		task_id='run_parallel_spark_silversmith',
		python_callable=_run_parallel_spark_silversmith
	)

	# 결과
	conclude = PythonOperator(
		task_id='conclude',
		python_callable=_conclude
	)

	# 파이프라인
	setup >> check_dup_run >> insert_silversmith_jobs >> run_parallel_spark_silversmith >> conclude
