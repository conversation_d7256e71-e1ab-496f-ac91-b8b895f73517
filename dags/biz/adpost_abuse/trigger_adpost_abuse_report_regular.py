"""
### ADPOST 어뷰즈 리포트 정규 트리거 DAG

#### 0. 위키
- [32. DAG - Adpost 어뷰즈 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=1620915937)
- [12. 비지니스 - Adpost 어뷰즈 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=1624088836)

#### 1. 주기
- 매월 22일 09시

#### 2. 트리거 대상 DAG
- spark_adpost_abuse_report

#### 3. config
- target_ym : 처리할 날짜 YYYYMM ( default: 전달 )

#### 4. max_active_runs=1
"""

import os
import logging
import pendulum

from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.utils.types import DagRunType
from airflow.operators.python import PythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YM

from biz.adpost_abuse.spark_adpost_abuse_report import DAG_ID as SPARK_DAG_ID

_DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [TRIGGER-ADPOST-ABUSE-REPORT-REGULAR]'


def _setup(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
	- target_ym

	:param context:
	"""
	target_ym = context['params'][XCOM_TARGET_YM]
	if target_ym:
		pendulum.from_format(target_ym, 'YYYYMM')
		logging.info(f'{_LOG_PREFIX} Using target_ym from "params". target_ym={target_ym}')
	else:
		target_ym = pendulum.instance(context['logical_date']).in_tz(DEFAULT_TZ).add(months=1).format('YYYYMM')
		logging.info(f'{_LOG_PREFIX} Using target_ym from tz-shifted "logical_date". target_ym={target_ym}, logical_date={context["logical_date"]}')

	context['ti'].xcom_push(XCOM_TARGET_YM, target_ym)


with DAG(
		_DAG_ID,
		description='Trigger regular adpost abuse report DAG',
		tags=['trigger', 'regular', 'abuse', 'adpost', 'adpost_abuse', 'monthly', 'hdfs', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			'target_ym': '',
		},
		start_date=pendulum.datetime(2023, 7, 1, tz=DEFAULT_TZ),
		schedule_interval='0 9 22 * *',  # 매월 22일 09시
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	trigger_adpost_abuse_report = TriggerDagRunOperator(
		trigger_dag_id=SPARK_DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{SPARK_DAG_ID}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=False,
		poke_interval=60,
		reset_dag_run=True,
		conf={
			XCOM_TARGET_YM: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YM}") }}}}',
			'trigger_by': DagRunType.SCHEDULED,
		}
	)

	setup >> trigger_adpost_abuse_report
