"""
### ADPOST 어뷰즈 리포트 재처리 트리거 DAG

#### 0. 위키
- [32. DAG - Adpost 어뷰즈 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=1620915937)
- [12. 비지니스 - Adpost 어뷰즈 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=1624088836)

#### 1. 주기
- 매월 22-23일 3시간 간격

#### 2. 스케쥴 콜렉션
- Data DB : Jobs
- Jobs 를 사용하는 경우, 가장 과거 날짜가 끝나기 전까지는 다음 날짜를 처리하지 못한다.

#### 3. 트리거 대상 DAG
- spark_adpost_abuse_report

#### 4. max_active_runs=1
"""

import os
import logging
import pendulum

from airflow.models import DagRun
from airflow.models.dag import DAG
from airflow.utils.types import DagRunType
from airflow.operators.python import ShortCircuitOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator

from core.dao import job_dao
from core.base import DEFAULT_TZ, XCOM_TARGET_YM, ALERT_EMAIL_ADDRESSES

from biz.adpost_abuse.spark_adpost_abuse_report import DAG_ID as SPARK_DAG_ID

# DAG 기본 정보
_DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [TRIGGER-ADPOST-ABUSE-REPORT-REPROCESS]'


def _setup(**context):
	"""
	가장 오래된 실패 이력 1건만 가져와서 target_ym 설정
	:param context:
	:return:
	"""
	doc = job_dao.get_job({'type': SPARK_DAG_ID, 'manual': 0})
	if doc:
		if doc['retryCnt'] >= job_dao.MAX_RETRY_CNT:
			# monitor_jobs 에서 알림 메일 발송하기 때문에, 별도 처리 하지 않음
			logging.warning(
				f'{_LOG_PREFIX} {doc["datetime"]}의 retryCnt={doc["retryCnt"]}. 최대처리횟수({job_dao.MAX_RETRY_CNT})에 도달해서 스킵.')
		elif doc['running'] == 1:
			logging.warning(
				f'{_LOG_PREFIX} {doc["datetime"]} 이미 처리 중이므로 스킵. doc={doc}')
		else:
			context['ti'].xcom_push(XCOM_TARGET_YM, doc['datetime'])
			return True
	else:
		logging.info(f'{_LOG_PREFIX} 처리할 job 없음')

	return False


with DAG(
		_DAG_ID,
		description='Trigger reprocess adpost abuse report DAG',
		tags=['trigger', 'reprocess', 'abuse', 'adpost', 'adpost_abuse', 'monthly', 'hdfs', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		start_date=pendulum.datetime(2023, 7, 1, tz=DEFAULT_TZ),
		schedule_interval='0 */3 22-23 * *',  # 매월 22-23일 3시간 간격
		catchup=False,
		max_active_runs=1,
) as dag:
	dag.doc_md = __doc__

	# 처리해야 할 job이 있을 때만 다운스트림 태스크 진행
	# Jobs 에서 시간대순으로 하나의 시간대만 추출
	setup = ShortCircuitOperator(
		task_id='setup',
		python_callable=_setup,
	)

	trigger_adpost_abuse_report = TriggerDagRunOperator(
		trigger_dag_id=SPARK_DAG_ID,
		trigger_run_id=f'{DagRun.generate_run_id(DagRunType.SCHEDULED, pendulum.now(DEFAULT_TZ))}',
		task_id=f'trigger_{SPARK_DAG_ID}',
		execution_date=pendulum.now(DEFAULT_TZ),
		wait_for_completion=False,
		poke_interval=60,
		reset_dag_run=True,
		conf={
			XCOM_TARGET_YM: f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YM}") }}}}',
			'trigger_by': DagRunType.SCHEDULED,
		}
	)

	setup >> trigger_adpost_abuse_report
