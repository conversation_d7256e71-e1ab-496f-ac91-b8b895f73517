"""
### ADPOST 어뷰즈 리포트 스파크 집계

#### 1. 위키
- [32. DAG - Adpost 어뷰즈 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=1620915937)
- [12. 비지니스 - Adpost 어뷰즈 리포트](https://wiki.navercorp.com/pages/viewpage.action?pageId=1624088836)

#### 2. 저장소
- hdfs : /user/gfp-data/abuse-report/yyyy/mm/publisherId=xxx/gfp_yyyymm.csv

#### 3. config
- target_ym : 처리할 날짜 YYYYMM ( default: 전달 )
- trigger_by : scheduled or manual ( 로깅용 )

#### 4. 타임아웃
- wait_for_log : 3시간 (10분 간격으로 retry)
"""

import os
import logging
import pendulum

from typing import List
from functools import partial
from datetime import timedelta

from airflow.utils.types import DagRunType
from airflow.utils.state import DagRunState
from airflow.models import DAG, DagRun
from airflow.exceptions import AirflowException
from airflow.sensors.python import PythonSensor
from airflow.operators.python import PythonOperator

from core import utils
from core.base import DEFAULT_TZ, PROFILE, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YM, \
	PROFILE_LOCAL, PROFILE_DEV, PROFILE_TEST, PROFILE_STAGE, PROFILE_REAL, SPARK_SUBMIT_OPTIONS, \
	POOL_SPARK, SPARKLING_APP_JAR_PATH, SPARKLING_IMAGE, \
	SPARKLING_APP_HARD_LIMIT_1 as SPARKLING_APP_HARD_LIMIT

from core.spark_pool import POOL_SLOT_TRIVIAL_2
from core.spark_task_group import create_task_group
from core.spark_submit_op import invoke_job_with_args

from core.dao import job_dao

from biz.adpost_abuse.adpost_abuse_report_dao import get_adpost_abuse_publisher_ids, is_ready_abuse_report_log

# DAG 기본 정보
DAG_ID = _DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')
_LOG_PREFIX = '.......... [SPARK-ADPOST-ABUSE-REPORT]'

_XCOM_PUBLISHER_IDS = 'publisher_ids'

# Spark
_SPARK_APP_CLASS = 'com.navercorp.gfp.biz.abuse.AdpostAbuseAggregator'

# Profile
_PROFILE_SETTINGS = {
	PROFILE_TEST: {
		SPARK_SUBMIT_OPTIONS: """
			--num-executors 3
			--executor-cores 3
			--executor-memory 1g
			--conf spark.executor.memoryOverhead=500m
			--conf spark.sql.shuffle.partitions=5
			--conf spark.sql.files.maxPartitionBytes=32mb
			""".split()
	},
	PROFILE_REAL: {
		SPARK_SUBMIT_OPTIONS: """
			--num-executors 3
			--executor-cores 3
			--executor-memory 1g
			--conf spark.executor.memoryOverhead=500m
			--conf spark.sql.shuffle.partitions=5
			--conf spark.sql.files.maxPartitionBytes=32mb
			--conf spark.hadoop.mapred.output.compress=false
			""".split()
	}
}
_PROFILE_SETTINGS[PROFILE_LOCAL] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_DEV] = _PROFILE_SETTINGS[PROFILE_TEST]
_PROFILE_SETTINGS[PROFILE_STAGE] = _PROFILE_SETTINGS[PROFILE_REAL]

# 디폴트 3시간
_LOG_CHECK_TIMEOUT = 10800


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""
	_init_settings(**context)
	_print_settings(**context)


def _init_settings(**context):
	"""
	DAG 실행에 필요한 설정 정보 초기화
	:param context:
	:return:
	"""
	target_ym = context['params'][XCOM_TARGET_YM]

	if target_ym:
		target_ym = str(target_ym).strip()
		pendulum.from_format(target_ym, 'YYYYMM')

		context['ti'].xcom_push(XCOM_TARGET_YM, target_ym)
	else:
		# target_ym 가 없으면 에러
		logging.error(f'There is not "params.target_ym"')
		raise AirflowException('There is not "params.target_ym"')

	# Adpost Abuse 리포트 대상 매체 id 목록 정보 추출
	publisher_ids = get_adpost_abuse_publisher_ids()

	if publisher_ids:
		context['ti'].xcom_push(_XCOM_PUBLISHER_IDS, publisher_ids)
	else:
		# publisher_ids 가 없으면 에러
		logging.error(f'There is not "publisher_ids"')
		raise AirflowException('There is not "publisher_ids"')


def _print_settings(**context):
	logging.info(f'''
---------------------------------------- Settings ----------------------------------------
Airflow Pool: {POOL_SPARK}
Airflow Pool Slot: {POOL_SLOT_TRIVIAL_2}

Profile: {PROFILE}
Spark Submit Options: {_PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS]}

Params:
	target_ym= {context['ti'].xcom_pull(key=XCOM_TARGET_YM)}
	publisher_ids= {context['ti'].xcom_pull(key=_XCOM_PUBLISHER_IDS)}
------------------------------------------------------------------------------------------
''')


def _check_dup_run(**context):
	"""
	중복 실행을 방지하기 위한 방어 로직
	:param context:
	:return:
	"""
	target_ym = context['ti'].xcom_pull(key=XCOM_TARGET_YM)
	dag_run_id = context['dag_run'].run_id
	doc = job_dao.get_job({'type': _DAG_ID, 'datetime': target_ym, 'running': 1})

	if doc:
		running_dag_runs: List["DagRun"] = DagRun.find(dag_id=_DAG_ID, state=DagRunState.RUNNING)
		for running_dag_run in running_dag_runs:
			ti = running_dag_run.get_task_instance('setup')
			running_target_ym = ti.xcom_pull(key=XCOM_TARGET_YM)
			if dag_run_id != running_dag_run.run_id and target_ym == running_target_ym:
				raise AirflowException(f'이미 실행 중인 DagRun 이 있음 ({running_dag_run.run_id} :: {running_target_ym})')
	else:
		pass


def _upsert_job(**context):
	"""
	해당 시간대의 Job 추가 또는 running=1 로 업데이트
	:param context:
	:return:
	"""
	target_ym = context["ti"].xcom_pull(key=XCOM_TARGET_YM)

	filters = {'type': _DAG_ID, 'datetime': target_ym}
	doc = job_dao.get_job(filters)

	if doc:
		update = {'$set': {'running': 1, 'modifiedAt': pendulum.now(tz=DEFAULT_TZ)}}
		job_dao.update_job(filters, update)
	else:
		trigger_by = context['params'].get('trigger_by')
		doc = {
			'type': _DAG_ID,
			'datetime': target_ym,
			'retryCnt': -1,
			'running': 1,
			'manual': 0 if trigger_by == DagRunType.SCHEDULED else 1
		}
		job_dao.insert_job(doc)
		logging.info(f'{_LOG_PREFIX} {target_ym} Jobs 에 추가. {doc}')


def _wait_for_log(**context):
	"""
	어뷰즈 연동 로그 적재가 완료되었는지 체크
		- Adpost Abuse 리포트 대상 매체 id 목록을 기준으로 체크

	:param context:
	:return is_ready: Boolean
	"""

	target_ym = context["ti"].xcom_pull(key=XCOM_TARGET_YM)
	publisher_ids = context["ti"].xcom_pull(key=_XCOM_PUBLISHER_IDS)

	is_ready = is_ready_abuse_report_log(target_ym, publisher_ids)

	logging.info(f'{_LOG_PREFIX} 어뷰즈 리포트 연동 완료 유무 = {is_ready}')

	return is_ready


def _clean_up_to_success(**context):
	"""
	성공했으므로 job 삭제
	:param context:
	:return:
	"""
	job_dao.clean_up(_DAG_ID, context["ti"].xcom_pull(key=XCOM_TARGET_YM), True)


def _clean_up_to_failure(context: dict):
	"""
	실패했으므로 retryCnt 증가시키고 running=0 설정
	:param context:
	:return:
	"""
	job_dao.clean_up(_DAG_ID, context["ti"].xcom_pull(key=XCOM_TARGET_YM), False)


def _get_app_args(context: dict):
	"""
	spark-submit 시, 넘겨줄 파라미터 정보
	:param context:
	:return:
	"""
	target_ym = context['ti'].xcom_pull(key=XCOM_TARGET_YM)
	logging.info(f'{_LOG_PREFIX} target_ym= {target_ym}')
	return [target_ym]


with DAG(
		_DAG_ID,
		description='Invoke spark app for adpost abuse report',
		tags=['spark', 'abuse', 'adpost', 'adpost_abuse', 'monthly', 'hdfs', 'bitna.cho'],
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
		},
		params={
			XCOM_TARGET_YM: '',
			'trigger_by': '',
		},
		start_date=pendulum.datetime(2023, 7, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	# 환경 설정
	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	# 중복 실행 여부 확인
	check_dup_run = PythonOperator(
		task_id='check_dup_run',
		python_callable=_check_dup_run,
	)

	# 해당 시간대의 Jobs.running=1 로 추가
	upsert_job = PythonOperator(
		task_id='upsert_job',
		python_callable=_upsert_job
	)

	wait_for_log = PythonSensor(
		task_id='wait_for_log',
		python_callable=_wait_for_log,
		poke_interval=60 * 10,  # 10분마다 재시도
		execution_timeout=timedelta(seconds=_LOG_CHECK_TIMEOUT),  # 3시간 동안
		on_failure_callback=partial(_clean_up_to_failure)
	)

	task_group_id = 'spark_run_adpost_abuse_report'
	spark_run_adpost_abuse_report = create_task_group(
		task_group_id,
		tooltip=_SPARK_APP_CLASS,
		spark_pool=POOL_SPARK,
		pool_slot=POOL_SLOT_TRIVIAL_2,
		tz=DEFAULT_TZ,
		summary_history_kwargs={
			'aggregator_name': _SPARK_APP_CLASS,
			'dt': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YM}") }}}}',
			'dag_id': _DAG_ID,
			'dag_run_id': '{{ run_id }}',
			'dag_run_url': f'{utils.get_dag_run_url(_DAG_ID, "{{ run_id }}")}'
		},
		invoke_job_callable=invoke_job_with_args,
		invoke_job_kwargs={
			'task_group_id': task_group_id,
			'image': SPARKLING_IMAGE,
			'app_jar': SPARKLING_APP_JAR_PATH,
			'app_class': _SPARK_APP_CLASS,
			'app_args_fn': _get_app_args,
			'spark_submit_options': _PROFILE_SETTINGS[PROFILE][SPARK_SUBMIT_OPTIONS],
			'tz': DEFAULT_TZ,
			'alt_datetime': f'{{{{ ti.xcom_pull(key="{XCOM_TARGET_YM}") }}}}',
		},
		spark_app_execution_limit=SPARKLING_APP_HARD_LIMIT,
		wait_showup_failure_cb=partial(_clean_up_to_failure),
		wait_complete_failure_cb=partial(_clean_up_to_failure),
		conclude_app_failure_cb=partial(_clean_up_to_failure)
	)

	# 성공 시 Jobs 다큐먼트 삭제
	clean_up_to_success = PythonOperator(
		task_id='clean_up_to_success',
		python_callable=_clean_up_to_success
	)

	setup >> check_dup_run >> upsert_job >> wait_for_log >> spark_run_adpost_abuse_report >> clean_up_to_success
