"""
[ 개요 ]
    - 브론즈 지연 감시

[ 주기 ]
    - 매 시 45분

[ config ]
    - 없음
"""
import logging
import os

import pendulum
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.email import send_email

from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, \
    BRONZE_CLIENT, BRONZE_SERVER, HADOOP_FILE_BROWSER_VIEW, PROFILE
from core.dao.environment_dao import get_environment_value_by_name

# 기본 정보
_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [MONITOR-BRONZE]'


def _monitor_bronze(**context):
    """
    브론즈 로그 적재에 지연이 있는지 감시
        - 현재 시간이 18시일 때 참을 수 있는 최대 지연 시간이 3시간이라면
        - 15시까지는 지연을 허용하므로 14시 대가 없으면 지연
    Environments 값으로 체크
        - 브론즈의 최근 적재일시: Environments.name='bronze-log-recent-accumulation-ymdh'
        - 참을 수 있는 최대 지연 시간: Environments.name='bronze-log-max-tolerable-delay-hours'
    :return:
    """
    # 참을 수 있는 최대 지연 시간
    max_tolerable_hours = int(get_environment_value_by_name('bronze-log-max-tolerable-delay-hours'))
    expected_bronze_dt = pendulum.now(tz=DEFAULT_TZ).subtract(hours=max_tolerable_hours) \
        .set(minute=0, second=0, microsecond=0)

    # 마지막 적재일시
    last_bronze_ymdh = get_environment_value_by_name('bronze-log-recent-accumulation-ymdh')
    last_bronze_dt = pendulum.from_format(last_bronze_ymdh, 'YYYYMMDDHH', DEFAULT_TZ)

    logging.info(f'{_LOG_PREFIX} max_tolerable_hours = {max_tolerable_hours}')
    logging.info(f'{_LOG_PREFIX}      last_bronze_dt = {last_bronze_dt}')
    logging.info(f'{_LOG_PREFIX}  expected_bronze_dt = {expected_bronze_dt}')

    # 최근 적재일시가 기대되는 적재일시보다 작으면(참을 수 있는 최대 지연 시간을 넘으면)
    if last_bronze_dt < expected_bronze_dt:
        title = f'[{PROFILE} - 브론즈 로그 적재 지연 알림]'

        # 현재 시간으로부터 지연된 시간치
        diff = (pendulum.now(tz=DEFAULT_TZ) - last_bronze_dt).in_hours()
        logging.info(f'{_LOG_PREFIX}                diff = {diff}')

        body = f'브론즈 로그가 지연되고 있습니다.<br/><br/>' \
               f'마지막 적재 일시: {last_bronze_ymdh}<br/>' \
               f'허용 가능한 최대 지연 시간: {max_tolerable_hours}<br/>' \
               f'지연 시간: <span style="color: red; font-weight: bold;">{diff}</span> 시간<br/><br/>'

        body += f'<a href="{HADOOP_FILE_BROWSER_VIEW}={BRONZE_CLIENT}">er-ssp-client</a><br/>'
        body += f'<a href="{HADOOP_FILE_BROWSER_VIEW}={BRONZE_SERVER}">er-ssp-server</a>'

        logging.info(f'{_LOG_PREFIX} title = {title}')
        logging.info(f'{_LOG_PREFIX}  body = {body}')
        send_email(get_environment_value_by_name('bronze-log-delay-alarm-receivers'), title, body)


with DAG(
        _DAG_ID,
        description='브론즈 로그 감시',
        tags=['monitor', 'bronze', 'juyoun.kim'],
        default_args={
            'owner': 'juyoun.kim',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        start_date=pendulum.datetime(2023, 1, 1, tz=DEFAULT_TZ),
        schedule_interval='45 * * * *',
        catchup=False,
) as dag:
    monitor_bronze = PythonOperator(
        task_id='monitor_bronze',
        python_callable=_monitor_bronze,
    )

    monitor_bronze
