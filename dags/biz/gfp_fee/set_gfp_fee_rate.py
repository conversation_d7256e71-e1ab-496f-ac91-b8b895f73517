"""
### GFP 수수료율 설정

#### 위키
- [11. 비지니스 - GFP 수수료율 생성](https://wiki.navercorp.com/pages/viewpage.action?pageId=1447614007)
- [DAG 상세](https://wiki.navercorp.com/pages/viewpage.action?pageId=1402992188)

#### 개요
- 기간에 따른 GFP 수수료율을 설정한다.
- 모든 수수료율 정보를 삭제하고 다시 만듦
- ssp-batch 서버 호출 (/batch/gfpfeerate/init)

#### 주기
- 매일 18시

#### config
- period: 수수료율을 설정할 기간. 오늘을 포함하여 며칠 전 날짜까지 설정할지.
"""

import logging
import os

import pendulum
import requests
import tenacity
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator

from core.base import ALERT_EMAIL_ADDRESSES, DEFAULT_TZ, BATCH_SERVER_ADDRESS

# DAG 기본 정보
DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")
_LOG_PREFIX = '.......... [SET-GFP-FEE-RATE]'

# Batch 호출 주소
_REQ_URL = BATCH_SERVER_ADDRESS + '/batch/gfpfeerate'
_XCOM_PERIOD = 'period'


def _setup(**context):
    """
    사용자로부터 period(갱신범위)를 받았다면 xcom에 push
    :param context: 
    :return: 
    """
    period = context['params'].get('period')
    print(f'${_LOG_PREFIX} period={period}')
    # xcom에 push
    context['ti'].xcom_push(_XCOM_PERIOD, period)


def _request(url: str):
    """
    ssp-batch 서버로 수수료율 갱신 요청
    :param url:
    :return:
    """

    def _call_and_check():
        response = requests.get(url, timeout=60 * 20)
        response.raise_for_status()
        return response

    retry_obj = tenacity.Retrying(
        wait=tenacity.wait.wait_fixed(wait=3),
        stop=tenacity.stop.stop_after_attempt(3),
        retry=tenacity.retry_if_exception_type(requests.exceptions.HTTPError)
    )

    res = retry_obj(_call_and_check)
    logging.info(f'{_LOG_PREFIX} retry stat: attempts={retry_obj.statistics["attempt_number"]}')
    logging.info(f'{_LOG_PREFIX} status code: {res.status_code}')
    logging.info(f'{_LOG_PREFIX} response:\n{res.text}')

    jres = res.json()

    if jres.get('code') != '200':
        return False

    return True


def _init_gfp_fee_rate(**context):
    uri = f'{_REQ_URL}/init'

    # 사용자로부터 받은 기간이 있다면 설정
    period = context['ti'].xcom_pull(key=_XCOM_PERIOD)
    if period:
        uri = f'{uri}?period={period}'

    # ssp-batch 호출
    logging.info(f'{_LOG_PREFIX} url={uri}')
    res = _request(uri)
    return res


with DAG(
        DAG_ID,
        description='GFP 수수료율 설정',
        default_args={
            'owner': 'juyoun.kim',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        start_date=pendulum.datetime(2023, 4, 1, tz=DEFAULT_TZ),
        schedule_interval="1 0 * * *",  # 매일 0시 1분
        tags=['feerate', 'batch', 'juyoun.kim'],
        catchup=False,
        params={
            'USAGE': 'Refer to the DAG code for detailed parameter usage.',
            'period': '',
        },
) as dag:
    dag.doc_md = __doc__

    # 필요한 정보 세팅
    setup = PythonOperator(
        task_id='setup',
        python_callable=_setup,
        doc='config로 받은 기간으로 적용'
    )

    # GFP 수수료율 갱신
    init_gfp_fee_rate = PythonOperator(
        task_id='init',
        python_callable=_init_gfp_fee_rate,
    )

    # 파이프라인
    setup >> init_gfp_fee_rate
