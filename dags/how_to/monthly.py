# Working with TaskFlow
# 	https://airflow.apache.org/docs/apache-airflow/stable/tutorial/taskflow.html
from datetime import datetime
from pprint import pp
from typing import Dict

from airflow.configuration import conf
from airflow.decorators import dag, task
from airflow.models import TaskInstance
from airflow.operators.python import get_current_context
from airflow.utils.trigger_rule import TriggerRule
from sqlalchemy_utils.types.enriched_datetime.pendulum_date import pendulum

DEFAULT_TZ = pendulum.timezone(conf.get('core', 'default_timezone'))
FILE_PATH = "/data/log/gfp/monthly/{ym}_{name}.csv"
HDFS_CONN_ID = "hdfs"
HDFS_QUEUE = "root.users.gfp"


@dag(
    start_date=datetime(2022, 10, 31),
    catchup=False,
    # schedule_interval="0 14 2 * *", # 매 월 2일 14시
    schedule_interval=None,
    render_template_as_native_obj=True,
    tags=['howto', 'monthly', 'adprovider', 'adunit'],
    params={
        'ym': ''
    }
)
def monthly():
    """
    월별 보고서 생성
    설계서: https://wiki.navercorp.com/pages/viewpage.action?pageId=**********
    :return:
    """

    @task(multiple_outputs=True)
    def boot(**context) -> Dict[str, str]:
        """
        대상이 되는 월 설정
        :param context:
        :return:
        """
        print(f'......... boot() ::')
        pp(context)

        params = context['params']['ym']
        if params:  # config가 있는 경우 해당 값으로 설정
            ym = params
        else:
            logical_date: datetime = context['logical_date']
            ym = pendulum.instance(logical_date).in_tz(DEFAULT_TZ).subtract(months=1).format('YYYYMM')  # 전 달

        print(f'boot ym:{ym}')

        return {'ym': ym}

    @task()
    def aggregate_adunit(**context):
        """
        광고유닛 보고서 생성. spark app 실행
        :param context:
        :return:
        """
        ti: TaskInstance = context['ti']

        # target1 = ti.xcom_pull(task_ids="boot", key='target1')
        # print(f'aggregate_adunit. howto target1:{target1}')

        ym = ti.xcom_pull(task_ids="boot", key='ym')  # task_ids, key를 같이 명시해서 가져오는 경우
        print(f'aggregate_adunit. howto ym:{ym}')

    @task()
    def aggregate_adprovider(**context):
        """
        광고공급자 보고서 생성. spark app 실행
        :param context:
        :return:
        """
        ti: TaskInstance = context['ti']
        ym = ti.xcom_pull(key='ym')  # key만 명시해서 가져오는 경우
        print(f'aggregate_adprovider. howto ym:{ym}')

    @task(
        trigger_rule=TriggerRule.ALL_SUCCESS
    )
    def wait_for_aggregation():
        """
        광고유닛 & 광고공급자 보고서 생성되길 기다리기
        :return:
        """
        print(f'wait_for_aggregation')

    """
    하둡 파일 센싱
    """

    # with TaskGroup('sensing_files') as sensing_files:
    # 	hdfs_sensor_for_adunit_aur = HdfsSensor(
    # 		task_id="hdfs_sensor_for_adunit_aur",
    # 		filepath=FILE_PATH.format(ym=f'{{{{ ti.xcom_pull(key="ym") }}}}', name='adunit_aur'),  # "/data/log/gfp/monthly/202209_adunit_aur.csv",
    # 		hdfs_conn_id=HDFS_CONN_ID,  # airflow > Admin > Connections 에 설정한 hdfs connection 명
    # 		queue=HDFS_QUEUE,  # airflow사용 queue,
    # 		poke_interval=3,  # 지정한 interval이 지난후 체크
    # 		timeout=6,  # 지정한 timeout 시간동안 체크를 지속
    # 	)
    #
    # 	hdfs_sensor_for_adunit_dlv = HdfsSensor(
    # 		task_id="hdfs_sensor_for_adunit_dlv",
    # 		filepath=FILE_PATH.format(ym=f'{{{{ ti.xcom_pull(key="ym") }}}}', name='adunit_dlv'),  # "/data/log/gfp/monthly/202209_adunit_dlv.csv",
    # 		hdfs_conn_id=HDFS_CONN_ID,  # airflow > Admin > Connections 에 설정한 hdfs connection 명
    # 		queue=HDFS_QUEUE,  # airflow사용 queue,
    # 		poke_interval=3,  # 지정한 interval이 지난후 체크
    # 		timeout=6,  # 지정한 timeout 시간동안 체크를 지속
    # 	)
    #
    # 	hdfs_sensor_for_adprovider = HdfsSensor(
    # 		task_id="hdfs_sensor_for_adprovider",
    # 		filepath=FILE_PATH.format(ym=f'{{{{ ti.xcom_pull(key="ym") }}}}', name='adprovider'),  # "/data/log/gfp/monthly/202209_adprovider.csv",
    # 		hdfs_conn_id=HDFS_CONN_ID,  # airflow > Admin > Connections 에 설정한 hdfs connection 명
    # 		queue=HDFS_QUEUE,  # airflow사용 queue,
    # 		poke_interval=3,  # 지정한 interval이 지난후 체크
    # 		timeout=6,  # 지정한 timeout 시간동안 체크를 지속
    # 	)

    @task(
        trigger_rule=TriggerRule.ALL_SUCCESS,
        multiple_outputs=True,
    )
    def wait_for_files(**context) -> Dict[str, str]:
        """
        하둡에 파일이 생성되길 기다리기
        :param context:
        :return:
        """
        print(f'.......... context::')
        pp(context)  # dictionary(딕셔너리)를 읽기 쉽게 표현해주는 pprint

        print(f'................ howto context["ti"].xcom_pull(key="ym"):: {context["ti"].xcom_pull(key="ym")}')

        report_path_au_aur = 'aur'
        report_path_au_dlv = 'dlv'
        report_path_adp = 'ap'

        return {
            'aur': report_path_au_aur,
            'dlv': report_path_au_dlv,
            'adprovider': report_path_adp
        }

    @task()
    def send_mail():
        context = get_current_context()
        ti: TaskInstance = context['ti']
        # aur = ti.xcom_pull(task_ids="wait_for_files", key='aur')
        # dlv = ti.xcom_pull(task_ids="wait_for_files", key='dlv')
        aur = ti.xcom_pull(key='aur')
        dlv = ti.xcom_pull(key='dlv')
        print(f'send_mail...... howto aur2:{aur}, dlv2:{dlv}')

    # receivers = ['<EMAIL>']
    # title = '월별 보고서'
    # body = '어쩌구 저쩌구.........'
    # send_email(receivers, title, body)

    # boot() >> [aggregate_adunit(), aggregate_adprovider()] >> wait_for_aggregation() >> \
    # 	sensing_files >> wait_for_files() >> send_mail()

    boot()


dag = monthly()
