from datetime import datetime

from airflow.decorators import dag, task
from airflow.utils.edgemodifier import Label


@dag(
	start_date=datetime(2022, 10, 31),
	catchup=False,
	schedule_interval=None,
	render_template_as_native_obj=True,
	tags=['howto', 'parentdag'],
)
def parentdag():
	@task()
	def task0(**context):
		print(f'parentdag.task0')

	@task()
	def task1(**context):
		print(f'parentdag.task1')

	task0() >> Label('광고유닛 월간 보고서 생성') >> task1()


dag = parentdag()
