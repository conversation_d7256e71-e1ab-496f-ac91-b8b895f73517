from datetime import datetime
from pprint import pp
from typing import Dict

from airflow import DAG
from airflow.decorators import task
from airflow.models import TaskInstance
from airflow.operators.dummy import DummyOperator
from airflow.operators.python import BranchPythonOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from sqlalchemy_utils.types.enriched_datetime.pendulum_date import pendulum

# A DAG represents a workflow, a collection of tasks
with DAG(
        dag_id='gold',
        description='gold.....',
        start_date=datetime(2022, 10, 31),
        catchup=False,
        schedule_interval=None,
        tags=['howto', 'gold'],
        params={
            'ymdh': ''
        }
) as dag:
    @task(multiple_outputs=True)
    def boot(**context) -> Dict[str, str]:
        """
        대상이 되는 일시 설정
        :param context:
        :return:
        """
        pp(context)

        logical_date = context['logical_date']
        ymdh = logical_date.strftime('%Y%m%d%H')
        print(f'boot ymdh:{ymdh}')

        return {'ymdh': ymdh}


    @task(multiple_outputs=True)
    def goldsmith(**context) -> Dict[str, str]:
        pp(context)
        print(f'...... goldsmith')
        return {'goldsmith': 'goldsmith...'}


    @task(multiple_outputs=True)
    def goldcompactor(**context) -> Dict[str, str]:
        pp(context)
        print(f'...... goldcompactor')
        return {'goldcompactor': 'goldcompactor...'}


    # 월별 보고서 생성 DAG 호출
    trg_monthly_report = TriggerDagRunOperator(
        trigger_dag_id='monthly',
        task_id='trg_monthly_reoprt',
        execution_date='{{ logical_date }}',
        wait_for_completion=False,
        poke_interval=3,
        reset_dag_run=True,
        # conf={  # execution_date로 넣어줬으므로 별도로 전달하지 않아도 됨
        # 	'ymdh': '{{ ts_nodash }}'
        # }
    )


    def _do_monthly():
        print(f'........... do_monthly')


    def _skip_monthly():
        print(f'........... skip_monthly')


    do_monthly_report = DummyOperator(task_id='do_monthly_report', on_execute_callback=_do_monthly)
    skip_monthly_report = DummyOperator(task_id='skip_monthly_report', on_execute_callback=_skip_monthly)


    def _monthly_branch(**context):
        # 기본 context
        pp(context)

        # @task의 리턴값을 참조하는 방법
        ti: TaskInstance = context['ti']
        print(f'......... howto goldsmith:{ti.xcom_pull(task_ids="goldsmith", key="goldsmith")}')
        print(f'......... howto goldcompactor:{ti.xcom_pull(task_ids="goldcompactor", key="goldcompactor")}')

        # context 참조하여 logical_date 얻어오기
        logical_date: datetime = context["logical_date"]
        lower_bound = pendulum.datetime(logical_date.year, logical_date.month, 1)
        upper_bound = pendulum.datetime(logical_date.year, logical_date.month, 2)

        # 월별 보고서를 만들어야 하는 날짜인지 확인
        if lower_bound <= logical_date < upper_bound:
            return "do_monthly_report"
        else:
            return "skip_monthly_report"


    # 매 월 2일에만 실행하도록 분기 - BranchPythonOperator
    monthly_branch = BranchPythonOperator(
        task_id='monthly_branch',
        python_callable=_monthly_branch
    )

    boot() >> goldsmith() >> goldcompactor() >> monthly_branch >> [do_monthly_report, skip_monthly_report]
    do_monthly_report >> trg_monthly_report
