import os
import logging
from time import sleep

import pendulum

from airflow.operators.dummy import Dummy<PERSON>perator
from airflow.utils.task_group import TaskGroup

from airflow.models import DAG
from airflow.utils.trigger_rule import TriggerRule
from airflow.exceptions import AirflowFailException, AirflowSkipException
from airflow.operators.python import <PERSON>Operator, BranchPythonOperator

from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, XCOM_TARGET_YMD

# DAG 기본 정보
DAG_ID = _DAG_ID = os.path.basename(__file__).replace('.pyc', '').replace('.py', '')


def _setup(**context):
	"""
	DAG 실행에 필요한 환경 설정 및 확인

	:param context:
	:return:
	"""

	logging.info(f'_setup()')

	context['ti'].xcom_push("spark_run_group1_state", "READY")
	context['ti'].xcom_push("spark_run_group2_state", "READY")


def _branch_spark_run(**context):
	"""
	report_state 각 항목이 READY 또는 FAILURE 인 경우, 스파크 집계 분기 처리
		- 상태 : None, READY, COMPLETE, FAILURE
		- ADPROVIDER : spark_run_performance_adprovider_report
		- ADUNIT : spark_run_performance_adunit_report
	:param context:
	:return:
	"""
	logging.info(f'_branch_spark_run()')

	# return ['spark_run_group1.dummy_1', 'spark_run_group2.dummy_2']
	return ['spark_run_group1.dummy_1']


def _spark_run(name:str, **context):
	"""
	task group 생성을 위한 공통 로직
	:param context:
	:param task_group_id:
	:param report_type:
	:return:
	"""

	logging.info(f'_spark_run() :: {name}')

	if name == "spark_run_1":
		context['ti'].xcom_push("spark_run_group1_state", "FAILURE")
		raise AirflowFailException('spark_run_1 failure')

		# sleep(5)
		# context['ti'].xcom_push("spark_run_group1_state", "COMPLETE")


	if name == "spark_run_2":
		# context['ti'].xcom_push("spark_run_group2_state", "FAILURE")
		# raise AirflowFailException('spark_run_2 failure')

		sleep(1)
		context['ti'].xcom_push("spark_run_group2_state", "COMPLETE")


def _create_task_group(
		*,
		task_group_id: str,
		number: int,
):
	with TaskGroup(
			task_group_id,
			tooltip=task_group_id,
	) as group:
		dummy = DummyOperator(task_id=f'dummy_{number}')

		spark_run = PythonOperator(
			task_id=f'spark_run_{number}',
			python_callable=_spark_run,
			op_args=[f'spark_run_{number}'],
		)

		dummy2 = DummyOperator(task_id=f'dummy2_{number}')

		dummy >> spark_run >> dummy2

	return group


def check_failure(**context):
	spark_run_group1_state = context['ti'].xcom_pull(key="spark_run_group1_state")
	spark_run_group2_state = context['ti'].xcom_pull(key="spark_run_group2_state")

	if "FAILURE" in (spark_run_group1_state, spark_run_group2_state):
		logging.info(f'check_failure() :: failure 처리')
	else:
		raise AirflowSkipException("check_failure() :: failure 없음")


def final_task(**context):
	logging.info(f'final_task()')


with DAG(
		_DAG_ID,
		default_args={
			'owner': 'bitna.cho',
			'email': ALERT_EMAIL_ADDRESSES,
			'email_on_failure': True,
			'retries': 3
		},
		start_date=pendulum.datetime(2024, 8, 1, tz=DEFAULT_TZ),
		schedule_interval=None,
		catchup=False,
) as dag:
	dag.doc_md = __doc__

	setup = PythonOperator(
		task_id='setup',
		python_callable=_setup,
	)

	branch_spark_run = BranchPythonOperator(
		task_id='branch_spark_run',
		python_callable=_branch_spark_run,
	)

	spark_run_group1 = _create_task_group(
		task_group_id='spark_run_group1',
		number=1,
	)

	spark_run_group2 = _create_task_group(
		task_group_id='spark_run_group2',
		number=2,
	)

	success = DummyOperator(
		task_id='success',
		trigger_rule=TriggerRule.NONE_FAILED,
	)

	failed = PythonOperator(
		task_id='failed',
		python_callable=check_failure,
		trigger_rule=TriggerRule.ALL_DONE,
	)

	final_task = PythonOperator(
		task_id='final_task',
		python_callable=final_task,
	)

	setup>> branch_spark_run >> [spark_run_group1, spark_run_group2]
	spark_run_group1 >> [failed, success]
	spark_run_group2 >> [failed, success]
	success >> final_task
