"""
#### 개요
- airflow scheduler container 내의 로그파일을 주기적으로 삭제
- scheduler pod 내 airflow-scheduler 컨테이너에서 kubectl 을 통해 python script 를 실행하는 방식으로 동작

#### 주기
- 매일 13시 50분

#### config
- 없음
"""
import json
import os
from datetime import timed<PERSON>ta

import pendulum
from airflow.exceptions import AirflowException
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator

from core import kubectl
from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, SCHEDULER_LOG_CLEANUP_CONFIG

_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")

_XCOM_POD_NAME = 'pod_name'
_XCOM_CLEANUP_CONF = 'cleanup_conf'


def _setup(**context):
    """
    로그 삭제 명령어를 실행하기 위한 파라미터를 세팅

    :param context:
    :return:
    """
    cleanup_conf = json.loads(SCHEDULER_LOG_CLEANUP_CONFIG)
    # {
    #   "pod_selector": "release=airflow,app=airflow,component=scheduler",
    #   "container": "airflow-scheduler",
    #   "python_path": "/path/to/python",
    #   "script": "/path/to/scripts/log-cleanup.py",
    #   "args": ["arg1"]
    # }
    # stage 인 경우 release 와 script 가 다르다

    pod_names = kubectl.find_pods(cleanup_conf['pod_selector']).split()
    if len(pod_names) != 1:
        raise AirflowException(f'{cleanup_conf["pod_selector"]} 와 일치하는 pods 이 유일하지 않습니다. ({len(pod_names)}개)')

    context['ti'].xcom_push(key=_XCOM_POD_NAME, value=pod_names[0])
    context['ti'].xcom_push(key=_XCOM_CLEANUP_CONF, value=cleanup_conf)


def _run_cleanup_script(**context):
    """
    파라미터로 주어진 python script 를 대상으로 하는 Kubectl 명령어 실행

    :param context:
    :return:
    """
    pod_name = context['ti'].xcom_pull(key=_XCOM_POD_NAME)
    cleanup_conf = context['ti'].xcom_pull(key=_XCOM_CLEANUP_CONF)

    container = cleanup_conf['container']
    python_path = cleanup_conf.get('python_path', 'python')
    script_path = cleanup_conf['script']
    args = cleanup_conf['args']

    kubectl.execute_python_script(
        pod=pod_name,
        container=container,
        python_path=python_path,
        script_path=script_path,
        args=args,  # container 내의 scheduler_log_path (/opt/airflow/logs/scheduler) 를 가르키지만 사용되지 않음
    )


with DAG(
        _DAG_ID,
        description='airflow scheduler 로그를 주기적으로 삭제하는 dag',
        default_args={
            'owner': 'ins.cho',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
        },
        schedule_interval='50 13 * * *',
        start_date=pendulum.yesterday(tz=DEFAULT_TZ),
        tags=['airflow', 'maintenance', 'cleanup', 'scheduler-log', 'daily'],
        catchup=False,
) as dag:
    dag.doc_md = __doc__

    setup = PythonOperator(
        task_id='setup',
        python_callable=_setup,
        execution_timeout=timedelta(minutes=30),
        dag=dag,
    )

    run_cleanup_script = PythonOperator(
        task_id='run_cleanup_script',
        python_callable=_run_cleanup_script,
        execution_timeout=timedelta(minutes=30),
        dag=dag,
    )

    setup >> run_cleanup_script
