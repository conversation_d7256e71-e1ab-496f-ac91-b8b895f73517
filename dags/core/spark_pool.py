from math import ceil

PRI_LOW = 1
PRI_NORMAL = 5
PRI_HIGH = 10
PRI_HIGHER = 20
PRI_HIGHEST = 30
PRI_LIMIT = 100

# 
POOL_CAPACITY = 100

POOL_SLOT_TRIVIAL_2 = 2
POOL_SLOT_TRIVIAL_3 = 3
POOL_SLOT_TRIVIAL_5 = 5
POOL_SLOT_LIGHT_10 = 10
POOL_SLOT_NORMAL_20 = 20
POOL_SLOT_NORMAL_25 = 25
POOL_SLOT_NORMAL_33 = 33
POOL_SLOT_HEAVY_50 = 50
POOL_SLOT_HEAVY_66 = 66
POOL_SLOT_HEAVY_75 = 75
POOL_SLOT_HEAVY_80 = 80

def POOL_SLOT_ASSIGN(*, percent: float):
    return ceil(POOL_CAPACITY * percent/100)

POOL_AP_CAPACITY = 100

POOL_AP_SLOT_TRIVIAL_2 = 2
POOL_AP_SLOT_TRIVIAL_3 = 3
POOL_AP_SLOT_TRIVIAL_5 = 5
POOL_AP_SLOT_LIGHT_10 = 10
POOL_AP_SLOT_NORMAL_20 = 20
POOL_AP_SLOT_NORMAL_25 = 25
POOL_AP_SLOT_NORMAL_33 = 33
POOL_AP_SLOT_HEAVY_50 = 50
POOL_AP_SLOT_HEAVY_66 = 66
POOL_AP_SLOT_HEAVY_75 = 75
POOL_AP_SLOT_HEAVY_80 = 80

def POOL_AP_SLOT_ASSIGN(*, percent: float):
    return ceil(POOL_AP_CAPACITY * percent/100)