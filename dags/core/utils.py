import json
import logging
import os
import stat
from os import stat_result
from pathlib import Path
from typing import List, Optional

import nanoid
import pendulum
import requests
import tenacity
from airflow import AirflowException
from airflow.configuration import conf
from airflow.providers.mongo.hooks.mongo import MongoHook
from bson import json_util
from bson.objectid import ObjectId
from pendulum.datetime import DateTime
from datetime import datetime

_nanoid_alphabet = '-0123456789abcdefghijklmnopqrstuvwxyz'


def get_nanoid(sz: int = 8) -> str:
	new_id = nanoid.generate(_nanoid_alphabet, size=sz)
	while new_id.endswith('-'):
		new_id = nanoid.generate(_nanoid_alphabet, size=sz)
	return new_id


def ts2dt_ms(ts: float, tz='UTC'):
	dt = pendulum.from_timestamp(ts / 1000, tz=tz)
	return dt


def id_arr_to_csv(arr: Optional[List[ObjectId]]):
	if not arr:  # None or []
		return ""
	return ",".join([str(oid) for oid in arr])


def csv_to_id_arr(csv: str) -> List[ObjectId]:
	if csv:
		return [ObjectId(sid) for sid in csv.split(',')]
	else:
		return []


def csv_to_str_arr(csv: str) -> List[str]:
	if csv:
		return csv.split(',')
	else:
		return []


def format_spark_options(options: List[str]):
	res = ''
	for i, w in enumerate(['""' if o == '' else o for o in options]):
		if i > 0 and i % 2 == 0:
			res += '\n'
		elif i > 0:
			res += ' '
		res += repr(w)
	return res


def get_dag_run_url(dag_id: str, dag_run_id: str):
	base_url = conf.get('webserver', 'base_url')
	return f'{base_url}/graph?dag_id={dag_id}&run_id={dag_run_id}'


def _clean_old_logs(
		dir: Path,
		pivot_dt: DateTime,
		dry_run: bool,
):
	def try_delete(entry: Path, st: stat_result):
		mtime = pendulum.from_timestamp(st.st_mtime)
		if stat.S_ISREG(st.st_mode) or stat.S_ISLNK(st.st_mode):
			if pivot_dt > mtime:
				if not dry_run:
					logging.info(f'{entry} will be deleted: {mtime}')
					entry.unlink()
				else:
					logging.info(f'{entry} would be deleted: {mtime}')
				return True
		return False

	def try_rmdir(entry: Path):
		if not dry_run:
			logging.info(f'{entry} will be deleted')
			entry.rmdir()
		else:
			logging.info(f'{entry} would be deleted')

	survivor_cnt = 0
	for e in dir.iterdir():
		st = e.lstat()
		if stat.S_ISDIR(st.st_mode):
			if _clean_old_logs(e, pivot_dt, dry_run) > 0:
				survivor_cnt += 1
			else:
				try_rmdir(e)
		elif not try_delete(e, st):
			survivor_cnt += 1

	return survivor_cnt


def clean_dag_logs(dag_id: str, *, max_age_hours: int, dry_run=True):
	base_log_folder = conf.get('logging', 'base_log_folder')
	dag_log_path = Path(base_log_folder) / dag_id
	pivot_dt = pendulum.now().subtract(hours=max_age_hours)

	logging.info(f'HOSTNAME: {os.environ.get("HOSTNAME")}')
	logging.info(f'log dir:  {dag_log_path}')
	logging.info(f'max age:  {max_age_hours} hours')
	logging.info(f'pivot dt: {pivot_dt}')

	if not dag_log_path.is_dir():
		logging.warning(f'{dag_log_path} is not a directory')
		return

	_clean_old_logs(dag_log_path, pivot_dt, dry_run)


def get_between_ymdh_list(from_ymdh: str, to_ymdh: str):
	"""
	from, to 사이의 1시간 단위 대상 일시 구성

	:param from_ymdh: 'YYYYMMDDHH' (inclusive)
	:param to_ymdh: 'YYYYMMDDHH' (inclusive)
	:return:
	"""

	if from_ymdh > to_ymdh:
		raise AirflowException(f'from_target_ymdh must be less than or equal to to_target_ymdh.'
							   f' from_ymdh={from_ymdh} to_ymdh={to_ymdh}')

	begin = pendulum.from_format(from_ymdh, 'YYYYMMDDHH')
	end = pendulum.from_format(to_ymdh, 'YYYYMMDDHH')

	ymdh_list = []
	while begin <= end:
		ymdh_list.append(begin.format('YYYYMMDDHH'))
		begin = begin.add(hours=1)

	return ymdh_list


def get_between_ymd_list(from_ymd: str, to_ymd: str):
	"""
	from, to 사이의 1일 단위 대상 일시 구성

	:param from_ymd: 'YYYYMMDD' (inclusive)
	:param to_ymd: 'YYYYMMDD' (inclusive)
	:return:
	"""

	if from_ymd > to_ymd:
		raise AirflowException(f'from_target_ymd must be less than or equal to to_target_ymd.'
							   f' from_ymd={from_ymd} to_ymd={to_ymd}')

	begin = pendulum.from_format(from_ymd, 'YYYYMMDD')
	end = pendulum.from_format(to_ymd, 'YYYYMMDD')

	ymdh_list = []
	while begin <= end:
		ymdh_list.append(begin.format('YYYYMMDD'))
		begin = begin.add(days=1)

	return ymdh_list


def get_between_ym_list(from_ym: str, to_ym: str):
	"""
	from, to 사이의 1달 단위 대상 월 구성

	:param from_ym: 'YYYYMM' (inclusive)
	:param to_ym: 'YYYYMM' (inclusive)
	:return:
	"""

	if from_ym > to_ym:
		raise AirflowException(f'from_target_ym must be less than or equal to to_target_ym.'
							   f' from_ym={from_ym} to_ym={to_ym}')

	begin = pendulum.from_format(from_ym, 'YYYYMM')
	end = pendulum.from_format(to_ym, 'YYYYMM')

	ym_list = []
	while begin <= end:
		ym_list.append(begin.format('YYYYMM'))
		begin = begin.add(months=1)

	return ym_list


def get_between_dt_list(from_dt: str, to_dt: str, unit=None, unit_size=1):
	"""
	from, to 사이의 (unit_size)(unit) 단위 대상 월 구성
	ex) (unit_size=1)(unit='days') -> 1달
	from_dt > to_dt 인 경우 시간 역순으로 반환

	:param from_dt: 'YYYYMMDDHH' or 'YYYYMMDD' or 'YYYYMM' (inclusive)
	:param to_dt: 'YYYYMMDDHH' or 'YYYYMMDD' or 'YYYYMM' (inclusive)
	:param unit: from_dt ~ to_dt 까지의 시간 간격 유닛 정보. default: from_dt && to_dt 의 최소 단위
	:param unit_size: 시간 간격 유닛에 대한 크기 default: 1
	:return:
	"""
	dt_format_info = {
		10: ['YYYYMMDDHH', 'hours'],
		8: ['YYYYMMDD', 'days'],
		6: ['YYYYMM', 'months']
	}
	dt_key = len(from_dt)
	dt_format = dt_format_info[dt_key][0]

	try:
		pendulum.from_format(from_dt, dt_format)
		pendulum.from_format(to_dt, dt_format)
	except Exception as e:
		raise AirflowException(
			f'from_dt({from_dt}) 와 to_dt({to_dt}) 의 포멧은 ["YYYYMMDDHH","YYYYMMDD","YYYYMM"] 중 하나로 일치해야 합니다.')

	if unit is not None and unit not in ['months', 'days', 'hours']:
		raise AirflowException(f'시간 간격의 단위({unit})는 ["months","days","hours"] 중 하나로 입력해야 합니다.')
	elif unit_size != int(unit_size) or int(unit_size) < 0:
		raise AirflowException(f'시간 간격의 크기({unit_size})는 양의 정수로 입력해야 합니다.')

	unit_params = dt_format_info[dt_key][1] if unit is None else unit

	begin = pendulum.from_format(from_dt, dt_format)
	end = pendulum.from_format(to_dt, dt_format)

	reverse = False
	if begin >= end:
		reverse = True

	dt_list = []
	while begin <= end:
		if not reverse:
			dt_list.append(begin.format(dt_format))
			begin = begin.add(**{unit_params: unit_size})
		else:
			dt_list.append(end.format(dt_format))
			end = end.minus(**{unit_params: unit_size})

	return dt_list


def request(url: str, timeout: int = 60 * 5, method: str = 'GET', rtn_raw_res: bool = False, **kwargs):
	def _call_and_check():
		response = requests.request(method, url, timeout=timeout, **kwargs)
		response.raise_for_status()  # 응답코드가 4XX, 5XX 인 경우 에러 발생
		return response

	retry_obj = tenacity.Retrying(
		wait=tenacity.wait.wait_fixed(wait=3),
		stop=tenacity.stop.stop_after_attempt(3),
		retry=tenacity.retry_if_exception_type((requests.exceptions.HTTPError, requests.exceptions.Timeout)),
		reraise=True
	)

	res = retry_obj(_call_and_check)
	logging.info(f'retry stat: attempts={retry_obj.statistics["attempt_number"]}')
	logging.info(f'status code: {res.status_code}')
	logging.info(f'response header: {res.headers}')
	logging.info(f'response: \n{res.text}')

	if rtn_raw_res:
		return res
	else:
		if 'application/json' in res.headers['Content-Type']:
			try:
				jres = res.json()
			except json.JSONDecodeError as e:
				logging.info(f'응답 값이 JSON 형식이 아닙니다. response.content: {res.content}')
				logging.error(e)
				return res.text
			else:
				return jres
		else:
			return res.text


def prettyStr(doc: dict):
	# ensure_ascii=False를 사용하여 한글이 Unicode로 출력되지 않도록 설정
	return json.dumps(doc, ensure_ascii=False, indent=4)


def prettyStrOfMongoDoc(doc, indent=4):
	# ensure_ascii=False를 사용하여 한글이 Unicode로 출력되지 않도록 설정
	return json.dumps(json.loads(json_util.dumps(doc)), ensure_ascii=False, indent=indent)


def get_db_uri(conn_id):
	with MongoHook(conn_id) as hook:
		print(f'....................... utils.get_db_uri() uri={hook.uri}')
		return hook.uri


def transform_mongo_data(data):
	"""
	mongodb doc 을 Python 객체로 일괄 변환
	:param data:
	:return:
	"""
	if isinstance(data, dict):
		return {k: transform_mongo_data(v) for k, v in data.items()}
	elif isinstance(data, list):
		return [transform_mongo_data(item) for item in data]
	elif isinstance(data, ObjectId):
		return str(data)
	elif isinstance(data, datetime):
		return data.isoformat()  # ISO 8601 형식으로 변환
	else:
		return data
