import logging
from textwrap import indent
from typing import List

import requests
from requests_gssapi import HTTPSP<PERSON><PERSON>OA<PERSON>

from core import c3, utils
from core.base import C3_USER, C3_NAMESPACE, SPARKLING_YARN_ARCHIVE, SPARK_HISTORY_SERVER, PRO<PERSON>LE, PRO<PERSON>LE_LOCAL, \
	PROFILE_DEV, PROFILE_TEST, PROFILE_STAGE, PROFILE_REAL, C3_REALM
from core.utils import get_nanoid

_JOB_SPEC = '''\
apiVersion: batch/v1
kind: Job
metadata:
    name: {jobName}
spec:
    ttlSecondsAfterFinished: {ttl}
    template:
        spec:
            containers:
            -   name: spark-submit
                image: {image}
                command:
                -   spark-submit
                args:
{args}
                volumeMounts:
                -   name: c3s-keytab # 이게 가리키는 것은 spec.template.spec.volumes.name = 'c3s-keytab'을 의미함
                    mountPath: /home1/irteam/apps/c3/gfp-data.keytab
                    subPath: gfp-data.keytab # /home1/irteam/apps/c3 경로 아래의 다른 파일을 유지하고 gfp-data.keytab만 마운트
            volumes:
            -   name: c3s-keytab
                secret:
                    secretName: c3s-gfp-data-keytab
    
    
            restartPolicy: Never
    backoffLimit: 0
'''

_DEFAULT_SPARK_OPTIONS = {
	PROFILE_TEST: [
		'--deploy-mode', 'cluster'
		, '--master', 'yarn'
		, '--queue', 'biz_gep'
		, '--conf', f'spark.eventLog.dir=hdfs://{C3_NAMESPACE}/user/{C3_USER}/spark-history/'
		, '--conf', 'spark.eventLog.enabled=true'
		, '--conf', 'spark.executor.extraJavaOptions=-XX:+UseG1GC'
		, '--conf', 'spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922'
		, '--conf', 'spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro'
		, '--conf', 'spark.driver.extraJavaOptions=-Dhdp.version=*******-78 -XX:+UseG1GC -Dfile.encoding=utf-8'
		, '--conf', f'spark.hadoop.dfs.nameservices={C3_NAMESPACE}'
		, '--conf', f'spark.kerberos.access.hadoopFileSystems=hdfs://{C3_NAMESPACE}'
		, '--conf', f'spark.kerberos.keytab=/home1/irteam/apps/c3/{C3_USER}.keytab'
		, '--conf', f'spark.kerberos.principal={C3_USER}@{C3_REALM}'
		, '--conf', 'spark.serializer=org.apache.spark.serializer.KryoSerializer'
		, '--conf', 'spark.sql.caseSensitive=true'
		, '--conf', 'spark.sql.parquet.mergeSchema=true'
		, '--conf', f'spark.sql.warehouse.dir=hdfs://{C3_NAMESPACE}/user/gfp-data/apps/spark/warehouse'
		, '--conf', 'spark.yarn.am.extraJavaOptions=-Dhdp.version=*******-78'
		, '--conf', 'spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro'
		, '--conf', 'spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922'
		, '--conf', f'spark.yarn.archive={SPARKLING_YARN_ARCHIVE}'
		, '--conf', 'spark.yarn.submit.waitAppCompletion=false'
		, '--conf', f'spark.yarn.historyServer.address={SPARK_HISTORY_SERVER}'
	],
	PROFILE_REAL: [
		'--deploy-mode', 'cluster'
		, '--master', 'yarn'
		, '--queue', 'biz_gep'
		, '--conf', f'spark.eventLog.dir=hdfs://pgcm/user/{C3_USER}/spark-history/'
		, '--conf', 'spark.eventLog.enabled=true'
		, '--conf', 'spark.executor.extraJavaOptions=-XX:+UseG1GC'
		, '--conf', 'spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922'
		, '--conf', 'spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro'
		, '--conf', 'spark.driver.extraJavaOptions=-Dhdp.version=*******-78 -XX:+UseG1GC -Dfile.encoding=utf-8'
		, '--conf', f'spark.hadoop.dfs.nameservices=pgcm,pg01,{C3_NAMESPACE}'
		, '--conf', f'spark.kerberos.access.hadoopFileSystems=hdfs://pgcm,hdfs://pg01,hdfs://{C3_NAMESPACE}'
		, '--conf', f'spark.kerberos.keytab=/home1/irteam/apps/c3/{C3_USER}.keytab'
		, '--conf', f'spark.kerberos.principal={C3_USER}@{C3_REALM}'
		, '--conf', 'spark.serializer=org.apache.spark.serializer.KryoSerializer'
		, '--conf', 'spark.sql.caseSensitive=true'
		, '--conf', 'spark.sql.parquet.mergeSchema=true'
		, '--conf', f'spark.sql.warehouse.dir=hdfs://{C3_NAMESPACE}/user/gfp-data/apps/spark/warehouse'
		, '--conf', 'spark.yarn.am.extraJavaOptions=-Dhdp.version=*******-78'
		, '--conf', 'spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro'
		, '--conf', 'spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922'
		, '--conf', f'spark.yarn.archive={SPARKLING_YARN_ARCHIVE}'
		, '--conf', 'spark.yarn.submit.waitAppCompletion=false'
		, '--conf', f'spark.yarn.historyServer.address={SPARK_HISTORY_SERVER}'
	],
}
_DEFAULT_SPARK_OPTIONS[PROFILE_LOCAL] = _DEFAULT_SPARK_OPTIONS[PROFILE_TEST]
_DEFAULT_SPARK_OPTIONS[PROFILE_DEV] = _DEFAULT_SPARK_OPTIONS[PROFILE_TEST]
_DEFAULT_SPARK_OPTIONS[PROFILE_STAGE] = _DEFAULT_SPARK_OPTIONS[PROFILE_REAL]

# _DEFAULT_SPARK_OPTIONS = [
#     '--deploy-mode', 'cluster'
#     , '--master', 'yarn'
#     , '--queue', 'biz_gep'
#     , '--conf', f'spark.eventLog.dir=hdfs://pgcm/user/{C3_USER}/spark-history/'
#     , '--conf', 'spark.eventLog.enabled=true'
#     , '--conf', 'spark.executor.extraJavaOptions=-XX:+UseG1GC'
#     , '--conf', 'spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922'
#     , '--conf', 'spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro'
#     , '--conf', 'spark.driver.extraJavaOptions=-Dhdp.version=*******-78 -XX:+UseG1GC -Dfile.encoding=utf-8'
#     , '--conf', f'spark.hadoop.dfs.nameservices=pgcm,pg01,{C3_NAMESPACE}'
#     , '--conf', f'spark.kerberos.access.hadoopFileSystems=hdfs://pgcm,hdfs://pg01,hdfs://{C3_NAMESPACE}'
#     , '--conf', f'spark.kerberos.keytab=/home1/irteam/apps/c3/{C3_USER}.keytab'
#     , '--conf', f'spark.kerberos.principal={C3_USER}@{C3_REALM}'
#     , '--conf', 'spark.serializer=org.apache.spark.serializer.KryoSerializer'
#     , '--conf', 'spark.sql.caseSensitive=true'
#     , '--conf', 'spark.sql.parquet.mergeSchema=true'
#     , '--conf', f'spark.sql.warehouse.dir=hdfs://{C3_NAMESPACE}/user/gfp-data/apps/spark/warehouse'
#     , '--conf', 'spark.yarn.am.extraJavaOptions=-Dhdp.version=*******-78'
#     , '--conf', 'spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro'
#     , '--conf', 'spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922'
#     , '--conf', f'spark.yarn.archive={SPARKLING_YARN_ARCHIVE}'
#     , '--conf', 'spark.yarn.submit.waitAppCompletion=false'
#     , '--conf', f'spark.yarn.historyServer.address={SPARK_HISTORY_SERVER}'
# ]

_JOB_SPEC_ARGS_INDENT = 16
_JOB_DEFAULT_TTL_SECONDS = 86400  # 24시간


def create_spark_submit_job_spec(
		*,
		job_name: str,
		image: str,
		spark_submit_args: List[str],
		app_jar: str,
		app_args: List[str],
		ttl_seconds: int = _JOB_DEFAULT_TTL_SECONDS
) -> str:
	"""
	kubel job spec 생성

	:param job_name:
	:param image:
	:param spark_submit_args:
	:param app_jar:
	:param app_args:
	:param ttl_seconds:
	:return:
	"""

	def quote_str(s: str):
		if s.startswith('"'):
			return "'" + s + "'"
		return '"' + s + '"'

	spark_submit_options = _get_spark_submit_full_args(spark_submit_args, app_jar, app_args)

	job_args = ['-   {arg}'.format(arg=quote_str(arg)) for arg in spark_submit_options]
	job_args = '\n'.join(job_args)
	job_args = indent(job_args, ' ' * _JOB_SPEC_ARGS_INDENT)

	return _JOB_SPEC.format(jobName=job_name, image=image, args=job_args, ttl=ttl_seconds)


def _get_spark_submit_full_args(spark_submit_args, app_jar, app_args):
	"""
	spark-submit의 파라미터 생성
	사용자 설정 파라미터 + 디폴트 파라미터 + app_jar + app_args
	:param spark_submit_args:
	:return:
	"""

	spark_submit_args.extend(_DEFAULT_SPARK_OPTIONS[PROFILE])  # 사용자 설정 파라미터에 디폴트 파라미터 추가

	spark_submit_args.append(app_jar)
	spark_submit_args.extend(app_args)

	return spark_submit_args


def generate_app_name(app_class: str, alt_datetime: str = '') -> str:
	"""
	스파크 앱 이름 동적 생성

	:param app_class:
	:param alt_datetime:
	:return:
	"""
	paths = app_class.split('.')
	app_name = paths[-2] + "." + paths[-1] if len(paths) >= 2 else paths[-1]
	if PROFILE == PROFILE_STAGE:
		app_name = 'stg-' + app_name
	elif PROFILE == PROFILE_DEV:
		app_name = 'dev-' + app_name

	return '{}-{}-{}'.format(
		app_name,
		alt_datetime,
		get_nanoid(6),
	)


def get_history_url(history_server_addr: str, app_id: str, attempt_id='1'):
	"""
	app_id에 해댕하는 Spark Application History Server 주소 얻기

	:param history_server_addr:
	:param app_id:
	:param attempt_id:
	:return:
	"""
	return f'{history_server_addr.rstrip("/")}/history/{app_id}/{attempt_id}/jobs/'


PROBE_STATUS_ON_TRACKING = 'OnTracking'
PROBE_STATUS_NOT_FOUND = 'NotFound'
PROBE_STATUS_COMPLETED = 'Completed'
PROBE_STATUS_FAILED = 'Failed'
PROBE_STATUS_KILLED = 'Killed'
PROBE_STATUS_SUCCEEDED = 'Succeeded'
PROBE_STATUS_EXCEPTION = 'UnexpectedException'


def get_yarn_tracking_url(yarn_rsc_mgr_addr: str, app_id: str):
	"""
	Yarn Cluster Application API 호출 주소 생성
	https://hadoop.apache.org/docs/r2.7.3/hadoop-yarn/hadoop-yarn-site/ResourceManagerRest.html#Cluster_Application_API
	:param yarn_rsc_mgr_addr:
	:param app_id:
	:return:
	"""
	return f'{yarn_rsc_mgr_addr.rstrip("/")}/ws/v1/cluster/apps/{app_id}'


def get_yarn_app_tracking_status(tracking_url: str):
	"""
	Yarn Resource Manager로부터 Spark App의 상태 조회
	:param tracking_url:
	:return:
	"""
	try:
		# 커버로스 인증 정보 실어서 요청
		# c3.kinit_using_krbticket()
		c3.kinit_using_subprocess()
		auth = HTTPSPNEGOAuth()
		res = requests.get(tracking_url, timeout=10, auth=auth)
		if res.ok:
			return PROBE_STATUS_ON_TRACKING
		else:
			logging.info(f'get_yarn_app_tracking_status(): res.ok가 아님. {res.text}')
			return PROBE_STATUS_EXCEPTION
	except requests.ConnectionError:
		logging.error(f'{tracking_url}: connection exception')
		return PROBE_STATUS_EXCEPTION


def get_yarn_app_info(tracking_url: str):
	"""
	Yarn 앱 정보 조회
		- yarn resource manager API Spec
			https://hadoop.apache.org/docs/r2.7.3/hadoop-yarn/hadoop-yarn-site/ResourceManagerRest.html#Cluster_Application_API

		- Req
			http://atcdh002-sa.nfra.io:8088/ws/v1/cluster/apps/application_1669881451082_8705

		- Res
			<app>
				<id>application_1669881451082_8705</id>
				<user>irteam</user>
				<name>performance.AdUnitPerformanceDailyAggregator-20221207-ncstql</name>
				<queue>root.users.gfp</queue>
				<state>FINISHED</state> # NEW, NEW_SAVING, SUBMITTED, ACCEPTED, RUNNING, FINISHED, FAILED, KILLED
				<finalStatus>SUCCEEDED</finalStatus> # UNDEFINED, SUCCEEDED, FAILED, KILLED
				<progress>100.0</progress>
				<trackingUI>History</trackingUI>
				<trackingUrl>http://atcdh002-sa.nfra.io:8088/proxy/application_1669881451082_8705/</trackingUrl>
				<diagnostics/>
				<clusterId>1669881451082</clusterId>
				<applicationType>SPARK</applicationType>
				<applicationTags/>
				<startedTime>1670490206596</startedTime>
				<finishedTime>1670490251300</finishedTime>
				<elapsedTime>44704</elapsedTime>
				<amContainerLogs>http://atcdh004-sa.nfra.io:8042/node/containerlogs/container_e43_1669881451082_8705_01_000001/irteam</amContainerLogs>
				<amHostHttpAddress>atcdh004-sa.nfra.io:8042</amHostHttpAddress>
				<allocatedMB>-1</allocatedMB>
				<allocatedVCores>-1</allocatedVCores>
				<reservedMB>-1</reservedMB>
				<reservedVCores>-1</reservedVCores>
				<runningContainers>-1</runningContainers>
				<memorySeconds>1769055</memorySeconds>
				<vcoreSeconds>775</vcoreSeconds>
				<preemptedResourceMB>0</preemptedResourceMB>
				<preemptedResourceVCores>0</preemptedResourceVCores>
				<numNonAMContainerPreempted>0</numNonAMContainerPreempted>
				<numAMContainerPreempted>0</numAMContainerPreempted>
				<logAggregationStatus>DISABLED</logAggregationStatus>
			</app>
	:param tracking_url:
	:return:
	"""
	try:
		# 커버로스 인증 정보 실어서 요청
		c3.kinit_using_subprocess()
		auth = HTTPSPNEGOAuth()
		res = requests.get(tracking_url, timeout=15, auth=auth)
		if not res.ok:
			logging.error(f'{tracking_url}: {res.status_code}: {res.text}')
			return {'status': PROBE_STATUS_EXCEPTION}
	except requests.ConnectionError:
		logging.error(f'{tracking_url}: connection exception')
		return {'status': PROBE_STATUS_EXCEPTION}

	try:
		body = res.json()
	except Exception:
		logging.warning('json parse error')
		return {'status': PROBE_STATUS_EXCEPTION}

	# 실패했거나 KILL당했거나 진행중일 경우 중간에 return
	print(f'.........body.get("app")={utils.prettyStr(body.get("app"))}')
	app_info = {}
	if body.get('app'):
		if body['app'].get('state') == 'FAILED':
			app_info['status'] = PROBE_STATUS_FAILED
		elif body['app'].get('state') == 'KILLED':
			app_info['status'] = PROBE_STATUS_KILLED
		elif body['app'].get('state') != 'FINISHED':
			app_info['status'] = PROBE_STATUS_ON_TRACKING
			return app_info
	else:
		app_info['status'] = PROBE_STATUS_ON_TRACKING
		return app_info

	# 끝났을 경우 시작/종료시간, 마지막 상태 설정
	app_info['startTimeEpoch'] = body['app']['startedTime']
	app_info['endTimeEpoch'] = body['app']['finishedTime']
	app_info['finalStatus'] = body['app']['finalStatus']

	# yarn app 정보에 state, finalStatus가 있는데, 다 끝났을 경우엔 finalStatus로 status로 설정한다.
	if app_info['finalStatus'] == 'SUCCEEDED':
		app_info['status'] = PROBE_STATUS_SUCCEEDED
	elif app_info['finalStatus'] == 'KILLED':
		app_info['status'] = PROBE_STATUS_KILLED
	else:
		app_info['status'] = PROBE_STATUS_FAILED

	return app_info


def is_app_showup_in_shs(app_id: str) -> bool:
	"""
	- Spark History Server를 통해 Spark App이 떴는지 확인
	- https://oss.navercorp.com/da-ssp/bts/issues/2445
		- [DATA] airflow spark dag에서 상태 확인을 위해 yarn api 결과가 200 ok가 아닌 경우 shs를 통해 한 번 더 확인하도록 개선
	:param app_id:
	:return:
	"""
	try:
		# 커버로스 인증 정보 실어서 요청
		c3.kinit_using_subprocess()
		auth = HTTPSPNEGOAuth()

		url = f'{SPARK_HISTORY_SERVER.rstrip("/")}/api/v1/applications/{app_id}'
		logging.info(f'is_app_showup_in_shs() url={url}')
		res = requests.get(url, timeout=10, auth=auth)
		if res.status_code == 200:
			body = res.json()
			logging.info(f'body={utils.prettyStr(body)}')
			if body['id'] and body['id'] == app_id:
				logging.info(f'is_app_showup_in_shs() SHS에 {app_id} 존재')
				return True
			else:
				return False
		else:
			return False
	except requests.ConnectionError as ex:
		logging.error(f'{url}: connection exception. ex={ex}')
		raise ex


def is_app_complete_in_shs(app_id: str) -> bool:
	"""
	- Spark History Server를 통해 Spark App이 끝났는지 확인
	- https://oss.navercorp.com/da-ssp/bts/issues/2445
		- [DATA] airflow spark dag에서 상태 확인을 위해 yarn api 결과가 200 ok가 아닌 경우 shs를 통해 한 번 더 확인하도록 개선
	:param app_id:
	:return:
	"""
	try:
		# 커버로스 인증 정보 실어서 요청
		c3.kinit_using_subprocess()
		auth = HTTPSPNEGOAuth()

		url = f'{SPARK_HISTORY_SERVER.rstrip("/")}/api/v1/applications/{app_id}'
		logging.info(f'is_app_complete_in_shs() url={url}')
		res = requests.get(url, timeout=10, auth=auth)
		if res.status_code == 200:
			body = res.json()
			logging.info(f'body={utils.prettyStr(body)}')

			# 시도 중 completed가 false인게 하나라도 있다면 진행중
			attempts = body['attempts']
			is_not_complete = any(not attempt["completed"] for attempt in attempts)
			if is_not_complete:
				logging.info(f'is_app_complete_in_shs() {app_id} 아직 안 끝났음.')
				return False

			logging.info(f'is_app_complete_in_shs() {app_id} 종료.')
			return True
		else:
			return False
	except requests.ConnectionError as ex:
		logging.error(f'{url}: connection exception. ex={ex}')
		raise ex


def get_shs_app_info(app_id: str) -> object:
	"""
	- Spark History Server를 통해 Spark App이 성공/실패인지 확인
	- https://oss.navercorp.com/da-ssp/bts/issues/2445
		- [DATA] airflow spark dag에서 상태 확인을 위해 yarn api 결과가 200 ok가 아닌 경우 shs를 통해 한 번 더 확인하도록 개선

	- https://.../api/v1/applications/application_1711009858797_1831206
		{
			"id": "application_1711009858797_1831206",
			"name": "b.ZirconBGfpAggregator-20240501-pub-*-xfxdbi",
			"attempts": [
				{
					"attemptId": "4",
					"startTime": "2024-05-03T20:07:37.526GMT",
					"endTime": "2024-05-03T20:21:08.580GMT",
					"lastUpdated": "2024-05-03T20:21:08.618GMT",
					"duration": 811054,
					"sparkUser": "gfp-data",
					"completed": true,
					"appSparkVersion": "3.2.1",
					"startTimeEpoch": 1714766857526,
					"endTimeEpoch": 1714767668580,
					"lastUpdatedEpoch": 1714767668618
				},
				...
				{
					"attemptId": "1",
					"startTime": "2024-05-03T19:25:48.920GMT",
					"endTime": "2024-05-03T19:43:07.601GMT",
					"lastUpdated": "2024-05-03T19:43:07.634GMT",
					"duration": 1038681,
					"sparkUser": "gfp-data",
					"completed": true,
					"appSparkVersion": "3.2.1",
					"startTimeEpoch": 1714764348920,
					"endTimeEpoch": 1714765387601,
					"lastUpdatedEpoch": 1714765387634
				}
			]
		}

	- https://.../api/v1/applications/application_1711009858797_1831206/4/jobs?status=failed
		[
			{
				"jobId": 62,
				"name": "parquet at ZirconBGfpAggregator.scala:917",
				"submissionTime": "2024-05-03T20:12:03.235GMT",
				"completionTime": "2024-05-03T20:17:17.918GMT",
				"stageIds": [
					86
				],
				"status": "FAILED",
				"numTasks": 558,
				"numActiveTasks": 0,
				"numCompletedTasks": 293,
				"numSkippedTasks": 0,
				"numFailedTasks": 4,
				"numKilledTasks": 20,
				"numCompletedIndices": 293,
				"numActiveStages": 0,
				"numCompletedStages": 0,
				"numSkippedStages": 0,
				"numFailedStages": 1,
				"killedTasksSummary": {
					"Stage cancelled": 20
				}
			},
			...
			{
				"jobId": 51,
				"name": "parquet at ZirconBGfpAggregator.scala:917",
				"submissionTime": "2024-05-03T20:11:22.714GMT",
				"completionTime": "2024-05-03T20:17:33.155GMT",
				"stageIds": [
					72
				],
				"status": "FAILED",
				"numTasks": 681,
				"numActiveTasks": 0,
				"numCompletedTasks": 585,
				"numSkippedTasks": 0,
				"numFailedTasks": 4,
				"numKilledTasks": 25,
				"numCompletedIndices": 585,
				"numActiveStages": 0,
				"numCompletedStages": 0,
				"numSkippedStages": 0,
				"numFailedStages": 1,
				"killedTasksSummary": {
					"Stage cancelled": 25
				}
			}
		],
	:param app_id:
	:return:
	"""
	try:
		# 커버로스 인증 정보 실어서 요청
		c3.kinit_using_subprocess()
		auth = HTTPSPNEGOAuth()

		app_info = {}

		app_url = f'{SPARK_HISTORY_SERVER.rstrip("/")}/api/v1/applications/{app_id}'
		logging.info(f'get_shs_app_info() app_url={app_url}')
		res = requests.get(app_url, timeout=10, auth=auth)
		if res.status_code == 200:
			body = res.json()
			logging.info(f'app status body={utils.prettyStr(body)}')

			attempts = body['attempts']

			if attempts:
				# 마지막 시도
				# last_attempt = {
				# 	"attemptId": "4",
				# 	"startTime": "2024-05-03T20:07:37.526GMT",
				# 	"endTime": "2024-05-03T20:21:08.580GMT",
				# 	"lastUpdated": "2024-05-03T20:21:08.618GMT",
				# 	"duration": 811054,
				# 	"sparkUser": "gfp-data",
				# 	"completed": true,
				# 	"appSparkVersion": "3.2.1",
				# 	"startTimeEpoch": 1714766857526,
				# 	"endTimeEpoch": 1714767668580,
				# 	"lastUpdatedEpoch": 1714767668618
				# }
				last_attempt = attempts[0]

				app_info = last_attempt

				# 마지막 시도에 해당하는 job의 상태가 하나라도 "FAILED"인게 있다면 실패
				job_url = f'{app_url}/{last_attempt.get("attemptId")}/jobs?status=failed'
				logging.info(f'get_shs_app_info() job_url={job_url}')
				res = requests.get(job_url, timeout=10, auth=auth)
				if res.status_code == 200:
					jobs = res.json()
					logging.info(f'job status body={utils.prettyStr(jobs)}')

					if jobs and len(jobs) > 0:
						logging.info(f'실패된 job이 하나라도 있으므로 실패. status=PROBE_STATUS_FAILED')
						app_info['status'] = PROBE_STATUS_FAILED
					else:
						logging.info(f'실패된 job이 하나도 없으므로 성공. status=PROBE_STATUS_SUCCEEDED')
						app_info['status'] = PROBE_STATUS_SUCCEEDED
				else:
					logging.info(f'job 조회에 대한 응답 코드가 200이 아님. status=PROBE_STATUS_EXCEPTION')
					app_info['status'] = PROBE_STATUS_EXCEPTION
			else:
				logging.info(f'app에 대한 attempts가 없음. status=PROBE_STATUS_NOT_FOUND')
				app_info['status'] = PROBE_STATUS_NOT_FOUND
		else:
			app_info['status'] = PROBE_STATUS_EXCEPTION

		if app_info['status'] == PROBE_STATUS_SUCCEEDED:
			app_info['finalStatus'] = PROBE_STATUS_SUCCEEDED.upper()
		elif app_info['status'] == PROBE_STATUS_KILLED:
			app_info['finalStatus'] = PROBE_STATUS_KILLED.upper()
		else:
			# 성공, kill 아니면 모두 실패로 처리
			app_info['finalStatus'] = PROBE_STATUS_FAILED.upper()

		logging.info(f'get_shs_app_info() app_info={utils.prettyStr(app_info)}')
		return app_info
	except requests.ConnectionError as ex:
		logging.error(f'{app_url}: connection exception. ex={ex}')
		raise ex
