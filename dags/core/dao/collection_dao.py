import logging
from typing import Dict, <PERSON>ple

from pymongo.collection import Collection


def do_sync(src: Collection, dest: Collection, filter: Dict) -> Tuple[int, int]:
    replace_cnt, insert_cnt = 0, 0

    for doc in src.find(filter):
        if dest.replace_one({'_id': doc['_id']}, doc, upsert=True).matched_count:
            replace_cnt += 1
        else:
            insert_cnt += 1

    return (replace_cnt, insert_cnt)


def delete_differences_from_target(target: Collection, ref: Collection, dry_run: bool = True):
    unmatched_docs = target.aggregate([
        {
            '$lookup': {
                'from': ref.name,
                'localField': '_id',
                'foreignField': '_id',
                'as': 'matched_docs'
            }
        },
        {
            '$match': {
                'matched_docs': {'$eq': []}
            }
        },
        {
            '$project': {'_id': 1}
        }
    ])

    deleteCnt = 0
    for doc in unmatched_docs:
        if dry_run:
            logging.info(f"{doc['_id']}: unmatched, candidate for delete")
        else:
            delRes = target.delete_one({'_id': doc['_id']})
            if delRes.deleted_count != 1:
                raise ValueError(f"{doc['_id']}: unexpected delete count: {delRes.deleted_count}")
        deleteCnt += 1

    return deleteCnt
