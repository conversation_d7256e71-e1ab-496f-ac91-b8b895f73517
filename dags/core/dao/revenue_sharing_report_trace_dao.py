"""
PyMongo 3.12.3 documentation » API Documentation
    https://pymongo.readthedocs.io/en/3.12.3/api/pymongo/index.html
"""
import logging

import pymongo
from airflow.providers.mongo.hooks.mongo import MongoHook
from bson import ObjectId
from pymongo.results import InsertOneResult
from sqlalchemy_utils.types.enriched_datetime.pendulum_datetime import pendulum

from core.base import CONN_MONGO_FOR_DATA, DEFAULT_TZ
from core.dao.base_dao import decide_state_by_task_state, decide_at

_COLLECTION = 'RevenueSharingReportTrace'


def get_trace(filter: dict) -> dict:
	"""
	DATA DB > RevenueSharingReportTrace 컬렉션에서 filter에 해당하는 다큐먼트 조회
	:param filter:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]
		doc = coll.find_one(filter)
		return doc


def get_traces(filter: dict, sort: dict = [('date', pymongo.ASCENDING), ('createdAt', pymongo.ASCENDING)],
			   limit=None) -> list:
	"""
	DATA DB > RevenueSharingReportTrace 컬렉션에서 filter에 해당하는 목록 조회
	:param filter:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]
		docs = coll.find(filter).sort(sort)
		if limit:
			docs.limit(limit)
		doc_list = list(docs)

		return doc_list


def update_trace(filter: dict, set_values: dict):
	"""
	트레이스 업데이트

	:param filter
	:param set_values
	"""

	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]

		# updated에 modifiedAt이 없다면 추가
		if 'modifiedAt' not in set_values:
			set_values['modifiedAt'] = pendulum.now()

		res = coll.update_one(
			filter,
			{'$set': set_values}
		)

		if res.modified_count > 0:
			logging.info(f'{_COLLECTION}: filter={filter}: Updated: set_values={set_values}')
		else:
			logging.info(f'{_COLLECTION}: filter={filter}: Not updated: set_values={set_values}')



def upsert_trace(filter, doc) -> InsertOneResult:
	"""
	없으면  추가, 있으면 업데이트
	:param doc:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]

		if not doc.get('modifiedAt'):
			doc['modifiedAt'] = pendulum.now(DEFAULT_TZ)

		return coll.update_one(
			filter,
			{
				'$set': doc,
				'$setOnInsert': {'createdAt': pendulum.now(DEFAULT_TZ)}
			},
			upsert=True
		)


def update_state(report_id: str, task_state: str):
	set_values = {
		'state': decide_state_by_task_state(task_state),
		**decide_at(task_state)
	}

	update_trace({'report_id': ObjectId(report_id)}, set_values)
