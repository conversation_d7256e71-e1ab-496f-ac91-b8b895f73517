import logging

import pymongo
from airflow.providers.mongo.hooks.mongo import MongoHook

from core.base import CONN_MONGO_FOR_DATA

COLLECTION = 'SyncPublishers'


def get_publishers(filter: dict, sort: list = [('_id', pymongo.ASCENDING)]) -> list:
	"""
	filter에 해당하는 이력 1 건 조회.
	:param filter:
	:param sort: [('field1', pymongo.ASCENDING), ('field2', pymongo.DESCENDING)]
	:return:
	"""
	with Mon<PERSON><PERSON>ook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]

		cursor = coll.find(filter).sort(sort)
		doc_list = list(cursor)

		return doc_list


def get_publisher(filter: dict) -> dict:
	"""
	:param filter:
	:return:
	"""
	with Mon<PERSON><PERSON><PERSON>(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]

		doc = coll.find_one(filter)

		if doc:
			return doc
		else:
			logging.error(f'no such document in Publishers. filter={filter}')
