import logging
import pendulum
import pymongo

from bson.objectid import ObjectId
from airflow.providers.mongo.hooks.mongo import MongoHook

from core.base import CONN_MONGO_FOR_DATA, \
	ST_IN_PROGRESS, ST_COMPLETE, ST_FAILURE, \
	T_ST_AGG_IN_PROGRESS, T_ST_AGG_COMPLETE, T_ST_AGG_FAILURE, T_ST_UPLOAD_FAILURE


# https://wiki.navercorp.com/display/GFP/CustomReports
COLLECTION = 'CustomReports'


def count_reports(filter: dict):
	"""
	CustomReports 에서 state=READY 인 리포트 건수 조회

	:return: count
	"""

	with <PERSON>go<PERSON><PERSON>(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]

		count = coll.count_documents(filter=filter)

		logging.info(f'count_reports() filter= {filter}, count= {count}')

		return count


def get_reports(filter: dict, projection: dict = None):
	"""
	CustomReports 에서 리포트 목록 조회 ( createdAt 순 )

	:return: reports
	"""

	with <PERSON>go<PERSON><PERSON>(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]

		reports = list(coll.find(filter=filter, projection=projection).sort('createdAt', pymongo.ASCENDING))

		logging.info(f'get_reports() filter= {filter}, reports = {reports}')

		return reports


def get_report_by_report_id(report_id: str):
	"""
	CustomReports 에서 report_id 에 해당 하는 리포트 단건 조회

	:return: report
	"""

	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]

		report = coll.find_one(ObjectId(report_id))

		logging.info(f'get_report() report = {report}')

		return report


def get_report_task_state(report_id: str):
	"""
	report_id 에 해당 하는 taskState 조회
		taskState = AGG_IN_PROGRESS / AGG_FAILURE / AGG_COMPLETE / UPLOAD_IN_PROGRESS / UPLOAD_COMPLETE / UPLOAD_FAILURE

	:param report_id:
	:return: taskState
	"""

	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]

		report = coll.find_one(ObjectId(report_id), ['taskState'])

		return report['taskState']


def _update_report(report_id: str, set_values: dict):
	"""
	report_id 에 해당 하는 리포트 정보 업데이트

	:param report_id: 리포트 id
	:param set_values: state / taskState / startedAt / succeededAt / expiredAt / errorMessage / failedAt
	"""

	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[COLLECTION]

		res = coll.update_one(
			{'_id': ObjectId(report_id)},
			{'$set': set_values}
		)

		if res.modified_count == 1:
			logging.info(f'{COLLECTION}: report_id={report_id}: Updated: set_values={set_values}')
		else:
			logging.info(f'{COLLECTION}: report_id={report_id}: Not updated: set_values={set_values}')


def update_state(report_id: str, state: str):
	set_values = {}

	if state == ST_IN_PROGRESS:
		set_values = {'state': ST_IN_PROGRESS, 'startedAt': pendulum.now()}
	elif state == ST_COMPLETE:
		set_values = {'state': ST_COMPLETE, 'succeededAt': pendulum.now()}
	elif state in [T_ST_AGG_IN_PROGRESS, T_ST_AGG_COMPLETE]:
		set_values = {'taskState': state}
	elif state in [T_ST_AGG_FAILURE, T_ST_UPLOAD_FAILURE]:
		set_values = {'state': ST_FAILURE, 'taskState': state, 'errorMessage': state, 'failedAt': pendulum.now()}

	# UPLOAD_IN_PROGRESS, UPLOAD_COMPLETE 은 배치에서 처리

	_update_report(report_id, set_values)
