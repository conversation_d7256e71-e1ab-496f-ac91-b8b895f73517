"""
PyMongo 3.12.3 documentation » API Documentation
    https://pymongo.readthedocs.io/en/3.12.3/api/pymongo/index.html
"""
import logging

import pymongo
from airflow.providers.mongo.hooks.mongo import MongoHook
from bson import ObjectId
from sqlalchemy_utils.types.enriched_datetime.pendulum_datetime import pendulum

from core.base import CONN_MONGO_FOR_DATA
from core.dao.base_dao import decide_state_by_task_state, decide_at

_COLLECTION = 'RevenueSharingReports'

_metaPipeline = [
	{
		'$lookup': {
			'from': 'Schedules',
			'localField': 'schedule_id',
			'foreignField': '_id',
			'as': 'sch'
		}
	},
	{
		'$unwind': '$sch'
	},
	{
		'$lookup': {
			'from': 'SyncPublishers',
			'localField': 'sch.publisher_id',
			'foreignField': '_id',
			'as': 'pub'
		}
	},
	{
		'$unwind': '$pub'
	},
	{
		'$replaceRoot': {
			'newRoot': {
				'$mergeObjects': [
					'$$ROOT',
					{'cmsType': '$pub.cmsType'},
					{'publisher_id': '$pub._id'},
					{'publisherName': '$pub.name'},
					{'schedule_id': '$sch._id'},
					{'scheduleName': '$sch.name'},
				]
			}
		}
	},
	{'$project': {'pub': 0, 'sch': 0}}
]


def get_reports_count(filter):
	"""
	filter에 해당하는 수익쉐어 리포트 건수 조회

	:return: count
	"""

	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]

		count = coll.count_documents(filter)

		return count


def get_report(filter: dict) -> dict:
	"""
	DATA DB > RevenueSharingReports 컬렉션에서 filter에 해당하는 다큐먼트 조회
	:param filter:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]
		doc = coll.find_one(filter)
		return doc


def get_report_with_meta(filter: dict) -> dict:
	"""
	DATA DB > RevenueSharingReports 컬렉션에서 filter에 해당하는 다큐먼트 조회
	:param filter:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]
		pipeline = [
			{'$match': filter},
			*_metaPipeline,
			{'$limit': 1}
		]
		docs = coll.aggregate(pipeline)

	return docs.next()


def get_reports(filter: dict, sort: dict = [('date', pymongo.ASCENDING), ('modifiedAt', pymongo.ASCENDING)],
				limit=None) -> list:
	"""
	DATA DB > RevenueSharingReports 컬렉션에서 filter에 해당하는 목록 조회
	:param filter:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]

		docs = coll.find(filter).sort(sort)
		if limit:
			docs = docs.limit(limit)
		doc_list = list(docs)

		return doc_list


def get_reports_with_meta(filter: dict, sort: dict = [('date', pymongo.ASCENDING), ('modifiedAt', pymongo.ASCENDING)],
						  limit=None) -> list:
	"""
	DATA DB > RevenueSharingReports 컬렉션에서 filter에 해당하는 목록 조회
	:param filter:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]

		pipeline = [
			{'$match': filter},
			*_metaPipeline,
			{'$sort': dict(sort)},
			{'$limit': limit} if limit else {}
		]
		logging.info(f'pipeline={pipeline}')
		docs = coll.aggregate(pipeline)
		doc_list = list(docs)

		return doc_list


def get_reports_using_pipeline(pipeline: list) -> list:
	"""
	DATA DB > RevenueSharingReports 컬렉션에서 filter에 해당하는 목록 조회
	:param filter:
	:return:
	"""
	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]
		docs = coll.aggregate(pipeline)
		doc_list = list(docs)

		return doc_list


def get_task_state(report_id: str):
	"""
	report_id 에 해당 하는 taskState 조회
	:param report_id:
	:return: taskState
	"""

	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]

		report = coll.find_one(ObjectId(report_id), ['taskState'])

		return report['taskState']


def update_report(filter: dict, set_values: dict):
	"""
	리포트 업데이트

	:param filter
	:param set_values
	"""

	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]

		# updated에 modifiedAt이 없다면 추가
		if 'modifiedAt' not in set_values:
			set_values['modifiedAt'] = pendulum.now()

		res = coll.update_one(
			filter,
			{'$set': set_values}
		)

		if res.modified_count > 0:
			logging.info(f'{_COLLECTION}: filter={filter}: Updated: set_values={set_values}')
		else:
			logging.info(f'{_COLLECTION}: filter={filter}: Not updated: set_values={set_values}')


def update_reports(filter: dict, set_values: dict):
	"""
	리포트 업데이트

	:param filter
	:param set_values
	"""

	with MongoHook(CONN_MONGO_FOR_DATA) as hook:
		coll = hook.get_conn().get_database()[_COLLECTION]

		# updated에 modifiedAt이 없다면 추가
		if 'modifiedAt' not in set_values:
			set_values['modifiedAt'] = pendulum.now()

		res = coll.update_many(
			filter,
			{'$set': set_values}
		)

		if res.modified_count > 0:
			logging.info(f'{_COLLECTION}: filter={filter}: Updated: set_values={set_values}')
		else:
			logging.info(f'{_COLLECTION}: filter={filter}: Not updated: set_values={set_values}')


def update_task_state(report_id: str, task_state: str):
	set_values = {
		'taskState': task_state,
		'state': decide_state_by_task_state(task_state),
		**decide_at(task_state)
	}

	update_report({'_id': ObjectId(report_id)}, set_values)
