"""
####개요
    - mongo DB 접속 테스트
    - Airflow web - Connection 에 등록된 [CONN_MONGO_FOR_DATA, CONN_MONGO_FOR_CMS] 의 접속을 확인

####주기
    - 없음.

####config
    - 없음.
"""
import logging
import os

import pendulum
from airflow.models.dag import DAG
from airflow.operators.python import PythonOperator
from airflow.providers.mongo.hooks.mongo import MongoHook

from core.base import DEFAULT_TZ, CONN_MONGO_FOR_DATA, CONN_MONGO_FOR_CMS

_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")

_CONN_MONGO_TARGETS = [CONN_MONGO_FOR_DATA, CONN_MONGO_FOR_CMS]


def test_connections(**context):
    for conn_id in _CONN_MONGO_TARGETS:
        logging.info(f'{conn_id}: connecting default database...')
        with MongoHook(conn_id) as hook:
            _ = hook.get_conn().is_primary
            logging.info(f'{conn_id}: default database connected, and primary is confirmed')


with DAG(
        _DAG_ID,
        description='mongo DB 접속 테스트',
        default_args={
            'owner': 'ins.cho',
        },
        start_date=pendulum.datetime(2022, 3, 1, tz=DEFAULT_TZ),
        schedule_interval=None,
        tags=['airflow', 'operation', 'connection-test'],
        catchup=False,
) as dag:
    dag.doc_md = __doc__

    test_op = PythonOperator(
        task_id='test_connectivity',
        python_callable=test_connections,
    )
