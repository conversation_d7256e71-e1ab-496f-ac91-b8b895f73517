"""
#### 개요
- airflow worker container 내의 task 로그파일을 주기적으로 삭제 (30일치 로그 보관)
- worker pod 내 airflow-worker 컨테이너에서 bash 명령어을 실행하는 방식으로 동작
- test 환경의 make_silver_job DAG 의 로그의 사이즈가 너무 커져 15일치 로그를 보관
- [소스 참고](https://github.com/teamclairvoyant/airflow-maintenance-dags)

#### 주기
- 매일 14시 00분

#### config
- maxLogAgeInDays: (optional) 삭제 하고자 하는 로그의 날짜 기준 (해당 일 수 보다 오래된 로그 삭제. default 30일)
"""
import os
from datetime import timedelta

import jinja2
import pendulum
from airflow.configuration import conf
from airflow.models import DAG, Variable
from airflow.operators.bash import BashOperator
from airflow.operators.dummy import DummyOperator

from core.base import DEFAULT_TZ, ALERT_EMAIL_ADDRESSES, NUM_WORKERS, PROFILE

_DAG_ID = os.path.basename(__file__).replace(".pyc", "").replace(".py", "")

# 삭제하고자 하는 로그 기간. default 30일 (Variable 에 설정 되어 있지 않음)
_DEFAULT_MAX_LOG_AGE_IN_DAYS = Variable.get("airflow_log_cleanup__max_log_age_in_days", 30)

# pod 내의 로그 삭제 대상 디렉토리 경로 설정
try:
    BASE_LOG_FOLDER = conf.get("core", "BASE_LOG_FOLDER").rstrip("/")
except Exception as e:
    BASE_LOG_FOLDER = conf.get("logging", "BASE_LOG_FOLDER").rstrip("/")
if not BASE_LOG_FOLDER or BASE_LOG_FOLDER.strip() == "":
    raise ValueError(
        "BASE_LOG_FOLDER variable is empty in airflow.cfg. It can be found "
        "under the [core] (<2.0.0) section or [logging] (>=2.0.0) in the cfg file. "
        "Kindly provide an appropriate directory path."
    )
_DIRECTORIES_TO_DELETE = [BASE_LOG_FOLDER]

# 해당 경로의 lock 파일 또한 삭제함.
_LOG_CLEANUP_PROCESS_LOCK_FILE = "/tmp/airflow_log_cleanup_worker.lock"

_command = f"""
    echo "Getting Configurations..."
    BASE_LOG_FOLDER="{{{{ params.directory }}}}"
    WORKER_SLEEP_TIME="{{{{ params.sleep_time }}}}"
       
    sleep ${{WORKER_SLEEP_TIME}}s
    
    MAX_LOG_AGE_IN_DAYS="{{{{ dag_run.conf.get('maxLogAgeInDays') }}}}"
    if [ "${{MAX_LOG_AGE_IN_DAYS}}" == "None" ] || [ "${{MAX_LOG_AGE_IN_DAYS}}" == "" ]; then
        echo "maxLogAgeInDays conf variable isn't included. Using Default '{str(_DEFAULT_MAX_LOG_AGE_IN_DAYS)}'."
        MAX_LOG_AGE_IN_DAYS='{str(_DEFAULT_MAX_LOG_AGE_IN_DAYS)}'
    fi
    ENABLE_DELETE=true
    echo "Finished Getting Configurations"
    echo ""
    
    echo "Configurations:"
    echo "BASE_LOG_FOLDER:      '${{BASE_LOG_FOLDER}}'"
    echo "MAX_LOG_AGE_IN_DAYS:  '${{MAX_LOG_AGE_IN_DAYS}}'"
    echo "ENABLE_DELETE:        '${{ENABLE_DELETE}}'"
    
    cleanup() {{
        echo "Executing Find Statement: $1"
        FILES_MARKED_FOR_DELETE=`eval $1`
        echo "Process will be Deleting the following File(s)/Directory(s):"
        echo "${{FILES_MARKED_FOR_DELETE}}"
        echo "Process will be Deleting `echo "${{FILES_MARKED_FOR_DELETE}}" | grep -v '^$' | wc -l` File(s)/Directory(s)"
        # "grep -v '^$'" - removes empty lines.
        # "wc -l" - Counts the number of lines
        echo ""
        if [ "${{ENABLE_DELETE}}" == "true" ];
        then
            if [ "${{FILES_MARKED_FOR_DELETE}}" != "" ];
            then
                echo "Executing Delete Statement: $2"
                eval $2
                DELETE_STMT_EXIT_CODE=$?
                if [ "${{DELETE_STMT_EXIT_CODE}}" != "0" ]; then
                    echo "Delete process failed with exit code '${{DELETE_STMT_EXIT_CODE}}'"
    
                    echo "Removing lock file..."
                    rm -f {str(_LOG_CLEANUP_PROCESS_LOCK_FILE)}
                    if [ "${{REMOVE_LOCK_FILE_EXIT_CODE}}" != "0" ]; then
                        echo "Error removing the lock file. Check file permissions.\nTo re-run the DAG, ensure that the lock file has been deleted ({str(_LOG_CLEANUP_PROCESS_LOCK_FILE)})."
                        exit ${{REMOVE_LOCK_FILE_EXIT_CODE}}
                    fi
                    exit ${{DELETE_STMT_EXIT_CODE}}
                fi
            else
                echo "WARN: No File(s)/Directory(s) to Delete"
            fi
        else
            echo "WARN: You're opted to skip deleting the File(s)/Directory(s)!!!"
        fi
    }}
    
    
    if [ ! -f {str(_LOG_CLEANUP_PROCESS_LOCK_FILE)} ]; then
    
        echo "Lock file not found on this node! \nCreating it to prevent collisions..."
        touch {str(_LOG_CLEANUP_PROCESS_LOCK_FILE)}
        CREATE_LOCK_FILE_EXIT_CODE=$?
        if [ "${{CREATE_LOCK_FILE_EXIT_CODE}}" != "0" ]; then
            echo "Error creating the lock file. Check if the airflow user can create files under tmp directory. \nExiting..."
            exit ${{CREATE_LOCK_FILE_EXIT_CODE}}
        fi
    
        echo ""
        echo "Running Cleanup Process..."
    
        if [ "{PROFILE}" == "test" ]; then
            FIND_STATEMENT="find ${{BASE_LOG_FOLDER}}/make_silver_job/* -type f -mtime +15"
            DELETE_STMT="${{FIND_STATEMENT}} -exec rm -f {{}} \;"
        
            cleanup "${{FIND_STATEMENT}}" "${{DELETE_STMT}}"
            CLEANUP_EXIT_CODE=$?
        fi
        
        FIND_STATEMENT="find ${{BASE_LOG_FOLDER}}/*/* -type f -mtime +${{MAX_LOG_AGE_IN_DAYS}}"
        DELETE_STMT="${{FIND_STATEMENT}} -exec rm -f {{}} \;"
    
        cleanup "${{FIND_STATEMENT}}" "${{DELETE_STMT}}"
        CLEANUP_EXIT_CODE=$?
    
        FIND_STATEMENT="find ${{BASE_LOG_FOLDER}}/*/* -type d -empty"
        DELETE_STMT="${{FIND_STATEMENT}} -prune -exec rm -rf {{}} \;"
    
        cleanup "${{FIND_STATEMENT}}" "${{DELETE_STMT}}"
        CLEANUP_EXIT_CODE=$?
    
        FIND_STATEMENT="find ${{BASE_LOG_FOLDER}}/* -type d -empty"
        DELETE_STMT="${{FIND_STATEMENT}} -prune -exec rm -rf {{}} \;"
    
        cleanup "${{FIND_STATEMENT}}" "${{DELETE_STMT}}"
        CLEANUP_EXIT_CODE=$?
    
        echo "Finished Running Cleanup Process"
    
        echo "Deleting lock file..."
        rm -f {str(_LOG_CLEANUP_PROCESS_LOCK_FILE)}
        REMOVE_LOCK_FILE_EXIT_CODE=$?
        if [ "${{REMOVE_LOCK_FILE_EXIT_CODE}}" != "0" ]; then
            echo "Error removing the lock file. Check file permissions. \nTo re-run the DAG, ensure that the lock file has been deleted ({str(_LOG_CLEANUP_PROCESS_LOCK_FILE)})."
            exit ${{REMOVE_LOCK_FILE_EXIT_CODE}}
        fi
    
    else
        echo "Another task is already deleting logs on this worker node. \nSkipping it!"
        echo "If you believe you're receiving this message in error, kindly check \nif {str(_LOG_CLEANUP_PROCESS_LOCK_FILE)} exists and delete it."
        exit 0
    fi
    
"""

with DAG(
        _DAG_ID,
        description='airflow task 로그를 주기적으로 삭제하는 dag',
        default_args={
            'owner': 'ins.cho',
            'email': ALERT_EMAIL_ADDRESSES,
            'email_on_failure': True,
            'email_on_retry': False,
            'retries': 1,
            'retry_delay': timedelta(minutes=1),
            'depends_on_past': False,  # 이전 주기의 task instance 중 하나라도 실패한 경우, 다음 DAGrun 에서 해당 task 가 완료 될 때 까지 대기
        },
        schedule_interval='0 14 * * *',
        start_date=pendulum.yesterday(tz=DEFAULT_TZ),
        tags=['airflow', 'maintenance', 'cleanup', 'task-log', 'worker', 'hourly'],
        catchup=False,
        template_undefined=jinja2.StrictUndefined,
        params={
            'maxLogAgeInDays': ''
        }
) as dag:
    dag.doc_md = __doc__

    setup = DummyOperator(
        task_id='setup',
    )

    # 각 worker (pod) 에 대해서 실행
    for log_cleanup_id in range(1, NUM_WORKERS + 1):
        # 각 디렉토리에 대해 실행
        for dir_id, directory in enumerate(_DIRECTORIES_TO_DELETE):
            log_cleanup_op = BashOperator(
                task_id=f'log_cleanup_worker_num_{str(log_cleanup_id)}_dir_{str(dir_id)}',
                bash_command=_command,
                params={
                    "directory": str(directory),
                    "sleep_time": int(log_cleanup_id) * 3},
            )

            setup >> log_cleanup_op
