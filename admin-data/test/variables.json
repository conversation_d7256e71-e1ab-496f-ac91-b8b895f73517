{"airflow_alarm_receivers": "<EMAIL>", "airflow_num_workers": 4, "batch_server_addr": "http://*************:12000", "batch_server_ip_addr": ["http://*************:12000"], "bronze_log_home": "/user/biz-gep/data/log/ssp/parquet", "c3_app_rest_url": "", "c3_namespace": "bizcloud", "c3_password": "gfp-data", "c3_realm": "C3X.NAVER.COM", "c3_webhdfs_url": "http://adevthm003-sa.nfra.io:50070/webhdfs/v1", "druid_overlord": ["http://adbi001-sa.nfra.io:8090"], "gold_index_home": "/user/gfp-data/gold", "hadoop_file_browser_view": "http://adevthm001-sa.nfra.io:8080/views/FILES/1.0.0/AUTO_FILES_INSTANCE/#/files?path=", "neon_config": {"exchg_rate": {"endpoint": "https://nfidev.navercorp.com:5002", "nat_ip": ""}}, "profile": "test", "scheduler_log_cleanup": {"args": ["/opt/airflow/logs/scheduler"], "container": "airflow-scheduler", "pod_selector": "release=airflow,app=airflow,component=scheduler", "script": "/opt/airflow/dags/repo/dags/core/scheduler_log_cleanup.py"}, "silver_log_home": "/user/gfp-data/silver", "spark_history_server": "http://spark3-his.nam-batch.svc.ad1.io.navercorp.com:18080", "sparkling_image_name": "reg.navercorp.com/gfp/sparkling-test", "sparkling_image_tag": "db02000-**********", "sparkling_yarn_archive": "hdfs://bizcloud/user/gfp-data/apps/spark/3.2.1/spark3.2.1_jars.tar.gz", "sync_collections": {"completeSyncCollections": ["AdUnits", "AdProviderInfos", "AdProviderPlaces"], "incrementalSyncCollections": [], "sourceCollections": ["Publishers", "PublisherServices", "AdProviders", "AdUnits", "AdProviderInfos", "AdProviderPlaces", "Corporations", "MappingHistory", "Environments", "Deals", "BiddingGroups"], "targetPrefix": "Sync", "targetSuffix": ""}, "yarn_resource_manager": "http://adevthm001-sa.nfra.io:8088", "zircon_alarm_receiver": "<EMAIL>", "zircon_home": "/user/gfp-data/zircon"}