package main

import (
	"fmt"
	"io/ioutil"
	"net/http"
	apigw "oss.navercorp.com/api-gateway/api-gateway-hmac.git/go/macmanager"
)

func main() {
	apigwHmac()
}

func apigwHmac() {

	url := "http://dev.apis.naver.com/gladgfp_pub_blog/gladgfp/downloadRevenueSharingReport?rsKeyGroupId=5bf3a4e28399e7cabac24ac1&date=20191007"
	hmacKey := "1al7aMlXvzm7uKo0LEvrATfAOk9dWUH6cxdYkAFN9MfDvMdU3io0Z4eQUgcjr1qW" // consumerId: gladgfp_pub_blog's hmac key
	encodedUrl := apigw.GetEncryptURLByKey(url, hmacKey)                          // URL 전체를 hamcKey로 암호화

	/*
		url:		http://dev.apis.naver.com/gladgfp_pub_blog/gladgfp/downloadRevenueSharingReport?rsKeyGroupId=5bf3a4e28399e7cabac24ac1&date=20191007
		encodedUrl:	http://dev.apis.naver.com/gladgfp_pub_blog/gladgfp/downloadRevenueSharingReport?rsKeyGroupId=5bf3a4e28399e7cabac24ac1&date=20191007&msgpad=1579506727585&md=Zz7b%2BOo1r5%2FeohZ5AoAKv%2BJFV4M%3D
	*/
	fmt.Printf("url:\t\t%s\nencodedUrl:\t%s\n", url, encodedUrl)

	// 암호화된 URL로 GET 호출
	resp, err := http.Get(encodedUrl)
	if err != nil {
		panic(err)
	}

	defer resp.Body.Close()

	// 결과 출력
	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%s\n", string(data))
}
