# -*- coding: utf-8 -*-
import subprocess

# Python 2.7.5 버전으로 작성됨

# keyGroup_id와 schedule_id 쌍 입력
keyGroup_schedule_pairs = [
	{"keyGroup_id": "61a25d5fa3dbc5002e959e01", "schedule_id": "61a25fa21707a200208a0ab5",
	 "schedule_name": "NTV SMR 일단위수익쉐어"},
	{"keyGroup_id": "5f07cd2a70e6e00017f90ad6", "schedule_id": "6018fe901a643d002ec750d2",
	 "schedule_name": "네이버TV 일단위 매출"},
	{"keyGroup_id": "601288472b82de0019777aca", "schedule_id": "6018fe3e2a4d98002069561a",
	 "schedule_name": "스포츠 동영상 일단위 매출"}
]

# 연도와 월 설정
yyyy = "2024"
months = ["{:02}".format(month) for month in range(5, 6)]  # 05월부터 10월까지
dds = ["{:02}".format(month) for month in range(25, 26)]  # 25일부터 25일까지


def backup():
	for pair in keyGroup_schedule_pairs:
		keyGroup_id = pair["keyGroup_id"]
		schedule_id = pair["schedule_id"]
		print(
			"-------------------------- keyGroup_id: {}, schedule_id: {} schedule_name: {} --------------------------".format(
				keyGroup_id, schedule_id, pair['schedule_name']))

		for mm in months:
			for dd in dds:
				file_name = "{}_{}{}{}.csv".format(keyGroup_id, yyyy, mm, dd)

				# 명령어 정의
				my_dir = "/home1/owfs_web/download/report/revenuesharing2/{}/{}/{}".format(yyyy, mm, keyGroup_id)
				cmd_mkdir = "mkdir -p {}/1166".format(my_dir)
				cmd_copy = "cp {}/{} {}/1166/.".format(my_dir, file_name, my_dir)

				# 명령어 실행
				try:
					print("명령어 실행 mkdir: {}".format(cmd_mkdir))
					subprocess.call(cmd_mkdir, shell=True)  # subprocess.run 대신 subprocess.call 사용

					print("명령어 실행 org: {}".format(cmd_copy))
					subprocess.call(cmd_copy, shell=True)  # subprocess.run 대신 subprocess.call 사용

					print("명령어 실행 완료: {}, {}, {}-{}-{}\n".format(keyGroup_id, schedule_id, yyyy, mm, dd))
				except Exception as e:
					print("명령어 실행 중 오류 발생: {}".format(e))


def upload_to_hdfs():
	# 반복 처리
	for pair in keyGroup_schedule_pairs:
		keyGroup_id = pair["keyGroup_id"]
		schedule_id = pair["schedule_id"]
		print(
			"-------------------------- keyGroup_id: {}, schedule_id: {} schedule_name: {} --------------------------".format(
				keyGroup_id, schedule_id, pair['schedule_name']))

		for mm in months:
			for dd in dds:
				file_name = "{}_{}{}{}.csv".format(keyGroup_id, yyyy, mm, dd)
				target_org_file_name = "{}_{}{}{}_org.csv".format(keyGroup_id, yyyy, mm, dd)
				target_new_file_name = "{}_{}{}{}_new.csv".format(keyGroup_id, yyyy, mm, dd)

				my_dir = "/home1/owfs_web/download/report/revenuesharing2/{}/{}/{}".format(yyyy, mm, keyGroup_id)

				# 명령어 정의
				cmd_org = "hdfs dfs -put {}/1166/{} hdfs://pg07/user/gfp-data/temp/juyounkim/sus/1166/{}".format(
					my_dir, file_name, target_org_file_name)
				cmd_new = "hdfs dfs -put {}/{} hdfs://pg07/user/gfp-data/temp/juyounkim/sus/1166/{}".format(
					my_dir, file_name, target_new_file_name)

				# 명령어 실행
				try:
					print("명령어 실행 org: {}".format(cmd_org))
					subprocess.call(cmd_org, shell=True)  # subprocess.run 대신 subprocess.call 사용

					print("명령어 실행 new: {}".format(cmd_new))
					subprocess.call(cmd_new, shell=True)  # subprocess.run 대신 subprocess.call 사용

					print("명령어 실행 완료: {}, {}, {}-{}-{}\n".format(keyGroup_id, schedule_id, yyyy, mm, dd))
				except Exception as e:
					print("명령어 실행 중 오류 발생: {}".format(e))

# backup()
# upload_to_hdfs()
