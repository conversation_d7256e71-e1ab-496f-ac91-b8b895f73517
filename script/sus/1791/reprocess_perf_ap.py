from datetime import datetime, timedelta

import pymongo
from pymongo import MongoClient

# dbUrl = '*******************************************************************************************************************************************************'
# dbName = 'gfp-test'

dbUrl = '***************************************************************************************************************************'
dbName = 'gfp-real'

client = MongoClient(dbUrl)
db = client[dbName]
ap_perf_coll_name = 'AdProviderPerformanceDaily'


def _get_gfp_fee_rate_map(ymd):
    """
    날짜별 수수료율 맵 생성
    :param ymd: 
    :return: 
    """
    coll = db['GfpFeeRate']

    filter = {'date': ymd}
    project = {'_id': 0, 'publisher_id': 1, 'adProvider_id': 1, 'feeRate': 1}
    cursor = coll.find(filter, project)
    doc_list = list(cursor)

    rate_map = {}
    for doc in doc_list:
        # print(f'doc = {doc}')
        key = f'{doc["publisher_id"]}_{doc["adProvider_id"]}'
        rate_map[key] = float(str(doc["feeRate"]))

    # for item in rate_map:
    # print(f'{item} = {rate_map[key]}')

    return rate_map


def _get_adProvider_perf(ymd):
    """
    날짜별 성과리포트(광고공급자) 조회
    :param ymd: 
    :return: 
    """
    coll = db[ap_perf_coll_name]

    filter = {
        'date': ymd,
    }
    cursor = coll.find(filter)
    doc_list = list(cursor)

    # for doc in doc_list:
    #     print(f'doc = {doc}')

    return doc_list


def _reprocess():
    # start_time = datetime(2022, 4, 11, 0, 0, 0)  # test
    # start_time = datetime(2022, 7, 13, 0, 0, 0)  # real

    start_time = datetime(2023, 6, 6, 0, 0, 0)  # inclusive
    end_time   = datetime(2023, 6, 7, 0, 0, 0)  # inclusive

    curr_time = start_time

    total_begin_time = datetime.now()
    coll = db[ap_perf_coll_name]
    while curr_time <= end_time:
        begin_time = datetime.now()

        # 적용할 날짜
        ymd = curr_time.strftime("%Y%m%d")
        print(f'\n[{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] ymd: {ymd} ...')

        # 수수료율 맵 초기화
        fee_rate_map = _get_gfp_fee_rate_map(ymd)

        # 성과리포트 조회
        perf_list = _get_adProvider_perf(ymd)
        for perf in perf_list:
            # 수수료율 설정
            key = f'{perf["publisher_id"]}_{perf["adProvider_id"]}'
            # print(f'key={key}')
            if key in fee_rate_map:
                fee_rate = fee_rate_map[key]
                # print(f'수수료율 > 0')
            else:
                fee_rate = 0.0
                # print(f'수수료율 정보 없음')

            # 수수료율에 따른 순수익 업데이트
            krwNetBidPriceSum = perf["krwBidPriceSum"] * (1 - fee_rate)
            usdNetBidPriceSum = perf["usdBidPriceSum"] * (1 - fee_rate)
            coll.update_one({'_id': perf["_id"]}, {'$set': {
                'krwNetBidPriceSum': krwNetBidPriceSum,
                'usdNetBidPriceSum': usdNetBidPriceSum,
            }})

        # 날짜별 소요 시간 측정
        finish_time = datetime.now()
        elapsed_time = finish_time - begin_time
        print(f'elapsed_time={elapsed_time}')

        curr_time = curr_time + timedelta(days=1)

    # 총 소요 시간 측정
    total_finish_time = datetime.now()
    total_elapsed_time = total_finish_time - total_begin_time
    print(f'total_elapsed_time={total_elapsed_time}')


_reprocess()
