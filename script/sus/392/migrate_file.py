'''
테스트환경
	- 2019년도 owfs 파일은 모두 삭제했음.(이상한 파일 이름들이 있어서)

	- 이관된 정보
		as-is: /home1/owfs_web/download/report/revenuesharing2/2025/01/5bfe4649762f05001995b8fe/5bfe4649762f05001995b8fe_20250130.csv
		to-be:                                /revenue_sharing_report/2025/01/scheduleId=5c08ecf9ee89a20033b55712


'''
import json
import os
import subprocess

from bson import json_util
from bson.objectid import ObjectId
from pymongo import MongoClient

conf = {
	'test': {
		'db': {
			'cms': {
				'url': '************************************************************************************************************',
				'name': 'ssp-test'
			},
			'data': {
				'url': '*******************************************************************************************************************************************************',
				'name': 'gfp-test',
			},
		},
		'nubes': {
			'bucket': 'nam_api_dev',
			'lookupAddress': 'a-dev.lookup.nubes.navercorp.com:8080',
			'gatewayAddress': 'a-dev.nubes.sto.navercorp.com:8000',
		},
		'vlive': {
			'scheduleIds': ['5c049b41b9e0dc7224134339', '5ddcdf124e36ca002e87e253'],
			'keyGroupIds': ['5bf3a4e28399e7cabac24ac1'],
		}
	},
	'real': {
		'db': {
			'cms': {
				'url': '**************************************************************************************************************************************************',
				'name': 'ssp'
			},
			'data': {
				'url': '****************************************************************************************************************************',
				'name': 'gfp-real',
			},
		},
		'nubes': {
			'bucket': 'nam_api_report',
			'lookupAddress': 'a.lookup.nubes.navercorp.com:8080',
			'gatewayAddress': 'a.nubes.sto.navercorp.com:8000',
		},
		'vlive': {
			'scheduleIds': ['60483a39e66884002001844b', '5c6a7fcf237ae90384abaa97'],
			'keyGroupIds': ['5c6a7fcf237ae90384abaa97', '60483a39e66884002001844b', '5dc8fb322dce19002300da04'],
		}
	}
}

phase = 'test'

client = MongoClient(conf[phase]['db']['data']['url'])
db = client[conf[phase]['db']['data']['name']]
sch_coll = db['TempSchedules']

NUBES = {
	'DAY': f'/revenue_sharing_report/:yyyy/:mm/scheduleId=:scheduleId/RevenueSharingReport_:yyyy:mm:dd_:scheduleId.csv',
	'MONTH': f'/revenue_sharing_report/:yyyy/:mm/scheduleId=:scheduleId/RevenueSharingReport_:yyyy:mm_:scheduleId.csv'
}

'''
## 리포트 파일명 규칙
- 5bf3a4e28399e7cabac24ac1_20190828.csv        (period=DAY,   fileCreateCriteria=null,    interval=DAILY)   - 리포트를 매일 만듦
- 5bf3a4e28399e7cabac24ac1_201908.csv          (period=MONTH, fileCreateCriteria=MONTHLY, interval=DAILY)   - 리포트를 한 달에 한 번 만들고 한 달치를 한 파일로 만들되, 파일 내에서 일별로 분리
- 5bf3a4e28399e7cabac24ac1_201908_summary.csv  (period=MONTH, fileCreateCriteria=MONTHLY, interval=MONTHLY) - 리포트를 한 달에 한 번 만들고 한 달치를 한 파일로 만들되, 파일 내에서 일 데이터를 월로 합산해서 보여줌

## 파일 경로 (리얼)
SummaryRevenueSharingReport.filePath
-----------------------------------------------
/report/revenuesharing     12433
/report/revenuesharing1       26
/report/revenuesharing2    32421
/revenuesharing2/*          2655
/revenuesharing2/2022        240
/revenuesharing2/2023       1095
/revenuesharing2/2024       1098
/revenuesharing2/2025        222
null                          32 (정상도 포함)
-----------------------------------------------
total                      47567

### 파일 경로 설명(리얼)
- "/report/revenuesharing"로 직하는 것들은 초창기에 만들어져 V2로 버전업 됐을 때 이관되지 않은 것들
- "/revenuesharing2/"로 시작하는 것들은 NAM 리포트
- null은 STANDBY로 끝나거나, 아직 생성 전인 리포트

'''
OWFS_HOME = '/home1/owfs_web/download'
V1 = 'report/revenuesharing1'
V2 = 'report/revenuesharing2'


def prettyStr(doc: dict):
	return json.dumps(doc, indent=4)


def prettyStrOfMongoDoc(doc, indent=4):
	return json.dumps(json.loads(json_util.dumps(doc)), indent=indent)


def _migrate(target_version):
	# OWFS_HOME = '/home1/owfs_web/download'
	# V0 = 'report/revenuesharing1'
	owfs_v1_dir = os.path.join(OWFS_HOME, target_version)

	# 년 경로
	year_dirs = sorted(os.listdir(owfs_v1_dir))
	for year in year_dirs:
		year_dir = os.path.join(owfs_v1_dir, year)
		# print(f'year: {year} year_dir: {year_dir}')

		if phase == 'test' and year != '2025':
			continue

		# 월 경로
		month_dirs = sorted(os.listdir(year_dir))
		for month in month_dirs:
			month_dir = os.path.join(owfs_v1_dir, year, month)
			# print(f'month: {month} month_dir: {month_dir}')

			# key_group 경로
			key_group_dirs = sorted(os.listdir(month_dir))
			for key_group in key_group_dirs:
				if key_group in conf[phase]['vlive']['keyGroupIds']:
					continue

				key_group_dir = os.path.join(owfs_v1_dir, year, month, key_group)
				print(f'key_group: {key_group} key_group_dir: {key_group_dir}')

				# csv 파일 리스트
				entries = sorted(os.listdir(key_group_dir))
				csv_files = [entry for entry in entries if
							 os.path.isfile(os.path.join(key_group_dir, entry)) and entry.endswith('.csv')]
				# print(f'csv_files: {csv_files}')
				for csv_file in csv_files:
					file_name = csv_file.split('.')[0]  # csv_file에서 .csv 제거
					date_for_file_name = file_name.split('_')[1]
					dd = ''

					period = 'DAY'
					interval = 'DAILY'
					if file_name.endswith('_summary'):
						period = 'MONTH'
						interval = 'MONTHLY'
					else:
						if len(date_for_file_name) == 6:
							period = 'MONTH'
							interval = 'DAILY'
						else:
							period = 'DAY'
							interval = 'DAILY'
							dd = date_for_file_name[-2:]

					# 스케줄 조회. period, interval, key_group_id로 schedule 조회
					schedule = sch_coll.find_one(
						{'keyGroup_id': ObjectId(key_group), 'period': period, 'interval': interval},
						{'_id': 1, 'name': 1}
					)
					# print(
					# 	f'\n\tkey_group = {key_group} file_name = {file_name} period = {period} interval = {interval} schedule = {schedule}')

					# NUBES = {
					# 	'DAY': f'/revenue_sharing_report/:yyyy/:mm/scheduleId=:scheduleId/RevenueSharingReport_:yyyy:mm:dd_:scheduleId.csv',
					# 	'MONTH': f'/revenue_sharing_report/:yyyy/:mm/scheduleId=:scheduleId/RevenueSharingReport_:yyyy:mm_:scheduleId.csv'
					# }

					'''
					key_group = 5bfe4649762f05001995b8fe file_name = 5bfe4649762f05001995b8fe_20190102_103003 period = DAY interval = DAILY schedule = {'_id': ObjectId('5c08ecf9ee89a20033b55712'), 'name': 'BLOG수익쉐어스케줄'}
					
							as_is_path = /home1/owfs_web/download/report/revenuesharing2/2022/12/601288472b82de0019777aca/601288472b82de0019777aca_20221201.csv
							nubes_path = /revenue_sharing_report/2022/12/scheduleId=6018fe3e2a4d98002069561a/RevenueSharingReport_20221201_6018fe3e2a4d98002069561a.csv
					
							as_is_path = /home1/owfs_web/download/report/revenuesharing2/2022/12/601288472b82de0019777aca/601288472b82de0019777aca_20221202.csv
							nubes_path = /revenue_sharing_report/2022/12/scheduleId=6018fe3e2a4d98002069561a/RevenueSharingReport_20221202_6018fe3e2a4d98002069561a.csv
					'''
					as_is_path = os.path.join(owfs_v1_dir, year, month, key_group, csv_file)

					if _get_file_size(as_is_path) == 0:
						print(f'\tfile size is 0. as_is_path = {as_is_path}')
						continue

					nubes_path = NUBES[period].replace(':yyyy', year).replace(':mm', month).replace(':dd', dd).replace(
						':scheduleId', str(schedule['_id']))
					print(f'\tas_is_path = {as_is_path}')
					print(f'\tnubes_path = {nubes_path}')

					try:
						result = _pupload(conf[phase]['nubes']['gatewayAddress'], conf[phase]['nubes']['bucket'],
										  as_is_path,
										  nubes_path)
						if not result:
							raise Exception('pupload() error. result is False')
					except Exception as e:
						print(e)
						return

				return


def _get_file_size(file_path):
	try:
		file_size = os.path.getsize(file_path)
		return file_size
	except Exception as e:
		print(e)


def _pupload(gateway_address, bucket, source, dest, part_size='100M'):
	env = f'export NUBES_GATEWAY_ADDRESS={gateway_address};'
	cmd = f'nubescli pupload --no-progress -w -p {part_size} {bucket}{dest} {source}'

	try:
		print(f'\tnubes 업로드 중.. {cmd}')
		result = subprocess.run(f'{env}{cmd}', shell=True, capture_output=True, text=True)

		if result.returncode != 0:
			print(f'nubes-cli.pupload() :: {cmd} >>>> error=\n\n{result.stderr}')
			raise Exception(result.stderr)
		elif 'ERROR' in result.stderr.upper():
			print(f'nubes-cli.pupload() :: {cmd} stderr >>>> error=\n\n{result.stderr}')
			raise Exception(result.stderr)
		else:
			# print(f'nubes-cli.pupload() :: {cmd} stderr >>>> complete=\n\n{result.stderr}')
			# print(f'nubes-cli.pupload() :: {cmd} stdout >>>> \n\n{result.stdout}')
			return True
	except Exception as err:
		raise err


def main():
	# _migrate(V1)
	_migrate(V2)


main()
