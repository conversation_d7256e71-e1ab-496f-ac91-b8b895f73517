{"name": "@ssp/webhdfs", "version": "1.2.3", "description": "Node.js WebHDFS REST API client for SSP Data", "main": "lib/webhdfs.js", "scripts": {"test": "./node_modules/.bin/mocha --reporter spec test/"}, "keywords": ["webhdfs", "hadoop", "big data", "fs", "storage"], "author": "<PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://oss.navercorp.com/da-ssp/webhdfs"}, "license": "MIT", "devDependencies": {"mocha": "^2.3.3", "must": "^0.13.1", "sinon": "^1.17.2", "webhdfs-proxy": "^0.1.2", "webhdfs-proxy-memory": "^0.1.2"}, "dependencies": {"buffer-stream-reader": "^0.1.1", "extend": "^3.0.0", "request": "^2.74.0"}}