package middleware

import (
	"github.com/gin-gonic/gin"
	"oss.navercorp.com/acl/acl-filter-go/acl"
	"oss.navercorp.com/da-ssp/gfp-api/config"
)

var myacl *acl.Acl

func registerAcl(router *gin.Engine) {
	myacl = acl.New()
	myacl.SetRemoteAclUrl(config.GetConfig("acl.remoteAclUrl").(string))     // "http://dev.acl-portal.navercorp.com/api/v2/acls"
	myacl.SetProviderId(config.GetConfig("acl.providerId").(string))         // "gladgfp"
	myacl.SetPrintLoadedInfo(config.GetConfig("acl.printLoadedInfo").(bool)) // true
	myacl.Load()
	router.Use(gin.WrapF(myacl.CheckAclFunc))
}
