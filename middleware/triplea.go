package middleware

import (
	"fmt"
	"time"
	"strconv"
	"net/http"
	"oss.navercorp.com/da-ssp/gfp-api/logger"

	"github.com/gin-gonic/gin"

	gfpError "oss.navercorp.com/da-ssp/gfp-api/error"
	"oss.navercorp.com/da-ssp/gfp-api/triplea"
)

/**
인증 확인 미들웨어
*/
func registerAuthentication(router *gin.Engine) gin.HandlerFunc {
	return func(ginCtx *gin.Context) {
		entry := ginCtx.Value("LogEntry").(*logger.Entry)

		// 파라미터 파싱
		userId := ginCtx.Query("userId")
		encodedUserId := ginCtx.Query("encodedUserId")
		timestamp := ginCtx.Query("timestamp")

		// 필수 파라미터 체크
		if len(userId) < 1 {
			param := map[string]string{"params": "userId"}
			ginCtx.AbortWithStatusJSON(http.StatusOK, gfpError.MissingRequiredParam.BizError(param))
			return
		}
		if len(encodedUserId) < 1 {
			param := map[string]string{"params": "encodedUserId"}
			ginCtx.AbortWithStatusJSON(http.StatusOK, gfpError.MissingRequiredParam.BizError(param))
			return
		}

		// timestamp 유효 30분 체크
		if len(timestamp) > 0 {
			timeLimit := time.Now().Add(time.Minute * -30).Unix()
			timestamp, err := strconv.ParseInt(timestamp, 10, 64)

			if err != nil || timestamp < timeLimit {
				entry.Warnf("[TRIPLE-A] timeLimit= %+v, timestamp=%+v", timeLimit, timestamp)
				entry.Warnf("[TRIPLE-A] Invalid Timestamp. %+v", err)
				ginCtx.AbortWithStatusJSON(http.StatusOK, gfpError.InvalidTimestamp.BizError(nil))
				return
			}
		}

		// 기본 인증
		if !triplea.Authenticate(ginCtx, userId, encodedUserId, timestamp) {
			entry.Warnf("[TRIPLE-A] Base-Auth Denied. UnMatched UserId-EncodedUserId. userId: %s", userId)
			param := map[string]string{"msg": "userId:" + userId}
			ginCtx.AbortWithStatusJSON(http.StatusUnauthorized, gfpError.UnAuthenticated.BizError(param))
			return
		}

		// API GW 인증
		apiGwConsumerId := ginCtx.GetHeader("x-apigw-consumer-id")
		if len(apiGwConsumerId) > 0 && !triplea.AuthenticateApiGw(ginCtx, userId, apiGwConsumerId) {
			msg := fmt.Sprintf("UnMatched UserId-GWConsumerId. userId: %s ApiGwConsumerId: %s", userId, apiGwConsumerId)
			entry.Warnf("[TRIPLE-A] APIGW-Auth Denied. %s", msg)
			param := map[string]string{"msg": msg}
			ginCtx.AbortWithStatusJSON(http.StatusUnauthorized, gfpError.UnAuthenticated.BizError(param))
			return
		}

		// API 사용권한 있는지 체크
		if !triplea.CheckApi(ginCtx, userId) {
			path := ginCtx.Request.URL.Path
			entry.Warnf("[TRIPLE-A] API 사용권한 없음.. %s", path)
			param := map[string]string{"path": path}
			ginCtx.AbortWithStatusJSON(http.StatusUnauthorized, gfpError.ApiAccessDenied.BizError(param))
			return
		}
	}
}
