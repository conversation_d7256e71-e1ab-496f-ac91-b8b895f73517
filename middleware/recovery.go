package middleware

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"oss.navercorp.com/acl/acl-filter-go/acl"

	gfpError "oss.navercorp.com/da-ssp/gfp-api/error"
)

func registerRecovery() gin.HandlerFunc {
	return func(ginCtx *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				switch err.(type) {
				case *gfpError.GfpApiError: // 명시적으로 GfpApiError를 panic()시킨 경우
					explicitErr := err.(*gfpError.GfpApiError)
					gfpError.ResponseError(ginCtx, explicitErr)
				case *acl.AccessDenied:
					log.Debugf("Recovered from the panic. Error type is ACL AcessDenied. So abort request.")
					ginCtx.AbortWithStatus(http.StatusForbidden)
				default: // GfpApiError 에러가 아닌 나머지(go 내장 함수에서 받은 에러 또는 문자열로 panic() 시킨 경우)
					param := map[string]string{
						"msg": fmt.Sprintf("%v", err),
					}
					implicitErr := gfpError.Default.BizError(param)
					gfpError.ResponseError(ginCtx, implicitErr)
				}
			}
		}()
		ginCtx.Next()
	}
}
