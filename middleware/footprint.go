package middleware

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
	"time"
)

func registerFootPrint(router *gin.Engine) gin.HandlerFunc {
	return func(ginCtx *gin.Context) {
		entry := ginCtx.Value("LogEntry").(*logger.Entry)

		t := time.Now()
		ginCtx.Next()
		latency := time.Since(t)
		entry.Debugf("ClientIP: %s Proto: %s Method: %s UserAgent: \"%s\" Path: %s Latency: %v Status: %v(%s)\n",
			ginCtx.ClientIP(), ginCtx.Request.Proto, ginCtx.Request.Method, ginCtx.Request.UserAgent(),
			ginCtx.Request.URL, latency, ginCtx.Writer.Status(), http.StatusText(ginCtx.Writer.Status()))
	}
}
