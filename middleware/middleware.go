package middleware

import (
	"github.com/gin-gonic/gin"

	"oss.navercorp.com/da-ssp/gfp-api/config"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
)

var log = logger.GetLogger("default")

/*
	등록된 순서대로 실행됨
	c.Next()를 사용하지 않고 기술하면 request 전에 실행됨.

	GUIDE ::
	gin.Context의 key는 대문자로 시작하는 CamelCase를 사용해 주세요.
		예) ginCtx.Set("RequestId", requestId)
*/
func Init(router *gin.Engine) {
	// APIDOC SampleUrl Cors 설정
	if config.GetEnv() == "test" {
		log.Debugf("미들웨어 등록 - CORS")
		router.Use(registerCors(router))
	}

	log.Debugf("미들웨어 등록 - Recovery")
	router.Use(registerRecovery())

	log.Debugf("미들웨어 등록 - RequestId")
	router.Use(registerRequestId(router))

	log.Debugf("미들웨어 등록 - RequestId를 갖는 로거")
	router.Use(registerLoggerWithRequestId(router))

	log.Debugf("미들웨어 등록 - FootPrint")
	router.Use(registerFootPrint(router))

	if config.GetEnv() == "production" {
		log.Debugf("미들웨어 등록 - ACL")
		registerAcl(router)
	}

	log.Debugf("미들웨어 등록 - AAA")
	router.Use(registerAuthentication(router))

}
