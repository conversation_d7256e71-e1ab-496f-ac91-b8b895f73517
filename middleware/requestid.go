package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/satori/go.uuid"
)

/*
	디버깅을 위해 request 마다 id 부여
*/
func registerRequestId(router *gin.Engine) gin.HandlerFunc {
	return func(ginCtx *gin.Context) {
		requestId := uuid.NewV4().String()
		ginCtx.Set("RequestId", requestId)                    // context에도 넣고
		ginCtx.Writer.Header().Set("X-Request-Id", requestId) // 헤더에도 넣고
		ginCtx.Next()
	}
}

/*
	gin.Context에 'RequestId'를 field로 추가한 *logrus.Entry 설정
*/
func registerLoggerWithRequestId(router *gin.Engine) gin.HandlerFunc {
	return func(ginCtx *gin.Context) {
		requestId, isExist := ginCtx.Get("RequestId")
		if isExist {
			entry := log.WithField("RequestId", requestId)
			ginCtx.Set("LogEntry", entry)
		}
	}
}
