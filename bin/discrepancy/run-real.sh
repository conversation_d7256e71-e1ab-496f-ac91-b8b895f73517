#!/bin/bash

# bin/discrepancy/run-real.sh 20250417 TTD 60dab3e46a93f789ce1e693b,625784b7ac46c9780677dbca 5fb4d7d4595924002d2476fa,615fd3d1fd3b680020464c0b,5f3cdb09a4ad0b0031bd4bd2,5f5f0901a4ad0b0031bd4cbf,615fd394fd3b680020464bfb,5c6a7e1dfbdf5c00196f4f65,66a8b51946f185002876105a,5c5d3e1728a2be037abf0948,5d11dc1a34480e001d31fb26,62ba83b9da47a8002758681e,62bab856fe520500193a200a,60236ba48a79d80020f80c9a,6371eb7b33337f002e664f20,5d8ad6db0fe0f652a82f60d3,5fbdb7c779012500176c9993
# bin/discrepancy/run-real.sh 20250417 NATIVO 673c11bdde1bb30012fa375a 5d8ad6db0fe0f652a82f60d3,5f5f0901a4ad0b0031bd4cbf,5c5d3e1728a2be037abf0948,5d11dc1a34480e001d31fb26,60236ba48a79d80020f80c9a,66a8b51946f185002876105a
# bin/discrepancy/run-real.sh 20250417 PANGLE 65fd4552a620e500274432f3 60236ba48a79d80020f80c9a,5fbdb7c779012500176c9993,5b8f695b422d910019805eff,66a8b51946f185002876105a,5f3cdb09a4ad0b0031bd4bd2,5d8ad6db0fe0f652a82f60d3,5f5f0901a4ad0b0031bd4cbf,5fb4d7d4595924002d2476fa,5c5d3e1728a2be037abf0948,5d11dc1a34480e001d31fb26,5e68ad97d7b77c00274b08f6
# bin/discrepancy/run-real.sh 20250417 RTBHOUSE 64e2dee3d0b344002096950b 5d11dc1a34480e001d31fb26,5f3cdb09a4ad0b0031bd4bd2,5c5d3e1728a2be037abf0948,66a8b51946f185002876105a,5f5f0901a4ad0b0031bd4cbf,5d8ad6db0fe0f652a82f60d3,60236ba48a79d80020f80c9a
# bin/discrepancy/run-real.sh 20250417 ADVIEW 5f0e90036a93f789ce6189fe 5d8ad6db0fe0f652a82f60d3,5c6a7e1dfbdf5c00196f4f65,5d11dc1a34480e001d31fb26,60ee40a8ed26150027a2ad5e,60236ba48a79d80020f80c9a,60ee40ecc633f500276a2a70,5f5f0901a4ad0b0031bd4cbf,62ba83b9da47a8002758681e,6371eb7b33337f002e664f20,62bab856fe520500193a200a,5c5d3e1728a2be037abf0948,66a8b51946f185002876105a,5f3cdb09a4ad0b0031bd4bd2,5b8f695b422d910019805eff,5e68ad97d7b77c00274b08f6
# bin/discrepancy/run-real.sh 20250417 BRIGHT_MOUNTAIN 6729e69611ac620018f83a72 5f3cdb09a4ad0b0031bd4bd2,5d8ad6db0fe0f652a82f60d3,5d11dc1a34480e001d31fb26,66a8b51946f185002876105a,5f5f0901a4ad0b0031bd4cbf,60236ba48a79d80020f80c9a,5c5d3e1728a2be037abf0948

source ~/.bashrc

sparkling_home=$(pwd)
aggregator="com.navercorp.gfp.biz.discrepancy.DiscrepancyDailyAggregator"

dateTime=$1
execTime=$(date '+%Y-%m-%dT%H:%M:%S.%3N')

echo sparkling_home=${sparkling_home}
echo dateTime=${dateTime}
echo aggregator=${aggregator}
echo execTime=${execTime}

spark-submit --class ${aggregator} \
	--name ${aggregator}-${dateTime}-${execTime} \
	--master yarn --deploy-mode cluster \
	--queue biz_gep \
	--num-executors 1 \
	--executor-cores 3 \
	--executor-memory 512m \
	--conf "spark.driver.extraJavaOptions=-Dhdp.version=*******-78 -XX:+UseG1GC -Dfile.encoding=utf-8" \
	--conf "spark.executor.extraJavaOptions=-XX:+UseG1GC" \
	--conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
	--conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
	--conf spark.hadoop.dfs.nameservices=pgcm,pg01,pg07 \
	--conf spark.kerberos.access.hadoopFileSystems=hdfs://pgcm,hdfs://pg01,hdfs://pg07 \
	--conf spark.kerberos.keytab=/home1/irteam/apps/c3/gfp-data.keytab \
	--conf spark.kerberos.principal=<EMAIL> \
	--conf spark.serializer=org.apache.spark.serializer.KryoSerializer \
	--conf spark.sql.caseSensitive=true \
	--conf spark.sql.parquet.mergeSchema=true \
	--conf spark.sql.warehouse.dir=hdfs://pg07/user/gfp-data/apps/spark/warehouse \
	--conf spark.eventLog.enabled=true \
	--conf spark.eventLog.dir=hdfs://pgcm/user/gfp-data/spark-history/ \
	--conf spark.yarn.am.extraJavaOptions=-Dhdp.version=*******-78 \
	--conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
	--conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
	--conf spark.yarn.archive=hdfs://pg07/user/gfp-data/apps/spark/3.2.1/spark3.2.1_jars.tar.gz \
	--conf spark.yarn.historyServer.address=https://gfp-data--spark-history-server--shs--18080.proxy-pan.c3s.navercorp.com \
	${sparkling_home}/jar/sparkling-s.jar ${dateTime} "${@:2}"

