#!/bin/bash

source ~/.bashrc

sparkling_home=$1
aggregator=$2
dateTime=$3
execTime=`date '+%Y-%m-%dT%H:%M:%S.%3N'`

echo sparkling_home=${sparkling_home}
echo dateTime=${dateTime}
echo aggregator=${aggregator}
echo execTime=${execTime}

spark-submit --class ${aggregator} \
	--name ${aggregator}-${dateTime}-${execTime} \
	--master yarn --deploy-mode cluster \
	--queue root.users.gfp \
	--num-executors 24 \
	--executor-cores 3 \
	--executor-memory 600m \
	--conf spark.sql.shuffle.partitions=5 \
	--conf spark.driver.memoryOverhead=1g \
	--conf spark.executor.memoryOverhead=500m \
	--conf spark.driver.extraJavaOptions=-XX:+UseG1GC \
	--conf spark.executor.extraJavaOptions=-XX:+UseG1GC \
	--conf spark.eventLog.enabled=true \
	--conf spark.sql.parquet.mergeSchema=true \
	--conf spark.sql.caseSensitive=true \
	--conf spark.serializer=org.apache.spark.serializer.KryoSerializer \
	--conf spark.yarn.archive=hdfs://BizCloud/user/irteam/spark3LibArchives/spark-3.2.1-libs.jar \
	${sparkling_home}/jar/sparkling-s.jar ${dateTime} "${@:4}"
