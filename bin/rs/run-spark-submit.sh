#!/bin/bash

# input_start는 include
# input_end는 exclude

input_start=20231120
input_end=20231122

startdate=$(date '+%Y%m%d' -d "$input_start") || exit -1
enddate=$(date '+%Y%m%d' -d "$input_end")     || exit -1

d="$startdate"
while [ "$d" != "$enddate" ]; do
        echo $d

        cmd="/home1/irteam/Projects/ssp-develop/sparkling-s/bin/rs/run-test.sh /home1/irteam/repo/sparkling-s com.navercorp.gfp.biz.revenuesharing.RevenueSharingAggregator $d"
        echo $cmd

        result=`$cmd`
        echo "$result"
        echo

        d=$(date '+%Y%m%d' -d "$d + 1 day")
done
