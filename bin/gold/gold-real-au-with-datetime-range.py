from datetime import datetime, timedelta
import subprocess

startTime = datetime(2022, 12, 15, 12, 0, 0)  # inclusive
endTime = datetime(2022, 12, 16, 6, 0, 0)  # exclusive
currTime = startTime

while currTime < endTime:
    mytime = currTime.strftime("%Y%m%d%H")

    print(f'\n[{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] ymdh: {mytime} ...')

    f = open(f'result_gold_au_{mytime}.txt', 'w')
    try:
        out = subprocess.check_output([r'/home1/irteam/repo/sparkling-s/bin/gold/run-real-au.sh',
                                       '/home1/irteam/repo/sparkling-s',
                                       'com.navercorp.gfp.biz.gold.AdUnitGoldsmith',
                                       mytime], encoding='utf-8')
        f.write(out)
        print(f'[{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] ymdh: {mytime} done')
    except Exception as ex:
        f.write(f"{mytime} 수행하다 에러 발생. {ex}")
    finally:
        f.close()

    currTime = currTime + timedelta(hours=1)

