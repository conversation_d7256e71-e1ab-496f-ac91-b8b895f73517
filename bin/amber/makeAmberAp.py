import subprocess


INMOBI2 = ["0", "5fae524f6a93f789cee8859b", "5d11dc1a34480e001d31fb26,60236ba48a79d80020f80c9a"]



process_INMOBI2 = subprocess.Popen(["./bin/amber-real-ap.sh", "/Users/<USER>/Documents/git/sparkling-s", "com.navercorp.gfp.biz.amber.AmberApDailyAggregator", "20221001", "20221031", INMOBI2[0], INMOBI2[1], INMOBI2[2]])

process_INMOBI2.wait()

print("COMPLETE")





# # OUTSIDE
# GOOGLE1 = ["0", "5c2436b438a0a3c9ea911556", "5d11dc1a34480e001d31fb26,5f5f0901a4ad0b0031bd4cbf,60236ba48a79d80020f80c9a,6183b1fac30079002001befc"]
# GOOGLE2 = ["0", "5c2436e438a0a3c9ea9121d5", "5d11dc1a34480e001d31fb26,6183b1fac30079002001befc"]
# GOOGLE3 = ["0", "5c24371938a0a3c9ea912ccf", "5d8ad6db0fe0f652a82f60d3"]
# GOOGLE4 = ["0", "60eeeb6d6a93f789ce0836c7", "5f5f0901a4ad0b0031bd4cbf,60236ba48a79d80020f80c9a,60ee4190f18654002e152997"]

# FAN1 = ["0", "5ff69da36a93f789ce252120", "5d11dc1a34480e001d31fb26,5f5f0901a4ad0b0031bd4cbf,60236ba48a79d80020f80c9a"]
# FAN2 = ["0", "60eeebab6a93f789ce085633", "5f5f0901a4ad0b0031bd4cbf,60236ba48a79d80020f80c9a,60ee4190f18654002e152997"]

# APP_NEXUS1 = ["0", "5d8ac41f0fe0f652a82f608d", "5d11dc1a34480e001d31fb26"]
# APP_NEXUS2 = ["0", "5f100e3f6a93f789ce25d3ae", "5f5f0901a4ad0b0031bd4cbf,5fb4d7d4595924002d2476fa,5fbdb7c779012500176c9993,60236ba48a79d80020f80c9a,60ee40a8ed26150027a2ad5e,60ee40ecc633f500276a2a70,615fd394fd3b680020464bfb,615fd3d1fd3b680020464c0b"]

# INMOBI1 = ["0", "5d8afe8338a0a3c9ea0e591d", "5d11dc1a34480e001d31fb26"]
# INMOBI2 = ["0", "5fae524f6a93f789cee8859b", "5d11dc1a34480e001d31fb26,5f5f0901a4ad0b0031bd4cbf,5fb4d7d4595924002d2476fa,5fbdb7c779012500176c9993,60236ba48a79d80020f80c9a,615fd394fd3b680020464bfb,615fd3d1fd3b680020464c0b"]

# MOLOCO = ["0", "5fffff0f6a93f789ce245f90", "5d11dc1a34480e001d31fb26,5f5f0901a4ad0b0031bd4cbf,5fb4d7d4595924002d2476fa,5fbdb7c779012500176c9993,60236ba48a79d80020f80c9a,60ee40a8ed26150027a2ad5e,60ee40ecc633f500276a2a70,615fd394fd3b680020464bfb,615fd3d1fd3b680020464c0b"]

# PUBMATIC = ["0", "5dc50c1f38a0a3c9ea85f79b", "5d11dc1a34480e001d31fb26,5f5f0901a4ad0b0031bd4cbf,5fb4d7d4595924002d2476fa,5fbdb7c779012500176c9993,60236ba48a79d80020f80c9a,60ee40a8ed26150027a2ad5e,60ee40ecc633f500276a2a70,615fd394fd3b680020464bfb,615fd3d1fd3b680020464c0b"]

# RUBICON = ["0", "5db6625577bd856e48781e89", "5d11dc1a34480e001d31fb26,5f5f0901a4ad0b0031bd4cbf,5fb4d7d4595924002d2476fa,5fbdb7c779012500176c9993,60236ba48a79d80020f80c9a,60ee40a8ed26150027a2ad5e,60ee40ecc633f500276a2a70,615fd394fd3b680020464bfb,615fd3d1fd3b680020464c0b"]

# APP_NEXUS_HB = ["0", "5ebb9df96a93f789ceb8ee7a", "5d11dc1a34480e001d31fb26"]

# AMAZON_HB = ["0", "5ec5f5876a93f789ceb2c693", "5d11dc1a34480e001d31fb26,5f5f0901a4ad0b0031bd4cbf,60236ba48a79d80020f80c9a"]

# OPENX = ["0", "5ed0a2586a93f789ced3ec68", "5d11dc1a34480e001d31fb26,5f5f0901a4ad0b0031bd4cbf,5fb4d7d4595924002d2476fa,5fbdb7c779012500176c9993,60236ba48a79d80020f80c9a,60ee40a8ed26150027a2ad5e,60ee40ecc633f500276a2a70,615fd394fd3b680020464bfb,615fd3d1fd3b680020464c0b"]

# ADVIEW = ["0", "5f0e90036a93f789ce6189fe", "5d11dc1a34480e001d31fb26,5f5f0901a4ad0b0031bd4cbf,60236ba48a79d80020f80c9a"]

# RHYTHMONE = ["0", "5f3c92956a93f789ce5346ae", "5d11dc1a34480e001d31fb26,5f5f0901a4ad0b0031bd4cbf,60236ba48a79d80020f80c9a"]

# CRITEO = ["0", "5f3c92f86a93f789ce536fb5", "5d11dc1a34480e001d31fb26,5f5f0901a4ad0b0031bd4cbf,5fb4d7d4595924002d2476fa,5fbdb7c779012500176c9993,60236ba48a79d80020f80c9a,60ee40a8ed26150027a2ad5e,60ee40ecc633f500276a2a70,615fd394fd3b680020464bfb,615fd3d1fd3b680020464c0b"]

# SMAATO = ["0", "5fb6152d6a93f789ce13c441", "5d11dc1a34480e001d31fb26,5f5f0901a4ad0b0031bd4cbf,60236ba48a79d80020f80c9a"]

# UAM = ["0", "5fcdce5a6a93f789ce12a280", "5d11dc1a34480e001d31fb26"]

# TTD1 = ["0", "60dab3e46a93f789ce1e693b", "5d11dc1a34480e001d31fb26,5d8ad6db0fe0f652a82f60d3,5f5f0901a4ad0b0031bd4cbf,5fb4d7d4595924002d2476fa,5fbdb7c779012500176c9993,60236ba48a79d80020f80c9a,615fd394fd3b680020464bfb,615fd3d1fd3b680020464c0b"]
# TTD2 = ["0", "625784b7ac46c9780677dbca", "5f5f0901a4ad0b0031bd4cbf"]

# UNITYADS = ["0", "60eeebc66a93f789ce08615f", "5f5f0901a4ad0b0031bd4cbf,60236ba48a79d80020f80c9a,60ee4190f18654002e152997"]


# # IN_NAVER
# gfa = ["1", "62bab6f6e55c373033e04316,62b40e122a231b2c212d752b,5c5d422538a0a3c9ea897abd,5d809f4f38a0a3c9ea0848ce,5f83e05b6a93f789ceefca28,6214cc826a93f789ce1cafb3"]

# gfd = ["1", "5fbe00906a93f789ce8088ad,5d1ea51838a0a3c9eaf9d22f,5c247be538a0a3c9ea9f72cf,5bfe68a338a0a3c9eaee0dcc"]

# ncc = ["1", "5e3b6ee638a0a3c9ea228e88,5b8e654538a0a3c9ea69459e,5cb6bec938a0a3c9ea9a075d,5b5836acc951958e829dd694,5c3ec00838a0a3c9ea318a30"]

# ndp = ["1", "5d6f14fb38a0a3c9ea4ca07a,5b8e652f38a0a3c9ea694133,5d1085eb38a0a3c9eaaa343d,5d258e8438a0a3c9ea5cc69f,5d258efa38a0a3c9ea5d1ef1"]
