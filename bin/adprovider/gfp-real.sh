#!/bin/bash

source ~/.bashrc

sparkling_home=$1
aggregator=$2
dateTime=$3
execTime=`date '+%Y-%m-%dT%H:%M:%S.%3N'`

echo sparkling_home=${sparkling_home}
echo dateTime=${dateTime}
echo aggregator=${aggregator}
echo execTime=${execTime}

spark-submit --class ${aggregator} \
	--name ${aggregator}-${dateTime}-${execTime} \
	--master yarn --deploy-mode cluster \
	--queue biz_gep \
	--num-executors 12 \
	--executor-cores 3 \
	--executor-memory 1g \
	--conf spark.sql.shuffle.partitions=200 \
	--conf spark.sql.files.maxPartitionBytes=192mb \
	--conf spark.executor.extraJavaOptions=-XX:+UseG1GC \
	--conf "spark.driver.extraJavaOptions=-Dhdp.version=*******-78 -XX:+UseG1GC -Dfile.encoding=utf-8" \
	--conf spark.sql.caseSensitive=true \
	--conf spark.sql.parquet.mergeSchema=true \
	--conf spark.serializer=org.apache.spark.serializer.KryoSerializer \
	--conf spark.eventLog.enabled=true \
	--conf spark.eventLog.dir=hdfs://pgcm/user/gfp-data/spark-history/ \
	--conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
	--conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
	--conf spark.hadoop.dfs.nameservices=pgcm,pg01,pg07 \
	--conf spark.kerberos.access.hadoopFileSystems=hdfs://pgcm,hdfs://pg01,hdfs://pg07 \
	--conf spark.kerberos.keytab=/home1/irteam/apps/c3/gfp-data.keytab \
	--conf spark.kerberos.principal=<EMAIL> \
	--conf spark.sql.warehouse.dir=hdfs://pg07/user/gfp-data/apps/spark/warehouse \
	--conf spark.yarn.am.extraJavaOptions=-Dhdp.version=*******-78 \
	--conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
	--conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
	--conf spark.yarn.archive=hdfs://pg07/user/gfp-data/apps/spark/3.2.4/spark3.2.4_jars.tar.gz \
	--conf spark.yarn.historyServer.address=https://gfp-data--spark-history-server--shs--18080.proxy-pan.c3s.navercorp.com \
	${sparkling_home}/jar/sparkling-s.jar ${dateTime} "${@:4}"
