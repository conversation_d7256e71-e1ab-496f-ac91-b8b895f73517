#!/bin/bash

# [ test ]
# 네이버서비스		5b8f669428b373001fe56ea8

# [ real ]
# 네이버서비스		5b8f669428b373001fe56ea8
# 네이버TV			5f07c40db85575001dd194aa
# 네이버웹툰 		5b8f695b422d910019805eff
# 글로벌커뮤니티	5c5d3e1728a2be037abf0948
# 퍼스널커뮤니티	5bfd00d166de9300257b0bcb
# 오픈커뮤니티		5d9ef168e933ef00170334a1
# 시리즈앱			5e68ad97d7b77c00274b08f6
# SNOW 				5f5f0901a4ad0b0031bd4cbf
# LINEWEBTOON		5d11dc1a34480e001d31fb26
# LINETODAY_TW		5fb4d7d4595924002d2476fa
# NAVER_Z			60236ba48a79d80020f80c9a

# 실행 스크립트
# ./zircon/r/r1_run_zircon_r_gfp_real.sh /home1/irteam/projects/wd-zr/sparkling-s com.navercorp.gfp.biz.zircon.r.ZirconRGfpAggregator manual 20250612 "*" "-"

sparkling_home=$1
aggregator=$2
kind=$3
date_time=$4
exec_time=`date '+%Y-%m-%dT%H:%M:%S.%3N'`

echo sparkling_home="${sparkling_home}"
echo aggregator="${aggregator}"
echo date_time="${date_time}"
echo exec_time="${exec_time}"
echo "${@:3}"

spark-submit --class ${aggregator} \
    --name ${aggregator}-${date_time}-${exec_time} \
    --executor-memory 10g \
    --num-executors 40 \
    --executor-cores 4 \
    --conf spark.executor.memoryOverhead=1g \
	--conf spark.sql.shuffle.partitions=1600 \
	--conf spark.scheduler.mode=FAIR \
	--conf spark.scheduler.allocation.file=hdfs://pg07/user/gfp-data/fairscheduler.xml \
	--conf spark.scheduler.pool=zircon_r_gfp \
    --deploy-mode cluster \
    --master yarn \
    --queue biz_gep \
    --conf spark.eventLog.dir=hdfs://pgcm/user/gfp-data/spark-history/ \
    --conf spark.eventLog.enabled=true \
    --conf spark.executor.extraJavaOptions=-XX:+UseG1GC \
    --conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
    --conf spark.executorEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
    --conf "spark.driver.extraJavaOptions=-Dhdp.version=*******-78 -XX:+UseG1GC -Dfile.encoding=utf-8" \
    --conf spark.hadoop.dfs.nameservices=pgcm,pg01,pg07 \
    --conf spark.kerberos.access.hadoopFileSystems=hdfs://pgcm,hdfs://pg01,hdfs://pg07 \
    --conf spark.kerberos.keytab=/home1/irteam/apps/c3/gfp-data.keytab \
    --conf spark.kerberos.principal=<EMAIL> \
    --conf spark.serializer=org.apache.spark.serializer.KryoSerializer \
    --conf spark.sql.caseSensitive=true \
    --conf spark.sql.parquet.mergeSchema=true \
    --conf spark.sql.warehouse.dir=hdfs://pg07/user/gfp-data/apps/spark/warehouse \
    --conf spark.yarn.am.extraJavaOptions=-Dhdp.version=*******-78 \
    --conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_MOUNTS=/usr/hdp:/usr/hdp:ro \
    --conf spark.yarn.appMasterEnv.YARN_CONTAINER_RUNTIME_DOCKER_IMAGE=reg.c3s.navercorp.com/c3/python3.6-modules:20200922 \
    --conf spark.yarn.archive=hdfs://pg07/user/gfp-data/apps/spark/3.2.4/spark3.2.4_jars.tar.gz \
    --conf spark.yarn.submit.waitAppCompletion=true \
    --conf spark.yarn.historyServer.address=https://gfp-data--spark-history-server--shs--18080.proxy-pan.c3s.navercorp.com \
    "${sparkling_home}"/jar/sparkling-s.jar "${@:3}"
