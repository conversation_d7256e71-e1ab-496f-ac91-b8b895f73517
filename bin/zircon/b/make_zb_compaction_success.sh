#!/bin/bash

# 리얼에서 특정 날짜 원본 데이터를 생성한 뒤, x개월치 복사본을 만들 때 사용함.

# 시작 날짜와 종료 날짜를 설정합니다.
start_date="2024-07-03"
end_date="2024-09-20"

# 시작 날짜부터 종료 날짜까지 하루씩 증가시키면서 반복합니다.
current_date="$start_date"
while [[ "$current_date" < "$end_date" ]] || [[ "$current_date" == "$end_date" ]]; do
	compaction_success_file="/user/gfp-data/zircon/b/warehouse_hh/$(echo $current_date | tr - /)/_COMPACTION_SUCCESS"
	echo $compaction_success_file

	# hadoop fs -touch /user/gfp-data/zircon/b/warehouse_hh/2024/09/23/_COMPACTION_SUCCESS
	hadoop fs -touch "$compaction_success_file"

	# 다음 날짜로 이동
	current_date=$(date -I -d "$current_date + 1 day")
done
