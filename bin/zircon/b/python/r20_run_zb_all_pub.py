"""
모든 매체에 대해서 날짜 범위를 주고 ZB GFP ACCU -> ZB GFP COMP -> ZB ACCU -> ZB COMP 를 모두 수행하는 스크립트
"""
import subprocess
from datetime import datetime, timedelta

import pendulum

import manual_job_dao
from zbconstants import BRANCH, PROFILE

PROFILE_PREFIX = PROFILE.lower()[0]

start_date = datetime(2024, 9, 27, 0, 0, 0)  # inclusive
end_date = datetime(2024, 9, 29, 0, 0, 0)  # inclusive
curr_date = start_date

# [ test ]
# 네이버서비스		5b8f669428b373001fe56ea8

# [ real ]
# 네이버서비스		5b8f669428b373001fe56ea8
# 퍼스널커뮤니티	5bfd00d166de9300257b0bcb
# 오픈커뮤니티		5d9ef168e933ef00170334a1
# SNOW 				5f5f0901a4ad0b0031bd4cbf
# 네이버TV			5f07c40db85575001dd194aa

f = open(f'/home1/irteam/temp/logs/r20.log', 'w')


def run(title: str, ymd: str, args: []):
	start_time = pendulum.now(manual_job_dao.DEFAULT_TZ)

	msg = f'\n[{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] {title} {ymd} ...\n'
	print(msg)
	f.write(msg)
	f.flush()

	# subprocess.check_output(args, encoding='utf-8')
	subprocess.run(args, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.STDOUT, encoding='utf-8')
	# f.write(out)

	elapsed_time = pendulum.now(manual_job_dao.DEFAULT_TZ).diff(start_time)
	msg = f'[{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] {title} {ymd} done. 소요시간 {elapsed_time.in_minutes()}분 {elapsed_time.in_seconds() % 60}초\n'
	print(msg)
	f.write(msg)
	f.flush()


def _get_failed_list(ymd: str, type: str) -> list:
	"""
	실패 작업 수 조회
	:return:
	"""
	filter = {
		'type': type,
		'datetime': ymd,
		'retryCnt': {'$gte': 0}
	}
	failed_list = manual_job_dao.get_jobs(filter)
	return failed_list


def _delete_jobs(ymd: str, type: str) -> list:
	filter = {
		'type': type,
		'datetime': ymd,
	}
	manual_job_dao.delete_jobs(filter)


def _init_retry_cnt(job_ids: []):
	"""
	실패 작업 초기화
	:return:
	"""
	filter = {'_id': {'$in': job_ids}}
	update = {'$set': {'retryCnt': -1}}
	manual_job_dao.update_jobs(filter, update)


def run_zb_accu(ymd: str):
	title = "ZB ACCU"
	type = 'spark_zircon_b'

	_delete_jobs(ymd, type)

	run(title, ymd, [
		'python3',
		f'/home1/irteam/projects/{BRANCH}/sparkling-s/bin/zircon/b/python/r21_run_zircon_b_accu.py',
		ymd,
		"0"
	])

	while True:
		# 실패한 게 하나라도 있으면 break
		failed_list = _get_failed_list(ymd, type)
		if len(failed_list) > 0:
			print(
				f'[{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] {title} {ymd} fail. cnt={len(failed_list)} failedList={failed_list}')

			# retryCnt = -1로 초기화
			job_ids = [doc['_id'] for doc in failed_list]
			_init_retry_cnt(job_ids)

			run(title, ymd, [
				'python3',
				f'/home1/irteam/projects/{BRANCH}/sparkling-s/bin/zircon/b/python/r21_run_zircon_b_accu.py',
				ymd,
				"1"
			])
		else:
			break


def run_zb_comp(ymd: str):
	title = "ZB COMP"
	type = 'spark_zircon_b_compactor'

	_delete_jobs(ymd, type)

	run(title, ymd, [
		'python3',
		f'/home1/irteam/projects/{BRANCH}/sparkling-s/bin/zircon/b/python/r22_run_zircon_b_comp.py',
		ymd,
		"0"
	])

	while True:
		# 실패한 게 하나라도 있으면 break
		failed_list = _get_failed_list(ymd, type)
		if len(failed_list) > 0:
			print(
				f'[{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] {title} {ymd} fail. cnt={len(failed_list)} failedList={failed_list}')

			# retryCnt = -1로 초기화
			job_ids = [doc['_id'] for doc in failed_list]
			_init_retry_cnt(job_ids)

			run(title, ymd, [
				'python3',
				f'/home1/irteam/projects/{BRANCH}/sparkling-s/bin/zircon/b/python/r22_run_zircon_b_comp.py',
				ymd,
				"1"
			])
		else:
			break


try:
	while curr_date <= end_date:
		ymd = curr_date.strftime("%Y%m%d")
		print(f'[{datetime.now().strftime("%Y.%m.%d_%H:%M:%S")}] {ymd} =====================================')

		run("KINIT", ymd, [f'/home1/irteam/support/bin/run_kinit.sh'])

		run_zb_accu(ymd)

		run_zb_comp(ymd)

		curr_date = curr_date + timedelta(days=1)

except Exception as ex:
	print(ex)
	f.write(f"수행하다 에러 발생. {ex}")
finally:
	f.close()
