@echo off
set sparkling_home=%1
set aggregator=%2
set dateTime=%3

set hour=%TIME:~0,2%
if "%hour:~0,1%" == " " set hour=0%hour:~1,1%
set fullTime=%hour%:%TIME:~3,2%:%TIME:~6,2%
set execTime=%DATE%T%fullTime%

echo sparkling_home=%sparkling_home%
echo aggregator=%aggregator%
echo dateTime=%dateTime%
echo execTime=%execTime%

rem [ spark 3.2부터는 디폴트 true ]
rem 	--conf spark.sql.adaptive.enabled=true ^
rem		--conf spark.sql.adaptive.coalescePartitions.enabled=true ^

rem	[ biz cloud test 환경 최대 리소스 ]
rem		mem=50g, core=30

rem	[ specific java version 기술 ]
rem		--conf spark.yarn.dist.archives=%sparkling_home%/jar/jdk-8u331-linux-x64.tar.gz ^
rem		--conf spark.yarn.dist.archives=hdfs://tesseract-dev/user/irteam/gfp/jdk-8u331-linux-x64.tar.gz ^
rem		--conf spark.driverEnv.JAVA_HOME=./jdk-8u331-linux-x64.tar.gz/jdk1.8.0_331 ^
rem		--conf spark.executorEnv.JAVA_HOME=./jdk-8u331-linux-x64.tar.gz/jdk1.8.0_331 ^
rem		--conf spark.yarn.appMasterEnv.JAVA_HOME=./jdk-8u331-linux-x64.tar.gz/jdk1.8.0_331 ^

rem [ default option ]
rem		--class %aggregator% ^
rem 	--name %aggregator%-%dateTime%-%execTime% ^
rem		--deploy-mode cluster ^
rem		--master yarn ^
rem		--queue root.users.gfp ^
rem		--conf spark.driver.extraJavaOptions=-XX:+UseG1GC ^
rem		--conf spark.executor.extraJavaOptions=-XX:+UseG1GC ^
rem		--conf spark.eventLog.enabled=true ^
rem		--conf spark.sql.caseSensitive=true ^
rem		--conf spark.sql.parquet.mergeSchema=true ^
rem		--conf spark.serializer=org.apache.spark.serializer.KryoSerializer ^
rem		--conf spark.yarn.archive=hdfs://tesseract-dev/user/irteam/spark3LibArchives/spark-3.2.1-libs.jar \
rem 	%sparkling_home%/jar/sparkling-s.jar %dateTime%

rem [ fair scheduler ]
rem 	--conf spark.scheduler.mode=FAIR ^
rem 	--conf spark.scheduler.allocation.file=hdfs://tesseract-dev/data/log/gfp/fairscheduler.xml ^
rem 	--conf spark.scheduler.pool=silver_compactor ^

rem	[ variable option ]
rem 	--executor-cores 2 ^
rem 	--num-executors 40 ^
rem 	--executor-memory 1g ^
rem 	--driver-memory 2g ^
rem 	--conf spark.driver.memoryOverhead=2g ^ default=1g
rem 	--conf spark.executor.memoryOverhead=2g ^ default=1g
rem 	--conf spark.sql.shuffle.partitions=800 ^


spark-submit --class %aggregator% ^
	--name %aggregator%-%dateTime%-%execTime% ^
	--executor-cores 4 ^
	--num-executors 8 ^
	--executor-memory 2g ^
	--driver-memory 2g ^
	--conf spark.sql.shuffle.partitions=160 ^
	--deploy-mode cluster ^
	--master yarn ^
	--queue root.users.gfp ^
	--conf spark.driver.extraJavaOptions=-XX:+UseG1GC ^
	--conf spark.executor.extraJavaOptions=-XX:+UseG1GC ^
	--conf spark.eventLog.enabled=true ^
	--conf spark.sql.caseSensitive=true ^
	--conf spark.sql.parquet.mergeSchema=true ^
	--conf spark.serializer=org.apache.spark.serializer.KryoSerializer ^
	--conf spark.yarn.archive=hdfs://tesseract-dev/user/irteam/spark3LibArchives/spark-3.2.1-libs.jar ^
	%sparkling_home%/jar/sparkling-s.jar %dateTime%
