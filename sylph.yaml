Name:           sylph-airflow-scheduler-5487875bf-qwnfk
Namespace:      nam-batch
Priority:       0
Node:           ad1w1412.ncc/************
Start Time:     Fri, 11 Apr 2025 13:43:37 +0900
Labels:         app=airflow
                component=scheduler
                pod-template-hash=5487875bf
                release=sylph-airflow
Annotations:    checksum/secret-config-envs: 49d48696365f3b82f8fec91b13b6aba200018e76845d77e4256323ad44ab09df
                checksum/secret-local-settings: e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
Status:         Running
IP:             **************
IPs:            <none>
Controlled By:  ReplicaSet/sylph-airflow-scheduler-5487875bf
Init Containers:
  install-pip-packages:
    Container ID:  docker://eaca00fa5b6e501d7a28beea0fc6874d9de0263a35409e63494cfccfd224ddf5
    Image:         reg.navercorp.com/gfp/airflow-nam:2.2.5-**********
    Image ID:      docker-pullable://reg.navercorp.com/gfp/airflow-nam@sha256:7490b83976fa30db7ce720869f5abb86a082d5b3cbf1274d4be752c803c9e535
    Port:          <none>
    Host Port:     <none>
    Command:
      /usr/bin/dumb-init
      --
      /entrypoint
    Args:
      bash
      -c
      unset PYTHONUSERBASE && \
      pip install --user "nanoid==2.0.0" "requests-gssapi==1.2.3" "krbticket==1.0.6"  && \
      echo "copying '/home/<USER>/.local/*' to '/opt/home-airflow-local'..." && \
      cp -r /home/<USER>/.local/* /opt/home-airflow-local
      
    State:          Terminated
      Reason:       Completed
      Exit Code:    0
      Started:      Mon, 14 Apr 2025 18:34:59 +0900
      Finished:     Mon, 14 Apr 2025 18:35:15 +0900
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:                4
      ephemeral-storage:  8Gi
      memory:             4Gi
    Requests:
      cpu:                100m
      ephemeral-storage:  8Gi
      memory:             512Mi
    Environment Variables from:
      sylph-airflow-config-envs  Secret  Optional: false
    Environment:
      DATABASE_PASSWORD:               <set to the key 'postgresql-airflow-sylph-password' in secret 'postgresql'>  Optional: false
      REDIS_PASSWORD:                  <set to the key 'password' in secret 'redis'>                                Optional: false
      CONNECTION_CHECK_MAX_COUNT:      0
      AIRFLOW__CORE__FERNET_KEY:       <set to the key 'fernet-key' in secret 'airflow'>            Optional: false
      AIRFLOW__WEBSERVER__SECRET_KEY:  <set to the key 'webserver-secret-key' in secret 'airflow'>  Optional: false
      NCC_CLUSTER_NAME:                ad1
      NCC_CLUSTER_NETWORK:             develop
      NCC_CLUSTER_PHASE:               develop
      TZ:                              Asia/Seoul
    Mounts:
      /opt/home-airflow-local from home-airflow-local (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from default-token-snrhb (ro)
  dags-git-clone:
    Container ID:   docker://883b1fec6a4b6cb71624efbdafae66ed63bedddbce92bc2933fce7b96506975f
    Image:          reg.navercorp.com/gfp/git-sync:3.5.0
    Image ID:       docker-pullable://reg.navercorp.com/gfp/git-sync@sha256:36fde1821837624f17d3d12b55ec6f9d0e899c4d357aaeb5ddc463d9a623864c
    Port:           <none>
    Host Port:      <none>
    State:          Terminated
      Reason:       Completed
      Exit Code:    0
      Started:      Mon, 14 Apr 2025 18:35:17 +0900
      Finished:     Mon, 14 Apr 2025 18:35:17 +0900
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:                1
      ephemeral-storage:  8Gi
      memory:             2Gi
    Requests:
      cpu:                100m
      ephemeral-storage:  8Gi
      memory:             256Mi
    Environment Variables from:
      sylph-airflow-config-envs  Secret  Optional: false
    Environment:
      GIT_SYNC_ONE_TIME:               true
      GIT_SYNC_ROOT:                   /dags
      GIT_SYNC_DEST:                   repo
      GIT_SYNC_REPO:                   *********************:da-ssp/airflow-dags.git
      GIT_SYNC_BRANCH:                 feature-wd-phase3-gfp-392
      GIT_SYNC_REV:                    HEAD
      GIT_SYNC_DEPTH:                  1
      GIT_SYNC_WAIT:                   300
      GIT_SYNC_TIMEOUT:                120
      GIT_SYNC_ADD_USER:               true
      GIT_SYNC_MAX_SYNC_FAILURES:      0
      GIT_SYNC_SSH:                    true
      GIT_SSH_KEY_FILE:                /etc/git-secret/id_rsa
      GIT_KNOWN_HOSTS:                 true
      GIT_SSH_KNOWN_HOSTS_FILE:        /etc/git-secret/known_hosts
      DATABASE_PASSWORD:               <set to the key 'postgresql-airflow-sylph-password' in secret 'postgresql'>  Optional: false
      REDIS_PASSWORD:                  <set to the key 'password' in secret 'redis'>                                Optional: false
      CONNECTION_CHECK_MAX_COUNT:      0
      AIRFLOW__CORE__FERNET_KEY:       <set to the key 'fernet-key' in secret 'airflow'>            Optional: false
      AIRFLOW__WEBSERVER__SECRET_KEY:  <set to the key 'webserver-secret-key' in secret 'airflow'>  Optional: false
      NCC_CLUSTER_NAME:                ad1
      NCC_CLUSTER_NETWORK:             develop
      NCC_CLUSTER_PHASE:               develop
      TZ:                              Asia/Seoul
    Mounts:
      /dags from dags-data (rw)
      /etc/git-secret/id_rsa from git-secret (ro,path="ssh-private")
      /etc/git-secret/known_hosts from git-known-hosts (ro,path="known_hosts")
      /var/run/secrets/kubernetes.io/serviceaccount from default-token-snrhb (ro)
  check-db:
    Container ID:  docker://a961583c02e94fd48595b96e4d0f19cd5fbe72d7c164f3102a93a5fe309809e6
    Image:         reg.navercorp.com/gfp/airflow-nam:2.2.5-**********
    Image ID:      docker-pullable://reg.navercorp.com/gfp/airflow-nam@sha256:7490b83976fa30db7ce720869f5abb86a082d5b3cbf1274d4be752c803c9e535
    Port:          <none>
    Host Port:     <none>
    Command:
      /usr/bin/dumb-init
      --
      /entrypoint
    Args:
      bash
      -c
      exec timeout 60s airflow db check
    State:          Terminated
      Reason:       Completed
      Exit Code:    0
      Started:      Mon, 14 Apr 2025 18:35:18 +0900
      Finished:     Mon, 14 Apr 2025 18:35:26 +0900
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:                4
      ephemeral-storage:  8Gi
      memory:             4Gi
    Requests:
      cpu:                100m
      ephemeral-storage:  8Gi
      memory:             512Mi
    Environment Variables from:
      sylph-airflow-config-envs  Secret  Optional: false
    Environment:
      DATABASE_PASSWORD:               <set to the key 'postgresql-airflow-sylph-password' in secret 'postgresql'>  Optional: false
      REDIS_PASSWORD:                  <set to the key 'password' in secret 'redis'>                                Optional: false
      CONNECTION_CHECK_MAX_COUNT:      0
      AIRFLOW__CORE__FERNET_KEY:       <set to the key 'fernet-key' in secret 'airflow'>            Optional: false
      AIRFLOW__WEBSERVER__SECRET_KEY:  <set to the key 'webserver-secret-key' in secret 'airflow'>  Optional: false
      NCC_CLUSTER_NAME:                ad1
      NCC_CLUSTER_NETWORK:             develop
      NCC_CLUSTER_PHASE:               develop
      TZ:                              Asia/Seoul
    Mounts:
      /home/<USER>/.local from home-airflow-local (rw)
      /opt/airflow/dags from dags-data (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from default-token-snrhb (ro)
  wait-for-db-migrations:
    Container ID:  docker://efa5c16e90dde636b64b8fc63fc839c49ad04b9bc3bff8ec60283a5e91e7d90c
    Image:         reg.navercorp.com/gfp/airflow-nam:2.2.5-**********
    Image ID:      docker-pullable://reg.navercorp.com/gfp/airflow-nam@sha256:7490b83976fa30db7ce720869f5abb86a082d5b3cbf1274d4be752c803c9e535
    Port:          <none>
    Host Port:     <none>
    Command:
      /usr/bin/dumb-init
      --
      /entrypoint
    Args:
      bash
      -c
      exec airflow db check-migrations -t 60
    State:          Terminated
      Reason:       Completed
      Exit Code:    0
      Started:      Mon, 14 Apr 2025 18:35:27 +0900
      Finished:     Mon, 14 Apr 2025 18:35:36 +0900
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:                4
      ephemeral-storage:  8Gi
      memory:             4Gi
    Requests:
      cpu:                100m
      ephemeral-storage:  8Gi
      memory:             512Mi
    Environment Variables from:
      sylph-airflow-config-envs  Secret  Optional: false
    Environment:
      DATABASE_PASSWORD:               <set to the key 'postgresql-airflow-sylph-password' in secret 'postgresql'>  Optional: false
      REDIS_PASSWORD:                  <set to the key 'password' in secret 'redis'>                                Optional: false
      CONNECTION_CHECK_MAX_COUNT:      0
      AIRFLOW__CORE__FERNET_KEY:       <set to the key 'fernet-key' in secret 'airflow'>            Optional: false
      AIRFLOW__WEBSERVER__SECRET_KEY:  <set to the key 'webserver-secret-key' in secret 'airflow'>  Optional: false
      NCC_CLUSTER_NAME:                ad1
      NCC_CLUSTER_NETWORK:             develop
      NCC_CLUSTER_PHASE:               develop
      TZ:                              Asia/Seoul
    Mounts:
      /home/<USER>/.local from home-airflow-local (rw)
      /opt/airflow/dags from dags-data (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from default-token-snrhb (ro)
Containers:
  airflow-scheduler:
    Container ID:  docker://88c59b5fdb0a71ca5e21e752ffef469fbda0812d1929328587b3655e9fe398db
    Image:         reg.navercorp.com/gfp/airflow-nam:2.2.5-**********
    Image ID:      docker-pullable://reg.navercorp.com/gfp/airflow-nam@sha256:7490b83976fa30db7ce720869f5abb86a082d5b3cbf1274d4be752c803c9e535
    Port:          <none>
    Host Port:     <none>
    Command:
      /usr/bin/dumb-init
      --
      /entrypoint
    Args:
      bash
      -c
      exec airflow scheduler -n -1
    State:          Running
      Started:      Mon, 14 Apr 2025 18:35:38 +0900
    Ready:          True
    Restart Count:  6
    Limits:
      cpu:                4
      ephemeral-storage:  16Gi
      memory:             4Gi
      ncc/type.any:       1
    Requests:
      cpu:                100m
      ephemeral-storage:  16Gi
      memory:             512Mi
      ncc/type.any:       1
    Liveness:             exec [python -Wignore -c import sys
from typing import List
from airflow.jobs.scheduler_job import SchedulerJob
from airflow.utils.db import create_session
from airflow.utils.net import get_hostname
from airflow.utils.state import State

with create_session() as session:
    hostname = get_hostname()
    query = session \
        .query(SchedulerJob) \
        .filter_by(state=State.RUNNING, hostname=hostname) \
        .order_by(SchedulerJob.latest_heartbeat.desc())
    jobs: List[SchedulerJob] = query.all()
    alive_jobs = [job for job in jobs if job.is_alive()]
    count_alive_jobs = len(alive_jobs)

if count_alive_jobs == 1:
    # scheduler is healthy - we expect one SchedulerJob per scheduler
    pass
elif count_alive_jobs == 0:
    sys.exit(f"UNHEALTHY - 0 alive SchedulerJob for: {hostname}")
else:
    sys.exit(f"UNHEALTHY - {count_alive_jobs} (more than 1) alive SchedulerJob for: {hostname}")
] delay=10s timeout=60s period=30s #success=1 #failure=5
    Environment Variables from:
      sylph-airflow-config-envs  Secret  Optional: false
    Environment:
      DATABASE_PASSWORD:               <set to the key 'postgresql-airflow-sylph-password' in secret 'postgresql'>  Optional: false
      REDIS_PASSWORD:                  <set to the key 'password' in secret 'redis'>                                Optional: false
      CONNECTION_CHECK_MAX_COUNT:      0
      AIRFLOW__CORE__FERNET_KEY:       <set to the key 'fernet-key' in secret 'airflow'>            Optional: false
      AIRFLOW__WEBSERVER__SECRET_KEY:  <set to the key 'webserver-secret-key' in secret 'airflow'>  Optional: false
      NCC_CLUSTER_NAME:                ad1
      NCC_CLUSTER_NETWORK:             develop
      NCC_CLUSTER_PHASE:               develop
      TZ:                              Asia/Seoul
    Mounts:
      /home/<USER>/.local from home-airflow-local (rw)
      /opt/airflow/dags from dags-data (rw)
      /var/run/secrets/kubernetes.io/serviceaccount from default-token-snrhb (ro)
  dags-git-sync:
    Container ID:   docker://967149b94807d3b19737df803714ecfa56b37f9e0e2599d17eeebf5eb16f2994
    Image:          reg.navercorp.com/gfp/git-sync:3.5.0
    Image ID:       docker-pullable://reg.navercorp.com/gfp/git-sync@sha256:36fde1821837624f17d3d12b55ec6f9d0e899c4d357aaeb5ddc463d9a623864c
    Port:           <none>
    Host Port:      <none>
    State:          Running
      Started:      Fri, 11 Apr 2025 13:44:16 +0900
    Ready:          True
    Restart Count:  0
    Limits:
      cpu:                1
      ephemeral-storage:  8Gi
      memory:             2Gi
    Requests:
      cpu:                100m
      ephemeral-storage:  8Gi
      memory:             256Mi
    Environment Variables from:
      sylph-airflow-config-envs  Secret  Optional: false
    Environment:
      GIT_SYNC_ROOT:                   /dags
      GIT_SYNC_DEST:                   repo
      GIT_SYNC_REPO:                   *********************:da-ssp/airflow-dags.git
      GIT_SYNC_BRANCH:                 feature-wd-phase3-gfp-392
      GIT_SYNC_REV:                    HEAD
      GIT_SYNC_DEPTH:                  1
      GIT_SYNC_WAIT:                   300
      GIT_SYNC_TIMEOUT:                120
      GIT_SYNC_ADD_USER:               true
      GIT_SYNC_MAX_SYNC_FAILURES:      0
      GIT_SYNC_SSH:                    true
      GIT_SSH_KEY_FILE:                /etc/git-secret/id_rsa
      GIT_KNOWN_HOSTS:                 true
      GIT_SSH_KNOWN_HOSTS_FILE:        /etc/git-secret/known_hosts
      DATABASE_PASSWORD:               <set to the key 'postgresql-airflow-sylph-password' in secret 'postgresql'>  Optional: false
      REDIS_PASSWORD:                  <set to the key 'password' in secret 'redis'>                                Optional: false
      CONNECTION_CHECK_MAX_COUNT:      0
      AIRFLOW__CORE__FERNET_KEY:       <set to the key 'fernet-key' in secret 'airflow'>            Optional: false
      AIRFLOW__WEBSERVER__SECRET_KEY:  <set to the key 'webserver-secret-key' in secret 'airflow'>  Optional: false
      NCC_CLUSTER_NAME:                ad1
      NCC_CLUSTER_NETWORK:             develop
      NCC_CLUSTER_PHASE:               develop
      TZ:                              Asia/Seoul
    Mounts:
      /dags from dags-data (rw)
      /etc/git-secret/id_rsa from git-secret (ro,path="ssh-private")
      /etc/git-secret/known_hosts from git-known-hosts (ro,path="known_hosts")
      /var/run/secrets/kubernetes.io/serviceaccount from default-token-snrhb (ro)
Conditions:
  Type              Status
  Initialized       True 
  Ready             True 
  ContainersReady   True 
  PodScheduled      True 
Volumes:
  dags-data:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     
    SizeLimit:  <unset>
  git-secret:
    Type:        Secret (a volume populated by a Secret)
    SecretName:  airflow-dags-deploy
    Optional:    false
  git-known-hosts:
    Type:        Secret (a volume populated by a Secret)
    SecretName:  sylph-airflow-known-hosts
    Optional:    false
  home-airflow-local:
    Type:       EmptyDir (a temporary directory that shares a pod's lifetime)
    Medium:     
    SizeLimit:  <unset>
  default-token-snrhb:
    Type:        Secret (a volume populated by a Secret)
    SecretName:  default-token-snrhb
    Optional:    false
QoS Class:       Burstable
Node-Selectors:  <none>
Tolerations:     node.kubernetes.io/not-ready:NoExecute for 300s
                 node.kubernetes.io/unreachable:NoExecute for 300s
Events:
  Type    Reason   Age                 From                   Message
  ----    ------   ----                ----                   -------
  Normal  Created  20m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Created container install-pip-packages
  Normal  Started  20m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Started container install-pip-packages
  Normal  Pulled   20m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Container image "reg.navercorp.com/gfp/airflow-nam:2.2.5-**********" already present on machine
  Normal  Pulled   20m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Container image "reg.navercorp.com/gfp/git-sync:3.5.0" already present on machine
  Normal  Created  20m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Created container dags-git-clone
  Normal  Pulled   19m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Container image "reg.navercorp.com/gfp/airflow-nam:2.2.5-**********" already present on machine
  Normal  Started  19m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Started container dags-git-clone
  Normal  Started  19m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Started container check-db
  Normal  Created  19m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Created container check-db
  Normal  Pulled   19m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Container image "reg.navercorp.com/gfp/airflow-nam:2.2.5-**********" already present on machine
  Normal  Created  19m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Created container wait-for-db-migrations
  Normal  Started  19m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Started container wait-for-db-migrations
  Normal  Pulled   19m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Container image "reg.navercorp.com/gfp/airflow-nam:2.2.5-**********" already present on machine
  Normal  Created  19m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Created container airflow-scheduler
  Normal  Started  19m (x6 over 2d5h)  kubelet, ad1w1412.ncc  Started container airflow-scheduler
