package nativeadstyle

import (
	"context"
	"time"

	"github.com/araddon/dateparse"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readconcern"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	"oss.navercorp.com/da-ssp/gfp-api/database"

	gfpError "oss.navercorp.com/da-ssp/gfp-api/error"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
)

// NativeAdStyle 네이티브 광고스타일
type NativeAdStyle struct {
	ID                          string             `json:"id" bson:"id"`
	Name                        string             `json:"name" bson:"name"`
	NativeAdStyleType           string             `json:"nativeAdStyleType" bson:"nativeAdStyleType"`
	NativeAdStyleCategory       string             `json:"nativeAdStyleCategory" bson:"nativeAdStyleCategory"`
	NativeAssetCoverage         string             `json:"nativeAssetCoverage" bson:"nativeAssetCoverage"`
	WebSupport                  int                `json:"webSupport" bson:"webSupport"`
	NativeAssets                *[]NativeAssetInfo `json:"nativeAssets,omitempty" bson:"nativeAssets"`
	MinAllowableAdResponseCount int                `json:"minAllowableAdResponseCount" bson:"minAllowableAdResponseCount"`
	MaxAllowableAdResponseCount int                `json:"maxAllowableAdResponseCount" bson:"maxAllowableAdResponseCount"`
	CompositeInfo               *CompositeInfo     `json:"compositeInfo" bson:"compositeInfo"`

	ModifiedAt time.Time `json:"modifiedAt" bson:"modifiedAt"`
}

// NativeAssetInfo 네이티브 에셋 info
type NativeAssetInfo struct {
	Required      int                `json:"required" bson:"required"`
	NativeAssetID primitive.ObjectID `json:"-" bson:"nativeAsset_id"`
	NativeAsset   NativeAsset        `json:"nativeAsset" bson:"nativeAsset"`
}

// NativeAsset 네이티브 에셋
type NativeAsset struct {
	NativeAssetID       primitive.ObjectID `json:"-" bson:"_id"`
	ID                  int                `json:"id" bson:"id"`
	Name                string             `json:"name" bson:"name"`
	NativeAssetType     string             `json:"nativeAssetType" bson:"nativeAssetType"`
	NativeAssetCategory string             `json:"nativeAssetCategory" bson:"nativeAssetCategory"`
	Fields              *[]Field           `json:"fields" bson:"fields"`
	Clickable           int                `json:"clickable" bson:"clickable"`
}

// Field 에셋필드
type Field struct {
	Key   string `json:"key" bson:"key"`
	Value string `json:"value" bson:"value"`
}

// CompositeInfo 복합 광고스타일
type CompositeInfo struct {
	NativeAdStyles *[]NativeAdStyle `json:"nativeAdStyles" bson:"nativeAdStyles"`
}

type NativeAdStyleService struct{}

// List 광고스타일 리스트 조회
func (service *NativeAdStyleService) List(ginCtx *gin.Context, adStyleParam NativeAdStyleParam) (adStyles []NativeAdStyle, err *gfpError.GfpApiError) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[List] 광고스타일 리스트 조회")

	// 광고스타일 리스트 조회
	if adStyles, err = service.getAdStyles(ginCtx, adStyleParam); err != nil {
		return nil, err
	}

	if len(adStyles) == 0 {
		entry.Debug("[List] 광고스타일 내역 없음")
		return
	}

	// 에셋 리스트 조회
	assets, err := service.getAssets(ginCtx, adStyles)
	if err != nil {
		return nil, err
	}

	// 광고스타일에 에셋 상세 정보 추가
	if err = service.setAssets(ginCtx, &adStyles, &assets); err != nil {
		return nil, err
	}

	return
}

/*
	광고스타일 리스트 조회
*/
func (service *NativeAdStyleService) getAdStyles(ginCtx *gin.Context, adStyleParam NativeAdStyleParam) (adStyles []NativeAdStyle, err *gfpError.GfpApiError) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[getAdStyles] 광고스타일 리스트 조회")

	// 1. DB 파이프라인 설정
	matchQuery := bson.M{}

	if adStyleParam.ID != "" {
		matchQuery["id"] = adStyleParam.ID
	}

	if adStyleParam.Since != "" {
		modifiedAt, _ := dateparse.ParseAny(adStyleParam.Since)

		matchQuery["modifiedAt"] = bson.M{"$gte": modifiedAt}
	}

	matchQuery["nativeAssetCoverage"] = primitive.Regex{Pattern: ".*" + adStyleParam.NativeAssetCoverage + ".*", Options: "i"}

	pipeline := mongo.Pipeline{
		{{"$match", matchQuery}},
		{{"$lookup", bson.M{
			"from":         "NativeAdStyles",
			"localField":   "compositeInfo.nativeAdStyle_ids",
			"foreignField": "_id",
			"as":           "compositeInfo.nativeAdStyles"},
		}},
		{{"$project", bson.M{
			"id":                          1,
			"name":                        1,
			"nativeAdStyleType":           1,
			"nativeAdStyleCategory":       1,
			"nativeAssetCoverage":         1,
			"webSupport":                  1,
			"nativeAssets":                1,
			"minAllowableAdResponseCount": 1,
			"maxAllowableAdResponseCount": 1,
			"compositeInfo":               1,
			"modifiedAt":                  1},
		}},
		{{"$sort", bson.M{"modifiedAt": -1}}},
	}

	// 2. Aggregation 옵션 설정
	opts := options.Aggregate()
	opts.SetAllowDiskUse(true)

	// 3. timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// 4. Aggregation 실행
	collOptions := &options.CollectionOptions{}
	collOptions.SetReadConcern(readconcern.Majority())
	collOptions.SetReadPreference(readpref.SecondaryPreferred(readpref.WithMaxStaleness(time.Second * 90)))

	collection := database.GFP.Collection("NativeAdStyles", collOptions)
	cur, e := collection.Aggregate(ctx, pipeline, opts)
	if e != nil {
		entry.Error(e)

		param := map[string]string{"err": e.Error()}
		err = gfpError.NativeAdStyleCommon.BizError(param)

		return
	}
	defer cur.Close(ctx)

	// 5. 조회 결과 가져오기
	for cur.Next(ctx) {
		var elem NativeAdStyle

		e = cur.Decode(&elem)
		if e != nil {
			entry.Error(e)

			param := map[string]string{"err": e.Error()}
			err = gfpError.NativeAdStyleCommon.BizError(param)

			return
		}

		adStyles = append(adStyles, elem)
	}

	if e = cur.Err(); e != nil {
		entry.Error(e)

		param := map[string]string{"err": e.Error()}
		err = gfpError.NativeAdStyleCommon.BizError(param)
	}

	// entry.Debugf("adStyles ::: %+v\n", adStyles)

	return
}

/*
	에셋 리스트 조회
*/
func (service *NativeAdStyleService) getAssets(ginCtx *gin.Context, adStyles []NativeAdStyle) (assets []NativeAsset, err *gfpError.GfpApiError) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[getAssets] 에셋 리스트 조회")

	// 0. adStyles 에서 assetIds 추출하기
	assetIds, err := getAssetIds(adStyles)

	// 1. DB 파이프라인 설정
	pipeline := mongo.Pipeline{
		{{"$match", bson.M{"_id": bson.M{"$in": assetIds}}}},
		{{"$lookup", bson.M{
			"from":         "NativeAssetTypes",
			"localField":   "nativeAssetType_id",
			"foreignField": "_id",
			"as":           "nativeAssetType"},
		}},
		{{"$lookup", bson.M{
			"from":         "NativeAssetCategories",
			"localField":   "nativeAssetType.nativeAssetCategory_id",
			"foreignField": "_id",
			"as":           "nativeAssetCategory"},
		}},
		{{"$unwind", bson.M{"path": "$nativeAssetType", "preserveNullAndEmptyArrays": false}}},
		{{"$unwind", bson.M{"path": "$nativeAssetCategory", "preserveNullAndEmptyArrays": false}}},
		{{"$project", bson.M{
			"_id":                 1,
			"id":                  1,
			"name":                1,
			"nativeAssetType":     "$nativeAssetType.key",
			"nativeAssetCategory": "$nativeAssetCategory.key",
			"fields":              1,
			"clickable":           1},
		}},
	}

	// 2. Aggregation 옵션 설정
	opts := options.Aggregate()
	opts.SetAllowDiskUse(true)

	// 3. timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// 4. Aggregation 실행
	collOptions := &options.CollectionOptions{}
	collOptions.SetReadConcern(readconcern.Majority())
	collOptions.SetReadPreference(readpref.SecondaryPreferred(readpref.WithMaxStaleness(time.Second * 90)))

	collection := database.GFP.Collection("NativeAssets", collOptions)
	cur, e := collection.Aggregate(ctx, pipeline, opts)
	if e != nil {
		entry.Error(e)

		param := map[string]string{"err": e.Error()}
		err = gfpError.NativeAdStyleCommon.BizError(param)

		return
	}
	defer cur.Close(ctx)

	// 5. 조회 결과 가져오기
	for cur.Next(ctx) {
		var elem NativeAsset

		e = cur.Decode(&elem)
		if e != nil {
			entry.Error(e)

			param := map[string]string{"err": e.Error()}
			err = gfpError.NativeAdStyleCommon.BizError(param)

			return
		}

		assets = append(assets, elem)
	}

	if e = cur.Err(); e != nil {
		entry.Error(e)

		param := map[string]string{"err": e.Error()}
		err = gfpError.NativeAdStyleCommon.BizError(param)
	}

	// entry.Debugf("assets ::: %+v\n", assets)

	return
}

/*
	assetIds 추출
*/
func getAssetIds(adStyles []NativeAdStyle) (assetIds []primitive.ObjectID, err *gfpError.GfpApiError) {
	ids := make(map[primitive.ObjectID]struct{})

	// 모든 에셋 id 추출
	for _, adStyle := range adStyles {
		// 단일인 경우, nativeAssets 에서 추출
		if adStyle.NativeAdStyleType == "SINGLE" {
			for _, asset := range *adStyle.NativeAssets {
				ids[asset.NativeAssetID] = struct{}{}
			}
		}

		// 복합인 경우, compositeInfo.nativeAdStyles.nativeAssets 에서 추출
		if adStyle.NativeAdStyleType == "COMPOSITE" {
			for _, adStyle := range *adStyle.CompositeInfo.NativeAdStyles {
				for _, asset := range *adStyle.NativeAssets {
					ids[asset.NativeAssetID] = struct{}{}
				}
			}
		}
	}

	for id := range ids {
		assetIds = append(assetIds, id)
	}

	return
}

/*
	광고스타일에 에셋 상세 정보 추가
*/
func (service *NativeAdStyleService) setAssets(ginCtx *gin.Context, adStyles *[]NativeAdStyle, assets *[]NativeAsset) (err *gfpError.GfpApiError) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[setAssets] 광고스타일에 에셋 상세 정보 추가")

	// 에셋 정보 셋팅
	for _, adStyle := range *adStyles {
		// [단일] nativeAssets
		if adStyle.NativeAdStyleType == "SINGLE" {
			for index, asset := range *adStyle.NativeAssets {
				(*adStyle.NativeAssets)[index].NativeAsset = getAsset(assets, asset.NativeAssetID)
			}
		}

		// [복합] compositeInfo.nativeAdStyles.nativeAssets
		if adStyle.NativeAdStyleType == "COMPOSITE" {
			for _, adStyle := range *adStyle.CompositeInfo.NativeAdStyles {
				for index, asset := range *adStyle.NativeAssets {
					(*adStyle.NativeAssets)[index].NativeAsset = getAsset(assets, asset.NativeAssetID)
				}
			}
		}
	}

	return
}

/*
	asset 필터
*/
func getAsset(assets *[]NativeAsset, assetId primitive.ObjectID) (asset NativeAsset) {
	for _, asset = range *assets {
		if asset.NativeAssetID == assetId {
			return
		}
	}

	return
}
