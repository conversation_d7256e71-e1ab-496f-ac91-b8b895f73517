package nativeadstyle

import (
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"net/http"

	gfpError "oss.navercorp.com/da-ssp/gfp-api/error"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
)

type NativeAdStyleParam struct {
	ID                  string `form:"id" binding:"omitempty"`
	NativeAssetCoverage string `form:"nativeAssetCoverage" binding:"omitempty"`
	Since               string `form:"since" binding:"omitempty,validateDateFormat"`
}

type NativeAdStyleController struct{}

var empty = make([]interface{}, 0)

/**
 	@apiVersion 1.0.0
	@api {get} /native/adstyle/list NativeAdStyle 목록 조회
	@apiName List
	@apiGroup NativeAdStyle
	@apiDescription NativeAdStyle 목록 조회

	@apiParam {String} userId API User ID<br/>ex) "admin"
	@apiParam {String{64}} encodedUserId Encoded API User ID<br/>ex) "1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1"
	@apiParam {String} id Native AdStyle Id <br/>ex) 10011
	@apiParam {String} nativeAssetCoverage 에셋 범위 (NAVER, LINE, ORTB)
	@apiParam {String} since 시작일자<br/>ex) 2019-08-01 01:02:03 또는 2019-08-01T01:02:03.000Z

	@apiParamExample {form} Request-Example:
		http://test-api-gfp.da.navercorp.com:8080/native/adstyle/list?userId=admin&encodedUserId=1eb9b92526bfd49002af2cc84598df2e0c2f60e6b2d24cffbffe674da58d47d1&id=10011&nativeAssetCoverage=NAVER&since=2019-08-01T01:02:03.000Z

	@apiSuccess {json} array
	@apiSuccessExample {json} Success-Response-Example: publisher 목록
		HTTP/1.1 200 OK
		[
			{
				{
					"id": "10012",
					"name": "native_basic_mail",
					"nativeAdStyleType": "SINGLE",
					"nativeAdStyleCategory": "BANNER",
					"nativeAssetCoverage": "NAVER",
					"webSupport": 1,
					"nativeAssets": [
						{
							"required": 1,
							"nativeAsset": {
								"id": 20001,
								"name": "Naver Title_20",
								"nativeAssetType": "title",
								"nativeAssetCategory": "title",
								"fields": [
									{
										"key": "len",
										"value": "20"
									}
								],
								"clickable": 1
							}
						},
						...
					],
					"minAllowableAdResponseCount": 1,
					"maxAllowableAdResponseCount": 1,
					"modifiedAt": "2021-07-28T06:52:19.617Z"
				}
			},
			...
		]

	@apiError (400) BadRequest
						Since가 날짜 형식이 아닌 경우<br>
	@apiError (403) Forbidden Not authenticated. You need to register server IP in the pasta acl consumer.
	@apiError (500) InternalServerError
						DB 조회 오류<br/>

	@apiErrorExample {json} Error-Response-Example: BadReqeust
		HTTP/1.1 400 Bad Request
		{
			"Status": 400,
			"Code": "2011",
			"Message": "invalid param. (Key: 'NativeAdStyleParam.Since' Error:Field validation for 'Since' failed on the 'validateDateFormat' tag)"
		}
	@apiErrorExample {json} Error-Response-Example: Forbidden
		HTTP/1.1 403 Forbidden
	@apiErrorExample {json} Error-Response-Example: InternalServerError
		HTTP/1.1 500 Internal Server Error
		{
			"Status": 500,
			"Code": "1000",
			"Message": "runtime error: invalid memory address or nil pointer dereference"
		}

	@apiSampleRequest /native/adstyle/list
*/
func (controller *NativeAdStyleController) List(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debug("[List] 광고스타일 리스트 조회")

	// 쿼리스트링 바인딩 및 밸리데이션 체크
	var adStyleParam NativeAdStyleParam
	if err := ginCtx.ShouldBindWith(&adStyleParam, binding.Query); err != nil {
		param := map[string]string{"params": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.InValidParam.BizError(param))
		return
	}

	adstlyes, err := adStyleService.List(ginCtx, adStyleParam)
	if err != nil {
		param := map[string]string{"err": err.Error()}
		gfpError.ResponseError(ginCtx, gfpError.NativeAdStyleCommon.BizError(param))
		return
	}

	if len(adstlyes) == 0 {
		ginCtx.IndentedJSON(http.StatusOK, empty)
		return
	}

	ginCtx.IndentedJSON(http.StatusOK, adstlyes)
}
