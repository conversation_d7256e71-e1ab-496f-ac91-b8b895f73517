/**
nClavis API 가이드 ( nClavis API Guide )	https://yobi.navercorp.com/nClavis_guide/posts/5

용어 소개
	DataEncryptionKey ( DEK )
		사용자의 정보 보호를 위해 민감한 개인정보 등을 암호화하여 저장하는데 사용하는 대칭키
		클라이언트 프로젝트에서 소유하는 대칭키, 클라이언트 프로젝트에서 데이터를 암호화하여 DB에 저장하기 위해 사용하는 대칭키

		예) 우리 쪽의 API KEY

	KeyEncryptionKey ( KEK )
		nClavis v3에서 DEK를 보호하기 위해 사용하는 대칭키
		nClavis v3에서 저장하는 대칭키, 클라이언트 프로젝트 담당자가 DEK를 암호화하기 위해 nClavis v3에서 생성하는 대칭키

		예) https://dev.nclavis.navercorp.com/services/249/kek/jF7QhngykYISI1fkV30aLao0JNk=

	WrappedDataEncryptKey ( WDEK )
		클라이언트 프로젝트의 DEK를 nClavis v3의 Wrap API를 통해 암호화한 결과
		클라이언트 프로젝트에서는 WDEK 보관하고, DEK 필요시 nClavis v3 UnWrap API를 통해 복호화된 DEK를 획득

		예) 우리 쪽의 API KEY를 Nclavis enctry API(/kms/consumer/{kekResourceId}/encrypt/inline)를 통해 암호화한 결과.
		    몽고디비 컬렉션 Nclavis.wdek

*/
package triplea

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"io/ioutil"
	"net/http"
	"oss.navercorp.com/da-ssp/gfp-api/database"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
	"time"

	"oss.navercorp.com/da-ssp/gfp-api/config"
)

type NClavisDoc struct {
	Id         primitive.ObjectID `bson:"_id,omitempty"`
	UserId     string             `bson:"userId"`
	Wdek       string             `bson:"wdek"`
	CreatedAt  time.Time          `bson:"createdAt"`
	ModifiedAt time.Time          `bson:"modifiedAt"`
}

type DecryptReqBody struct {
	InlineCipherText string `json:"inlineCipherText"`
}

type DecryptResBody struct {
	PlainText string `json:"plainText"`
}

type Nclavis struct {
}

var (
	client     *http.Client
	nClavisUrl string
	log        = logger.GetLogger("default")
)

func init() {
	// 환경설정
	url := config.GetConfig("nclavis.url").(string)
	nClavisUrl = url
	log.Debugf("nClavisUrl: %s", nClavisUrl)

	// http client 할당
	// 검색어 : "golang https x509 certificate signed by unknown authority"
	// 해결 : https://github.com/andygrunwald/go-jira/issues/52
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client = &http.Client{Transport: tr}
}

func (nclavis *Nclavis) GetApiKey(ginCtx *gin.Context, userId string) (apiKey string, err error) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	wdek := getWdek(ginCtx, userId) // DB에서 userId에 해당하는 wdek 가져오기

	// nclavis로의 요청 body 만들기
	/*
		{
			"inlineCipherText": "1:l2-2J69nLzVwLsGw:q6r-ts_48VKC68MbCsor_-Fx5sIUORMvgopacgbUO_PY9DR9IokQKWWIMYs4P8jZBJeFCSG21oIH-N1g3U9EC7gEz09-xvu7bC6447aroaI="
		}
	*/
	decryptReqBody := DecryptReqBody{wdek}
	jsonBytes, err := json.Marshal(&decryptReqBody)
	if err != nil {
		return
	}

	// nclavis로 wdek에 대한 복호화 요청
	myurl := nClavisUrl + "/decrypt/inline"
	reqBody := bytes.NewReader(jsonBytes)
	req, err := http.NewRequest(http.MethodPost, myurl, reqBody)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json;charset=UTF-8")

	// https://oss.navercorp.com/da-ssp/bts/issues/1042
	// go는 request를 재사용하므로 EOF 나는 경우 이를 false로 놓고 씀. https://stackoverflow.com/a/34474535
	req.Close = true

	// Token-Based 인증 설정 https://yobi.navercorp.com/nClavis_guide/posts/4#yb-header--token-based-%EC%9D%B8%EC%A6%9D-%EC%84%A4%EC%A0%95
	if config.GetEnv() == "local" {
		// NEOID USER TOKEN은 유효기간이 있으므로 만료되면 다시 발급해야 함.
		req.Header.Set("nclavis-access-token", "n+i/2iONEmmbQ7J/kEJW3DFLTjncZ+PvIkaQ0gJZ8Z2/3jjkXHP1afbywjzSr9WQN1YSIlB21XI+DEml1GMHgJ+XfRMO/D/2I8rm6X+DFbj+jI/9+SboWyu9BAdQ772G4IS1KJN9br5oCAhiLT13RU6jj4pli6O0hYP0vIhlF6XifQNDuR8e4wRC69yh5hHUzqHA0LHHtMD1jXdEMUAldys0vM/2N/NV6HDOWIBtd5cOR6SccJIxteB0OYIPVjeK98QAdRr9uDZkJzGCvjAsXGpn5zkOPpJHNo5fG0gVDFnghnjUYRuqGq4NI4TYp/vWE310fQhUeDqIbEjIfLI1v+FQPGy38AN7oQ/TE22UpJHdjW69rN0Q+Aj2RX5LXt0h")
	}

	//entry.Debugf("---------------------- json:%s", string(jsonBytes))
	//entry.Debugf("---------------------- url:%s", myurl)
	//entry.Debugf("---------------------- header %v", req.Header)
	//entry.Debugf("---------------------- nclavis-access-token:%s", req.Header.Get("nclavis-access-token"))
	//entry.Debugf("---------------------- reqBody:%s", reqBody)

	if err != nil {
		panic(err.Error())
	}

	var decryptResBody DecryptResBody
	resp, err := client.Do(req)
	if err != nil {
		entry.Errorf("[N-CLAVIS] request fail. error:%v", err)
	} else {
		defer resp.Body.Close()

		switch resp.StatusCode {
		case 200:
			bodyBytes, _ := ioutil.ReadAll(resp.Body)

			// 구조체로 매핑
			/*
				{
				  "plainText": "2dce505d96a53c5768052ee90f3df2055657518dad489160df9913f66042e160"
				}
			*/
			err := json.Unmarshal(bodyBytes, &decryptResBody)
			if err != nil {
				panic(err.Error())
			}

		default:
			entry.Errorf("An error has occurred. statusCode: %d(%s)", resp.StatusCode, http.StatusText(resp.StatusCode))
		}
	}

	apiKey = decryptResBody.PlainText

	return
}

func getWdek(ginCtx *gin.Context, userId string) string {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	var nclavisDoc NClavisDoc
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	collection := database.GFP.Collection("Nclavis")

	filter := bson.M{"userId": "gfp.api.user." + userId}

	findOptions := options.FindOneOptions{}
	findOptions.SetProjection(bson.M{"wdek": 1})

	err := collection.FindOne(ctx, filter, &findOptions).Decode(&nclavisDoc)
	if err != nil {
		entry.Errorf("[N-CLAVIS] apiUser: %v에 대한 wdek 조회 실패. %v", userId, err)
		return ""
	}
	//entry.Debugf("--------------------------------------apiUser: %v에 대한 wdek: %s", userId, nclavisDoc.Wdek)

	return nclavisDoc.Wdek
}
