/**
AAA (Triple A) - Accounting, Authentication, Authorization
*/
package triplea

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gobwas/glob"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"oss.navercorp.com/da-ssp/gfp-api/database"
	gfpError "oss.navercorp.com/da-ssp/gfp-api/error"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
)

type ApiUser struct {
	Id              primitive.ObjectID   `bson:"_id,omitempty"`
	UserId          string               `bson:"userId"`
	Type            string               `bson:"type"`
	Name            string               `bson:"name"`
	ApiIds          []primitive.ObjectID `bson:"api_ids"`
	PubIds          []primitive.ObjectID `bson:"publisher_ids"`
	AdpIds          []primitive.ObjectID `bson:"adProvider_ids"`
	ApiGwConsumerId string               `bson:"apiGwConsumerId"`
}

var (
	nclavis *Nclavis
)

func init() {
	nclavis = &Nclavis{}
}

/**
기본 인증 (API 사용자가 맞는지 확인)
*/
func Authenticate(ginCtx *gin.Context, userId string, encodedUserId string, timestamp string) bool {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// userId로 apiKey를 찾고
	apiKey, err := nclavis.GetApiKey(ginCtx, userId)
	if err != nil {
		entry.Errorf("[TRIPLE-A] [BASE-AUTHEN] NClavis로부터 API KEY 얻어 오는데 실패. UserId: %s Err: %s", userId, err)
		return false
	}
	//entry.Debugf("[TRIPLE-A] [BASE-AUTHEN] UserId: %s ApiKey: %s", userId, apiKey)

	// [optional] timestamp 적용
	data := userId
	if timestamp != "" {
		data += "_" + timestamp
	}
	entry.Debugf("[TRIPLE-A] [BASE-AUTHEN] Data: %s", data)
	entry.Debugf("[TRIPLE-A] [BASE-AUTHEN] API_KEY: %s", apiKey)
	entry.Debugf("[TRIPLE-A] [BASE-AUTHEN] UserID: %s", userId)
	entry.Debugf("[TRIPLE-A] [BASE-AUTHEN] timestamp: %s", timestamp)

	// Create a new HMAC by defining the hash type and the key (as byte array)
	h := hmac.New(sha256.New, []byte(apiKey))

	// Write Data to it
	h.Write([]byte(data))

	// Get result and encode as hexadecimal string
	sha := hex.EncodeToString(h.Sum(nil))

	entry.Debugf("[TRIPLE-A] [BASE-AUTHEN] encodedUserId   (mySide): %s", sha)
	entry.Debugf("[TRIPLE-A] [BASE-AUTHEN] encodedUserId (yourSide): %s", encodedUserId)
	entry.Debugf("[TRIPLE-A] [BASE-AUTHEN]                 비교결과: %d", strings.Compare(encodedUserId, sha))

	// encodedUserId가 내가 encode한 값과 일치하면 인증 통과
	if strings.Compare(encodedUserId, sha) == 0 {
		return true
	}

	return false
}

/**
API GW 인증 (Consumer ID가 기술되어 온 경우 Api userId와 매칭되는지 확인)
*/
func AuthenticateApiGw(ginCtx *gin.Context, userId string, apiGwConsumerId string) bool {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debugf("[TRIPLE-A] [APIGW-AUTHEN] ---------------------------- userId:%s, apiGwConsumerId:%s", userId, apiGwConsumerId)
	collection := database.GFP.Collection("ApiUsers")

	filter := bson.M{
		"userId":          userId,
		"apiGwConsumerId": apiGwConsumerId,
	}

	findOptions := options.FindOneOptions{}
	findOptions.SetProjection(bson.M{"_id": 1, "userId": 1, "apiGwConsumerId": 1})

	var apiUser ApiUser
	ctx, _ := context.WithTimeout(context.TODO(), 30*time.Second)
	err := collection.FindOne(ctx, filter, &findOptions).Decode(&apiUser)
	entry.Debugf("[TRIPLE-A] [APIGW-AUTHEN] apiGwConsumerId로 찾은 apiUser: %v", apiUser)
	if err != nil {
		entry.Errorf("[TRIPLE-A] [APIGW-AUTHEN] 인증 정보 가져오기 실패. %v", err)
		return false
	}

	if len(apiUser.Id) > 0 {
		return true
	}

	return false
}

/**
해당 API에 대해 사용권한이 있는지
*/
func CheckApi(ginCtx *gin.Context, userId string) bool {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debugf("[TRIPLE-A] [CHECK-API] userId: %s", userId)

	// 조건 설정
	matchQuery := bson.M{
		"userId": userId,
	}

	pipeline := mongo.Pipeline{
		{{
			"$match", matchQuery,
		}},
		{{
			"$lookup", bson.M{
				"from":         "Apis",
				"foreignField": "_id",
				"localField":   "api_ids",
				"as":           "api",
			},
		}},
		{{
			"$unwind", bson.M{
				"path":                       "$api",
				"preserveNullAndEmptyArrays": false,
			},
		}},
		{{
			"$project", bson.M{
				"url": "$api.url",
			},
		}},
	}

	// Aggregation 옵션 설정
	opts := options.Aggregate()
	opts.SetAllowDiskUse(true)

	// timeout 30초인 context 설정
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	// Aggregation 실행
	collection := database.GFP.Collection("ApiUsers")
	cur, e := collection.Aggregate(ctx, pipeline, opts)
	if e != nil {
		entry.Errorf("[TRIPLE-A] API 사용권한 조회 실패. %v", e.Error())
		param := map[string]string{"msg": e.Error()}
		gfpError.Default.BizError(param)
		return false
	}
	defer cur.Close(ctx)

	// 조회 결과 가져오기
	var api bson.M
	for cur.Next(ctx) {
		e := cur.Decode(&api)
		if e != nil {
			entry.Errorf("[TRIPLE-A] API 사용권한 조회 실패. %v", e.Error())
			param := map[string]string{"msg": e.Error()}
			gfpError.Default.BizError(param)

			return false
		}

		// URL이 맞는지
		var g, _ = glob.Compile(api["url"].(string))
		if g.Match(ginCtx.Request.URL.Path) {
			return true
		}
	}

	return false
}

/**
권한 검증
*/
func Authorization(ginCtx *gin.Context, userId string, reqPubIds []string, reqAdpIds []string) bool {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debugf("[TRIPLE-A] [AUTHORI] userId:%s, reqPubIds:%v reqAdpIds:%v", userId, reqPubIds, reqAdpIds)
	collection := database.GFP.Collection("ApiUsers")

	filter := bson.M{"userId": userId}

	findOptions := options.FindOneOptions{}
	findOptions.SetProjection(bson.M{"publisher_ids": 1, "adProvider_ids": 1})

	var apiUser ApiUser
	ctx, _ := context.WithTimeout(context.TODO(), 30*time.Second)
	err := collection.FindOne(ctx, filter, &findOptions).Decode(&apiUser)
	if err != nil {
		entry.Errorf("[TRIPLE-A] [AUTHORI] 권한 정보 가져오기 실패. %v", err)
		return false
	}

	if len(reqPubIds) > 0 && !checkPubs(ginCtx, userId, reqPubIds, apiUser.PubIds) {
		entry.Warnf("[TRIPLE-A] [AUTHORI] publisher 권한 없음")
		return false
	}

	if len(reqAdpIds) > 0 && !checkAdps(ginCtx, userId, reqAdpIds, apiUser.AdpIds) {
		entry.Warnf("[TRIPLE-A] [AUTHORI] adProvider 권한 없음")
		return false
	}

	return true
}

/**
접근하려는 Publisher Id에 대한 권한이 있는지
reqPubIds			: "5c08ecf9ee89a20033b55712"
registeredPubIds	: [ObjectId("5c08ecf9ee89a20033b55712"), ObjectId("5c08ecf9ee89a20033b55712") ]
*/
func checkPubs(ginCtx *gin.Context, userId string, reqPubIds []string, registeredPubIds []primitive.ObjectID) bool {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// DB에 등록된 publisher_ids를 맵으로
	registeredPublisherIdMap := make(map[string]primitive.ObjectID)
	for i := 0; i < len(registeredPubIds); i++ {
		registeredPublisherIdMap[registeredPubIds[i].Hex()] = registeredPubIds[i]
	}

	if len(registeredPubIds) < 1 {
		entry.Warnf("[TRIPLE-A] [AUTHORI] 요청한 publisherId에 대한 권한이 없음. userId: %s, registeredPubIds length is 0", userId)
		return false
	}

	for i := 0; i < len(reqPubIds); i++ {
		_, ok := registeredPublisherIdMap[reqPubIds[i]]
		if !ok {
			entry.Warnf("[TRIPLE-A] [AUTHORI] 요청한 publisherId에 대한 권한이 없음. userId: %s, reqPubId:%s registeredPubIds:%v", userId, reqPubIds[i], registeredPubIds)
			return false
		} else {
			entry.Debugf("[TRIPLE-A] [AUTHORI] 요청한 publisherId에 대한 권한 있음. userId: %s, reqPubId:%s registeredPubIds:%v", userId, reqPubIds[i], registeredPubIds)
		}
	}
	return true
}

/**
접근하려는 AdProvider Id에 대한 권한이 있는지
*/
func checkAdps(ginCtx *gin.Context, userId string, reqAdpIds []string, registeredAdpIds []primitive.ObjectID) bool {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	// DB에 등록된 adProvider_ids를 맵으로
	registeredPublisherIdMap := make(map[string]primitive.ObjectID)
	for i := 0; i < len(registeredAdpIds); i++ {
		registeredPublisherIdMap[registeredAdpIds[i].Hex()] = registeredAdpIds[i]
	}

	if len(registeredPublisherIdMap) < 1 {
		entry.Warnf("[TRIPLE-A] [AUTHORI] 요청한 adProviderId에 대한 권한이 없음. userId: %s, registeredAdpIds length is 0", userId)
		return false
	}

	for i := 0; i < len(reqAdpIds); i++ {
		_, ok := registeredPublisherIdMap[reqAdpIds[i]]
		if !ok {
			entry.Warnf("[TRIPLE-A] [AUTHORI] 요청한 adProviderId에 대한 권한이 없음. userId: %s, reqAdpId:%s registeredAdpIds:%v", userId, reqAdpIds[i], registeredAdpIds)
			return false
		} else {
			entry.Debugf("[TRIPLE-A] [AUTHORI] 요청한 adProviderId에 대한 권한 있음. userId: %s, reqPubId:%s registeredAdpIds:%v", userId, reqAdpIds[i], registeredAdpIds)
		}
	}
	return true
}

/**
ApiUser 정보 가져오기
*/
func GetApiUser(ginCtx *gin.Context, userId string) (apiUser *ApiUser, err error) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	entry.Debugf("[TRIPLE-A] [AUTHORI] [GetApiUser] userId:%s", userId)

	collection := database.GFP.Collection("ApiUsers")

	filter := bson.M{"userId": userId}

	ctx, _ := context.WithTimeout(context.TODO(), 30*time.Second)

	err = collection.FindOne(ctx, filter).Decode(&apiUser)
	if err != nil {
		entry.Errorf("[TRIPLE-A] [AUTHORI] [GetApiUser] ApiUser 정보 가져오기 실패. %v", err)
		return
	}

	// PubIds가 nil 인 경우, 빈 배열 처리
	if apiUser.PubIds == nil {
		apiUser.PubIds = make([]primitive.ObjectID, 0)
	}

	// AdpIds가 nil 인 경우, 빈 배열 처리
	if apiUser.AdpIds == nil {
		apiUser.AdpIds = make([]primitive.ObjectID, 0)
	}

	// ApiIds가 nil 인 경우, 빈 배열 처리
	if apiUser.ApiIds == nil {
		apiUser.ApiIds = make([]primitive.ObjectID, 0)
	}

	return
}
