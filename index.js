const data = require('./dist/index');
const defaultLang = "KO";

function dataList(code, lang) {
  return getDataList(code, lang || defaultLang, false);
}

function dataEnc(lang) {
  return getDataEnc(lang || defaultLang, false);
}

function dataListAvailable(code, lang) {
  return getDataList(code, lang || defaultLang, true);
}

function dataEncAvailable(lang) {
  return getDataEnc(lang || defaultLang, true);
}


function getDataList(code, lang, available) {
  let list = [];
  Object.keys(data[code]).forEach(key => {
    if (available) {
      if (data[code][key].use === 1) {
        list.push({
          code: data[code][key].code,
          name: data[code][key].name[lang] || data[code][key].name[defaultLang]
        });
      }
    } else {
      list.push({
        code: data[code][key].code,
        name: data[code][key].name[lang] || data[code][key].name[defaultLang]
      });
    }
  });
  return list;
}

function getDataEnc(lang, available) {
  let obj = {};
  Object.keys(data).forEach(key1st => {
    obj[key1st] = {};
    Object.keys(data[key1st]).forEach(key2nd => {
      if (available) {
        if (data[key1st][key2nd].use === 1) {
          obj[key1st][key2nd] = {
            code: data[key1st][key2nd].code,
            name: data[key1st][key2nd].name[lang]
          };
        }
      } else {
        obj[key1st][key2nd] = {
          code: data[key1st][key2nd].code,
          name: data[key1st][key2nd].name[lang]
        };
      }
    });
  });
  return obj;
}

exports.code = data;
exports.codeList = dataList;
exports.codeEnc = dataEnc;
exports.codeListAvailable = dataListAvailable;
exports.codeEncAvailable = dataEncAvailable;
