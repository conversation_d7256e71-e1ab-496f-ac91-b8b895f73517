#######################################################################################
#################################### Dockerfile 안내 ##################################
## 이미지 빌드
# docker build -t data-test -f Dockerfile_all_test .
# 애플 칩인 경우 아래 설정 추가
#   export DOCKER_DEFAULT_PLATFORM=linux/amd64

## 빌드된 이미지 실행
# 일반
#   docker run -it -d -p 12000:12000 -v /Users/<USER>/Documents/git/ssp-batch:/home1/irteam/apps/ssp-batch -v /Users/<USER>/Documents/git/sparkling:/home1/irteam/apps/sparkling -v /Users/<USER>/Documents/git/sparkling-s:/home1/irteam/apps/sparkling-s -v /Users/<USER>/Documents/logs/backup_restore:/home1/irteam/backup_restore --name data-test data-test:latest
# 주연
#   docker run -it --rm -p 12000:12000 -v D:/Projects:/home1/irteam/projects -v D:/local_download:/home1/irteam/local_download -v D:/owfs_download:/home1/irteam/owfs_download -v D:/backup_restore:/home1/irteam/backup_restore -v D:/Temp:/home1/irteam/temp --name data-test data-test:latest
#   docker run -it --rm -p 12000:12000 -v /Users/<USER>/Projects:/home1/irteam/projects -v /Users/<USER>/Storage/local_download:/home1/irteam/local_download -v /Users/<USER>/Storage/owfs_download:/home1/irteam/owfs_download -v /Users/<USER>/Storage/backup_restore:/home1/irteam/backup_restore -v /Users/<USER>/Storage/Temp:/home1/irteam/temp --name data-test data-test:latest
#   docker run -it --rm --network host -v /Users/<USER>/Projects:/home1/irteam/projects -v /Users/<USER>/Storage/local_download:/home1/irteam/local_download -v /Users/<USER>/Storage/owfs_download:/home1/irteam/owfs_download -v /Users/<USER>/Storage/backup_restore:/home1/irteam/backup_restore -v /Users/<USER>/Storage/Temp:/home1/irteam/temp --name data-test data-test:latest
# 애플 칩인 경우 아래 설정 추가
#   export DOCKER_DEFAULT_PLATFORM=linux/amd64

## 컨테이터에 들어가기
# docker exec -it data-test bash


#######################################################################################
##################################### C3 기본이미지 ###################################
# C3 Docker Image 가져오기
FROM reg.c3s.navercorp.com/c3/c3s-pan-env:20220818

USER root
ENV HOME=/home1/irteam
ENV APPS_HOME=/home1/irteam/apps

WORKDIR $HOME

RUN mkdir $APPS_HOME
RUN mkdir $APPS_HOME/c3
RUN echo "alias l='ls -al'" >> $HOME/.bashrc


#######################################################################################
####################################### n2c ###########################################
WORKDIR $APPS_HOME/n2c
RUN wget https://artifactory.navercorp.com/artifactory/dist/n2c/pkgs/n2c/linux-amd64/stable/n2c
RUN chmod +x ./n2c

# RUN ./n2c install -f 대신 아래 명령어 사용
# 사용자 입력을 요구하는 명령어 실행(n2c 다운받을 경로 물어볼 때 엔터가 자동 입력 되도록 함)
RUN echo | ./n2c install -f

# helm 버전 3 설치(n2c에서 디폴트로 깔리는 helm은 버전 2)
RUN curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3
RUN chmod 700 get_helm.sh
RUN ./get_helm.sh
RUN cp /usr/local/bin/helm $APPS_HOME/n2c/helm

ENV PATH $APPS_HOME/n2c:$PATH
RUN echo "alias k='kubectl'" >> $HOME/.bashrc

# 테스트 환경 ncc cluster 설정
RUN ./ncc cluster set nam-batch@ad1

WORKDIR $HOME


#######################################################################################
####################################### python 3.8 ####################################
# install python 3.8
RUN yum install -y gcc \
					openssl-devel \
					bzip2-devel \
					libffi-devel

ARG PYTHON=3.8.19
ADD https://www.python.org/ftp/python/$PYTHON/Python-$PYTHON.tgz .

RUN tar -xvf Python-$PYTHON.tgz --no-same-owner -C $APPS_HOME \
	&& rm Python-$PYTHON.tgz

WORKDIR $APPS_HOME/Python-$PYTHON

RUN ./configure --enable-optimizations
RUN make install

RUN echo 'alias python="/usr/local/bin/python3"' >> $HOME/.bashrc
RUN source $HOME/.bashrc

# install pip
RUN curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
RUN python3 get-pip.py

# install required python module
RUN python3 -m pip install sqlalchemy_utils pendulum pymongo



#######################################################################################
######################################## nodejs #######################################
# install utilities
RUN yum install -y yum-plugin-ovl \
					gcc-c++ \
					make \
					unzip \
					wget \
					git-core \
					logrotate \
					net-tools \
					krb5-devel \
					pigz

# nvm environment variables
ENV HOME=/home1/irteam
ENV NODE_VERSION 10.15.3
ENV NVM_VERSION 0.33.11
ENV NPM_VERSION 6.14.12

# install nvm
RUN curl --silent -o- https://raw.githubusercontent.com/creationix/nvm/v$NVM_VERSION/install.sh | bash

# install node and npm
RUN source $HOME/.nvm/nvm.sh \
	&& nvm install $NODE_VERSION \
	&& nvm alias default $NODE_VERSION \
	&& nvm use default

# add node and npm to path so the commands are available
ENV NODE_PATH $HOME/.nvm/v$NODE_VERSION/lib/node_modules
ENV PATH $HOME/.nvm/versions/node/v$NODE_VERSION/bin:$PATH

RUN npm install -g npm@$NPM_VERSION
RUN npm install -g node-gyp

RUN echo 'export NODE_ENV=local' >> /home1/irteam/.bashrc

CMD ["bash"]


######################################################################################
################################# nubescli and csync #################################
WORKDIR $APPS_HOME

# nubescli 설치
RUN mkdir -p $APPS_HOME/nubes \
    && curl http://owfsrepo.navercorp.com/nubes/dist/nubescli_latest/linux/nubescli -o $APPS_HOME/nubes/nubescli \
    && chmod +x $APPS_HOME/nubes/nubescli

# csync 설치 (container 실행 후, 최초 수동으로 csync 명령어 실행하여 관련모듈 추가 설치 필요)
# 파이썬 'requests' 모듈이 필요하여 같이 설치 (python2)
RUN curl https://cuve.navercorp.com/doc/kr/get-cuve-release.py | python - cuve-eco csync \
    && tar -xzf csync-*.tar.gz --no-same-owner \
    && rm csync-*.tar.gz \
    && curl https://bootstrap.pypa.io/pip/2.7/get-pip.py | python - \
    && python -m pip install requests \
    && ln -s $APPS_HOME/csync-1.2.14/bin/csync /usr/bin/csync

ENV PATH $APPS_HOME/nubes:$APPS_HOME/csync:$PATH


#######################################################################################
####################################### spark 3.2.1 ###################################
# install spark3
WORKDIR $APPS_HOME

# spark 3.2.1 다운로드
ARG SPARK=spark-3.2.1-bin-without-hadoop
ADD https://archive.apache.org/dist/spark/spark-3.2.1/$SPARK.tgz .

# spark 3.2.1 설치
RUN tar -xzf $SPARK.tgz --no-same-owner -C $APPS_HOME \
	&& rm $SPARK.tgz

ARG SPARK_HOME=$APPS_HOME/$SPARK

# /home1/irteam/apps 경로 권한을 irteam 으로 변경
RUN chown -R irteam:irteam $APPS_HOME

# airflow에서 kubectl apply 할 때 적용되는 환경 변수
ENV SPARK_HOME=$SPARK_HOME \
    PATH=$SPARK_HOME/bin:$PATH

# kubectl exec로 들어갈 때 스파크 환경을 맞춰주기 위해 .bashrc 설정
RUN echo "export SPARK_HOME=$SPARK_HOME" >> $HOME/.bashrc \
	&& echo "export PATH=$SPARK_HOME/bin:$PATH" >> $HOME/.bashrc


#############################################################################################
####################################### c3s conf ############################################
WORKDIR $HOME

COPY conf/test $APPS_HOME/c3
COPY conf/test/etc/krb5.conf /etc/krb5.conf
COPY conf/test/etc/hadoop/* /etc/hadoop/conf/
COPY conf/test/etc/spark2/spark-defaults.conf /usr/hdp/current/spark2-client/conf/
COPY conf/test/etc/spark2/log4j.properties /usr/hdp/current/spark2-client/conf/

RUN echo '10 8,20 * * * /home1/irteam/apps/c3/run_kinit.sh > /home1/irteam/apps/c3/run_kinit.sh.log 2>&1' >> /etc/cron.d/cronjob
RUN chmod +x /etc/cron.d/cronjob
RUN crontab /etc/cron.d/cronjob
RUN chmod +x $APPS_HOME/c3/run_kinit.sh

# krb5 설정 정보 추가
RUN echo 'export KRB5CCNAME=/home1/irteam/apps/c3/etc/krb5cc' >> $HOME/.bashrc
RUN echo '[ -f "/home1/irteam/apps/c3/run_kinit.sh" ] && \. "/home1/irteam/apps/c3/run_kinit.sh"' >> /home1/irteam/.bashrc

RUN kdestroy


#############################################################################################
####################################### batch env setting ###################################
RUN mkdir deploy && ln -s /home1/irteam/local_download /home1/irteam/deploy/local_download


#############################################################################################
####################################### local setting #######################################
RUN echo 'export PS1="\e[38;5;81m[TEST::\u@\$PWD]\\$ \e[0m"' >> /home1/irteam/.bashrc

CMD ["bash"]
