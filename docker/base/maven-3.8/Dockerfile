FROM reg.navercorp.com/gfp/base/jdk:8u272

ARG MAVEN_URL=https://dlcdn.apache.org/maven/maven-3/3.8.5/binaries/apache-maven-3.8.5-bin.tar.gz
ARG MAVEN_BASEDIR=apache-maven-3.8.5

ENV M2_HOME=/home1/irteam/$MAVEN_BASEDIR
ENV PATH=$M2_HOME/bin:$PATH

WORKDIR /home1/irteam

RUN curl -fsSL "$MAVEN_URL" -o mvn.tar.gz \
  && tar -xzf mvn.tar.gz --no-same-owner \
  && rm mvn.tar.gz \
  && mvn -v

LABEL MAVEN_DOCKERFILE="https://oss.navercorp.com/da-ssp/k8s-lib/docker/base/maven-3.8/Dockerfile"

CMD ["bash"]

