FROM reg.navercorp.com/base/ubuntu:18.04

USER root

ENV PATH /usr/local/go/bin:$PATH

ENV GOLANG_VERSION 1.15.14

RUN curl -sSLf https://golang.org/dl/go1.15.14.linux-amd64.tar.gz -o golang.tar.gz \
    && tar -C /usr/local -xzf golang.tar.gz \
    && rm golang.tar.gz \
    && go version

ENV GOPATH /go
ENV PATH $GOPATH/bin:$PATH
RUN mkdir -p "$GOPATH/src" "$GOPATH/bin" && chmod -R 777 "$GOPATH"
WORKDIR $GOPATH
