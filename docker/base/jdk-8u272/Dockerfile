FROM reg.navercorp.com/base/ubuntu:18.04

ARG JDK_URL=https://github.com/AdoptOpenJDK/openjdk8-binaries/releases/download/jdk8u272-b10/OpenJDK8U-jdk_x64_linux_hotspot_8u272b10.tar.gz

# Dockerfile references
#  - https://oss.navercorp.com/ncc-sample/image/blob/master/harbor/base/centos/jdk/Dockerfile
#  - https://github.com/docker-library/openjdk/blob/master/8/jre/buster/Dockerfile

USER root

ENV JAVA_HOME=/usr/local/openjdk8
ENV PATH=$JAVA_HOME/bin:$PATH

COPY ./jdk8u202_java.security .

RUN mkdir -p $JAVA_HOME \
  && curl -fsSL "$JDK_URL" -o jdk.tar.gz \
  && tar -xzf jdk.tar.gz -C $JAVA_HOME --strip-components 1 --no-same-owner \
  && rm jdk.tar.gz \
  && mv -f ./jdk8u202_java.security $JAVA_HOME/jre/lib/security/java.security \
  && find "$JAVA_HOME/lib" -name '*.so' -exec dirname '{}' ';' | sort -u > /etc/ld.so.conf.d/docker-openjdk.conf \
  && ldconfig \
  && java -version

LABEL JDK_DOCKERFILE="https://oss.navercorp.com/da-ssp/k8s-lib/docker/base/jdk-8u272/Dockerfile"

CMD ["bash"]
