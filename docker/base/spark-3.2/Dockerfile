#ARG BASE_IMAGE=reg.navercorp.com/gfp/base/jdk:8u272
ARG BASE_IMAGE=reg.navercorp.com/gfp/base/maven:3.8.5
FROM ${BASE_IMAGE}

ARG SPARK_URL=https://dlcdn.apache.org/spark/spark-3.2.1/spark-3.2.1-bin-hadoop2.7.tgz
ARG SPARK_BASEDIR=spark-3.2.1-bin-hadoop2.7

ENV SPARK_HOME=/home1/irteam/$SPARK_BASEDIR
ENV PATH=$SPARK_HOME/bin:$PATH

WORKDIR /home1/irteam

RUN curl -fsSL "$SPARK_URL" -o spark.tar.gz \
  && tar -xzf spark.tar.gz --no-same-owner \
  && rm spark.tar.gz \
  && spark-submit --version

LABEL SPARK_DOCKERFILE="https://oss.navercorp.com/da-ssp/k8s-lib/docker/base/spark-3.2/Dockerfile"

CMD ["bash"]

