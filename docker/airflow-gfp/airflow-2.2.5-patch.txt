diff --git a/Dockerfile b/Dockerfile
index aadf896a4..aa03635c5 100644
--- a/Dockerfile
+++ b/Dockerfile
@@ -203,8 +203,7 @@ ENV PATH=${PATH}:/opt/mssql-tools/bin
 
 COPY docker-context-files /docker-context-files
 
-RUN adduser --gecos "First Last,RoomNumber,WorkPhone,HomePhone" --disabled-password \
-       --quiet "airflow" --uid "${AIRFLOW_UID}" --gid "0" --home "${AIRFLOW_USER_HOME_DIR}" && \
+RUN useradd "airflow" --uid "${AIRFLOW_UID}" --gid "0" -m --home "${AIRFLOW_USER_HOME_DIR}" && \
     mkdir -p ${AIRFLOW_HOME} && chown -R "airflow:0" "${AIRFLOW_USER_HOME_DIR}" ${AIRFLOW_HOME}
 
 USER airflow
@@ -386,7 +385,6 @@ ARG RUNTIME_APT_DEPS="\
        netcat \
        openssh-client \
        postgresql-client \
-       python2 \
        rsync \
        sasl2-bin \
        sqlite3 \
@@ -454,8 +452,7 @@ RUN chmod a+x /scripts/docker/install_mysql.sh /scripts/docker/install_mssql.sh
     && sync \
     && /scripts/docker/install_mysql.sh prod \
     && /scripts/docker/install_mssql.sh \
-    && adduser --gecos "First Last,RoomNumber,WorkPhone,HomePhone" --disabled-password \
-           --quiet "airflow" --uid "${AIRFLOW_UID}" --gid "0" --home "${AIRFLOW_USER_HOME_DIR}" \
+    && useradd "airflow" --uid "${AIRFLOW_UID}" --gid "0" -m --home "${AIRFLOW_USER_HOME_DIR}" \
 # Make Airflow files belong to the root group and are accessible. This is to accommodate the guidelines from
 # OpenShift https://docs.openshift.com/enterprise/3.0/creating_images/guidelines.html
     && mkdir -pv "${AIRFLOW_HOME}" \
@@ -476,7 +473,6 @@ COPY --chown=airflow:0 scripts/in_container/prod/clean-logs.sh /clean-logs
 # Set default groups for airflow and root user
 
 RUN chmod a+x /entrypoint /clean-logs \
-    && chmod g=u /etc/passwd \
     && chmod g+w "${AIRFLOW_USER_HOME_DIR}/.local" \
     && usermod -g 0 airflow -G 0
 
