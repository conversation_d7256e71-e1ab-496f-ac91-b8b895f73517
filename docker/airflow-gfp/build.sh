#!/bin/bash

DOCKER=${DOCKER:-docker}


AIRFLOW_REPO=https://github.com/apache/airflow
AIRFLOW_VERSION=${AIRFLOW_VERSION:-2.2.5}

TARGET_REPO=reg.navercorp.com/gfp/airflow-gfp
TARGET_TAG=${TARGET_TAG:-$AIRFLOW_VERSION}

# cd airflow-repo; git diff > patch.txt
# 1) cd airflow-repo; git apply patch.txt
# 2) cd airflow-repo; patch -p1 < patch.txt
PATCH_FILE=airflow-${AIRFLOW_VERSION}-patch.txt

PYTHON_BASE_IMAGE=reg.navercorp.com/gfp/base/python:3.8

# From reg.navercorp.com/gfp/base/python:3.8
#
# ARG PYTHON_PIP_VERSION=21.3.1
AIRFLOW_PIP_VERSION=21.3.1

# From Dockerfile of airflow 2.2.4 
#
# ARG AIRFLOW_EXTRAS="amazon,async,celery,cncf.kubernetes,dask,docker,elasticsearch,ftp,google,google_auth,grpc,hashicorp,http,ldap,microsoft.azure,mysql,odbc,pandas,postgres,redis,sendgrid,sftp,slack,ssh,statsd,virtualenv"
AIRFLOW_EXTRAS=(
    async
    celery
    cncf.kubernetes
    elasticsearch
    ftp
    grpc
    http
    ldap
    postgres
    redis
    sftp
    slack
    ssh
    virtualenv
    google
    google_auth
)

ADDITIONAL_AIRFLOW_EXTRAS=(
    kerberos
    mongo
    apache.hive
    apache.webhdfs
    apache.druid
)

set -ex
git clone --depth 1 --branch $AIRFLOW_VERSION "$AIRFLOW_REPO" airflow
(cd airflow; patch -p1 < ../$PATCH_FILE)

$DOCKER build -t $TARGET_REPO:$TARGET_TAG \
    --build-arg AIRFLOW_VERSION=$AIRFLOW_VERSION \
    --build-arg AIRFLOW_PIP_VERSION=$AIRFLOW_PIP_VERSION \
    --build-arg PYTHON_BASE_IMAGE=$PYTHON_BASE_IMAGE \
    --build-arg INSTALL_MYSQL_CLIENT="false" \
    --build-arg INSTALL_MSSQL_CLIENT="false" \
    --build-arg AIRFLOW_EXTRAS=$(IFS=, ; echo "${AIRFLOW_EXTRAS[*]}") \
    --build-arg ADDITIONAL_AIRFLOW_EXTRAS=$(IFS=, ; echo "${ADDITIONAL_AIRFLOW_EXTRAS[*]}") \
    "${@}" \
    airflow
