ARG AIRFLOW_VERSION=2.1.1
FROM reg.navercorp.com/gfp/airflow-base:$AIRFLOW_VERSION

ARG SPARK_DIST_URL=https://archive.apache.org/dist/spark/spark-2.3.4/spark-2.3.4-bin-hadoop2.7.tgz
ARG SPARK_DIST_PREFIX=spark-2.3.4-bin-hadoop2.7

USER root

# N2C security violation
RUN chmod 644 /etc/passwd

ENV SPARK_HOME=/usr/local/${SPARK_DIST_PREFIX}
ENV PATH=$SPARK_HOME/bin:$PATH

RUN cd $(dirname $SPARK_HOME) \
    && curl -sSfL "$SPARK_DIST_URL" -o spark.tgz \
    && tar xzf spark.tgz --no-same-owner \
    && rm spark.tgz

USER airflow

CMD ["bash"]
