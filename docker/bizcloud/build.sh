#!/bin/bash

cat <<EOF
#
# Artifacts
#  reg.navercorp.com/gfp/bizcloud:2.7
#
# Copy hadoop 2.7 tarball into the build context
# Copy biz-cloud hadoop confs (both of test and real) into the build context

# Build command
#
# $ wget https://archive.apache.org/dist/hadoop/common/hadoop-2.7.7/hadoop-2.7.7.tar.gz
# ...
# $ cp -R /path/to/bizcloud-conf .
# $
# $ docker build -t reg.navercorp.com/gfp/bizcloud:2.7 \\
# > --build-arg hadoop_conf_test_dir=./conf/test \\
# > --build-arg hadoop_conf_real_dir=./conf/real \\
# > --build-arg hadoop_tarball=./hadoop-2.7.7.tar.gz \\
# > --build-arg hadoop_basedir=hadoop-2.7.7 \\
# > .
#
# See Dockerfile for exact build args
#
# Running
#  - Define 'HADOOP_CONF_DIR' to either /home1/irteam/hadoop-conf-test
#    or /home1/irteam/hadoop-conf-real
#
EOF
