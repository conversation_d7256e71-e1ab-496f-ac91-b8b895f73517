#!/bin/bash

DOCKER=${DOCKER:-docker}

set -exuo pipefail

# Referenced by apache airflow helm chart 1.0.0
redis_repo="https://github.com/docker-library/redis"
redis_path="6.2"
redis_dir=redis-repo

n2c_patch=redis-6.2-patch.txt
n2c_pg=reg.navercorp.com/gfp/redis:6.2-p1

build_redis()
{
    git clone $redis_repo $redis_dir
    (cd $redis_dir; patch -p1 < ../$n2c_patch)
    (cd $redis_dir/$redis_path; $DOCKER build -t $n2c_pg .)

    rm -rf $redis_dir
}

build_redis
