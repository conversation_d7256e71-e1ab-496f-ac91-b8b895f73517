diff --git a/6.2/Dockerfile b/6.2/Dockerfile
index 44d6236..644fb42 100644
--- a/6.2/Dockerfile
+++ b/6.2/Dockerfile
@@ -1,4 +1,6 @@
-FROM debian:bullseye-slim
+FROM reg.navercorp.com/base/ubuntu:18.04
+
+USER root
 
 # add our user and group first to make sure their IDs get assigned consistently, regardless of whatever dependencies get added
 RUN groupadd -r -g 999 redis && useradd -r -g redis -u 999 redis
@@ -110,7 +112,6 @@ RUN set -eux; \
 	redis-server --version
 
 RUN mkdir /data && chown redis:redis /data
-VOLUME /data
 WORKDIR /data
 
 COPY docker-entrypoint.sh /usr/local/bin/
