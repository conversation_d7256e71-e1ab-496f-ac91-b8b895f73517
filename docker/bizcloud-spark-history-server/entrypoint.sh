#!/bin/bash
trap on_exit EXIT

set -eu

start_server=$SPARK_HOME/sbin/start-history-server.sh
stop_server=$SPARK_HOME/sbin/stop-history-server.sh

log()
{
    echo $(date "+%Y-%m-%d %H:%M:%S") '[entrypoint.sh]' "${@}"
}

on_exit()
{
    log executing $stop_server
    $stop_server
    log "waiting for some cooldown"
    sleep 2
    log "exiting..."
}

check_server()
{
    jps | grep HistoryServer >/dev/null 2>&1
    if [ "$?" != "0" ]; then
        trap - EXIT

        log "history server is not running, exiting.."
        exit 1
    fi
}

log "Environments"
env

log ""
log "Starting history server..."
$start_server

while true; do
    sleep 5
    check_server
    tail -5 /home1/irteam/logs/*.out || log "tailing server logs failed..."
    sleep 5
done
