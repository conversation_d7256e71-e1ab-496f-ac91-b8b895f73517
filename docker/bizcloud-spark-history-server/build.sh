#!/bin/bash

cat <<EOF
#
# Artifact
#  reg.navercorp.com/gfp/bizcloud-spark-his:3.2.1
#
# Build command
#
# $ docker build -t reg.navercorp.com/gfp/bizcloud-spark-his:3.2.1 .
#
# Running
#  - Define 'HADOOP_CONF_DIR' to either /home1/irteam/hadoop-conf-test
#    or /home1/irteam/hadoop-conf-real
#  - Define 'SPARK_HISTORY_OPTS'
#    ex) SPARK_HISTORY_OPTS="-Dspark.history.fs.logDirectory=hdfs/namserver/path/to/sparkEventLog"
#
EOF
