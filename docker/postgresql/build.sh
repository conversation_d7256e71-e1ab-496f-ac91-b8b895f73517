#!/bin/bash

DOCKER=${DOCKER:-docker}

set -exuo pipefail

# Referenced by bitnami postgresql helm chart 9.8.12
bitnami_pg_repo="https://github.com/bitnami/bitnami-docker-postgresql"
bitnami_pg_path="11/debian-10"
bitnami_pg_tag="11.10.0-debian-10-r0"
bitnami_pg_dir=bitnami-pg

# Patch changes summary
#  * Change base image to n2c ubuntu:18.04
#  * Remove prerequisite libraries for purpose of build
#    - libicu63   (libicu60  in ubuntu)
#    - libtinfo6  (libtinfo5 in ubuntu)
#  * Add postgres user/group
#  * Remain as root to avoid nss_wrapper activation
#
# Update:
#  * postgres binary has links to libicu63, so revert the followings
#    - leave base image and prerequisite libs intact
n2c_patch=$bitnami_pg_tag.patch

n2c_pg=reg.navercorp.com/gfp/bitnami-pg:11.10.0-debian-10-r0

build_postgres()
{
    git clone $bitnami_pg_repo $bitnami_pg_dir
    (cd $bitnami_pg_dir; git checkout $bitnami_pg_tag)
    (cd $bitnami_pg_dir; patch -p1 < ../$n2c_patch)
    (cd $bitnami_pg_dir/$bitnami_pg_path; $DOCKER build -t $n2c_pg .)

    rm -rf $bitnami_pg_dir
}

build_postgres
