diff --git a/11/debian-10/Dockerfile b/11/debian-10/Dockerfile
index dc96df3d..0f62797b 100644
--- a/11/debian-10/Dockerfile
+++ b/11/debian-10/Dockerfile
@@ -23,6 +23,9 @@ RUN echo 'en_US.UTF-8 UTF-8' >> /etc/locale.gen && locale-gen
 COPY rootfs /
 RUN /opt/bitnami/scripts/postgresql/postunpack.sh
 RUN /opt/bitnami/scripts/locales/add-extra-locales.sh
+
+RUN /opt/bitnami/scripts/postgresql/user_setup.sh
+
 ENV BITNAMI_APP_NAME="postgresql" \
     BITNAMI_IMAGE_VERSION="11.10.0-debian-10-r0" \
     LANG="en_US.UTF-8" \
diff --git a/11/debian-10/rootfs/opt/bitnami/scripts/postgresql/user_setup.sh b/11/debian-10/rootfs/opt/bitnami/scripts/postgresql/user_setup.sh
new file mode 100755
index 00000000..168c1694
--- /dev/null
+++ b/11/debian-10/rootfs/opt/bitnami/scripts/postgresql/user_setup.sh
@@ -0,0 +1,7 @@
+#!/bin/bash
+
+set -eux
+. /opt/bitnami/scripts/postgresql-env.sh \
+    && groupadd -r postgres --gid=1001 \
+    && useradd -r -g postgres --uid=1001 -G root -d $POSTGRESQL_BASE_DIR postgres \
+    && chown -R postgres:postgres $POSTGRESQL_BASE_DIR $POSTGRESQL_VOLUME_DIR
