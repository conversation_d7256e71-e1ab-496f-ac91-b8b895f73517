#!/bin/bash

cat <<EOF
#
# Artifacts
#  reg.navercorp.com/gfp/bizcloud-spark:3.2.1
#
# Copy spark3 tarball into the build context
# Copy biz-cloud hadoop confs (both of test and real) into the build context

# Build command
#
# $ docker build -t reg.navercorp.com/gfp/bizcloud-spark:3.2.1 \\
# > --build-arg hadoop_conf_test_dir=./conf/test \\
# > --build-arg hadoop_conf_real_dir=./conf/real \\
# > --build-arg spark3_tarball=./spark-3.2.1-bin-hadoop2.7.tgz \\
# > --build-arg spark3_basedir=spark-3.2.1-bin-hadoop2.7 \\
# > .
#
# See Dockerfile for exact build args
#
# Running
#  - Define 'HADOOP_CONF_DIR' to either /home1/irteam/hadoop-conf-test
#    or /home1/irteam/hadoop-conf-real
#
EOF
