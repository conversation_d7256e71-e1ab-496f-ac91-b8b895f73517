<?xml version="1.0" encoding="UTF-8"?>

<!--Autogenerated by Cloudera Manager-->
<configuration>
  <property>
    <name>dfs.nameservices</name>
    <value>BizCloud</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.BizCloud</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.ha.automatic-failover.enabled.BizCloud</name>
    <value>true</value>
  </property>
  <property>
    <name>ha.zookeeper.quorum</name>
    <value>ascdh026-sa.nfra.io:2181,ascdh027-sa.nfra.io:2181,ascdh028-sa.nfra.io:2181,ascdh029-sa.nfra.io:2181,ascdh030-sa.nfra.io:2181</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.BizCloud</name>
    <value>namenode1504,namenode1506</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.BizCloud.namenode1504</name>
    <value>ascdh102-sa.nfra.io:8020</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.BizCloud.namenode1504</name>
    <value>ascdh102-sa.nfra.io:8022</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.BizCloud.namenode1504</name>
    <value>ascdh102-sa.nfra.io:50070</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.BizCloud.namenode1504</name>
    <value>ascdh102-sa.nfra.io:50470</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.BizCloud.namenode1506</name>
    <value>ascdh103-sa.nfra.io:8020</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.BizCloud.namenode1506</name>
    <value>ascdh103-sa.nfra.io:8022</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.BizCloud.namenode1506</name>
    <value>ascdh103-sa.nfra.io:50070</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.BizCloud.namenode1506</name>
    <value>ascdh103-sa.nfra.io:50470</value>
  </property>
  <property>
    <name>dfs.replication</name>
    <value>3</value>
  </property>
  <property>
    <name>dfs.blocksize</name>
    <value>134217728</value>
  </property>
  <property>
    <name>dfs.client.use.datanode.hostname</name>
    <value>false</value>
  </property>
  <property>
    <name>fs.permissions.umask-mode</name>
    <value>022</value>
  </property>
  <property>
    <name>dfs.namenode.acls.enabled</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.client.use.legacy.blockreader</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.domain.socket.path</name>
    <value>/var/run/hdfs-sockets/dn</value>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit.skip.checksum</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.client.domain.socket.data.traffic</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.datanode.hdfs-blocks-metadata.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.client.block.write.locateFollowingBlock.retries</name>
    <value>20</value>
  </property>
</configuration>
