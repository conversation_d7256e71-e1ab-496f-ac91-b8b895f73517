<?xml version="1.0" encoding="UTF-8"?>

<!--Autogenerated by Cloudera Manager-->
<configuration>
  <property>
    <name>dfs.nameservices</name>
    <value>tesseract-dev</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.tesseract-dev</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.ha.automatic-failover.enabled.tesseract-dev</name>
    <value>true</value>
  </property>
  <property>
    <name>ha.zookeeper.quorum</name>
    <value>atcdh006-sa.nfra.io:2181,atcdh007-sa.nfra.io:2181,atcdh008-sa.nfra.io:2181,atcdh009-sa.nfra.io:2181,atcdh010-sa.nfra.io:2181</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.tesseract-dev</name>
    <value>namenode249,namenode238</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.tesseract-dev.namenode249</name>
    <value>atcdh001-sa.nfra.io:8020</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.tesseract-dev.namenode249</name>
    <value>atcdh001-sa.nfra.io:8022</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.tesseract-dev.namenode249</name>
    <value>atcdh001-sa.nfra.io:50070</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.tesseract-dev.namenode249</name>
    <value>atcdh001-sa.nfra.io:50470</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.tesseract-dev.namenode238</name>
    <value>atcdh002-sa.nfra.io:8020</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.tesseract-dev.namenode238</name>
    <value>atcdh002-sa.nfra.io:8022</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.tesseract-dev.namenode238</name>
    <value>atcdh002-sa.nfra.io:50070</value>
  </property>
  <property>
    <name>dfs.namenode.https-address.tesseract-dev.namenode238</name>
    <value>atcdh002-sa.nfra.io:50470</value>
  </property>
  <property>
    <name>dfs.replication</name>
    <value>3</value>
  </property>
  <property>
    <name>dfs.blocksize</name>
    <value>134217728</value>
  </property>
  <property>
    <name>dfs.client.use.datanode.hostname</name>
    <value>false</value>
  </property>
  <property>
    <name>fs.permissions.umask-mode</name>
    <value>022</value>
  </property>
  <property>
    <name>dfs.namenode.acls.enabled</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.client.use.legacy.blockreader</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.domain.socket.path</name>
    <value>/var/run/hdfs-sockets/dn</value>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit.skip.checksum</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.client.domain.socket.data.traffic</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.datanode.hdfs-blocks-metadata.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.client.block.write.locateFollowingBlock.retries</name>
    <value>20</value>
  </property>
</configuration>
