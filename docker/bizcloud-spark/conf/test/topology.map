<?xml version="1.0" encoding="UTF-8"?>

<!--Autogenerated by Cloudera Manager-->
<topology>
  <node name="atcdh001-sa.nfra.io" rack="/default"/>
  <node name="10.105.73.145" rack="/default"/>
  <node name="atcdh002-sa.nfra.io" rack="/default"/>
  <node name="10.105.73.146" rack="/default"/>
  <node name="atcdh003-sa.nfra.io" rack="/default"/>
  <node name="10.105.73.147" rack="/default"/>
  <node name="atcdh004-sa.nfra.io" rack="/default"/>
  <node name="10.105.73.148" rack="/default"/>
  <node name="atcdh005-sa.nfra.io" rack="/default"/>
  <node name="10.105.73.149" rack="/default"/>
  <node name="atcdh006-sa.nfra.io" rack="/default"/>
  <node name="10.105.73.150" rack="/default"/>
  <node name="atcdh007-sa.nfra.io" rack="/default"/>
  <node name="10.105.73.151" rack="/default"/>
  <node name="atcdh008-sa.nfra.io" rack="/default"/>
  <node name="10.105.73.154" rack="/default"/>
  <node name="atcdh009-sa.nfra.io" rack="/default"/>
  <node name="10.105.73.78" rack="/default"/>
  <node name="atcdh010-sa.nfra.io" rack="/default"/>
  <node name="10.105.73.79" rack="/default"/>
</topology>
