  <configuration  xmlns:xi="http://www.w3.org/2001/XInclude">
    
    <property>
      <name>docker_prechecking</name>
      <value>false</value>
    </property>
    
    <property>
      <name>hadoop.http.cross-origin.allowed-origins</name>
      <value>regex:.*[.]nfra[.]io(:\d*)?</value>
    </property>
    
    <property>
      <name>hadoop.registry.client.auth</name>
      <value>kerberos</value>
    </property>
    
    <property>
      <name>hadoop.registry.dns.bind-address</name>
      <value>0.0.0.0</value>
    </property>
    
    <property>
      <name>hadoop.registry.dns.bind-port</name>
      <value>53</value>
      <hidden>true</hidden>
    </property>
    
    <property>
      <name>hadoop.registry.dns.domain-name</name>
      <value>EXAMPLE.COM</value>
    </property>
    
    <property>
      <name>hadoop.registry.dns.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>hadoop.registry.dns.zone-mask</name>
      <value>*************</value>
    </property>
    
    <property>
      <name>hadoop.registry.dns.zone-subnet</name>
      <value>**********</value>
    </property>
    
    <property>
      <name>hadoop.registry.jaas.context</name>
      <value>Client</value>
    </property>
    
    <property>
      <name>hadoop.registry.secure</name>
      <value>true</value>
    </property>
    
    <property>
      <name>hadoop.registry.system.accounts</name>
      <value>sasl:yarn,sasl:jhs,sasl:hdfs-bizcloud,sasl:rm,sasl:hive,sasl:spark</value>
    </property>
    
    <property>
      <name>hadoop.registry.zk.quorum</name>
      <value>adevthm001-sa.nfra.io:2181,adevthm002-sa.nfra.io:2181,adevthm003-sa.nfra.io:2181</value>
    </property>
    
    <property>
      <name>manage.include.files</name>
      <value>false</value>
    </property>
    
    <property>
      <name>yarn.acl.enable</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.admin.acl</name>
      <value>yarn hadoop-admins</value>
    </property>
    
    <property>
      <name>yarn.application.classpath</name>
      <value>$HADOOP_CONF_DIR,/usr/hdp/*******-78/hadoop/*,/usr/hdp/*******-78/hadoop/lib/*,/usr/hdp/current/hadoop-hdfs-client/*,/usr/hdp/current/hadoop-hdfs-client/lib/*,/usr/hdp/current/hadoop-yarn-client/*,/usr/hdp/current/hadoop-yarn-client/lib/*</value>
    </property>
    
    <property>
      <name>yarn.authorization-provider</name>
      <value>org.apache.ranger.authorization.yarn.authorizer.RangerYarnAuthorizer</value>
    </property>
    
    <property>
      <name>yarn.client.nodemanager-connect.max-wait-ms</name>
      <value>60000</value>
    </property>
    
    <property>
      <name>yarn.client.nodemanager-connect.retry-interval-ms</name>
      <value>10000</value>
    </property>
    
    <property>
      <name>yarn.http.policy</name>
      <value>HTTP_ONLY</value>
    </property>
    
    <property>
      <name>yarn.log-aggregation-enable</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.log-aggregation.retain-seconds</name>
      <value>2592000</value>
    </property>
    
    <property>
      <name>yarn.log.server.url</name>
      <value>http://adevthm001-sa.nfra.io:19888/jobhistory/logs</value>
    </property>
    
    <property>
      <name>yarn.log.server.web-service.url</name>
      <value>http://adevthdp002-sa.nfra.io:8188/ws/v1/applicationhistory</value>
    </property>
    
    <property>
      <name>yarn.node-labels.enabled</name>
      <value>false</value>
    </property>
    
    <property>
      <name>yarn.node-labels.fs-store.retry-policy-spec</name>
      <value>2000, 500</value>
    </property>
    
    <property>
      <name>yarn.node-labels.fs-store.root-dir</name>
      <value>/system/yarn/node-labels</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.address</name>
      <value>0.0.0.0:45454</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.admin-env</name>
      <value>MALLOC_ARENA_MAX=$MALLOC_ARENA_MAX</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.aux-services</name>
      <value>mapreduce_shuffle,spark2_shuffle,timeline_collector</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.aux-services.mapreduce_shuffle.class</name>
      <value>org.apache.hadoop.mapred.ShuffleHandler</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.aux-services.spark2_shuffle.class</name>
      <value>org.apache.spark.network.yarn.YarnShuffleService</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.aux-services.spark2_shuffle.classpath</name>
      <value>/usr/hdp/*******-78/spark2/aux/*</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.aux-services.spark_shuffle.class</name>
      <value>org.apache.spark.network.yarn.YarnShuffleService</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.aux-services.spark_shuffle.classpath</name>
      <value>/usr/hdp/${hdp.version}/spark/aux/*</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.aux-services.timeline_collector.class</name>
      <value>org.apache.hadoop.yarn.server.timelineservice.collector.PerNodeTimelineCollectorsAuxService</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.bind-host</name>
      <value>0.0.0.0</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.container-executor.class</name>
      <value>org.apache.hadoop.yarn.server.nodemanager.LinuxContainerExecutor</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.container-metrics.unregister-delay-ms</name>
      <value>60000</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.container-monitor.interval-ms</name>
      <value>3000</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.delete.debug-delay-sec</name>
      <value>0</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.disk-health-checker.max-disk-utilization-per-disk-percentage</name>
      <value>90</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.disk-health-checker.min-free-space-per-disk-mb</name>
      <value>1000</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.disk-health-checker.min-healthy-disks</name>
      <value>0.25</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.health-checker.interval-ms</name>
      <value>135000</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.health-checker.script.timeout-ms</name>
      <value>60000</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.keytab</name>
      <value>/etc/security/keytabs/nm.service.keytab</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.linux-container-executor.cgroups.strict-resource-usage</name>
      <value>false</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.linux-container-executor.group</name>
      <value>hadoop</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.linux-container-executor.nonsecure-mode.limit-users</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.local-dirs</name>
      <value>/data1/yarn,/data2/yarn,/data3/yarn,/data4/yarn,/data5/yarn,/data6/yarn,/data7/yarn,/data8/yarn,/data9/yarn,/data10/yarn</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.log-aggregation.compression-type</name>
      <value>gz</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.log-aggregation.debug-enabled</name>
      <value>false</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.log-aggregation.num-log-files-per-app</name>
      <value>30</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.log-aggregation.roll-monitoring-interval-seconds</name>
      <value>3600</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.log-dirs</name>
      <value>/hadoop/yarn/log</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.log.retain-seconds</name>
      <value>604800</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.principal</name>
      <value>nm/<EMAIL></value>
    </property>
    
    <property>
      <name>yarn.nodemanager.recovery.dir</name>
      <value>/var/log/hadoop-yarn/nodemanager/recovery-state</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.recovery.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.recovery.supervised</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.remote-app-log-dir</name>
      <value>/app-logs</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.remote-app-log-dir-suffix</name>
      <value>logs</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.resource-plugins</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.nodemanager.resource-plugins.gpu.allowed-gpu-devices</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.nodemanager.resource-plugins.gpu.docker-plugin</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.nodemanager.resource-plugins.gpu.docker-plugin.nvidiadocker-v1.endpoint</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.nodemanager.resource-plugins.gpu.path-to-discovery-executables</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.nodemanager.resource.cpu-vcores</name>
      <value>38</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.resource.memory-mb</name>
      <value>102400</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.resource.percentage-physical-cpu-limit</name>
      <value>80</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.runtime.linux.allowed-runtimes</name>
      <value>default,docker</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.runtime.linux.docker.allowed-container-networks</name>
      <value>host,none,bridge</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.runtime.linux.docker.capabilities</name>
      <value>
      CHOWN,DAC_OVERRIDE,FSETID,FOWNER,MKNOD,NET_RAW,SETGID,SETUID,SETFCAP,
      SETPCAP,NET_BIND_SERVICE,SYS_CHROOT,KILL,AUDIT_WRITE</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.runtime.linux.docker.default-container-network</name>
      <value>host</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.runtime.linux.docker.privileged-containers.acl</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.nodemanager.runtime.linux.docker.privileged-containers.allowed</name>
      <value>false</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.vmem-check-enabled</name>
      <value>false</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.vmem-pmem-ratio</name>
      <value>2.1</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.webapp.cross-origin.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.webapp.spnego-keytab-file</name>
      <value>/etc/security/keytabs/spnego.service.keytab</value>
    </property>
    
    <property>
      <name>yarn.nodemanager.webapp.spnego-principal</name>
      <value>HTTP/<EMAIL></value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.address</name>
      <value>adevthm001-sa.nfra.io:8050</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.admin.address</name>
      <value>adevthm001-sa.nfra.io:8141</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.am.max-attempts</name>
      <value>2</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.bind-host</name>
      <value>0.0.0.0</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.cluster-id</name>
      <value>yarn-cluster</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.connect.max-wait.ms</name>
      <value>900000</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.connect.retry-interval.ms</name>
      <value>30000</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.display.per-user-apps</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.fs.state-store.retry-policy-spec</name>
      <value>2000, 500</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.fs.state-store.uri</name>
      <value> </value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.ha.automatic-failover.zk-base-path</name>
      <value>/yarn-leader-election</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.ha.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.ha.rm-ids</name>
      <value>rm1,rm2</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.hostname</name>
      <value>adevthm001-sa.nfra.io</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.hostname.rm1</name>
      <value>adevthm001-sa.nfra.io</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.hostname.rm2</name>
      <value>adevthm004-sa.nfra.io</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.keytab</name>
      <value>/etc/security/keytabs/rm.service.keytab</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.monitor.capacity.preemption.intra-queue-preemption.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.monitor.capacity.preemption.monitoring_interval</name>
      <value>15000</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.monitor.capacity.preemption.natural_termination_factor</name>
      <value>1</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.monitor.capacity.preemption.total_preemption_per_round</name>
      <value>0.1</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.nodes.exclude-path</name>
      <value>/etc/hadoop/conf/yarn.exclude</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.placement-constraints.handler</name>
      <value>scheduler</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.principal</name>
      <value>rm/<EMAIL></value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.principal.pattern</name>
      <value>*</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.proxy-user-privileges.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.proxyuser.*.groups</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.proxyuser.*.hosts</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.proxyuser.*.users</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.recovery.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.resource-tracker.address</name>
      <value>adevthm001-sa.nfra.io:8025</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.resource-tracker.address.rm1</name>
      <value>adevthm001-sa.nfra.io:8025</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.resource-tracker.address.rm2</name>
      <value>adevthm004-sa.nfra.io:8025</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.scheduler.address</name>
      <value>adevthm001-sa.nfra.io:8030</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.scheduler.class</name>
      <value>org.apache.hadoop.yarn.server.resourcemanager.scheduler.capacity.CapacityScheduler</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.scheduler.monitor.enable</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.state-store.max-completed-applications</name>
      <value>${yarn.resourcemanager.max-completed-applications}</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.store.class</name>
      <value>org.apache.hadoop.yarn.server.resourcemanager.recovery.ZKRMStateStore</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.system-metrics-publisher.dispatcher.pool-size</name>
      <value>10</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.system-metrics-publisher.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.webapp.address</name>
      <value>adevthm001-sa.nfra.io:8088</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.webapp.address.rm1</name>
      <value>adevthm001-sa.nfra.io:8088</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.webapp.address.rm2</name>
      <value>adevthm004-sa.nfra.io:8088</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.webapp.cross-origin.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.webapp.delegation-token-auth-filter.enabled</name>
      <value>false</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.webapp.https.address</name>
      <value>adevthm001-sa.nfra.io:8090</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.webapp.https.address.rm1</name>
      <value>adevthm001-sa.nfra.io:8090</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.webapp.https.address.rm2</name>
      <value>adevthm004-sa.nfra.io:8090</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.webapp.spnego-keytab-file</name>
      <value>/etc/security/keytabs/spnego.service.keytab</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.webapp.spnego-principal</name>
      <value>HTTP/<EMAIL></value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.work-preserving-recovery.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.work-preserving-recovery.scheduling-wait-ms</name>
      <value>10000</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.zk-acl</name>
      <value>sasl:rm:rwcda</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.zk-address</name>
      <value>adevthm001-sa.nfra.io:2181,adevthm002-sa.nfra.io:2181,adevthm003-sa.nfra.io:2181</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.zk-num-retries</name>
      <value>1000</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.zk-retry-interval-ms</name>
      <value>1000</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.zk-state-store.parent-path</name>
      <value>/rmstore</value>
    </property>
    
    <property>
      <name>yarn.resourcemanager.zk-timeout-ms</name>
      <value>10000</value>
    </property>
    
    <property>
      <name>yarn.rm.system-metricspublisher.emit-container-events</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.scheduler.capacity.ordering-policy.priority-utilization.underutilized-preemption.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.scheduler.maximum-allocation-mb</name>
      <value>102400</value>
    </property>
    
    <property>
      <name>yarn.scheduler.maximum-allocation-vcores</name>
      <value>38</value>
    </property>
    
    <property>
      <name>yarn.scheduler.minimum-allocation-mb</name>
      <value>1024</value>
    </property>
    
    <property>
      <name>yarn.scheduler.minimum-allocation-vcores</name>
      <value>1</value>
    </property>
    
    <property>
      <name>yarn.service.framework.path</name>
      <value>/hdp/apps/${hdp.version}/yarn/service-dep.tar.gz</value>
    </property>
    
    <property>
      <name>yarn.service.system-service.dir</name>
      <value>/services</value>
    </property>
    
    <property>
      <name>yarn.system-metricspublisher.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.address</name>
      <value>adevthdp002-sa.nfra.io:10200</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.bind-host</name>
      <value>0.0.0.0</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.client.max-retries</name>
      <value>30</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.client.retry-interval-ms</name>
      <value>1000</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.entity-group-fs-store.active-dir</name>
      <value>/ats/active/</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.entity-group-fs-store.app-cache-size</name>
      <value>10</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.entity-group-fs-store.cleaner-interval-seconds</name>
      <value>3600</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.entity-group-fs-store.done-dir</name>
      <value>/ats/done/</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.entity-group-fs-store.group-id-plugin-classes</name>
      <value>org.apache.hadoop.yarn.applications.distributedshell.DistributedShellTimelinePlugin</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.entity-group-fs-store.group-id-plugin-classpath</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.timeline-service.entity-group-fs-store.retain-seconds</name>
      <value>604800</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.entity-group-fs-store.scan-interval-seconds</name>
      <value>60</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.entity-group-fs-store.summary-store</name>
      <value>org.apache.hadoop.yarn.server.timeline.RollingLevelDBTimelineStore</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.generic-application-history.save-non-am-container-meta-info</name>
      <value>false</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.generic-application-history.store-class</name>
      <value>org.apache.hadoop.yarn.server.applicationhistoryservice.NullApplicationHistoryStore</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.hbase-schema.prefix</name>
      <value>prod.</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.hbase.configuration.file</name>
      <value>file:///usr/hdp/*******-78/hadoop/conf/embedded-yarn-ats-hbase/hbase-site.xml</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.hbase.coprocessor.jar.hdfs.location</name>
      <value>file:///usr/hdp/*******-78/hadoop-yarn/timelineservice/hadoop-yarn-server-timelineservice-hbase-coprocessor.jar</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.alt-kerberos.non-browser.user-agents</name>
      <value>java,curl,wget,perl,python,commons-httpclient,go-http-client,spnego-auth</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.authentication.provider.url</name>
      <value>https://adevthm004-sa.nfra.io:8443/gateway/knoxsso/api/v1/websso</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.cookie.domain</name>
      <value>.nfra.io</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.cookie.path</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.kerberos.keytab</name>
      <value>/etc/security/keytabs/spnego.service.keytab</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.kerberos.name.rules</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.kerberos.principal</name>
      <value>HTTP/<EMAIL></value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.proxyuser.*.groups</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.proxyuser.*.hosts</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.proxyuser.*.users</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.proxyuser.ambari-server-bizcloud.groups</name>
      <value>*</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.proxyuser.ambari-server-bizcloud.hosts</name>
      <value>adevthm001-sa.nfra.io</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.public.key.pem</name>
      <value>MIICUTCCAbqgAwIBAgIIFmjpzZhITX8wDQYJKoZIhvcNAQEFBQAwazELMAkGA1UEBhMCVVMxDTALBgNVBAgTBFRlc3QxDTALBgNVBAcTBFRlc3QxDzANBgNVBAoTBkhhZG9vcDENMAsGA1UECxMEVGVzdDEeMBwGA1UEAxMVYWRldnRobTAwNC1zYS5uZnJhLmlvMB4XDTIzMDcwMzA3MzAxMloXDTI0MDcwMjA3MzAxMlowazELMAkGA1UEBhMCVVMxDTALBgNVBAgTBFRlc3QxDTALBgNVBAcTBFRlc3QxDzANBgNVBAoTBkhhZG9vcDENMAsGA1UECxMEVGVzdDEeMBwGA1UEAxMVYWRldnRobTAwNC1zYS5uZnJhLmlvMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDG/8pOeOoh3wn38DK32zNMSMqSBWU7VNucFE7n6ow2Ux7qD+ACLfh2ImZhyFFJYaYFPyxsYdgOwTKfKnHCEGdww0OpIjSIb+/JCzMCAQURQrcARrO033s97dy7WGk40MW2bDrPI3TDXuCya63qEXyoMhySQ8k6UxGjT1g719jAWQIDAQABMA0GCSqGSIb3DQEBBQUAA4GBAGoKnO89sK2+afbYTtcgBr16ZM0IoARv2uQcf5eWiXJKKEiTGhCkkN3Q1pslv224VNv0N88g+5L0S/XFSamD/E6eXtx+kFmHy9rz603dciKXcohc16gi0/mdOSMRmRy/nYf9UWTGo3ZXg+0yxpHE5UmDA8NBuDky60/Bcs4bmfk+</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.signature.secret</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.signature.secret.file</name>
      <value>/etc/security/http_secret</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.signer.secret.provider</name>
      <value>file</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.signer.secret.provider.object</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.simple.anonymous.allowed</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.token.validity</name>
      <value></value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-authentication.type</name>
      <value>org.apache.hadoop.security.authentication.server.JWTRedirectAuthenticationHandler</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-cross-origin.allowed-origins</name>
      <value>*</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.http-cross-origin.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.keytab</name>
      <value>/etc/security/keytabs/yarn.service.keytab</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.leveldb-state-store.path</name>
      <value>/hadoop/yarn/timeline</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.leveldb-timeline-store.path</name>
      <value>/hadoop/yarn/timeline</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.leveldb-timeline-store.read-cache-size</name>
      <value>104857600</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.leveldb-timeline-store.start-time-read-cache-size</name>
      <value>10000</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.leveldb-timeline-store.start-time-write-cache-size</name>
      <value>10000</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.leveldb-timeline-store.ttl-interval-ms</name>
      <value>300000</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.principal</name>
      <value>yarn/<EMAIL></value>
    </property>
    
    <property>
      <name>yarn.timeline-service.principal.pattern</name>
      <value>*</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.reader.webapp.address</name>
      <value>adevthdp001-sa.nfra.io:8198</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.reader.webapp.https.address</name>
      <value>adevthdp001-sa.nfra.io:8199</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.recovery.enabled</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.state-store-class</name>
      <value>org.apache.hadoop.yarn.server.timeline.recovery.LeveldbTimelineStateStore</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.store-class</name>
      <value>org.apache.hadoop.yarn.server.timeline.EntityGroupFSTimelineStore</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.ttl-enable</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.ttl-ms</name>
      <value>2678400000</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.version</name>
      <value>2.0f</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.versions</name>
      <value>1.5f,2.0f</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.webapp.address</name>
      <value>adevthdp002-sa.nfra.io:8188</value>
    </property>
    
    <property>
      <name>yarn.timeline-service.webapp.https.address</name>
      <value>adevthdp002-sa.nfra.io:8190</value>
    </property>
    
    <property>
      <name>yarn.webapp.api-service.enable</name>
      <value>true</value>
    </property>
    
    <property>
      <name>yarn.webapp.ui2.enable</name>
      <value>true</value>
    </property>
    
  </configuration>