FROM reg.navercorp.com/gfp/base/jdk:8u272

ARG hadoop_conf_test_dir=./conf/test
ARG hadoop_conf_test_c3_dir=./conf/test-c3
ARG hadoop_conf_real_dir=./conf/real

ARG spark3_tarball=./spark-3.2.2-bin-hadoop2.7.tgz
ARG spark3_basedir=spark-3.2.2-bin-hadoop2.7

USER irteam
WORKDIR /home1/irteam

COPY $spark3_tarball ./

COPY $hadoop_conf_test_dir hadoop-conf-test/
COPY $hadoop_conf_test_c3_dir hadoop-conf-test-c3/
COPY $hadoop_conf_real_dir hadoop-conf-stage/
COPY $hadoop_conf_real_dir hadoop-conf-real/

RUN tar xzf $spark3_tarball \
  && rm -f $spark3_tarball

ENV SPARK_HOME=/home1/irteam/$spark3_basedir

# ENV HADOOP_CONF_DIR=/home1/irteam/hadoop-conf-test
ENV HADOOP_USER_NAME=irteam

<PERSON> ["$SPARK_HOME/bin/spark-submit", "-h"]
