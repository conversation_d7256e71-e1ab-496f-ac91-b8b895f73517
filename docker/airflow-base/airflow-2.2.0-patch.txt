diff --git a/Dockerfile b/Dockerfile
index 4e4bbe825..d3671f5cd 100644
--- a/Dockerfile
+++ b/Dockerfile
@@ -63,6 +63,7 @@ ENV PYTHON_BASE_IMAGE=${PYTHON_BASE_IMAGE} \
 # Install curl and gnupg2 - needed for many other installation steps
 RUN apt-get update \
     && apt-get install -y --no-install-recommends \
+           ca-certificates \
            curl \
            gnupg2 \
     && apt-get autoremove -yqq --purge \
@@ -132,8 +133,8 @@ RUN mkdir -pv /usr/share/man/man1 \
     && apt-get clean \
     && rm -rf /var/lib/apt/lists/*
 
-ARG INSTALL_MYSQL_CLIENT="true"
-ARG INSTALL_MSSQL_CLIENT="true"
+ARG INSTALL_MYSQL_CLIENT="false"
+ARG INSTALL_MSSQL_CLIENT="false"
 ARG AIRFLOW_REPO=apache/airflow
 ARG AIRFLOW_BRANCH=main
 ARG AIRFLOW_EXTRAS
@@ -335,6 +336,7 @@ ENV PYTHON_BASE_IMAGE=${PYTHON_BASE_IMAGE} \
 # Install curl and gnupg2 - needed for many other installation steps
 RUN apt-get update \
     && apt-get install -y --no-install-recommends \
+           ca-certificates \
            curl \
            gnupg2 \
     && apt-get autoremove -yqq --purge \
@@ -365,7 +367,7 @@ ARG RUNTIME_APT_DEPS="\
        netcat \
        openssh-client \
        postgresql-client \
-       python2 \
+       python2.7 \
        rsync \
        sasl2-bin \
        sqlite3 \
@@ -375,8 +377,8 @@ ARG ADDITIONAL_RUNTIME_APT_DEPS=""
 ARG RUNTIME_APT_COMMAND="echo"
 ARG ADDITIONAL_RUNTIME_APT_COMMAND=""
 ARG ADDITIONAL_RUNTIME_APT_ENV=""
-ARG INSTALL_MYSQL_CLIENT="true"
-ARG INSTALL_MSSQL_CLIENT="true"
+ARG INSTALL_MYSQL_CLIENT="false"
+ARG INSTALL_MSSQL_CLIENT="false"
 ARG AIRFLOW_USER_HOME_DIR=/home/<USER>
 ARG AIRFLOW_HOME
 # Having the variable in final image allows to disable providers manager warnings when
