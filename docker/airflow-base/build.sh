#!/bin/bash

DOCKER=${DOCKER:-docker}

TARGET_REPO=reg.navercorp.com/gfp/airflow-base

AIRFLOW_REPO=https://github.com/apache/airflow
AIRFLOW_VERSION=${AIRFLOW_VERSION:-2.1.4}

# cd airflow-repo; git diff > patch.txt
# 1) cd airflow-repo; git apply patch.txt
# 2) cd airflow-repo; patch -p1 < patch.txt
PATCH_FILE=airflow-${AIRFLOW_VERSION}-patch.txt

PYTHON_BASE_IMAGE=reg.navercorp.com/gfp/base/jdk-python:8u272-3.8

ADDITIONAL_AIRFLOW_EXTRAS=(
    airbyte
    kerberos
    jenkins
    mongo
    apache.spark
    apache.hdfs
    apache.webhdfs
    apache.druid
)

set -ex
git clone "$AIRFLOW_REPO" airflow
(cd airflow; git checkout -q $AIRFLOW_VERSION)
(cd airflow; patch -p1 < ../$PATCH_FILE)

$DOCKER build -t $TARGET_REPO:$AIRFLOW_VERSION \
    --build-arg AIRFLOW_VERSION=$AIRFLOW_VERSION \
    --build-arg PYTHON_BASE_IMAGE=$PYTHON_BASE_IMAGE \
    --build-arg ADDITIONAL_AIRFLOW_EXTRAS=$(IFS=, ; echo "${ADDITIONAL_AIRFLOW_EXTRAS[*]}") \
    airflow \
&& rm -rf airflow
