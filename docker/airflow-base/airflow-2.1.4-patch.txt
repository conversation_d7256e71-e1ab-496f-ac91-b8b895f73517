diff --git a/Dockerfile b/Dockerfile
index 020eec5d4..8734e8f71 100644
--- a/Dockerfile
+++ b/Dockerfile
@@ -62,7 +62,7 @@ ENV PYTHON_BASE_IMAGE=${PYTHON_BASE_IMAGE} \
     LC_CTYPE=C.UTF-8 LC_MESSAGES=C.UTF-8
 
 # Install curl and gnupg2 - needed for many other installation steps
-RUN apt-get update \
+RUN apt-get update; apt-get install ca-certificates \
     && apt-get install -y --no-install-recommends \
            curl \
            gnupg2 \
@@ -331,7 +331,7 @@ ENV PYTHON_BASE_IMAGE=${PYTHON_BASE_IMAGE} \
     AIRFLOW_PIP_VERSION=${AIRFLOW_PIP_VERSION}
 
 # Install curl and gnupg2 - needed for many other installation steps
-RUN apt-get update \
+RUN apt-get update; apt-get install ca-certificates \
     && apt-get install -y --no-install-recommends \
            curl \
            gnupg2 \
@@ -363,7 +363,7 @@ ARG RUNTIME_APT_DEPS="\
        netcat \
        openssh-client \
        postgresql-client \
-       python2 \
+       python2.7 \
        rsync \
        sasl2-bin \
        sqlite3 \
diff --git a/scripts/docker/install_mysql.sh b/scripts/docker/install_mysql.sh
index 534ed9804..3c161e06f 100755
--- a/scripts/docker/install_mysql.sh
+++ b/scripts/docker/install_mysql.sh
@@ -56,7 +56,7 @@ install_mysql_client() {
     rm -rf "${GNUPGHOME}"
     unset GNUPGHOME
     apt-key list > /dev/null 2>&1
-    echo "deb http://repo.mysql.com/apt/debian/ buster mysql-${MYSQL_VERSION}" | tee -a /etc/apt/sources.list.d/mysql.list
+    echo "deb http://repo.mysql.com/apt/ubuntu/ bionic mysql-${MYSQL_VERSION}" | tee -a /etc/apt/sources.list.d/mysql.list
     apt-get update
     apt-get install --no-install-recommends -y "${packages[@]}"
     apt-get autoremove -yqq --purge
