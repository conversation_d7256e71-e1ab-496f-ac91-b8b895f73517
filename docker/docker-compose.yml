version: '3.7'

services:
  airflow_postgres:
    image: postgres:9.6.2
    environment:
      - POSTGRES_USER=airflow
      - POSTGRES_PASSWORD=airflow
      - POSTGRES_DB=airflow
    volumes:
      - "airflow_dbdata:/var/lib/postgresql/data"
    ports:
      - "5432:5432"

  airflow_initdb:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    depends_on:
      - airflow_postgres
    volumes:
      - ../airflow.cfg:/opt/airflow/airflow.cfg
      - ../admin-data:/opt/airflow/admin-data
    # use 'bash' instead of '/bin/bash'. see /entrypoint.sh
    command:
      - bash
      - -c
      - |
        airflow db init
        if [[ -e /opt/airflow/admin-data/local/variables.json ]]; then
            airflow connections import /opt/airflow/admin-data/local/connections.json
            airflow variables import /opt/airflow/admin-data/local/variables.json
            airflow pools import /opt/airflow/admin-data/local/pools.json
        fi
        # Enable this if you choose to have RBAC UI activated in the webserver
        airflow users create -r Admin -u admin -e <EMAIL> -f Air -l Flow -p admin
  airflow_webserver:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    restart: always
    depends_on:
      - airflow_initdb
    volumes:
      - ../airflow.cfg:/opt/airflow/airflow.cfg
      - ../airflow-dags/dags:/opt/airflow/dags
      - ../airflow-dags/plugins:/opt/airflow/plugins
      - ../c3s/gfp-data.keytab:/opt/airflow/c3s/gfp-data.keytab
      - ../c3s/krb5.conf:/etc/krb5.conf
    ports:
      - "8080:8080"
    command: webserver
  airflow_scheduler:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    restart: always
    depends_on:
      - airflow_webserver
    volumes:
      - ../airflow.cfg:/opt/airflow/airflow.cfg
      - ../airflow-dags/dags:/opt/airflow/dags
      - ../airflow-dags/plugins:/opt/airflow/plugins
      - ../c3s/gfp-data.keytab:/opt/airflow/c3s/gfp-data.keytab
      - ../c3s/krb5.conf:/etc/krb5.conf
      - ./entrypoint.sh:/opt/airflow/entrypoint.sh
    entrypoint: /opt/airflow/entrypoint.sh
volumes:
  airflow_dbdata:
