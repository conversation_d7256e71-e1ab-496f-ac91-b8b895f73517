package kvext

import (
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"net/http"
	gfpError "oss.navercorp.com/da-ssp/gfp-api/error"
)

type KeyValueExtensionValidator struct{}

/* publisherId, key, value가 없거나 유효하지 않은 경우, 400 에러 */
func (validator *KeyValueExtensionValidator) ValidateRequestParam(kvExtRequest *KeyValueExtensionRequest) (err *gfpError.GfpApiError) {

	// PublisherId가 없으면 에러
	if kvExtRequest.PublisherId == "" {
		param := map[string]string{"params": "PublisherId"}
		err = gfpError.InValidParam.BizError(param)
		return
	}

	// objectId 가 아니면 에러
	_, e := primitive.ObjectIDFromHex(kvExtRequest.PublisherId)
	if e != nil {
		param := map[string]string{"params": "PublisherId 는 ObjectId 타입이어야 합니다."}
		err = gfpError.InValidParam.BizError(param)
		return
	}

	// 조회 시, key, value가 없으면 에러
	keyValueExtensions := kvExtRequest.KeyValueExtensions
	if kvExtRequest.HttpMethod == http.MethodGet && (keyValueExtensions == nil || len(keyValueExtensions) < 1 || keyValueExtensions[0].Key == "" || keyValueExtensions[0].Value == "") {
		param := map[string]string{"params": "key, value"}
		err = gfpError.InValidParam.BizError(param)
	}

	return
}

/* keyValueExtensions 가 없는 경우, 400 에러 */
func (validator *KeyValueExtensionValidator) ValidateKeyValueExtensions(kvExtRequest *KeyValueExtensionRequest) (err *gfpError.GfpApiError) {
	if kvExtRequest.KeyValueExtensions == nil || len(kvExtRequest.KeyValueExtensions) == 0 {
		param := map[string]string{"params": "keyValueExtensions"}
		err = gfpError.InValidParam.BizError(param)
	}

	return
}

/* items 가 없는 경우, 400 에러 (추가/수정 시) */
func (validator *KeyValueExtensionValidator) ValidateItems(kvExtRequest *KeyValueExtensionRequest) (err *gfpError.GfpApiError) {
	// 삭제인 경우, 체크 안 함
	if kvExtRequest.HttpMethod == http.MethodDelete {
		return
	}

	for _, keyValueExtension := range kvExtRequest.KeyValueExtensions {
		items := keyValueExtension.Items
		if items == nil || len(items) == 0 {
			param := map[string]string{"params": "items"}
			err = gfpError.InValidParam.BizError(param)
		}
	}

	return
}

/* key가 사전등록 키가 아닌 경우, 500 에러 */
func (validator *KeyValueExtensionValidator) ValidateKey(ginCtx *gin.Context, kvExtRequest *KeyValueExtensionRequest) (err *gfpError.GfpApiError) {
	isExtendableKey, err := kvExtService.CheckExtendableKey(ginCtx, kvExtRequest)

	if err != nil {
		return
	}

	if isExtendableKey != true {
		err = &gfpError.KeyValueExtensionNotExtendableKey
	}

	return
}
