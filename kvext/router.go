package kvext

import (
	"github.com/gin-gonic/gin"
	"oss.navercorp.com/da-ssp/gfp-api/logger"
)

var (
	kvExtController = KeyValueExtensionController{}
	kvExtService    = KeyValueExtensionService{}
	kvExtValidator  = KeyValueExtensionValidator{}
)

func SetUpRoute(router *gin.Engine) {
	group := router.Group("/kvext")

	// 사전등록 키 확장 정보 조회
	group.GET("/:publisherId/:key/:value", getHandler)

	// 사전등록 키 확장 정보 단건 추가 작업 요청
	group.POST("/:publisherId", kvExtController.RegisterJob)

	// 사전등록 키 확장 정보 단건 수정 작업 요청
	group.PUT("/:publisherId", kvExtController.RegisterJob)

	// 사전등록 키 확장 정보 단건 삭제 작업 요청
	group.DELETE("/:publisherId", kvExtController.RegisterJob)

	// 사전등록 키 확장 정보 작업 요청 상태 조회
	group.GET("/:publisherId/:key", getHandler)

	kvextsGroup := router.Group("/kvexts")

	// 사전등록 키 확장 정보 다건 추가 작업 요청
	kvextsGroup.POST("/:publisherId", kvExtController.RegisterJobs)

	// 사전등록 키 확장 정보 다건 수정 작업 요청
	kvextsGroup.PUT("/:publisherId", kvExtController.RegisterJobs)

	// 사전등록 키 확장 정보 다건 삭제 작업 요청
	kvextsGroup.DELETE("/:publisherId", kvExtController.RegisterJobs)
}

/**
gin의 routing 한계로 인해 아래와 같이 라우팅을 등록하면
router.GET("/kvext/:publisherId/:key/:value", kvExtController.Get)
router.GET("/kvext/status/:reqId", kvExtController.GetStatus)

이런 에러를 뱉음.
panic: ':reqId' in new path '/kvext/:publisherId/:reqId' conflicts with existing wildcard ':key' in existing prefix '/kvext/:publisherId/:key

따라서 우회적인 방법으로 아래와 같이 라우팅을 등록하고, GetHandler에서 별도 처리함.
router.GET("/kvext/:publisherId/:key/:value", getHandler)
router.GET("/kvext/:publisherId/:key", getHandler)
*/
func getHandler(ginCtx *gin.Context) {
	entry := ginCtx.Value("LogEntry").(*logger.Entry)

	pubId := ginCtx.Param("publisherId")
	key := ginCtx.Param("key")
	entry.Debugf("pubId:%s key:%s", pubId, key)

	if pubId == "status" {
		ginCtx.Set("reqId", key) // key = :reqId
		kvExtController.GetStatus(ginCtx)
	} else {
		kvExtController.Get(ginCtx)
	}
}
