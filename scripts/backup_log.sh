#!/bin/bash

LOG_DIR=/home1/irteam/gfp-api/log
BACKUP_DIR=/home1/irteam/gfp-api/log/backup
YEAR_DIR=$BACKUP_DIR/$YEAR
MONTH_DIR=$YEAR_DIR/$MONTH

YESTERDAY=`date -d "-1 days" "+%Y%m%d"`
echo "yesterday: $YESTERDAY"

backupStdOut() {
        cp $LOG_DIR/std.out $MONTH_DIR/std.out.$YESTERDAY
        cat /dev/null > $LOG_DIR/std.out
}

makeDir() {
        YEAR=`date -d "-1 days" "+%Y"`
        MONTH=`date -d "-1 days" "+%m"`

        YEAR_DIR=$BACKUP_DIR/$YEAR
        MONTH_DIR=$YEAR_DIR/$MONTH

        echo "backupDir: $BACKUP_DIR"
        echo "yearDir: $YEAR_DIR"
        echo "monthDir: $MONTH_DIR"

        if [ -d "$BACKUP_DIR" ]; then
                echo "$BACKUP_DIR already exists"
        else
                echo "$BACKUP_DIR doesn't exist.so mkdir"
                mkdir $BACKUP_DIR
        fi

        if [ -d "$YEAR_DIR" ]; then
                echo "$YEAR_DIR already exists"
        else
                echo "$YEAR_DIR doesn't exist.so mkdir"
                mkdir $YEAR_DIR
        fi

        if [ -d "$MONTH_DIR" ]; then
                echo "$MONTH_DIR already exists"
        else
                echo "$MONTH_DIR doesn't exist.so mkdir"
                mkdir $MONTH_DIR
        fi
}

backup() {
        fileCnt=`ls -1 $LOG_DIR | grep $YESTERDAY | wc -l`
        echo "fileCnt to move: $fileCnt"

        if [ $fileCnt -gt 0 ]; then
                mv $LOG_DIR/*.$YESTERDAY $MONTH_DIR/.
                echo "$fileCnt files were moved to backup diir"
        else
                echo "fileCnt not exists "
        fi
}

makeDir
backupStdOut
backup