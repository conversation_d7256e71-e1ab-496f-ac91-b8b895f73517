#!/bin/bash

BUILD_DIR=/home1/irteam/build
SRC_DIR=$BUILD_DIR/doc_base
DOC_DIR=/home1/irteam/doc
DEPLOY_DIR=/home1/irteam/gfp-api
LOG_DIR=$DEPLOY_DIR/log
CONF_DIR=$DEPLOY_DIR/conf
BIN_DIR=$DEPLOY_DIR/bin
RUN_DIR=$DEPLOY_DIR/run
PID_FILE=$RUN_DIR/gfp-api.pid
RUNNER=gfp-api-runner

initDir() {
        echo "initDir..."

        if [ ! -d "$BUILD_DIR" ]; then
                mkdir $BUILD_DIR
        fi

        if [ ! -d "$SRC_DIR" ]; then
                mkdir $SRC_DIR
        fi

        if [ -d "$DEPLOY_DIR" ]; then
                if [ ! -d "$LOG_DIR" ]; then
                        mkdir $LOG_DIR
                fi

                if [ ! -d "$BIN_DIR" ]; then
                        mkdir $BIN_DIR
                fi

                if [ ! -d "$CONF_DIR" ]; then
                        mkdir $CONF_DIR
                fi

                if [ ! -d "$RUN_DIR" ]; then
                        mkdir $RUN_DIR
                fi
        else
                mkdir $DEPLOY_DIR
                mkdir $LOG_DIR
                mkdir $BIN_DIR
                mkdir $CONF_DIR
                mkdir $RUN_DIR
        fi

        echo -e "initDir done\n"
}

deploy() {
        echo "deploy...."
        cp $SRC_DIR/gfp-api $BIN_DIR/$RUNNER
        cp $SRC_DIR/config/env/common.json $CONF_DIR
        cp $SRC_DIR/config/env/$GFPAPI_PROFILE.json $CONF_DIR
        echo -e "deploy done\n"
}

stop() {
        echo "stop gfp-api..."

        pids=`ps -ef | grep $RUNNER | grep -v "grep" | awk '{print $2}'`

        echo $pids

        for pid in $pids
        do
                kill $pid
                echo "killed $pid"
        done

        echo -e "stop gfp-api done\n"
}

start() {
        echo "start gfp-api..."
	cd $BIN_DIR
        nohup $BIN_DIR/$RUNNER -confdir $CONF_DIR >> $LOG_DIR/std.out &
        echo -e "start gfp-api done\n"
}

makeApidoc() {
	echo "make apidoc"

	cd $SRC_DIR
	rm -rf $DOC_DIR

	apidoc -i ./ -o $DOC_DIR

        echo -e "apidoc done\n"
}

pid() {
        ps -ef | grep $RUNNER | grep -v "grep" | grep -v $0 | awk '{print $2}'
}

if [ "$1" == "start" ]; then
        start
	echo "completed."
elif [ "$1" == "stop" ]; then
        stop
	echo "completed."
elif [ "$1" == "restart" ]; then
        stop
        start
	echo "completed."
elif [ "$1" == "deploy" ]; then
        initDir
	stop

	# 실행파일을 복사하기 전 sleep을 줘서 Text file busy 에러 회피
	sleep 3

        deploy
	start
	echo "completed."
elif [ "$1" == "apidoc" ]; then
	makeApidoc
	echo "completed."
elif [ "$1" == "pid" ]; then
	pid
else
	echo "usage :: $ ./gfp-api.sh stop|start|restart|deploy|apidoc|pid"
        exit 1
fi
